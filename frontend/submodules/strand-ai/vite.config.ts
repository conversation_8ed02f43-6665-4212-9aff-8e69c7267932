import { Plugin } from 'vite'
import react from '@vitejs/plugin-react-swc'
import fs from 'fs'
import mkcert from 'vite-plugin-mkcert'
import path from 'path'

const pngLoader: Plugin = {
  name: 'png-loader',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transform(_: any, id: string) {
    if (id.endsWith('.png')) {
      const data = fs.readFileSync(id)
      const base64 = data.toString('base64')

      return `export default 'data:image/png;base64,${base64}';`
    }
  }
}

export default () => {
  const target = process.env.TARGET
  let buildOptions = {}
  if (target === 'react') {
    buildOptions = {
      emptyOutDir: false,
      lib: {
        entry: './src/Bot.tsx',
        name: 'strand-bot',
        fileName: (format: string) => `strand-bot.${format}.js`
      },
      rollupOptions: {
        external: ['react', 'react-dom'],
        output: {
          globals: {
            react: 'React'
          }
        }
      }
    }
  } else if (target === 'web-component') {
    buildOptions = {
      emptyOutDir: false,
      lib: {
        entry: './src/web-component.ts',
        name: 'strand-bot',
        fileName: (format: string) => `strand-bot-web-component.${format}.js`
      },
      target: 'esnext',
      assetsInlineLimit: 4096000
    }
  } else {
    buildOptions = false
  }

  return {
    base: './',
    define: {
      'process.env': {
        NODE_ENV: 'production'
      }
    },
    plugins: [mkcert(), react(), pngLoader],
    server: {
      port: 3030
    },
    preview: {
      port: 8080
    },
    build: buildOptions,
    resolve: {
      alias: {
        react: path.resolve(__dirname, './node_modules/react'),
        'react-dom': path.resolve(__dirname, './node_modules/react-dom')
      }
    }
  }
}
