interface Breakpoints {
  [index: string]: number
}

interface Theme {
  color: {
    [index: string]: string
  }
  font: {
    [index: string]: string
  }
  layout: {
    [index: string]: string
  }
}

export const breakpoints: Breakpoints = {
  xxs: 0,
  xs: 575,
  sm: 767,
  md: 991,
  lg: 1199,
  xl: 1439,
  xxl: 1829
}

const theme: Theme = {
  color: {
    body: '#ffffff',
    red: '#C60000',
    primary: '#976243',
    primaryLight: '#E4D5C9',
    primaryExtraLight: '#F6EFE9',
    global: '#000000',
    muted: '#777777',
    mutedIntense: 'rgba(0,0,0,0.3)',
    light: '#EEEEEE',
    inverted: '#ffffff',
    line: 'rgba(0,0,0,0.1)'
  },
  font: {
    primary: 'Inter, sans-serif',
    secondary: 'Avenir, sans-serif',
    tertiary: 'SangBleu Republic Trial, Lora, serif'
  },
  layout: {
    navigationSize: '5rem',
    navigationSizeExtended: '30vw',
    outerMargin: '4rem'
  }
}

export default theme
