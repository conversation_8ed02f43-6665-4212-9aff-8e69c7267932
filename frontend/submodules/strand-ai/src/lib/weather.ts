interface Icons {
  [key: string]: Array<number>
}

const icons: Icons = {
  'cloudy.svg': [119],
  'fog.svg': [248, 143, 260],
  'freezingrain.svg': [377, 374, 350, 314, 311, 284, 281, 185],
  'heavyrain.svg': [308, 305],
  'heavyshower.svg': [356],
  'lightning.svg': [299, 296],
  'lightrain.svg': [176, 293],
  'lightshower.svg': [266, 263, 359],
  'lightsnow.svg': [227, 179],
  'mostlycloudy.svg': [122],
  'partlycloudy.svg': [116],
  'rain.svg': [21, 51, 52, 53, 58, 59, 60, 62, 63],
  'shower.svg': [353],
  'snow.svg': [395, 392, 338, 335, 332, 329, 230],
  'snowrain.svg': [320, 317],
  'snowshower.svg': [368, 323, 326, 371],
  'snowrainshower.svg': [365, 362, 182],
  'sunny.svg': [113],
  'thunderstorm.svg': [389, 386, 200],
  'wind.svg': [18, 19]
}

export function getIcon(code: number) {
  let result
  for (const icon in icons) {
    for (let i = 0; i < icons[icon].length; i++) {
      if (code === icons[icon][i]) {
        result = icon
      }
    }
  }
  return result || 'unknow.svg'
}

interface Descriptions {
  [key: string]: Array<number>
}

const descriptions: Descriptions = {
  cloudy: [-8, 7, 8],
  fog: [-4, 10, 11, 12, 28, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49],
  freezingRain: [-5, 24, 56, 66, 67],
  heavyRain: [-6, -2, -1, 54, 55, 64, 65],
  heavyShower: [],
  lightning: [13],
  lightRain: [20, 50, 61],
  lightShower: [14, 15, 16, 25],
  lightSnow: [26, 70, 71],
  mostlyCloudy: [4, 5, 6],
  partlyCloudy: [1, 2, 3],
  rain: [21, 51, 52, 53, 58, 59, 60, 62, 63],
  shower: [80, 81],
  snow: [338, 335, 332, 329],
  snowAndRain: [23, 27, 68, 69, 82, 83],
  snowShower: [323, 326],
  snowShowerAndRain: [3],
  clear: [0],
  thunderstorm: [17, 29, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  unknown: [9, 30, 31, 32, 33, 34, 35],
  windy: [18, 19]
}

export function setDescription(code: number) {
  for (const description in descriptions) {
    for (let i = 0; i < descriptions[description].length; i++) {
      if (code === descriptions[description][i]) {
        return description
      }
    }
  }
}
