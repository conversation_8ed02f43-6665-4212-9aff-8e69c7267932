import { timingFunctions } from 'polished'
import { css } from 'styled-components'

export const TransitionPrimary = (property = 'all', delay = '0s') => css`
  transition-property: ${property};
  transition-duration: 0.2s;
  transition-delay: ${delay};
  transition-timing-function: ${timingFunctions('easeOutQuad')};
  will-change: ${property};
`

export const TransitionSecondary = (property = 'all', delay = '0s') => css`
  transition-property: ${property};
  transition-duration: 0.5s;
  transition-delay: ${delay};
  transition-timing-function: ${timingFunctions('easeOutExpo')};
  will-change: ${property};
`

export const Cover = (offset = '0') => css`
  position: absolute;
  top: ${offset};
  left: ${offset};
  right: ${offset};
  bottom: ${offset};
  content: ' ';
`

export const ImgCover = () => css`
  img {
    display: block;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
`

export const Reset = ($type = 'all') => {
  if ($type === 'link') {
    return css`
      color: inherit;

      &:hover,
      &:focus {
        color: inherit;
      }
    `
  }
  if ($type === 'spacing') {
    return css`
      margin: 0;
      padding: 0;
    `
  }
  if ($type === 'list') {
    return css`
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        margin: 0;
        padding: 0;
      }
    `
  }
  if ($type === 'button') {
    return css`
      color: inherit;
      padding: 0;
      background-color: transparent;
      border: 0;
      cursor: pointer;
      outline: none;
    `
  }
  if ($type === 'all') {
    return css`
      color: inherit;
      background-color: transparent;
      border: 0;
      float: none;
      height: auto;
      list-style: none;
      margin: 0;
      padding: 0;
      position: static;
      width: auto;
    `
  }
}

export const HasSvgIcon = (size = '1em') => css`
  svg {
    height: ${size};
    width: ${size};
    display: block;
  }
`

export const LastMb0 = () => css`
  > * {
    &:last-child {
      margin-bottom: 0;
    }
  }
`
