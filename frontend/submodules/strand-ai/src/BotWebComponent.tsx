import ReactDOM from 'react-dom'
import { StyleSheetManager, ThemeProvider } from 'styled-components'
import theme from './lib/theme.ts'
import Bot, { BotProps } from './Bot.tsx'
import './lib/i18n.ts'
import useWidgetState from './features/Widget/Widget.store.ts'
import { ChatPropertyResponse } from './features/Chat/Chat.types.ts'

const normalizeAttribute = (attribute: string) => {
  return attribute.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

class WidgetWebComponent extends HTMLElement {
  private readonly styleHost: HTMLElement
  private readonly mountPoint: HTMLElement
  private _setState: ((filters: ChatPropertyResponse) => void) | null = null

  constructor() {
    super()
    this.styleHost = document.createElement('div')
    this.mountPoint = document.createElement('div')
    this.mountPoint.style.position = 'fixed'
    this.mountPoint.style.zIndex = '9999'
    this.mountPoint.setAttribute('id', 'root')
    this.attachShadow({ mode: 'open' })
    this.open = this.open.bind(this)
    this.close = this.close.bind(this)
    this.fetchFilter = this.fetchFilter.bind(this)
    this.toggleFullscreen = this.toggleFullscreen.bind(this)
  }
  toggleFullscreen() {
    useWidgetState.getState().toggleFullscreen()
  }
  open() {
    useWidgetState.getState().open()
  }
  close() {
    useWidgetState.getState().close()
  }
  fetchFilter(filters: ChatPropertyResponse) {
    if (this._setState) {
      this._setState(filters)
    }
  }
  connectedCallback() {
    const props = this.getPropsFromAttributes<BotProps>()

    this.shadowRoot?.appendChild(this.styleHost)
    this.shadowRoot?.appendChild(this.mountPoint)
    ReactDOM.render(
      <StyleSheetManager target={this.styleHost}>
        <ThemeProvider theme={theme}>
          <Bot {...props} parseFiltersCallback={filters => this.fetchFilter(filters)} />
        </ThemeProvider>
      </StyleSheetManager>,
      this.mountPoint
    )
  }

  private getPropsFromAttributes<T>(): T {
    const props: Record<string, string> = {}

    for (let index = 0; index < this.attributes.length; index++) {
      const attribute = this.attributes[index]
      props[normalizeAttribute(attribute.name)] = attribute.value
    }

    return props as T
  }

  set setStateFunction(func: (filters: ChatPropertyResponse) => void) {
    this._setState = func
  }
}

export default WidgetWebComponent
