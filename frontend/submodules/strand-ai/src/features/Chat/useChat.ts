import { useContext, useEffect, useRef } from 'react'
import { ChatContext } from './Chat.context'
import { ActionType, ChatMessageProps, ChatPropertyResponse } from './Chat.types'
import api from '../../lib/api'
import usePromptState from '../../components/Prompt/Prompt.store'

export const useChat = () => {
  const context = useContext(ChatContext)
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider')
  }
  const { state, dispatch } = context
  const { setIsTouched } = usePromptState()
  const { messages, status, abortController } = state
  const setMessages = (payload: ChatMessageProps[]) => dispatch({ type: ActionType.SET_MESSAGES, payload })
  const setFilters = (payload: ChatPropertyResponse) => dispatch({ type: ActionType.SET_FILTERS, payload })
  const setStatus = (payload: string) => dispatch({ type: ActionType.SET_STATUS, payload })
  const setAbortController = (payload: AbortController) => dispatch({ type: ActionType.SET_ABORT_CONTROLLER, payload })
  const reset = () => {
    setIsTouched(false)
    dispatch({ type: ActionType.RESET })
  }

  const messagesRef = useRef(messages)

  const resetChat = async () => {
    if (status === 'loading') {
      abortController.abort()
      setAbortController(new AbortController())
    }
    reset()
  }

  const sendMessage = async ({
    message,
    isPredefined,
    abortController
  }: {
    message: string | undefined
    isPredefined?: boolean
    abortController?: AbortController
  }) => {
    if (status === 'loading') return

    const ac = abortController || new AbortController()

    setAbortController(ac)
    setStatus('loading')

    const data = {
      message,
      customer: {
        name: null,
        email: null,
        language: 'en-US',
        conversation: messages
      },
      model: 'gpt-4',
      max_tokens: 0,
      top_k: 5
    }

    let messagesPrototype: ChatMessageProps[] = []

    const messageLoader: ChatMessageProps = {
      type: 'ai',
      content: '',
      state: 'loading'
    }

    if (message) {
      const newMessage: ChatMessageProps = {
        type: 'human',
        content: message,
        state: 'success',
        isPredefined
      }
      messagesPrototype = [...messagesRef.current, newMessage, messageLoader]
    } else {
      messagesPrototype = [...messagesRef.current, messageLoader]
    }

    setMessages(messagesPrototype)

    api
      .post('/chat', data, {
        signal: ac.signal
      })
      .then(response => {
        const parsedData = response.data.match(/[^\r\n]+/g).map((o: string) => JSON.parse(o))
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const summary = parsedData.find((o: any) => !o.message)

        setMessages([
          ...messagesRef.current.filter(o => o.state !== 'loading'),
          {
            type: 'ai',
            content: summary.customer.conversation.slice(-1)[0].content,
            recommendations: summary.recommendations,
            questions: summary.questions,
            state: 'success'
          }
        ])
        setStatus('default')
      })
      .catch(error => {
        console.error('sendMessage', error)
        const computedMessages = [...messagesRef.current.filter(o => o.state !== 'loading')]
        if (computedMessages.length > 0) {
          setMessages([...messagesRef.current.filter(o => o.state !== 'loading')])
        }
        setStatus('default')
      })

    api
      .post('/filters', data, {
        signal: ac.signal
      })
      .then(response => {
        const filters = response.data
        setFilters(filters)
      })
      .catch(error => {
        console.error('sendMessage', error)
        const computedMessages = [...messagesRef.current.filter(o => o.state !== 'loading')]
        if (computedMessages.length > 0) {
          setMessages([...messagesRef.current.filter(o => o.state !== 'loading')])
        }
        setStatus('default')
      })
  }

  useEffect(() => {
    messagesRef.current = messages
  }, [messages])

  return {
    ...state,
    setMessages,
    reset,
    setStatus,
    sendMessage,
    resetChat,
    setAbortController,
    setFilters
  }
}
