import React, { createContext, useReducer } from 'react'
import { ActionType, ChatAction, ChatContextState } from './Chat.types'

const initialState: ChatContextState = {
  messages: [],
  status: 'default',
  abortController: new AbortController(),
  filters: {
    area_level1: [],
    property_type: [],
    property_subtype: [],
    bedrooms: 0,
    bathrooms: 0,
    parkingspaces: false,
    toilets: 0,
    floor: '',
    pool: false,
    climate_control: [],
    max_price_eur: 0,
    min_size: 0,
    max_size: 0,
    condition: [],
    orientation: [],
    settings: [],
    views: [],
    personal_notes: '',
    is_strandified: false,
    area_level1_id: [],
    area_level2_id: [],
    min_price_eur: 0,
    property_type_id: []
  }
}

const chatReducer = (state: ChatContextState, action: ChatAction): ChatContextState => {
  switch (action.type) {
    case ActionType.SET_MESSAGES:
      return {
        ...state,
        messages: action.payload
      }
    case ActionType.RESET:
      return {
        ...state,
        messages: [],
        status: 'default',
        abortController: new AbortController()
      }
    case ActionType.SET_STATUS:
      return {
        ...state,
        status: action.payload
      }
    case ActionType.SET_ABORT_CONTROLLER:
      return {
        ...state,
        abortController: action.payload
      }
    case ActionType.SET_FILTERS:
      return {
        ...state,
        filters: action.payload
      }
    default:
      return state
  }
}

export const ChatContext = createContext<{
  state: ChatContextState
  dispatch: React.Dispatch<ChatAction>
}>({
  state: initialState,
  dispatch: () => {}
})

export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState)

  return <ChatContext.Provider value={{ state, dispatch }}>{children}</ChatContext.Provider>
}
