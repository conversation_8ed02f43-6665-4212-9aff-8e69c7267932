import styled, { css, keyframes } from 'styled-components'
import { ChatMessageType } from './Chat.types'
import { Reset, TransitionPrimary } from '../../lib/mixins'

export const ChatWrapper = styled.div`
  flex: 1 1 auto;
  padding: 12px 18px 18px;
  overflow-y: auto;
  mask-image: linear-gradient(to top, transparent, black 12px, black calc(100% - 18px), transparent);
`

export const ChatMessageBody = styled.div`
  display: flex;
`

export const ChatMessageWrapper = styled.div<{ variant: ChatMessageType }>`
  ${props =>
    props.variant === 'human' &&
    css`
      ${ChatMessageBody} {
        justify-content: flex-end;
      }
    `};

  &:not(:last-child) {
    margin-bottom: 16px;
  }
`

export const ChatMessageCards = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
  padding-top: 18px;
`

export const ChatMessageAvatar = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid ${props => props.theme.color.line};
  margin-right: 8px;
  margin-left: -8px;
`

export const ChatMessageContent = styled.div<{ variant: ChatMessageType }>`
  flex: 0 1 auto;
  padding: 8px;
  border-radius: 8px;
  white-space: pre-wrap;
  text-align: left;

  &:not(:last-child) {
    margin-bottom: 16px;
  }

  ${props =>
    props.variant === 'ai' &&
    css`
      border-top-left-radius: 0;
      background-color: ${props => props.theme.color.light};
    `};

  ${props =>
    props.variant === 'human' &&
    css`
      border-top-right-radius: 0;
      background-color: ${props => props.theme.color.global};
      color: ${props => props.theme.color.inverted};
    `};
`

const dotAnimation = keyframes`
  0%{ opacity: 0.3; }
  100%{ opacity: 1; }
`

export const ChatLoaderWrapper = styled.div`
  display: flex;
  height: 20px;
  align-items: center;
  margin: 0 2px;
  > span {
    margin: 0 2px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: currentColor;
    animation: ${dotAnimation} 0.6s infinite alternate;
    opacity: 0.3;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
`

export const ChatMessageQuestionsList = styled.div`
  padding-top: 18px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
`
export const ChatMessageQuestion = styled.button<{
  active: boolean
  disabled: boolean
  onClick: (question: string) => void
}>`
  ${Reset('button')}
  text-align: left;
  padding: 8px;
  border: 1px solid currentColor;
  border-radius: 8px 0px 8px 8px;
  display: block;
  max-width: calc(100% - 32px);
  ${TransitionPrimary('background-color')}
  &:not(:last-child) {
    margin-bottom: 8px;
  }

  ${props =>
    props.active &&
    css`
      background-color: ${props.theme.color.global};
      color: ${props.theme.color.inverted};
    `};

  ${props =>
    props.disabled &&
    css`
      cursor: not-allowed;
    `};
`
