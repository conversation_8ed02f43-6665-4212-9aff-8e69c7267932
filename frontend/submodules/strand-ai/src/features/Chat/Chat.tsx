import { useEffect, useRef, useState } from 'react'
import {
  ChatLoader<PERSON>rapper,
  ChatMessageAvatar,
  ChatMessageContent,
  ChatMessageBody,
  ChatMessageWrapper,
  ChatWrapper,
  ChatMessageCards,
  ChatMessageQuestionsList,
  ChatMessageQuestion
} from './Chat.styles'
import { useChat } from './useChat'
import axios from 'axios'
import { PropertyCardProps } from '../../components/PropertyCard/PropertyCard.types'
import PropertyCard from '../../components/PropertyCard'
import { ChatMessageProps, ChatPropertyFeature, ChatProps } from './Chat.types'
import { AnimatePresence } from 'framer-motion'
import WebPanel from '../WebPanel'
import avatar from '../../assets/img/ai-avatar.png'
import useWidgetState from '../Widget/Widget.store'
import { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock'
import { useMediaQuery } from '../../utils/useMediaQuery'
import usePromptState from '../../components/Prompt/Prompt.store'

const ChatLoader = () => {
  return (
    <ChatLoaderWrapper>
      <span></span>
      <span></span>
      <span></span>
    </ChatLoaderWrapper>
  )
}

const ChatMessage = ({ content, type, state, recommendations, questions, onQuestionSelect }: ChatMessageProps) => {
  const [properties, setProperties] = useState<PropertyCardProps[]>([])
  const [activeQuestion, setActiveQuestion] = useState<string | null>(null)
  const [activeProperty, setActiveProperty] = useState<PropertyCardProps | null>(null)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const avatarSrc = typeof avatar === 'string' ? avatar : (avatar as any).src

  const handleQuestionSelect = (question: string) => {
    if (activeQuestion) return
    setActiveQuestion(question)
    if (onQuestionSelect) onQuestionSelect(question)
  }

  useEffect(() => {
    recommendations?.forEach(async (property: string) => {
      try {
        const { data } = await axios.get(`https://api-stg.app.strand.es/strandwebsite/${property}`, {
          headers: {
            'X-Api-Key': 'potato'
          }
        })
        setProperties(state => [
          ...state,
          {
            name: data.reference,
            title: data.title,
            price: data.priceSale || data.priceRentLongTerm || data.priceRentShortTerm,
            size: data.builtArea,
            beds: data.bedrooms,
            baths: data.bathrooms,
            image: data.mainImg,
            details: data.features.map((o: ChatPropertyFeature) => {
              return o.name
            })
          }
        ])
      } catch (error) {
        console.error(error)
      }
    })
  }, [recommendations, setProperties])

  return (
    <ChatMessageWrapper variant={type}>
      <ChatMessageBody>
        {type === 'ai' && <ChatMessageAvatar src={avatarSrc} />}
        <ChatMessageContent variant={type}>
          {state === 'loading' && <ChatLoader />}
          {content}
        </ChatMessageContent>
      </ChatMessageBody>
      {/* Properties */}
      {properties.length > 0 && (
        <ChatMessageCards>
          {properties.map((property, index) => (
            <PropertyCard key={index} {...property} onClick={() => setActiveProperty(property)} />
          ))}
        </ChatMessageCards>
      )}
      {/* Questions */}
      {questions && questions.length > 0 && (
        <ChatMessageQuestionsList>
          {questions.map((question, index) => (
            <ChatMessageQuestion
              key={index}
              onClick={() => handleQuestionSelect(question)}
              active={activeQuestion === question}
              disabled={!!activeQuestion}
            >
              {question}
            </ChatMessageQuestion>
          ))}
        </ChatMessageQuestionsList>
      )}
      <AnimatePresence>
        {activeProperty && (
          // @ts-ignore: Suppress specific issue TS2786
          <WebPanel
            title={activeProperty.title}
            url={`https://strandproperties.com/single-property/?ref=${activeProperty.name}&embedded=1`}
            onClose={() => setActiveProperty(null)}
          />
        )}
      </AnimatePresence>
    </ChatMessageWrapper>
  )
}

const Chat = ({ hideAiMessages }: ChatProps) => {
  const { messages, sendMessage, abortController } = useChat()
  const wrapperRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery('<=sm')
  const { mode: promptMode, isTouched: isPromptTouched } = usePromptState()

  const { isOpen } = useWidgetState()

  useEffect(() => {
    const updateScroll = () => {
      if (wrapperRef.current) {
        wrapperRef.current.scrollTop = wrapperRef.current.scrollHeight - 1
      }
    }

    if (messages.length === 0 && promptMode === 'default' && !isPromptTouched) {
      sendMessage({ message: 'Hi!', isPredefined: true }).then(() => {
        abortController.abort()
      })
    }

    updateScroll()
  }, [messages, sendMessage, promptMode, isPromptTouched])

  useEffect(() => {
    if (wrapperRef.current) {
      if (isOpen && isMobile) {
        disableBodyScroll(wrapperRef.current)
      } else {
        enableBodyScroll(wrapperRef.current)
      }
    }
  }, [isOpen, isMobile])

  return (
    <ChatWrapper ref={wrapperRef}>
      {messages
        .filter(message => !message.isPredefined && (!hideAiMessages || message.type !== 'ai'))
        .map((message, index) => (
          <ChatMessage
            key={index}
            {...message}
            onQuestionSelect={question => sendMessage({ message: question, isPredefined: true })}
          />
        ))}
    </ChatWrapper>
  )
}
export default Chat
