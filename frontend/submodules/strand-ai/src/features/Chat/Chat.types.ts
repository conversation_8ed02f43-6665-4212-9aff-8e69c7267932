export type ChatMessageType = 'ai' | 'human'
export type ChatMessageProps = {
  content: string
  type: ChatMessageType
  state?: 'loading' | 'error' | 'success'
  recommendations?: string[]
  questions?: string[]
  isPredefined?: boolean
  onQuestionSelect?: (question: string) => void
}

export type ChatProps = {
  hideAiMessages?: boolean
}

export type ChatMessageResponse = {
  message: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  customer?: any
  recommendations: null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  questions: Array<any>
}

export enum ActionType {
  SET_MESSAGES = 'SET_MESSAGES',
  RESET = 'RESET',
  SET_STATUS = 'SET_STATUS',
  SET_ABORT_CONTROLLER = 'SET_ABORT_CONTROLLER',
  SET_FILTERS = 'SET_FILTERS'
}

export type ChatContextState = {
  messages: ChatMessageProps[]
  status: 'default' | 'loading'
  abortController: AbortController
  filters: ChatPropertyResponse
}

export type ChatAction = {
  type: ActionType
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  payload?: any
}

export type ChatProperty = {
  reference: string
  name: string
  priceSale: number
  interiorArea: string
  bedrooms: number
  bathrooms: number
  features: ChatPropertyFeature[]
}

export type ChatPropertyFeature = {
  id: number
  name: string
  featureGroupName: string
}

export type ChatPropertyResponse = {
  area_level1: string[]
  property_type: string[]
  property_subtype: string[]
  bedrooms: number
  bathrooms: number
  parkingspaces: boolean
  toilets: number
  floor: string
  pool: boolean
  climate_control: string[]
  max_price_eur: number
  min_size: number
  max_size: number
  condition: string[]
  orientation: string[]
  settings: string[]
  views: string[]
  personal_notes: string
  is_strandified: boolean
  area_level1_id: string[]
  area_level2_id: string[]
  min_price_eur: number
  property_type_id: number[]
}
