import styled from 'styled-components'
import { Cover, Reset, TransitionPrimary } from '../../lib/mixins'

export const WebPanelWrapper = styled.div`
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background-color: ${props => props.theme.color.body};
  z-index: 99999999;
`
export const WebPanelHeader = styled.div`
  height: 60px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  padding: 0 8px;
`
export const WebPanelUtils = styled.div`
  flex: 0 0 60px;
  min-width: 60px;
`
export const WebPanelDetails = styled.div`
  flex: 1 1 auto;
  text-align: center;
  padding: 12px;
  overflow: hidden;
  font-size: 12px;
`
export const WebPanelTitle = styled.h5`
  font-size: 12px;
  margin: 0;
  font-weight: 500;
  line-height: 1.1;
`
export const WebPanelUrl = styled.span`
  font-size: 12px;
  opacity: 0.4;
  white-space: nowrap;
  overflow: hidden;
  display: block;
  text-overflow: ellipsis;
`
export const WebPanelBody = styled.div`
  position: relative;
  display: flex;
  flex: 1 1 auto;
`
export const WebPanelIframe = styled.iframe`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
`

export const WebPanelLoader = styled.div`
  ${Cover()}
  display: flex;
  align-items: center;
  justify-content: center;
`

export const WebPanelButtonWrapper = styled.button`
  ${Reset('button')}
  width: 42px;
  height: 42px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &:before {
    ${Cover()}
    background-color: ${props => props.theme.color.light};
    border-radius: 50%;
    transform: scale(0.8);
    opacity: 0;
    ${TransitionPrimary('opacity, transform')}
  }

  svg {
    position: relative;
    display: block;
    width: 60%;
    height: 60%;
  }

  &:hover {
    &:before {
      opacity: 1;
      transform: scale(1);
    }
  }
`
