import { ReactNode, useEffect, useRef, useState } from 'react'
import { WebPanelButtonProps, WebPanelProps } from './WebPanel.types'
import {
  WebPanelBody,
  WebPanelButtonWrapper,
  WebPanelDetails,
  WebPanelHeader,
  WebPanelIframe,
  WebPanelLoader,
  WebPanelTitle,
  WebPanelUrl,
  WebPanelWrapper
} from './WebPanel.styles'
import { createPortal } from 'react-dom'
import { motion } from 'framer-motion'
import Icon from '../../components/Icon'
import { useMediaQuery } from '../../utils/useMediaQuery'
import Button from '../../components/Button'

const WebPanelButton = ({ onClick, icon }: WebPanelButtonProps) => {
  return (
    <WebPanelButtonWrapper onClick={onClick}>
      <Icon name={icon} />
    </WebPanelButtonWrapper>
  )
}

const WebPanelMobileWrapper = ({ children }: { children: ReactNode }) => {
  const transition = {
    initial: { y: 0, x: 20, opacity: 0 },
    animate: { y: 0, x: 0, opacity: 1 },
    exit: { y: 0, x: -20, opacity: 0 }
  }

  return (
    <WebPanelWrapper as={motion.div} {...transition}>
      {children}
    </WebPanelWrapper>
  )
}

const WebPanelDesktopWrapper = ({ children }: { children: ReactNode }) => {
  const transition = {
    initial: { y: 10, x: 0, opacity: 0 },
    animate: { y: 0, x: 0, opacity: 1 },
    exit: { y: -10, x: 0, opacity: 0 }
  }

  return (
    <WebPanelWrapper as={motion.div} {...transition}>
      {children}
    </WebPanelWrapper>
  )
}

const WebPanel = ({ url, title, onClose }: WebPanelProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const isMobile = useMediaQuery('<=sm')
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const portalParent =
    document.querySelector('strand-bot')?.shadowRoot?.querySelector('#root') || document.querySelector('body')

  useEffect(() => {
    const iframe = iframeRef.current
    const handleLoad = () => {
      setIsLoading(false)
    }
    setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    iframe?.addEventListener('load', handleLoad)

    return () => {
      iframe?.removeEventListener('load', handleLoad)
    }
  }, [url])

  const openInNewTab = () => {
    window.open(url.replace('&embedded=1', ''), '_blank')
  }

  const WebPanelWrapperComponent = isMobile ? WebPanelMobileWrapper : WebPanelDesktopWrapper

  if (!portalParent) return
  return createPortal(
    <WebPanelWrapperComponent>
      <WebPanelHeader>
        <WebPanelButton icon={isMobile ? 'arrow_back' : 'close_padded'} onClick={onClose}></WebPanelButton>
        <WebPanelDetails>
          <WebPanelTitle>{title || 'Strand Properties'}</WebPanelTitle>
          <WebPanelUrl>
            <span>{url}</span>
          </WebPanelUrl>
        </WebPanelDetails>
        <WebPanelButton icon="ios_share" onClick={openInNewTab}></WebPanelButton>
      </WebPanelHeader>
      <WebPanelBody>
        <WebPanelIframe ref={iframeRef} src={url}></WebPanelIframe>
        {isLoading && (
          <WebPanelLoader
            as={motion.div}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <Button type="oval" variant="transparent" isLoading={true}></Button>
          </WebPanelLoader>
        )}
      </WebPanelBody>
    </WebPanelWrapperComponent>,
    portalParent
  )
}

export default WebPanel
