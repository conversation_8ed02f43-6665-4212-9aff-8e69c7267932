import Header from '../../components/Header'
import Chat from '../Chat'
import { useEffect, useRef } from 'react'
import Prompt from '../../components/Prompt'
import { WidgetBody, WidgetFooter, WidgetVendor, WidgetWrapper } from './Widget.styles'
import { useChat } from '../Chat/useChat'
import { AnimatePresence, motion } from 'framer-motion'
import useWidgetState from './Widget.store'
import SVG from 'react-inlinesvg'
import logo from '../../assets/img/logo-footer.svg'
import { WidgetProps } from './Widget.types'
import Intro from '../Intro'

const Widget = ({
  isMobile,
  introType,
  parseFiltersCallback,
  hideAiMessages,
  isStrandPropertiesMobile
}: WidgetProps) => {
  const { filters, sendMessage } = useChat()
  const { isFullscreen, mode } = useWidgetState()
  const bodyRef = useRef<HTMLDivElement>(null)
  const widgetRef = useRef<HTMLDivElement>(null)
  const activeVariant = isFullscreen || isMobile ? 'fullscreen' : 'popup'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const logoSrc = typeof logo === 'string' ? logo : (logo as any).src

  const variants = {
    popup: {
      left: 'auto',
      top: 'auto',
      bottom: '30px',
      right: '30px',
      width: '375px',
      height: '660px',
      maxHeight: '90%',
      boxShadow: '0px 4px 54px rgba(0, 0, 0, 0.25)'
    },
    fullscreen: {
      bottom: 0,
      right: 0,
      width: '100vw',
      height: window.innerHeight + 'px',
      maxHeight: '100%'
    }
  }

  useEffect(() => {
    if (parseFiltersCallback) {
      parseFiltersCallback(filters)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters])

  return (
    <WidgetWrapper
      as={motion.div}
      ref={widgetRef}
      variants={variants}
      initial={activeVariant}
      animate={activeVariant}
      offsetTop={isStrandPropertiesMobile ? 160 : 0}
    >
      <Header />
      <WidgetBody ref={bodyRef}>{mode === 'chat' && <Chat hideAiMessages={hideAiMessages} />}</WidgetBody>
      <WidgetFooter>
        <Prompt onChange={({ message }) => sendMessage({ message })} />
        <WidgetVendor offsetBottom={isStrandPropertiesMobile ? 160 : 0}>
          {/* @ts-ignore: Suppress specific issue TS2786 */}
          Powered by <SVG src={logoSrc}></SVG>
        </WidgetVendor>
      </WidgetFooter>
      <AnimatePresence>
        {mode === 'intro' && <Intro type={introType} isStrandPropertiesMobile={isStrandPropertiesMobile} />}
      </AnimatePresence>
    </WidgetWrapper>
  )
}

export default Widget
