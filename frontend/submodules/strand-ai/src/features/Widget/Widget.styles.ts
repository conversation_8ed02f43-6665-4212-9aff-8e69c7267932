import styled from 'styled-components'
import media from '../../lib/media'

export const WidgetWrapper = styled.div<{ offsetTop?: number }>`
  position: fixed;
  top: ${props => `${props.offsetTop}px`};
  font-size: 14px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: ${props => props.theme.color.body};
  z-index: 1000;

  ${media('>sm')} {
    align-items: center;
  }

  * {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4 {
    font-weight: 400;
  }
`

export const WidgetBody = styled.div`
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;

  ${media('>sm')} {
    max-width: 680px;
  }
`

export const WidgetFooter = styled.div`
  padding-bottom: 18px;
  padding: 6px 18px 18px;
  width: 100%;

  ${media('>sm')} {
    max-width: 680px;
  }
`

export const WidgetVendor = styled.div<{ offsetBottom?: number }>`
  font-size: 10px;
  user-select: none;
  color: ${props => props.theme.color.mutedIntense};
  padding-top: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${props => `${props.offsetBottom}px`};
  svg {
    display: block;
    height: 12px;
    margin-left: 0.15rem;
  }
`
