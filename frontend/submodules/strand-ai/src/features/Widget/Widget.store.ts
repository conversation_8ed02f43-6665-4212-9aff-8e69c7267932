import { create } from 'zustand'

type WidgetMode = 'intro' | 'chat'

interface WidgetState {
  isFullscreen: boolean
  isOpen: boolean
  mode: WidgetMode
  toggleFullscreen: () => void
  setMode: (mode: WidgetMode) => void
  open: () => void
  close: () => void
}

const useWidgetState = create<WidgetState>(set => ({
  isFullscreen: false,
  isOpen: false,
  mode: 'intro',
  toggleFullscreen: () => set((state: WidgetState) => ({ isFullscreen: !state.isFullscreen })),
  open: () => set({ isOpen: true }),
  close: () => set({ isOpen: false }),
  setMode: (mode: WidgetMode) => set({ mode })
}))

export default useWidgetState
