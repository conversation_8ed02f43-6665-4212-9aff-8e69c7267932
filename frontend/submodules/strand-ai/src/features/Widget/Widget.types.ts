import { ChatPropertyResponse } from '../Chat/Chat.types'
import { IntroType } from '../Intro/Intro.types'

export enum ActionType {
  TOGGLE_FULLSCREEN = 'TOGGLE_FULLSCREEN'
}

export interface WidgetContextState {
  isFullscreen: boolean
}

export interface WidgetAction {
  type: ActionType
  payload?: boolean
}

export interface WidgetProps {
  isMobile: boolean
  introType?: IntroType
  parseFiltersCallback?: (filters: ChatPropertyResponse) => void
  hideAiMessages?: boolean
  isStrandPropertiesMobile?: boolean // Used for only import this bot in app.strand.es or app.strand.fi
}
