import styled, { css } from 'styled-components'
import { ButtonWrapper } from '../../components/Button/Button.styles'
import { IntroType } from './Intro.types'

export const IntroVideo = styled.video`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 75%, rgba(0, 0, 0, 0) 100%);
`

export const IntroMedia = styled.div`
  position: relative;
  flex: 0 1 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  object-fit: cover;
`

export const IntroLogo = styled.div`
  height: 36px;
  max-width: 80%;
  z-index: 2;
  position: relative;

  svg {
    width: 100%;
    height: 100%;
    display: block;
    fill: ${props => props.theme.color.inverted};
  }
`

export const IntroEmblem = styled.div<{ scale?: number }>`
  width: ${props => `${props.scale ? 136 * props.scale : 136}px`};
  height: ${props => `${props.scale ? 136 * props.scale : 136}px`};
  background-color: ${props => props.theme.color.body};
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 24px;

  svg {
    width: 50%;
    height: 50%;
    display: block;
    fill: ${props => props.theme.color.inverted};
  }
`

export const IntroContent = styled.div`
  text-align: center;
  padding: 20px;
  margin: 0 auto;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;

  ${ButtonWrapper} {
    width: 100%;
    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }
`

export const IntroTitle = styled.h1`
  font-family: ${props => props.theme.font.tertiary};
  font-size: 32px;
  margin: 0 0 16px;
`

export const IntroDescription = styled.p`
  font-size: 15px;
  margin: 0 0 32px;
  line-height: 1.5;
`

export const IntroClose = styled.div`
  position: absolute;
  top: 14px;
  right: 18px;
  z-index: 2;
`

export const IntroWrapper = styled.div<{ type?: IntroType }>`
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background-color: ${props => props.theme.color.body};
  color: ${props => props.theme.color.global};
  z-index: 99999999;
  overflow: auto;

  ${props =>
    props.type === 'ONE' &&
    css`
      background-color: ${props => props.theme.color.primaryExtraLight};

      ${IntroContent} {
        padding-top: max(40px, 5vh);
        padding-bottom: max(40px, 5vh);
        flex-grow: 1;
      }

      ${IntroDescription} {
        margin-top: auto;
        font-size: 16px;
        line-height: 1.65;
        font-family: ${props => props.theme.font.tertiary};

        strong {
          font-family: ${props => props.theme.font.primary};
        }
      }

      ${IntroTitle} {
        font-size: 20px;
        line-height: 1.7;
        font-family: ${props => props.theme.font.tertiary};
      }
    `};

  ${props =>
    props.type === 'TWO' &&
    css`
      background-color: ${props => props.theme.color.body};
    `};
`

export const IntroVendor = styled.div<{ offsetBottom?: number }>`
  position: absolute;
  left: 0;
  right: 0;
  bottom: ${props => `${props.offsetBottom}px`};
  font-size: 10px;
  user-select: none;
  color: ${props => props.theme.color.mutedIntense};
  padding: 0 18px 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    display: block;
    height: 12px;
    margin-left: 0.15rem;
  }
`
