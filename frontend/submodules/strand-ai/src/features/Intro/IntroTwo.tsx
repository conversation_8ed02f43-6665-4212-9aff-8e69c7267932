import { useTranslation } from 'react-i18next'
import {
  IntroClose,
  IntroContent,
  IntroDescription,
  IntroLogo,
  IntroMedia,
  IntroTitle,
  IntroVideo,
  IntroWrapper
} from './Intro.styles'
import Button from '../../components/Button'
import useWidgetState from '../Widget/Widget.store'
import usePromptState from '../../components/Prompt/Prompt.store'
import { motion } from 'framer-motion'
import SVG from 'react-inlinesvg'
import logo from '../../assets/img/logo-main.svg'
import { IntroProps } from './Intro.types'

const IntroTwo = ({ type }: IntroProps) => {
  const { t } = useTranslation()
  const { close, setMode } = useWidgetState()
  const { setMode: setPromptMode } = usePromptState()
  return (
    <IntroWrapper
      as={motion.div}
      initial={false}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0, scale: 1.1 }}
      transition={{ duration: 0.2 }}
      type={type}
    >
      <IntroMedia>
        <IntroVideo src="/video/intro-video-sample.mp4" autoPlay playsInline muted loop></IntroVideo>
        <IntroLogo>
          {/* @ts-ignore: Suppress specific issue TS2786 */}
          <SVG src={logo}></SVG>
        </IntroLogo>
      </IntroMedia>
      <IntroContent>
        <IntroTitle>{t('introTwo.title')}</IntroTitle>
        <IntroDescription>{t('introTwo.description')}</IntroDescription>
        <Button
          variant="tertiary"
          icon="keyboard_voice"
          onClick={() => {
            setPromptMode('voice')
            setMode('chat')
          }}
        >
          {t('introTwo.voiceSearch')}
        </Button>
        <Button
          variant="link"
          onClick={() => {
            setMode('chat')
            setPromptMode('default')
          }}
        >
          {t('introTwo.startChat')}
        </Button>
      </IntroContent>
      <IntroClose>
        <Button type="oval" icon="close" onClick={() => close()}></Button>
      </IntroClose>
    </IntroWrapper>
  )
}

export default IntroTwo
