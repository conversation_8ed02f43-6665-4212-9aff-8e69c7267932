import { Trans, useTranslation } from 'react-i18next'
import {
  IntroClose,
  IntroContent,
  IntroDescription,
  IntroEmblem,
  IntroTitle,
  IntroVendor,
  IntroWrapper
} from './Intro.styles'
import Button from '../../components/Button'
import useWidgetState from '../Widget/Widget.store'
import usePromptState from '../../components/Prompt/Prompt.store'
import { motion } from 'framer-motion'
import SVG from 'react-inlinesvg'
import emblem from '../../assets/img/logo-emblem.svg'
import { IntroProps } from './Intro.types'
import logo from '../../assets/img/logo-footer.svg'

const IntroOne = ({ type, isStrandPropertiesMobile }: IntroProps) => {
  const { t } = useTranslation()
  const { close, setMode } = useWidgetState()
  const { setMode: setPromptMode } = usePromptState()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const logoSrc = typeof logo === 'string' ? logo : (logo as any).src
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const emblemSrc = typeof logo === 'string' ? emblem : (emblem as any).src

  return (
    <IntroWrapper
      as={motion.div}
      initial={false}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0, scale: 1.1 }}
      transition={{ duration: 0.2 }}
      type={type}
    >
      <IntroContent>
        <IntroEmblem scale={isStrandPropertiesMobile ? 0.5 : 1}>
          {/* @ts-ignore: Suppress specific issue TS2786 */}
          <SVG src={emblemSrc}></SVG>
        </IntroEmblem>
        <IntroTitle>{t('introOne.title')}</IntroTitle>
        <IntroDescription>
          <Trans i18nKey="introOne.description"></Trans>
        </IntroDescription>
        <Button
          variant="primary"
          icon="keyboard_voice"
          onClick={() => {
            setPromptMode('voice')
            setMode('chat')
          }}
          offsetBottom={isStrandPropertiesMobile ? 160 : 0}
        >
          {t('introOne.voiceSearch')}
        </Button>
        <Button
          variant="primary"
          icon="edit"
          onClick={() => {
            setMode('chat')
            setPromptMode('default')
          }}
          offsetBottom={isStrandPropertiesMobile ? 160 : 0}
        >
          {t('introOne.startChat')}
        </Button>
      </IntroContent>
      <IntroClose>
        <Button type="oval" icon="close" onClick={() => close()}></Button>
      </IntroClose>
      <IntroVendor offsetBottom={isStrandPropertiesMobile ? 150 : 0}>
        {/* @ts-ignore: Suppress specific issue TS2786 */}
        Powered by <SVG src={logoSrc}></SVG>
      </IntroVendor>
    </IntroWrapper>
  )
}

export default IntroOne
