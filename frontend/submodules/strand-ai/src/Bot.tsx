import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>t<PERSON>rap<PERSON> } from './Bot.styles'
import Widget from './features/Widget/Widget'
import useWidgetState from './features/Widget/Widget.store'
import { ChatProvider } from './features/Chat/Chat.context'
import { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useMediaQuery } from './utils/useMediaQuery'
import { ThemeProvider } from 'styled-components'
import theme from './lib/theme'
import './lib/i18n'
import { ChatPropertyResponse } from './features/Chat/Chat.types'
import { IntroType } from './features/Intro/Intro.types'

export interface BotProps {
  autoOpen?: boolean | number
  isOpen?: boolean
  introType?: IntroType
  onOpen?: () => void
  onClose?: () => void
  parseFiltersCallback?: (filters: ChatPropertyResponse) => void
  hideAiMessages?: boolean
  isStrandPropertiesMobile?: boolean
}

export type BotRef = {
  open?: () => void
  close?: () => void
  toggleFullscreen?: () => void
}

const Bot = forwardRef<BotRef, BotProps>(
  (
    {
      autoOpen,
      isOpen: isOpenProp,
      onOpen,
      onClose,
      parseFiltersCallback,
      introType = 'ONE',
      hideAiMessages,
      isStrandPropertiesMobile
    },
    ref
  ) => {
    const { isOpen, open, close, toggleFullscreen } = useWidgetState()
    const isMobile = useMediaQuery('<=sm')

    useImperativeHandle(ref, () => ({
      open,
      close,
      toggleFullscreen
    }))

    useEffect(() => {
      if (isOpenProp !== undefined) {
        if (isOpenProp) {
          open()
        } else {
          close()
        }
      }
    }, [isOpenProp, open, close])

    useEffect(() => {
      if (isOpen) {
        onOpen && onOpen()
      } else {
        onClose && onClose()
      }
    }, [isOpen])

    useEffect(() => {
      if (autoOpen) {
        setTimeout(() => {
          open()
        }, Number(autoOpen))
      }
    }, [])

    return (
      <ThemeProvider theme={theme}>
        <ChatProvider>
          <BotWrapper>
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.15 }}
                >
                  <Widget
                    isMobile={isMobile}
                    introType={introType}
                    hideAiMessages={hideAiMessages}
                    parseFiltersCallback={parseFiltersCallback}
                    isStrandPropertiesMobile={isStrandPropertiesMobile}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </BotWrapper>
        </ChatProvider>
      </ThemeProvider>
    )
  }
)

export default Bot
