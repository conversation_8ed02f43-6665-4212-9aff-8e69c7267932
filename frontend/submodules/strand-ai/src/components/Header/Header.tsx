import { useChat } from '../../features/Chat/useChat'
import useWidgetState from '../../features/Widget/Widget.store'
import { useMediaQuery } from '../../utils/useMediaQuery'
import Button from '../Button'
import { Header<PERSON>ogo, HeaderTools, HeaderWrapper } from './Header.styles'
import SVG from 'react-inlinesvg'
import logo from '../../assets/img/logo-main.svg'

const Header = () => {
  const isMobile = useMediaQuery('<=sm')
  const { resetChat } = useChat()
  const { close, isFullscreen, toggleFullscreen } = useWidgetState()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const logoSrc = typeof logo === 'string' ? logo : (logo as any).src

  return (
    <HeaderWrapper>
      <HeaderLogo>
        {/* @ts-ignore: Suppress specific issue TS2786 */}
        <SVG src={logoSrc}></SVG>
      </HeaderLogo>
      <HeaderTools>
        <Button type="oval" variant="secondary" icon="refresh" onClick={() => resetChat()}></Button>
        {!isMobile && (
          <Button
            type="oval"
            variant="secondary"
            icon={isFullscreen ? 'open_in_popup' : 'open_in_full'}
            onClick={() => toggleFullscreen()}
          ></Button>
        )}
        <Button
          type="oval"
          icon="close"
          onClick={() => {
            close()
          }}
        ></Button>
      </HeaderTools>
    </HeaderWrapper>
  )
}

export default Header
