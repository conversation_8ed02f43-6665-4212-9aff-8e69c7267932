import styled from 'styled-components'

export const HeaderWrapper = styled.header`
  display: flex;
  align-items: center;
  padding: 0 18px;
  height: 68px;
  flex-shrink: 0;
  width: 100%;
`

export const HeaderLogo = styled.div`
  img,
  svg {
    display: block;
    height: 23px;
    position: relative;
    top: 2px;
    fill: ${props => props.theme.color.global};
  }
`

export const HeaderTools = styled.nav`
  margin-left: auto;
  display: flex;
  gap: 6px;
  align-items: center;
`
