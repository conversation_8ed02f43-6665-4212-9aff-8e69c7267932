import Icon from '../Icon'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonWrapper } from './Button.styles'
import { ButtonProps } from './Button.types'
import { motion } from 'framer-motion'

const Button = ({
  variant = 'primary',
  type = 'default',
  children,
  icon,
  isLoading,
  onClick,
  disabled,
  offsetBottom
}: ButtonProps) => {
  const getIconSize = () => {
    switch (icon) {
      case 'refresh':
      case 'close':
      case 'open_in_full':
        return 'small'
      case 'edit':
        return 'large'
      default:
        return 'default'
    }
  }

  return (
    <ButtonWrapper
      variant={variant}
      type={type}
      isLoading={isLoading}
      onClick={onClick}
      iconSize={getIconSize()}
      disabled={disabled}
      offsetBottom={offsetBottom}
    >
      {children && !isLoading && <ButtonLabel>{children}</ButtonLabel>}
      {icon && !isLoading && <Icon name={icon} />}
      {isLoading && (
        <ButtonLoader
          as={motion.span}
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
        >
          <Icon name="app_badging" />
        </ButtonLoader>
      )}
    </ButtonWrapper>
  )
}
export default Button
