import styled, { css } from 'styled-components'
import { ButtonTypes, ButtonVariants } from './Button.types'
import { Cover, Reset, TransitionPrimary } from '../../lib/mixins'

export const ButtonWrapper = styled.button<{
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  as: any
  variant: ButtonVariants
  type: ButtonTypes
  isLoading?: boolean
  disabled?: boolean
  iconSize?: 'default' | 'small' | 'large'
  offsetBottom?: number
}>`
  ${Reset('button')};
  margin-bottom: ${props => `${props.offsetBottom}px`};
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  font-size: 12px;
  font-weight: 600;
  font-family: ${props => props.theme.font.secondary};
  letter-spacing: 0.05em;
  ${TransitionPrimary('background-color, color, opacity')};

  &:before {
    ${Cover()}
    ${TransitionPrimary('transform')};
  }

  span {
    position: relative;
  }

  svg {
    position: relative;
    display: block;
  }

  ${props =>
    props.type === 'default' &&
    css`
      height: 42px;
      &:before {
        border-radius: 30px;
      }
      svg {
        width: 16px;
        height: 16px;
        margin-left: 0.25em;
      }
    `};

  ${props =>
    props.type === 'default' &&
    !props.disabled &&
    css`
      &:hover {
        &:before {
          transform: scale(0.95, 0.925);
        }
      }
    `};

  ${props =>
    props.type === 'oval' &&
    css`
      width: 40px;
      height: 40px;
      border-radius: 20px;

      &:before {
        border-radius: 20px;
      }
    `};

  ${props =>
    props.type === 'oval' &&
    !props.disabled &&
    css`
      &:hover {
        &:before {
          transform: scale(0.9);
        }
      }
    `};

  ${props =>
    props.type === 'oval' &&
    props.iconSize === 'default' &&
    css`
      svg {
        width: 45%;
        height: 45%;
      }
    `};

  ${props =>
    props.type === 'oval' &&
    props.iconSize === 'small' &&
    css`
      svg {
        width: 35%;
        height: 35%;
      }
    `};

  ${props =>
    props.type === 'oval' &&
    props.iconSize === 'large' &&
    css`
      svg {
        width: 55%;
        height: 55%;
      }
    `};

  ${props =>
    props.type === 'default' &&
    props.iconSize === 'large' &&
    css`
      svg {
        width: 20px;
        height: 20px;
      }
    `};

  ${props =>
    props.variant === 'primary' &&
    css`
      color: ${props.theme.color.inverted};

      &:before {
        background-color: ${props.theme.color.global};
      }
    `};

  ${props =>
    props.variant === 'secondary' &&
    css`
      &:before {
        background-color: transparent;
        border: 1px solid ${props.theme.color.line};
      }
      color: inherit;
    `};

  ${props =>
    props.variant === 'tertiary' &&
    css`
      &:before {
        background-color: ${props => props.theme.color.primaryLight};
        color: ${props => props.theme.color.global};
      }
      color: inherit;
    `};

  ${props =>
    props.variant === 'link' &&
    css`
      &:before {
        background-color: transparent;
        border: 0;
      }
      ${ButtonLabel} {
        &:after {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 1px;
          background-color: currentColor;
          opacity: 0;
          transform: scaleX(0.6) translateY(3px);
          visibility: hidden;
          ${TransitionPrimary('transform, opacity, visibility')}
          content: ' ';
        }
      }
      color: inherit;
    `};

  ${props =>
    props.type === 'default' &&
    !props.disabled &&
    props.variant === 'link' &&
    css`
      &:hover {
        ${ButtonLabel} {
          &:after {
            opacity: 1;
            transform: scale(1) translateY(0);
            visibility: visible;
          }
        }
      }
    `};

  ${props =>
    props.variant === 'red' &&
    css`
      &:before {
        background-color: ${props.theme.color.red};
      }
      color: white;
    `};

  ${props =>
    props.variant === 'transparent' &&
    css`
      &:before {
        background-color: ${props.theme.color.light};
      }
      color: ${props.theme.color.global};
    `};

  ${props =>
    props.disabled &&
    css`
      opacity: 0.5;
      cursor: not-allowed;
    `};
`

export const ButtonLabel = styled.span`
  position: relative;
  white-space: nowrap;
`

export const ButtonLoader = styled.div`
  width: 45%;
  height: 45%;

  svg {
    width: 100%;
    height: 100%;
  }
`
