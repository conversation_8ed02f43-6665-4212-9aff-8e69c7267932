import SVG from 'react-inlinesvg'
import { IconProps } from './Icon.types'

import app_badging from '../../assets/icons/app_badging.svg'
import arrow_back from '../../assets/icons/arrow_back.svg'
import arrow_upward from '../../assets/icons/arrow_upward.svg'
import close_padded from '../../assets/icons/close_padded.svg'
import close from '../../assets/icons/close.svg'
import edit from '../../assets/icons/edit.svg'
import ios_share from '../../assets/icons/ios_share.svg'
import keyboard_voice from '../../assets/icons/keyboard_voice.svg'
import open_in_full from '../../assets/icons/open_in_full.svg'
import open_in_popup from '../../assets/icons/open_in_popup.svg'
import refresh from '../../assets/icons/refresh.svg'
import stop_circle from '../../assets/icons/stop_circle.svg'

const ICONS = {
  app_badging,
  arrow_back,
  arrow_upward,
  close_padded,
  close,
  edit,
  ios_share,
  keyboard_voice,
  open_in_full,
  open_in_popup,
  refresh,
  stop_circle
}

const Icon = ({ name }: IconProps) => {
  try {
    const src = ICONS[name as keyof typeof ICONS]
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const iconSrc = typeof src === 'string' ? src : (src as any).src

    // @ts-ignore: Suppress specific issue TS2786
    return <SVG src={iconSrc} preProcessor={code => code.replace(/fill=".*?"/g, 'fill="currentColor"')}></SVG>
  } catch (e) {
    console.error(`<Icon> Missing "${name}" icon`, e)
    return <div></div>
  }
}

export default Icon
