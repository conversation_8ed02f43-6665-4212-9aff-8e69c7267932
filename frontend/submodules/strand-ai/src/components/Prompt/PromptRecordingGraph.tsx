import { useEffect, useRef } from 'react'
import { PromptRecordingIcon } from './Prompt.styles'

const WIDTH = 252
const HEIGHT = 46

const PromptRecordingGraph = ({ stream }: { stream: MediaStream | null }) => {
  const animationRef = useRef<number>()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const audioCtxRef = useRef<AudioContext | null>(null)
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)

  useEffect(() => {
    if (stream) {
      setTimeout(() => {
        initCanvas()
      }, 500)
    }
    return () => {
      if (animationRef.current) cancelAnimationFrame(animationRef.current)
    }
  }, [stream])

  if (!stream) {
    return null
  }

  const initCanvas = () => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = WIDTH
    canvas.height = HEIGHT

    if (window.devicePixelRatio > 1) {
      const canvasWidth = WIDTH
      const canvasHeight = HEIGHT

      canvas.width = canvasWidth * window.devicePixelRatio
      canvas.height = canvasHeight * window.devicePixelRatio
    }

    audioCtxRef.current = new AudioContext()
    sourceRef.current = audioCtxRef.current.createMediaStreamSource(stream)
    analyserRef.current = audioCtxRef.current.createAnalyser()
    analyserRef.current.fftSize = 32
    sourceRef.current.connect(analyserRef.current)

    draw()
  }

  const draw = () => {
    if (!stream || !canvasRef.current) return

    const canvas = canvasRef.current
    const WIDTH = canvas.width
    const HEIGHT = canvas.height
    const ctx = canvasRef.current.getContext('2d')

    if (!ctx || !analyserRef.current) return

    const bufferLength = analyserRef.current.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    analyserRef.current.getByteFrequencyData(dataArray)

    ctx.clearRect(0, 0, WIDTH, HEIGHT)

    const barWidth = 2
    let barHeight
    let x = 0

    for (let i = 0; i < bufferLength; i++) {
      barHeight = dataArray[i] / 4

      ctx.fillStyle = `rgb(255,255,255)`
      ctx.fillRect(x, HEIGHT / 2 - barHeight / 2, barWidth, barHeight)

      x += barWidth + WIDTH / bufferLength
    }

    // ctx.fillStyle = 'rgb(50,50,50)'
    // ctx.fillRect(20, 20, WIDTH, HEIGHT)

    animationRef.current = requestAnimationFrame(draw)
  }

  return (
    <PromptRecordingIcon>
      <canvas ref={canvasRef}></canvas>
    </PromptRecordingIcon>
  )
}

export default PromptRecordingGraph
