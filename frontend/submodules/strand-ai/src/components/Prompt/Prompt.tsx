import { ChangeEvent, useEffect, useRef, useState } from 'react'
import {
  PromptAction,
  PromptActionItem,
  PromptInput,
  PromptMessage,
  PromptOverlay,
  PromptStatus,
  PromptWrapper
} from './Prompt.styles'
import PromptRecording from './PromptRecording'
import { AnimatePresence, motion } from 'framer-motion'
import Button from '../Button'
import { PromptProps, PromptRecordingRef } from './Prompt.types'
import { useChat } from '../../features/Chat/useChat'
import { useTranslation } from 'react-i18next'
import usePromptState from './Prompt.store'

const Prompt = ({ onChange }: PromptProps) => {
  const { mode, setMode, setIsTouched } = usePromptState()
  const [status, setStatus] = useState<'default' | 'listening' | 'processing'>('default')
  const [loading, setLoading] = useState<boolean>(false)
  const { status: chatStatus } = useChat()
  const [value, setValue] = useState<string>('')
  const recordingRef = useRef<PromptRecordingRef>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const isLoading = chatStatus === 'loading' || loading
  const { t } = useTranslation()

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value)
  }

  const handleInputFocus = () => {
    setMode('text')
  }

  const stopRecording = () => {
    recordingRef.current?.stopRecording()
  }

  const startRecording = () => {
    setMode('voice')
  }

  const handleSubmit = () => {
    setIsTouched(true)
    if (isLoading || !value) return
    if (onChange) onChange({ message: value })
    if (inputRef.current) {
      inputRef.current.value = ''
      inputRef.current.blur()
    }
    resetInput()
  }

  const resetInput = () => {
    setMode('default')
    setValue('')
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit()
    }
    if (e.key === 'Escape') {
      resetInput()
    }
  }

  const handleRecordEnd = ({ message }: { message: string }) => {
    setIsTouched(true)
    if (onChange) onChange({ message })
    setLoading(false)
    setStatus('default')
    setMode('default')
  }

  const actionProps = {
    as: motion.div,
    initial: { opacity: 0, scale: 0.8, x: -8 },
    animate: { opacity: 1, scale: 1, x: 0 },
    exit: { opacity: 0, scale: 0.8, x: 8 }
  }

  useEffect(() => {
    if (mode === 'default') {
      setStatus('default')
    }
  }, [mode])

  return (
    <PromptWrapper>
      <PromptMessage addMargin={mode === 'voice'}>
        <PromptInput
          ref={inputRef}
          placeholder="Explain what you're looking for..."
          active={mode === 'text'}
          onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange(e)}
          onFocus={handleInputFocus}
          onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => handleInputKeyDown(e)}
          hide={status !== 'default'}
        ></PromptInput>
        <PromptAction>
          {isLoading && (
            <PromptActionItem key="loading" {...actionProps}>
              <Button type="oval" variant="transparent" isLoading={true} onClick={stopRecording}></Button>
            </PromptActionItem>
          )}
          {!isLoading && mode === 'default' && (
            <PromptActionItem key="voice" {...actionProps}>
              <Button type="oval" icon="keyboard_voice" onClick={startRecording}></Button>
            </PromptActionItem>
          )}
          {!isLoading && mode === 'text' && (
            <PromptActionItem key="text" {...actionProps}>
              <Button type="oval" icon="arrow_upward" onClick={handleSubmit} disabled={value.length === 0}></Button>
            </PromptActionItem>
          )}
          {!isLoading && mode === 'voice' && (
            <PromptActionItem key="stopRecording" {...actionProps}>
              <Button type="oval" variant="red" icon="stop_circle" onClick={stopRecording}></Button>
            </PromptActionItem>
          )}
        </PromptAction>
        {mode === 'voice' && status === 'listening' && <PromptStatus>{t('listening')}</PromptStatus>}
        {mode === 'voice' && status === 'processing' && <PromptStatus>{t('transcribing')}</PromptStatus>}
      </PromptMessage>
      <AnimatePresence>
        {mode === 'voice' && (
          <PromptRecording
            ref={recordingRef}
            onRecordEnd={handleRecordEnd}
            onProcessing={() => {
              setLoading(true)
              setStatus('processing')
            }}
            onRecordStart={() => setStatus('listening')}
            onCancel={() => {
              setStatus('default')
              setMode('default')
            }}
          />
        )}
      </AnimatePresence>
      {mode === 'text' && <PromptOverlay onClick={() => setMode('default')} />}
    </PromptWrapper>
  )
}
export default Prompt
