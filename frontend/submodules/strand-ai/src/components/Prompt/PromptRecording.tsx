import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import {
  PromptRecordingBody,
  PromptRecordingButton,
  PromptRecordingStatus,
  PromptRecordingTime,
  PromptRecordingVisualizer,
  PromptRecordingWrapper
} from './Prompt.styles'
import { AnimatePresence, motion } from 'framer-motion'
import Icon from '../Icon'
import { PromptRecordingProps, PromptRecordingRef } from './Prompt.types'
import { useReactMediaRecorder } from 'react-media-recorder'
import PromptRecordingGraph from './PromptRecordingGraph'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import api from '../../lib/api'

const PromptRecording = forwardRef<PromptRecordingRef, PromptRecordingProps>(
  ({ onRecordEnd, onProcessing, onRecordStart, onCancel }, ref) => {
    const [status, setStatus] = useState<'initial' | 'recording' | 'loading' | 'exit'>('initial')
    const [startTime, setStartTime] = useState<number | null>(null)
    const [currentTime, setCurrentTime] = useState<number | null>(null)
    const { t } = useTranslation()
    const intervalRef = useRef<NodeJS.Timeout | undefined>(undefined)

    const bodyVariants = {
      recording: { opacity: 1, y: 0 },
      loading: { opacity: 0, y: 10 },
      exit: { opacity: 0, y: 10 }
    }

    const statusVariants = {
      recording: { opacity: 0, y: -10 },
      loading: { opacity: 1, y: 0 },
      exit: { opacity: 1, scale: 0.9 }
    }

    const transition = {
      ease: 'easeOut',
      duration: 0.2
    }

    const submitRecording = async (_blobUrl: string, blob: Blob) => {
      clearInterval(intervalRef.current)

      const formData = new FormData()

      formData.append('file', blob, 'audio.wav')

      if (onProcessing) onProcessing()
      setStatus('loading')

      api
        .post('/voice', formData, { headers: { 'Content-Type': 'multipart/form-data' } })
        .then(res => {
          setStatus('exit')
          if (onRecordEnd) onRecordEnd({ message: res.data.message })
        })
        .catch(() => {
          cancelRecording()
        })
    }

    const handleStartRecording = () => {
      setStatus('recording')
      setStartTime(Date.now())
      setCurrentTime(Date.now())
      intervalRef.current = setInterval(() => {
        setCurrentTime(Date.now())
      }, 1000) as NodeJS.Timeout

      if (onRecordStart) onRecordStart()
    }

    const cancelRecording = () => {
      setStatus('exit')
      if (onCancel) onCancel()
    }

    const {
      startRecording,
      stopRecording,
      previewAudioStream,
      error: streamError
    } = useReactMediaRecorder({
      mediaRecorderOptions: {
        mimeType: 'audio/wav'
      },
      onStart: handleStartRecording,
      onStop: submitRecording
    })

    const showMessage = status === 'initial' || status === 'loading' || streamError

    useImperativeHandle(
      ref,
      () => ({
        stopRecording: () => {
          if (streamError && onCancel) {
            onCancel()
          } else {
            stopRecording()
          }
        }
      }),
      [stopRecording]
    )

    useEffect(() => {
      startRecording()

      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          cancelRecording()
        }
      }

      window.addEventListener('keydown', handleKeyDown)
      return () => {
        window.removeEventListener('keydown', handleKeyDown)
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
      <PromptRecordingWrapper as={motion.div} initial={{ height: 0 }} exit={{ height: 0 }} animate={{ height: 125 }}>
        <PromptRecordingBody as={motion.div} transition={transition} variants={bodyVariants} animate={status}>
          {!streamError && status === 'recording' && (
            <>
              {currentTime && startTime && (
                <PromptRecordingTime>{dayjs(currentTime - startTime).format('mm:ss')}</PromptRecordingTime>
              )}
              <PromptRecordingVisualizer>
                <PromptRecordingGraph stream={previewAudioStream} />
              </PromptRecordingVisualizer>
              <PromptRecordingButton onClick={stopRecording}>
                <Icon name="stop_circle"></Icon> {t('stopRecording')}
              </PromptRecordingButton>
            </>
          )}
        </PromptRecordingBody>
        <AnimatePresence>
          {showMessage && (
            <PromptRecordingStatus
              as={motion.div}
              transition={transition}
              variants={statusVariants}
              animate={status}
              initial="initial"
            >
              {!streamError && status === 'initial' && <>{t('pleaseWait')}</>}
              {!streamError && status === 'loading' && <>{t('processing')}</>}
              {streamError && <>{t(`errorCode.${streamError}`)}</>}
            </PromptRecordingStatus>
          )}
        </AnimatePresence>
      </PromptRecordingWrapper>
    )
  }
)

export default PromptRecording
