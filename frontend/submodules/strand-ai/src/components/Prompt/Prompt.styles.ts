import styled, { css } from 'styled-components'
import { Cover, Reset, TransitionPrimary } from '../../lib/mixins'

export const PromptWrapper = styled.div`
  flex: 0 0 auto;
`
export const PromptOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
`

export const PromptMessage = styled.div<{ addMargin: boolean }>`
  position: relative;
  display: flex;
  padding: 0 8px;
  border-radius: 8px;
  background-color: ${props => props.theme.color.light};
  margin-bottom: ${props => (props.addMargin ? '8px' : '0')};
  min-height: 48px;
  z-index: 2;
`
export const PromptAction = styled.div`
  position: relative;
  padding-top: 4px;
  z-index: 2;
`

export const PromptActionItem = styled.div``

export const PromptInput = styled.textarea<{ active: boolean; hide: boolean }>`
  ${Reset('all')}
  flex: 1 1 auto;
  padding: 14px 8px;
  font-size: 15px;
  resize: none;
  height: 48px;
  ${TransitionPrimary('height, opacity, visibility')}
  font-family: ${props => props.theme.font.primary};

  ${props =>
    props.hide &&
    css`
      opacity: 0;
      visibility: hidden;
    `};

  ${props =>
    props.active &&
    css`
      height: 100px;
    `};

  &:focus,
  &:active {
    outline: none;
    box-shadow: none;
    appearance: none;
    resize: none;
    border: 0;
  }

  &::placeholder {
    color: ${props => props.theme.color.muted};
  }
`

export const PromptStatus = styled.div`
  ${Cover()}
  padding: 16px 18px;
  font-size: 12px;
`

export const PromptRecordingWrapper = styled.div`
  position: relative;
  background-color: ${props => props.theme.color.global};
  color: ${props => props.theme.color.inverted};
  border-radius: 8px;
  text-transform: uppercase;
  font-size: 12px;
  text-align: center;
  z-index: 2;
`
export const PromptRecordingVisualizer = styled.div`
  position: relative;
  max-width: min(252px, 80%);
  width: 252px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  height: 46px;
`
export const PromptRecordingIcon = styled.div`
  width: 100%;

  canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
`
export const PromptRecordingTime = styled.div`
  position: absolute;
  left: 12px;
  top: 8px;
  font-size: 12px;
`
export const PromptRecordingBody = styled.div`
  ${Cover()}
  position: relative;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 18px 12px;
`
export const PromptRecordingButton = styled.button`
  ${Reset('button')}
  display: inline-flex;
  padding: 12px;
  align-items: center;
  text-transform: uppercase;

  svg {
    margin-right: 8px;
    height: 16px;
    width: 16px;
  }
`
export const PromptRecordingStatus = styled.div`
  ${Cover()}
  display: flex;
  align-items: center;
  justify-content: center;
`
