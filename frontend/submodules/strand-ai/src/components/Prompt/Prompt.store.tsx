import { create } from 'zustand'

type PromptMode = 'default' | 'text' | 'voice'

interface PromptState {
  mode: PromptMode
  isTouched: boolean
  setMode: (mode: PromptMode) => void
  setIsTouched: (isTouched: boolean) => void
}

const usePromptState = create<PromptState>(set => ({
  mode: 'default',
  isTouched: false,
  setMode: (mode: PromptMode) => set({ mode }),
  setIsTouched: (isTouched: boolean) => set({ isTouched })
}))

export default usePromptState
