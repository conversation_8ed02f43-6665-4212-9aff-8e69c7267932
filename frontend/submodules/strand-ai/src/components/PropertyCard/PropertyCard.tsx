import { useTranslation } from 'react-i18next'
import {
  PropertyCardContent,
  PropertyCardDetails,
  PropertyCardFeatures,
  PropertyCardHeader,
  PropertyCardImage,
  PropertyCardPrice,
  PropertyCardSize,
  PropertyCardWrapper
} from './PropertyCard.styles'
import { PropertyCardProps } from './PropertyCard.types'
import { useEffect, useRef } from 'react'
import { formatPrice } from '../../utils/formatPrice'

const PropertyCard = ({ price, size, beds, baths, details, image, name, onClick }: PropertyCardProps) => {
  const { t } = useTranslation()
  const wrapperRef = useRef<HTMLAnchorElement>(null)

  const handleMouseIn = () => {
    const siblings = wrapperRef.current?.parentElement?.children
    if (siblings) {
      Array.from(siblings).forEach(sibling => {
        if (sibling !== wrapperRef.current) {
          ;(sibling as HTMLElement).style.opacity = '0.65'
        }
      })
      wrapperRef.current.style.opacity = '1'
    }
  }
  const handleMouseOut = () => {
    const siblings = wrapperRef.current?.parentElement?.children
    if (siblings) {
      Array.from(siblings).forEach(sibling => {
        ;(sibling as HTMLElement).style.opacity = '1'
      })
    }
  }

  useEffect(() => {
    const propertyCardWrapper = wrapperRef.current

    if (propertyCardWrapper) {
      propertyCardWrapper.addEventListener('mouseover', handleMouseIn)
      propertyCardWrapper.addEventListener('mouseout', handleMouseOut)
    }
    return () => {
      if (propertyCardWrapper) {
        propertyCardWrapper.removeEventListener('mouseover', handleMouseIn)
        propertyCardWrapper.removeEventListener('mouseout', handleMouseOut)
      }
    }
  }, [])

  return (
    <PropertyCardWrapper ref={wrapperRef} onClick={onClick}>
      <PropertyCardImage>{image && <img src={`${image}?width=360`} alt={name} />}</PropertyCardImage>
      <PropertyCardContent>
        <PropertyCardHeader>
          <PropertyCardPrice>
            {price ? formatPrice({ price, currency: 'EUR', removeDecimals: true }) : '-'}
          </PropertyCardPrice>
          <PropertyCardSize>{size || '-'} M²</PropertyCardSize>
        </PropertyCardHeader>
        <PropertyCardFeatures>
          <li>
            {beds} {t('bed')}
          </li>
          <li>
            {baths} {t('bath')}
          </li>
        </PropertyCardFeatures>
        <PropertyCardDetails>
          {details.slice(0, 4).map((detail, index) => (
            <li key={index}>{detail}</li>
          ))}
        </PropertyCardDetails>
      </PropertyCardContent>
    </PropertyCardWrapper>
  )
}

export default PropertyCard
