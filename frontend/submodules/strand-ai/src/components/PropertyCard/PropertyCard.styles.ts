import styled from 'styled-components'
import { Reset, TransitionPrimary } from '../../lib/mixins'

export const PropertyCardWrapper = styled.a`
  color: inherit;
  text-decoration: none;
  ${TransitionPrimary('opacity')};
  cursor: pointer;
`

export const PropertyCardImage = styled.figure`
  margin: 0;
  padding: 0;
  position: relative;
  padding-bottom: 70%;
  background-color: ${props => props.theme.color.light};
  border-radius: 6px 6px 0 0;

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    width: 100%;
    border-radius: 6px 6px 0 0;
  }
`

export const PropertyCardContent = styled.div`
  border: 1px solid ${props => props.theme.color.line};
  border-top: 0;
  border-radius: 0 0 6px 6px;
  padding: 12px;
  font-size: 13px;
  min-height: 106px;
`

export const PropertyCardHeader = styled.div`
  font-weight: bold;
  display: flex;
  align-content: center;
  margin-bottom: 12px;
`

export const PropertyCardPrice = styled.span`
  margin-right: auto;
  padding-right: 12px;
`

export const PropertyCardSize = styled.span`
  font-size: 0.8em;
`

export const PropertyCardFeatures = styled.ul`
  ${Reset('list')}
  font-size: 11px;
  font-weight: bold;
  color: ${props => props.theme.color.primary};
  text-transform: uppercase;
  margin-bottom: 0.25em;
  li {
    display: inline-block;

    &:not(:last-child):after {
      content: ', ';
      margin-right: 0.2em;
    }
  }
`

export const PropertyCardDetails = styled.ul`
  ${Reset('list')}
  font-size: 10px;
  li {
    display: inline-block;

    &:not(:last-child):after {
      content: ', ';
      margin-right: 0.2em;
    }
  }
`
