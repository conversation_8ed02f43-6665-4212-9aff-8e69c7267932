<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <title>StrandAI</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .toggler {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999;
        background-color: white;
        border-radius: 50%;
        border: 1px solid black;
        transition: all 0.15s;
      }
      .toggler svg {
        width: 50%;
        height: 50%;
        object-fit: contain;
        fill: black;
        transition: all 0.15s;
      }
      .toggler:hover {
        background-color: black;
      }
      .toggler:hover svg {
        fill: white;
      }
    </style>
  </head>
  <body>
    <!-- Demo -->
    <div class="my-20">
      <div class="w-100 max-w-screen-md mx-4 md:mx-20">
        <!-- Installation -->
        <div class="pt-6 space-y-16">
          <div>
            <img src="/img/logo-main.svg" alt="" style="max-width: 700px; width: 90%" />
            <p class="font-thin text-2xl md:text-2xl">Powered by Strand AI</p>
          </div>
          <div class="space-y-4">
            <h4 class="text-xl font-bold">Installation:</h4>
            <pre class="p-6 bg-slate-100 text-sm md:text-base overflow-auto overflow-auto">
&lt;!-- Component --&gt;
&lt;strand-bot&gt;&lt;/strand-bot&gt;

&lt;!-- Script --&gt;
&lt;script src="./strand-bot.umd.js"&gt;&lt;/script&gt;
</pre
            >
          </div>
          <!-- Properties -->
          <div class="space-y-4">
            <h4 class="text-xl font-bold">Properties:</h4>
            <table class="table-auto w-full">
              <thead>
                <tr>
                  <th class="border-b font-medium py-4 pr-8 text-left text-slate-400">Property</th>
                  <th class="border-b font-medium py-4 pr-8 text-left text-slate-400">Type</th>
                  <th class="border-b font-medium py-4 text-left text-slate-400">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="border-b font-medium py-4 pr-8 text-left md:text-lg whitespace-nowrap">auto-open</td>
                  <td class="border-b font-medium py-4 pr-8 text-left md:text-lg">number</td>
                  <td class="border-b py-4 text-left text-sm md:text-lg">
                    Initiates the bot after a specified duration in milliseconds.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- Methods -->
          <div class="space-y-4">
            <h4 class="text-xl font-bold">Methods:</h4>
            <div class="space-y-8">
              <!-- Open -->
              <div class="space-y-2">
                <p class="font-bold">
                  <code class="mr-3">.open()</code>
                  <a href="#" data-toggle="open" class="inline-block py-2 px-4 border-1 border-black border-y border-x"
                    >Click to open</a
                  >
                </p>
                <pre class="p-6 bg-slate-100 text-sm md:text-base overflow-auto">
document.querySelector('strand-bot').open()</pre
                >
              </div>
              <!-- Close -->
              <div class="space-y-2">
                <p class="font-bold">
                  <code class="mr-3">.close()</code>
                  <a href="#" data-toggle="close" class="inline-block py-2 px-4 border-1 border-black border-y border-x"
                    >Click to close</a
                  >
                </p>
                <pre class="p-6 bg-slate-100 text-sm md:text-base overflow-auto">
document.querySelector('strand-bot').close()</pre
                >
              </div>
              <!-- Toggle Fulscreen -->
              <div class="space-y-2">
                <p class="font-bold">
                  <code class="mr-3">.toggleFullscreen()</code>
                  <a
                    href="#"
                    data-toggle="fullscreen"
                    class="inline-block py-2 px-4 border-1 border-black border-y border-x"
                    >Click to toggle fullscreen</a
                  >
                </p>
                <pre class="p-6 bg-slate-100 text-sm md:text-base overflow-auto">
document.querySelector('strand-bot').toggleFullscreen()</pre
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toggler -->
    <button class="toggler" data-toggle="open">
      <svg viewBox="0 0 18 27">
        <path
          d="M12.6649 12.7246C12.2185 12.5608 11.7892 12.4079 11.3784 12.2668C10.9671 12.1266 10.562 11.9925 10.1635 11.8628C9.76406 11.7348 9.3585 11.5994 8.94767 11.4583C8.5364 11.3181 8.09573 11.1552 7.62652 10.9665C6.42872 10.5223 5.45432 9.92516 4.70201 9.17387C3.95102 8.42433 3.57443 7.42886 3.57443 6.18661C3.57443 5.39033 3.70962 4.68097 3.98043 4.06028C4.2508 3.43959 4.62827 2.91718 5.11152 2.49567C5.59302 2.07416 6.14649 1.71729 6.80619 1.54694C11.1515 0.423498 13.6121 4.62462 14.0593 5.3864C14.507 6.14817 15.1338 7.93642 15.3216 8.64928H15.7105V0.898734H15.3216C14.8862 1.24817 14.0044 1.85139 13.5756 2.04489C13.5524 2.04489 10.8578 0.673783 8.72075 0.715279C7.59053 0.737555 6.70085 0.814432 5.67641 1.10752C4.65241 1.40105 3.7478 1.85095 2.96389 2.46116C2.18042 3.07049 1.55672 3.8314 1.09191 4.74561C0.627091 5.65939 0.39622 6.74921 0.39622 8.01417C0.39622 8.76503 0.524823 9.47308 0.784663 10.1405C1.04319 10.8079 1.3851 11.4181 1.80866 11.9685C2.23222 12.5197 2.72732 13.0002 3.29177 13.4091C3.85709 13.8201 4.44524 14.1424 5.05841 14.3761C5.60004 14.5875 6.06441 14.7575 6.45373 14.8863C6.8413 15.0147 7.21833 15.1445 7.58307 15.2724C7.94825 15.4017 8.34284 15.5363 8.7664 15.6765C9.19083 15.8176 9.68505 15.9932 10.2508 16.2046C10.8148 16.4147 11.3446 16.6492 11.8393 16.9065C12.3344 17.166 12.7636 17.4748 13.1288 17.8391C13.494 18.2025 13.7819 18.6297 13.9939 19.1215C14.2055 19.6133 14.3117 20.1886 14.3117 20.8447C14.3117 22.4613 13.7696 23.7433 12.6868 24.6924C11.6036 25.6425 10.0862 25.9827 8.34372 25.9469C5.65885 25.8919 3.00076 23.0977 1.93156 20.8172C1.28064 19.429 1.01071 18.6808 0.80617 17.6626H0.369446L0.398853 26.5051H0.717069C1.05416 26.2003 1.79856 25.597 2.46221 25.0432C3.34575 25.5525 5.76156 26.6148 8.60005 26.7091C11.3898 26.8017 13.8078 25.6455 15.1061 24.7462C15.9532 24.1595 16.555 23.3528 17.0137 22.4613C17.4728 21.5715 17.7028 20.5621 17.7028 19.4382C17.7028 17.7744 17.2621 16.3858 16.3816 15.2724C15.5012 14.1608 14.2621 13.3099 12.6649 12.7246Z"
        />
      </svg>
    </button>

    <!-- Component -->
    <strand-bot></strand-bot>

    <!-- Script -->
    <script type="module" src="/src/main.tsx"></script>
    <script>
      const bot = document.querySelector('strand-bot')

      document.querySelectorAll('[data-toggle="open"]').forEach(el => {
        el.addEventListener('click', () => bot.open())
      })

      document.querySelectorAll('[data-toggle="close"]').forEach(el => {
        el.addEventListener('click', () => bot.close())
      })

      document.querySelectorAll('[data-toggle="fullscreen"]').forEach(el => {
        el.addEventListener('click', () => bot.toggleFullscreen())
      })
    </script>
  </body>
</html>
