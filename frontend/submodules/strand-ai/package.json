{"name": "strand-bot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc && TARGET=demo vite build && TARGET=react vite build && TARGET=web-component vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@types/body-scroll-lock": "^3.1.2", "@types/react-outside-click-handler": "^1.3.3", "axios": "^1.7.4", "body-scroll-lock": "^4.0.0-beta.0", "dayjs": "^1.11.10", "fetch-sse": "^1.0.23", "framer-motion": "^11.0.24", "fs": "^0.0.1-security", "gsap": "^3.12.5", "i18next": "^23.11.1", "lodash": "^4.17.21", "polished": "^4.3.1", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^14.1.0", "react-inlinesvg": "^4.1.3", "react-media-recorder": "1.6.6", "react-outside-click-handler": "^1.3.0", "sse.js": "^2.4.1", "styled-components": "^6.1.8", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.12.12", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-unused-imports": "^3.1.0", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-mkcert": "^1.17.5"}, "peerDependencies": {}}