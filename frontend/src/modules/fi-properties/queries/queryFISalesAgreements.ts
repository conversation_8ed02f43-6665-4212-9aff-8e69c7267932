import { axiosInstance } from '@/lib/axiosConfig'
import { getSession } from 'next-auth/react'
import { GetServerSidePropsContext } from 'next/types'

import { FISalesAgreementCreate } from '../types/FISalesAgreement'
import {
  SchemaFiSalesAgreementList,
  SchemaFiSalesAgreementRead,
} from '@/generated-types/api'
import { Signer } from '@/utils/signingUtils'
import { useQuery, UseQueryResult } from '@tanstack/react-query'

export const getFISalesAgreementsForProperty = async (
  params?: { propertyId: string },
  context?: GetServerSidePropsContext
): Promise<SchemaFiSalesAgreementList> => {
  const session = await getSession(context)

  const { data } = await axiosInstance.get<SchemaFiSalesAgreementList>(
    '/fi-sales-agreement',
    {
      params,
      headers: {
        Authorization: `Bearer ${session?.token}`,
      },
    }
  )

  return data
}

export const createFISalesAgreement = async (
  payload: FISalesAgreementCreate & {
    validate?: boolean
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  let url = `/fi-sales-agreement`
  if (payload.validate !== undefined) {
    url += `?shouldValidate=${payload.validate}`
  }
  const { data } = await axiosInstance.post(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}

export const editFISalesAgreement = async (
  payload: Partial<FISalesAgreementCreate> & {
    salesAgreementId: string
    validate?: boolean
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  let url = `/fi-sales-agreement/${payload.salesAgreementId}`
  if (payload.validate !== undefined) {
    url += `?shouldValidate=${payload.validate}`
  }
  const { data } = await axiosInstance.patch(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}

export const getFISalesAgreement = async (
  id: string,
  context?: GetServerSidePropsContext
): Promise<SchemaFiSalesAgreementRead> => {
  const session = await getSession(context)

  const { data } = await axiosInstance.get(`/fi-sales-agreement/${id}`, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}

export const useFISalesAgreement = (
  salesAgreementId: string
): UseQueryResult<SchemaFiSalesAgreementRead> => {
  return useQuery<SchemaFiSalesAgreementRead>({
    queryKey: getFISalesAgreementsQueryKey(salesAgreementId),
    queryFn: () => getFISalesAgreement(salesAgreementId),
    enabled: !!salesAgreementId,
  })
}

export const getFISalesAgreementsQueryKey = (salesAgreementId: string) => [
  'fi-sales-agreement',
  salesAgreementId,
]

export const getFISalesAgreementAsPdf = async (
  id: string,
  context?: GetServerSidePropsContext
): Promise<void> => {
  const session = await getSession(context)

  try {
    const response = await axiosInstance.get(
      `/documents/${id}/pdf?template_enum=sales_agreement.html`,
      {
        headers: {
          Authorization: `Bearer ${session?.token}`,
        },
        responseType: 'blob', // Important: treat response as binary
      }
    )

    // Create a blob URL and trigger download
    const url = window.URL.createObjectURL(
      new Blob([response.data], { type: 'application/pdf' })
    )
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `sales_agreement_${id}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
    console.error('Error downloading sales agreement PDF:', error)
  }
}
export const getFISalesAgreementAsPreview = async (
  id: string,
  context?: GetServerSidePropsContext
): Promise<string> => {
  const session = await getSession(context)

  const { data } = await axiosInstance.get(
    `/documents/${id}/preview?template_enum=sales_agreement.html`,
    {
      headers: {
        Authorization: `Bearer ${session?.token}`,
      },
    }
  )
  return data
}

export const sendSalesAgreementForSigning = async (
  payload: Partial<FISalesAgreementCreate> & {
    salesAgreementId: string
    lastSigningDate: string
    comment: string
    language: string
    signers: Signer[]
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  const url = `/fi-sales-agreement/${payload.salesAgreementId}/send-for-signing`
  const { data } = await axiosInstance.post(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}

export const fetchDocumentSignignStatus = async (
  payload: Partial<FISalesAgreementCreate> & {
    salesAgreementId: string
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  const url = `/fi-sales-agreement/${payload.salesAgreementId}/fetch-signing-status`
  const { data } = await axiosInstance.get(url, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}

export const sendReminderForSigners = async (
  payload: Partial<FISalesAgreementCreate> & {
    salesAgreementId: string
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  const url = `/fi-sales-agreement/${payload.salesAgreementId}/remind-signers`
  const { data } = await axiosInstance.get(url, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })
  return data
}
