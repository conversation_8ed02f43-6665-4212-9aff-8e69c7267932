import {
  SchemaFiDetailsOfSaleCreate,
  SchemaFiDetailsOfSaleEdit,
  SchemaFiDetailsOfSalePrefillRead,
  SchemaFiDetailsOfSaleRead,
} from '@/generated-types/api'
import { axiosInstance } from '@/lib/axiosConfig'
import { useQuery } from '@tanstack/react-query'
import { GetServerSidePropsContext } from 'next'
import { getSession } from 'next-auth/react'

export const getFIDetailsOfSaleForProperty = async (
  params?: {
    propertyId: string
  },
  context?: GetServerSidePropsContext
): Promise<SchemaFiDetailsOfSaleRead[]> => {
  const session = await getSession(context)
  const { data } = await axiosInstance.get<SchemaFiDetailsOfSaleRead[]>(
    '/fi-details-of-sale',
    {
      params,
      headers: {
        Authorization: `Bearer ${session?.token}`,
      },
    }
  )

  return data
}

export const createFIDetailsOfSale = async (
  payload: Omit<SchemaFiDetailsOfSaleCreate, 'status'> & {
    validate?: boolean
  },
  context?: GetServerSidePropsContext
): Promise<SchemaFiDetailsOfSaleRead> => {
  const session = await getSession(context)
  let url = `/fi-details-of-sale`
  if (payload.validate !== undefined) {
    url += `?shouldValidate=${payload.validate}`
  }
  const { data } = await axiosInstance.post(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })

  return data
}

export const getFIDetailsOfSale = async (
  id: string,
  context?: GetServerSidePropsContext
): Promise<SchemaFiDetailsOfSaleRead> => {
  const session = await getSession(context)
  const { data } = await axiosInstance.get(`/fi-details-of-sale/${id}`, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })

  return data
}

export const useFiDetailsOfSale = (id: string) => {
  return useQuery({
    queryKey: ['fi-details-of-sale', id],
    queryFn: () => getFIDetailsOfSale(id),
    enabled: !!id,
  })
}

export const editFIDetailsOfSale = async (
  payload: Partial<SchemaFiDetailsOfSaleEdit> & {
    detailsOfSaleId: string
    validate?: boolean
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  const id = payload.detailsOfSaleId
  let url = `/fi-details-of-sale/${id}`
  if (payload.validate !== undefined) {
    url += `?shouldValidate=${payload.validate}`
  }
  const { data } = await axiosInstance.patch(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })

  return data
}

export const updateFiDetailsOfSaleStatus = async (
  payload: Pick<SchemaFiDetailsOfSaleEdit, 'status'> & {
    detailsOfSaleId: string
  },
  context?: GetServerSidePropsContext
) => {
  const session = await getSession(context)
  const { data } = await axiosInstance.patch(
    `/fi-details-of-sale/${payload.detailsOfSaleId}/update-status`,
    { status: payload.status },
    { headers: { Authorization: `Bearer ${session?.token}` } }
  )
  return data
}

export const getFiDetailsOfSalePrefill = async (
  propertyId: number,
  context?: GetServerSidePropsContext
): Promise<SchemaFiDetailsOfSalePrefillRead> => {
  const session = await getSession(context)
  const { data } = await axiosInstance.get(
    `/fi-details-of-sale/prefill/${propertyId}`,
    {
      headers: { Authorization: `Bearer ${session?.token}` },
    }
  )
  return data
}
