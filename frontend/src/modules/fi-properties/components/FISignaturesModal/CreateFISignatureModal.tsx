import { getUsers } from '@/queries/users'
import { Heading, Stack, Text } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useTranslation } from 'next-i18next'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { DeviceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import * as Yup from 'yup'
import { InputWithLabel } from '@/components/Form/Input'
import LabeledField from '@/components/Form/LabeledField'
import SelectDropdown from '@/components/DropdownMenu/SelectDropdown'
import { MultiSelectDropdown } from '@/components/DropdownMenu/MultiSelectDropdown'
import FICustomerSelect from '@/components/FICustomerSelect'
import { TextArea } from '@/components/Form/TextArea'
import {
  ContactType,
  SchemaDocumentLibraryItemRead,
} from '@/generated-types/api'
import { useDocumentTypeTranslator } from '@/modules/document-library/hooks/useDocumentTypeTranslator'
import { formatDate, formatDateAndTime } from '@/utils/date'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { useMobile } from '@/hooks/useMobile'

const validationSchema = (
  t: (key: string, option?: Record<string, string>) => string,
  signers: { realtors?: boolean; contacts?: boolean }
) => {
  const schema: Record<string, Yup.AnySchema> = {
    lastSigningDate: Yup.string().required().label(t('lastSigningDate')),
    language: Yup.mixed<'fi' | 'en'>()
      .oneOf(['fi', 'en'])
      .required()
      .label(t('language')),
    comment: Yup.string().label(t('comment')),
  }

  if (signers.realtors) {
    schema.realtorUserIds = Yup.array()
      .of(Yup.number().required())
      .min(1, t('oneRealtorRequired'))
      .required(t('oneRealtorRequired'))
      .label(t('realtorUserIds'))
  }

  if (signers.contacts) {
    schema.contactIds = Yup.array()
      .of(Yup.number().required())
      .min(1, t('oneContactRequired'))
      .required(t('oneContactRequired'))
      .label(t('contactIds'))
  }

  return Yup.object().shape(schema)
}

export const CreateFISignatureModal = ({
  isSaving,
  onSubmitMutation,
  onClose,
  items = [],
  initialValues,
  dateMode = 'date',
  signers = {
    realtors: true,
    contacts: true,
  },
}: {
  isSaving: boolean
  onSubmitMutation: (values: SigningFormProps) => void
  onClose: () => void
  items?: SchemaDocumentLibraryItemRead[]
  initialValues?: Partial<SigningFormProps>
  dateMode?: 'date' | 'datetime'
  signers: { realtors: boolean; contacts: boolean }
}) => {
  const { t } = useTranslation(['common'])

  const onSubmit = async (values: SigningFormProps) => {
    onSubmitMutation(values)
  }

  const handleAcceptClick = async (formik: FormikProps<SigningFormProps>) => {
    await formik.setTouched({
      realtorUserIds: true,
      contactIds: true,
      lastSigningDate: true,
      comment: true,
      language: true,
    })
    formik.validateForm().then(() => {
      formik.submitForm()
    })
  }

  const completeInitialValues = {
    lastSigningDate: '',
    realtorUserIds: [],
    contactIds: [],
    comment: '',
    language: 'fi',
    ...initialValues,
  } satisfies SigningFormProps

  return (
    <Formik
      initialValues={completeInitialValues}
      onSubmit={onSubmit}
      validationSchema={validationSchema(t, signers)}
      validateOnMount
    >
      {(formik) => (
        <DeviceAdapter
          isOpen={true}
          onClose={onClose}
          onAccept={() => handleAcceptClick(formik)}
          onReject={onClose}
          title={t('signing.sendForSigning')}
          acceptTitle={t('send') ?? ''}
          disableAcceptButton={formik.isSubmitting || isSaving}
          dirty={formik.dirty}
          isSubmitting={formik.isSubmitting}
        >
          <CreateFiSignaturesFormBody
            {...formik}
            items={items}
            signers={signers}
            dateMode={dateMode}
          />
        </DeviceAdapter>
      )}
    </Formik>
  )
}

export interface SigningFormProps {
  lastSigningDate: string
  language: string
  comment: string
  realtorUserIds: number[]
  contactIds: number[]
}

type SigningDocumentItem = Pick<
  SchemaDocumentLibraryItemRead,
  'filename' | 'documentType' | 'createdAt'
>

const CreateFiSignaturesFormBody = ({
  values,
  errors,
  setFieldValue,
  items = [],
  signers = {
    realtors: true,
    contacts: true,
  },
  dateMode,
}: FormikProps<SigningFormProps> & {
  items?: SigningDocumentItem[]
  signers?: { realtors?: boolean; contacts?: boolean }
  dateMode: 'date' | 'datetime'
}) => {
  const translateDocumentType = useDocumentTypeTranslator()

  const { t } = useTranslation(['common'])
  const isMobile = useMobile()
  const { organization } = useUserAndOrganization()
  const { paginationProps: realtors } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        onlyActive: true,
        organizationId: organization?.id,
      }),
  })

  return (
    <Stack paddingBottom={20}>
      <Stack gap="1rem" direction={isMobile ? 'column' : 'row'}>
        <InputWithLabel
          label={t('signing.lastSigningDate')}
          name="lastSigningDate"
          defaultValue={values.lastSigningDate}
          type={dateMode === 'date' ? 'date' : 'datetime-local'}
          error={errors.lastSigningDate}
          min={formatDate(
            new Date(),
            undefined,
            dateMode === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DDTHH:mm'
          )}
          max="9999-12-31"
          style={{ height: '50px' }}
          required={true}
        />
        <LabeledField label={t('language')} width={'100%'}>
          <SelectDropdown
            onChange={(value) => setFieldValue('language', value)}
            valueKey="value"
            labelKey="label"
            data={[
              { label: 'Suomi', value: 'fi' },
              { label: 'English', value: 'en' },
            ]}
            defaultValue={'fi'}
            name="language"
          />
        </LabeledField>
      </Stack>
      <LabeledField label={`${t('signing.comment')}`} width="100%">
        <TextArea
          name="comment"
          size="sm"
          defaultValue={values.comment}
          onBlur={(e) => setFieldValue('comment', e.currentTarget.value)}
        />
      </LabeledField>
      {items.length > 0 && (
        <Stack>
          <Text fontSize="small" fontWeight={350}>
            {t('signing.selectedDocuments')}
          </Text>
          {items?.map((item) => (
            <Stack key={item.filename} borderBottom="1px solid #F2F2F2" gap={0}>
              <Text flex="4" mt={0} fontSize="small" fontWeight={800}>
                {translateDocumentType(item.documentType)}
              </Text>
              <Text flex="8" mt={0} variant="small">
                {item.filename} {formatDateAndTime(item.createdAt)}
              </Text>
            </Stack>
          ))}
        </Stack>
      )}

      <Heading variant="H1">{t('signing.signers')}</Heading>
      <Stack gap="1rem">
        {signers.realtors && (
          <LabeledField label={t('realtors')} required={true}>
            <MultiSelectDropdown
              name="realtorUserIds"
              placement="bottomStart"
              valueKey="id"
              labelKey="email"
              {...realtors}
              error={errors.realtorUserIds}
            />
          </LabeledField>
        )}
        {signers.contacts && (
          <FICustomerSelect
            label={t('contacts')}
            name="contactIds"
            querySource="contacts"
            filterContactFn={(contact) => contact.type === ContactType.Person}
          />
        )}
      </Stack>
    </Stack>
  )
}
