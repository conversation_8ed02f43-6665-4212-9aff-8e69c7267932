import { Stack, useToast } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useTranslation } from 'next-i18next'
import { DeviceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import * as Yup from 'yup'
import FICustomerSelect from '@/components/FICustomerSelect'
import { ContactType } from '@/generated-types/api'
import { useMutation } from '@tanstack/react-query'
import { addSigner } from '../../queries/queryFIDocumentSigning'
import { getToastProps } from '@/utils/toastProps'
import { mapContactIdToSigner } from '@/utils/signingUtils'
import LabeledField from '@/components/Form/LabeledField'
import SelectDropdown from '@/components/DropdownMenu/SelectDropdown'

const validationSchema = (
  t: (key: string, option?: Record<string, string>) => string
) => {
  return Yup.object().shape({
    contactIds: Yup.array()
      .of(Yup.number().required())
      .min(1, t('oneContactRequired'))
      .required(t('oneContactRequired'))
      .label(t('contactIds')),
    language: Yup.mixed<'fi' | 'en'>()
      .oneOf(['fi', 'en'])
      .required()
      .label(t('language')),
  })
}

export const AddSignerModal = ({
  isOpen,
  onClose,
  documentSigningId,
}: {
  isOpen: boolean
  onClose: () => void
  documentSigningId: number
}) => {
  const { t } = useTranslation(['common'])
  const toast = useToast()

  const { mutate: addSigners, isPending: isAddingSigners } = useMutation({
    mutationFn: addSigner,
    onSuccess: () => {
      toast(
        getToastProps({
          title: t('signing.signerAdded'),
          description: t('signing.signersAdded'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
      onClose()
    },
    onError: (_error) => {
      toast(
        getToastProps({
          title: t('error'),
          description: t('signing.failedAddingSigners'),
          status: 'error',
        })
      )
    },
  })

  const onSubmit = async (values: AddSignerFormProps) => {
    addSigners({
      documentSigningId,
      signer: mapContactIdToSigner(values.contactIds[0]),
      language: 'en',
    })
  }

  const handleAcceptClick = async (formik: FormikProps<AddSignerFormProps>) => {
    await formik.setTouched({
      contactIds: true,
    })
    formik.validateForm().then(() => {
      formik.submitForm()
    })
  }

  const initialValues: AddSignerFormProps = {
    contactIds: [],
    language: 'fi',
  }

  if (!isOpen) return null

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={validationSchema(t)}
      validateOnMount
    >
      {(formik) => (
        <DeviceAdapter
          isOpen={true}
          onClose={onClose}
          onAccept={() => handleAcceptClick(formik)}
          onReject={onClose}
          title={t('signing.addSigner')}
          acceptTitle={t('add') ?? ''}
          disableAcceptButton={formik.isSubmitting || isAddingSigners}
          dirty={formik.dirty}
          isSubmitting={formik.isSubmitting}
        >
          <AddSignerFormBody {...formik} />
        </DeviceAdapter>
      )}
    </Formik>
  )
}

export interface AddSignerFormProps {
  contactIds: number[]
  language: 'fi' | 'en'
}

const AddSignerFormBody = ({
  setFieldValue,
}: FormikProps<AddSignerFormProps>) => {
  const { t } = useTranslation(['common'])

  return (
    <Stack paddingBottom={20}>
      <Stack gap="1rem">
        <FICustomerSelect
          label={t('signing.signer')}
          name="contactIds"
          querySource="contacts"
          singleSelect={true}
          filterContactFn={(contact) => contact.type === ContactType.Person}
          required
        />
        <LabeledField label={t('language')} width={'100%'}>
          <SelectDropdown
            onChange={(value) => setFieldValue('language', value)}
            valueKey="value"
            labelKey="label"
            data={[
              { label: 'Suomi', value: 'fi' },
              { label: 'English', value: 'en' },
            ]}
            defaultValue={'fi'}
            name="language"
          />
        </LabeledField>
      </Stack>
    </Stack>
  )
}
