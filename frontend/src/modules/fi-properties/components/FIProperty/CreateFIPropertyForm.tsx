import { getUsers } from '@/queries/users'
import {
  CreateFIPropertyMutationProps,
  FIListingTypeEnum,
  FIOwnershipTypeEnum,
  FIPropertyCreateEdit,
  FIPropertyTypeGroupEnum,
} from '../../types'
import { Flex, useToast, Text, Alert, AlertIcon, Link } from '@chakra-ui/react'
import { useMutation } from '@tanstack/react-query'
import { Formik, FormikProps } from 'formik'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import * as Yup from 'yup'
import { useAuth } from '@/hooks/useAuth'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { getContacts } from '@/queries/contact'
import { useFIProperties } from '../../hooks'
import { getFullName } from '@/types/users'
import CreateFIPropertyFormBody from './CreateFIPropertyFormBody'
import { createFIProperty } from '../../queries/queryFIProperties'
import { DeviceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import { useMobile } from '@/hooks/useMobile'
import { COUNTRIES } from '@/modules/country'
import { DataSource, StatusPortals } from '@/types/property'
import {
  CoordinateAccuracyEnum,
  CoordinateSourceEnum,
} from '@/modules/google-map'
import { removeTemporaryFields } from '../../utils/TypeUtil'

const validationSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object({
    isExclusive: Yup.boolean().required().label(t('isExclusive')),

    realtorUserIds: Yup.array()
      .of(Yup.number().required())
      .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
      .label(t('realtors')),

    contactIds: Yup.array()
      .of(Yup.number().required())
      .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
      .label(t('contacts')),

    _ownershipType: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('fiProperty.fields.ownershipType')),

    _listingType: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('fiProperty.fields.listingType')),

    _propertyTypeGroup: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('fiProperty.fields.propertyTypeGroup')),

    _propertyType: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('fiProperty.fields.propertyType')),

    fiRealty: Yup.object({
      fiAddress: Yup.object({
        streetAddress: Yup.string()
          .label(t('addressForm.fields.streetAddress'))
          .required(t('errors.fieldRequired')),
        postalCode: Yup.string()
          .label(t('addressForm.fields.postalCode'))
          .required(t('errors.fieldRequired')),
        municipality: Yup.string()
          .required(t('errors.fieldRequired'))
          .label(t('addressForm.fields.municipality')),
        subregion: Yup.string(),
        region: Yup.string(),

        location: Yup.object({
          latitude: Yup.number()
            .required(t('errors.fieldRequired'))
            .positive(t('errors.mustBePositive'))
            .label(t('addressForm.fields.latitude')),
          longitude: Yup.number()
            .required(t('errors.fieldRequired'))
            .positive(t('errors.mustBePositive'))
            .label(t('addressForm.fields.longitude')),
          coordinateSource: Yup.string()
            .oneOf(Object.values(CoordinateSourceEnum))
            .label(t('addressForm.fields.coordinateSource')),
          coordinateAccuracy: Yup.string()
            .oneOf(Object.values(CoordinateAccuracyEnum))
            .label(t('addressForm.fields.coordinateAccuracy')),
        }),
      }),
    }),
  })

export default function CreateFIPropertyForm({
  onClose,
}: {
  onClose: () => void
}) {
  const { t } = useTranslation(['common'])
  const router = useRouter()
  const isMobile = useMobile()
  const toast = useToast()
  const { updateFIPropertyState } = useFIProperties()

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        pageSize: 120,
        onlyActive: true,
      }).then((data) => ({
        records: data.records.map((user) => ({
          value: user.id,
          label: getFullName(user),
        })),
        metadata: data.metadata,
      })),
  })

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }).then((data) => ({
          records: data.records.map((contact) => ({
            value: contact.id,
            label: contact.name,
          })),
          metadata: data.metadata,
        })),
    }
  )

  const {
    authStates: { currentUser },
  } = useAuth()

  const initialValues: CreateFIPropertyMutationProps = {
    // FIPropertyCreate
    isExclusive: true,
    dataSource: DataSource.STRAND,
    fiPropertyTypeId: 0,

    fiRealty: {
      currencyCode: COUNTRIES.FINLAND.defaultCurrency,
      fiAddress: {
        streetAddress: '',
        stairwell: '',
        apartmentNumber: '',
        postalCode: '',
        district: '',
        municipality: '',
        subregion: '',
        region: '',
        country: COUNTRIES.FINLAND.name,

        location: {
          latitude: 0,
          longitude: 0,
          coordinateSource: CoordinateSourceEnum.GEO_CODING,
          coordinateAccuracy: CoordinateAccuracyEnum.UNKNOWN,
        },
      },
    },

    realtorUserIds: currentUser ? [currentUser.id] : [],
    contactIds: [],

    _listingType: FIListingTypeEnum.SALE,
    _ownershipType: FIOwnershipTypeEnum.PROPERTY,
    _propertyTypeGroup: FIPropertyTypeGroupEnum.RESIDENTIAL,
    _propertyType: undefined,
    portals: {} as unknown as StatusPortals,
  }

  const {
    isPending: isCreatingFIPropertyLoading,
    mutate: mutationCreateFIProperty,
  } = useMutation({
    mutationKey: ['property'],
    mutationFn: createFIProperty,
    onSuccess: (propertyCreated) => {
      onClose()
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        title: `${t('fiProperty.messages.propertyCreatedWithId', {
          id: propertyCreated.id,
        })}`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
        render() {
          return (
            <Alert
              status="success"
              backgroundColor="alert.success.bg"
              borderRadius={10}
              boxShadow="none"
              display="flex"
              alignContent={'center'}
            >
              <AlertIcon />
              <Flex direction={'column'}>
                <Text>{`${t('fiProperty.messages.propertyCreatedWithId', {
                  id: propertyCreated.id,
                })}`}</Text>
                <Text fontSize={16} fontWeight={400}>
                  {t('fiProperty.messages.canViewProperty')}{' '}
                  <Link
                    color="black"
                    textDecoration="underline"
                    onClick={() => {
                      router.push({
                        pathname: `/properties/${propertyCreated.reference}`,
                        query: { callbackUrl: router.asPath },
                      })
                    }}
                  >
                    {t('here')}
                  </Link>
                </Text>
              </Flex>
            </Alert>
          )
        },
      })
      updateFIPropertyState(propertyCreated, 'updateFIProperty')
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        title: t('errors.title'),
        description: t('errors.propertyNotCreated'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const onSubmit = async (values: CreateFIPropertyMutationProps) => {
    const valuesWithoutUnderscore = removeTemporaryFields({ ...values })
    mutationCreateFIProperty(valuesWithoutUnderscore as FIPropertyCreateEdit)
  }

  const handleAcceptClick = async (
    formik: FormikProps<CreateFIPropertyMutationProps>
  ) => {
    await formik.setTouched({
      isExclusive: true,
      _listingType: true,
      _ownershipType: true,
      _propertyTypeGroup: true,
      _propertyType: true,
      fiPropertyTypeId: true,
      fiRealty: {
        fiAddress: true,
      },
      realtorUserIds: true,
      contactIds: true,
      commission: true,
      commissionType: true,
    })
    formik.validateForm().then(() => {
      formik.submitForm()
    })
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={validationSchema(t)}
      validateOnMount
    >
      {(formik) => (
        <DeviceAdapter
          isOpen={true}
          onClose={onClose}
          onAccept={() => handleAcceptClick(formik)}
          onReject={onClose}
          title={t('createProperty.title')}
          acceptTitle={t('create') || undefined}
          isLoading={isCreatingFIPropertyLoading}
          disableAcceptButton={formik.isSubmitting}
          dirty={formik.dirty}
          isSubmitting={formik.isSubmitting}
        >
          <CreateFIPropertyFormBody
            t={t}
            usersPaginationProps={usersPaginationProps}
            sellersPaginationProps={sellersPaginationProps}
          />
        </DeviceAdapter>
      )}
    </Formik>
  )
}
