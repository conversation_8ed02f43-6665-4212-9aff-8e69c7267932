import { Box, Flex, SimpleGrid, Text } from '@chakra-ui/react'
import PropertyCard from '../../../../components/PropertyCard'

import { CustomPagination } from '@/components/Pagination/Pagination'
import { useTranslation } from 'next-i18next'
import { formatFIPropertyAreaUnit } from '../../utils/FIPropertyFormatters'
import { FIPropertyListRead, Status } from '../../types'
import { useLocaleInfo } from '@/hooks/useLocaleInfo'
import { getFIPropertyPriceBasedOnType } from '../../utils/FIPropertyUtils'
import { formatCurrency } from '@/utils/formatNumber'
import { LocaleEnum } from '@/types/localeInfo'

interface Props {
  data: FIPropertyListRead[]
  onClickProperty: (reference: string) => void
  totalItems: number
  pageSize: number
  currentPage: number
  handlePageChange: (page: number) => void
  onChangePageSize: (pageSize: number) => void
}

const FIPropertyGridPanel = ({
  data = [],
  onClickProperty,
  totalItems,
  pageSize,
  currentPage,
  handlePageChange,
  onChangePageSize,
}: Props) => {
  const { t } = useTranslation(['common'])
  const localeInfo = useLocaleInfo()

  return (
    <Box>
      <SimpleGrid
        columns={{ base: 1, md: 2, xl: 3, '2xl': 4 }}
        width="100%"
        gap="4"
      >
        {localeInfo &&
          data.map((property) => {
            const price = getFIPropertyPriceBasedOnType(property)
            const formattedPrice =
              formatCurrency({
                locale: LocaleEnum.FI,
                value: price,
                currency: property.fiRealty.currencyCode,
                removeDecimals: true,
              }) ?? '-'

            const location = [
              property.fiRealty.fiAddress?.district,
              property.fiRealty.fiAddress?.municipality,
            ]
              .filter((item) => !!item)
              .join(' ')
            const propertyType = [
              property.fiPropertyType.propertyTypeGroup,
              property.fiPropertyType.propertyType,
            ].join(' > ')

            const propertyArea = formatFIPropertyAreaUnit(property, localeInfo)
            return (
              <PropertyCard
                key={property.reference}
                isPublished={Status.PUBLISHED === property.fiRealty.status}
                isStrandified={property.isStrandified}
                mainImg={property.mainImg}
                priceSale={formattedPrice}
                commission={property.fiPropertyType.listingType}
                realtorUsers={property.realtorUsers}
                location={location}
                propertyType={`${propertyType}\n${propertyArea}`}
                builtArea={''}
                status={property.fiRealty.status}
                soldBy={property.soldBy}
                onClick={() => onClickProperty(property.reference)}
              />
            )
          })}
      </SimpleGrid>
      <Flex
        mt="4"
        border="1px solid"
        borderColor="grays.grayBorder"
        justifyContent="space-between"
        alignItems="center"
        height="8"
        p={['2', '8']}
        direction={['column', 'row']}
      >
        <Text variant="medium" color="grays.darkGray">{`${t(
          'viewing'
        )} ${Math.min(currentPage * pageSize, totalItems)} / ${totalItems} ${t(
          'properties'
        )}`}</Text>
        <CustomPagination
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
          handleChangeLimit={onChangePageSize}
        />
      </Flex>
    </Box>
  )
}

export default FIPropertyGridPanel
