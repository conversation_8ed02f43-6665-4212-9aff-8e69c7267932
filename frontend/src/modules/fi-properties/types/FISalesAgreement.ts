import {
  SchemaFiSalesAgreementCreate,
  FISalesAgreementStatusEnum,
  SchemaFiSalesAgreementContactAcquisitionBase,
} from '@/generated-types/api'

export type FISalesAgreementCreate = Omit<
  SchemaFiSalesAgreementCreate,
  'createdBy'
>
export const emptyFiSalesAgreement: Omit<
  SchemaFiSalesAgreementCreate,
  'propertyId' | 'createdBy' | 'contactAcquisitions'
> = {
  agreedMarketingMethods: null,
  separateMarketingAppendix: null,
  unencumberedPriceRequest: null,
  averageSellingTimeEstimate: null,
  unencumberedPriceRequestEstimate: null,
  sharesIncludeLoan: null,
  loanAmount: null,
  loanDetails: null,
  paymentTermOther: null,
  factorsAffectingSales: null,
  dateWhenAvailable: null,
  availabilityDetails: null,
  tenantName: null,
  tenantContactDetails: null,
  leaseAgreementTerm: null,
  leaseTerminated: null,
  tenantHasPaidRentOnTime: null,
  tenantPayingRentDetails: null,
  leaseAgreementDetails: null,
  restrictiveRightOfUser: null,
  restrictiveRightOfUserDetails: null,
  belongsToBusinessActivities: false,
  assignmentValidity: null,
  startDate: null,
  endDate: null,
  commissionFixed: null,
  commissionPercentage: null,
  commissionDetails: null,
  otherExpensesDetails: null,
  digitalTradingAllowed: null,
  startAssignmentImmediately: null,
  startMarketingAfterCancelPeriod: null,
  customerAskedToReadPrivacyPolicy: null,
  additionalDetails: null,
  paymentTerms: null,
  availability: null,
  leaseAgreement: null,
  commissionBasisCode: null,
  commissionType: null,
  vat: null,
  realtorUserIds: [],
  contactIds: [],
  consenterIds: [],
  leaseAmount: null,
  leaseDeposit: null,
  leaseStartDate: null,
  leaseEndDate: null,
  assignmentValidityRenewalPeriod: null,
  expenseIfNoCommission: null,
  previousExternalSalesAgreement: null,
  previousExternalSalesAgreementDetails: null,
  priceIncludingLoan: null,
  writtenConsentToTransfer: null,
  shareRegisterFormat: null,
  shareRegisterStorage: null,
  unpaidMaintenanceCharge: null,
  unpaidMaintenanceChargeAmount: null,
  isDomesticSale: null,
  status: FISalesAgreementStatusEnum.draft,
  commissionOther: null,
}

export const createEmptyContactAcquisition = (
  contactId: number
): SchemaFiSalesAgreementContactAcquisitionBase => ({
  contactId,
  acquisition: null,
  sellerIsMarriedOrInRegisteredRelationship: null,
  sellerHasBeenMarriedOrInRegisteredRelationship: null,
  sellerHasSpousesConsent: null,
  legalPartitioningIsComplete: null,
  divorceLegallyBinding: null,
  acquisitionDate: null,
  acquisitionCost: null,
  clientHasUsedResidenceAsResidence: null,
  residencyStartDate: null,
  residencyEndDate: null,
  taxConsequence: null,
})
