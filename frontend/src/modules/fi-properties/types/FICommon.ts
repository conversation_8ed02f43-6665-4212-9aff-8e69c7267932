export enum FIChoiceEnum {
  YES = 'YES',
  NO = 'NO',
}

export interface FITranslatedText {
  languageCode: string
  text: string
  title?: string
}

export interface FIDescriptionTranslate {
  tagline: string
  description: string
  type: string
  language: string
}

export enum FIAreaUnitCodeEnum {
  SQUARE_METERS = 'M2',
  HECTARES = 'HA',
}

export enum FIEnergyCertificateTypeCodeEnum {
  A_2007 = 'A_2007',
  B_2007 = 'B_2007',
  C_2007 = 'C_2007',
  D_2007 = 'D_2007',
  E_2007 = 'E_2007',
  F_2007 = 'F_2007',
  G_2007 = 'G_2007',
  A_2013 = 'A_2013',
  B_2013 = 'B_2013',
  C_2013 = 'C_2013',
  D_2013 = 'D_2013',
  E_2013 = 'E_2013',
  F_2013 = 'F_2013',
  G_2013 = 'G_2013',
  A_2018 = 'A_2018',
  B_2018 = 'B_2018',
  C_2018 = 'C_2018',
  D_2018 = 'D_2018',
  E_2018 = 'E_2018',
  F_2018 = 'F_2018',
  G_2018 = 'G_2018',
  H = 'H',
  NOT_AVAILABLE = 'NOT_AVAILABLE',
  NOT_REQUIRED = 'NOT_REQUIRED',
}

export interface FICommonArea {
  value: number
  areaUnitCode: FIAreaUnitCodeEnum
}

export interface FIEnergyCertificate {
  typeCode?: FIEnergyCertificateTypeCodeEnum
  description?: FITranslatedText
}

export enum FICoordinateAccuracyEnum {
  STREET = 'STREET',
  ADDRESS = 'ADDRESS',
  UNKNOWN = 'UNKNOWN',
}

export enum FICoordinateSourceEnum {
  GEO_CODING = 'GEO_CODING',
  MANUAL = 'MANUAL',
}

export interface Location {
  latitude: number
  longitude: number
  coordinateAccuracy: FICoordinateAccuracyEnum
  coordinateSource: FICoordinateSourceEnum
}

export interface FIAsbestosMapping {
  isAsbestosMappingDone?: FIChoiceEnum
  isAsbestosMappingReportAvailable?: FIChoiceEnum
  constructionMaterialsMightHaveAsbestos?: FIChoiceEnum
  description?: FITranslatedText[]
}

export enum FIConstructionMaterialCodeEnum {
  BRICK = 'BRICK',
  CONCRETE = 'CONCRETE',
  ELEMENT = 'ELEMENT',
  STEEL = 'STEEL',
  STONE = 'STONE',
  TIMBER = 'TIMBER',
  WOOD = 'WOOD',
  OTHER = 'OTHER',
}

export interface FIConstructionMaterials {
  typeCode?: FIConstructionMaterialCodeEnum
  typeOtherDescription?: FITranslatedText[]
  description?: FITranslatedText[]
}

export enum FIOuterRoofTypeCodeEnum {
  GABLED = 'GABLED',
  HIPPED = 'HIPPED',
  PENT = 'PENT',
  FLAT = 'FLAT',
  GAMBREL = 'GAMBREL',
  MANSARD = 'MANSARD',
  SATERI = 'SATERI',
  OTHER = 'OTHER',
}

export enum FIOuterRoofMaterialCodeEnum {
  BRICK = 'BRICK',
  SHEET_METAL = 'SHEET_METAL',
  FELT = 'FELT',
  BITUMEN_FELT = 'BITUMEN_FELT',
  REINFORCED_CONCRETE = 'REINFORCED_CONCRETE',
  PVC = 'PVC',
  STONE_COATED_METAL = 'STONE_COATED_METAL',
  COPPER = 'COPPER',
  GREEN_ROOF = 'GREEN_ROOF',
  OTHER = 'OTHER',
}

export interface FIOuterRoof {
  typeCode?: FIOuterRoofTypeCodeEnum
  typeOtherDescription?: FITranslatedText[]
  materialCode?: FIOuterRoofMaterialCodeEnum
  otherMaterialDescription?: FITranslatedText[]
  description?: FITranslatedText[]
}

export enum FIHeatingSystemCodeEnum {
  DISTRICT_HEATING = 'DISTRICT_HEATING',
  ELECTRIC = 'ELECTRIC',
  GAS = 'GAS',
  GEOTHERMAL_HEATING = 'GEOTHERMAL_HEATING',
  EXHAUST_AIR_HEAT_PUMP = 'EXHAUST_AIR_HEAT_PUMP',
  OIL = 'OIL',
  SUN = 'SUN',
  WATER_HEAT_PUMP = 'WATER_HEAT_PUMP',
  WOOD = 'WOOD',
  OTHER = 'OTHER',
}

export enum FIHeatDistributionSystemCodeEnum {
  AIR_HEAT_PUMP = 'AIR_HEAT_PUMP',
  ELECTRIC_CEILING_HEATING = 'ELECTRIC_CEILING_HEATING',
  ELECTRIC_RADIATOR = 'ELECTRIC_RADIATOR',
  ELECTRIC_UNDERFLOOR_HEATING = 'ELECTRIC_UNDERFLOOR_HEATING',
  WATER_RADIATOR = 'WATER_RADIATOR',
  WATER_UNDERFLOOR_HEATING = 'WATER_UNDERFLOOR_HEATING',
  OTHER = 'OTHER',
}

export interface FIHeating {
  systemCodes?: FIHeatingSystemCodeEnum[]
  systemOtherDescription?: FITranslatedText[]
  distributionSystemCodes?: FIHeatDistributionSystemCodeEnum[]
  distributionSystemOtherDescription?: FITranslatedText[]
  description?: FITranslatedText[]
}

export interface FIResidentialPropertyOilTank {
  locationDescription?: FITranslatedText[]
  inspectionDescription?: FITranslatedText[]
  manufacturingYear?: number
}

export enum FIVentilationSystemCodeEnum {
  FORCED_EXHAUST = 'FORCED_EXHAUST',
  FORCED_INTAKE = 'FORCED_INTAKE',
  GRAVITY_BASED = 'GRAVITY_BASED',
  HEAT_RECOVERY = 'HEAT_RECOVERY',
  HEATING_AND_COOLING = 'HEATING_AND_COOLING',
}

export interface FIVentilation {
  systemCodes?: FIVentilationSystemCodeEnum[]
  systemsDescription?: FITranslatedText[]
}

export enum FITelevisionTypeCodeEnum {
  ANTENNA = 'ANTENNA',
  CABLE = 'CABLE',
  IPTV = 'IPTV',
  SATELLITE = 'SATELLITE',
}

export interface FITelevision {
  typeCodes?: FITelevisionTypeCodeEnum[]
  description?: FITranslatedText[]
}

export enum FIParkingSpaceTypeCodeEnum {
  CARPORT_SPACE = 'CARPORT_SPACE',
  CARPORT_SPACE_WITH_ELECTRICAL_PLUG = 'CARPORT_SPACE_WITH_ELECTRICAL_PLUG',
  CHARGING_POINT_FOR_ELECTRICAL_CARS = 'CHARGING_POINT_FOR_ELECTRICAL_CARS',
  GARAGE = 'GARAGE',
  OUTDOOR_PARKING_SPACE = 'OUTDOOR_PARKING_SPACE',
  OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG = 'OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG',
  PARKING_HALL_SPACE = 'PARKING_HALL_SPACE',
  PARKING_SPACE_SHARE = 'PARKING_SPACE_SHARE',
}

export enum FIRenovationStatusCodeEnum {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  FINISHED = 'FINISHED',
}

export enum FIRenovationTypeCodeEnum {
  KITCHEN = 'KITCHEN',
  BATHROOM = 'BATHROOM',
  FLOOR = 'FLOOR',
  PLUMBING = 'PLUMBING',
  FACADE = 'FACADE',
  ROOF = 'ROOF',
  BALCONY = 'BALCONY',
  WINDOW = 'WINDOW',
  LIFT = 'LIFT',
  ELECTRICAL = 'ELECTRICAL',
  SUBDRAINAGE = 'SUBDRAINAGE',
  OTHER = 'OTHER',
}

export enum FIResidentialTypeCodeEnum {
  APARTMENT_HOUSE = 'APARTMENT_HOUSE',
  DETACHED_HOUSE = 'DETACHED_HOUSE',
  ROW_HOUSE = 'ROW_HOUSE',
  SEMI_DETACHED_HOUSE = 'SEMI_DETACHED_HOUSE',
  SEPARATE_HOUSE = 'SEPARATE_HOUSE',
  WOODEN_HOUSE_APARTMENT = 'WOODEN_HOUSE_APARTMENT',
  BALCONY_ACCESS_BLOCK = 'BALCONY_ACCESS_BLOCK',
  COTTAGE = 'COTTAGE',
  TIME_SHARE_APARTMENT = 'TIME_SHARE_APARTMENT',
  LEISURE_APARTMENT = 'LEISURE_APARTMENT',
  OTHER = 'OTHER',
}

export enum FIAdministrationTypeCodeEnum {
  APARTMENT_HOUSING_COMPANY = 'APARTMENT_HOUSING_COMPANY',
  REAL_ESTATE_COMPANY = 'REAL_ESTATE_COMPANY',
  PART_OWNERSHIP = 'PART_OWNERSHIP',
  OTHER = 'OTHER',
}

export interface FIAdministration {
  typeCode?: FIAdministrationTypeCodeEnum
  typeOtherDescription?: FITranslatedText[]
}

export enum FIRealtyInspectionTypeCodeEnum {
  CONDITION_INSPECTION = 'CONDITION_INSPECTION',
  HUMIDITY_INSPECTION = 'HUMIDITY_INSPECTION',
}

export interface FIRealtyInspection {
  date?: string
  description?: FITranslatedText[]
  typeCode: FIRealtyInspectionTypeCodeEnum
}

export enum FIApartmentRoomCountCodeEnum {
  _1H = '1H',
  _2H = '2H',
  _3H = '3H',
  _4H = '4H',
  _5H = '5H',
  _5H_PLUS = '5H+',
}

export enum FIFloorSurfaceMaterialCodeEnum {
  TILED = 'TILED',
  LAMINATE = 'LAMINATE',
  PARQUET = 'PARQUET',
  PLASTIC = 'PLASTIC',
  BOARD = 'BOARD',
  STONE = 'STONE',
  CONCRETE = 'CONCRETE',
  MICROCEMENT = 'MICROCEMENT',
  VINYL = 'VINYL',
  VINYL_CORK = 'VINYL_CORK',
  CORK = 'CORK',
  ACRYLIC_MASS = 'ACRYLIC_MASS',
  WALL_TO_WALL_CARPET = 'WALL_TO_WALL_CARPET',
  OTHER = 'OTHER',
}

export enum FIWallSurfaceMaterialCodeEnum {
  CERAMIC_TILE = 'CERAMIC_TILE',
  WOOD = 'WOOD',
  LOG = 'LOG',
  PANEL = 'PANEL',
  WAINSCOT = 'WAINSCOT',
  WALLPAPER = 'WALLPAPER',
  GLASS_FIBRE_TEXTILE_COVERED = 'GLASS_FIBRE_TEXTILE_COVERED',
  GLASS = 'GLASS',
  PARTIALLY_TILED = 'PARTIALLY_TILED',
  PLASTIC = 'PLASTIC',
  STONE = 'STONE',
  CONCRETE = 'CONCRETE',
  MICROCEMENT = 'MICROCEMENT',
  PAINT = 'PAINT',
  OTHER = 'OTHER',
}

export enum FICeilingSurfaceMaterialCodeEnum {
  PLASTER = 'PLASTER',
  PANEL = 'PANEL',
  STONE = 'STONE',
  PAINTED = 'PAINTED',
  WOOD = 'WOOD',
  OTHER = 'OTHER',
}

export enum FITypeCodeEnum {
  BATH_ROOM = 'BATH_ROOM',
  BEDROOM = 'BEDROOM',
  DINING_ROOM = 'DINING_ROOM',
  DRAUGHT_LOBBY = 'DRAUGHT_LOBBY',
  HALL = 'HALL',
  HALLWAY = 'HALLWAY',
  KITCHEN = 'KITCHEN',
  LIBRARY = 'LIBRARY',
  LIVING_ROOM = 'LIVING_ROOM',
  LOFT = 'LOFT',
  SAUNA = 'SAUNA',
  STUDY = 'STUDY',
  TOILET = 'TOILET',
  UTILITY_ROOM = 'UTILITY_ROOM',
  WALK_IN_CLOSET = 'WALK_IN_CLOSET',
  OTHER = 'OTHER',
  TERRACE = 'TERRACE',
  BALCONY = 'BALCONY',
  PATIO = 'PATIO',
  YARD = 'YARD',
  STORAGE = 'STORAGE',
}

export enum FIBathRoomFeatureCode {
  WC = 'WC',
  SHOWER = 'SHOWER',
  TWO_SHOWERS = 'TWO_SHOWERS',
  SHOWER_WALL = 'SHOWER_WALL',
  WALK_IN_SHOWER = 'WALK_IN_SHOWER',
  WASHING_MACHINE_CONNECTION = 'WASHING_MACHINE_CONNECTION',
  FIXED_LAMPS = 'FIXED_LAMPS',
  WASHING_MACHINE = 'WASHING_MACHINE',
  TUMBLE_DRYER = 'TUMBLE_DRYER',
  DRYING_CABINET = 'DRYING_CABINET',
  MIRROR = 'MIRROR',
  MIRROR_CABINET = 'MIRROR_CABINET',
  BASIN_CABINET = 'BASIN_CABINET',
  JACUZZI = 'JACUZZI',
  BATHTUB = 'BATHTUB',
  BATHROOM_CABINETS = 'BATHROOM_CABINETS',
}

export enum FIKitchenFeatureCode {
  REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT = 'REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT',
  REFRIGERATOR_FREEZER = 'REFRIGERATOR_FREEZER',
  REFRIGERATOR = 'REFRIGERATOR',
  REFRIGERATED_CABINET = 'REFRIGERATED_CABINET',
  COLD_ROOM = 'COLD_ROOM',
  FREEZER = 'FREEZER',
  REFRIGERATOR_CHILLER = 'REFRIGERATOR_CHILLER',
  DISHWASHER = 'DISHWASHER',
  DISHWASHER_CONNECTION = 'DISHWASHER_CONNECTION',
  RESERVED_LOCATION_FOR_DISHWASHER = 'RESERVED_LOCATION_FOR_DISHWASHER',
  INTEGRATED_DISHWASHER = 'INTEGRATED_DISHWASHER',
  FREE_STANDING_ISLANDS = 'FREE_STANDING_ISLANDS',
  FREE_STANDING_CABINETS = 'FREE_STANDING_CABINETS',
  WINE_CABINET = 'WINE_CABINET',
  INTEGRATED_HOUSEHOLD_APPLIANCES = 'INTEGRATED_HOUSEHOLD_APPLIANCES',
  CERAMIC_STOVE = 'CERAMIC_STOVE',
  HOB = 'HOB',
  BAKING_OVEN = 'BAKING_OVEN',
  COOKTOP = 'COOKTOP',
  INTEGRATED_STOVE = 'INTEGRATED_STOVE',
  EXTRACTOR_HOOD = 'EXTRACTOR_HOOD',
  WOOD_BURNING_STOVE = 'WOOD_BURNING_STOVE',
  ELECTRIC_STOVE = 'ELECTRIC_STOVE',
  INDUCTION_STOVE = 'INDUCTION_STOVE',
  GAS_STOVE = 'GAS_STOVE',
  MICROWAVE_OVEN = 'MICROWAVE_OVEN',
  SEPARATE_OVEN = 'SEPARATE_OVEN',
  EXTRACTOR_HOOD_WITH_FLUE = 'EXTRACTOR_HOOD_WITH_FLUE',
  COOKER_HOOD = 'COOKER_HOOD',
  CONCRETE = 'CONCRETE',
  STONE = 'STONE',
  WOOD = 'WOOD',
  LAMINATE = 'LAMINATE',
  COMPOSITE = 'COMPOSITE',
  METAL = 'METAL',
}

export enum FILivingRoomFeatureCode {
  ROOM_WITH_FIREPLACE = 'ROOM_WITH_FIREPLACE',
}

export enum FISaunaFeatureCode {
  ELECTRIC_HEATER = 'ELECTRIC_HEATER',
  WOOD_HEATED_SAUNA_STOVE = 'WOOD_HEATED_SAUNA_STOVE',
  ALWAYS_READY_HEATER = 'ALWAYS_READY_HEATER',
  READY_FOR_ELECTRIC_HEATER = 'READY_FOR_ELECTRIC_HEATER',
  INTEGRATED_BUCKET = 'INTEGRATED_BUCKET',
  WATER_FAUCET = 'WATER_FAUCET',
  OPTICAL_FIBRE_LIGHTING = 'OPTICAL_FIBRE_LIGHTING',
  LED_LIGHTING = 'LED_LIGHTING',
  WINDOW_OUT = 'WINDOW_OUT',
}
export enum FIToiletFeatureCode {
  FLOOR_MOUNTED_WC = 'FLOOR_MOUNTED_WC',
  WALL_HUNG_WC = 'WALL_HUNG_WC',
  BIDET = 'BIDET',
  UNDERFLOOR_HEATING = 'UNDERFLOOR_HEATING',
  FLOOR_DRAIN = 'FLOOR_DRAIN',
  LAUNDRY_CABINETS = 'LAUNDRY_CABINETS',
  TABLE_TOP = 'TABLE_TOP',
  IRONING_TABLE_BOARD = 'IRONING_TABLE_BOARD',
  BABY_CHANGING_TABLE = 'BABY_CHANGING_TABLE',
  SINK = 'SINK',
  SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR = 'SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR',
  CENTRAL_VACUUM_UNIT = 'CENTRAL_VACUUM_UNIT',
  EXIT = 'EXIT',
}

export interface FIApartmentRoomFeature {
  featureCodes: string[]
  description: FITranslatedText[]
}

export interface FIApartmentRoom {
  typeCode: FITypeCodeEnum
  description: FITranslatedText[]
  features?: FIApartmentRoomFeature[]
  floorMaterialCodes?: FIFloorSurfaceMaterialCodeEnum[]
  wallMaterialCodes?: FIWallSurfaceMaterialCodeEnum[]
  ceilingMaterialCodes?: FICeilingSurfaceMaterialCodeEnum[]
}

export enum FICompassPointEnum {
  NORTH = 'NORTH',
  EAST = 'EAST',
  SOUTH = 'SOUTH',
  WEST = 'WEST',
  NORTHEAST = 'NORTHEAST',
  SOUTHEAST = 'SOUTHEAST',
  SOUTHWEST = 'SOUTHWEST',
  NORTHWEST = 'NORTHWEST',
}

export enum FIAreaBasisCodeEnum {
  ARTICLES_OF_ASSOCIATION = 'ARTICLES_OF_ASSOCIATION',
  MANAGERS_CERTIFICATE = 'MANAGERS_CERTIFICATE',
  VERIFYING_MEASUREMENT = 'VERIFYING_MEASUREMENT',
}

export interface FIApartmentArea {
  areaBasisCodes?: FIAreaBasisCodeEnum[]
  livingArea?: FICommonArea
  otherSpaceArea?: FICommonArea
  totalArea?: FICommonArea
}

export enum FIResidentialPropertyBalconyTypeCodeEnum {
  FRENCH_WINDOW = 'FRENCH_WINDOW',
  GLAZED = 'GLAZED',
  PROTRUDING = 'PROTRUDING',
  RETRACTED = 'RETRACTED',
  OTHER = 'OTHER',
}

export enum FIHearthTypeCodeEnum {
  CONVECTION_FIREPLACE = 'CONVECTION_FIREPLACE',
  FIREPLACE = 'FIREPLACE',
  FLUE_IN_PLACE = 'FLUE_IN_PLACE',
  HEAT_RETAINING_FIREPLACE = 'HEAT_RETAINING_FIREPLACE',
  IRON_STOVE = 'IRON_STOVE',
  OPEN_FIREPLACE = 'OPEN_FIREPLACE',
  PLACE_ALLOCATED_FOR_FIREPLACE = 'PLACE_ALLOCATED_FOR_FIREPLACE',
  BAKING_OVEN = 'BAKING_OVEN',
  OTHER = 'OTHER',
}

export interface FIHearth {
  typeCodes?: FIHearthTypeCodeEnum[]
  typeOtherDescription?: FITranslatedText[]
  description?: FITranslatedText[]
}

export enum FILivingFloorCountCodeEnum {
  SINGLE_FLOOR = 'SINGLE_FLOOR',
  TWO_FLOORS = 'TWO_FLOORS',
  FLOOR_AND_A_HALF = 'FLOOR_AND_A_HALF',
  MORE_THAN_TWO_FLOORS = 'MORE_THAN_TWO_FLOORS',
  NOT_KNOWN = 'NOT_KNOWN',
}

export enum FIPropertyStorageTypeCodeEnum {
  ATTIC = 'ATTIC',
  CELLAR = 'CELLAR',
  OUTDOOR = 'OUTDOOR',
  REFRIGERATED_CELLAR = 'REFRIGERATED_CELLAR',
  OTHER = 'OTHER',
}

export interface FIRedemption {
  redeemableByHousingCompany?: FIChoiceEnum
  redeemableByExistingShareholders?: FIChoiceEnum
  redemptionRightAppliesToAllShares?: FIChoiceEnum
  otherRestrictions?: FITranslatedText
}

export enum FIShareCertificateFormEnum {
  DIGITAL = 'DIGITAL',
  PAPER = 'PAPER',
  OTHER = 'OTHER',
}

export interface FIShareCertificate {
  shareCertificateAvailable?: FIChoiceEnum
  mortgageDeclaredSeparately?: FIChoiceEnum
  shareCertificateUsedAsMortgage?: FIChoiceEnum
  mortgageHolder?: string
  liabilityAmount?: number
  currencyCode?: string
  shareCertificateLocation?: string
  shareGroupIdentifiers?: string[]
  quantityOfShares?: number
  shareCertificateFormCode?: FIShareCertificateFormEnum
  permanentDwellingIdentifier?: string
}

export interface FIHousingCompanyParkingSpace {
  parkingSpaceTypeCode?: FIParkingSpaceTypeCodeEnum
  count?: number
}

export interface FIRenovationShare {
  date: string
  decidedByGeneralMeeting?: FIChoiceEnum
  description: FITranslatedText[]
  isHousingCompanyNotified?: FIChoiceEnum
  statusCode: FIRenovationStatusCodeEnum
  typeCode: FIRenovationTypeCodeEnum
  typeOtherDescription?: FITranslatedText[]
}
