import {
  Flex,
  List,
  ListItem,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { Field, Formik, FormikProps } from 'formik'
import { TFunction, useTranslation } from 'next-i18next'
import { InputWithLabel } from '@/components/Form/Input'
import * as Yup from 'yup'
import { CustomModal } from '@/components/Modal/Modal'
import LabeledField from '@/components/Form/LabeledField'
import { getCurrentUser } from '@/queries/users'
import { TextArea } from '@/components/Form/TextArea'
import { UserRead } from '@/types/users'
import { useGenerateBrokerageOfferMutation } from '../queries/useGenerateBrokerageOfferMutation'
import FiCustomerSelect from '@/components/FICustomerSelect'
import { SchemaBrokerageOfferRequest } from '@/generated-types/api'
import { CreateBrokerageOfferDrawer } from './CreateBrokerageOfferDrawer'
import { useState } from 'react'
import { ConfirmationModal } from '@/components/Modal/ConfirmationModal'
import { EditUserForm } from '@/components/User/EditUserForm'

const NOTES_CHARACTER_LIMIT = 500
const REALTOR_DESCRIPTION_CHARACTER_LIMIT = 500
const PRICE_INQUIRY_MIN = 0
const COMMISSION_MIN = 0
const COMMISSION_MAX = 100

const getMissingProfileFields = (
  currentUser?: UserRead | null
): {
  missing: boolean
  missingFields?: Array<{ missing: boolean; translationKey: string }>
} => {
  if (!currentUser) return { missing: true }

  const requiredFields = [
    {
      missing: !currentUser.phoneNumber,
      translationKey: 'brokerageOffer.missingPhone',
    },
    {
      missing: !currentUser.email,
      translationKey: 'brokerageOffer.missingEmail',
    },
    {
      missing: !currentUser.details?.position,
      translationKey: 'brokerageOffer.missingJobTitle',
    },
    {
      missing: !currentUser.details?.company?.companyName,
      translationKey: 'brokerageOffer.missingCompanyName',
    },
    {
      missing: !currentUser.details?.company?.companyId,
      translationKey: 'brokerageOffer.missingCompanyId',
    },
  ]

  const onlyMissingFields = requiredFields.filter((field) => field.missing)

  return {
    missing: onlyMissingFields.length > 0,
    missingFields: onlyMissingFields.length > 0 ? onlyMissingFields : undefined,
  }
}

const CreateBrokerageOfferSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object({
    realtorId: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('brokerageOffer.realtor')),
    address: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('brokerageOffer.address')),
    priceInquiry: Yup.number()
      .required(t('errors.fieldRequired'))
      .min(PRICE_INQUIRY_MIN, t('brokerageOffer.priceInquiryError'))
      .label(t('brokerageOffer.priceInquiry')),
    commission: Yup.number()
      .required(t('errors.fieldRequired'))
      .min(COMMISSION_MIN, t('brokerageOffer.commissionError'))
      .max(COMMISSION_MAX, t('brokerageOffer.commissionError'))
      .label(t('brokerageOffer.commission')),
    realtorDescription: Yup.string().max(
      REALTOR_DESCRIPTION_CHARACTER_LIMIT,
      t('brokerageOffer.realtorDescriptionTooLong', {
        characterLimit: String(REALTOR_DESCRIPTION_CHARACTER_LIMIT),
      })
    ),
    notes: Yup.string().max(
      NOTES_CHARACTER_LIMIT,
      t('brokerageOffer.notesTooLong', {
        characterLimit: String(NOTES_CHARACTER_LIMIT),
      })
    ),
    contactIds: Yup.array()
      .of(Yup.number().required())
      .min(1, t('brokerageOffer.recipientRequired'))
      .label(t('brokerageOffer.contact')),
  })

const Body = ({
  t,
  formik,
  currentUser,
}: {
  t: TFunction
  currentUser?: UserRead | null
  formik: FormikProps<SchemaBrokerageOfferRequest>
}) => {
  const { missing, missingFields } = getMissingProfileFields(currentUser)

  return (
    <Flex direction="column" gap="4">
      {missing && (
        <Flex direction="column">
          <Text color="#e53e3e" fontSize="14px">
            {t('brokerageOffer.missingFieldsPrefix')}
            {missingFields && (
              <List listStyleType="circle" pl="18px">
                {missingFields.map((field, index) => (
                  <ListItem key={index}>{t(field.translationKey)}</ListItem>
                ))}
              </List>
            )}
          </Text>
          {currentUser && <EditUserForm user={currentUser} />}
        </Flex>
      )}
      <Flex width="100%" direction="column">
        <FiCustomerSelect
          name="contactIds"
          label={t('brokerageOffer.shareWith')}
          querySource="contacts"
          required
        />
      </Flex>

      <InputWithLabel
        label={t('brokerageOffer.address')}
        placeholder={t('brokerageOffer.address') || ''}
        name="address"
        type="text"
        autoComplete="off"
        error={formik.errors.address}
        touched={formik.touched.address}
        required
      />

      <Flex width="100%" gap="4" direction={['column', 'row']}>
        <InputWithLabel
          label={t('brokerageOffer.priceInquiry')}
          placeholder={t('brokerageOffer.priceInquiry') || ''}
          name="priceInquiry"
          type="currency"
          autoComplete="off"
          error={formik.errors.priceInquiry}
          touched={formik.touched.priceInquiry}
          required
        />
        <InputWithLabel
          label={t('brokerageOffer.commission')}
          placeholder={t('brokerageOffer.commission') || ''}
          name="commission"
          type="number"
          error={formik.errors.commission}
          touched={formik.touched.commission}
          required
        />
      </Flex>

      <LabeledField label={`${t('brokerageOffer.notes')}`} width={'100%'}>
        <Field
          name="notes"
          as={TextArea}
          width="100%"
          placeholder={t('brokerageOffer.notes') || ''}
          autoComplete="off"
          error={formik.errors.notes}
          touched={formik.touched.notes}
          whiteSpace={'pre-wrap'}
          overflowWrap={'break-word'}
          size="sm"
          minH="80px"
        />
      </LabeledField>
      <LabeledField
        label={`${t('brokerageOffer.realtorDescription')}`}
        width={'100%'}
      >
        <Field
          name="realtorDescription"
          as={TextArea}
          width="100%"
          placeholder={t('brokerageOffer.realtorDescription') || ''}
          error={formik.errors.realtorDescription}
          touched={formik.touched.realtorDescription}
          whiteSpace={'pre-wrap'}
          overflowWrap={'break-word'}
          size="sm"
          minH="175px"
        />
      </LabeledField>
    </Flex>
  )
}

export const CreateBrokerageOfferFormModal = ({
  onClose,
}: {
  onClose: () => void
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })

  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const { data: currentUser, isPending: isCurrentUserLoading } = useQuery({
    queryKey: ['me'],
    queryFn: () => getCurrentUser(),
  })

  const { mutate: generatePDF, isPending: isGenerating } =
    useGenerateBrokerageOfferMutation()

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  const handleSubmit = async (values: SchemaBrokerageOfferRequest) => {
    if (
      !values.realtorId ||
      values.priceInquiry == null ||
      values.commission == null ||
      getMissingProfileFields(currentUser).missing
    ) {
      return
    }

    generatePDF(values, {
      onSuccess: () => {
        onClose()
      },
    })
  }

  const getInitialValues = (
    user: UserRead | null | undefined,
    translateFn: TFunction
  ): SchemaBrokerageOfferRequest => ({
    realtorId: user?.id ?? 0,
    address: '',
    priceInquiry: 0,
    commission: 0,
    realtorDescription:
      translateFn('brokerageOffer.realtorDescriptionDefault') || '',
    notes: '',
    contactIds: [],
  })

  const initialValues = getInitialValues(currentUser, t)
  const validationSchema = CreateBrokerageOfferSchema(t)

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={validationSchema}
        validateOnMount
        validateOnChange
      >
        {(formik: FormikProps<SchemaBrokerageOfferRequest>) => {
          return !isMobile ? (
            <CustomModal
              isOpen={true}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={formik.submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('brokerageOffer.title')}
              acceptTitle={t('brokerageOffer.generatePDF') || undefined}
              isLoading={isCurrentUserLoading || isGenerating}
              disableAcceptButton={
                !formik.isValid ||
                formik.isSubmitting ||
                isGenerating ||
                getMissingProfileFields(currentUser).missing
              }
            >
              <Body t={t} formik={formik} currentUser={currentUser} />
            </CustomModal>
          ) : (
            <CreateBrokerageOfferDrawer
              isOpen={true}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={formik.submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('brokerageOffer.title')}
              acceptTitle={t('brokerageOffer.generatePDF') || undefined}
              isLoading={isCurrentUserLoading || isGenerating}
              disableAcceptButton={
                !formik.isValid ||
                formik.isSubmitting ||
                isGenerating ||
                getMissingProfileFields(currentUser).missing
              }
            >
              <Body t={t} formik={formik} currentUser={currentUser} />
            </CreateBrokerageOfferDrawer>
          )
        }}
      </Formik>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={confirmationModalAcceptFunction}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
    </>
  )
}
