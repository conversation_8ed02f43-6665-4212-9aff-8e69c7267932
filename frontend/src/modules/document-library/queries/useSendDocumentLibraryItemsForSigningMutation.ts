import { SchemaSendDocumentLibraryItemForSigning } from '@/generated-types/api'
import { useMobile } from '@/hooks/useMobile'
import { axiosInstance } from '@/lib/axiosConfig'
import { useToast } from '@chakra-ui/react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { getSession } from 'next-auth/react'
import { useTranslation } from 'next-i18next'
import { Owner } from '../types/Owner'
import { getDocumentLibraryItemsQueryKey } from './useGetDocumentLibraryItemsQuery'
import { getToastProps } from '@/utils/toastProps'
import { getErrorLabel } from '@/lib/errorLabel'

const sendDocumentLibraryItemsForSigning = async (
  owner: Owner,
  payload: SchemaSendDocumentLibraryItemForSigning
): Promise<void> => {
  const url = `/document-library-items/${owner.type}/${owner.id}/send-for-signing`
  const session = await getSession()
  const { data } = await axiosInstance.post(url, payload, {
    headers: {
      Authorization: `Bearer ${session?.token}`,
    },
  })

  return data
}

export const useSendDocumentLibraryItemsForSigningMutation = ({
  owner,
  onSuccess,
}: {
  owner: Owner
  onSuccess: () => void
}) => {
  const toast = useToast()
  const { t } = useTranslation(['common'])
  const isMobile = useMobile()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: SchemaSendDocumentLibraryItemForSigning) =>
      sendDocumentLibraryItemsForSigning(owner, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getDocumentLibraryItemsQueryKey(owner),
      })
      onSuccess()
      toast(
        getToastProps({
          isMobile,
          title: t('documentLibrary.sendForSigningSuccessTitle'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
    },
    onError: (error) => {
      toast(
        getToastProps({
          isMobile,
          title: t('errors.title'),
          description: getErrorLabel(t, error),
          status: 'error',
          variant: 'customError',
        })
      )
    },
  })
}
