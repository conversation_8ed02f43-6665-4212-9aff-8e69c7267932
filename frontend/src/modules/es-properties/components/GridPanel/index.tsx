import { ListProperties, StatusEnum } from '@/types/property'
import { Box, Flex, SimpleGrid, Text } from '@chakra-ui/react'
import PropertyCard from '../../../../components/PropertyCard'
import {
  getPropertyCommissionAndRent,
  getPropertyPrice,
} from '../../utils/type.utils'
import { CustomPagination } from '@/components/Pagination/Pagination'
import { useTranslation } from 'next-i18next'

interface Props {
  data: ListProperties[]
  onClickProperty: (reference: string) => void
  totalItems: number
  pageSize: number
  currentPage: number
  handlePageChange: (page: number) => void
  onChangePageSize: (pageSize: number) => void
}

const PropertyGridPanel = ({
  data = [],
  onClickProperty,
  totalItems,
  pageSize,
  currentPage,
  handlePageChange,
  onChangePageSize,
}: Props) => {
  const { t } = useTranslation(['common'])
  return (
    <Box>
      <SimpleGrid
        columns={{ base: 1, lg: 3, md: 2, xl: 4 }}
        width="100%"
        gap="4"
      >
        {data.map((property) => {
          const commission = getPropertyCommissionAndRent(property)
          const price = getPropertyPrice(property)

          return (
            <PropertyCard
              key={property.reference}
              isPublished={property.status === StatusEnum.PUBLISHED}
              isStrandified={property.isStrandified}
              mainImg={property.mainImg}
              priceSale={price}
              commission={commission}
              realtorUsers={property.realtorUsers ?? []}
              location={property.location}
              propertyType={property.propertyType}
              builtArea={property.builtArea && `${property.builtArea} m²`}
              status={property.status}
              soldBy={property.soldBy}
              onClick={() => onClickProperty(property.reference)}
            />
          )
        })}
      </SimpleGrid>
      <Flex
        mt="4"
        border="1px solid"
        borderColor="grays.grayBorder"
        justifyContent="space-between"
        alignItems="center"
        height="8"
        p={['2', '4']}
        direction={['column', 'row']}
      >
        <Text variant="medium" color="grays.darkGray">{`${t(
          'viewing'
        )} ${Math.min(currentPage * pageSize, totalItems)} / ${totalItems} ${t(
          'properties'
        )}`}</Text>
        <CustomPagination
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
          handleChangeLimit={onChangePageSize}
        />
      </Flex>
    </Box>
  )
}

export default PropertyGridPanel
