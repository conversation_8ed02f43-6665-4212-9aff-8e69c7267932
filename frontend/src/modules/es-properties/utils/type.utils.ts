import {
  CommissionTypeEnum,
  ListingTypeEnum,
  ListProperties,
} from '@/types/property'
import { getCommissionInCurrency } from '@/utils/commission'
import { formatESPrice } from '@/utils/formatESPrice'

export const formatPropertyTypeInfo = (
  property: ListProperties,
  t: (key: string) => string
): string => {
  const builtAreaText = property.builtArea
    ? `${property.builtArea}m² ${t('interior')}`
    : ''

  const plotAreaText = property.plotArea
    ? `${property.plotArea}m² ${t('plot')}`
    : ''

  const bedroomsText = property.bedrooms
    ? property.bedrooms > 1
      ? `${property.bedrooms} ${t('beds')} `
      : `${property.bedrooms} ${t('bed')} `
    : ''

  const bathroomsText = property.bathrooms
    ? property.bathrooms > 1
      ? `${property.bathrooms} ${t('baths')}`
      : `${property.bathrooms} ${t('bath')}`
    : ''

  return [bedroomsText, bathroomsText, builtAreaText, plotAreaText]
    .filter((item) => !!item)
    .join(', ')
}

export const getPropertyPrice = (property: ListProperties) => {
  let price
  if (
    property.listingTypes
      ?.map((item) => item.name)
      .includes(ListingTypeEnum.SALE)
  ) {
    price = formatESPrice({
      price: property.priceSale ?? 0,
      currency: property.currency,
      removeDecimals: true,
    })
  } else if (
    property.listingTypes
      ?.map((item) => item.name)
      .includes(ListingTypeEnum.RENT_LONG)
  ) {
    price = formatESPrice({
      price: property.priceRentLongTerm ?? 0,
      currency: property.currency,
      removeDecimals: true,
    })
  } else {
    price = formatESPrice({
      price: property.priceRentShortTerm ?? 0,
      currency: property.currency,
      removeDecimals: true,
    })
  }

  return price
}

export const getPropertyCommissionAndRent = (property: ListProperties) => {
  const getCommissionBasedOnCommissionType = () => {
    if (property.commissionType === CommissionTypeEnum.FIXED) {
      return formatESPrice({
        price: property.commission ?? 0,
        currency: property.currency,
        removeDecimals: true,
      })
    }

    return `${property.commission}% = ${getCommissionInCurrency(property)}`
  }
  const commissionText =
    property.commission !== null ? getCommissionBasedOnCommissionType() : null
  const rentPriceText = [
    property.priceRentLongTerm,
    property.priceRentShortTerm,
  ]
    .filter(Boolean)
    .join('-')

  return [commissionText, rentPriceText ? `Rent: ${rentPriceText} €` : null]
    .filter(Boolean)
    .join(' | ')
}
