import { <PERSON>ceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import { Stack, useBreakpointValue, useToast } from '@chakra-ui/react'
import { Formik } from 'formik'
import { useTranslation } from 'next-i18next'
import { DetailOfSaleRead } from '@/types/detailOfSale'
import { requestChangeDoS } from '@/queries/detailsOfSale'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as Yup from 'yup'
import { useRouter } from 'next/router'
import { TextArea } from '@/components/Form/TextArea'
import LabeledField from '@/components/Form/LabeledField'

interface Props {
  open?: boolean
  onClose: () => void
}

const RequestChangeSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object().shape({
    message: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('createEmail.message')),
  })

const RequestChangeModal = ({ open = true, onClose }: Props) => {
  const { t } = useTranslation(['common'])
  const toast = useToast()

  const queryClient = useQueryClient()
  const router = useRouter()

  const dos = queryClient.getQueryData<DetailOfSaleRead>([
    'dos',
    router.query.id,
  ])
  const isMobile = useBreakpointValue({ base: true, md: false })

  const { mutate: mutationRequestChangeDos, isPending } = useMutation({
    mutationKey: ['detailOfSale'],
    mutationFn: requestChangeDoS,
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['dos', router.query.id],
      })

      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendRequestChange',
        title: t('requestChangesDos.changingRequested'),
        description: `${t('requestChangesDos.requestChangeDescription')} `,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('errors.title'),
        description: t('errors.detailOfSaleNotCreated'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const handleSubmit = (data: { message: string }) => {
    if (dos) {
      mutationRequestChangeDos({ id: dos.id, data })
    }
  }

  const schema = RequestChangeSchema(t)

  const initValues: { message: string } = {
    message: '',
  }
  return (
    <Formik
      initialValues={initValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {({
        submitForm,
        isValid,
        values,
        setFieldValue,
        dirty,
        isSubmitting,
      }) => (
        <DeviceAdapter
          isLoading={isPending}
          isOpen={open}
          title={t('requestChanges')}
          acceptTitle={`${t('requestChanges')}`}
          cancelTitle={`${t('cancel')}`}
          onClose={onClose}
          onAccept={submitForm}
          size="xl"
          onReject={onClose}
          disableAcceptButton={!isValid}
          dirty={dirty}
          isSubmitting={isSubmitting}
        >
          <Stack gap={6}>
            <LabeledField label={`${t('createEmail.message')}:`}>
              <TextArea
                value={values.message}
                onChange={(e) => setFieldValue('message', e.target.value)}
                key="message"
                name="message"
                placeholder={`${t('writeYourNote')}`}
                data-testid="noteToReviewer-value-input"
              />
            </LabeledField>
          </Stack>
        </DeviceAdapter>
      )}
    </Formik>
  )
}

export default RequestChangeModal
