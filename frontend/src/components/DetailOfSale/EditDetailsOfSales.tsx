import { Formik, validateYupSchema, yupToFormErrors } from 'formik'
import {
  COMMISSION_TYPE,
  DEPOSIT_ACCOUNT_TYPE,
  DetailOfSaleForm,
  DetailOfSaleRead,
  DETAILS_OF_SALE_ATTACHMENTS_TYPE,
  INVOICE_TYPE,
} from '@/types/detailOfSale'
import { useSteps } from '@/hooks/useSteps'
import { useBreakpointValue, useToast } from '@chakra-ui/react'
import { useMemo } from 'react'
import { useTranslation } from 'next-i18next'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDoS } from '@/queries/detailsOfSale'
import { DeviceAdapter } from '../ActionDrawer/DeviceAdapter'
import { EditOrCreateDosForm } from '../ActionDrawer/CreateDetailOfSale'
import * as Yup from 'yup'
import { get, groupBy } from 'lodash'
import { PROPERTY_TYPE } from '@/types/property'
import { SOWISE_STATUS } from '@/types/common'

const GENERAL_DETAIL_OF_SALE_FORM = 1
const COMMISSION_SPLIT_IN_HOUSE = 2
const READY_TO_SEND = 3

interface Props {
  isOpen?: boolean
  onClose: () => void
  detail: DetailOfSaleRead
}

const UpdateDetailOfSaleSchema = (t: (key: string) => string) =>
  Yup.object().shape({
    propertyReference: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('property')),
    buyers: Yup.array()
      .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
      .label(t('offer.buyers')),
    sellers: Yup.array()
      .of(
        Yup.object({
          invoicePercentage: Yup.number()
            .required(t('errors.fieldRequired'))
            .label(t('invoice')),
        })
      )
      .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
      .label(t('offer.sellers')),
    offerAgreedDate: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('offerAgreedDate')),
    salePrice: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('salePrice')),
    reviewerOfficeId: Yup.number()
      .nullable()
      .when('$activeStep', {
        is: (activeStep: number) => {
          return activeStep === READY_TO_SEND
        },
        then: (schema) => schema.required(t('errors.fieldRequired')),
      })
      .label(t('reviewer')),

    depositAccountType: Yup.string().nullable(),
    depositPercentage: Yup.number()
      .nullable()
      .when(['depositAmount', 'depositAccountType'], {
        is: (
          depositAmount: number | null | undefined,
          depositAccountType: string
        ) => {
          if (depositAccountType === DEPOSIT_ACCOUNT_TYPE.NO_DEPOSIT_PAID)
            return false
          return !depositAmount
        },
        then: (schema) =>
          schema
            .required(t('errors.fieldRequired'))
            .moreThan(0, t('errors.fieldRequired')),
      })
      .label(t('depositPaid')),

    completionNotaryDeadline: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('deadlineOfTheCompletionNotary')),
    notaryDayBooked: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('notaryDayBooked')),
    totalCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('totalCommission')),
    strandCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('strandCommission')),
    otherAgencyCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('otherAgencyCommissionAmount')),
    agents: Yup.array().of(
      Yup.object({
        userId: Yup.string()
          .required(t('errors.fieldRequired'))
          .label(t('agent')),
        commissionPercentage: Yup.number()
          .nullable()
          .when('commissionAmount', {
            is: (commissionAmount: number | null | undefined) => {
              return !commissionAmount
            },
            then: (schema) => schema.required(t('errors.fieldRequired')),
          }),
      })
    ),
  })

export const EditDetailsOfSale = ({
  isOpen = true,
  onClose,
  detail,
}: Props) => {
  const steps = [
    GENERAL_DETAIL_OF_SALE_FORM,
    COMMISSION_SPLIT_IN_HOUSE,
    READY_TO_SEND,
  ]
  const toast = useToast()
  const isMobile = useBreakpointValue({ base: true, md: false })
  const queryClient = useQueryClient()

  const { t } = useTranslation(['common'])
  const { activeStep, goToNext, goToPrevious, handleSetValidStep, isInvalid } =
    useSteps(steps)

  const detailFormValue: DetailOfSaleForm | undefined = useMemo(() => {
    if (!detail) return undefined
    const attachments = groupBy(
      detail.document.documentAttachments,
      'attachmentType'
    )

    return {
      ...detail,
      buyers: detail.buyers.map((item) => item.id),
      propertyType: detail?.customReferenceProperty
        ? PROPERTY_TYPE.CUSTOM_PROPERTY
        : PROPERTY_TYPE.EXIST_PROPERTY,
      propertyReference:
        detail?.property?.reference || detail.customReferenceProperty,
      separateInvoiceType: detail.separateInvoiceForEachSeller
        ? INVOICE_TYPE.EACH_OTHER
        : INVOICE_TYPE.EVERYONE,
      notateSimpleSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.NOTE_SIMPLE_PDF],
      nieAttachmentsSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.NIE_DNI],
      passportPDFSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.PASSPORT_PDF],
      reservationAgreementSignedPDFSaved:
        attachments[
          DETAILS_OF_SALE_ATTACHMENTS_TYPE.RESERVATION_AGREEMENT_SIGNED_PDF
        ],
      proofOfTransferDepositSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.PROOF_OF_TRANSFER_DEPOSIT],
      saleAgreementSignedAttachmentsSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.SALE_AGREEMENT_SIGNED],
      ibiReceiptAttachmentsSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.IBI_RECEIPT],
      basuraReceiptAttachmentsSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.BASURA_RECEIPT],
      copyOfTitleDeedSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.COPY_OF_THE_TITLE_DEED],
      sellerInvoicesSaved:
        attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.SELLER_INVOICES],
      extraDosSaved: attachments[DETAILS_OF_SALE_ATTACHMENTS_TYPE.EXTRA],
      language: 'en',
      sellers: detail.sellers?.map((item) => ({
        ...item,
        invoicePercentage: 100,
      })),
      sellerIds: detail.sellers.map((item) => item.sellerId),
      removedDocument: [],
      renameDocuments: {},
      agents: detail.agents.map((item) => ({
        ...item,
        userId: item.user?.id || 0,
        attachmentsSaved: item.documentAttachments,
      })),
      sellerInvoices: [],
    }
  }, [detail])

  const initialValues: DetailOfSaleForm = detailFormValue || {
    propertyType: PROPERTY_TYPE.EXIST_PROPERTY,
    depositAccountType: DEPOSIT_ACCOUNT_TYPE.STRAND_CLIENTS_ACCOUNT,
    strandCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    totalCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    otherAgencyCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    agents: [],
    separateInvoiceType: INVOICE_TYPE.EVERYONE,
    salePrice: 0,
    propertyReference: '',
    reviewerOfficeId: null,
    totalCommissionAmount: 0,
    strandCommissionAmount: 0,
    buyers: [],
    sellers: [],
    depositAmount: 0,
    depositPercentage: 0,
    externalLead: '',
    otherAgencyCommissionAmount: 0,
    notateSimple: [],
    nieAttachments: [],
    saleAgreementSignedAttachments: [],
    ibiReceiptAttachments: [],
    basuraReceiptAttachments: [],
    copyOfTitleDeed: [],
    sellerInvoices: [],
    notes: '',
    language: 'en',
    renameDocuments: {},
  }
  const schema = UpdateDetailOfSaleSchema(t)

  const { mutate: mutationUpdateDoS, isPending: isCreating } = useMutation({
    mutationKey: ['detailOfSale'],
    mutationFn: updateDoS,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['dos', detail.id.toString()],
      })
      onClose()
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('editDoS.sentForReview'),
        description: `${t('createDoS.sendForReviewDescription')} `,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: (e) => {
      const description = get(e, 'response.data.error')
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('errors.title'),
        description: description
          ? JSON.stringify(description)
          : t('errors.detailOfSaleNotEdited'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const handleUpdateDoS = async (values: DetailOfSaleForm) => {
    mutationUpdateDoS({
      ...values,
      separateInvoiceForEachSeller:
        values.separateInvoiceType === INVOICE_TYPE.EACH_OTHER,
      sendForReview: activeStep === READY_TO_SEND,
      status: SOWISE_STATUS.IN_REVIEW,
    })
  }

  const acceptTitle = (() => {
    if (activeStep === READY_TO_SEND) {
      return t('sendForReview')
    }
    return t('next')
  })()

  const cancelTitle = (() => {
    if (activeStep === GENERAL_DETAIL_OF_SALE_FORM) {
      return t('save')
    }
    return t('prev')
  })()

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={handleUpdateDoS}
        validate={(values) => {
          try {
            validateYupSchema(values, schema, true, { activeStep })
          } catch (e) {
            return yupToFormErrors(e)
          }
        }}
      >
        {({ values, isValid, submitForm, dirty, isSubmitting }) => {
          const handleClickAccept = () => {
            if (activeStep === READY_TO_SEND) {
              submitForm()
              return
            }
            goToNext()
          }
          const handleClickReject = () => {
            if (activeStep === GENERAL_DETAIL_OF_SALE_FORM) {
              mutationUpdateDoS({
                ...values,
                separateInvoiceForEachSeller:
                  values.separateInvoiceType === INVOICE_TYPE.EACH_OTHER,
                sendForReview: false,
              })
              return
            }
            goToPrevious()
          }
          const isSaveDraftValid =
            !!values.propertyReference &&
            !!values.sellerIds?.length &&
            (!values.offerId || !!values.buyers?.length)

          return (
            <DeviceAdapter
              size="md"
              isOpen={isOpen}
              onAccept={handleClickAccept}
              onClose={onClose}
              onReject={handleClickReject}
              disableAcceptButton={!isValid || isInvalid}
              isLoading={isCreating}
              title={t('detailsOfSale.edit')}
              acceptTitle={acceptTitle}
              cancelTitle={cancelTitle}
              disableRejectButton={!isSaveDraftValid || isCreating}
              dirty={dirty}
              isSubmitting={isSubmitting}
              rejectHasSecondaryActions={true}
            >
              <EditOrCreateDosForm
                onValidateMissingInfo={handleSetValidStep}
                values={values}
                activeStep={activeStep}
                steps={steps}
              />
            </DeviceAdapter>
          )
        }}
      </Formik>
    </>
  )
}
