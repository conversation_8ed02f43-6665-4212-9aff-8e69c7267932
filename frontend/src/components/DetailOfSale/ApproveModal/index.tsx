import { <PERSON>ceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import { useSteps } from '@/hooks/useSteps'
import {
  Stack,
  Step,
  StepIndicator,
  Stepper,
  StepSeparator,
  StepStatus,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { Formik } from 'formik'
import { useTranslation } from 'next-i18next'
import ApproveDetailsOfSaleForm from './StepContainer/ApproveForm'
import { ApproveDoSForm, DetailOfSaleRead } from '@/types/detailOfSale'
import { ReviewApproveForm } from './StepContainer/PreviewApproveForm'
import { ReadyToApprove } from './StepContainer/ReadyToApprove'
import { approveDoS } from '@/queries/detailsOfSale'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as Yup from 'yup'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { get } from 'lodash'
import { ERROR_CODE } from '@/types/common'

interface Props {
  open?: boolean
  onClose: () => void
}

const APPROVE_FORM_STEP = 1
const REVIEW_INVOICES_STEP = 2
const APPROVE_AND_CREATE_INVOICES = 3

const steps = [
  APPROVE_FORM_STEP,
  REVIEW_INVOICES_STEP,
  APPROVE_AND_CREATE_INVOICES,
]

const ApproveSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object().shape({
    invoiceDate: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('invoiceDate')),
    invoiceDueDate: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('invoiceDueDate')),
    agentId: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('agentNameOnTheInvoice')),
    issuedBy: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('invoiceIssuedBy')),

    sellers: Yup.array().of(
      Yup.object().shape({
        invoiceNumber: Yup.string()
          .required(t('errors.fieldRequired'))
          .label(t('invoiceNumber')),
      })
    ),
  })

const ApproveModal = ({ open = true, onClose }: Props) => {
  const { t } = useTranslation(['common'])
  const [existedInvoiceNumbers, setExistedInvoiceNumber] = useState<string[]>(
    []
  )
  const toast = useToast()
  const {
    activeStep,
    goToNext,
    goToPrevious,
    handleSetValidStep,
    isInvalid,
    setActiveStep,
  } = useSteps(steps)

  const queryClient = useQueryClient()
  const router = useRouter()

  const dos = queryClient.getQueryData<DetailOfSaleRead>([
    'dos',
    router.query.id,
  ])
  const isMobile = useBreakpointValue({ base: true, md: false })

  const { mutate: mutationCreateDoS, isPending: isCreating } = useMutation({
    mutationKey: ['detailOfSale'],
    mutationFn: approveDoS,
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['dos', router.query.id],
      })

      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('approveDos.approved'),
        description: `${t('approveDos.sendForReviewDescription')} `,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: (e) => {
      const status = get(e, 'response.data.code')

      const message =
        get(e, 'response.data.error') || t('errors.detailOfSaleNotCreated')
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('errors.title'),
        description: message,
        status: 'error',
        duration: 9000,
        isClosable: true,
      })

      if (status === ERROR_CODE.DOS_INVOICE_NUMBER_DUPLICATE) {
        const duplicateInvoiceNumbers = get(e, 'response.data.data') || []
        if (
          Array.isArray(duplicateInvoiceNumbers) &&
          duplicateInvoiceNumbers.length
        ) {
          setExistedInvoiceNumber(duplicateInvoiceNumbers)
        }
        setActiveStep(APPROVE_FORM_STEP)
      }
    },
  })

  const acceptTitle = (() => {
    if (activeStep === APPROVE_AND_CREATE_INVOICES) {
      return t('readyForApproveDoS.approveAndCreateInvoices')
    }
    return t('next')
  })()

  const cancelTitle = (() => {
    return t('prev')
  })()

  const handleSubmit = (data: ApproveDoSForm) => {
    if (activeStep === APPROVE_AND_CREATE_INVOICES && dos) {
      //approve mutate
      mutationCreateDoS({
        id: dos.id,
        data,
      })
      return
    }
    goToNext()
  }

  const schema = ApproveSchema(t)

  const initValues: ApproveDoSForm = {
    agentId: null,
    invoiceDate: '',
    invoiceDueDate: '',
    issuedBy: null,
    sellers:
      dos?.sellers.map((item) => ({
        id: item.id,
        invoiceNumber: '',
        name: item.name,
        invoicePercentage: item.invoicePercentage,
      })) || [],
  }

  return (
    <Formik
      initialValues={initValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {({ submitForm, isValid, values, dirty, isSubmitting }) => {
        const duplicateInvoiceNumber = (() => {
          const listInvoiceNumber = values.sellers
            .map((item) => item.invoiceNumber)
            .concat(existedInvoiceNumbers)

          if (dos?.separateInvoiceForEachSeller && listInvoiceNumber.length)
            return listInvoiceNumber.filter(
              (item, index) => listInvoiceNumber.indexOf(item) !== index
            )
          if (
            !dos?.separateInvoiceForEachSeller &&
            existedInvoiceNumbers.length
          ) {
            return listInvoiceNumber.filter(
              (item, index) => existedInvoiceNumbers.indexOf(item) !== index
            )
          }
          return []
        })()
        return (
          <DeviceAdapter
            isLoading={isCreating}
            isOpen={open}
            title={t('approveDoS')}
            acceptTitle={acceptTitle}
            cancelTitle={cancelTitle}
            onClose={onClose}
            onAccept={submitForm}
            size="xl"
            onReject={goToPrevious}
            disableRejectButton={activeStep === APPROVE_FORM_STEP}
            disableAcceptButton={
              (!isValid && isInvalid) || !!duplicateInvoiceNumber.length
            }
            dirty={dirty}
            isSubmitting={isSubmitting}
          >
            <Stack gap={6}>
              <Stepper
                size="sm"
                colorScheme="blackAlpha"
                mb={4}
                index={activeStep}
              >
                {steps.map((_step, index) => (
                  <Step key={index + 1}>
                    <StepIndicator>
                      <StepStatus />
                    </StepIndicator>
                    <StepSeparator />
                  </Step>
                ))}
              </Stepper>

              <ApproveDetailsOfSaleForm
                open={activeStep === APPROVE_FORM_STEP}
                invoiceForEachSeller={dos?.separateInvoiceForEachSeller}
                duplicateInvoiceNumber={duplicateInvoiceNumber}
              />
              <ReviewApproveForm
                onValidateMissingInfo={(value) =>
                  handleSetValidStep(REVIEW_INVOICES_STEP, value)
                }
                enabled={activeStep > APPROVE_FORM_STEP}
                visible={activeStep === REVIEW_INVOICES_STEP}
              />
              {activeStep === APPROVE_AND_CREATE_INVOICES && <ReadyToApprove />}
            </Stack>
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}

export default ApproveModal
