import {
  Button,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON>lose<PERSON><PERSON>on,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  ModalHeader,
  ModalOverlay,
  Text,
  Heading,
  BoxProps,
} from '@chakra-ui/react'
import { ReactNode } from 'react'
import { useTranslation } from 'next-i18next'
import { ScrollableRootContextProvider } from '../useScrollableRootContext'

type CustomModalProps = {
  isOpen: boolean
  onClose: () => void
  onAccept?: () => void
  onReject?: () => void
  title: string | ReactNode
  children: ReactNode
  disableAcceptButton?: boolean
  disableRejectButton?: boolean
  disableModalClosing?: boolean
  isLoading?: boolean
  acceptTitle?: string
  cancelTitle?: string
  scrollable?: boolean
  size?: string
  isCentered?: boolean
  headerContent?: ReactNode
  modalBodyProps?: BoxProps
  footerContent?: ReactNode
}

export const CustomModal = ({
  isOpen,
  onClose,
  onAccept,
  onRej<PERSON><PERSON>,
  title,
  children,
  disableAcceptButton,
  disableRejectButton,
  disableModalClosing,
  isLoading,
  acceptTitle,
  cancelTitle,
  scrollable,
  size = 'md',
  isCentered = false,
  headerContent,
  modalBodyProps,
  footerContent,
}: CustomModalProps) => {
  const { t } = useTranslation(['common'])

  return (
    <Modal
      isOpen={isOpen}
      isCentered={isCentered}
      onClose={onClose}
      size={size}
      closeOnEsc={!disableModalClosing}
      closeOnOverlayClick={false}
      scrollBehavior="inside"
    >
      <ModalOverlay />
      <ModalContent
        maxHeight="calc(100vh - 60px)"
        display="flex"
        flexDirection="column"
      >
        <ModalHeader
          padding="16px 26px"
          borderBottom="1px solid"
          borderBottomColor="gray.200"
          display="flex"
          flexDirection="column"
          gap="10px"
        >
          <Heading variant="H1">{title}</Heading>
          {headerContent}
        </ModalHeader>
        {!disableModalClosing && <ModalCloseButton size="lg" />}
        <ModalBody
          className={scrollable ? 'scrollable' : ''}
          px="8"
          py="4"
          overflow="auto"
          {...modalBodyProps}
        >
          <ScrollableRootContextProvider>
            {children}
          </ScrollableRootContextProvider>
        </ModalBody>
        {footerContent ? (
          footerContent
        ) : (
          <ModalFooter
            padding="18px 24px"
            borderTop="1px solid"
            borderTopColor="gray.200"
            justifyContent="space-between"
          >
            <Button
              padding="6px 16px"
              variant="ghost"
              colorScheme="whiteAlpha"
              color="black"
              mr={3}
              isDisabled={disableRejectButton}
              onClick={onReject}
              data-testid="custom-modal-previous-button"
              size="mdWide"
            >
              <Text variant="modal">{cancelTitle || t('cancel')}</Text>
            </Button>
            <Button
              padding="6px 16px"
              onClick={onAccept}
              isDisabled={disableAcceptButton}
              isLoading={isLoading}
              data-testid="custom-modal-next-button"
              size="mdWide"
            >
              <Text variant="modal">{acceptTitle || t('apply')}</Text>
            </Button>
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  )
}
