import { PreviewDrawer } from '@/modules/document-library/components/Preview/PreviewDrawer'
import {
  getFIPurchaseOfferAsPdf,
  getFIPurchaseOfferAsPreview,
} from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import {
  getFISalesAgreementAsPdf,
  getFISalesAgreementAsPreview,
} from '@/modules/fi-properties/queries/queryFISalesAgreements'

import { FiDownload } from 'react-icons/fi'
import { useMutation, useQuery } from '@tanstack/react-query'
import { IconButton } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'

export const PreviewHtmlModal = ({
  type,
  entityId,
  previewIsOpen,
  setPreviewIsOpen,
}: {
  type: 'sales-agreement' | 'purchase-offer'
  entityId: string
  title?: string
  previewIsOpen?: boolean
  setPreviewIsOpen?: (b: boolean) => void
}) => {
  const { t } = useTranslation(['common'])
  const { data: previewHtml } = useQuery({
    queryKey: [`${type}-preview`, entityId],
    queryFn: () => {
      switch (type) {
        case 'sales-agreement':
          return getFISalesAgreementAsPreview(entityId)
        case 'purchase-offer':
          return getFIPurchaseOfferAsPreview(entityId)
      }
    },
  })

  const { mutate: downloadPdf } = useMutation({
    mutationFn: () => {
      switch (type) {
        case 'sales-agreement':
          return getFISalesAgreementAsPdf(entityId)
        case 'purchase-offer':
          return getFIPurchaseOfferAsPdf(entityId)
      }
    },
  })

  if (!previewHtml) {
    return null
  }

  if (previewIsOpen === undefined || setPreviewIsOpen === undefined) {
    return null
  }

  return (
    <PreviewDrawer
      title={t('fiSalesAgreement.previewContract')}
      onClose={() => setPreviewIsOpen(false)}
      rightElement={
        <IconButton
          aria-label={t('downloadPDF')}
          icon={<FiDownload />}
          onClick={async () => await downloadPdf()}
        />
      }
    >
      {previewHtml && <div dangerouslySetInnerHTML={{ __html: previewHtml }} />}
    </PreviewDrawer>
  )
}

export default PreviewHtmlModal
