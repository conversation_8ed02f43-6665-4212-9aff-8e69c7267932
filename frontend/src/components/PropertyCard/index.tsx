import {
  Box,
  Card,
  CardBody,
  Text,
  Flex,
  Badge,
  SimpleGrid,
  AvatarGroup,
} from '@chakra-ui/react'
import { IMAGE_SIZE_M_WIDTH } from '@/utils/constants'
import { ImageWithFallback } from '@/components/ImageWithFallback'
import { StrandifiedTag } from '@/components/StrandifiedTag'
import placeholder from '@/cache/images/property_placeholder.png'
import { useTranslation } from 'next-i18next'
import { CustomAvatar } from '../Avatar/CustomAvatar'
import { StatusEnum } from '@/types/property'
import { getFullName, UserBasicInfo, UserListRead } from '@/types/users'

interface Props {
  isPublished?: boolean
  isStrandified?: boolean
  mainImg?: string | null
  priceSale?: string | null
  commission?: string | null
  currency?: string
  realtorUsers: Array<UserListRead | UserBasicInfo>
  location?: string | null
  propertyType?: string | null
  builtArea?: number | null | string
  status?: string | null
  soldBy?: string | null
  onClick: () => void
}

const PropertyCard = ({
  status,
  soldBy,
  isStrandified,
  mainImg,
  priceSale,
  commission,
  realtorUsers,
  location,
  propertyType,
  builtArea,
  onClick,
}: Props) => {
  const { t } = useTranslation(['common'])

  const type = propertyType || ''
  const area = builtArea || ''
  const propertyTypeAndSize = `${type}${area ? `, ${area}` : ''}`

  return (
    <Card
      cursor="pointer"
      borderRadius="lg"
      overflow="hidden"
      boxShadow="md"
      transition="all 0.2s"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: 'lg',
      }}
      onClick={onClick}
    >
      <Box position="relative" overflow="hidden">
        <Box
          _hover={{
            transform: 'scale(1.07)',
          }}
          transition="all 1s"
          height="60"
        >
          <ImageWithFallback
            sizes="(max-height: 240px)"
            alt="Property main image"
            aria-hidden="true"
            fallback={placeholder.src}
            src={
              mainImg
                ? `${mainImg}?width=${IMAGE_SIZE_M_WIDTH}`
                : placeholder.src
            }
          />
        </Box>

        <Flex position="absolute" bottom="3" left="3" gap="2" zIndex={1}>
          <Badge
            justifyContent="center"
            alignItems="center"
            padding="2px 8px"
            bgColor={
              status ? `badge.${status.toLowerCase()}.bg` : 'transparent'
            }
          >
            <Text fontSize="12px" fontWeight="normal" color="black">
              {status === StatusEnum.SOLD && soldBy
                ? `${status} (${soldBy})`
                : status}
            </Text>
          </Badge>
          {isStrandified && <StrandifiedTag />}
        </Flex>
      </Box>

      <CardBody p="4">
        <SimpleGrid columns={2} gap="2" pt="4">
          <Box>
            <Text fontSize="lg">{priceSale}</Text>
            <Text fontSize="sm" color="gray.600" mt="0">
              {commission}
            </Text>
          </Box>
          <Flex gap="2" alignItems="center">
            <AvatarGroup size="md" max={2}>
              {realtorUsers.map((realtor) => (
                <CustomAvatar
                  key={realtor.id}
                  name={getFullName(realtor)}
                  border="2px solid black"
                />
              ))}
            </AvatarGroup>

            <Flex gap="2" flexWrap="wrap">
              <Text fontSize="sm" whiteSpace="nowrap">
                {realtorUsers.length >= 1 && getFullName(realtorUsers[0])}
              </Text>
              <Text fontSize="sm" fontWeight="bold" mt={0}>
                {realtorUsers.length >= 2 &&
                  t(
                    realtorUsers.length === 2
                      ? 'plusSomeOther_one'
                      : 'plusSomeOther_other',
                    { number: realtorUsers.length - 1 }
                  )}
              </Text>
            </Flex>
          </Flex>
        </SimpleGrid>

        <Box mt="3">
          <Text variant="medium" fontWeight="semibold" mb="1">
            {location || ''}
          </Text>

          <Text fontSize="small" color="gray.600" whiteSpace="pre-line">
            {propertyTypeAndSize}
          </Text>
        </Box>
      </CardBody>
    </Card>
  )
}

export default PropertyCard
