import {
  Formik,
  useFormikContext,
  validateYupSchema,
  yupToFormErrors,
} from 'formik'
import { <PERSON><PERSON><PERSON>dapter } from './DeviceAdapter'
import {
  COMMISSION_TYPE,
  DEPOSIT_ACCOUNT_TYPE,
  DetailOfSaleForm,
  INVOICE_TYPE,
  PreOptionsDoS,
} from '@/types/detailOfSale'
import { useSteps } from '@/hooks/useSteps'
import {
  Box,
  Step,
  StepIndicator,
  Stepper,
  StepSeparator,
  StepStatus,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { GeneralDetailOfSaleForm } from '../DetailOfSale/GeneralDetailOfSaleForm'
import { useEffect } from 'react'
import * as Yup from 'yup'
import { useTranslation } from 'next-i18next'
import { ReviewContactDetail } from '../Offer/ContactPreview'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDoS } from '@/queries/detailsOfSale'
import { ReadyToReview } from '../DetailOfSale/ReadyToReview'
import { PROPERTY_TYPE } from '@/types/property'
import { get } from 'lodash'
import { SOWISE_STATUS } from '@/types/common'

const GENERAL_DETAIL_OF_SALE_FORM = 1
const COMMISSION_SPLIT_IN_HOUSE = 2
const READY_TO_SEND = 3

interface Props {
  isOpen?: boolean
  onClose: () => void
  defaultValues?: Partial<DetailOfSaleForm>
  preOptions?: PreOptionsDoS
}

interface BodyProps {
  values: DetailOfSaleForm
  activeStep: number
  steps: number[]
  onValidateMissingInfo: (step: number, value: boolean) => void
}

const CreateDetailOfSaleSchema = (t: (key: string) => string) =>
  Yup.object().shape({
    propertyReference: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('property')),
    buyers: Yup.array()
      .nullable()
      .when(['offerId'], {
        is: (offerId: number) => {
          return !offerId
        },
        then: (schema) =>
          schema
            .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
            .label(t('offer.buyers')),
      }),
    sellers: Yup.array()
      .of(
        Yup.object({
          invoicePercentage: Yup.number()
            .required(t('errors.fieldRequired'))
            .label(t('invoice')),
        })
      )
      .min(1, t('errors.AtLeastOneFieldMustBeSelected'))
      .label(t('offer.sellers')),
    offerAgreedDate: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('offerAgreedDate')),
    salePrice: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('salePrice')),
    reviewerOfficeId: Yup.number()
      .nullable()
      .when('$activeStep', {
        is: (activeStep: number) => {
          return activeStep === READY_TO_SEND
        },
        then: (schema) => schema.required(t('errors.fieldRequired')),
      })
      .label(t('reviewer')),
    depositAccountType: Yup.string().nullable(),
    depositPercentage: Yup.number()
      .nullable()
      .when(['depositAmount', 'depositAccountType'], {
        is: (
          depositAmount: number | null | undefined,
          depositAccountType: string
        ) => {
          if (depositAccountType === DEPOSIT_ACCOUNT_TYPE.NO_DEPOSIT_PAID)
            return false
          return !depositAmount
        },
        then: (schema) => schema.required(t('errors.fieldRequired')),
      })
      .label(t('depositPaid')),

    completionNotaryDeadline: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('deadlineOfTheCompletionNotary')),
    notaryDayBooked: Yup.string()
      .required(t('errors.fieldRequired'))
      .label(t('notaryDayBooked')),
    totalCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('totalCommission')),
    strandCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('strandCommission')),
    otherAgencyCommissionAmount: Yup.number()
      .required(t('errors.fieldRequired'))
      .label(t('otherAgencyCommissionAmount')),
    agents: Yup.array().of(
      Yup.object({
        userId: Yup.string()
          .required(t('errors.fieldRequired'))
          .label(t('agent')),
        commissionPercentage: Yup.number()
          .nullable()
          .when('commissionAmount', {
            is: (commissionAmount: number | null | undefined) => {
              return !commissionAmount
            },
            then: (schema) => schema.required(t('errors.fieldRequired')),
          })
          .label(t('totalCommission')),
      })
    ),
  })

export const EditOrCreateDosForm = ({
  steps,
  activeStep,
  onValidateMissingInfo,
}: BodyProps) => {
  const { validateForm, values } = useFormikContext<DetailOfSaleForm>()

  useEffect(() => {
    validateForm()
  }, [validateForm, activeStep])

  return (
    <Box>
      <Stepper size="sm" colorScheme="blackAlpha" index={activeStep} mb={6}>
        {steps.map((_step, index) => (
          <Step key={index + 1}>
            <StepIndicator>
              <StepStatus />
            </StepIndicator>
            <StepSeparator />
          </Step>
        ))}
      </Stepper>
      <GeneralDetailOfSaleForm
        isOpen={activeStep === GENERAL_DETAIL_OF_SALE_FORM}
        onValidatePropertyHasDoS={(value) =>
          onValidateMissingInfo(GENERAL_DETAIL_OF_SALE_FORM, value)
        }
      />
      <ReviewContactDetail
        enabled={activeStep >= COMMISSION_SPLIT_IN_HOUSE}
        visible={activeStep === COMMISSION_SPLIT_IN_HOUSE}
        contactIds={values.sellerIds || []}
        onValidateMissingInfo={(value) =>
          onValidateMissingInfo(COMMISSION_SPLIT_IN_HOUSE, value)
        }
      />
      {activeStep === READY_TO_SEND && <ReadyToReview />}
    </Box>
  )
}

export const CreateDetailOfSale = ({
  isOpen = true,
  onClose,
  preOptions,
  defaultValues,
}: Props) => {
  const steps = [
    GENERAL_DETAIL_OF_SALE_FORM,
    COMMISSION_SPLIT_IN_HOUSE,
    READY_TO_SEND,
  ]
  const toast = useToast()
  const isMobile = useBreakpointValue({ base: true, md: false })

  const { t } = useTranslation(['common'])
  const { activeStep, goToNext, goToPrevious, handleSetValidStep, isInvalid } =
    useSteps(steps)

  const initialValues: DetailOfSaleForm = {
    propertyType: defaultValues?.offerId
      ? PROPERTY_TYPE.EXIST_PROPERTY
      : PROPERTY_TYPE.CUSTOM_PROPERTY,
    depositAccountType: DEPOSIT_ACCOUNT_TYPE.STRAND_CLIENTS_ACCOUNT,
    strandCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    totalCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    otherAgencyCommissionType: COMMISSION_TYPE.PERCENT_PLUS_VAT,
    agents: [],
    renameDocuments: {},
    separateInvoiceType: INVOICE_TYPE.EVERYONE,
    salePrice: 0,
    propertyReference: '',
    reviewerOfficeId: null,
    totalCommissionAmount: 0,
    strandCommissionAmount: 0,
    buyers: [],
    sellers: [],
    depositAmount: 0,
    externalLead: null,
    otherAgencyCommissionAmount: 0,
    notateSimple: [],
    nieAttachments: [],
    saleAgreementSignedAttachments: [],
    ibiReceiptAttachments: [],
    basuraReceiptAttachments: [],
    copyOfTitleDeed: [],
    sellerInvoices: [],
    notes: null,
    language: 'en',
    externalLeadPercentage: null,
    otherAgencyName: null,
    completionNotaryDeadline: null,
    depositPaidDate: null,
    depositPercentage: null,
    notaryDayBooked: null,
    offerAgreedDate: null,
    ppcDate: null,
    passportPDF: [],
    proofOfTransferDeposit: [],
    reservationAgreementSignedPDF: [],
    status: SOWISE_STATUS.DRAFT,
    ...defaultValues,
  }

  const schema = CreateDetailOfSaleSchema(t)
  const queryClient = useQueryClient()

  useEffect(() => {
    if (preOptions) {
      queryClient.setQueryData(['pre-options-dos'], preOptions)
    }
    return () => {
      queryClient.removeQueries({
        queryKey: ['pre-options-dos'],
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const { mutate: mutationCreateDoS, isPending: isCreating } = useMutation({
    mutationKey: ['detailOfSale'],
    mutationFn: createDoS,
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['list-dos'],
        exact: false,
      })
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('createDoS.sentForReview'),
        description: `${t('createDoS.sendForReviewDescription')} `,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: (e) => {
      const description = get(e, 'response.data.error')
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'sendForSign',
        title: t('errors.title'),
        description: description
          ? JSON.stringify(description)
          : t('errors.detailOfSaleNotCreated'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const handleCreateDoS = async (values: DetailOfSaleForm) => {
    mutationCreateDoS({
      ...values,
      depositAmount: values.depositAmount || 0,
      depositPercentage: values.depositPercentage || 0,
      separateInvoiceForEachSeller:
        values.separateInvoiceType === INVOICE_TYPE.EACH_OTHER,
      sendForReview: activeStep === READY_TO_SEND,
      status: SOWISE_STATUS.IN_REVIEW,
    })
  }

  const acceptTitle = (() => {
    if (activeStep === READY_TO_SEND) {
      return t('sendForReview')
    }
    return t('next')
  })()

  const cancelTitle = (() => {
    if (activeStep === GENERAL_DETAIL_OF_SALE_FORM) {
      return t('saveAsDraft')
    }
    return t('prev')
  })()
  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleCreateDoS}
      validate={(values) => {
        try {
          validateYupSchema(values, schema, true, { activeStep })
        } catch (e) {
          return yupToFormErrors(e)
        }
      }}
    >
      {({ values, isValid, submitForm, dirty }) => {
        const handleClickAccept = () => {
          if (activeStep === READY_TO_SEND) {
            submitForm()
            return
          }
          goToNext()
        }

        const handleClickReject = () => {
          if (activeStep === GENERAL_DETAIL_OF_SALE_FORM) {
            mutationCreateDoS({
              ...values,
              separateInvoiceForEachSeller:
                values.separateInvoiceType === INVOICE_TYPE.EACH_OTHER,
              sendForReview: true,
            })
            return
          }
          goToPrevious()
        }

        const isValidToSaveAsDraft =
          !!values.propertyReference &&
          !!values.sellerIds?.length &&
          (!!values.offerId || !!values.buyers?.length)

        return (
          <DeviceAdapter
            rejectHasSecondaryActions={true}
            size="md"
            isOpen={isOpen}
            onAccept={handleClickAccept}
            onClose={onClose}
            onReject={handleClickReject}
            disableAcceptButton={!isValid || isInvalid || isCreating}
            isLoading={isCreating}
            title={t('actionDropdown.create.DoS')}
            acceptTitle={acceptTitle}
            cancelTitle={cancelTitle}
            disableRejectButton={
              !isValidToSaveAsDraft || isCreating || isInvalid
            }
            dirty={dirty}
          >
            <EditOrCreateDosForm
              onValidateMissingInfo={handleSetValidStep}
              values={values}
              activeStep={activeStep}
              steps={steps}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}
