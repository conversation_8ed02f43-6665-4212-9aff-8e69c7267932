import { Flex, Textarea, useBreakpointValue, useToast } from '@chakra-ui/react'
import { Formik, FormikProps, useFormikContext } from 'formik'
import { InputWithLabel } from '../Form/Input'
import { TFunction, useTranslation } from 'next-i18next'
import { useMutation, useQuery } from '@tanstack/react-query'
import { getContacts } from '@/queries/contact'
import * as Yup from 'yup'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import LabeledField from '../Form/LabeledField'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import { CreateEmailMutationProps, EmailListRead } from '@/types/email'
import { createEmail, getMatchMakingEmailTemplate } from '@/queries/matchmaking'
import { getGroups } from '@/queries/groups'
import { MailgunEmailTemplate } from '@/types/matchmaking'
import EmailTemplateDropdown from '../MatchMaking/EmailTemplateDropdown'
import { <PERSON>ceAdapter } from './DeviceAdapter'
import { CreateContactProps } from '@/types/contact'
import { SchemaContactListRead } from '@/generated-types/api'

const CreateEmailSchema = Yup.object().shape({
  subject: Yup.string().required('Subject is required'),
  body: Yup.string().required('Body is required'),
  recipientIds: Yup.array().of(Yup.number()),
  groupIds: Yup.array().of(Yup.number()),
})

const Body = ({
  t,
  sellersPaginationProps,
  groupsPaginationProps,
  emailTemplates,
  isAllContact,
  initialBody = '',
  preSelectedContacts,
}: {
  t: TFunction
  sellersPaginationProps: PaginationProps<SchemaContactListRead>
  groupsPaginationProps?: PaginationProps<{
    value: number
    label: string
  }>
  formik: FormikProps<CreateEmailMutationProps>
  emailTemplates?: MailgunEmailTemplate[]
  isAllContact?: boolean
  initialBody?: string
  preSelectedContacts?: {
    id: number
    name: string
  }[]
}) => {
  const { setFieldValue } = useFormikContext<CreateContactProps>()

  return (
    <Flex direction="column" gap="2">
      {!isAllContact && (
        <>
          <Flex width="100%" gap="2">
            <LabeledField label={t('createEmail.recipients')}>
              <MultiSelectDropdown
                name="recipientIds"
                placement="bottomStart"
                cleanable={false}
                valueKey="id"
                labelKey="name"
                {...sellersPaginationProps}
                preOptions={preSelectedContacts}
                data={sellersPaginationProps.data.map((contact) => {
                  return {
                    ...contact,
                    name: contact.name,
                  }
                })}
              />
            </LabeledField>
          </Flex>
          <Flex width="100%" gap="2">
            <LabeledField label={t('groups')}>
              <MultiSelectDropdown
                name="groupIds"
                placement="bottomStart"
                cleanable={false}
                {...groupsPaginationProps}
              />
            </LabeledField>
          </Flex>
        </>
      )}
      <Flex width="100%" gap="2">
        <EmailTemplateDropdown emailTemplates={emailTemplates || []} />
      </Flex>
      <Flex width="100%" gap="2">
        <InputWithLabel
          label={t('createEmail.subject')}
          placeholder={t('createEmail.subject') ?? undefined}
          name="subject"
          type="text"
        />
      </Flex>
      <Flex width="100%" gap="2">
        <LabeledField label={t('createEmail.message')} width="100%">
          <Textarea
            placeholder={t('createEmail.message') ?? undefined}
            name={t('createEmail.message') as string}
            size="sm"
            onBlur={(e) => setFieldValue('body', e.currentTarget.value)}
            rows={4}
            defaultValue={initialBody}
            resize="none"
            style={{ height: 'auto' }}
          />
        </LabeledField>
      </Flex>
    </Flex>
  )
}

export const CreateEmail = ({
  onClose,
  groupId,
  initialBody = '',
  preSelectedContacts,
}: {
  onClose: (group?: EmailListRead) => void
  onSuccess?: (group: EmailListRead) => void
  groupId?: number
  initialBody?: string
  preSelectedContacts?: {
    id: number
    name: string
  }[]
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts-group'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }),
    }
  )

  const { paginationProps: groupsPaginationProps } = usePaginationDropdownData({
    queryKey: ['groups'],
    queryFn: (params) =>
      getGroups({
        ...params,
      }).then((data) => {
        return {
          records: data.records.map((group) => ({
            value: group.id,
            label: group.name,
          })),
          metadata: data.metadata,
        }
      }),
  })

  const initialValues: CreateEmailMutationProps = {
    subject: '',
    recipientIds: preSelectedContacts?.map((contact) => contact.id) || [],
    emailTemplateName: '',
    body: initialBody,
    groupIds: groupId ? [groupId] : [],
  }

  const { isPending: isCreatingEmailLoading, mutate: mutationCreateEmail } =
    useMutation({
      mutationKey: ['email'],
      mutationFn: createEmail,
      onSuccess: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('createEmail.emailSent'),
          description: t('createEmail.emailSent'),
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        onClose()
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.emailNotSent'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const { data: emailTemplates } = useQuery<MailgunEmailTemplate[]>({
    queryKey: ['email-templates'],
    queryFn: () => getMatchMakingEmailTemplate(),
  })

  const onSubmit = async (values: CreateEmailMutationProps) => {
    mutationCreateEmail(values)
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={CreateEmailSchema}
      validateOnMount
    >
      {(formik: FormikProps<CreateEmailMutationProps>) => {
        return (
          <DeviceAdapter
            isOpen={true}
            onClose={onClose}
            onAccept={formik.submitForm}
            onReject={onClose}
            title={t('createEmail.title')}
            acceptTitle={t('create') || undefined}
            isLoading={isCreatingEmailLoading}
            disableAcceptButton={!formik.isValid || formik.isSubmitting}
            dirty={formik.dirty}
            isSubmitting={formik.isSubmitting}
          >
            <Body
              t={t}
              sellersPaginationProps={sellersPaginationProps}
              groupsPaginationProps={groupsPaginationProps}
              formik={formik}
              emailTemplates={emailTemplates}
              initialBody={initialBody}
              preSelectedContacts={preSelectedContacts}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}

export const CreateContactEmail = ({
  onClose,
  groupId,
  sellersPaginationProps,
  isAllContact,
  preSelectedContacts,
}: {
  onClose: (group?: EmailListRead) => void
  onSuccess?: (group: EmailListRead) => void
  groupId?: number
  sellersPaginationProps: PaginationProps<SchemaContactListRead>
  isAllContact?: boolean
  preSelectedContacts?: {
    id: number
    name: string
  }[]
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()

  const { paginationProps: groupsPaginationProps } = usePaginationDropdownData({
    queryKey: ['groups'],
    queryFn: (params) =>
      getGroups({
        ...params,
      }).then((data) => {
        return {
          records: data.records.map((group) => ({
            value: group.id,
            label: group.name,
          })),
          metadata: data.metadata,
        }
      }),
  })

  const initialValues: CreateEmailMutationProps = {
    subject: '',
    recipientIds: preSelectedContacts?.map((contact) => contact.id) || [],
    emailTemplateName: '',
    body: '',
    groupIds: groupId ? [groupId] : [],
  }

  const { isPending: isCreatingEmailLoading, mutate: mutationCreateEmail } =
    useMutation({
      mutationKey: ['email'],
      mutationFn: createEmail,
      onSuccess: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('createEmail.emailSent'),
          description: t('createEmail.emailSent'),
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        onClose()
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.emailNotSent'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const { data: emailTemplates } = useQuery<MailgunEmailTemplate[]>({
    queryKey: ['email-templates'],
    queryFn: () => getMatchMakingEmailTemplate(),
  })

  const onSubmit = async (values: CreateEmailMutationProps) => {
    mutationCreateEmail({
      ...values,
      isAllContact: isAllContact || false,
    })
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={CreateEmailSchema}
      validateOnMount
    >
      {(formik: FormikProps<CreateEmailMutationProps>) => {
        return (
          <DeviceAdapter
            isOpen={true}
            onClose={onClose}
            onAccept={formik.submitForm}
            onReject={onClose}
            title={t('createEmail.title')}
            acceptTitle={t('create') || undefined}
            isLoading={isCreatingEmailLoading}
            disableAcceptButton={!formik.isValid || formik.isSubmitting}
            dirty={formik.dirty}
            isSubmitting={formik.isSubmitting}
          >
            <Body
              t={t}
              sellersPaginationProps={sellersPaginationProps}
              groupsPaginationProps={groupsPaginationProps}
              formik={formik}
              emailTemplates={emailTemplates}
              isAllContact={isAllContact}
              preSelectedContacts={preSelectedContacts}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}
