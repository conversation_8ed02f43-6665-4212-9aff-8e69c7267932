import {
  Flex,
  Text,
  Heading,
  useBreakpointValue,
  useToast,
  Spinner,
  Textarea,
  AccordionPanel,
  AccordionItem,
  Accordion,
  AccordionButton,
  AccordionIcon,
  Box,
  ListItem,
  UnorderedList,
  <PERSON><PERSON>,
  Link,
  Stack,
} from '@chakra-ui/react'
import {
  FieldArray,
  Formik,
  FormikErrors,
  FormikTouched,
  useFormikContext,
} from 'formik'
import { InputWithLabel } from '../Form/Input'
import { TFunction, useTranslation } from 'next-i18next'
import { useMutation, useQuery } from '@tanstack/react-query'
import { createContact, getBanks, getPreviewContacts } from '@/queries/contact'
import LabeledField from '../Form/LabeledField'
import { getUsers } from '@/queries/users'
import { Option } from '@/types/common'
import * as Yup from 'yup'
import { CreateContactProps } from '@/types/contact'
import { Checkbox } from 'rsuite'
import { useCallback, useState } from 'react'
import countryList from 'react-select-country-list'
import { useTags } from '@/queries/tags'
import SelectDropdown from '../DropdownMenu/SelectDropdown'
import { useDecodedSessionToken } from '@/hooks/useDecodedSessionToken'
import { CONTACT_TYPES, MOST_SPOKEN_LANGUAGES } from '@/utils/constants'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getFullName, UserListRead } from '@/types/users'
import { useDebounce } from 'use-debounce'
import { getGroups } from '@/queries/groups'
import { GroupListRead } from '@/types/group'
import { FeatureFlag } from '../FeatureFlag'
import { useCreateGroupModal } from '@/hooks/useCommonModal'
import { CreateGroup } from './CreateGroup'
import { useDebounceState } from '@/hooks/useDebouceState'
import { BadgeItem } from '../Badge/BadgeItem'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { LocationsField, PropertyTypeField } from '../Form/PropertyFields'
import ContactClientTypeAndTags, {
  CONTACT_CLIENT_TYPE,
} from '../Contact/ContactClientTypeAndTags'
import SourceContactInput from '../Contact/SourceContactInput'
import { SchemaContactListRead } from '@/generated-types/api'
import { DeviceAdapter } from './DeviceAdapter'
import { emptyCreateContact } from '@/types/contact'

const LIST_TAGS_AVAILABLE_MATERIAL = ['Buyer', 'Rental']

const CreateContactSchema = Yup.object().shape({
  name: Yup.string()
    .required('Name is required')
    .test(
      'not-only-spaces',
      'This field cannot be empty or just spaces',
      (value) => value?.trim().length > 0
    ),
  phoneNumbers: Yup.array().of(Yup.string()),
  email: Yup.string()
    .nullable()
    .test('phone-or-email', 'Enter at least Phone 1 or Email', (_, context) => {
      const isCollaborator =
        context.parent.tags?.some(
          (item) => item.label === CONTACT_CLIENT_TYPE.COLLABORATOR
        ) || false
      const phoneNumbers = context.parent.phoneNumbers
      const email = context.parent.email
      return phoneNumbers?.some((item) => !!item) || email || isCollaborator
    }),
  assignedTo: Yup.array()
    .of(Yup.number().required())
    .min(1, 'Contact should be assigned to at least one Agent.'),
  source: Yup.string().nullable().required('Source is required'),
})

const countryOptions = countryList().getData()
const languageOptions = MOST_SPOKEN_LANGUAGES.map((language) => ({
  label: language,
  value: language,
}))

const Body = ({
  t,
  currentUser,
  usersPaginationProps,
  sameEmailContactsPaginationProps,
  existNameData,
  onEmailChange,
  onNameChange,
  listOfTags,
  listOfBanks,
  errors,
  touched,
  isLoadingTags,
  isLoadingBanks,
  isValidatingUniqueEmail,
  groupsPaginationProps,
  onGroupCreated,
}: {
  t: TFunction
  currentUser: { id: number; name: string } | undefined
  usersPaginationProps: PaginationProps<UserListRead>
  sameEmailContactsPaginationProps: PaginationProps<SchemaContactListRead>
  existNameData: SchemaContactListRead[] | undefined
  onEmailChange: (email: string) => void
  onNameChange: (formValue: CreateContactProps) => void
  listOfTags: Option[]
  listOfBanks: Option[]
  errors: FormikErrors<CreateContactProps>
  touched: FormikTouched<CreateContactProps>
  isLoadingTags: boolean
  isLoadingBanks: boolean
  isValidatingUniqueEmail: boolean
  groupsPaginationProps: PaginationProps<GroupListRead>
  onGroupCreated: (group: GroupListRead) => void
}) => {
  const { setFieldValue, values } = useFormikContext<CreateContactProps>()
  const [residentOfSpain, setResidentOfSpain] = useState<boolean>(false)
  const [accordionIndexes, setAccordionIndexes] = useState<number[]>([-1, -1])
  const { organization } = useUserAndOrganization()

  const {
    isOpen: isGroupDrawerOpen,
    onOpen: onGroupDrawerOpen,
    onClose: onGroupDrawerClose,
  } = useCreateGroupModal()

  const handleCreateGroupClick = useCallback(async () => {
    const groupCreated = await onGroupDrawerOpen()
    if (groupCreated?.id) onGroupCreated(groupCreated)
  }, [onGroupCreated, onGroupDrawerOpen])

  const handleAccordionChange = (index: number) => {
    const newAccordionIndexes = accordionIndexes.includes(index)
      ? accordionIndexes.filter((i) => i !== index)
      : [...accordionIndexes, index]
    setAccordionIndexes(newAccordionIndexes)
  }
  const handleBedroomChange = (value: number) => {
    setFieldValue('minBedrooms', value)
  }

  return (
    <Flex direction="column" gap="8">
      <Flex direction="column" gap="4">
        <Flex direction="column">
          <Heading variant="H1">{t('createContact.contactInfo')}</Heading>
          <Text fontSize={'14px'} mt={'2'}>
            {t('general.mandatory')}
          </Text>
        </Flex>
        <Flex direction="column" gap="2">
          <Flex width="100%" gap="2">
            <InputWithLabel
              label={`${t('createContact.name')}*`}
              placeholder={t('createContact.name') ?? undefined}
              name="name"
              type="text"
              error={errors.name}
              touched={touched.name}
              onChange={(e) => {
                const event =
                  e as unknown as React.ChangeEvent<HTMLInputElement>
                const value = event.target.value as string
                values.name = value
                setFieldValue('name', value)
                onNameChange({ ...values, name: value })
              }}
            />
          </Flex>
          {existNameData && existNameData.length > 0 && (
            <Box width="100%" gap="2">
              <Text variant="small" color="grays.gray7" pb={[0, '4px']}>
                {t('createContact.existNameContact')}
              </Text>
              <UnorderedList width="100%" gap="2">
                {existNameData
                  .map((contact) => contact.assignedToUsers) // Extract assignedToUsers arrays
                  .flat()
                  .map((user) => {
                    return (
                      <ListItem
                        fontSize={'14px'}
                        color={'orange.600'}
                        key={user.id}
                      >
                        <Link
                          color="black"
                          textDecoration="underline"
                          target={'_blank'}
                          href={`/users/${user.id}`}
                        >
                          {user.firstName} {user.lastName}
                          <span
                            className="material-symbols-outlined"
                            style={{
                              fontSize: '16px',
                              fontWeight: '400',
                              paddingLeft: '6px',
                            }}
                          >
                            open_in_new
                          </span>
                        </Link>
                      </ListItem>
                    )
                  })}
              </UnorderedList>
            </Box>
          )}
          <Flex width="100%" gap="2">
            <FieldArray
              name="phoneNumbers"
              render={(_arrayHelpers) => (
                <>
                  <InputWithLabel
                    label={`${t('phone')} 1`}
                    placeholder={t('phone') ?? undefined}
                    name={`phoneNumbers.${0}`}
                    type="text"
                    onChange={(e) => {
                      const event =
                        e as unknown as React.ChangeEvent<HTMLInputElement>
                      const value = event.target.value as string
                      setFieldValue(`phoneNumbers.${0}`, value)
                      const data = { ...values }
                      if (data.phoneNumbers) data.phoneNumbers[0] = value
                      onNameChange(data)
                    }}
                  />
                  <InputWithLabel
                    label={`${t('phone')} 2`}
                    placeholder={t('phone') ?? undefined}
                    name={`phoneNumbers.${1}`}
                    type="text"
                    onChange={(e) => {
                      const event =
                        e as unknown as React.ChangeEvent<HTMLInputElement>
                      const value = event.target.value as string
                      setFieldValue(`phoneNumbers.${1}`, value)
                      const data = { ...values }
                      if (data.phoneNumbers) data.phoneNumbers[1] = value
                      onNameChange(data)
                    }}
                  />
                </>
              )}
            />
          </Flex>
          <Flex width="100%" gap="2">
            <InputWithLabel
              label={t('email')}
              placeholder={t('email') ?? undefined}
              name="email"
              onChange={(e) => {
                const event =
                  e as unknown as React.ChangeEvent<HTMLInputElement>
                const value = event.target.value as string
                setFieldValue('email', value)
                onEmailChange(value)
              }}
              type="text"
              error={errors.email}
              touched={touched.email}
            />
          </Flex>
          {!isValidatingUniqueEmail &&
            values.email &&
            sameEmailContactsPaginationProps.data.length === 0 && (
              <Text fontSize={'14px'} color="green.500">
                {t('createContact.emailIsAvailable')}
              </Text>
            )}
          {sameEmailContactsPaginationProps.data.length > 0 && (
            <Text fontSize={'14px'} color="red.500">
              {t('createContact.emailAlreadyExists')}.
              {t('createContact.contactToAssignedPersons')}:
              {sameEmailContactsPaginationProps.data[0].assignedToUsers
                ?.length === 0 && <span> {t('createContact.admin')}</span>}
            </Text>
          )}
          {sameEmailContactsPaginationProps.data.length > 0 &&
            sameEmailContactsPaginationProps.data[0].assignedToUsers?.length >
              0 && (
              <UnorderedList>
                {sameEmailContactsPaginationProps.data[0].assignedToUsers.map(
                  (user) => {
                    return (
                      <ListItem key={user.id} fontSize={'14px'}>
                        {user.firstName} {user.lastName}, {user.email}
                      </ListItem>
                    )
                  }
                )}
              </UnorderedList>
            )}
          <Flex width="100%" gap="2">
            <InputWithLabel
              label={t('company')}
              placeholder={t('company') ?? undefined}
              name="company"
              type="text"
            />
          </Flex>
        </Flex>
      </Flex>
      <Accordion defaultIndex={[]} allowMultiple index={accordionIndexes}>
        <AccordionItem>
          <AccordionButton
            p="0"
            mb="0"
            height="47px"
            _hover={{ backgroundColor: 'transparent' }}
            onClick={() => {
              handleAccordionChange(0)
            }}
          >
            <Box as="span" flex="1" textAlign="left">
              <Text fontSize="xl">{t('moreInfo')}</Text>
            </Box>
            <Flex
              alignItems="center"
              justifyContent="center"
              width="30px"
              height="30px"
            >
              <AccordionIcon color="#1C1B1F" />
            </Flex>
          </AccordionButton>

          <AccordionPanel pb={2} pl="0" pt="0">
            <Flex direction="column" gap="4">
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('website')}
                  placeholder={t('website') ?? undefined}
                  name="website"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('address')}
                  placeholder={t('address') ?? undefined}
                  name="address"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('city')}
                  placeholder={t('city') ?? undefined}
                  name="city"
                  type="text"
                />
                <InputWithLabel
                  label={t('postCode')}
                  placeholder={t('postCode') ?? undefined}
                  name="postCode"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('country')}
                  placeholder={t('country') ?? undefined}
                  name="country"
                  type="text"
                />
              </Flex>
              <LabeledField label={t('nationality')} width={'100%'}>
                <SelectDropdown
                  valueKey="label"
                  labelKey="label"
                  data={countryOptions}
                  renderMenu={(menu) => {
                    if (countryOptions.length === 0) {
                      return (
                        <div
                          style={{
                            padding: 4,
                            color: '#999',
                            textAlign: 'center',
                          }}
                        >
                          <Spinner />
                        </div>
                      )
                    }
                    return menu
                  }}
                  onChange={(value) => {
                    setFieldValue('nationality', value)
                  }}
                />
              </LabeledField>
              <LabeledField label={t('preferredLanguage')} width={'100%'}>
                <SelectDropdown
                  valueKey="label"
                  labelKey="label"
                  data={languageOptions}
                  renderMenu={(menu) => {
                    if (languageOptions.length === 0) {
                      return (
                        <div
                          style={{
                            padding: 4,
                            color: '#999',
                            textAlign: 'center',
                          }}
                        >
                          <Spinner />
                        </div>
                      )
                    }
                    return menu
                  }}
                  onChange={(value) => {
                    setFieldValue('preferredLanguage', value)
                  }}
                />
              </LabeledField>
              <LabeledField label={t('type')} width={'100%'}>
                <SelectDropdown
                  data={CONTACT_TYPES.map((type) => ({
                    value: type,
                    label: type,
                  }))}
                  onChange={(value) => {
                    setFieldValue('type', value)
                  }}
                  searchable={false}
                />
              </LabeledField>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('passportNumber')}
                  placeholder={t('passportNumber') ?? undefined}
                  name="passportNumber"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('socialSecurityNumber')}
                  placeholder={t('socialSecurityNumber') ?? undefined}
                  name="socialSecurityNumber"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('businessId')}
                  placeholder={t('businessId') ?? undefined}
                  name="businessId"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('vatNumber')}
                  placeholder={t('vatNumber') ?? undefined}
                  name="vatNumber"
                  type="text"
                />
              </Flex>
              <LabeledField label={t('bank')} width={'100%'}>
                <SelectDropdown
                  data={listOfBanks}
                  onChange={(value) => {
                    setFieldValue('bankId', value)
                  }}
                  searchable={false}
                  loading={isLoadingBanks}
                />
              </LabeledField>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('iban')}
                  placeholder={t('iban') ?? undefined}
                  name="iban"
                  type="text"
                />
              </Flex>
              <Flex width="100%" gap="2">
                <InputWithLabel
                  label={t('bic')}
                  placeholder={t('bic') ?? undefined}
                  name="bic"
                  type="text"
                />
              </Flex>
              <Checkbox
                name="countrySpecific.residentOfSpain"
                checked={residentOfSpain}
                onChange={(event, value) => {
                  setResidentOfSpain(value)
                  setFieldValue('countrySpecific.residentOfSpain', value)
                }}
              >
                {t('residentOfSpain')}
              </Checkbox>
            </Flex>
          </AccordionPanel>
        </AccordionItem>
        <AccordionItem className="show-warning-when-has-error-field">
          <AccordionButton
            p="0"
            mb="0"
            height="47px"
            _hover={{ backgroundColor: 'transparent' }}
            onClick={() => {
              handleAccordionChange(1)
            }}
          >
            <Box
              as="span"
              className="material-symbols-outlined warning"
              color="red"
              display="none"
            >
              warning
            </Box>
            <Box as="span" flex="1" textAlign="left">
              <Text fontSize="xl">{t('leadInfo')}</Text>
            </Box>
            <Flex
              alignItems="center"
              justifyContent="center"
              width="30px"
              height="30px"
            >
              <AccordionIcon color="#1C1B1F" />
            </Flex>
          </AccordionButton>

          <AccordionPanel pb={2} pl="0" pt="0">
            <Flex direction="column" gap="4">
              <Flex width="100%" gap="2">
                <LabeledField label={`${t('assignedTo')}*`} width="100%">
                  <MultiSelectDropdown
                    name="assignedTo"
                    valueKey="id"
                    labelKey="name"
                    preOptions={currentUser ? [currentUser] : []}
                    {...usersPaginationProps}
                    data={usersPaginationProps.data.map((user) => ({
                      ...user,
                      name: getFullName(user),
                    }))}
                  />
                </LabeledField>
              </Flex>
              <FeatureFlag featureFlag="groupUser">
                <Flex width="100%" gap="2">
                  <LabeledField label={t('groups')} width="100%">
                    <MultiSelectDropdown
                      name="groups"
                      valueKey="id"
                      labelKey="name"
                      {...groupsPaginationProps}
                    />
                  </LabeledField>
                </Flex>
                <Flex width="100%" gap="2">
                  <Button
                    bg="white"
                    color="black"
                    border="1px solid black" // Optional: Add a border to make the button more visible
                    _hover={{ bg: 'black', color: 'white' }}
                    width="100%"
                    onClick={() => {
                      setAccordionIndexes([-1, -1])
                      handleCreateGroupClick()
                    }}
                  >
                    {t('createGroup.newGroup')}
                    <span className="material-symbols-outlined">add</span>
                  </Button>
                </Flex>
              </FeatureFlag>
              <Flex width="100%" gap="2">
                <SourceContactInput label={`${t('source')}*`} />
              </Flex>
              <ContactClientTypeAndTags
                listOfTags={listOfTags}
                isLoadingTags={isLoadingTags}
              />
              <Flex width="100%" direction="column">
                <Text fontSize="12px" fontWeight="400">
                  {t('notes')}
                </Text>
                <Textarea
                  width="100%"
                  size="xs"
                  whiteSpace={'pre-wrap'}
                  overflowWrap={'break-word'}
                  placeholder={t('notes') ?? undefined}
                  onBlur={(e) => setFieldValue(`notes`, e.currentTarget.value)}
                />
              </Flex>
            </Flex>
          </AccordionPanel>
        </AccordionItem>
        {values.tags.some((item) =>
          LIST_TAGS_AVAILABLE_MATERIAL.map((tag) => tag.toLowerCase()).includes(
            item.label.toString().toLowerCase()
          )
        ) && (
          <AccordionItem>
            <AccordionButton
              p="0"
              mb="0"
              height="47px"
              _hover={{ backgroundColor: 'transparent' }}
              onClick={() => {
                handleAccordionChange(2)
              }}
            >
              <Box as="span" flex="1" textAlign="left">
                <Heading fontSize="xl">{t('material')}</Heading>
              </Box>
              <Flex
                alignItems="center"
                justifyContent="center"
                width="30px"
                height="30px"
              >
                <AccordionIcon color="#1C1B1F" />
              </Flex>
            </AccordionButton>

            <AccordionPanel pb={2} pl="0" pt="0">
              <Stack>
                <Flex textAlign="left">
                  <Text variant="accordion">{t('filters.price')}</Text>
                </Flex>
                {/* Price */}
                <Flex width={'100%'} gap={2}>
                  <InputWithLabel
                    label={t('filters.minimum')}
                    name="minPrice"
                    type="currency"
                    rightElement={<span> {organization?.currency}</span>}
                    placeholder="0"
                    min={0}
                  />

                  <InputWithLabel
                    label={t('filters.maximum')}
                    name="maxPrice"
                    type="currency"
                    rightElement={<span> {organization?.currency}</span>}
                    placeholder="0"
                    min={0}
                  />
                </Flex>
                {/* Area and type */}
                <Flex width={'100%'} gap={2} direction={['column', 'row']}>
                  <Flex width={['100%', '49%']}>
                    <LocationsField
                      isMultiSelect={true}
                      hasDefaultLabel={false}
                      label={t('filters.area') ?? undefined}
                    />
                  </Flex>
                  <Flex width={['100%', '49%']}>
                    <PropertyTypeField
                      formikFieldName="propertyTypes"
                      isMultiSelect
                      placement="bottomEnd"
                    />
                  </Flex>
                </Flex>

                {/* Bedrooms */}
                <Flex direction={'column'} gap={2}>
                  <Flex textAlign="left">
                    <Text variant="accordion">{t('filters.bedrooms')}</Text>
                  </Flex>
                  <Flex gap={2}>
                    <Flex
                      direction={'row'}
                      overflowX={'scroll'}
                      css={{
                        scrollbarWidth: 'none',
                      }}
                      width={'100%'}
                      gap={2}
                    >
                      {Array(8)
                        .fill(0)
                        .map((_, index) => (
                          <BadgeItem
                            key={`bedrooms-${index}`}
                            label={`${index + 1} +`}
                            value={index + 1}
                            onClick={handleBedroomChange}
                            isChecked={index + 1 === values.minBedrooms}
                          />
                        ))}
                    </Flex>
                  </Flex>
                </Flex>
              </Stack>
            </AccordionPanel>
          </AccordionItem>
        )}
      </Accordion>
      {isGroupDrawerOpen && <CreateGroup onClose={onGroupDrawerClose} />}
    </Flex>
  )
}

export const CreateContact = ({
  onClose,
  onSuccess,
}: {
  onClose: (contact?: SchemaContactListRead) => void
  onSuccess?: (contact: SchemaContactListRead) => void
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const decodedToken = useDecodedSessionToken()

  const [email, setEmail] = useState<string>('')
  const [contactEmail] = useDebounce(email, 1000)
  const [contactName, setContactName] = useDebounceState('')
  const [values, setValues] = useState<CreateContactProps | null>(null)

  const {
    paginationProps: sameEmailContactsPaginationProps,
    isPending: isLoadingSameEmailContacts,
  } = usePaginationDropdownData({
    queryKey: ['previewContacts', contactEmail],
    queryFn: (params) =>
      getPreviewContacts(true, {
        ...params,
        email: contactEmail,
      }),
  })

  const existNameData = useQuery({
    queryKey: ['getExistName', contactName],

    queryFn: () =>
      getPreviewContacts(false, {
        email: contactEmail,
        name: values?.name,
        phone_number: values?.phoneNumbers?.[0] || values?.phoneNumbers?.[1],
      }),

    enabled: !!contactName,
  })

  const handleEmailChange = (email: string) => {
    setEmail(email)
  }

  const handleNameChange = (values: CreateContactProps) => {
    setValues(values)
    setContactName(values.name ?? '' + values.phoneNumbers?.toString())
  }

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        onlyActive: true,
      }),
  })

  const { paginationProps: groupsPaginationProps, refetch: groupRefetch } =
    usePaginationDropdownData({
      queryKey: ['groups'],
      queryFn: (params) =>
        getGroups({
          ...params,
        }),
    })

  const { data: listOfTags = [], isPending: isLoadingTags } = useTags()

  const { data: banks, isPending: isLoadingBanks } = useQuery({
    queryKey: ['banks'],
    queryFn: getBanks,
  })
  const listOfBanks: Option[] =
    banks?.map((bank) => ({
      value: bank.id,
      label: bank.name,
    })) || []

  const initialValues: CreateContactProps = {
    ...emptyCreateContact,
    assignedTo: decodedToken?.sub ? [decodedToken?.sub] : [],
  }

  const { isPending: isCreatingContactLoading, mutate: mutationCreateContact } =
    useMutation({
      mutationKey: ['contact'],
      mutationFn: createContact,
      onSuccess: (contactCreated: SchemaContactListRead) => {
        if (onSuccess) {
          onSuccess(contactCreated)
        } else {
          toast({
            position: isMobile ? 'top' : 'bottom-left',
            title: t('createContact.createdTitle'),
            description: `${t('createContact.createdDescription')} "${
              contactCreated?.name
            }"`,
            status: 'success',
            duration: 9000,
            isClosable: true,
            variant: 'customSuccess',
          })
          onClose(contactCreated)
        }
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.contactNotCreated'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const onSubmit = async (values: CreateContactProps) => {
    values.newTags = values.tags
      .filter((tag) => typeof tag.value === 'string')
      .map((tag) => tag.value as string)
    values.existingTags = values.tags
      .filter((tag) => typeof tag.value === 'number')
      .map((tag) => tag.value as number)
    mutationCreateContact(values)
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={CreateContactSchema}
      validateOnMount
    >
      {({
        submitForm,
        isSubmitting,
        errors,
        touched,
        dirty,
        setFieldValue,
      }) => {
        return (
          <DeviceAdapter
            size="md"
            isOpen={true}
            onAccept={submitForm}
            onClose={onClose}
            onReject={onClose}
            disableAcceptButton={isSubmitting}
            isLoading={isCreatingContactLoading}
            title={t('createContact.title')}
            acceptTitle={t('create') || undefined}
            dirty={dirty}
            isSubmitting={isSubmitting}
          >
            <Body
              t={t}
              currentUser={
                decodedToken && {
                  id: decodedToken.sub,
                  name: `${decodedToken.userFirstName} ${decodedToken.userLastName}`,
                }
              }
              usersPaginationProps={usersPaginationProps}
              sameEmailContactsPaginationProps={
                sameEmailContactsPaginationProps
              }
              existNameData={existNameData.data?.records}
              onEmailChange={handleEmailChange}
              onNameChange={handleNameChange}
              listOfTags={listOfTags}
              listOfBanks={listOfBanks}
              errors={errors}
              touched={touched}
              isLoadingTags={isLoadingTags}
              isLoadingBanks={isLoadingBanks}
              isValidatingUniqueEmail={
                isLoadingSameEmailContacts || email !== contactEmail
              }
              groupsPaginationProps={groupsPaginationProps}
              onGroupCreated={(group) => {
                groupRefetch()
                setFieldValue('groups', [
                  ...(initialValues.groups || []),
                  group.id,
                ])
              }}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}
