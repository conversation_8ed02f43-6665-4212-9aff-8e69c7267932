import { Box, Flex, Text, useBreakpointValue, useToast } from '@chakra-ui/react'

import { Formik, Form } from 'formik'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { getPropertyListingTypes, postProperty } from '@/queries/property'
import {
  CommissionTypeEnum,
  CreatePropertyFormTypes,
  CreatePropertyMutationProps,
  ListingTypeEnum,
  PropertyListingType,
  ReadProperty,
} from '@/types/property'
import * as Yup from 'yup'

import { useTranslation } from 'next-i18next'
import { InputWithLabel } from '../Form/Input'
import {
  ContractTypeField,
  LocationsField,
  PropertyTypeField,
} from '../Form/PropertyFields'
import { getCurrentUser, getUsers } from '@/queries/users'
import { CustomModal } from '../Modal/Modal'
import { CreatePropertyDrawer } from './CreatePropertyDrawer'
import LabeledField from '../Form/LabeledField'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import { useMemo, useState } from 'react'
import Radio from '../Form/Radio'
import { getContacts } from '@/queries/contact'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { getFullName } from '@/types/users'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'

type InitialValues = CreatePropertyFormTypes

type Option = {
  label: string
  value: string | number
}

const mapListingTypeToOption = (
  listingTypes?: PropertyListingType[]
): Option[] => {
  if (!listingTypes) {
    return []
  }

  return listingTypes.map((listingType) => ({
    label: listingType.name,
    value: listingType.id,
  }))
}

const CreateProperty = ({
  onClose,
}: {
  onClose: (property?: ReadProperty) => void
}) => {
  const { t } = useTranslation(['common'])
  const { isUserAdmin, user } = useUserAndOrganization()

  const queryClient = useQueryClient()

  const { data: currentUser } = useQuery({
    queryKey: ['me'],
    queryFn: () => getCurrentUser(),
  })

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        onlyActive: true,
      }),
  })

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }),
    }
  )

  const { data: listingTypesData, isPending: isLoadingListingTypesData } =
    useQuery({
      queryKey: ['propertyListingTypes'],
      queryFn: getPropertyListingTypes,
    })

  const validationSchema = Yup.object().shape({
    realtorUserIds: Yup.array()
      .of(Yup.number().required())
      .min(1, 'At least one agent is required')
      .test(
        'has-it',
        `${user?.firstName} ${user?.lastName} should be selected`,
        (value) => {
          const isAdminOrAssign =
            value && user && (isUserAdmin || value.includes(user.id))

          return isAdminOrAssign
        }
      ),
    propertyTypeId: Yup.number().required(),
    isExclusive: Yup.boolean().required(),
    listingTypeIds: Yup.array()
      .of(Yup.number().required())
      .min(1, 'At least one listing type is required'),
    areaLevel1Id: Yup.string().required(),
    commissionType: Yup.string().required('Commission type is required'),
    commission: Yup.number().when(
      'commissionType',
      ([commissionType], schema) => {
        if (commissionType === CommissionTypeEnum.PERCENT) {
          return schema
            .min(0, 'Commission has to be between 0% and 100%')
            .max(100, 'Commission has to be between 0% and 100%')
            .required('Commission is required')
        }
        return schema.required('Commission is required')
      }
    ),

    address: Yup.string().required('Street address is required'),
    postCode: Yup.string().required('Postal code is required'),
    city: Yup.string().required('City is required'),
    priceSale: Yup.number()
      .min(1, 'Listing price - Sale should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Sale should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find((e) => e.name === ListingTypeEnum.SALE)?.id
          )
        ) {
          return schema.required('Listing price - Sale is required')
        }
        return schema.nullable()
      }),
    priceRentShortTerm: Yup.number()
      .min(1, 'Listing price - Rent Short Term should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Rent Short Term should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find(
                (e) => e.name === ListingTypeEnum.RENT_SHORT
              )?.id
          )
        ) {
          return schema.required('Listing price - Rent Short Term is required')
        }
        return schema.nullable()
      }),
    priceRentLongTerm: Yup.number()
      .min(1, 'Listing price - Rent Long Term should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Rent Long Term should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find(
                (e) => e.name === ListingTypeEnum.RENT_LONG
              )?.id
          )
        ) {
          return schema.required('Listing price - Rent Long Term is required')
        }
        return schema.nullable()
      }),
    contactIds: Yup.array().of(Yup.number().required()).min(0),
  })

  const initialValues: InitialValues = {
    listingTypeIds: [],
    organizationId: (currentUser?.roles[0]?.organization.id as number) || 0,
    realtorUserIds: [currentUser?.id || 0],
    propertyTypeId: NaN,
    isExclusive: true,
    isStrandified: false,
    areaLevel1Id: undefined,
    areaLevel2Id: undefined,
    areaLevel3Id: undefined,
    areaLevel4Id: undefined,
    areaLevel5Id: undefined,
    commission: NaN,
    commissionType: CommissionTypeEnum.PERCENT,
    priceSale: undefined,
    priceRentShortTerm: undefined,
    priceRentLongTerm: undefined,
    address: undefined,
    postCode: undefined,
    city: undefined,
    cadastralReference: undefined,
    contactIds: [],
    developmentName: '',
  }

  const { isPending: isCreatePropertyLoading, mutate: mutationCreateProperty } =
    useMutation({
      mutationKey: ['property'],
      mutationFn: postProperty,
      onSuccess: (newProperty) => {
        onClose(newProperty)
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: 'Property Created',
          description: `Successfully created property ${newProperty.reference}`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        queryClient.invalidateQueries({
          queryKey: ['property'],
        })
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.propertyNotCreated'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })
  const toast = useToast()
  const isMobile = useBreakpointValue({ base: true, md: false })

  const onSubmit = async (values: CreatePropertyFormTypes) => {
    const formattedValues: CreatePropertyMutationProps = {
      ...values,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      priceSale: values.priceSale ?? null,
      priceRentShortTerm: values.priceRentShortTerm ?? null,
      priceRentLongTerm: values.priceRentLongTerm ?? null,
      privateInfo: {
        location: {
          address: values.address,
          city: values.city,
          postCode: values.postCode,
        },
      },
      isStrandified: values.isStrandified ?? false,
    }

    mutationCreateProperty(formattedValues)
  }

  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  const userPreOptions = useMemo(() => {
    if (!currentUser) {
      return []
    }
    return [
      {
        ...currentUser,
        name: getFullName(currentUser),
      },
    ]
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser?.id])

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={onSubmit}
        validationSchema={validationSchema}
        enableReinitialize={true}
        validateOnMount
        validateOnChange
      >
        {({ isValid, submitForm, isSubmitting, values, errors, touched }) => {
          const showPriceFieldsBasedOnListingType = () => {
            const pricesInputsToShow: JSX.Element[] = []

            for (const listingTypeId of values.listingTypeIds) {
              switch (
                listingTypesData?.find((e) => e.id === listingTypeId)?.name
              ) {
                case ListingTypeEnum.SALE: {
                  pricesInputsToShow.push(
                    <InputWithLabel
                      width={'62%'}
                      label={`${t('listingPrice')} (Sale) *`}
                      name="priceSale"
                      defaultValue={values.priceSale}
                      rightElement={'€'}
                      // TODO: add dynamic currency support
                      type="currency"
                      min={1}
                      max={99999999}
                      dataTestId="listingPriceSale-value-input"
                      error={errors.priceSale}
                      touched={touched.priceSale}
                      key={'priceSaleKey'}
                    />
                  )
                  break
                }
                case ListingTypeEnum.RENT_LONG: {
                  pricesInputsToShow.push(
                    <InputWithLabel
                      width={'62%'}
                      label={`${t('listingPrice')} (Rent Long Term) *`}
                      name="priceRentLongTerm"
                      defaultValue={values.priceRentLongTerm}
                      rightElement={'€'}
                      // TODO: add dynamic currency support
                      type="currency"
                      min={1}
                      max={99999999}
                      dataTestId="listingPriceRentLongTerm-value-input"
                      error={errors.priceRentLongTerm}
                      touched={touched.priceRentLongTerm}
                      key={'priceRentLongTermKey'}
                    />
                  )
                  break
                }
                case ListingTypeEnum.RENT_SHORT: {
                  pricesInputsToShow.push(
                    <InputWithLabel
                      width={'62%'}
                      label={`${t('listingPrice')} (Rent Short Term) *`}
                      name="priceRentShortTerm"
                      defaultValue={values.priceRentShortTerm}
                      rightElement={'€'}
                      // TODO: add dynamic currency support
                      type="currency"
                      min={1}
                      max={99999999}
                      dataTestId="listingPriceRentShortTerm-value-input"
                      error={errors.priceRentShortTerm}
                      touched={touched.priceRentShortTerm}
                      key={'priceRentShortTermKey'}
                    />
                  )
                  break
                }
              }
            }

            return <>{pricesInputsToShow.map((priceInput) => priceInput)}</>
          }

          return !isMobile ? (
            <CustomModal
              isOpen={!isMobile}
              onClose={() => handleConfirmationModal(() => onClose())}
              onAccept={submitForm}
              onReject={() => handleConfirmationModal(() => onClose())}
              title={t('createProperty.title')}
              disableAcceptButton={!isValid || isSubmitting}
              isLoading={isCreatePropertyLoading}
            >
              <Box pt="16px" pr="10px">
                <Form>
                  <Flex direction="column" justifyContent="space-between">
                    <Box>
                      <Text fontSize={'14px'} mb={'2'}>
                        {t('general.mandatory')}
                      </Text>
                      <Flex
                        justifyContent="space-between"
                        wrap="wrap"
                        direction="row"
                        gap="2"
                      >
                        <Flex width={'100%'}>
                          <ContractTypeField placement="bottomStart" />
                        </Flex>
                        <Flex width={'100%'}>
                          <LabeledField
                            label={`${t('createProperty.realtors')}*`}
                            width="100%"
                          >
                            <MultiSelectDropdown
                              name="realtorUserIds"
                              placement="bottomStart"
                              valueKey="id"
                              labelKey="name"
                              {...usersPaginationProps}
                              preOptions={userPreOptions}
                              data={usersPaginationProps.data.map((user) => ({
                                ...user,
                                name: getFullName(user),
                              }))}
                            />
                          </LabeledField>
                        </Flex>
                        <Flex width={'100%'}>
                          <LabeledField label={t('sellers')}>
                            <MultiSelectDropdown
                              name="contactIds"
                              placement="bottomStart"
                              dataTestid="sellers-dropdown"
                              valueKey="id"
                              labelKey="name"
                              selectionLimit={10}
                              {...sellersPaginationProps}
                            />
                          </LabeledField>
                        </Flex>
                        <Flex width={'100%'} gap={2}>
                          <Flex width={'49%'}>
                            <LocationsField
                              isMultiSelect={false}
                              placement="bottomStart"
                            />
                          </Flex>
                          <Flex width={'49%'}>
                            <LabeledField
                              label={`${t('createProperty.listingType')}*`}
                              width="100%"
                            >
                              <MultiSelectDropdown
                                data={mapListingTypeToOption(listingTypesData)}
                                name="listingTypeIds"
                                searchable={false}
                                loading={isLoadingListingTypesData}
                                error={errors.listingTypeIds}
                                placement="bottomEnd"
                              />
                            </LabeledField>
                          </Flex>
                        </Flex>
                        <Flex width={'100%'}>
                          <PropertyTypeField
                            formikFieldName="propertyTypeId"
                            isRequiredField
                            placement="bottomStart"
                          />
                        </Flex>
                        <Flex
                          justifyContent="space-between"
                          wrap="wrap"
                          direction="row"
                          width={'100%'}
                          gap={2}
                        >
                          {showPriceFieldsBasedOnListingType()}

                          <Flex direction="column" gap={2} width={'100%'}>
                            <Flex alignItems="center" gap="4">
                              <Radio
                                label={`${t('editProperty.commissionType')}*`}
                                name="commissionType"
                                values={[
                                  {
                                    value: CommissionTypeEnum.PERCENT,
                                    label: t('editProperty.percentOfSalePrice'),
                                  },
                                  {
                                    value: CommissionTypeEnum.FIXED,
                                    label: t('editProperty.fixedAmount'),
                                  },
                                ]}
                                direction={{
                                  base: 'row',
                                  md: 'row',
                                }}
                                defaultValue={
                                  initialValues.commissionType as string
                                }
                              />
                            </Flex>

                            <InputWithLabel
                              width={'100%'}
                              label={
                                values.commissionType ===
                                CommissionTypeEnum.PERCENT
                                  ? `${t('commission')} % *`
                                  : `${t('commission')} EUR *`
                              }
                              name="commission"
                              defaultValue={values.commission}
                              rightElement={
                                values.commissionType ===
                                CommissionTypeEnum.PERCENT
                                  ? `%`
                                  : 'EUR'
                              }
                              type={
                                values.commissionType ===
                                CommissionTypeEnum.PERCENT
                                  ? 'number'
                                  : 'currency'
                              }
                              dataTestId="commission-value-input"
                              error={errors.commission}
                              touched={touched.commission}
                            />
                          </Flex>
                        </Flex>

                        <InputWithLabel
                          width={'100%'}
                          name="address"
                          defaultValue={values.address ?? ''}
                          label={`${t('streetAddress')}*`}
                          dataTestId="location-streetAddress-value-input"
                          placeholder="oi"
                          error={errors.address}
                          touched={touched.address}
                        />
                        <Flex
                          justifyContent="space-between"
                          wrap="wrap"
                          direction="row"
                          width={'100%'}
                        >
                          <InputWithLabel
                            name="city"
                            value={values.city ?? ''}
                            width={'62%'}
                            label={`${t('city')}*`}
                            dataTestId="location-city-value-input"
                            error={errors.city}
                            touched={touched.city}
                            readOnly
                          />
                          <InputWithLabel
                            width={'36%'}
                            name="postCode"
                            defaultValue={values.postCode ?? ''}
                            label={`${t('postalCode')}*`}
                            dataTestId="location-postalCode-value-input"
                            error={errors.postCode}
                            touched={touched.postCode}
                          />
                        </Flex>
                        <InputWithLabel
                          width="100%"
                          name="developmentName"
                          defaultValue={values.developmentName ?? ''}
                          label={`${t('developmentName')}`}
                          dataTestId="developmentName-value-input"
                        />
                        <Flex width={'100%'} gap="4%">
                          <InputWithLabel
                            width={'100%'}
                            name="cadastralReference"
                            defaultValue={values.cadastralReference ?? ''}
                            label={`${t('cadastralReference')}`}
                            dataTestId="privateInfo-cadastralReference-value-input"
                            error={errors?.cadastralReference}
                            touched={touched?.cadastralReference}
                          />
                        </Flex>
                      </Flex>
                    </Box>
                  </Flex>
                </Form>
              </Box>
            </CustomModal>
          ) : (
            <CreatePropertyDrawer
              onClose={() => handleConfirmationModal(() => onClose())}
              onReject={() => handleConfirmationModal(() => onClose())}
              onAccept={submitForm}
              isOpen={isMobile}
              disableAcceptButton={!isValid || isSubmitting}
              title={t('createProperty.title')}
              isLoading={isCreatePropertyLoading}
            >
              <Form>
                <Flex direction="column" justifyContent="space-between">
                  <Box>
                    <Text fontSize={'14px'} mb={'2'}>
                      {t('general.mandatory')}
                    </Text>
                    <Flex
                      justifyContent="space-between"
                      wrap="wrap"
                      direction="row"
                      gap="2"
                    >
                      <Flex width={['100%', '48%']}>
                        <ContractTypeField placement="bottomStart" />
                      </Flex>
                      <Flex width={['100%', '48%']}>
                        <LabeledField
                          label={`${t('createProperty.realtors')}*`}
                          width="100%"
                        >
                          <MultiSelectDropdown
                            name="realtorUserIds"
                            placement="bottomStart"
                            valueKey="id"
                            labelKey="name"
                            preOptions={currentUser ? [currentUser] : []}
                            {...usersPaginationProps}
                            data={usersPaginationProps.data.map((user) => ({
                              ...user,
                              name: getFullName(user),
                            }))}
                          />
                        </LabeledField>
                      </Flex>
                      <Flex width={['100%', '48%']}>
                        <LabeledField label={t('sellers')}>
                          <MultiSelectDropdown
                            name="contactIds"
                            placement="bottomStart"
                            dataTestid="sellers-dropdown"
                            valueKey="id"
                            labelKey="name"
                            selectionLimit={10}
                            {...sellersPaginationProps}
                          />
                        </LabeledField>
                      </Flex>
                      <Flex width={['100%', '48%']}>
                        <LocationsField
                          isMultiSelect={false}
                          placement="bottomStart"
                        />
                      </Flex>
                      <Flex width={['100%', '48%']}>
                        <LabeledField
                          label={`${t('createProperty.listingType')}*`}
                          width="100%"
                        >
                          <MultiSelectDropdown
                            data={mapListingTypeToOption(listingTypesData)}
                            name="listingTypeIds"
                            searchable={false}
                            loading={isLoadingListingTypesData}
                            error={errors.listingTypeIds}
                            placement="bottomStart"
                          />
                        </LabeledField>
                      </Flex>
                      <Flex width={['100%', '48%']}>
                        <PropertyTypeField
                          formikFieldName="propertyTypeId"
                          isRequiredField
                          placement="bottomStart"
                        />
                      </Flex>
                      <Flex
                        justifyContent="space-between"
                        wrap="wrap"
                        direction="row"
                        width={'100%'}
                        gap={2}
                      >
                        {showPriceFieldsBasedOnListingType()}

                        <Flex direction="column" gap={2} width={'100%'}>
                          <Flex alignItems="center" gap="4">
                            <Radio
                              label={`${t('editProperty.commissionType')}*`}
                              name="commissionType"
                              values={[
                                {
                                  value: CommissionTypeEnum.PERCENT,
                                  label: t('editProperty.percentOfSalePrice'),
                                },
                                {
                                  value: CommissionTypeEnum.FIXED,
                                  label: t('editProperty.fixedAmount'),
                                },
                              ]}
                              direction={{
                                base: 'row',
                                md: 'row',
                              }}
                              defaultValue={
                                initialValues.commissionType as string
                              }
                            />
                          </Flex>
                          <InputWithLabel
                            width={'100%'}
                            label={
                              values.commissionType ===
                              CommissionTypeEnum.PERCENT
                                ? `${t('commission')} % *`
                                : `${t('commission')} EUR *`
                            }
                            name="commission"
                            defaultValue={values.commission ?? 0}
                            rightElement={
                              values.commissionType ===
                              CommissionTypeEnum.PERCENT
                                ? `%`
                                : 'EUR'
                            }
                            type={
                              values.commissionType ===
                              CommissionTypeEnum.PERCENT
                                ? 'number'
                                : 'currency'
                            }
                            dataTestId="commission-value-input"
                            error={errors.commission}
                            touched={touched.commission}
                          />
                        </Flex>
                      </Flex>

                      <InputWithLabel
                        width={['100%', '48%']}
                        name="address"
                        defaultValue={values.address ?? ''}
                        label={`${t('streetAddress')}*`}
                        dataTestId="location-streetAddress-value-input"
                        error={errors.address}
                        touched={touched.address}
                      />
                      <Flex
                        justifyContent="space-between"
                        wrap="wrap"
                        direction="row"
                        width={['100%', '48%']}
                      >
                        <InputWithLabel
                          name="city"
                          value={values.city ?? ''}
                          width={'62%'}
                          label={`${t('city')}*`}
                          dataTestId="location-city-value-input"
                          error={errors.city}
                          touched={touched.city}
                          readOnly
                        />
                        <InputWithLabel
                          width={'36%'}
                          name="postCode"
                          defaultValue={values.postCode ?? ''}
                          label={`${t('postalCode')}*`}
                          dataTestId="location-postalCode-value-input"
                          error={errors.postCode}
                          touched={touched.postCode}
                        />
                      </Flex>
                      <InputWithLabel
                        width="100%"
                        name="developmentName"
                        defaultValue={values.developmentName ?? ''}
                        label={`${t('developmentName')}`}
                        dataTestId="developmentName-value-input"
                      />
                      <Flex width={'100%'}>
                        <InputWithLabel
                          width={'100%'}
                          name="cadastralReference"
                          defaultValue={values.cadastralReference ?? ''}
                          label={`${t('cadastralReference')}`}
                          dataTestId="privateInfo-cadastralReference-value-input"
                          error={errors?.cadastralReference}
                          touched={touched?.cadastralReference}
                        />
                      </Flex>
                    </Flex>
                  </Box>
                </Flex>
              </Form>
            </CreatePropertyDrawer>
          )
        }}
      </Formik>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={confirmationModalAcceptFunction}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
    </>
  )
}

export default CreateProperty
