import {
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON><PERSON>,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { Formik, FormikProps, useFormikContext } from 'formik'
import { InputWithLabel } from '../Form/Input'
import { TFunction, useTranslation } from 'next-i18next'
import { useMutation } from '@tanstack/react-query'
import { getContacts } from '@/queries/contact'
import { getUsers } from '@/queries/users'
import * as Yup from 'yup'
import { MOST_SPOKEN_LANGUAGES } from '@/utils/constants'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getFullName } from '@/types/users'
import { CreateGroupMutationProps, GroupListRead } from '@/types/group'
import LabeledField from '../Form/LabeledField'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import SelectDropdown from '../DropdownMenu/SelectDropdown'
import { createGroup } from '@/queries/groups'
import { CreatableDropdown } from '../CreatableDropdown'
import { Option } from '@/types/common'
import { useDecodedSessionToken } from '@/hooks/useDecodedSessionToken'
import { CreateContactProps } from '@/types/contact'
import { useTags } from '@/queries/tags'
import { DeviceAdapter } from './DeviceAdapter'
import { SchemaContactListRead } from '@/generated-types/api'

const CreateGroupSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
})

const languageOptions = MOST_SPOKEN_LANGUAGES.map((language) => ({
  label: language,
  value: language,
}))

const Body = ({
  t,
  sellersPaginationProps,
  realtorsPaginationProps,
  formik,
  listOfTags,
  isLoadingTags,
  preSelectedContacts,
}: {
  t: TFunction
  sellersPaginationProps: PaginationProps<SchemaContactListRead>
  realtorsPaginationProps: PaginationProps<{
    value: number
    label: string
  }>
  formik: FormikProps<CreateGroupMutationProps>
  listOfTags: Option[]
  isLoadingTags: boolean
  preSelectedContacts?: {
    id: number
    name: string
  }[]
}) => {
  const { setFieldValue } = useFormikContext<CreateContactProps>()
  const { errors, touched } = formik

  return (
    <Flex direction="column" gap="8">
      <Flex direction="column" gap="4">
        <Flex direction="column" gap="2">
          <Flex width="100%" gap="2">
            <InputWithLabel
              label={`${t('createGroup.groupName')}*`}
              placeholder={t('createGroup.groupName') ?? undefined}
              name="name"
              type="text"
              error={errors.name}
              touched={touched.name}
            />
          </Flex>
          <Flex width="100%" gap="2">
            <LabeledField label={t('description')} width="100%">
              <Textarea
                placeholder={t('description') ?? undefined}
                name={t('description') ?? undefined}
                size="sm"
                onBlur={(e) =>
                  setFieldValue('description', e.currentTarget.value)
                }
              />
            </LabeledField>
          </Flex>
          <LabeledField label={t('preferredLanguage')} width={'100%'}>
            <SelectDropdown
              valueKey="label"
              labelKey="label"
              data={languageOptions}
              renderMenu={(menu) => {
                if (languageOptions.length === 0) {
                  return (
                    <div
                      style={{
                        padding: 4,
                        color: '#999',
                        textAlign: 'center',
                      }}
                    >
                      <Spinner />
                    </div>
                  )
                }
                return menu
              }}
              onChange={(value) => {
                setFieldValue('language', value)
              }}
              placement="bottomStart"
            />
          </LabeledField>
          <Flex width="100%" gap="2">
            <LabeledField label={t('salesActivity.contacts')}>
              <MultiSelectDropdown
                name="contactIds"
                placement="bottomStart"
                dataTestid="sellers-dropdown"
                cleanable={false}
                valueKey="id"
                labelKey="name"
                {...sellersPaginationProps}
                preOptions={preSelectedContacts}
                data={sellersPaginationProps.data.map((contact) => {
                  return {
                    ...contact,
                    name: contact.name,
                  }
                })}
              />
            </LabeledField>
          </Flex>
          <Flex width="100%" gap="2">
            <LabeledField label={t('assignedTo')} width="100%">
              <MultiSelectDropdown
                name="realtors"
                placement="bottomStart"
                dataTestid="user-id-dropdown"
                cleanable={false}
                labelKey="label"
                valueKey="value"
                {...realtorsPaginationProps}
              />
            </LabeledField>
          </Flex>
          <Flex width="100%" gap="2">
            <LabeledField label={t('createContact.tags')} width="100%">
              <CreatableDropdown
                fieldName="tags"
                data={listOfTags}
                isLoading={isLoadingTags}
              />
            </LabeledField>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  )
}

export const CreateGroup = ({
  onClose,
  onSuccess,
}: {
  onClose: (group?: GroupListRead) => void
  onSuccess?: (group: GroupListRead) => void
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const decodedToken = useDecodedSessionToken()
  const currentUser = decodedToken && {
    id: decodedToken.sub,
    name: `${decodedToken.userFirstName} ${decodedToken.userLastName}`,
  }

  const { data: listOfTags = [], isLoading: isLoadingTags } = useTags()

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts-group'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }),
    }
  )

  const { paginationProps: realtorsPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['users-group'],
      queryFn: (params) =>
        getUsers({
          ...params,
          onlyActive: true,
        }).then((data) => {
          return {
            records: data.records.map((user) => ({
              value: user.id,
              label: getFullName(user),
            })),
            metadata: data.metadata,
          }
        }),
    })

  const initialValues: CreateGroupMutationProps = {
    name: '',
    contactIds: [],
    realtors: currentUser ? [currentUser.id] : [],
    description: '',
    language: undefined,
    tags: [],
    newTags: [],
    existingTags: [],
  }

  const { isPending: isCreatingContactLoading, mutate: mutationCreateGroup } =
    useMutation({
      mutationKey: ['group'],
      mutationFn: createGroup,
      onSuccess: (contactCreated: GroupListRead) => {
        if (onSuccess) {
          onSuccess(contactCreated)
        } else {
          toast({
            position: isMobile ? 'top' : 'bottom-left',
            title: t('createContact.createdTitle'),
            description: `${t('createContact.createdDescription')} "${
              contactCreated?.name
            }"`,
            status: 'success',
            duration: 9000,
            isClosable: true,
            variant: 'customSuccess',
          })
          onClose(contactCreated)
        }
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.groupNotCreated'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const onSubmit = async (values: CreateGroupMutationProps) => {
    const newGroup = {
      name: values.name,
      description: values.description,
      contactIds: values.contactIds,
      realtors: values.realtors,
      language: values.language,
      tags: values.tags,
      newTags: (values.newTags = values.tags
        .filter((tag) => typeof tag.value === 'string')
        .map((tag) => tag.value as string)),
      existingTags: (values.existingTags = values.tags
        .filter((tag) => typeof tag.value === 'number')
        .map((tag) => tag.value as number)),
    }
    mutationCreateGroup(newGroup)
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={CreateGroupSchema}
      validateOnMount
    >
      {(formik: FormikProps<CreateGroupMutationProps>) => {
        return (
          <DeviceAdapter
            isOpen={true}
            onClose={onClose}
            onAccept={formik.submitForm}
            onReject={onClose}
            title={t('createGroup.title')}
            acceptTitle={t('create') || undefined}
            isLoading={isCreatingContactLoading}
            disableAcceptButton={!formik.isValid || formik.isSubmitting}
            dirty={formik.dirty}
            isSubmitting={formik.isSubmitting}
          >
            <Body
              t={t}
              sellersPaginationProps={sellersPaginationProps}
              realtorsPaginationProps={realtorsPaginationProps}
              formik={formik}
              listOfTags={listOfTags}
              isLoadingTags={isLoadingTags}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}

export const CreateContactGroup = ({
  onClose,
  onSuccess,
  sellersPaginationProps,
  preSelectedContacts,
}: {
  onClose: (group?: GroupListRead) => void
  onSuccess?: (group: GroupListRead) => void
  sellersPaginationProps: PaginationProps<SchemaContactListRead>
  preSelectedContacts?: {
    id: number
    name: string
  }[]
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const decodedToken = useDecodedSessionToken()
  const currentUser = decodedToken && {
    id: decodedToken.sub,
    name: `${decodedToken.userFirstName} ${decodedToken.userLastName}`,
  }
  const { data: listOfTags = [], isLoading: isLoadingTags } = useTags()

  const { paginationProps: realtorsPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['users-group'],
      queryFn: (params) =>
        getUsers({
          ...params,
          onlyActive: true,
        }).then((data) => {
          return {
            records: data.records.map((user) => ({
              value: user.id,
              label: getFullName(user),
            })),
            metadata: data.metadata,
          }
        }),
    })

  const initialValues: CreateGroupMutationProps = {
    name: '',
    contactIds: preSelectedContacts?.map((contact) => contact.id) || [],
    realtors: currentUser ? [currentUser.id] : [],
    description: '',
    language: undefined,
    tags: [],
    newTags: [],
    existingTags: [],
  }

  const { isPending: isCreatingContactLoading, mutate: mutationCreateGroup } =
    useMutation({
      mutationKey: ['group'],
      mutationFn: createGroup,
      onSuccess: (contactCreated: GroupListRead) => {
        if (onSuccess) {
          onSuccess(contactCreated)
        } else {
          toast({
            position: isMobile ? 'top' : 'bottom-left',
            title: t('createContact.createdTitle'),
            description: `${t('createContact.createdDescription')} "${
              contactCreated?.name
            }"`,
            status: 'success',
            duration: 9000,
            isClosable: true,
            variant: 'customSuccess',
          })
          onClose(contactCreated)
        }
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.groupNotCreated'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const onSubmit = async (values: CreateGroupMutationProps) => {
    const newGroup = {
      name: values.name,
      description: values.description,
      contactIds: values.contactIds,
      realtors: values.realtors,
      language: values.language,
      tags: values.tags,
      newTags: (values.newTags = values.tags
        .filter((tag) => typeof tag.value === 'string')
        .map((tag) => tag.value as string)),
      existingTags: (values.existingTags = values.tags
        .filter((tag) => typeof tag.value === 'number')
        .map((tag) => tag.value as number)),
    }
    mutationCreateGroup(newGroup)
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={CreateGroupSchema}
      validateOnMount
    >
      {(formik: FormikProps<CreateGroupMutationProps>) => {
        return (
          <DeviceAdapter
            isOpen={true}
            onClose={onClose}
            onAccept={formik.submitForm}
            onReject={onClose}
            title={t('createGroup.title')}
            acceptTitle={t('create') || undefined}
            isLoading={isCreatingContactLoading}
            disableAcceptButton={!formik.isValid || formik.isSubmitting}
            dirty={formik.dirty}
            isSubmitting={formik.isSubmitting}
          >
            <Body
              t={t}
              sellersPaginationProps={sellersPaginationProps}
              realtorsPaginationProps={realtorsPaginationProps}
              formik={formik}
              listOfTags={listOfTags}
              isLoadingTags={isLoadingTags}
              preSelectedContacts={preSelectedContacts}
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}
