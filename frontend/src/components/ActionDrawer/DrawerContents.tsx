import { useDrawer } from '@/hooks/useDrawer'
import CreateESPropertyForm from './CreateProperty'
import InitialDrawer from './InitialDrawer'
import { CreateContact } from './CreateContact'
import { CreateSalesAgreement } from './CreateSalesAgreement'
import CreateLeadForm from '../Lead/CreateLeadForm'
import { LeadStatus } from '@/types/lead'
import { CreateUserForm } from '../User/CreateUserForm'
import { CreateOffer } from './CreateOffer'
import { CreateSellingOffer } from './CreateSellingOffer'
import { useIsFinland } from '@/hooks/useIsFinland'
import { CreateFIPropertyForm } from '@/modules/fi-properties'
import CreateEventForm from '../Event/CreateEventForm'
import { CreateShareTrade } from '../DiasTrades/CreateShareTrade'
import { CreateMatchMaking } from './CreateMatchMaking'
import { CreateAdvertisementModal } from '../Advertisements/modal/CreateAdvertisement'
import { CreateDetailOfSale } from './CreateDetailOfSale'
import { CreateBrokerageOfferFormModal } from '@/modules/brokerage-offer'
import { CreateContactV2 } from '../Contact/ContactFormV2'

const DrawerContent = ({ onClose }: { onClose: () => void }) => {
  const { state, setState } = useDrawer()
  const isFinland = useIsFinland()

  switch (state) {
    case 'initial':
      return <InitialDrawer />
    case 'createProperty':
      return isFinland ? (
        <CreateFIPropertyForm
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      ) : (
        <CreateESPropertyForm
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createContact':
      return isFinland ? (
        <CreateContactV2
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      ) : (
        <CreateContact
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createUser':
      return (
        <CreateUserForm
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createMatchMaking':
      return (
        <CreateMatchMaking
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createSalesAgreement':
      return (
        <CreateSalesAgreement
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createLead':
      return (
        <CreateLeadForm
          status={LeadStatus.NEW}
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createOffer':
      return (
        <CreateOffer
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createDos':
      return (
        <CreateDetailOfSale
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createEvent':
      return (
        <CreateEventForm
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createSellingOffer':
      return (
        <CreateSellingOffer
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createShareTrade':
      return (
        <CreateShareTrade
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createAdvertisement':
      return (
        <CreateAdvertisementModal
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    case 'createBrokerageOffer':
      return (
        <CreateBrokerageOfferFormModal
          onClose={() => {
            setState('initial')
            onClose()
          }}
        />
      )
    default:
      return <InitialDrawer />
  }
}

export default DrawerContent
