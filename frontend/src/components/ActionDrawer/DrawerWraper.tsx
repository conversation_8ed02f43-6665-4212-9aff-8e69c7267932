import { useTranslation } from 'next-i18next'
import { ReactNode } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  Flex,
  Heading,
} from '@chakra-ui/react'
import { DrawerProvider } from '@/hooks/useDrawer'
import { useMobile } from '@/hooks/useMobile'
import { ScrollableRootContextProvider } from '../useScrollableRootContext'

type ActionDrawerProps = {
  onClose: () => void
  onReject: () => void
  onAccept: () => void
  isOpen: boolean
  isLoading?: boolean
  children: ReactNode
  acceptTitle?: string
  cancelTitle?: string
  title: string
  disableAcceptButton?: boolean
  disableRejectButton?: boolean
  disableModalClosing?: boolean
  noPadding?: boolean
  footerContent?: ReactNode
}

export const DrawerWrapper = (props: ActionDrawerProps) => {
  const { t } = useTranslation(['common'])
  const isMobile = useMobile()
  return (
    <DrawerProvider>
      <Drawer placement="bottom" onClose={props.onClose} isOpen={props.isOpen}>
        <DrawerOverlay />
        <DrawerContent height="calc(100% - 91px)">
          <>
            <DrawerHeader
              justifyContent="space-between"
              alignItems="center"
              display="flex"
              borderBottom="1px solid"
              borderColor="grays.gray6"
              height="58px"
            >
              <Heading variant="H1">{props.title}</Heading>
              <div>
                {isMobile ? (
                  <Button
                    onClick={() => props.onClose()}
                    p="7px"
                    variant="transparent"
                    borderColor="transparent"
                    fontWeight={500}
                    fontSize="16px"
                  >
                    <span
                      style={{ fontSize: '30px', marginRight: '-20px' }}
                      className="material-symbols-outlined"
                    >
                      close
                    </span>
                  </Button>
                ) : (
                  <Flex gap={4}>
                    <Button
                      onClick={() => props.onReject()}
                      isDisabled={props.disableRejectButton}
                      p="7px"
                      variant="transparent"
                      borderColor="transparent"
                      fontWeight={500}
                      fontSize="16px"
                    >
                      {props.cancelTitle || t('cancel')}
                    </Button>
                    <Button
                      onClick={props.onAccept}
                      fontWeight={500}
                      fontSize="16px"
                      isLoading={props.isLoading}
                      isDisabled={props.disableAcceptButton}
                    >
                      {props.acceptTitle || t('apply')}
                    </Button>
                  </Flex>
                )}
              </div>
            </DrawerHeader>
            <DrawerBody
              padding={props.noPadding ? '0' : '16px 16px 24px 26px'}
              overflow="auto"
              flex="1"
            >
              <ScrollableRootContextProvider>
                <Box height="100%">{props.children}</Box>
              </ScrollableRootContextProvider>
            </DrawerBody>
            {isMobile &&
              (props.footerContent ? (
                <DrawerFooter>{props.footerContent}</DrawerFooter>
              ) : (
                <DrawerFooter borderTop="1px solid #E2E2E2">
                  <Flex
                    width="100%"
                    height="91px"
                    alignItems="center"
                    justifyContent="center"
                    background="common.white"
                    zIndex={5}
                  >
                    <Button
                      onClick={props.onReject}
                      variant="transparent"
                      borderColor="transparent"
                      fontWeight={500}
                      fontSize="16px"
                      size="lg"
                      width="45%"
                      isDisabled={props.disableRejectButton}
                    >
                      {props.cancelTitle || t('cancel')}
                    </Button>
                    <Button
                      onClick={props.onAccept}
                      fontWeight={500}
                      fontSize="16px"
                      size="lg"
                      width="45%"
                      isLoading={props.isLoading}
                      isDisabled={props.disableAcceptButton}
                    >
                      {props.acceptTitle || t('apply')}
                    </Button>
                  </Flex>
                </DrawerFooter>
              ))}
          </>
        </DrawerContent>
      </Drawer>
    </DrawerProvider>
  )
}
