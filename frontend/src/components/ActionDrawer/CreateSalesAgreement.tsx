import {
  Flex,
  Heading,
  useBreakpointValue,
  useToast,
  Box,
  Step,
  StepIndicator,
  StepSeparator,
  StepStatus,
  Stepper,
  Text,
  Stack,
} from '@chakra-ui/react'
import { CustomModal } from '../Modal/Modal'
import {
  Formik,
  useFormikContext,
  validateYupSchema,
  yupToFormErrors,
} from 'formik'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  generateSalesAgreement,
  downloadDocument,
  sendToSign,
} from '@/queries/document'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import LabeledField from '../Form/LabeledField'
import * as Yup from 'yup'
import { useEffect, useState } from 'react'
import { getProperties, getProperty } from '@/queries/property'
import {
  DataSource,
  LanguageCodeEnum,
  ListProperties,
  ListingTypeEnum,
  PropertyRead,
  ReadProperty,
} from '@/types/property'
import { SimpleDropdown } from '../DropdownMenu/SimpleDropdown'
import { InputWithLabel } from '../Form/Input'
import { CreateSalesAgreementDrawer } from './CreateSalesAgreementDrawer'
import { SalesAgreementProps } from '@/types/document'
import { useTranslation } from 'next-i18next'
import { UserRead } from '@/types/users'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { ReviewPropertyDetail } from '../Offer/PropertyPreview'
import { ReviewContactDetail } from '../Offer/ContactPreview'
import { EditPropertyForm } from '../PropertyPage/EditPropertyForm'
import { SaveAndDownloadOffer } from '../Offer/SaveAndDownLoad'
import Radio from '../Form/Radio'
import { SIGN_METHODS } from '@/types/common'
import { Checkbox } from 'rsuite'
import { useSteps } from '@/hooks/useSteps'
import { downloadBase64File } from '@/utils/file'
import { PropertyDropdownItem } from '../PropertyDropdownItem'
import { PROPERTY_DROPDOWN_ITEM_HEIGHT, STALE_TIME } from '@/utils/constants'
import { useInContactDetailPage } from '@/hooks/useInContactPage'
import { useInPropertyDetailPage } from '@/hooks/useInPropertyPage'
import { useCreatePropertyModal } from '@/hooks/useCommonModal'
import { useFeatureFlag } from '@/hooks/useFeatureFlag'
import { SendToSign } from '../Offer/SendToSign'
import { OFFICE_IN_PALMA_CITY } from '@/utils/auth'
import { useAuth } from '@/hooks/useAuth'

const CreateSalesAgreementSchema = Yup.object().shape({
  propertyId: Yup.string().required('Property is required'),
  sellerIds: Yup.array()
    .min(1, 'At least one seller must be selected')
    .max(2, 'Maximum of two sellers can be selected')
    .required('Sellers are required'),
  realtorId: Yup.number().required('Realtor is required'),
  validityInMonths: Yup.number()
    .min(1, 'Validity cannot be less than 1 month')
    .max(60, 'Validity cannot be more than 60 months')
    .required('Validity in months is required'),
})

type InitialFormValues = SalesAgreementProps & {
  propertyReference: string
  sellerIds: number[]
  levelOfSignature: SIGN_METHODS
  property?: ReadProperty
  liabilityInsurance?: string
  roaiib?: string
}

interface BodyProps {
  activeStep: number
  preSelectedProperty?: ReadProperty | ListProperties
  onClickEditProperty: (property: ReadProperty) => void
  currentUser?: UserRead | null
  onValidateMissingInfo: (step: number, value: boolean) => void
  isMissingInfo: boolean
  steps: number[]
  onDownload: () => void
  isDownloading?: boolean
}

const providedDocumentation = {
  identityDocument: false,
  publicDeedOrNotaSimple: false,
  energyPerformanceCertificate: false,
  firstOccupancyLicense: false,
  urbanContributionTaxBill: false,
  rubbishCollectionFeesReceipt: false,
  communityFeesReceipt: false,
  certificateOfCommunityPropertyOwners: false,
  certificateOfTaxResidence: false,
  legalRepresentativeAndPowerOfAttorney: false,
  thePowersInCaseOfSociety: false,
  separationRuling: false,
  prenuptialAgreement: false,
  certificateOfInheritance: false,
  certificateOfOutstandingDebt: false,
  utilityBill: false,
}

const CREATE_SALE_AGREEMENT_FORM = 1
const REVIEW_PROPERTY = 2
const REVIEW_CONTACT = 3
const SAVE_AND_DOWNLOAD = 4

const Body = ({
  activeStep,
  preSelectedProperty,
  currentUser,
  isMissingInfo,
  steps,

  onValidateMissingInfo,
  onClickEditProperty,

  onDownload,
  isDownloading,
}: BodyProps) => {
  const { t } = useTranslation(['common'])
  const { verifyFeatureFlag } = useFeatureFlag()

  const { setFieldValue, values, errors, touched } =
    useFormikContext<InitialFormValues>()
  const isMobile = useBreakpointValue({ base: true, md: false })
  const {
    property: propertyOnPage,
    isInPropertyDetailPage,
    referenceCode,
  } = useInPropertyDetailPage()
  const placeDropdown = 'bottomStart'

  const { data: property } = useQuery({
    queryKey: ['property', values.propertyReference],
    queryFn: () => getProperty(values.propertyReference),
    enabled: !!values.propertyReference,
    staleTime: STALE_TIME,
  })

  const { contact, contactId, isInContactPage } = useInContactDetailPage()

  const sellerIdParams = contactId ? [Number(contactId)] : undefined

  const { paginationProps } = usePaginationDropdownData({
    queryKey: ['properties'],
    queryFn: (params) =>
      getProperties({
        ...params,
        dataSource: DataSource.STRAND,
        sellers: sellerIdParams,
      }),
  })

  const { onOpen } = useCreatePropertyModal()
  const [propertyAlreadyCreated, setPropertyAlreadyCreated] =
    useState<PropertyRead>()
  const defaultFullName = (() => {
    return `${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`
  })()

  const listOfSellers = property?.contacts || []

  const enabledESignature = verifyFeatureFlag('eSignature')

  const signMethods = [
    { value: SIGN_METHODS.PEN_PAPER, label: t('signingMethods.penAndPaper') },
    {
      value: SIGN_METHODS.E_SIGNATURE,
      label: t(
        enabledESignature
          ? 'signingMethods.eSignature'
          : 'signingMethods.eSignatureComingSoon'
      ),
      isDisabled: !enabledESignature,
    },
  ]

  useEffect(() => {
    if (preSelectedProperty || isInPropertyDetailPage) {
      setFieldValue(
        'propertyReference',
        preSelectedProperty?.reference || referenceCode
      )
    }
    if (isInContactPage) {
      setFieldValue('sellerIds', [contactId])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (property) {
      setFieldValue('property', property)

      setFieldValue(
        'sellerIds',
        property.contacts?.map(({ id }) => id) || [],
        true
      )
      onValidateMissingInfo(
        REVIEW_PROPERTY,
        property.listingTypes?.some(
          (listing) => listing.name === ListingTypeEnum.SALE
        )
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [property?.id])

  const prePropertyOptions = [
    propertyAlreadyCreated,
    preSelectedProperty,
    propertyOnPage,
  ].filter((item) => !!item)

  const handleCreateProperty = async () => {
    const propertyCreated = await onOpen()

    if (propertyCreated?.reference) {
      setFieldValue('propertyReference', propertyCreated.reference)
      setPropertyAlreadyCreated(propertyCreated)
    }
  }

  const documentLanguages = [
    { value: LanguageCodeEnum.EN, label: t(LanguageCodeEnum.EN) },
    { value: LanguageCodeEnum.ES, label: t(LanguageCodeEnum.ES) },
    { value: LanguageCodeEnum.DE, label: t(LanguageCodeEnum.DE) },
  ]

  return (
    <>
      <Stepper size="sm" colorScheme="blackAlpha" index={activeStep}>
        {steps.map((_step, index) => (
          <Step key={index + 1}>
            <StepIndicator>
              <StepStatus />
            </StepIndicator>
            <StepSeparator />
          </Step>
        ))}
      </Stepper>
      {activeStep < REVIEW_PROPERTY && (
        <Text fontSize="14px" mt="4">
          {t('general.mandatory')}
        </Text>
      )}
      <Box
        key={CREATE_SALE_AGREEMENT_FORM}
        display={activeStep === CREATE_SALE_AGREEMENT_FORM ? 'block' : 'none'}
      >
        <Flex pt="16px" pb="8px">
          <Heading variant="H2" data-testid="create-salesAgreement-title">
            {t('createSalesAgreement.agreementDetails')}
          </Heading>
        </Flex>
        <Flex
          mt="12px"
          width="100%"
          gap="10px"
          flexWrap="wrap"
          justifyContent="space-between"
        >
          <LabeledField label={`${t('property')}*`} width={['100%', '58%']}>
            <SimpleDropdown
              name="propertyReference"
              valueKey="reference"
              labelKey="reference"
              {...paginationProps}
              cleanable={false}
              dataTestid="property-id-dropdown"
              placement={placeDropdown}
              preOptions={prePropertyOptions as ReadProperty[]}
              renderMenuItem={PropertyDropdownItem}
              itemSize={PROPERTY_DROPDOWN_ITEM_HEIGHT}
              renderExtraFooter={() =>
                !isMobile && (
                  <Box p="4" mt="2">
                    <Text
                      cursor="pointer"
                      fontSize="small"
                      fontWeight="bold"
                      color="tabs.selected"
                      onClick={handleCreateProperty}
                      textTransform="uppercase"
                    >
                      {t('actionDropdown.create.header')}
                    </Text>
                  </Box>
                )
              }
            />
          </LabeledField>
          <InputWithLabel
            label={`${t('createProperty.validityMonths')}*`}
            name="validityInMonths"
            type="number"
            defaultValue={values.validityInMonths}
            min={1}
            max={60}
            width={['100%', '38%']}
            onChange={(e) => {
              const event = e as unknown as React.ChangeEvent<HTMLInputElement>
              values.validityInMonths = event.target.value as unknown as number
            }}
            dataTestId="validity-in-months-input"
            error={errors.validityInMonths}
            touched={touched.validityInMonths}
          />
        </Flex>
        <Flex
          width="100%"
          mt="10px"
          gap="10px"
          justifyContent="space-between"
          flexWrap="wrap"
        >
          <Flex width={['100%', '48%']}>
            <InputWithLabel
              label={`${t('realtors')}*`}
              placeholder={currentUser?.firstName as string}
              name="realtor"
              type="text"
              readOnly
              value={defaultFullName}
              dataTestId="realtor-text-input"
            />
          </Flex>
          <Flex width={['100%', '48%']}>
            <LabeledField label={`${t('sellers')}*`}>
              <MultiSelectDropdown
                preOptions={listOfSellers}
                data={contact && [contact]}
                name="sellerIds"
                selectionLimit={10}
                dataTestid="sellers-dropdown"
                placement="bottomEnd"
                valueKey="id"
                labelKey="name"
              />
            </LabeledField>
          </Flex>
        </Flex>
        <Box mt="8">
          <Heading variant="H2" mb="2">
            {t('documentation')}
          </Heading>
          <Stack gap={0}>
            {Object.keys(providedDocumentation).map((attr) => (
              <Checkbox
                name={`documents.${attr}`}
                checked={
                  values[
                    `documents.${attr}` as keyof InitialFormValues['documents']
                  ]
                }
                onChange={(value, checked) =>
                  setFieldValue(`documents.${attr}`, checked)
                }
                key={attr}
              >
                {t(`${attr}`)}
              </Checkbox>
            ))}
          </Stack>
        </Box>

        <Box mt="4">
          <Radio
            formLabelProps={{ fontSize: 'x-large', as: Text }}
            label={`${t('signingMethods.title')}:`}
            name="levelOfSignature"
            direction="column"
            values={signMethods}
            defaultValue={values.levelOfSignature}
          />
        </Box>
        <Box mt="8">
          <Heading variant="H2" mb="2">
            {t('documentLanguage')}
          </Heading>
          <SimpleDropdown
            name="documentLanguage"
            valueKey="value"
            labelKey="label"
            data={documentLanguages}
            searchable={false}
            cleanable={false}
          />
        </Box>
      </Box>

      {activeStep === REVIEW_PROPERTY && property && (
        <ReviewPropertyDetail
          property={property}
          onClickEditProperty={onClickEditProperty}
          isMissingInfo={isMissingInfo}
        />
      )}
      <ReviewContactDetail
        contactIds={values.sellerIds}
        visible={activeStep === REVIEW_CONTACT}
        enabled={activeStep > REVIEW_PROPERTY}
        onValidateMissingInfo={(value) =>
          onValidateMissingInfo(REVIEW_CONTACT, value)
        }
      />
      {activeStep === SAVE_AND_DOWNLOAD &&
        values.levelOfSignature !== SIGN_METHODS.E_SIGNATURE && (
          <SaveAndDownloadOffer key={4} />
        )}
      {activeStep === SAVE_AND_DOWNLOAD &&
        values.levelOfSignature === SIGN_METHODS.E_SIGNATURE && (
          <SendToSign onDownload={onDownload} isDownloading={isDownloading} />
        )}
    </>
  )
}

export const CreateSalesAgreement = ({
  onClose,
  preSelectedProperty,
}: {
  onClose: () => void
  // CreateSalesAgreement can call from property table or property detail
  preSelectedProperty?: ReadProperty | ListProperties
}) => {
  // States
  const steps = [
    CREATE_SALE_AGREEMENT_FORM,
    REVIEW_PROPERTY,
    REVIEW_CONTACT,
    SAVE_AND_DOWNLOAD,
  ]
  const {
    activeStep,
    goToNext,
    goToPrevious,
    isInvalid: isInvalidToNextStep,
    handleSetValidStep,
    setActiveStep,
  } = useSteps(steps)
  const [editPropertyData, setEditPropertyData] = useState<ReadProperty>()

  const [docIsDownloading, setDownloading] = useState(false)
  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const queryClient = useQueryClient()
  const {
    authStates: { currentUser },
  } = useAuth()

  const isRealtorInPalmaOffice = currentUser?.offices.some(
    (item) => item.name === OFFICE_IN_PALMA_CITY
  )

  const initialFormValues: InitialFormValues = {
    levelOfSignature: SIGN_METHODS.PEN_PAPER,
    propertyId: 0,
    realtorId: 0,
    sellerIds: [],
    documents: {
      ...providedDocumentation,
    },
    documentLanguage: LanguageCodeEnum.EN,
    validityInMonths: 6,
    sellerIds: [],
    propertyReference: preSelectedProperty?.reference ?? '',
    roaiib: currentUser?.roaiib,
    liabilityInsurance: currentUser?.liabilityInsurance,
  }

  // Dropdown lists

  const nextStep = async (values: InitialFormValues) => {
    if (
      activeStep === SAVE_AND_DOWNLOAD &&
      values.levelOfSignature === SIGN_METHODS.E_SIGNATURE &&
      salesAgreementCreated?.documentId
    ) {
      mutationSendForSigning(salesAgreementCreated?.documentId)
      return
    }
    if (
      (activeStep === REVIEW_CONTACT &&
        values.levelOfSignature === SIGN_METHODS.E_SIGNATURE) ||
      activeStep === SAVE_AND_DOWNLOAD
    ) {
      onSubmit(values)
      return
    }
    goToNext()
  }

  const previousStep = () => {
    setShowConfirmationModal(false)
    goToPrevious()
  }

  const {
    data: salesAgreementCreated,
    isPending: isCreatingSalesAgreementLoading,
    mutate: mutationCreateSalesAgreement,
  } = useMutation({
    mutationKey: ['document'],
    mutationFn: generateSalesAgreement,
    onSuccess: async (data) => {
      if (activeStep === SAVE_AND_DOWNLOAD && data.documentId) {
        await previewDocumentHandler(data.documentId)
        onClose()
      }
      if (activeStep < SAVE_AND_DOWNLOAD) {
        goToNext()
      }
      queryClient.invalidateQueries({
        queryKey: ['me'],
      })

      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'generateDocument',
        title: t('createSalesAgreement.createdSalesAgreement'),
        description: `${t('createSalesAgreement.createdDescription')} `,
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      setActiveStep(REVIEW_CONTACT)
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'generateDocument',
        title: t('errors.title'),
        description: t('errors.salesAgreementNotCreated'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })
  const { mutate: mutationSendForSigning, isPending: isSendingForSigning } =
    useMutation({
      mutationKey: ['document'],
      mutationFn: sendToSign,
      onSuccess: () => {
        onClose()
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          id: 'sendForSign',
          title: t('createSalesAgreement.sentForSign'),
          description: `${t('createSalesAgreement.sendForSignDescription')} `,
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          id: 'sendForSign',
          title: t('errors.title'),
          description: t('errors.sendForSignFail'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const previewDocumentHandler = async (documentId: string) => {
    try {
      setDownloading(true)
      const { content, name } = await downloadDocument(documentId)
      setDownloading(false)
      downloadBase64File(content, 'application/pdf', name)
    } catch (err) {
      setDownloading(false)
    }
  }

  const onSubmit = async (values: InitialFormValues) => {
    const salesAgreementValues: SalesAgreementProps = {
      propertyId: values.property?.id || 0,
      realtorId: currentUser?.id || 0,
      sellerIds: values.sellerIds,
      validityInMonths: values.validityInMonths,
      documents: values.documents,
      documentLanguage: values.documentLanguage,
      levelOfSignature: values.levelOfSignature,
      companyReference: values.companyReference,
      roaiib: values.roaiib,
      liabilityInsurance: values.liabilityInsurance,
    }

    mutationCreateSalesAgreement(salesAgreementValues)
  }

  // feature e-signature will be re-enabled soon
  // const sendForSignHandler = async () => {
  //   mutationSendForSigning(documentId)
  // }

  const handleReject = () => {
    setShowConfirmationModal(false)

    onClose()
  }

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  return (
    <>
      <Formik
        initialValues={initialFormValues}
        onSubmit={onSubmit}
        validate={(values) => {
          try {
            validateYupSchema(values, CreateSalesAgreementSchema, true, {
              isRealtorInPalmaOffice,
            })
          } catch (e) {
            return yupToFormErrors(e)
          }
        }}
      >
        {(formik) => {
          const { values, isValid, isSubmitting } = formik
          const onAccept = () => {
            nextStep(values)
          }
          const acceptLabel = (() => {
            if (activeStep < SAVE_AND_DOWNLOAD) return t('next')
            if (values.levelOfSignature === SIGN_METHODS.PEN_PAPER)
              return t('saveAndDownload')
            return t('createSalesAgreement.sendForSign')
          })()

          const cancelTitle = (() => {
            if (activeStep > CREATE_SALE_AGREEMENT_FORM) return t('prev')
            return t('cancel')
          })()

          return !isMobile ? (
            <CustomModal
              isOpen={!isMobile}
              onClose={() => handleConfirmationModal(() => handleReject)}
              onAccept={onAccept}
              onReject={
                activeStep > CREATE_SALE_AGREEMENT_FORM &&
                activeStep <= SAVE_AND_DOWNLOAD
                  ? activeStep === SAVE_AND_DOWNLOAD
                    ? () => handleConfirmationModal(() => previousStep)
                    : previousStep
                  : () => handleConfirmationModal(() => handleReject)
              }
              title={t('createSalesAgreement.title')}
              acceptTitle={acceptLabel}
              cancelTitle={cancelTitle}
              isLoading={
                isCreatingSalesAgreementLoading ||
                isSendingForSigning ||
                docIsDownloading
              }
              disableAcceptButton={
                !isValid ||
                isCreatingSalesAgreementLoading ||
                isSendingForSigning ||
                docIsDownloading ||
                isInvalidToNextStep
              }
              disableRejectButton={
                isCreatingSalesAgreementLoading ||
                isSendingForSigning ||
                docIsDownloading ||
                (activeStep === SAVE_AND_DOWNLOAD &&
                  values.levelOfSignature === SIGN_METHODS.E_SIGNATURE)
              }
              disableModalClosing={
                isCreatingSalesAgreementLoading ||
                isSendingForSigning ||
                docIsDownloading
              }
              size="xl"
            >
              <Body
                activeStep={activeStep}
                onClickEditProperty={setEditPropertyData}
                currentUser={currentUser}
                preSelectedProperty={preSelectedProperty}
                onValidateMissingInfo={handleSetValidStep}
                isMissingInfo={isInvalidToNextStep}
                steps={steps}
                onDownload={() =>
                  salesAgreementCreated?.documentId &&
                  previewDocumentHandler(salesAgreementCreated?.documentId)
                }
                isDownloading={docIsDownloading}
              />
            </CustomModal>
          ) : (
            <CreateSalesAgreementDrawer
              isOpen={isMobile}
              onClose={() => handleConfirmationModal(() => handleReject)}
              onAccept={onAccept}
              onReject={
                activeStep > CREATE_SALE_AGREEMENT_FORM &&
                activeStep <= SAVE_AND_DOWNLOAD
                  ? activeStep === SAVE_AND_DOWNLOAD
                    ? () => handleConfirmationModal(() => previousStep)
                    : previousStep
                  : () => handleConfirmationModal(() => handleReject)
              }
              title={t('createSalesAgreement.title')}
              acceptTitle={acceptLabel}
              isLoading={isCreatingSalesAgreementLoading}
              disableAcceptButton={
                !isValid || isSubmitting || isInvalidToNextStep
              }
              disableRejectButton={
                isCreatingSalesAgreementLoading ||
                isSendingForSigning ||
                docIsDownloading ||
                (activeStep === SAVE_AND_DOWNLOAD &&
                  values.levelOfSignature === SIGN_METHODS.E_SIGNATURE)
              }
            >
              <Body
                {...formik}
                activeStep={activeStep}
                currentUser={currentUser}
                onClickEditProperty={setEditPropertyData}
                preSelectedProperty={preSelectedProperty}
                onValidateMissingInfo={handleSetValidStep}
                isMissingInfo={isInvalidToNextStep}
                steps={steps}
                onDownload={() =>
                  salesAgreementCreated?.documentId &&
                  previewDocumentHandler(salesAgreementCreated?.documentId)
                }
                isDownloading={docIsDownloading}
              />
            </CreateSalesAgreementDrawer>
          )
        }}
      </Formik>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={confirmationModalAcceptFunction}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
      {editPropertyData && (
        <EditPropertyForm
          isOpen={true}
          onClose={() => setEditPropertyData(undefined)}
          onSuccess={() => setEditPropertyData(undefined)}
          propertyData={editPropertyData}
          mainLanguage={LanguageCodeEnum.EN}
        />
      )}
    </>
  )
}
