import { DrawerWrapper } from './DrawerWraper'
import { CustomModal } from '../Modal/Modal'
import { ReactNode } from 'react'
import { useMobile } from '@/hooks/useMobile'
import { BoxProps } from '@chakra-ui/react'
import { useUnsavedChangesGuard } from '@/hooks/useUnsavedChangesGuard'
import { ConfirmationModal } from '../Modal/ConfirmationModal'

interface Props {
  isOpen: boolean
  onClose: () => void
  onAccept: () => void
  onReject: () => void
  title: string
  children: ReactNode
  disableAcceptButton?: boolean
  disableRejectButton?: boolean
  disableModalClosing?: boolean
  isLoading?: boolean
  acceptTitle?: string
  cancelTitle?: string
  scrollable?: boolean
  size?: string
  isCentered?: boolean
  onlyDrawer?: boolean
  noPadding?: boolean
  headerContent?: React.ReactNode
  modalBodyProps?: BoxProps
  footerContent?: ReactNode
  dirty?: boolean
  isSubmitting?: boolean
  rejectHasSecondaryActions?: boolean
}

export const DeviceAdapter = (props: Props) => {
  const { dirty, isSubmitting, onClose, onReject, rejectHasSecondaryActions } =
    props
  const shouldBlock = !!dirty && !isSubmitting

  const { isOpen, guard, confirm, cancel } = useUnsavedChangesGuard(shouldBlock)

  const isMobile = useMobile()
  return (
    <>
      {isMobile || props.onlyDrawer ? (
        <DrawerWrapper
          {...props}
          onClose={() => guard(() => onClose())}
          onReject={() =>
            !rejectHasSecondaryActions ? guard(() => onReject()) : onReject()
          }
        />
      ) : (
        <CustomModal
          {...props}
          onClose={() => guard(() => onClose())}
          onReject={() =>
            !rejectHasSecondaryActions ? guard(() => onReject()) : onReject()
          }
        />
      )}
      <ConfirmationModal
        isOpen={isOpen}
        isCentered
        onAccept={confirm} // user confirms: run the guarded action (onClose)
        onReject={cancel} // user cancels: keep editing
        hideText={false}
      />
    </>
  )
}
