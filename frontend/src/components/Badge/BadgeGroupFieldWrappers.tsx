import BadgeGroupField from './BadgeGroupField'
import { FormLabel } from '@chakra-ui/react'
import { useField } from 'formik'
import {
  ContactType,
  FIPurchaseOfferPaymentMethod,
  FIPurchaseOfferRightOfUseTransfer,
  FIPurchaseOfferDigitalPurchaseExpenses,
  FIPurchaseOfferDownPaymentTerm,
  FIDetailsOfSaleOwnershipType,
  FIDetailsOfSaleTransactionMethod,
  FISalesAgreementShareRegisterFormatEnum,
} from '@/generated-types/api'
import { TFunction, useTranslation } from 'next-i18next'

export const getBadgeGroupItemLabel = (label: string) => (
  <FormLabel overflow="hidden" whiteSpace="nowrap" textOverflow="ellipsis">
    {label}
  </FormLabel>
)

const getItemLabels = (t: TFunction, itemLabelPrefix: string) => {
  return (itemEntry: [string, string | number]) => {
    const [key] = itemEntry

    switch (itemLabelPrefix) {
      case 'enums.contactType':
        switch (key) {
          case ContactType.Person:
            return t('enums.contactType.Person')
          case ContactType.Organization:
            return t('enums.contactType.Organization')
          case ContactType.Estate:
            return t('enums.contactType.Estate')
          default:
            return key
        }

      case 'fiPurchaseOffer.form.enums.paymentMethod':
        switch (key) {
          case FIPurchaseOfferPaymentMethod.cash:
            return t('fiPurchaseOffer.form.enums.paymentMethod.cash')
          case FIPurchaseOfferPaymentMethod.installment:
            return t('fiPurchaseOffer.form.enums.paymentMethod.installment')
          default:
            return key
        }

      case 'fiPurchaseOffer.form.enums.transferRightOfUse':
        switch (key) {
          case FIPurchaseOfferRightOfUseTransfer.purchase:
            return t('fiPurchaseOffer.form.enums.transferRightOfUse.purchase')
          case FIPurchaseOfferRightOfUseTransfer.later:
            return t('fiPurchaseOffer.form.enums.transferRightOfUse.later')
          default:
            return key
        }

      case 'fiPurchaseOffer.form.enums.digitalPurchaseExpenses':
        switch (key) {
          case FIPurchaseOfferDigitalPurchaseExpenses.buyer:
            return t('fiPurchaseOffer.form.enums.digitalPurchaseExpenses.buyer')
          case FIPurchaseOfferDigitalPurchaseExpenses.seller:
            return t(
              'fiPurchaseOffer.form.enums.digitalPurchaseExpenses.seller'
            )
          case FIPurchaseOfferDigitalPurchaseExpenses.both:
            return t('fiPurchaseOffer.form.enums.digitalPurchaseExpenses.both')
          case FIPurchaseOfferDigitalPurchaseExpenses.other:
            return t('fiPurchaseOffer.form.enums.digitalPurchaseExpenses.other')
          default:
            return key
        }

      case 'fiPurchaseOffer.form.enums.downPaymentTerm':
        switch (key) {
          case FIPurchaseOfferDownPaymentTerm.mark_as_paid:
            return t('fiPurchaseOffer.form.enums.downPaymentTerm.mark_as_paid')
          case FIPurchaseOfferDownPaymentTerm.to_be_paid:
            return t('fiPurchaseOffer.form.enums.downPaymentTerm.to_be_paid')
          case FIPurchaseOfferDownPaymentTerm.other:
            return t('fiPurchaseOffer.form.enums.downPaymentTerm.other')
          default:
            return key
        }

      case 'fiDetailsOfSale.form.enums.ownershipType':
        switch (key) {
          case FIDetailsOfSaleOwnershipType.percentage:
            return t('fiDetailsOfSale.form.enums.ownershipType.percentage')
          case FIDetailsOfSaleOwnershipType.fraction:
            return t('fiDetailsOfSale.form.enums.ownershipType.fraction')
          default:
            return key
        }

      case 'fiDetailsOfSale.form.enums.transactionMethod':
        switch (key) {
          case FIDetailsOfSaleTransactionMethod.traditional:
            return t('fiDetailsOfSale.form.enums.transactionMethod.traditional')
          case FIDetailsOfSaleTransactionMethod.dias:
            return t('fiDetailsOfSale.form.enums.transactionMethod.dias')
          default:
            return key
        }

      case 'fiSalesAgreement.form.enums.shareRegisterFormat':
        switch (key) {
          case FISalesAgreementShareRegisterFormatEnum.digital:
            return t('fiSalesAgreement.form.enums.shareRegisterFormat.digital')
          case FISalesAgreementShareRegisterFormatEnum.paper:
            return t('fiSalesAgreement.form.enums.shareRegisterFormat.paper')
          default:
            return key
        }
      default:
        const translationKey = `${itemLabelPrefix}.${key}`
        const translation = t(translationKey)
        return translation === translationKey ? key : translation
    }
  }
}

interface BooleanBadgeGroupFieldProps {
  label: string
  name: string
  onValueChanged?: (value: boolean | null) => void
  toggle?: boolean
  instruction?: string
  required?: boolean
}

export const BooleanBadgeGroupField: React.FC<BooleanBadgeGroupFieldProps> = ({
  label,
  name,
  onValueChanged,
  toggle = true,
  instruction,
  required,
}) => {
  const { t } = useTranslation(['common'])
  const [field] = useField(name)

  return (
    <BadgeGroupField
      toggle={toggle}
      label={getBadgeGroupItemLabel(label)}
      name={name}
      type="array"
      items={[true, false]}
      instruction={instruction}
      onValueChanged={(value) => {
        if (!onValueChanged) return
        if (field.value === value) {
          onValueChanged(null)
          return
        }
        onValueChanged(value)
      }}
      itemLabels={(item) => (item ? t('yes') : t('no'))}
      required={required}
    />
  )
}

interface EnumBadgeGroupFieldProps<T extends Record<string, string | number>>
  extends BooleanBadgeGroupFieldProps {
  enum: T
  itemLabelPrefix: string
  required?: boolean
}

export const EnumBadgeGroupField = <T extends Record<string, string | number>>({
  label,
  name,
  toggle,
  instruction,
  enum: enumType,
  itemLabelPrefix,
  required,
}: EnumBadgeGroupFieldProps<T>) => {
  const { t } = useTranslation(['common'])

  return (
    <BadgeGroupField
      toggle={toggle}
      itemProps={{ textTransform: 'none' }}
      label={getBadgeGroupItemLabel(label)}
      name={name}
      type="enum"
      items={Object.entries(enumType)}
      instruction={instruction}
      itemLabels={getItemLabels(t, itemLabelPrefix)}
      required={required}
    />
  )
}
