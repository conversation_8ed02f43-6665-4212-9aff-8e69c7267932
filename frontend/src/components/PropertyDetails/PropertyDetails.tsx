import {
  CommissionTypeEnum,
  FeaturesRead,
  LanguageCodeEnum,
  PropertyImage,
  PropertyImagesResponse,
  ReadProperty,
} from '@/types/property'
import {
  Box,
  BoxProps,
  Button,
  Divider,
  Flex,
  Grid,
  GridItem,
  Heading,
  Link,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react'
import { Dispatch, ReactNode, SetStateAction } from 'react'
import { useTranslation } from 'next-i18next'
import { ImageWithFallback } from '../ImageWithFallback'
import {
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
} from '@chakra-ui/react'
import PropertyStatus from '../PropertyStatus/PropertyStatus'
import placeholder from '@/cache/images/property_placeholder.png'
import { getDescriptionsGroupedByLanguage } from '@/utils/getDescriptionsGroupedByLanguage'
import { MediaCarousel } from '../Carousel'
import { formatESPrice } from '@/utils/formatESPrice'
import { Empty } from '../Empty'
import _ from 'lodash'
import { IMAGE_SIZE_M_WIDTH } from '@/utils/constants'
import {
  generateBrochureLink,
  generateWindowBrochureLink,
} from '@/lib/strandproperties'
import PropertyValidation from '../PropertyValidation/PropertyValidation'
import { isPropertyFromResalesOnline } from '@/utils/propertyFromWhichIntegration'
import { getCommissionInCurrency, getPrices } from '@/utils/commission'
import { EventRead } from '@/types/event'
import { getFormattedNotes } from '@/components/PropertyPage/GetFormattedNotes'
import usePhoneNumberWithOrganization from '@/hooks/usePhoneNumberWithOrganisation'
import PropertySidebar from '../PropertySidebar/PropertySidebar'
import { AdvertisementResponse } from '@/types/advertisements'
import { ApiResponseWithMetadata } from '@/types/common'

type Props = {
  property: ReadProperty
  setTabIndex: Dispatch<SetStateAction<number>>
  mainLanguage: LanguageCodeEnum
  coverImage?: PropertyImage
  propertyImages: PropertyImagesResponse
  isPhotographer: boolean
  events?: EventRead[]
  ads?: ApiResponseWithMetadata<AdvertisementResponse>
  refetchEvents: () => void
}

type ContainerProps = BoxProps & {
  title: string
  children: ReactNode
}

const Container = ({ title, children, ...props }: ContainerProps) => {
  return (
    <Box mb="10px" minW="70px" {...props}>
      <Text variant="small" color="grays.gray7" pb={[0, '4px']}>
        {title}
      </Text>
      <Box fontSize="15px">{children}</Box>
    </Box>
  )
}

const LANGUAGES = ['en', 'es', 'fi', 'de', 'sv'] as const

const getAmenitiesByGroup = (amenities: FeaturesRead[]) => {
  const formattedAmenities: { [key: string]: string[] } = {}

  for (const amenity of amenities) {
    if (formattedAmenities[amenity.featureGroupName])
      formattedAmenities[amenity.featureGroupName].push(amenity.name)
    else formattedAmenities[amenity.featureGroupName] = [amenity.name]
  }

  return formattedAmenities
}

const PropertyDetails = ({
  property,
  mainLanguage,
  coverImage,
  propertyImages,
  events,
  ads,
  refetchEvents,
}: Props) => {
  const { t } = useTranslation(['common'])

  const isMobile = useBreakpointValue({
    base: true,
    md: false,
  })
  const { formatPhoneNumber } = usePhoneNumberWithOrganization()

  const remainingLanguagesWithoutMainLanguage = LANGUAGES.filter(
    (lang) => lang !== mainLanguage
  )

  const prices = getPrices({
    property: property,
    getOldPriceIfCurrentNotAvailable: false,
  })

  const openVideoGallery = () =>
    MediaCarousel({
      urls: property.videoStreams
        .filter((video) => !video.isHidden)
        .map((video) => video.url),
    })

  const openImageGallery = () =>
    MediaCarousel({
      urls: propertyImages
        .filter((image) => !image.isHidden)
        .map((image) => image.url),
    })

  const handleCreateBrochure = () =>
    window.open(generateBrochureLink(property.reference), '_blank')

  const handleCreateWindowBrochure = () =>
    window.open(generateWindowBrochureLink(property.reference), '_blank')

  return (
    <Flex
      direction="column"
      width="100%"
      mr={[0, 0, '16px']}
      position="relative"
    >
      <Flex
        direction={['column', 'column', 'column', 'row-reverse']}
        justifyContent="space-between"
        mr={[0, 0, '16px']}
        mt="20px"
        mb={['110px', '110px', '0']}
        p={['0 24px', '0 24px', '0 24px', 0]}
        position="relative"
        width="100%"
        gap="4"
      >
        {/* Right Section */}
        <Flex
          direction="column"
          mb={[22, 22, 0]}
          maxW={['100%', '400px', '400px']}
        >
          <Box
            width={['100%', '400px', '400px']}
            height={['213px', '254px']}
            position="relative"
            mb="17px"
            onClick={openImageGallery}
            cursor={propertyImages.length > 0 ? 'pointer' : 'initial'}
          >
            <ImageWithFallback
              src={
                coverImage
                  ? `${coverImage.url}?width=${IMAGE_SIZE_M_WIDTH}`
                  : undefined
              }
              alt="main image"
              fallback={placeholder.src}
            />
          </Box>
          <Flex gap={2} mt={2}>
            {propertyImages.length > 0 && (
              <Button
                variant="transparent"
                width="88px"
                onClick={openImageGallery}
              >
                <span
                  className="material-symbols-outlined"
                  style={{
                    fontSize: '20px',
                    paddingRight: '6px',
                    fontWeight: '400',
                  }}
                >
                  imagesmode
                </span>
                <Text fontSize="12px" letterSpacing="0.1em" fontWeight="600">
                  ({propertyImages.length})
                </Text>
              </Button>
            )}
            {property.videoStreams.length > 0 && (
              <Button
                variant="transparent"
                width="88px"
                onClick={openVideoGallery}
              >
                <span
                  className="material-symbols-outlined"
                  style={{
                    fontSize: '20px',
                    paddingRight: '6px',
                    fontWeight: '400',
                  }}
                >
                  smart_display
                </span>
                <Text fontSize="12px" letterSpacing="0.1em" fontWeight="600">
                  ({property.videoStreams.length})
                </Text>
              </Button>
            )}
          </Flex>

          {
            <Flex gap={2} mt={2}>
              <Button
                width="fit-content"
                variant={['transparent']}
                size={'md'}
                onClick={handleCreateBrochure}
                mb="4"
              >
                {t('createBrochure')}
              </Button>
              <Button
                width="fit-content"
                variant={['transparent']}
                size={'md'}
                onClick={handleCreateWindowBrochure}
                mb="4"
              >
                {t('createWindowBrochure')}
              </Button>
            </Flex>
          }

          {isMobile && events && events.length > 0 && (
            <PropertySidebar
              events={events}
              advertisements={ads?.records}
              refreshEvents={refetchEvents}
            />
          )}

          {!isMobile && (
            <Flex direction="column" gap="2" pt="8">
              <Heading whiteSpace="nowrap" variant="H3" fontSize="xl" mb="4">
                {t('financialInformation')}
              </Heading>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                {prices.priceSale && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Sale)`}>
                      {formatESPrice({
                        price: prices.priceSale,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}

                {prices.priceRentLongTerm && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Rent Long Term)`}>
                      {formatESPrice({
                        price: prices.priceRentLongTerm,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}

                {prices.priceRentShortTerm && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Rent Short Term)`}>
                      {formatESPrice({
                        price: prices.priceRentShortTerm,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}
              </Grid>
              <Text color="tabs.selected" variant="xs">
                {t('commission').toUpperCase()}
              </Text>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('commissionType')}>
                    {property.commissionType}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container
                    title={`${
                      property.commissionType === CommissionTypeEnum.PERCENT
                        ? `${t('commission')} %`
                        : `${t('commission')} ${
                            property.currency?.toUpperCase() || 'EUR'
                          }`
                    }`}
                  >
                    {property.commissionType === CommissionTypeEnum.PERCENT
                      ? `${property.commission}% = ${getCommissionInCurrency(
                          property
                        )}`
                      : formatESPrice({
                          price: property.commission ?? 0,
                          currency: property.currency,
                          removeDecimals: true,
                        })}
                  </Container>
                </GridItem>
                <GridItem colSpan={2}>
                  <Container
                    title={t('commissionNotes')}
                    whiteSpace={'pre-wrap'}
                    overflowWrap={'break-word'}
                  >
                    {getFormattedNotes(property.commissionNotes) ?? <Empty />}
                  </Container>
                </GridItem>

                {/* IVA Tax */}
                <GridItem>
                  <Container title={`${t('ivaTax')}`}>
                    {property.ivaTax ?? <Empty />}
                  </Container>
                </GridItem>
              </Grid>
              <Text color="tabs.selected" variant="xs">
                {t('propertyFees').toUpperCase()}
              </Text>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('communalFees')}>
                    {property.communalFees ? (
                      `${property.communalFees} €/month`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>

                <GridItem>
                  <Container title={t('IBI')}>
                    {property.ibi ? `${property.ibi} €/year` : <Empty />}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('garbageTax')}>
                    {property.garbageTax ? (
                      `${property.garbageTax} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('water')}>
                    {property.waterFee ? (
                      `${property.waterFee} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('electricity')}>
                    {property.electricity ? (
                      `${property.electricity} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
              </Grid>
            </Flex>
          )}
        </Flex>

        {/* Left Section */}
        <Flex
          direction="column"
          width="100%"
          mt={['0px', '15px', '15px', '0px']}
          gap="4"
        >
          <Flex direction="column">
            <PropertyStatus
              status={property?.status || 'Draft'}
              realtors={property?.realtorUsers}
              referenceCode={property.reference}
              portals={property.portals}
              propertyId={property.id}
              dataSource={property.dataSource}
              soldBy={property.soldBy}
            />

            {property?.id &&
              !isPropertyFromResalesOnline(property.dataSource) && (
                <PropertyValidation propertyId={property.id} />
              )}
          </Flex>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('reference')}>{property.reference}</Container>
            </GridItem>
            <GridItem>
              <Container title={t('specs')}>
                {property.propertyType && `${property.propertyType}`}
                {property.bedrooms
                  ? property.bedrooms > 1
                    ? `, ${property.bedrooms} ${t('beds')}`
                    : `, ${property.bedrooms} ${t('bed')}`
                  : ''}
                {property.bathrooms
                  ? property.bathrooms > 1
                    ? `, ${property.bathrooms} ${t('baths')}`
                    : `, ${property.bathrooms} ${t('bath')}`
                  : ''}
                {property.interiorArea ? (
                  <>
                    {`, ${property.interiorArea}m`}
                    <sup>2</sup>
                    {` ${t('interior')}`}
                  </>
                ) : (
                  ''
                )}
                {property.plotArea ? (
                  <>
                    {`, ${property.plotArea}m`}
                    <sup>2</sup>
                    {` ${t('plot')}`}
                  </>
                ) : (
                  ''
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('condition')}>
                {property.condition ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('contractType')}>
                {property.isExclusive ? t('exclusive') : t('nonExclusive')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('listingTypes')}>
                {property.listingTypes
                  ?.map((listingType) => listingType.name)
                  .join(', ')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('realtors')}>
                {property.realtorUsers
                  ?.map((realtor) => `${realtor.firstName} ${realtor.lastName}`)
                  .join(', ')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('sellers')}>
                {property.contacts && property.contacts.length > 0 ? (
                  property.contacts.map((contact, index) => (
                    <Link
                      key={contact.id}
                      href={`/contacts/${contact.id}`}
                      isExternal
                    >
                      {index > 0 && ', '}
                      {contact.name}
                    </Link>
                  ))
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('createdAt')}>
                {property.createdAt ? (
                  `${new Date(property.createdAt).toLocaleString('en-GB')} GMT`
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('updatedAt')}>
                {property.updatedAt ? (
                  `${new Date(property.updatedAt).toLocaleString('en-GB')} GMT`
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container
                title={t('editProperty.hostawayPropertyId')}
                whiteSpace={'pre-wrap'}
                overflowWrap={'break-word'}
              >
                {property.hostawayPropertyId ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem colSpan={2}>
              <Container
                title={t('internalNotes')}
                whiteSpace="pre-wrap"
                overflowWrap="break-word"
              >
                {getFormattedNotes(property.internalNotes) ?? <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('locationAndAddress').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(3, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('location')}>{property.location}</Container>
            </GridItem>
            <GridItem colSpan={[1, 2]}>
              <Container title={t('city')}>
                {property.city || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('streetAddress')}>
                {property.privateInfo.location?.address || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('postalCode')}>
                {property.privateInfo.location?.postCode || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('country')}>
                {property.country || <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('coordinates').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('latitudeAndLongitude')}>
                {property.latitude && property.longitude ? (
                  <Link
                    href={`https://www.google.com/maps/search/?api=1&query=${property.latitude}%2C${property.longitude}`}
                    isExternal
                  >{`${property.latitude}, ${property.longitude}`}</Link>
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('coordinatesVisibility')}>
                {property.isPublicCoordinates ? 'Public' : 'Private'}
              </Container>
            </GridItem>
          </Grid>

          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="4"
            width="100%"
            pr={[0, 0, '60px']}
          >
            <Heading whiteSpace="nowrap" variant="H3" fontSize="xl">
              {t('description')}
            </Heading>
            <Divider />
          </Flex>

          <Flex pr={[0, 0, '60px']} direction="column" mb="4">
            <Text variant="small" color="grays.gray7">
              {t('title')}
            </Text>
            <Heading variant="brand" pt="1" pb="4">
              {(property.descriptions &&
                getDescriptionsGroupedByLanguage(property.descriptions)?.[
                  mainLanguage
                ]?.tagline) || <Empty />}
            </Heading>

            <Container
              title={t('fullDescription')}
              style={{ whiteSpace: 'pre-line' }}
            >
              {(property.descriptions &&
                getDescriptionsGroupedByLanguage(property.descriptions)?.[
                  mainLanguage
                ]?.description) || <Empty />}
            </Container>
            <Accordion allowMultiple>
              {remainingLanguagesWithoutMainLanguage.map((lang) => (
                <AccordionItem key={`accordion_${lang}`}>
                  <AccordionButton
                    p="0"
                    mb="0"
                    height="47px"
                    _hover={{ backgroundColor: 'transparent' }}
                  >
                    <Box as="span" flex="1" textAlign="left">
                      <Text color="tabs.selected" variant="xs">
                        {t(lang).toUpperCase()}
                      </Text>
                    </Box>
                    <Flex
                      alignItems="center"
                      justifyContent="center"
                      bg="grays.grayBorder"
                      width="30px"
                      height="30px"
                      borderRadius="50%"
                    >
                      <AccordionIcon color="#1C1B1F" />
                    </Flex>
                  </AccordionButton>

                  <AccordionPanel pb={2} pl="0" pt="0">
                    <Text variant="small" color="grays.gray7">
                      {t('title')}
                    </Text>
                    <Heading variant="brand" pt="1" pb="4">
                      {(property.descriptions &&
                        getDescriptionsGroupedByLanguage(
                          property.descriptions
                        )?.[lang]?.tagline) || <Empty />}
                    </Heading>

                    <Container
                      title={t('fullDescription')}
                      style={{ whiteSpace: 'pre-line' }}
                    >
                      {(property.descriptions &&
                        getDescriptionsGroupedByLanguage(
                          property.descriptions
                        )?.[lang]?.description) || <Empty />}
                    </Container>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
          </Flex>

          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="4"
            width="100%"
            pr={[null, null, '60px']}
          >
            <Heading whiteSpace="nowrap" variant="H3" fontSize="xl">
              {t('additionalDetails')}
            </Heading>
            <Divider />
          </Flex>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('cadastralReference')}>
                {property.cadastralReference || <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('buildingSpecifications').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('buildingType')}>
                {property.propertyType || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('constructionYear')}>
                {property.builtYear || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('buildingConstructor')}>
                {property.buildingConstructor || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('floor')}>
                {property.floor ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('totalFloors')}>
                {property.totalFloors ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('lift')}>
                {property.buildingHasElevator !== null ? (
                  property.buildingHasElevator ? (
                    t('yes')
                  ) : (
                    t('no')
                  )
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('buildingMaterials').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('foundationAndStructure')}>
                {property.foundationAndStructure || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('roof')}>
                {property.roof || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('exteriorWalls')}>
                {property.exteriorWalls || <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('energyCertificate').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(4, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('certificateConsumptionRating')}>
                {property.certificateConsumptionRating || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('certificateConsumptionValue')}>
                {property.certificateConsumptionValue ? (
                  property.certificateConsumptionValue + ' Kwh/m² Year'
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('certificateEmissionRating')}>
                {property.certificateEmissionRating || <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('certificateEmissionValue')}>
                {property.certificateEmissionValue ? (
                  property.certificateEmissionValue + ' KgCO2/m² Year'
                ) : (
                  <Empty />
                )}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('spaceAndSize').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(4, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('builtSize')}>
                {property.builtArea ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('plotSize')}>
                {property.plotArea ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('terraceSize')}>
                {property.terraceArea ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('interiorSize')}>
                {property.interiorArea ?? <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('rooms').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('bedrooms')}>
                {property.bedrooms ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('bathrooms')}>
                {property.bathrooms ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('pax')}>
                {property.pax ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('toilets')}>
                {property.toilets ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('suiteBaths')}>
                {property.suiteBaths ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('roomsTotal')}>
                {property.roomsTotal ?? <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(4, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Text color="tabs.selected" variant="xs" mb="2">
                {t('garage').toUpperCase()}
              </Text>

              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(1, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('garageTypes')}>
                    {property.garageTypes.length > 0 ? (
                      property.garageTypes
                        .map((garageType) => garageType.name)
                        .join(', ')
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('parkingSpaces')}>
                    {property.parkingSpaces ?? <Empty />}
                  </Container>
                </GridItem>
              </Grid>
            </GridItem>
            <GridItem>
              <Text color="tabs.selected" variant="xs" mb="2">
                {t('pool').toUpperCase()}
              </Text>

              <Grid
                templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('poolTypes')}>
                    {property.poolTypes.length > 0 ? (
                      property.poolTypes
                        .map((poolType) => poolType.name)
                        .join(', ')
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
              </Grid>
            </GridItem>
            <GridItem>
              <Text color="tabs.selected" variant="xs" mb="2">
                {t('garden').toUpperCase()}
              </Text>

              <Grid
                templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('gardenTypes')}>
                    {property.gardenTypes.length > 0 ? (
                      property.gardenTypes
                        .map((gardenType) => gardenType.name)
                        .join(', ')
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
              </Grid>
            </GridItem>
            <GridItem>
              <Text color="tabs.selected" variant="xs" mb="2">
                {t('orientations').toUpperCase()}
              </Text>

              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(1, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('orientation')}>
                    {property.orientations.length > 0 ? (
                      property.orientations
                        .map((orientation) => orientation.name)
                        .join(', ')
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
              </Grid>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('keysAndHandoff').toUpperCase()}
          </Text>
          <Text variant="small" color="grays.gray7" pb={[0, '4px']}>
            {t('keysInExistence')}
          </Text>

          <Grid
            templateColumns={['repeat(2, 1fr)', 'repeat(3, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('protectedKeysTotal')}>
                {property.keysAndHandoff?.protectedKeysTotal ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('protectedKeysDelivered')}>
                {property.keysAndHandoff?.protectedKeysDelivered ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('protectedKeysExisting')}>
                {property.keysAndHandoff?.protectedKeysExisting ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('unprotectedKeysTotal')}>
                {property.keysAndHandoff?.unprotectedKeysTotal ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('unprotectedKeysDelivered')}>
                {property.keysAndHandoff?.unprotectedKeysDelivered ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('unprotectedKeysExisting')}>
                {property.keysAndHandoff?.unprotectedKeysExisting ?? <Empty />}
              </Container>
            </GridItem>
            <GridItem colSpan={[1, 3]}>
              <Container title={t('whereKeysCanBeFound')}>
                {property.keysAndHandoff?.otherKeysInfoDescription || <Empty />}

                {property.keysAndHandoff?.otherKeysInfoPhoneNumber && (
                  <>
                    <br />
                    {formatPhoneNumber(
                      '',
                      property.keysAndHandoff?.otherKeysInfoPhoneNumber
                    )}
                  </>
                )}
              </Container>
            </GridItem>
            <GridItem colSpan={[1, 3]}>
              <Container title={t('strandPropertiesKeysInfoOffice')}>
                {property.keysAndHandoff?.strandPropertiesKeysInfoOffice || (
                  <Empty />
                )}

                {property.keysAndHandoff?.strandPropertiesKeysInfoNotes && (
                  <>
                    <br />
                    {property.keysAndHandoff?.strandPropertiesKeysInfoNotes}
                  </>
                )}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('propertyHasElectricity')}>
                {property.keysAndHandoff?.hasElectricity ? t('yes') : t('no')}
              </Container>
            </GridItem>
            <GridItem colSpan={[1, 2]}>
              <Container title={t('propertyHasLights')}>
                {property.keysAndHandoff?.hasLights ? t('yes') : t('no')}
              </Container>
            </GridItem>
          </Grid>

          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="4"
            width="100%"
            pr={[0, 0, '60px']}
          >
            <Heading whiteSpace="nowrap" variant="H3" fontSize="xl">
              {t('amenities')}
            </Heading>
            <Divider />
          </Flex>

          {Object.entries(getAmenitiesByGroup(property.features)).map(
            ([amenityGroup, amenities]) => (
              <Flex key={amenityGroup} direction="column">
                <Text color="tabs.selected" variant="xs">
                  {amenityGroup.toUpperCase()}
                </Text>
                <Text fontSize="15px">{amenities.join(', ')}</Text>
              </Flex>
            )
          )}

          {property.telecommunicationSystems &&
            Object.values(property.telecommunicationSystems).some(
              (item) => item
            ) && (
              <Flex direction="column">
                <Text color="tabs.selected" variant="xs">
                  {t('telecommunicationSystems').toUpperCase()}
                </Text>
                <Text fontSize="15px">
                  {Object.entries(property.telecommunicationSystems)
                    .filter(([, value]) => value)
                    .map(([system, value]) => {
                      if (value) return _.startCase(system)
                    })
                    .join(', ')}
                </Text>
              </Flex>
            )}

          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="4"
            width="100%"
            pr={[0, 0, '60px']}
          >
            <Heading whiteSpace="nowrap" variant="H3" fontSize="xl">
              {t('conditionAndHistory')}
            </Heading>
            <Divider />
          </Flex>

          <Text color="tabs.selected" variant="xs">
            {t('renovations').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(1, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('majorRenovationsPerformed')}>
                {property.renovations?.majorRenovationsPerformed
                  ? t('renovationsHaveBeenPerformed')
                  : t('renovationsHaveNotBeenPerformed')}
              </Container>
            </GridItem>
            {property.renovations?.majorRenovationsPerformed && (
              <GridItem>
                <Container title={t('description')}>
                  {property.renovations?.describePerformedRenovations || (
                    <Empty />
                  )}
                </Container>
              </GridItem>
            )}
            <GridItem>
              <Container title={t('plannedRenovations')}>
                {property.renovations?.plannedRenovations
                  ? t('thereArePlannedRenovations')
                  : t('thereAreNoPlannedRenovations')}
              </Container>
            </GridItem>
            {property.renovations?.plannedRenovations && (
              <GridItem>
                <Container title={t('description')}>
                  {property.renovations?.describePlannedRenovations || (
                    <Empty />
                  )}
                </Container>
              </GridItem>
            )}
            <GridItem>
              <Container title={t('renovationsPerformedBeforeSellerOwnership')}>
                {property.renovations?.renovationsPerformedBeforeSellerOwnership
                  ? t('renovationsHaveBeenPerformed')
                  : t('renovationsHaveNotBeenPerformed')}
              </Container>
            </GridItem>
            {property.renovations
              ?.renovationsPerformedBeforeSellerOwnership && (
              <GridItem>
                <Container title={t('description')}>
                  {property.renovations
                    .describePreviousPerformedRenovations || <Empty />}
                </Container>
              </GridItem>
            )}
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('damagesAndDefects').toUpperCase()}
          </Text>

          <Grid
            templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
            rowGap="2"
            mb="4"
          >
            <GridItem colSpan={[1, 2]}>
              <Container title={t('defectsDamagesRepairObserved')}>
                {property.damagesAndDefects?.defectsDamagesRepairObserved
                  ? t('defectsFound')
                  : t('noDefectsFound')}
              </Container>
            </GridItem>
            {property.damagesAndDefects?.defectsDamagesRepairObserved && (
              <GridItem colSpan={[1, 2]}>
                <Container title={t('description')}>
                  {property.damagesAndDefects
                    ?.describeDefectsDamagesRepairs || <Empty />}
                </Container>
              </GridItem>
            )}
            <GridItem>
              <Container title={t('officialPermitsAcquired')}>
                {property.damagesAndDefects?.officialPermitsAcquired
                  ? t('yes')
                  : t('no')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('finalInspectionOfChanges')}>
                {property.damagesAndDefects?.finalInspectionOfChanges
                  ? t('yes')
                  : t('no')}
              </Container>
            </GridItem>
            <GridItem colSpan={[1, 2]}>
              <Text fontSize="15px" mb="2">
                {t('detailedAccountOfDamagesOrFault')}
              </Text>
              <Container title={t('description')}>
                {property.damagesAndDefects?.describeRepairWork || <Empty />}
              </Container>
            </GridItem>
          </Grid>

          <Text color="tabs.selected" variant="xs">
            {t('otherDamages').toUpperCase()}
          </Text>
          <Text fontSize="15px">{t('suspectedDamagesOrProblems')}</Text>

          <Grid
            templateColumns={[
              'repeat(1, 1fr)',
              'repeat(2, 1fr)',
              'repeat(4, 1fr)',
            ]}
            rowGap="2"
            mb="4"
          >
            <GridItem>
              <Container title={t('waterDamage')}>
                {property.otherDamages?.waterDamage ? t('yes') : t('no')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('moistureDamage')}>
                {property.otherDamages?.moistureDamage ? t('yes') : t('no')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('moldOrFungalProblems')}>
                {property.otherDamages?.moldOrFungalProblems
                  ? t('yes')
                  : t('no')}
              </Container>
            </GridItem>
            <GridItem>
              <Container title={t('otherSpecialDamages')}>
                {property.otherDamages?.otherSpecialDamages
                  ? t('yes')
                  : t('no')}
              </Container>
            </GridItem>
          </Grid>

          {isMobile && (
            <Flex direction="column" gap="2" pt="8">
              <Heading whiteSpace="nowrap" variant="H3" fontSize="xl" mb="4">
                {t('financialInformation')}
              </Heading>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                {prices.priceSale && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Sale)`}>
                      {formatESPrice({
                        price: prices.priceSale,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}
                {prices.priceRentLongTerm && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Rent Long Term)`}>
                      {formatESPrice({
                        price: prices.priceRentLongTerm,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}

                {prices.priceRentShortTerm && (
                  <GridItem>
                    <Container title={`${t('listedFor')} (Rent Short Term)`}>
                      {formatESPrice({
                        price: prices.priceRentShortTerm,
                        currency: property.currency,
                        removeDecimals: true,
                      })}
                    </Container>
                  </GridItem>
                )}
              </Grid>
              <Text color="tabs.selected" variant="xs">
                {t('commission').toUpperCase()}
              </Text>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('commissionType')}>
                    {property.commissionType}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container
                    title={`${
                      property.commissionType === CommissionTypeEnum.PERCENT
                        ? `${t('commission')} %`
                        : `${t('commission')} ${
                            property.currency?.toUpperCase() || 'EUR'
                          }`
                    }`}
                  >
                    {property.commissionType === CommissionTypeEnum.PERCENT
                      ? `${property.commission}% = ${getCommissionInCurrency(
                          property
                        )}`
                      : formatESPrice({
                          price: property.commission ?? 0,
                          currency: property.currency,
                          removeDecimals: true,
                        })}
                  </Container>
                </GridItem>
                <GridItem colSpan={2}>
                  <Container
                    title={t('commissionNotes')}
                    whiteSpace={'pre-wrap'}
                    overflowWrap={'break-word'}
                  >
                    {getFormattedNotes(property.commissionNotes) ?? <Empty />}
                  </Container>
                </GridItem>
                {/* IVA Tax */}
                <GridItem>
                  <Container title={`${t('ivaTax')}`}>
                    {property.ivaTax ?? <Empty />}
                  </Container>
                </GridItem>
              </Grid>
              <Text color="tabs.selected" variant="xs">
                {t('propertyFees').toUpperCase()}
              </Text>
              <Grid
                templateColumns={['repeat(1, 1fr)', 'repeat(2, 1fr)']}
                rowGap="2"
                mb="4"
              >
                <GridItem>
                  <Container title={t('communalFees')}>
                    {property.communalFees ? (
                      `${property.communalFees} €/month`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>

                <GridItem>
                  <Container title={t('IBI')}>
                    {property.ibi ? `${property.ibi} €/year` : <Empty />}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('garbageTax')}>
                    {property.garbageTax ? (
                      `${property.garbageTax} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('water')}>
                    {property.waterFee ? (
                      `${property.waterFee} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
                <GridItem>
                  <Container title={t('electricity')}>
                    {property.electricity ? (
                      `${property.electricity} €/year`
                    ) : (
                      <Empty />
                    )}
                  </Container>
                </GridItem>
              </Grid>
            </Flex>
          )}
        </Flex>
      </Flex>
    </Flex>
  )
}

export default PropertyDetails
