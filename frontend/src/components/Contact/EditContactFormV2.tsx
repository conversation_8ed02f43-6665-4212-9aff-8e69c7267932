import {
  Button,
  useBreakpointValue,
  useToast,
  IconButton,
} from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useTranslation } from 'next-i18next'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { getContact } from '@/queries/contact'
import { getUsers } from '@/queries/users'
import { useRef, useState } from 'react'
import { get } from 'lodash'
import { useTags } from '@/queries/tags'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { EditIcon } from '@chakra-ui/icons'
import { ContactType, SchemaContactRead } from '@/generated-types/api'
import { EditContactV2Props } from '@/types/contact'
import createValidationSchema from './ContactSchema'
import { DeviceAdapter } from '../ActionDrawer/DeviceAdapter'
import {
  ContactFormV2,
  getRelatedParties,
  getRelatedPartiesInputFields,
  RelatedPartiesInputFields,
} from './ContactFormV2'
import { updateContactV2 } from '@/queries/contactV2'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { getFullName } from '@/types/users'
import { getToastProps } from '@/utils/toastProps'
import { getListTags } from './Tags'

export const EditContactByIdFormV2 = ({
  contactId,
  labelEditButton = 'editDetails',
  iconButton,
  onEditSuccess,
}: {
  contactId: string
  labelEditButton?: string
  iconButton?: boolean
  onEditSuccess?: () => void
}) => {
  const { data: contact, refetch } = useQuery({
    queryKey: ['contact', contactId],
    queryFn: () => getContact(contactId),
  })

  const handleEditSuccess = () => {
    refetch()
    onEditSuccess?.()
  }

  if (!contact) {
    return null
  }

  return (
    <EditContactFormV2
      contactData={contact}
      labelEditButton={labelEditButton}
      iconButton={iconButton}
      onEditSuccess={handleEditSuccess}
    />
  )
}

export const EditContactFormV2 = ({
  contactData,
  labelEditButton = 'editDetails',
  iconButton,
  onEditSuccess,
}: {
  contactData: SchemaContactRead
  labelEditButton?: string
  iconButton?: boolean
  onEditSuccess?: () => void
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const { organization } = useUserAndOrganization()
  const formRef =
    useRef<FormikProps<EditContactV2Props & RelatedPartiesInputFields>>(null)

  const [showEditContactForm, setShowEditContactForm] = useState<boolean>(false)

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        onlyActive: true,
        organizationId: organization?.id,
      }),
  })

  const { data: tags = [] } = useTags()

  const onClose = () => {
    setShowEditContactForm(false)
  }

  const queryClient = useQueryClient()
  const { isPending: isUpdatingContactLoading, mutate: mutationUpdateContact } =
    useMutation({
      mutationFn: updateContactV2,
      onSuccess: async () => {
        await queryClient.invalidateQueries({ queryKey: ['contacts'] })
        await queryClient.invalidateQueries({
          queryKey: ['contact', contactData.id],
        })
        await queryClient.invalidateQueries({
          queryKey: ['contactActivityEvents', contactData.id.toString()],
        })
        await queryClient.invalidateQueries({
          queryKey: ['tags'],
        })

        // Explicitly refetch the queries
        await queryClient.refetchQueries({ queryKey: ['contacts'] })
        await queryClient.refetchQueries({
          queryKey: ['contact', contactData.id],
        })
        await queryClient.refetchQueries({
          queryKey: ['contactActivityEvents', contactData.id.toString()],
        })
        await queryClient.refetchQueries({ queryKey: ['tags'] })

        onEditSuccess && onEditSuccess()
        onClose()
        toast(
          getToastProps({
            title: t('editContact.updatedTitle'),
            description: t('editContact.updatedDescription'),
            status: 'success',
            isMobile,
            variant: 'customSuccess',
          })
        )
      },
      onError: (e) => {
        const description = get(e, 'response.data.error')
        const finalDescription =
          description === 'tag-already-exists'
            ? t('tagAlreadyExists')
            : description || t('errors.contactNotUpdated')
        toast(
          getToastProps({
            title: t('errors.title'),
            description: finalDescription,
            status: 'error',
            isMobile,
          })
        )
      },
    })

  const handleOnSave = async () => {
    if (!formRef.current) {
      return
    } else {
      const validation = await formRef.current.validateForm()
      if (Object.keys(validation).length > 0) {
        formRef.current.setErrors(validation)
        await formRef.current.setTouched(
          Object.keys(validation).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {})
        )

        toast(
          getToastProps({
            title: t('createContact.validation.errorToast'),
            status: 'error',
            isMobile,
          })
        )
        return
      }
    }

    const tags = [
      // These are the system tags
      ...formRef.current.values.tags,
      // These are the user created tags
      ...formRef.current.values.listTags,
    ]

    // Convert tags to newTags and existingTags
    const editContactPayload = {
      ...formRef.current.values,
      tags,
      newTags: tags
        .filter((tag) => typeof tag.value === 'string')
        .map((tag) => tag.value.toString()),
      existingTags: tags
        .filter((tag) => typeof tag.value === 'number')
        .map((tag) => tag.value as number),
      phoneNumbers: formRef.current.values.phoneNumbers || [],
      assignedTo: formRef.current.values.assignedTo || [],
      relatedParties: getRelatedParties(formRef.current.values),
    } satisfies EditContactV2Props

    mutationUpdateContact({
      id: contactData.id,
      contactData: editContactPayload,
    })
  }

  const relatedParties = getRelatedPartiesInputFields(contactData)

  const initialValues: EditContactV2Props & RelatedPartiesInputFields = {
    ...contactData,
    tags: contactData.tags.map((tag) => ({
      value: tag.id,
      label: tag.name,
    })),
    listTags: getListTags(
      contactData.tags.map((tag) => ({
        value: tag.id,
        label: tag.name,
      }))
    ),
    newTags: [],
    existingTags: contactData.tags.map((tag) => tag.id),
    assignedTo: contactData.assignedToUsers.map((assignedTo) => assignedTo.id),
    groups: contactData.groups.map((group) => group.id),
    propertyTypes: contactData.propertyTypes?.map((item) => item.id) || [],
    //TODO: we need the type in the related parties to be able to map the right ones
    relatedParties: [],
    ...relatedParties,
  }

  const validationSchema = createValidationSchema(t)

  const getTitleByStep = (type: ContactType | null): string => {
    switch (type) {
      case ContactType.Person:
        return t('createContact.updateContact')
      case ContactType.Organization:
        return t('createContact.updateCompany')
      case ContactType.Estate:
        return t('createContact.updateEstate')
      default:
        return t('createContact.updateContact')
    }
  }

  return (
    <>
      {iconButton ? (
        <IconButton
          aria-label="Click here to open modal for editing contact details"
          variant="transparent"
          border="none"
          size={['xlg']}
          icon={<EditIcon />}
          onClick={() => setShowEditContactForm(true)}
          height="auto"
        />
      ) : (
        <Button
          variant={['smooth', 'smooth', 'transparent']}
          onClick={() => setShowEditContactForm(true)}
          aria-label="Click here to open modal for editing contact details"
          size={['xlg', 'xlg', 'md']}
        >
          {t(labelEditButton)}
        </Button>
      )}

      {showEditContactForm && (
        <Formik
          innerRef={formRef}
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={() => handleOnSave()}
        >
          {(formikProps) => {
            return (
              <DeviceAdapter
                size="xl"
                isOpen={true}
                acceptTitle={t('update') || undefined}
                isLoading={isUpdatingContactLoading}
                onAccept={() => handleOnSave()}
                onClose={() => onClose()}
                onReject={() => onClose()}
                title={getTitleByStep(formikProps.values.type)}
                dirty={formikProps.dirty}
                isSubmitting={formikProps.isSubmitting}
              >
                <ContactFormV2
                  {...formikProps}
                  step={'details'}
                  tags={tags}
                  usersPaginationProps={usersPaginationProps}
                  preUsersOptions={contactData.assignedToUsers.map((user) => ({
                    id: user.id,
                    name: getFullName(user),
                  }))}
                />
              </DeviceAdapter>
            )
          }}
        </Formik>
      )}
    </>
  )
}
