import { Stack, useBreakpointValue, useToast } from '@chakra-ui/react'
import { EnumBadgeGroupField } from '../Badge/BadgeGroupFieldWrappers'
import { FieldArray, Formik, FormikProps } from 'formik'
import { DeviceAdapter } from '../ActionDrawer/DeviceAdapter'
import { useRef, useState } from 'react'
import { CreateContactV2Props, emptyCreateContact } from '@/types/contact'
import { useDecodedSessionToken } from '@/hooks/useDecodedSessionToken'
import SourceContactInput from './SourceContactInput'
import {
  ContactRelatedPartyType,
  ContactType,
  SchemaContactListRead,
  SchemaContactRead,
  SchemaContactRelatedPartyCreate,
} from '@/generated-types/api'
import { InputWithLabel } from '../Form/Input'
import { useTranslation } from 'next-i18next'
import createValidationSchema from './ContactSchema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createContactV2 } from '@/queries/contactV2'
import { useTags } from '@/queries/tags'
import { Option } from '@/types/common'
import ContactClientType from './ClientType'
import Tags from './Tags'
import LabeledField from '../Form/LabeledField'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getUsers } from '@/queries/users'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import { getFullName, UserListRead } from '@/types/users'
import FiCustomerSelect from '../FICustomerSelect'
import { HeaderRow } from '../HeaderRow/HeaderRow'
import { ContactNewsletterFields } from './ContactNewsletterFields'
import { FeatureFlag } from '../FeatureFlag'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { get } from 'lodash'
import { getToastProps } from '@/utils/toastProps'

export type ContactFormStep = 'details' | 'type'

export type RelatedPartiesInputFields = {
  signingRightIds: number[]
  beneficiaryIds: number[]
  partyIds: number[]
  authorizedPartyIds: number[]
}

type FormikFormProps = CreateContactV2Props &
  RelatedPartiesInputFields & {
    id?: number
  }

export type FormProps = FormikProps<FormikFormProps> & {
  tags: Option[]
  usersPaginationProps: PaginationProps<UserListRead>
  preUsersOptions: { id: number; name: string }[]
}

const PersonDetailsForm = (props: FormProps) => {
  const { values, errors, touched } = props
  const { t } = useTranslation()
  const isMobile = useBreakpointValue({ base: true, md: false }) || false

  return (
    <Stack gap="1rem" pb={20}>
      <ContactClientType tags={props.tags} />
      <HeaderRow title={t('createContact.contactInfo')} />

      <ContactDetailsFields {...props} />
      <ContactAddressFields {...props} />

      <InputWithLabel
        name="socialSecurityNumber"
        label={t('socialSecurityNumber')}
        defaultValue={values.socialSecurityNumber}
        error={errors.socialSecurityNumber}
        touched={touched.socialSecurityNumber}
        w={isMobile ? '100%' : '50%'}
      />

      {/* <BooleanBadgeGroupField
        name="consentNewsletterMarketing"
        label={t('createContact.newsLetterMarketingConsent')}
        toggle
        onValueChanged={(value) =>
          setFieldValue('consentNewsletterMarketing', value || false)
        }
      />
      <BooleanBadgeGroupField
        name="consentEmailMarketing"
        label={t('createContact.emailMarketingConsent')}
        toggle
        onValueChanged={(value) =>
          setFieldValue('consentEmailMarketing', value || false)
        }
      />  */}
      <FeatureFlag featureFlag="marketingConsent">
        <HeaderRow title={t('createContact.marketingSettings')} />

        <ContactNewsletterFields {...props} />
      </FeatureFlag>
      <SystemInfoSection
        tags={props.tags}
        usersPaginationProps={props.usersPaginationProps}
        preUsersOptions={props.preUsersOptions}
      />
    </Stack>
  )
}

const EstateDetailsForm = (props: FormProps) => {
  const { values, errors, touched } = props
  const { t } = useTranslation()

  let filterContactFn
  if (values.id) {
    filterContactFn = (contact) => contact.id !== values.id
  }

  return (
    <Stack gap="1rem" pb={20}>
      <ContactClientType tags={props.tags} />
      <HeaderRow title={t('estateInfo')} />
      <InputWithLabel
        label={t('createContact.estateName')}
        name="name"
        defaultValue={values.name}
        error={errors.name}
        touched={touched.name}
        required
      />
      <ContactAddressFields {...props} />

      <HeaderRow title={t('contactPersoninfo')} />
      <ContactDetailsFields {...props} />

      <HeaderRow title={t('createContact.beneficiaries')} />
      <FiCustomerSelect
        name="partyIds"
        label={t('createContact.addPerson')}
        querySource="contacts"
        filterContactFn={filterContactFn}
      />
      <HeaderRow title={t('createContact.signer')} />
      <FiCustomerSelect
        name="authorizedPartyIds"
        label={t('createContact.addPerson')}
        querySource="contacts"
        filterContactFn={filterContactFn}
      />

      <SystemInfoSection
        tags={props.tags}
        usersPaginationProps={props.usersPaginationProps}
        preUsersOptions={props.preUsersOptions}
      />
    </Stack>
  )
}

export const CompanyDetailsForm = (props: FormProps) => {
  const { values, errors, touched } = props
  const { t } = useTranslation()
  const isMobile = useBreakpointValue({ base: true, md: false }) || false

  let filterContactFn
  if (values.id) {
    filterContactFn = (contact) => contact.id !== values.id
  }

  return (
    <Stack gap="1rem">
      <ContactClientType tags={props.tags} />
      <HeaderRow title={t('companyInfo')} />
      <Stack direction={isMobile ? 'column' : 'row'} gap="1rem">
        <InputWithLabel
          name="name"
          label={t('editUser.companyName')}
          defaultValue={values.name}
          error={errors.name}
          touched={touched.name}
          required
        />
        <InputWithLabel
          name="businessId"
          label={t('businessId')}
          defaultValue={values.businessId || ''}
          error={errors.businessId}
          touched={touched.businessId}
          required
        />
      </Stack>
      <ContactAddressFields {...props} />

      <HeaderRow title={t('contactPersoninfo')} />
      <ContactDetailsFields {...props} />

      <HeaderRow title={t('createContact.beneficiaries')} />
      <FiCustomerSelect
        name="beneficiaryIds"
        label={t('createContact.addPerson')}
        querySource="contacts"
        filterContactFn={filterContactFn}
      />
      <HeaderRow title={t('createContact.signingRights')} />
      <FiCustomerSelect
        name="signingRightIds"
        label={t('createContact.addPerson')}
        querySource="contacts"
        filterContactFn={filterContactFn}
      />
      <SystemInfoSection
        tags={props.tags}
        usersPaginationProps={props.usersPaginationProps}
        preUsersOptions={props.preUsersOptions}
      />
    </Stack>
  )
}

export const ContactFormV2 = (
  props: FormikProps<FormikFormProps> & {
    step: ContactFormStep
    tags: Option[]
    usersPaginationProps: PaginationProps<UserListRead>
    preUsersOptions: { id: number; name: string }[]
  }
) => {
  const { t } = useTranslation()

  const { step, values, tags, usersPaginationProps, preUsersOptions } = props

  switch (step) {
    case 'type':
      return (
        <Stack gap="1rem">
          <EnumBadgeGroupField
            name="type"
            enum={ContactType}
            label={t`Type`}
            itemLabelPrefix="enums.contactType"
            required={true}
          />
          <SourceContactInput label={t`leadSource`} required={true} />
        </Stack>
      )
    case 'details':
      switch (values.type) {
        case ContactType.Person:
          return (
            <PersonDetailsForm
              {...props}
              tags={tags}
              usersPaginationProps={usersPaginationProps}
              preUsersOptions={preUsersOptions}
            />
          )
        case ContactType.Estate:
          return (
            <EstateDetailsForm
              {...props}
              tags={tags}
              usersPaginationProps={usersPaginationProps}
              preUsersOptions={preUsersOptions}
            />
          )
        case ContactType.Organization:
          return (
            <CompanyDetailsForm
              {...props}
              tags={tags}
              usersPaginationProps={usersPaginationProps}
              preUsersOptions={preUsersOptions}
            />
          )
        default:
          return (
            <PersonDetailsForm
              {...props}
              tags={tags}
              usersPaginationProps={usersPaginationProps}
              preUsersOptions={preUsersOptions}
            />
          )
      }
    default:
      return null
  }
}

export const CreateContactV2 = ({
  onSuccess,
  onClose,
}: {
  onClose: (contact?: SchemaContactListRead) => void
  onSuccess?: (contact: SchemaContactListRead) => void
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false })
  const { organization, user: currentUser } = useUserAndOrganization()

  const queryClient = useQueryClient()
  const { t } = useTranslation()
  const toast = useToast()
  const [step, setStep] = useState<ContactFormStep>('type')
  const formRef =
    useRef<FormikProps<CreateContactV2Props & RelatedPartiesInputFields>>(null)
  const decodedToken = useDecodedSessionToken()

  const acceptTitle = {
    type: t('next'),
    details: t('create'),
  }

  const { data: tags = [] } = useTags()

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        onlyActive: true,
        organizationId: organization?.id,
      }),
  })

  const { mutate: createContact } = useMutation({
    mutationFn: createContactV2,
    onSuccess: async (contactCreated: SchemaContactListRead) => {
      await queryClient.invalidateQueries({ queryKey: ['contacts'] })
      await queryClient.invalidateQueries({
        queryKey: ['contact', contactCreated.id.toString()],
      })
      await queryClient.invalidateQueries({ queryKey: ['tags'] })

      // Explicitly refetch the queries
      await queryClient.refetchQueries({ queryKey: ['contacts'] })
      await queryClient.refetchQueries({
        queryKey: ['contact', contactCreated.id.toString()],
      })
      await queryClient.refetchQueries({ queryKey: ['tags'] })

      if (onSuccess) {
        onSuccess(contactCreated)
      } else {
        toast(
          getToastProps({
            title: t('createContact.createdTitle'),
            description: `${t('createContact.createdDescription')} "${
              contactCreated?.name
            }"`,
            status: 'success',
            isMobile,
            variant: 'customSuccess',
          })
        )
        onClose(contactCreated)
      }
    },
    onError: (e) => {
      const description = get(e, 'response.data.error')
      const finalDescription =
        description === 'tag-already-exists'
          ? t('tagAlreadyExists')
          : description || t('errors.contactNotUpdated')

      toast(
        getToastProps({
          title: t('createContact.error'),
          description: finalDescription,
          status: 'error',
          isMobile,
        })
      )
    },
  })

  const getTitleByStep = (
    step: ContactFormStep,
    type: ContactType | null
  ): string => {
    if (step === 'type') return t('createContact.title')

    switch (type) {
      case ContactType.Person:
        return t('createContact.title')
      case ContactType.Organization:
        return t('createContact.createCompany')
      case ContactType.Estate:
        return t('createContact.createEstate')
      default:
        return t('createContact.title')
    }
  }

  const handleOnSave = async () => {
    if (!formRef.current) {
      return
    }
    if (step === 'type') {
      await formRef.current.validateField('source')
      await formRef.current.validateField('type')
      await formRef.current.setFieldTouched('source', true)
      await formRef.current.setFieldTouched('type', true)

      const errors = formRef.current.errors
      if (!errors.source && !errors.type) {
        setStep('details')
      }
      return
    } else {
      const validation = await formRef.current.validateForm()
      if (Object.keys(validation).length > 0) {
        formRef.current.setErrors(validation)
        await formRef.current.setTouched(
          Object.keys(validation).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {})
        )
        toast(
          getToastProps({
            title: t('createContact.validation.errorToast'),
            status: 'error',
            isMobile,
          })
        )
        return
      }
    }

    const tags = [
      // These are the system tags
      ...formRef.current.values.tags,
      // These are the user created tags
      ...formRef.current.values.listTags,
    ]

    // Convert tags to newTags and existingTags
    const createContactPayload = {
      ...formRef.current.values,
      newTags: tags
        .filter((tag) => typeof tag.value === 'string')
        .map((tag) => tag.value.toString()),
      existingTags: tags
        .filter((tag) => typeof tag.value === 'number')
        .map((tag) => Number(tag.value)),
      phoneNumbers: formRef.current.values.phoneNumbers || [],
      assignedTo: formRef.current.values.assignedTo || [],
      relatedParties: getRelatedParties(formRef.current.values),
    } satisfies CreateContactV2Props
    createContact(createContactPayload)
  }

  const initialValues: CreateContactV2Props & RelatedPartiesInputFields = {
    ...emptyCreateContact,
    assignedTo: decodedToken?.sub ? [decodedToken?.sub] : [],
    relatedParties: [],
    signingRightIds: [],
    beneficiaryIds: [],
    partyIds: [],
    authorizedPartyIds: [],
    listTags: [],
  }

  // Create the validation schema with translation
  const validationSchema = createValidationSchema(t)

  return (
    <Formik
      innerRef={formRef}
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={() => handleOnSave()}
    >
      {(formikProps) => {
        return (
          <DeviceAdapter
            size="xl"
            isOpen={true}
            acceptTitle={acceptTitle[step]}
            onAccept={() => handleOnSave()}
            onClose={onClose}
            onReject={onClose}
            title={getTitleByStep(step, formikProps.values.type)}
            dirty={formikProps.dirty}
            isSubmitting={formikProps.isSubmitting}
          >
            <ContactFormV2
              {...formikProps}
              step={step}
              tags={tags}
              usersPaginationProps={usersPaginationProps}
              preUsersOptions={
                currentUser
                  ? [{ id: currentUser.id, name: getFullName(currentUser) }]
                  : []
              }
            />
          </DeviceAdapter>
        )
      }}
    </Formik>
  )
}

const ContactAddressFields = ({ values, errors, touched }: FormProps) => {
  const { t } = useTranslation()
  const isMobile = useBreakpointValue({ base: true, md: false }) || false
  return (
    <>
      <Stack direction={isMobile ? 'column' : 'row'} gap="1rem">
        <InputWithLabel
          label={t('streetAddress')}
          name="address"
          defaultValue={values.address}
          error={errors.address}
          touched={touched.address}
        />
      </Stack>
      <Stack direction={isMobile ? 'column' : 'row'} gap="1rem">
        <InputWithLabel
          label={t('postalCode')}
          name="postCode"
          defaultValue={values.postCode}
          error={errors.postCode}
          touched={touched.postCode}
        />
        <InputWithLabel
          label={t('city')}
          name="city"
          defaultValue={values.city}
          error={errors.city}
          touched={touched.city}
        />
      </Stack>
    </>
  )
}

const ContactDetailsFields = ({
  setFieldValue,
  errors,
  touched,
  values,
}: FormProps) => {
  const { t } = useTranslation()
  const isMobile = useBreakpointValue({ base: true, md: false }) || false

  return (
    <>
      <Stack direction={isMobile ? 'column' : 'row'} gap="1rem">
        <InputWithLabel
          label={t('createContact.firstName')}
          name="firstName"
          defaultValue={values.firstName}
          error={errors.firstName}
          touched={touched.firstName}
          required
        />
        <InputWithLabel
          label={t('createContact.lastName')}
          name="lastName"
          defaultValue={values.lastName}
          error={errors.lastName}
          touched={touched.lastName}
          required
        />
      </Stack>
      <Stack direction={isMobile ? 'column' : 'row'} gap="1rem">
        <FieldArray
          name="phoneNumbers"
          render={(_arrayHelpers) => (
            <InputWithLabel
              defaultValue={values.phoneNumbers?.[0]}
              label={t('phone')}
              placeholder={t('phone') ?? undefined}
              name={`phoneNumbers.${0}`}
              type="text"
              onChange={(e) => {
                const value = e.target.value
                setFieldValue(`phoneNumbers.${0}`, value)
              }}
              required
              error={errors.phoneNumbers?.toString()}
              touched={touched.phoneNumbers}
            />
          )}
        />
        <InputWithLabel
          label={t('createContact.email')}
          name="email"
          defaultValue={values.email}
          error={errors.email}
          touched={touched.email}
          required
        />
      </Stack>
    </>
  )
}

const SystemInfoSection = ({
  tags,
  usersPaginationProps,
  preUsersOptions,
}: {
  tags: Option[]
  usersPaginationProps: PaginationProps<UserListRead>
  preUsersOptions: { id: number; name: string }[]
}) => {
  const { t } = useTranslation()
  return (
    <>
      <HeaderRow title={t('createContact.systemInfo')} />
      <Stack gap="1rem">
        <LabeledField label={t('assignedTo')} required={true}>
          <MultiSelectDropdown
            name="assignedTo"
            placement="bottomStart"
            valueKey="id"
            labelKey="name"
            preOptions={preUsersOptions}
            {...usersPaginationProps}
            data={usersPaginationProps.data.map((user) => ({
              ...user,
              name: getFullName(user),
            }))}
          />
        </LabeledField>
        <SourceContactInput label={`${t('source')}`} required={true} />
        <Tags tags={tags} isLoadingTags={false} />
      </Stack>
    </>
  )
}

export const getRelatedParties = (
  values: FormikFormProps
): SchemaContactRelatedPartyCreate[] | null => {
  switch (values.type) {
    case ContactType.Person:
      return null
    case ContactType.Organization: {
      const beneficiaries = values.beneficiaryIds.map((id) => ({
        id,
        type: ContactRelatedPartyType.company_beneficiary,
      }))
      const signingRights = values.signingRightIds.map((id) => ({
        id,
        type: ContactRelatedPartyType.company_signing_rights,
      }))
      return [...beneficiaries, ...signingRights]
    }
    case ContactType.Estate: {
      const partys = values.partyIds.map((id) => ({
        id,
        type: ContactRelatedPartyType.estate_party,
      }))
      const authorized = values.authorizedPartyIds.map((id) => ({
        id,
        type: ContactRelatedPartyType.estate_authorized_party,
      }))
      return [...partys, ...authorized]
    }
    default:
      return null
  }
}

export const getRelatedPartiesInputFields = (
  values: SchemaContactRead
): RelatedPartiesInputFields => {
  //TODO: we need the type in the related parties to be able to map the right ones
  switch (values.type) {
    case ContactType.Person:
      return {
        signingRightIds: [],
        beneficiaryIds: [],
        partyIds: [],
        authorizedPartyIds: [],
      }
    case ContactType.Organization: {
      return {
        beneficiaryIds: values.relatedParties
          .filter(
            (row) => row.type === ContactRelatedPartyType.company_beneficiary
          )
          .map((row) => row.id),
        signingRightIds: values.relatedParties
          .filter(
            (row) => row.type === ContactRelatedPartyType.company_signing_rights
          )
          .map((row) => row.id),
        partyIds: [],
        authorizedPartyIds: [],
      }
    }
    case ContactType.Estate: {
      return {
        signingRightIds: [],
        beneficiaryIds: [],
        partyIds: values.relatedParties
          .filter((row) => row.type === ContactRelatedPartyType.estate_party)
          .map((row) => row.id),
        authorizedPartyIds: values.relatedParties
          .filter(
            (row) =>
              row.type === ContactRelatedPartyType.estate_authorized_party
          )
          .map((row) => row.id),
      }
    }
    default:
      return {
        signingRightIds: [],
        beneficiaryIds: [],
        partyIds: [],
        authorizedPartyIds: [],
      }
  }
}
