import React, { useCallback, useMemo } from 'react'
import { Flex, Heading, Radio, Text } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useFormikContext, ErrorMessage } from 'formik'
import { useQuery } from '@tanstack/react-query'
import { PROPERTY_DROPDOWN_ITEM_HEIGHT } from '@/utils/constants'
import { PropertyDropdownItem } from '@/components/PropertyDropdownItem'
import { SimpleDropdown } from '@/components/DropdownMenu/SimpleDropdown'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { getFIProperties } from '@/modules/fi-properties/queries/queryFIProperties'
import { getProperties } from '@/queries/property'
import { FIPropertyListRead } from '@/modules/fi-properties/types/FIProperty'
import { ListProperties, StatusEnum } from '@/types/property'
import { getEvents } from '@/queries/events'
import { EventRead } from '@/types/event'
import { AD_TYPE, AdvertisementCreate } from '@/types/advertisements'
import { useAuth } from '@/hooks/useAuth'
import { Status as FIStatus } from '@/modules/fi-properties/types/Common'
import { useIsFinland } from '@/hooks/useIsFinland'

type PropertyType = FIPropertyListRead & ListProperties

type AdTypeOption = {
  label: string
  iconName: string
  subText: string
  value: AD_TYPE
  isActive?: boolean
}

interface AdTypeOptionProps {
  value: AdTypeOption
  isActive?: boolean
  onClick?: () => void
}

const AdTypeEntitySelect = ({ type }: { type: AD_TYPE }) => {
  const { t } = useTranslation(['common'])
  const { values, setFieldValue } = useFormikContext<AdvertisementCreate>()
  const isFinland = useIsFinland() ?? false

  const formatProperties = useCallback(
    (properties: PropertyType[]) =>
      isFinland
        ? properties.map((property) => ({
            ...property,
            label: [
              property.fiRealty?.fiAddress?.streetAddress,
              property.fiRealty?.fiAddress?.district,
              property.fiRealty?.fiAddress?.municipality,
            ]
              .filter(Boolean)
              .join(' '),
          }))
        : properties.map((property) => ({
            ...property,
            label: property.reference,
          })),
    [isFinland]
  )

  const getStatusFilter = (isFinnishProperty: boolean, adType: AD_TYPE) => {
    const { PUBLISHED, SOLD } = isFinnishProperty ? FIStatus : StatusEnum
    const allStatuses = Object.values(isFinnishProperty ? FIStatus : StatusEnum)

    const statusMap = {
      [AD_TYPE.LISTING_PROPERTY]: [PUBLISHED],
      [AD_TYPE.PROPERTY_SOLD]: [SOLD],
      [AD_TYPE.CUSTOM]: allStatuses,
    }

    return statusMap[adType] || [PUBLISHED]
  }

  const queryFn = useCallback(
    (params) => {
      const statusFilter = getStatusFilter(isFinland, type)
      const queryParams = { ...params, status: statusFilter }

      return (isFinland ? getFIProperties : getProperties)(queryParams).then(
        (data) => ({
          records: formatProperties(data.records as PropertyType[]),
          metadata: data.metadata,
        })
      )
    },
    [isFinland, formatProperties, type]
  )

  const { paginationProps } = usePaginationDropdownData<PropertyType>({
    queryKey: ['properties', isFinland, type],
    queryFn: async (params) => {
      const response = await queryFn(params)
      return response
    },
  })

  const preOptions = useMemo(
    () => paginationProps.data ?? [],
    [paginationProps.data]
  )

  const { data: eventsResponse } = useQuery({
    enabled: !!values.propertyId,
    queryFn: () => getEvents({ propertyIds: `${values.propertyId}` }),
    queryKey: ['events', values.propertyId],
  })
  const eventsFiltered = useMemo(() => eventsResponse || [], [eventsResponse])
  const setProperty = (propertyId) => {
    setFieldValue('propertyId', propertyId)
    setFieldValue('eventId', null)
    setFieldValue('event', null)
    const selectedProperty = preOptions.find(
      (property) => property.id === propertyId
    )
    if (selectedProperty) {
      setFieldValue('property', selectedProperty)
    }
  }
  const setEvent = (event: EventRead) => {
    setFieldValue('eventId', event.id)
    setFieldValue('event', event)
  }
  if (type === AD_TYPE.AGENT) return null
  return (
    <>
      <Heading variant="H1" mt="10px" mb="10px">
        {t('adsManager.modal.selectProperty')}
        {type === AD_TYPE.CUSTOM && ` (${t('optional')})`}
      </Heading>
      <Flex flexDirection="column" width="100%">
        <SimpleDropdown
          name="propertyId"
          dataTestid="property-id-dropdown"
          valueKey="id"
          labelKey="label"
          cleanable
          placement="bottomStart"
          renderMenuItem={PropertyDropdownItem}
          itemSize={PROPERTY_DROPDOWN_ITEM_HEIGHT}
          value={values.propertyId}
          preOptions={preOptions}
          {...paginationProps}
          onSelect={(selectedPropertyId) => setProperty(selectedPropertyId)}
        />
        {type === AD_TYPE.EVENT && values.propertyId && (
          <Flex flexDirection="column" mt="20px">
            <Heading variant="H1" mb="10px">
              {t('adsManager.modal.selectEvent')}
            </Heading>
            {eventsFiltered.length > 0 ? (
              <Flex gap="10px" flexDirection="column">
                {eventsFiltered.map((event) => (
                  <Flex
                    key={event.id}
                    gap="10px"
                    cursor="pointer"
                    onClick={() => setEvent(event)}
                  >
                    <Radio
                      isChecked={String(values.eventId) === String(event.id)}
                      onChange={() => setEvent(event)}
                      pointerEvents="auto"
                    />
                    <Heading variant="H1" fontSize="15px">
                      {event.title}
                    </Heading>
                  </Flex>
                ))}
              </Flex>
            ) : (
              <Text color="red.500" fontSize="sm">
                {t('adsManager.modal.errors.noEventsAvailable')}
              </Text>
            )}
            <ErrorMessage name="eventId">
              {(msg) => (
                <Text color="red.500" fontSize="sm" mt="2">
                  {msg}
                </Text>
              )}
            </ErrorMessage>
          </Flex>
        )}
      </Flex>
      <ErrorMessage name="type">
        {(msg) => (
          <Text color="red.500" fontSize="sm" mt="2">
            {msg}
          </Text>
        )}
      </ErrorMessage>
    </>
  )
}

const AdTypeOption = ({
  value: { label, iconName, subText },
  isActive = false,
  onClick,
}: AdTypeOptionProps) => (
  <Flex
    w="100%"
    borderWidth="1px"
    borderStyle="solid"
    borderColor={isActive ? 'common.black' : 'grays.grayDivider'}
    borderRadius="10px"
    bg="common.white"
    p="12px 8px 12px 16px"
    alignItems="center"
    gap="16px"
    alignSelf="stretch"
    cursor="pointer"
    onClick={onClick}
  >
    <Flex
      justifyContent="center"
      alignItems="center"
      w="40px"
      h="40px"
      p="12px"
      borderRadius="40px"
      bg="primary.main"
      gap="10px"
    >
      <span className="material-symbols-outlined" style={{ fontSize: '26px' }}>
        {iconName}
      </span>
    </Flex>
    <Flex flexDirection="column" flex="1" alignItems="flex-start">
      <Heading
        variant="H1"
        fontSize="15px"
        fontWeight="800"
        fontStyle="normal"
        lineHeight="normal"
        fontFamily="Avenir"
      >
        {label}
      </Heading>
      <p>{subText}</p>
    </Flex>
    <Radio isChecked={isActive} pointerEvents="none" />
  </Flex>
)

const AdStepSelectType: React.FC = () => {
  const { t } = useTranslation(['common'])
  const { values, setFieldValue } = useFormikContext<AdvertisementCreate>()
  const { currentUser } = useAuth().authStates
  const typeOptions: AdTypeOption[] = useMemo(
    () => [
      {
        label: t('adsManager.modal.type.listingProperty.label'),
        iconName: 'holiday_village',
        subText: t('adsManager.modal.type.listingProperty.subText'),
        value: AD_TYPE.LISTING_PROPERTY,
      },
      {
        label: t('adsManager.modal.type.agent.label'),
        iconName: 'account_circle',
        subText: t('adsManager.modal.type.agent.subText'),
        value: AD_TYPE.AGENT,
      },
      {
        label: t('adsManager.modal.type.event.label'),
        iconName: 'calendar_month',
        subText: t('adsManager.modal.type.event.subText'),
        value: AD_TYPE.EVENT,
      },
      {
        label: t('adsManager.modal.type.propertySold.label'),
        iconName: 'handshake',
        subText: t('adsManager.modal.type.propertySold.subText'),
        value: AD_TYPE.PROPERTY_SOLD,
      },
      {
        label: t('adsManager.modal.type.custom.label'),
        iconName: 'more_horiz',
        subText: t('adsManager.modal.type.custom.subText'),
        value: AD_TYPE.CUSTOM,
      },
    ],
    [t]
  )
  const handleTypeChange = (optionValue: AD_TYPE) => {
    setFieldValue('type', optionValue)
    setFieldValue('propertyId', null)
    setFieldValue('property', null)
    setFieldValue('eventId', null)
    setFieldValue('event', null)
    setFieldValue('agentId', null)
    setFieldValue('advertisementImages', [])
    if (optionValue === AD_TYPE.AGENT && currentUser?.id) {
      setFieldValue('agentId', currentUser.id)
      if (currentUser.photoUrl)
        setFieldValue('advertisementImages', [
          {
            url: `${currentUser.photoUrl}${
              currentUser.photoUrl.includes('?') ? '&' : '?'
            }grayscale=true`,
          },
        ])
    }
  }
  return (
    <Flex
      w="100%"
      h="100%"
      gap="10px"
      flexDirection="column"
      justifyContent="center"
      alignItems="flex-start"
    >
      <Heading variant="H1">{t('adsManager.modal.adType')}</Heading>
      <Flex
        w="100%"
        gap="10px"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
      >
        {typeOptions.map((option, i) => {
          const isActive = values.type === option.value
          return (
            <AdTypeOption
              key={i}
              value={option}
              isActive={isActive}
              onClick={() => handleTypeChange(option.value)}
            />
          )
        })}
      </Flex>
      <AdTypeEntitySelect type={values.type} />
    </Flex>
  )
}

export default AdStepSelectType
