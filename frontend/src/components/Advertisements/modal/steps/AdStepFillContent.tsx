import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Button,
  Flex,
  Heading,
  Text,
  Textarea,
  Box,
  Card,
  CardBody,
  CardFooter,
  Checkbox,
  Badge,
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { ErrorMessage, useFormikContext } from 'formik'
import { InputWithLabel } from '@/components/Form/Input'
import { DeviceAdapter } from '@/components/ActionDrawer/DeviceAdapter'
import { IMAGE_SIZE_S_WIDTH } from '@/utils/constants'
import {
  DndContext,
  closestCenter,
  useSensors,
  useSensor,
  MouseSensor,
  TouchSensor,
  KeyboardSensor,
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  arrayMove,
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { ImageWithFallback } from '@/components/ImageWithFallback'
import LabeledField from '@/components/Form/LabeledField'
import { PropertyImage, PropertyImagesResponse } from '@/types/property'
import { getPropertyImages } from '@/queries/property'
import { useQuery } from '@tanstack/react-query'
import {
  AD_LANGUAGE,
  AD_TYPE,
  AdvertisementCreate,
  AdvertisementImage,
} from '@/types/advertisements'
import SelectDropdown from '@/components/DropdownMenu/SelectDropdown'
import { FacebookPlaceholder } from '../../FacebookPlaceholder'
import { UserRead } from '@/types/users'
import { getUser } from '@/queries/users'
import { SchemaAdTemplateRead } from '@/generated-types/api'
import { getAdTemplates } from '@/queries/organization'
import { SwiperSlide, Swiper, SwiperRef } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { Pagination } from 'swiper/modules'

const TemplateSelector = ({
  onSelect,
  adTemplateId,
}: {
  onSelect: (template: SchemaAdTemplateRead) => void
  adTemplateId: number | undefined
}) => {
  const { data: adTemplates = [], isPending } = useQuery<
    SchemaAdTemplateRead[]
  >({
    queryKey: ['adTemplates'],
    queryFn: () => getAdTemplates(),
  })
  const swiperRef = useRef<SwiperRef>(null)
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(
    adTemplateId || null
  )
  const handleClick = (template: SchemaAdTemplateRead) => {
    setSelectedTemplateId(template.id)
    onSelect(template)
  }

  if (isPending) return <></>

  return (
    <Box w="90%" mx="auto" position="relative">
      <Flex justifyContent={'right'}>
        <Button
          onClick={() => swiperRef.current?.swiper.slidePrev()}
          variant="transparent"
          p={0}
          borderColor="transparent"
          fontWeight={500}
          fontSize="16px"
        >
          <span
            style={{ fontSize: '30px' }}
            className="material-symbols-outlined"
          >
            chevron_left
          </span>
        </Button>
        <Button
          onClick={() => swiperRef.current?.swiper.slideNext()}
          p={0}
          variant="transparent"
          borderColor="transparent"
          fontWeight={500}
          fontSize="16px"
        >
          <span
            style={{ fontSize: '30px' }}
            className="material-symbols-outlined"
          >
            chevron_right
          </span>
        </Button>
      </Flex>
      <Swiper
        ref={swiperRef}
        modules={[Pagination]}
        slidesPerView={2.5}
        spaceBetween={16}
        pagination={{ clickable: true, type: 'bullets' }}
        style={{
          padding: '1rem 0 1rem 0',
          marginBottom: '4px',
          height: '130px',
        }}
      >
        <Flex>
          {adTemplates.map((tpl, index) => (
            <>
              <SwiperSlide key={tpl.id}>
                <Box
                  onClick={() => handleClick(tpl)}
                  cursor="pointer"
                  textAlign="center"
                  borderWidth={selectedTemplateId === tpl.id ? '3px' : '0'}
                  borderColor={
                    selectedTemplateId === tpl.id ? 'blue.500' : 'gray.200'
                  }
                >
                  <ImageWithFallback
                    src={tpl.imageUrl}
                    alt={`smartly template ${index}`}
                    fallback="/placeholder.png"
                    objectFit="contain"
                  />
                </Box>
              </SwiperSlide>
            </>
          ))}
        </Flex>
      </Swiper>
    </Box>
  )
}

const ImagePickerItem = ({
  item,
  onSelect,
  isSelected,
  isCover = false,
}: {
  item: PropertyImage
  onSelect: () => void
  isSelected: boolean
  isCover: boolean
}) => {
  const { t } = useTranslation(['common'])
  return (
    <Box>
      <Card
        width={['342px', '342px', '262px']}
        p="0"
        ml={[0, 0, '16px']}
        mt="16px"
        opacity={1}
      >
        <CardBody p={0} height={175} flex="0 0 auto" position="relative">
          <ImageWithFallback
            src={`${item.url}?width=${IMAGE_SIZE_S_WIDTH}`}
            alt="property image"
            fallback="/placeholder.png"
            objectFit="contain"
          />
          {isCover && (
            <Badge
              position="absolute"
              left="6px"
              bottom="6px"
              zIndex={3}
              variant="dark"
            >
              {t('mediaPage.coverPhoto')}
            </Badge>
          )}
        </CardBody>
        <CardFooter
          height="58px"
          borderTop="1px solid black"
          p="0 9px 0 16px"
          alignItems="center"
          justifyContent="space-between"
        >
          <Flex
            flexDirection="column"
            whiteSpace="nowrap"
            maxWidth="calc(100% - 60px)"
          >
            <Text
              variant="small"
              color="grays.gray7"
              textOverflow="ellipsis"
              overflow="hidden"
            >
              {item.uploadedDate?.toLocaleDateString('en-GB') || '-'}
            </Text>
            <Text
              variant="small"
              mt={0}
              textOverflow="ellipsis"
              overflow="hidden"
            >
              {item.author
                ? t('mediaPage.uploadedBy', { author: item.author })
                : ''}
            </Text>
          </Flex>
          <Checkbox
            colorScheme="checkboxColor"
            isChecked={isSelected}
            onChange={onSelect}
            size="lg"
          />
        </CardFooter>
      </Card>
    </Box>
  )
}

const SortableImagePickerItem: React.FC<{
  image: PropertyImage
  isSelected: boolean
  onSelect: () => void
  isCover: boolean
}> = ({ image, isSelected, onSelect, isCover }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: image.id.toString(),
    })
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }
  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <ImagePickerItem
        item={image}
        isSelected={isSelected}
        onSelect={onSelect}
        isCover={isCover}
      />
    </div>
  )
}

const ImagePicker = ({
  onClose,
  isOpen = false,
  imageOptions = [],
}: {
  isOpen: boolean
  onClose: () => void
  imageOptions: PropertyImagesResponse
}) => {
  const { t } = useTranslation(['common'])
  const { values, setFieldValue } = useFormikContext<AdvertisementCreate>()
  const [selectedImages, setSelectedImages] = useState<AdvertisementImage[]>(
    values.advertisementImages || []
  )
  const [images, setImages] = useState(imageOptions)

  useEffect(() => {
    setImages(imageOptions)
  }, [imageOptions])

  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor, {
      activationConstraint: { delay: 500, tolerance: 8 },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const selectImage = (img: AdvertisementImage) => {
    setSelectedImages((prevImages) =>
      prevImages.includes(img)
        ? prevImages.filter(({ id }: { id: number }) => id !== img.id)
        : [...prevImages, img]
    )
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      setImages((currentImages) => {
        const selected = currentImages.filter((img) =>
          selectedImages.includes(img)
        )
        const unselected = currentImages.filter(
          (img) => !selectedImages.includes(img)
        )
        return [...selected, ...unselected]
      })
    }, 300)
    return () => clearTimeout(timer)
  }, [selectedImages])

  const handleDragEnd = (event) => {
    const { active, over } = event
    if (over && active.id !== over.id) {
      setImages((items) => {
        const oldIndex = items.findIndex((i) => i.id.toString() === active.id)
        const newIndex = items.findIndex((i) => i.id.toString() === over.id)
        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  const handleClose = () => {
    setSelectedImages([])
    onClose()
  }

  const setImagesInFormik = () => {
    setFieldValue('advertisementImages', selectedImages)
    onClose()
  }

  const coverImageId = useMemo(() => {
    return images.find((img) => selectedImages.includes(img))?.id
  }, [images, selectedImages])

  return (
    <DeviceAdapter
      size="full"
      isOpen={isOpen}
      title={t('adsManager.selectPictures')}
      onAccept={setImagesInFormik}
      onClose={handleClose}
      onReject={handleClose}
      acceptTitle={
        t('adsManager.modal.imagesSelected', {
          number: selectedImages.length,
        }) || ''
      }
      cancelTitle={t('cancel') || ''}
      modalBodyProps={{ backgroundColor: 'primary.main', padding: '2px' }}
    >
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <Flex flexWrap="wrap" justifyContent="left" gap={2}>
          <SortableContext items={images.map((img) => img.id.toString())}>
            {images.map((image) => (
              <SortableImagePickerItem
                key={image.id}
                image={image}
                isSelected={selectedImages.includes(image)}
                onSelect={() => selectImage(image)}
                isCover={image.id === coverImageId}
              />
            ))}
          </SortableContext>
        </Flex>
      </DndContext>
    </DeviceAdapter>
  )
}

const AdStepFillContent: React.FC = () => {
  const { t } = useTranslation(['common'])
  const { values, setFieldValue } = useFormikContext<AdvertisementCreate>()
  const [imagePickerIsOpen, setImagePickerIsOpen] = useState(false)
  const [images, setImages] = useState<PropertyImagesResponse>([])
  const { data: imageOptions } = useQuery({
    queryKey: ['media', values.propertyId],

    queryFn: () =>
      values.propertyId
        ? getPropertyImages(values.property?.reference || '')
        : [],

    initialData: [],
    enabled: !!values.propertyId,
  })
  useEffect(() => {
    setImages(imageOptions)
  }, [imageOptions])

  const languageOptions = Object.entries(AD_LANGUAGE).map(([_, value]) => ({
    value,
    label: t(`adsManager.languages.${value}`),
  }))

  const setTemplate = (template: SchemaAdTemplateRead) => {
    setFieldValue('adTemplateId', template.id)
    setFieldValue('advertisementImages', [
      { url: template.imageUrl, isHidden: false },
    ])
  }

  const { data: agent } = useQuery<UserRead>({
    queryKey: ['user', values.agentId],
    queryFn: () => getUser(String(values.agentId)),
    enabled: values.type === AD_TYPE.AGENT && Boolean(values.agentId),
  })

  useEffect(() => {
    if (agent?.photoUrl) {
      setFieldValue('photoUrl', { id: 0, url: agent.photoUrl })
      setFieldValue('advertisementImages', [
        {
          url: `${agent.photoUrl}${
            agent.photoUrl.includes('?') ? '&' : '?'
          }grayscale=true`,
          isHidden: false,
        },
      ])
    }
  }, [agent, setFieldValue])
  return (
    <>
      <ImagePicker
        isOpen={imagePickerIsOpen}
        onClose={() => setImagePickerIsOpen(false)}
        imageOptions={images}
      />
      <Flex
        w="100%"
        minH="200px"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        backgroundColor="primary.main"
      >
        <Flex paddingY={6}>
          <FacebookPlaceholder
            primaryText={values.primaryText}
            headline={values.title}
            description={values.description || ''}
            imageUrl={`${values?.advertisementImages?.[0]?.url}?width=${IMAGE_SIZE_S_WIDTH}&height=${IMAGE_SIZE_S_WIDTH}`}
            isAgentPhoto={values.type === AD_TYPE.AGENT}
          />
        </Flex>
      </Flex>
      <Flex
        w="100%"
        px="8"
        mt="4"
        gap="4"
        flexDirection="column"
        justifyContent="center"
      >
        <Heading size="md">{t('adsManager.modal.fillContent.title')}</Heading>
        {![AD_TYPE.AGENT, AD_TYPE.CUSTOM].includes(values.type) && (
          <Flex gap={2} mt={2}>
            <Button
              variant="bgwhiteBorderblack"
              width="88px"
              onClick={() => setImagePickerIsOpen(true)}
            >
              <span
                className="material-symbols-outlined"
                style={{
                  fontSize: '20px',
                  paddingRight: '6px',
                  fontWeight: '400',
                }}
              >
                imagesmode
              </span>
              <Text fontSize="12px" letterSpacing="0.1em" fontWeight="600">
                ({values.advertisementImages.length})
              </Text>
            </Button>
          </Flex>
        )}
        {values.type === AD_TYPE.CUSTOM && (
          <>
            <TemplateSelector
              onSelect={setTemplate}
              adTemplateId={values.adTemplateId}
            />
          </>
        )}
        <Flex flexDirection="column">
          <LabeledField
            label={t('adsManager.modal.runData.language')}
            width="100%"
          >
            <SelectDropdown
              valueKey="value"
              labelKey="label"
              data={languageOptions}
              onChange={(language) => setFieldValue('language', language)}
              value={values.language}
            />
          </LabeledField>
          <ErrorMessage name="language">
            {(msg) => (
              <Text color="red.500" fontSize="sm" mt="2">
                {msg}
              </Text>
            )}
          </ErrorMessage>
        </Flex>
        <Flex flexDirection="column">
          <InputWithLabel
            name="title"
            type="text"
            label={t('adsManager.modal.fillContent.headline', 'Headline')}
            value={values.title || ''}
          />
          <ErrorMessage name="title">
            {(msg) => (
              <Text color="red.500" fontSize="sm" mt="2">
                {msg}
              </Text>
            )}
          </ErrorMessage>
        </Flex>

        <Flex flexDirection="column">
          <InputWithLabel
            name="description"
            type="text"
            label={t('adsManager.modal.fillContent.description', 'Description')}
            value={values.description || ''}
          />
          <ErrorMessage name="description">
            {(msg) => (
              <Text color="red.500" fontSize="sm" mt="2">
                {msg}
              </Text>
            )}
          </ErrorMessage>
        </Flex>
        <Flex flexDirection="column">
          <LabeledField
            label={t(
              'adsManager.modal.fillContent.primaryText',
              'Primary text'
            )}
            width="100%"
          >
            <Textarea
              width="100%"
              size="xs"
              whiteSpace="pre-wrap"
              overflowWrap="break-word"
              placeholder={
                t('adsManager.modal.fillContent.primaryText', 'Primary text') ||
                ''
              }
              value={values.primaryText || ''}
              onChange={(e) =>
                setFieldValue('primaryText', e.currentTarget.value)
              }
            />
          </LabeledField>
          <ErrorMessage name="primaryText">
            {(msg) => (
              <Text color="red.500" fontSize="sm" mt="2">
                {msg}
              </Text>
            )}
          </ErrorMessage>
        </Flex>
      </Flex>
    </>
  )
}

export default AdStepFillContent
