import React, { useMemo } from 'react'
import { Flex, Box, Text, Heading } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useFormikContext } from 'formik'
import dayjs from 'dayjs'
import { AD_TYPE, AdvertisementCreate } from '@/types/advertisements'
import { FacebookPlaceholder } from '../../FacebookPlaceholder'
import { formatDate } from '@/utils/date'

const AdStepReview: React.FC = () => {
  const { t } = useTranslation(['common'])
  const { values } = useFormikContext<AdvertisementCreate>()

  const dailyCost = useMemo(() => {
    if (!values.startDate || !values.endDate || !values.budgetTotal) return 0
    const start = dayjs(values.startDate)
    const end = dayjs(values.endDate)

    const diffDays = end.diff(start, 'day') + 1
    return diffDays > 0 ? Number(values.budgetTotal) / diffDays : 0
  }, [values.startDate, values.endDate, values.budgetTotal])

  return (
    <>
      <Flex direction="column" w="100%" p="10" gap="16">
        <Box>
          <Heading
            fontSize="14px"
            fontWeight="bold"
            fontFamily="Avenir"
            color="tabs.selected"
            mb="2"
          >
            {t('adsManager.modal.review.adFocus')}
          </Heading>
          <Flex justify="space-between">
            <Box>
              <Text fontSize="14px" color="gray.500">
                {t('adsManager.modal.review.adType')}
              </Text>
              <Text fontSize="16px" fontWeight="500">
                {t(`adsManager.types.${values.type}`)}
              </Text>
            </Box>
            <Box>
              <Text fontSize="14px" color="gray.500">
                {t('adsManager.modal.review.property')}
              </Text>
              <Text fontSize="16px" fontWeight="500">
                {values?.property?.reference || '-'}
              </Text>
            </Box>
          </Flex>
          {values.type === AD_TYPE.EVENT && (
            <Box mt="4">
              <Text fontSize="14px" color="gray.500">
                {t('adsManager.modal.review.event')}
              </Text>
              <Text fontSize="16px" fontWeight="500">
                {values?.event?.title || '-'}
              </Text>
              <Text mt="-1" fontSize="14px" color="gray.500" fontWeight="500">
                {formatDate(values?.event?.endTime)} -
                {formatDate(values?.event?.startTime)}
              </Text>
            </Box>
          )}
        </Box>

        <Box>
          <Heading
            fontSize="14px"
            fontWeight="bold"
            fontFamily="Avenir"
            color="tabs.selected"
            mb="2"
          >
            {t('adsManager.modal.review.adDetails')}
          </Heading>
          <Flex justify="space-between">
            <Box>
              <Text fontSize="14px" color="gray.500">
                {t('adsManager.modal.review.date')}
              </Text>
              <Text fontSize="14px" color="gray.500" fontWeight="500">
                {t('start')}: {formatDate(values.startDate)}
              </Text>
              <Text fontSize="14px" color="gray.500" fontWeight="500">
                {t('end')}: {formatDate(values.endDate)}
              </Text>
            </Box>
            <Box>
              <Text fontSize="14px" color="gray.500">
                {t('adsManager.modal.review.budget')}
              </Text>
              <Text fontSize="16px" fontWeight="500">
                € {values.budgetTotal}
              </Text>
              <Text fontSize="12px" color="gray.500">
                € {dailyCost.toFixed(2)} {t('adsManager.modal.review.perDay')}
              </Text>
            </Box>
          </Flex>
          <Box mt="4">
            <Text fontSize="14px" color="gray.500">
              {t('adsManager.modal.review.targetArea')}
            </Text>
            <Text fontSize="16px" fontWeight="500">
              {[values.country, values.municipality].join(', ')}
            </Text>
            <Text fontSize="16px" fontWeight="500">
              +{values.targetAreaRadiusKm} km
            </Text>
          </Box>
        </Box>
        <Box>
          <Heading
            fontSize="14px"
            fontWeight="bold"
            fontFamily="Avenir"
            color="tabs.selected"
            mb="2"
          >
            {t('adsManager.modal.review.content')}
          </Heading>
          <Text fontSize="14px" color="gray.500">
            {t('adsManager.modal.review.language')}
          </Text>
          <Text fontSize="16px" fontWeight="500">
            {t(`adsManager.languages.${values.language}`)}
          </Text>
        </Box>
      </Flex>
      <Flex
        w="100%"
        minH="200px"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        backgroundColor="primary.main"
      >
        <Flex paddingY={6}>
          <FacebookPlaceholder
            primaryText={values.primaryText}
            headline={values.title}
            description={values.description || ''}
            imageUrl={values?.advertisementImages?.[0]?.url}
            isAgentPhoto={values.type === AD_TYPE.AGENT}
          />
        </Flex>
      </Flex>
    </>
  )
}

export default AdStepReview
