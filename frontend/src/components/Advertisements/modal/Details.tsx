import { CustomModal } from '../../Modal/Modal'
import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Flex,
  Heading,
  Link,
  ModalFooter,
  Spinner,
  Stack,
  Text,
  Tooltip,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { FacebookPlaceholder } from '../FacebookPlaceholder'
import { ReactNode, useMemo } from 'react'
import {
  duplicateAdvertisement,
  endAdvertisement,
  getAdvertisement,
} from '@/queries/advertisements'
import { useMutation, useQuery, keepPreviousData } from '@tanstack/react-query'
import { useTranslation } from 'next-i18next'
import StatusBadge from '../StatusBadge'
import { getUser } from '@/queries/users'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { useRouter, NextRouter } from 'next/router'
import { formatDate } from '@/utils/date'
import { formatCurrency, formatPercent } from '@/utils/formatNumber'
import { LocaleEnum } from '@/types/localeInfo'
import { CurrencyEnum } from '@/types/property'
import { AD_TYPE, AdvertisementResponse } from '@/types/advertisements'

const LabelledValue = ({
  label,
  children,
}: {
  label: string
  children: ReactNode
}) => {
  return (
    <Stack direction="column" spacing="8px" width="100%">
      <Text fontSize="12px" color="secondary.input">
        {label}
      </Text>
      <Stack direction="column" spacing="4px">
        {children}
      </Stack>
    </Stack>
  )
}

const SmallText = ({ children }: { children: ReactNode }) => (
  <Box fontSize="12px">{children}</Box>
)

const AD_TYPE_TO_TRANSLATION_KEY: Record<AD_TYPE, string> = {
  [AD_TYPE.AGENT]: 'advertisement.details.agent',
  [AD_TYPE.EVENT]: 'advertisement.details.event',
  [AD_TYPE.LISTING_PROPERTY]: 'advertisement.details.property',
  [AD_TYPE.PROPERTY_SOLD]: 'advertisement.details.property',
  [AD_TYPE.CUSTOM]: 'advertisement.details.property',
}

const getEntityTranslationKey = (adType: AD_TYPE) => {
  return AD_TYPE_TO_TRANSLATION_KEY[adType] || 'advertisement.details.property'
}

const EntityContent = ({
  advertisement,
  user,
  onClose,
  router,
}: {
  advertisement: AdvertisementResponse
  user: { firstName?: string; lastName?: string } | undefined
  onClose: () => void
  router: NextRouter
}) => {
  const { t } = useTranslation('common')

  switch (advertisement.type) {
    case AD_TYPE.AGENT:
      return (
        <SmallText>
          {user?.firstName && user?.lastName
            ? `${user.firstName} ${user.lastName}`
            : '-'}
        </SmallText>
      )

    case AD_TYPE.EVENT:
      return <SmallText>{advertisement.event?.title || '-'}</SmallText>

    case AD_TYPE.LISTING_PROPERTY:
    case AD_TYPE.PROPERTY_SOLD:
    case AD_TYPE.CUSTOM:
    default:
      return (
        <>
          {advertisement.property?.reference ? (
            <>
              <Link
                onClick={() => {
                  router.push({
                    pathname: `/properties/${advertisement.property?.reference}`,
                  })
                  onClose()
                }}
                display="block"
                whiteSpace="nowrap"
              >
                {advertisement.property?.reference}
              </Link>
              <SmallText>
                {user?.firstName && user?.lastName
                  ? `${t('advertisement.details.managedBy')} ${
                      user.firstName
                    } ${user.lastName}`
                  : '-'}
              </SmallText>
            </>
          ) : (
            <SmallText>-</SmallText>
          )}
        </>
      )
  }
}

const MetricLabelWithTooltip = ({
  label,
  tooltipKey,
}: {
  label: string
  tooltipKey: string
}) => {
  const { t } = useTranslation('common')
  return (
    <Flex alignItems="flex-start" gap="4px">
      <Text fontSize="12px" color="secondary.input">
        {label}
      </Text>
      <Tooltip label={t(`adsManager.table.headers.${tooltipKey}.tooltip`)}>
        <Text
          className="material-symbols-outlined"
          cursor="pointer"
          fontWeight={400}
          fontSize="12px"
          color="secondary.input"
          lineHeight="3px"
        >
          info
        </Text>
      </Tooltip>
    </Flex>
  )
}

export const AdvertisementsDetailsModal = ({
  id,
  onClose,
}: {
  onClose: () => void
  id: number
}) => {
  const { t } = useTranslation('common')
  const toast = useToast()
  const router = useRouter()
  const isMobile = useBreakpointValue({ base: true, md: false })

  const { organization } = useUserAndOrganization()
  const { data: advertisement } = useQuery({
    queryKey: ['advertisement', id],
    queryFn: () => getAdvertisement(id),
    placeholderData: keepPreviousData,
  })

  const [startDate, endDate] = useMemo(() => {
    if (!advertisement) {
      return []
    }
    const start = advertisement.startDate
    const end = advertisement.endDate

    return [formatDate(start), formatDate(end)]
  }, [advertisement])

  const locale = LocaleEnum[organization?.countryCode || 'ES']

  const formatToEuro = (amountCents: number) =>
    formatCurrency({
      locale,
      value: amountCents / 100,
      currency: CurrencyEnum.EUR,
    }) || '-'

  const getCurrency = (value?: number | null) =>
    value === undefined || value === null
      ? '-'
      : formatCurrency({
          locale,
          value,
          currency: CurrencyEnum.EUR,
        })

  const formatPercentage = (value?: number | null) =>
    value === undefined || value === null
      ? '-'
      : formatPercent({
          locale,
          value,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })

  const { mutate: mutationDuplicate } = useMutation({
    mutationKey: ['duplicate'],
    mutationFn: duplicateAdvertisement,
    onSuccess: (_data) => {
      toast({
        id: 'adDuplicate',
        title: t('adsManager.messages.duplicate.title'),
        description: t('adsManager.messages.duplicate.description', {
          entity: advertisement?.title,
        }),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
      router.push('/advertisements')
    },
    onError: () => {
      toast({
        id: 'adDuplicate',
        title: t('adsManager.table.errors.duplicate.title'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const { mutate: mutationEndAd } = useMutation({
    mutationKey: ['endAd'],
    mutationFn: endAdvertisement,
    onSuccess: (_data) => {
      toast({
        id: 'adDuplicate',
        title: t('adsManager.messages.cancel.title'),
        description: t('adsManager.messages.end.description', {
          entity: advertisement?.title,
        }),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      toast({
        id: 'adDuplicate',
        title: t('adsManager.table.errors.end.title'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const endAd = (id) => {
    if (id) {
      mutationEndAd(id)
    }
  }

  const { data: user } = useQuery({
    queryKey: ['user', advertisement, organization?.countryCode == 'ES'],
    queryFn: () => advertisement && getUser(advertisement.ownerId.toString()),
  })

  const base64Encode = (str: string): string => {
    return Buffer.from(str, 'utf-8').toString('base64')
  }

  const encodedId = advertisement?.id
    ? base64Encode(JSON.stringify({ id: advertisement.id }))
    : undefined

  return (
    <CustomModal
      isOpen={true}
      onClose={onClose}
      title={
        <>
          {advertisement?.type
            ? t(`adsManager.types.${advertisement.type}`)
            : t('loading')}
          - {advertisement?.title || ''}
          {advertisement && (
            <StatusBadge status={advertisement.status} marginLeft={2} />
          )}
        </>
      }
      isLoading={false}
      disableAcceptButton={false}
      size="5xl"
      modalBodyProps={{ padding: 0 }}
      footerContent={
        <ModalFooter
          padding="18px 24px"
          borderTop="1px solid"
          borderTopColor="gray.200"
          justifyContent="space-between"
        >
          <Button
            padding="6px 16px"
            variant={'ghost'}
            colorScheme="whiteAlpha"
            color={'#EE5252'}
            mr={3}
            data-testid="custom-modal-previous-button"
            size="mdWide"
            onClick={() => endAd(advertisement?.id)}
          >
            <Text variant={'modal'}>End ad</Text>
          </Button>
          <Flex>
            <Button
              padding="6px 16px"
              variant={'ghost'}
              colorScheme="white"
              color={'black'}
              mr={3}
              data-testid="custom-modal-previous-button"
              size="mdWide"
              border="1px black solid"
              onClick={() =>
                advertisement && mutationDuplicate(advertisement.id)
              }
            >
              <Text variant={'modal'}>Duplicate</Text>
            </Button>
            <Button
              padding="6px 16px"
              variant="ghost"
              colorScheme="blackAlpha"
              bg="common.black"
              color="common.white"
              mr={3}
              data-testid="custom-modal-preview-button"
              size="mdWide"
              _hover={{ bg: 'common.black', opacity: 0.8 }}
              onClick={() =>
                encodedId &&
                window.open(
                  `/advertisements/preview/${encodedId}?callbackUrl=${router.asPath}`,
                  '_blank'
                )
              }
            >
              <Text variant="modal">
                {t('adsManager.table.client_preview')}
              </Text>
            </Button>
          </Flex>
        </ModalFooter>
      }
    >
      {!advertisement ? (
        <Flex
          width="100%"
          height="100%"
          minHeight="200px"
          justifyContent="center"
          alignItems="center"
        >
          <Spinner />
        </Flex>
      ) : (
        <Stack
          direction={isMobile ? 'column' : 'row'}
          width="100%"
          height="100%"
          gap="0"
        >
          <Box
            maxWidth={isMobile ? '100%' : '448px'}
            width="100%"
            minHeight="100%"
            order={isMobile ? 0 : 1}
          >
            <Stack height="100%" gap={0}>
              <Flex
                backgroundColor="#F6F1ED"
                justifyContent="center"
                alignItems="center"
                padding="40px 64px"
                flex="1"
              >
                <FacebookPlaceholder
                  imageUrl={advertisement.advertisementImages[0]?.url}
                  primaryText={advertisement.primaryText ?? ''}
                  headline={advertisement.title}
                  description={advertisement.description}
                  isAgentPhoto={advertisement.type === AD_TYPE.AGENT}
                />
              </Flex>
              <Alert>
                <AlertIcon />
                {t('advertisement.details.placeholderAlert')}
              </Alert>
            </Stack>
          </Box>
          <Stack
            flexGrow="1"
            spacing="32px"
            padding="24px"
            order={isMobile ? 1 : 0}
          >
            <Stack spacing="16px">
              <Heading fontFamily="Avenir" color="tabs.selected">
                {t('advertisement.details.focus')}
              </Heading>
              <Stack direction="row">
                <LabelledValue label={t('advertisement.details.type')}>
                  <SmallText>
                    {t(`adsManager.types.${advertisement.type}`)}
                  </SmallText>
                </LabelledValue>
                <LabelledValue
                  label={t(getEntityTranslationKey(advertisement.type))}
                >
                  <EntityContent
                    advertisement={advertisement}
                    user={user}
                    onClose={onClose}
                    router={router}
                  />
                </LabelledValue>
              </Stack>
            </Stack>
            <Stack spacing="16px">
              <Heading fontFamily="Avenir" color="tabs.selected">
                {t('advertisement.details.details')}
              </Heading>
              <Stack direction="row">
                <LabelledValue label={t('advertisement.details.date')}>
                  {t('start')}: {startDate}
                  <SmallText>
                    {t('end')}: {endDate}
                  </SmallText>
                </LabelledValue>
                <LabelledValue label={t('advertisement.details.targetArea')}>
                  {[advertisement.municipality, advertisement.country?.name]
                    .filter(Boolean)
                    .join(', ') || '-'}
                  <SmallText>+{advertisement.targetAreaRadiusKm} km</SmallText>
                </LabelledValue>
              </Stack>
              <Stack direction="row">
                <LabelledValue label={t('advertisement.details.budget')}>
                  {formatToEuro(advertisement.budgetTotal)}
                  <SmallText>
                    {`${formatToEuro(advertisement.budgetDaily)} ${t(
                      'adsManager.modal.review.perDay'
                    )}`}
                  </SmallText>
                </LabelledValue>
                <LabelledValue label={t('advertisement.details.language')}>
                  <SmallText>
                    {t(`adsManager.languages.${advertisement.language}`)}
                  </SmallText>
                </LabelledValue>
              </Stack>
            </Stack>
            <Stack spacing="16px">
              <Heading fontFamily="Avenir" color="tabs.selected">
                <SmallText>{t('advertisement.details.metrics')}</SmallText>
              </Heading>
              <Stack direction="row">
                <Stack direction="column" spacing="8px" width="100%">
                  <MetricLabelWithTooltip
                    label={t('advertisement.details.impressions')}
                    tooltipKey="impressions"
                  />
                  <Stack direction="column" spacing="4px">
                    <SmallText> {advertisement.impressions}</SmallText>
                  </Stack>
                </Stack>
                <Stack direction="column" spacing="8px" width="100%">
                  <MetricLabelWithTooltip
                    label={t('advertisement.details.cpm')}
                    tooltipKey="cpm"
                  />
                  <Stack direction="column" spacing="4px">
                    <SmallText>{getCurrency(advertisement.cpm)}</SmallText>
                  </Stack>
                </Stack>
              </Stack>
              <Stack direction="row">
                <Stack direction="column" spacing="8px" width="100%">
                  <MetricLabelWithTooltip
                    label={t('advertisement.details.linkClicks')}
                    tooltipKey="link_clicks"
                  />
                  <Stack direction="column" spacing="4px">
                    <SmallText>{advertisement.linkClicks}</SmallText>
                  </Stack>
                </Stack>
                <Stack direction="column" spacing="8px" width="100%">
                  <MetricLabelWithTooltip
                    label={t('advertisement.details.ctr')}
                    tooltipKey="ctr"
                  />
                  <Stack direction="column" spacing="4px">
                    <SmallText>{formatPercentage(advertisement.ctr)}</SmallText>
                  </Stack>
                </Stack>
              </Stack>
              <Stack direction="row">
                <Stack direction="column" spacing="8px" width="100%">
                  <MetricLabelWithTooltip
                    label={t('advertisement.details.cpc')}
                    tooltipKey="cpc"
                  />
                  <Stack direction="column" spacing="4px">
                    <SmallText>{getCurrency(advertisement.cpc)}</SmallText>
                  </Stack>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      )}
    </CustomModal>
  )
}
