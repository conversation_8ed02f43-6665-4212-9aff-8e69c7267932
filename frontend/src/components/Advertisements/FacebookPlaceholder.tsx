import { CustomAvatar } from '../Avatar/CustomAvatar'
import { Box, Flex, Image, Text } from '@chakra-ui/react'
import placeholder from '@/cache/images/property_placeholder.png'

export const FacebookPlaceholder = ({
  primaryText,
  headline,
  description,
  imageUrl,
  isAgentPhoto = false,
}: {
  primaryText: string
  headline: string
  description?: string
  imageUrl?: string
  isAgentPhoto?: boolean
}) => {
  return (
    <Box width="320px" backgroundColor="white" border="1px solid #E3D6CB">
      <Box padding="12px">
        <Flex gap="8px" alignItems="center">
          <CustomAvatar
            name={`Strand`}
            border="0px solid black"
            backgroundColor="#F6F1ED"
            color="black"
          />
          <Flex direction="column">
            <Text fontSize="14px">
              <b>Strand Properties</b>
            </Text>
            <Text fontSize="12px" color="#666666" margin="0">
              Promoted
            </Text>
          </Flex>
        </Flex>
      </Box>
      <Box padding="12px" fontSize="15px">
        {primaryText.length < 108 ? (
          primaryText
        ) : (
          <>
            {primaryText.slice(0, 108)} <b>See more...</b>
          </>
        )}
      </Box>
      <Image
        alt="advertisementImage"
        width="320px"
        height="210px"
        objectFit="cover"
        objectPosition={isAgentPhoto ? 'center top' : 'center'}
        src={
          imageUrl && imageUrl.trim() !== '' && !imageUrl.includes('undefined')
            ? imageUrl
            : placeholder.src
        }
        filter={isAgentPhoto ? 'grayscale(100%)' : 'none'}
      />
      <Flex direction="column" gap="2px" padding="12px">
        <Text fontSize="12px" color="#666">
          STRANDPROPERTIES.COM
        </Text>
        <Text fontSize="16px" margin="0">
          <b>{headline}</b>
        </Text>
        <Text fontSize="14px" margin="0">
          {description}
        </Text>
      </Flex>
    </Box>
  )
}
