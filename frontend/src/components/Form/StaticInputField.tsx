import {
  FormControl,
  FormLabel,
  Input as ChakraInput,
  InputGroup,
  InputRightElement,
  Flex,
} from '@chakra-ui/react'
import { ReactElement, ReactNode } from 'react'
import { CurrencyInput } from './Input'

export const StaticInputField = ({
  label,
  value,
  rightElement,
  width,
  mr,
  readOnly = true,
  type,
}: {
  label: (string | ReactElement)[] | string | null
  value: string | number | null | undefined
  rightElement?: (string | ReactElement)[] | string | ReactNode
  width?: string | number
  mr?: string | number
  readOnly?: boolean
  type?: string
}) => {
  return (
    <FormControl width={width} mr={mr}>
      <Flex gap="0.25rem">
        <FormLabel
          m="0"
          overflow="hidden"
          whiteSpace="nowrap"
          textOverflow="ellipsis"
        >
          {label}
        </FormLabel>
      </Flex>
      <InputGroup flexDir="column">
        {type === 'currency' ? (
          <ChakraInput
            as={CurrencyInput}
            readOnly={readOnly}
            value={value ?? ''}
            type={type}
          />
        ) : (
          <ChakraInput readOnly={readOnly} value={value ?? ''} type={type} />
        )}
        {rightElement && (
          <InputRightElement height="100%" width="fit-content" marginRight="4">
            {rightElement}
          </InputRightElement>
        )}
      </InputGroup>
    </FormControl>
  )
}
