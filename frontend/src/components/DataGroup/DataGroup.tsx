import { Stack, StackProps, Text, TextProps } from '@chakra-ui/react'
import { HeaderRow } from '../HeaderRow/HeaderRow'

export const DataGroupContainer = ({
  children,
  title,
}: {
  children: React.ReactNode
  title: string
}) => (
  <Stack>
    <HeaderRow title={title} />
    {children}
  </Stack>
)

export const DataRow = ({
  label,
  value,
  fontSize = 'medium',
  containerProps,
}: {
  label: string
  value: string
  fontSize?: TextProps['fontSize']
  containerProps?: StackProps
}) => (
  <Stack
    direction="row"
    alignItems="end"
    borderBottom="1px solid #F2F2F2"
    {...containerProps}
  >
    <Text flex="5" fontSize={fontSize} fontWeight={700} mr={'1rem'}>
      {label}
    </Text>
    <Text flex="7" fontSize={fontSize}>
      {value}
    </Text>
  </Stack>
)

export const DataGroup = ({
  title,
  values,
}: {
  title: string
  values: { label: string; value: string }[]
}) => {
  return (
    <DataGroupContainer title={title}>
      {values.map(({ value, label }) => (
        <DataRow key={label} label={label} value={value} />
      ))}
    </DataGroupContainer>
  )
}
