import {
  FormV<PERSON>ues,
  LeadListRead,
  LeadRelevance,
  LeadSource,
  LeadStatus,
  LeadType,
} from '@/types/lead'
import {
  Flex,
  Heading,
  Textarea,
  Wrap,
  WrapItem,
  useBreakpointValue,
} from '@chakra-ui/react'
import { TFunction, useTranslation } from 'next-i18next'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { getProperties } from '@/queries/property'
import { getUsers } from '@/queries/users'
import { updateLead } from '@/queries/leads'
import { CreateLeadMutationProps } from '@/types/lead'
import { Formik, useFormikContext } from 'formik'
import { useState } from 'react'
import { useToast } from '@chakra-ui/react'
import { CreateContactDrawer } from '@/components/ActionDrawer/CreateContactDrawer'
import { CustomModal } from '@/components/Modal/Modal'
import { DataSource, ListProperties } from '@/types/property'
import * as Yup from 'yup'
import { BadgeItem } from '../Badge/BadgeItem'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import { SimpleDropdown } from '../DropdownMenu/SimpleDropdown'
import { InputWithLabel } from '../Form/Input'
import LabeledField from '../Form/LabeledField'
import { useLeads } from '@/hooks/useLeads'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import { GenerateOption } from '@/types/common'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getContacts } from '@/queries/contact'
import { PropertyDropdownItem } from '../PropertyDropdownItem'
import { PROPERTY_DROPDOWN_ITEM_HEIGHT } from '@/utils/constants'
import { getFullName } from '@/types/users'
import { FeatureFlag } from '../FeatureFlag'

type BodyProps = {
  t: TFunction
  realtorsPaginationProps: PaginationProps<GenerateOption>
  propertiesPaginationProps: PaginationProps<ListProperties>
  sellersPaginationProps: PaginationProps<GenerateOption>
}
const Body = ({
  t,
  realtorsPaginationProps,
  propertiesPaginationProps,
  sellersPaginationProps,
}: BodyProps) => {
  const formik = useFormikContext<CreateLeadMutationProps>()
  const renderLeadStatus = () => {
    return Object.values(LeadStatus).map((status) => {
      return (
        <WrapItem key={status}>
          <BadgeItem
            label={t(`salesActivity.status.${status}`)}
            value={status}
            onClick={() => formik.setFieldValue('status', status)}
            isChecked={formik.values.status === status}
          />
        </WrapItem>
      )
    })
  }

  const NoneItem = ({
    name,
    formValues,
  }: {
    name: string
    formValues: FormValues
  }) => {
    return (
      <WrapItem>
        <BadgeItem
          label={t('salesActivity.none')}
          value={''}
          onClick={() => formik.setFieldValue(name, null)}
          isChecked={!formValues}
        />
      </WrapItem>
    )
  }
  const renderLeadRelevance = () => {
    return (
      <>
        <NoneItem name="relevance" formValues={formik.values.relevance} />
        {Object.values(LeadRelevance).map((relevance) => {
          return (
            <WrapItem key={relevance}>
              <BadgeItem
                label={t(`salesActivity.relevance.${relevance}`)}
                value={relevance}
                onClick={() => formik.setFieldValue('relevance', relevance)}
                isChecked={formik.values.relevance === relevance}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  const renderLeadSource = () => {
    return (
      <>
        <NoneItem name="source" formValues={formik.values.source} />
        {Object.values(LeadSource).map((source) => {
          return (
            <WrapItem key={source}>
              <BadgeItem
                label={t(`salesActivity.source.${source}`)}
                value={source}
                onClick={() => formik.setFieldValue('source', source)}
                isChecked={formik.values.source === source}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  const renderLeadType = () => {
    return (
      <>
        <NoneItem name="type" formValues={formik.values.type} />
        {Object.values(LeadType).map((type) => {
          return (
            <WrapItem key={type}>
              <BadgeItem
                label={t(`salesActivity.type.${type}`)}
                value={type}
                onClick={() => formik.setFieldValue('type', type)}
                isChecked={formik.values.type === type}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  return (
    <Flex direction="column" gap={2} overflowX={'scroll'}>
      <Flex direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityStatus')}
        </Heading>
        <Wrap>{renderLeadStatus()}</Wrap>
      </Flex>
      <Flex width="100%" gap="12">
        <InputWithLabel
          defaultValue={formik.values.title}
          label={`${t('title')}*`}
          placeholder={`${t('title')}`}
          name="title"
          type="text"
          error={formik.errors.title}
          touched={formik.touched.title}
        />
      </Flex>
      <Flex width="100%" gap="2">
        <LabeledField label={`${t('description')}`} width="100%">
          <Textarea
            defaultValue={formik.values.description}
            placeholder={`${t('description')}`}
            name={`${t('description')}`}
            size="sm"
            onBlur={(e) =>
              formik.setFieldValue('description', e.currentTarget.value)
            }
          />
        </LabeledField>
      </Flex>
      <Flex width="100%" gap="2">
        <LabeledField label={`${t('assignedTo')}*`} width="100%">
          <MultiSelectDropdown
            name="assignedTo"
            dataTestid="user-id-dropdown"
            placement="bottomStart"
            cleanable={false}
            {...realtorsPaginationProps}
          />
        </LabeledField>
      </Flex>
      <FeatureFlag featureFlag="listProperties">
        <Flex>
          <LabeledField label={`${t('property')}`}>
            <SimpleDropdown
              name="propertyReference"
              cleanable={false}
              dataTestid="property-id-dropdown"
              valueKey="reference"
              labelKey="reference"
              renderMenuItem={PropertyDropdownItem}
              itemSize={PROPERTY_DROPDOWN_ITEM_HEIGHT}
              {...propertiesPaginationProps}
            />
          </LabeledField>
        </Flex>
      </FeatureFlag>
      <Flex width="100%" gap="2">
        <LabeledField label={t('salesActivity.contacts')}>
          <MultiSelectDropdown
            name="contactIds"
            placement="bottomStart"
            dataTestid="sellers-dropdown"
            selectionLimit={10}
            {...sellersPaginationProps}
          />
        </LabeledField>
      </Flex>
      <Flex width="100%" direction="column">
        <Heading variant="H2">{t('salesActivity.salesActivitySource')}</Heading>
        <Wrap>{renderLeadSource()}</Wrap>
      </Flex>
      <Flex width="100%" direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityType')}
        </Heading>
        <Wrap>{renderLeadType()}</Wrap>
      </Flex>

      <Flex direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityRelevance')}
        </Heading>
        <Wrap>
          <Wrap>{renderLeadRelevance()}</Wrap>
        </Wrap>
      </Flex>
    </Flex>
  )
}

const EditLeadSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object({
    title: Yup.string()
      .label(t('title'))
      .required(t('errors.fieldRequired'))
      .max(120, t('errors.maxCharacters')),
    description: Yup.string()
      .label(t('description'))
      .max(500, t('errors.maxCharacters')),
    assignedTo: Yup.array()
      .of(Yup.number().required())
      .label(t('assignedTo'))
      .min(1, t('salesActivity.errors.realtorRequired')),
    propertyReferences: Yup.array()
      .of(Yup.number().required())
      .min(0)
      .label(t('properties')),
    contactIds: Yup.array()
      .of(Yup.number())
      .min(0)
      .label(t('event.fields.contacts')),
  })

export default function EditLeadForm({
  defaultLead,
  onClose,
}: {
  defaultLead: LeadListRead
  onClose: () => void
}) {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const { updateLeadState } = useLeads()
  const queryClient = useQueryClient()

  const { paginationProps: realtorsPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['users'],
      queryFn: (params) =>
        getUsers({
          ...params,
          pageSize: 120,
          onlyActive: true,
        }).then((data) => ({
          records: data.records.map((user) => ({
            value: user.id,
            label: getFullName(user),
          })),
          metadata: data.metadata,
        })),
    })

  const { paginationProps: propertiesPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['properties'],
      queryFn: (params) =>
        getProperties({
          ...params,
          dataSource: DataSource.STRAND,
        }),
    })

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }).then((data) => ({
          records: data.records.map((contact) => ({
            value: contact.id,
            label: contact.name,
          })),
          metadata: data.metadata,
        })),
    }
  )

  const initialValues: CreateLeadMutationProps = {
    status: defaultLead.status,
    assignedTo: defaultLead.assignedToUsers.map((user) => user.id),
    contactIds: defaultLead.contacts.map((contact) => contact.id),
    contacts: defaultLead.contacts.map((contact) => ({
      id: contact.id,
      name: contact.name,
    })),
    title: defaultLead.title,
    description: defaultLead.description,
    propertyReference: defaultLead.propertyReference,
    relevance: defaultLead.relevance,
    source: defaultLead.source,
    type: defaultLead.type,
  }

  const { isPending: isCreatingLeadLoading, mutate: mutationEditLead } =
    useMutation({
      mutationKey: ['leads'],
      mutationFn: updateLead,
      onSuccess: (leadEdited) => {
        queryClient.invalidateQueries({
          queryKey: [
            'saleActivityActivityEvents',
            defaultLead.id.toString(),
            '',
          ],
        })
        onClose()
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: `${t('salesActivity.editSalesActivity')} #${
            leadEdited?.id
          } ${t('updated')}`,
          description: t('salesActivity.editSalesActivityDescription'),
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        queryClient.invalidateQueries({
          queryKey: ['leads'],
        })
        updateLeadState(leadEdited, 'updateLead')
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t(
            'formik.errors.title' // i18next-extract-disable-line
          ),
          description: t(
            'formik.errors.salesActivityNotUpdated' // i18next-extract-disable-line
          ),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const onSubmit = async (values: CreateLeadMutationProps) => {
    mutationEditLead({
      id: defaultLead.id.toString(),
      leadData: values,
    })
  }

  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={onSubmit}
        validationSchema={EditLeadSchema(t)}
        validateOnMount
      >
        {({ isValid, isSubmitting, submitForm }) => {
          return !isMobile ? (
            <CustomModal
              isOpen={!isMobile}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('salesActivity.editSalesActivity')}
              acceptTitle={t('save') || undefined}
              isLoading={isCreatingLeadLoading}
              disableAcceptButton={!isValid || isSubmitting}
            >
              <Body
                t={t}
                realtorsPaginationProps={realtorsPaginationProps}
                propertiesPaginationProps={propertiesPaginationProps}
                sellersPaginationProps={sellersPaginationProps}
              />
            </CustomModal>
          ) : (
            <CreateContactDrawer
              isOpen={isMobile}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('salesActivity.editSalesActivity')}
              acceptTitle={t('edit') || undefined}
              isLoading={isCreatingLeadLoading}
              disableAcceptButton={!isValid || isSubmitting}
            >
              <Body
                t={t}
                realtorsPaginationProps={realtorsPaginationProps}
                propertiesPaginationProps={propertiesPaginationProps}
                sellersPaginationProps={sellersPaginationProps}
              />
            </CreateContactDrawer>
          )
        }}
      </Formik>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={() => {
          setShowConfirmationModal(false)
          confirmationModalAcceptFunction()
        }}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
    </>
  )
}
