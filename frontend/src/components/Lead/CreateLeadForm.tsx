import { getProperties } from '@/queries/property'
import { getUsers } from '@/queries/users'
import {
  CreateLeadMutationProps,
  FormValues,
  LeadRelevance,
  LeadSource,
  LeadStatus,
  LeadType,
} from '@/types/lead'
import { DataSource, ListProperties } from '@/types/property'
import {
  Flex,
  Heading,
  Textarea,
  Wrap,
  WrapItem,
  useBreakpointValue,
  useToast,
  Text,
  Alert,
  AlertIcon,
  Link,
} from '@chakra-ui/react'
import { useMutation } from '@tanstack/react-query'
import { Formik, useFormikContext } from 'formik'
import { TFunction, useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { CustomModal } from '@/components/Modal/Modal'
import { CreateContactDrawer } from '@/components/ActionDrawer/CreateContactDrawer'
import * as Yup from 'yup'
import { MultiSelectDropdown } from '@/components/DropdownMenu/MultiSelectDropdown'
import { createLead } from '@/queries/leads'
import { BadgeItem } from '@/components/Badge/BadgeItem'
import { SimpleDropdown } from '@/components/DropdownMenu/SimpleDropdown'
import { InputWithLabel } from '@/components/Form/Input'
import LabeledField from '@/components/Form/LabeledField'
import { useAuth } from '@/hooks/useAuth'
import { useLeads } from '@/hooks/useLeads'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import { GenerateOption } from '@/types/common'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getContacts } from '@/queries/contact'
import { PropertyDropdownItem } from '../PropertyDropdownItem'
import { PROPERTY_DROPDOWN_ITEM_HEIGHT } from '@/utils/constants'
import { getFullName } from '@/types/users'
import { FeatureFlag } from '../FeatureFlag'

type BodyProps = {
  t: TFunction
  realtorsPaginationProps: PaginationProps<GenerateOption>
  propertiesPaginationProps: PaginationProps<ListProperties>
  sellersPaginationProps: PaginationProps<GenerateOption>
}
const Body = ({
  t,
  realtorsPaginationProps,
  propertiesPaginationProps,
  sellersPaginationProps,
}: BodyProps) => {
  const formik = useFormikContext<CreateLeadMutationProps>()
  const renderLeadStatus = () => {
    return Object.values(LeadStatus).map((status) => {
      return (
        <WrapItem key={status}>
          <BadgeItem
            label={t(`salesActivity.status.${status}`)}
            value={status}
            onClick={() => formik.setFieldValue('status', status)}
            isChecked={formik.values.status === status}
          />
        </WrapItem>
      )
    })
  }

  const NoneItem = ({
    name,
    formValues,
  }: {
    name: string
    formValues: FormValues
  }) => {
    return (
      <WrapItem>
        <BadgeItem
          label={t('salesActivity.none')}
          value={''}
          onClick={() => formik.setFieldValue(name, null)}
          isChecked={!formValues}
        />
      </WrapItem>
    )
  }
  const renderLeadRelevance = () => {
    return (
      <>
        <NoneItem name="relevance" formValues={formik.values.relevance} />
        {Object.values(LeadRelevance).map((relevance) => {
          return (
            <WrapItem key={relevance}>
              <BadgeItem
                label={t(`salesActivity.relevance.${relevance}`)}
                value={relevance}
                onClick={() => formik.setFieldValue('relevance', relevance)}
                isChecked={formik.values.relevance === relevance}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  const renderLeadSource = () => {
    return (
      <>
        <NoneItem name="source" formValues={formik.values.source} />
        {Object.values(LeadSource).map((source) => {
          return (
            <WrapItem key={source}>
              <BadgeItem
                label={t(`salesActivity.source.${source}`)}
                value={source}
                onClick={() => formik.setFieldValue('source', source)}
                isChecked={formik.values.source === source}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  const renderLeadType = () => {
    return (
      <>
        <NoneItem name="type" formValues={formik.values.type} />
        {Object.values(LeadType).map((type) => {
          return (
            <WrapItem key={type}>
              <BadgeItem
                label={t(`salesActivity.type.${type}`)}
                value={type}
                onClick={() => formik.setFieldValue('type', type)}
                isChecked={formik.values.type === type}
              />
            </WrapItem>
          )
        })}
      </>
    )
  }

  return (
    <Flex direction="column" gap="4" overflowX={'scroll'}>
      <Flex direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityStatus')}
        </Heading>
        <Wrap>{renderLeadStatus()}</Wrap>
      </Flex>
      <Flex width="100%" gap="12">
        <InputWithLabel
          label={`${t('title')}*`}
          placeholder={`${t('title')}`}
          name="title"
          type="text"
          error={formik.errors.title}
          touched={formik.touched.title}
        />
      </Flex>
      <Flex width="100%" gap="2">
        <LabeledField label={`${t('description')}`} width="100%">
          <Textarea
            placeholder={`${t('description')}`}
            name={`${t('description')}`}
            size="sm"
            onBlur={(e) =>
              formik.setFieldValue('description', e.currentTarget.value)
            }
          />
        </LabeledField>
      </Flex>
      <Flex width="100%" gap="2">
        <LabeledField label={`${t('assignedTo')}*`} width="100%">
          <MultiSelectDropdown
            name="assignedTo"
            dataTestid="user-id-dropdown"
            placement="bottomStart"
            cleanable={false}
            {...realtorsPaginationProps}
          />
        </LabeledField>
      </Flex>
      <FeatureFlag featureFlag="listProperties">
        <Flex>
          <LabeledField label={`${t('property')}`}>
            <SimpleDropdown
              name="propertyReference"
              cleanable={false}
              dataTestid="property-id-dropdown"
              valueKey="reference"
              labelKey="reference"
              placeholder={t('select')}
              renderMenuItem={PropertyDropdownItem}
              itemSize={PROPERTY_DROPDOWN_ITEM_HEIGHT}
              {...propertiesPaginationProps}
            />
          </LabeledField>
        </Flex>
      </FeatureFlag>
      <Flex width="100%" gap="2">
        <LabeledField label={t('salesActivity.contacts')}>
          <MultiSelectDropdown
            name="contactIds"
            placement="bottomStart"
            placeholder={t('select')}
            dataTestid="sellers-dropdown"
            selectionLimit={10}
            {...sellersPaginationProps}
          />
        </LabeledField>
      </Flex>
      <Flex width="100%" direction="column">
        <Heading variant="H2">{t('salesActivity.salesActivitySource')}</Heading>
        <Wrap>{renderLeadSource()}</Wrap>
      </Flex>
      <Flex width="100%" direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityType')}
        </Heading>
        <Wrap>{renderLeadType()}</Wrap>
      </Flex>

      <Flex direction="column">
        <Heading variant="H2" padding="5px">
          {t('salesActivity.salesActivityRelevance')}
        </Heading>
        <Wrap>
          <Wrap>{renderLeadRelevance()}</Wrap>
        </Wrap>
      </Flex>
    </Flex>
  )
}

const CreateLeadSchema = (
  t: (key: string, option?: Record<string, string>) => string
) =>
  Yup.object({
    title: Yup.string()
      .label(t('title'))
      .required(t('errors.fieldRequired'))
      .max(120, t('errors.maxCharacters')),
    description: Yup.string()
      .label(t('description'))
      .max(500, t('errors.maxCharacters')),
    assignedTo: Yup.array()
      .of(Yup.number().required())
      .label(t('assignedTo'))
      .min(1, t('salesActivity.errors.realtorRequired')),
    propertyReferences: Yup.array()
      .of(Yup.number().required())
      .min(0)
      .label(t('properties')),
    contactIds: Yup.array()
      .of(Yup.number())
      .min(0)
      .label(t('event.fields.contacts')),
  })

export default function CreateLeadForm({
  status,
  onClose,
}: {
  status: LeadStatus
  onClose: () => void
}) {
  const { t } = useTranslation(['common'])
  const router = useRouter()
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const { updateLeadState } = useLeads()

  const { paginationProps: realtorsPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['users'],
      queryFn: (params) =>
        getUsers({
          ...params,
          pageSize: 120,
          onlyActive: true,
        }).then((data) => ({
          records: data.records.map((user) => ({
            value: user.id,
            label: getFullName(user),
          })),
          metadata: data.metadata,
        })),
    })

  const { paginationProps: propertiesPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['properties'],
      queryFn: (params) =>
        getProperties({
          ...params,
          dataSource: DataSource.STRAND,
        }),
    })

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }).then((data) => ({
          records: data.records.map((contact) => ({
            value: contact.id,
            label: contact.name,
          })),
          metadata: data.metadata,
        })),
    }
  )

  const { authStates } = useAuth()

  const initialValues: CreateLeadMutationProps = {
    status: status,
    assignedTo: authStates.currentUser ? [authStates.currentUser.id] : [],
    contactIds: [],
    title: '',
    description: '',
  }

  const { isPending: isCreatingLeadLoading, mutate: mutationCreateLead } =
    useMutation({
      mutationKey: ['lead'],
      mutationFn: createLead,
      onSuccess: (leadCreated) => {
        onClose()
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: `${t('activity')} #${leadCreated.id} ${t('created')}`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
          render() {
            return (
              <Alert
                status="success"
                backgroundColor="alert.success.bg"
                borderRadius={10}
                boxShadow="none"
                display="flex"
                alignContent={'center'}
              >
                <AlertIcon />
                <Flex direction={'column'}>
                  <Text>{`${t('activity')} #${leadCreated.id} ${t(
                    'created'
                  )}`}</Text>
                  <Text fontSize={16} fontWeight={400}>
                    {t('salesActivity.createSalesActivityWithId')}
                    <Link
                      color="black"
                      textDecoration="underline"
                      onClick={() => {
                        router.push({
                          pathname: `/sales-activity/${leadCreated.id}`,
                          query: { callbackUrl: router.asPath },
                        })
                      }}
                    >
                      {t('here')}
                    </Link>
                  </Text>
                </Flex>
              </Alert>
            )
          },
        })
        updateLeadState(leadCreated, 'updateLead')
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.salesActivityNotCreated'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  const onSubmit = async (values: CreateLeadMutationProps) => {
    const valuesToSubmit: CreateLeadMutationProps = {
      status: values.status,
      assignedTo: values.assignedTo,
      title: values.title,
      description: values.description,

      contactIds: values.contactIds,
      propertyReference: values.propertyReference,
    }

    if (values.relevance) valuesToSubmit.relevance = values.relevance
    if (values.source) valuesToSubmit.source = values.source
    if (values.type) valuesToSubmit.type = values.type

    mutationCreateLead(values)
  }

  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={onSubmit}
        validationSchema={CreateLeadSchema(t)}
        validateOnMount
      >
        {({ isValid, isSubmitting, submitForm }) => {
          return !isMobile ? (
            <CustomModal
              isOpen={!isMobile}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('salesActivity.createSalesActivity')}
              acceptTitle={t('create') || undefined}
              isLoading={isCreatingLeadLoading}
              disableAcceptButton={!isValid || isSubmitting}
            >
              <Body
                t={t}
                realtorsPaginationProps={realtorsPaginationProps}
                propertiesPaginationProps={propertiesPaginationProps}
                sellersPaginationProps={sellersPaginationProps}
              />
            </CustomModal>
          ) : (
            <CreateContactDrawer
              isOpen={isMobile}
              onClose={() => handleConfirmationModal(() => onClose)}
              onAccept={submitForm}
              onReject={() => handleConfirmationModal(() => onClose)}
              title={t('salesActivity.createSalesActivity')}
              acceptTitle={t('create') || undefined}
              isLoading={isCreatingLeadLoading}
              disableAcceptButton={!isValid || isSubmitting}
            >
              <Body
                t={t}
                realtorsPaginationProps={realtorsPaginationProps}
                propertiesPaginationProps={propertiesPaginationProps}
                sellersPaginationProps={sellersPaginationProps}
              />
            </CreateContactDrawer>
          )
        }}
      </Formik>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={confirmationModalAcceptFunction}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
    </>
  )
}
