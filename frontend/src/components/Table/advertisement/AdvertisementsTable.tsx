import React from 'react'
import { CustomPagination } from '@/components/Pagination/Pagination'
import {
  Text,
  Flex,
  Box,
  Tbody,
  Td,
  Thead,
  Tr,
  Tooltip,
  Table,
  ButtonProps,
  Button,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { ThWithSort } from '../ThWithSort'
import { SortProps } from '@/types/common'
import { AdvertisementResponse, AD_STATUS } from '@/types/advertisements'
import { Dropdown } from 'rsuite'
import {
  cancelAdvertisement,
  duplicateAdvertisement,
  publishAdvertisement,
} from '@/queries/advertisements'
import { useMutation } from '@tanstack/react-query'
import { ImgCell } from './cells/ImgCell'
import { StatusCell } from './cells/StatusCell'
import { DateCell } from './cells/DateCell'
import { LinkClicksCell } from './cells/LinkClicksCell'
import { PreviewCell } from './cells/PreviewCell'
import { UserCell } from './cells/UserCell'

interface QuickActionProps {
  text: string
  onClick: () => void
  disabled?: boolean
}

const QuickAction = ({ text, onClick, disabled = false }: QuickActionProps) => {
  return (
    <Flex>
      <button
        style={{
          width: '100%',
          textAlign: 'start',
          opacity: disabled ? 0.5 : 1,
          cursor: disabled ? 'not-allowed' : 'pointer',
        }}
        onClick={disabled ? undefined : onClick}
        disabled={disabled}
      >
        <Dropdown.Item
          style={{
            backgroundColor: 'transparent',
            color: 'black',
            fontSize: '16px',
          }}
        >
          {text}
        </Dropdown.Item>
      </button>
    </Flex>
  )
}

const renderIconButton = (
  props: ButtonProps,
  ref: React.Ref<HTMLButtonElement>
) => {
  return (
    <Button
      {...props}
      ref={ref}
      size="roundedXs"
      variant="unstyled"
      aria-label="Click here to open modal for adding various things"
      style={{
        transition: 'transform 0.3s ease',
      }}
    >
      <span className="material-symbols-outlined" style={{ fontSize: '40px' }}>
        more_vert
      </span>
    </Button>
  )
}

interface Header {
  label: string
  minWidth: string
  value?: string
  alignItem?: string
  tooltip?: string | null
}

const QuickActionMenu = ({
  ad,
  mutationPublish,
  mutationCancel,
  mutationDuplicate,
}: {
  ad: AdvertisementResponse
  mutationPublish: (id: number) => void
  mutationCancel: (id: number) => void
  mutationDuplicate: (id: number) => void
}) => {
  const { t } = useTranslation(['common'])

  const getActionsForStatus = (status: string) => {
    switch (status) {
      case AD_STATUS.DRAFT:
        return [
          ...(ad.publishable
            ? [{ text: t('publish'), onClick: () => mutationPublish(ad.id) }]
            : []),
          { text: t('cancel'), onClick: () => mutationCancel(ad.id) },
          { text: t('duplicate'), onClick: () => mutationDuplicate(ad.id) },
        ]
      case AD_STATUS.IN_REVIEW:
        return [
          { text: t('cancel'), onClick: () => mutationCancel(ad.id) },
          { text: t('duplicate'), onClick: () => mutationDuplicate(ad.id) },
        ]
      case AD_STATUS.ACTIVE:
        return [
          { text: t('cancel'), onClick: () => mutationCancel(ad.id) },
          { text: t('duplicate'), onClick: () => mutationDuplicate(ad.id) },
        ]
      case AD_STATUS.COMPLETED:
        return [
          { text: t('duplicate'), onClick: () => mutationDuplicate(ad.id) },
        ]
      default:
        return []
    }
  }

  const actions = getActionsForStatus(ad.status)

  return (
    <Flex justifyContent="end">
      <Dropdown
        placement="bottomEnd"
        renderToggle={(props, ref) => renderIconButton(props, ref)}
        style={{ position: 'relative' }}
      >
        {actions.map((action, index) => (
          <QuickAction
            key={index}
            text={action.text}
            onClick={action.onClick}
          />
        ))}
      </Dropdown>
    </Flex>
  )
}

const AdvertisementsTable = ({
  allAds,
  totalItems,
  pageSize,
  currentPage,
  setCurrentPage,
  onSortChange,
  sortColumn,
  sortDirection,
  showUserColumn = true,
}: {
  allAds: AdvertisementResponse[]
  totalItems: number
  pageSize: number
  currentPage: number
  setCurrentPage: (page: number) => void
  onSortChange?: (sort: SortProps) => void
  showUserColumn?: boolean
} & SortProps) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()

  const headers: Header[] = [
    { label: '', minWidth: '180px', value: 'mainImg' },
    {
      label: t('adsManager.table.headers.status.title'),
      minWidth: '180px',
      value: 'status',
    },
    {
      label: t('adsManager.table.headers.date.title'),
      minWidth: '80px',
      value: 'start_date',
    },
    {
      label: t('adsManager.table.headers.impressions.title'),
      minWidth: '100px',
      tooltip: t('adsManager.table.headers.impressions.tooltip'),
    },
    {
      label: t('adsManager.table.headers.link_clicks.title'),
      minWidth: '100px',
      tooltip: t('adsManager.table.headers.link_clicks.tooltip'),
    },
    { label: t('language'), minWidth: '100px', value: 'language' },
    {
      label: t('adsManager.table.headers.ad_preview.title'),
      minWidth: '200px',
    },
    ...(showUserColumn
      ? [{ label: t('user'), minWidth: '150px', value: 'user' }]
      : []),
    { label: '', minWidth: '40px' },
  ]

  const getAdTitleById = (incoming_id: number): string =>
    allAds.find(({ id }) => id === incoming_id)?.title || ''

  const { mutate: mutationDuplicate } = useMutation({
    mutationKey: ['duplicate'],
    mutationFn: duplicateAdvertisement,
    onSuccess: (_data, id) => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.messages.duplicate.title'),
        description: t('adsManager.messages.duplicate.description', {
          entity: getAdTitleById(id),
        }),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.errors.duplicate.title'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const { mutate: mutationCancel } = useMutation({
    mutationKey: ['cancel'],
    mutationFn: cancelAdvertisement,
    onSuccess: (_data, id) => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.messages.cancel.title'),
        description: t('adsManager.messages.cancel.description', {
          entity: getAdTitleById(id),
        }),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.errors.cancel.title'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const { mutate: mutationPublish } = useMutation({
    mutationKey: ['publish'],
    mutationFn: publishAdvertisement,
    onSuccess: (_data, id) => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.messages.publish.title'),
        description: t('adsManager.messages.publish.description', {
          entity: getAdTitleById(id),
        }),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('adsManager.errors.publish.title'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  return (
    <Box pt={['0', '21px']}>
      <Flex width="100%" overflowX="auto">
        <Table border="1px solid" borderColor="grays.grayBorder">
          <Thead>
            <Tr>
              {headers.map(
                ({ label, minWidth, alignItem, value, tooltip }, index) => (
                  <ThWithSort
                    value={value}
                    sortColumn={sortColumn}
                    sortDirection={sortDirection}
                    onSort={onSortChange}
                    whiteSpace="nowrap"
                    minWidth={minWidth}
                    alignItems={alignItem}
                    key={label + index}
                  >
                    <Flex gap={2}>
                      {t(label)}
                      {tooltip && (
                        <Tooltip label={tooltip}>
                          <Text
                            className="material-symbols-outlined"
                            cursor="pointer"
                            fontWeight={400}
                          >
                            info
                          </Text>
                        </Tooltip>
                      )}
                    </Flex>
                  </ThWithSort>
                )
              )}
            </Tr>
          </Thead>
          <Tbody position="relative">
            {allAds.map((ad: AdvertisementResponse) => {
              return (
                <Tr key={ad.type + ad.id}>
                  <ImgCell ad={ad} />
                  <StatusCell ad={ad} />
                  <DateCell ad={ad} />
                  <Td>{ad.impressions || '-'}</Td>
                  <LinkClicksCell ad={ad} />
                  <Td>{t(`adsManager.languages.${ad.language}`) || '-'}</Td>
                  <PreviewCell ad={ad} />
                  {showUserColumn && <UserCell ad={ad} />}
                  <Td position={'relative'} width="35px">
                    <QuickActionMenu
                      ad={ad}
                      mutationPublish={mutationPublish}
                      mutationCancel={mutationCancel}
                      mutationDuplicate={mutationDuplicate}
                    />
                  </Td>
                </Tr>
              )
            })}
          </Tbody>
        </Table>
      </Flex>
      <Flex
        border="1px solid"
        borderTop="none"
        borderColor="grays.grayBorder"
        justifyContent="space-between"
        alignItems="center"
        height="64px"
        p={['2', '4']}
        direction={['column', 'row']}
      >
        <Text fontSize="15px" color="grays.darkGray">{`${t(
          'viewing'
        )} ${Math.min(currentPage * pageSize, totalItems)} / ${totalItems} ${t(
          'adsManager.advertisements'
        )}`}</Text>
        <CustomPagination
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          handlePageChange={setCurrentPage}
        />
      </Flex>
    </Box>
  )
}

export default AdvertisementsTable
