import { Td } from '@chakra-ui/react'
import { AdvertisementResponse } from '@/types/advertisements'
import { ImageWithFallback } from '@/components/ImageWithFallback'
import { IMAGE_SIZE_XS_WIDTH } from '@/utils/constants'
import placeholder from '@/cache/images/property_placeholder.png'

interface ImgCellProps {
  ad: AdvertisementResponse
}

export const ImgCell = ({ ad }: ImgCellProps) => (
  <Td width="110px" height="70px" position="relative" py={4}>
    <div
      style={{
        width: '110px',
        height: '70px',
        display: 'flex',
        alignItems: 'flex-start',
      }}
    >
      <ImageWithFallback
        alt="main image"
        aria-hidden="true"
        fallback={placeholder.src}
        maxWidth="110px"
        objectFit="cover"
        src={
          ad?.advertisementImages?.[0]
            ? `${ad.advertisementImages[0].url}?width=${IMAGE_SIZE_XS_WIDTH}&fit=crop&crop=top&grayscale=true`
            : placeholder.src
        }
      />
    </div>
  </Td>
)
