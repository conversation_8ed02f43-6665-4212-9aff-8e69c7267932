import React from 'react'
import { Flex, Td, Text } from '@chakra-ui/react'
import { AdvertisementResponse } from '@/types/advertisements'
import { CustomAvatar } from '@/components/Avatar/CustomAvatar'
import { IMAGE_SIZE_AVATAR_WIDTH_MD } from '@/utils/constants'
import avatarPlaceholder from '@/cache/images/profile.png'
import { getFormattedImageUrl } from '@/utils/getFormattedImageUrl'

interface UserCellProps {
  ad: AdvertisementResponse
}

export const UserCell = ({ ad }: UserCellProps) => {
  return (
    <Td>
      <Flex justifyContent="left" gap={2} textAlign="center">
        <CustomAvatar
          src={
            getFormattedImageUrl({
              src: ad.owner?.photoUrl,
              width: IMAGE_SIZE_AVATAR_WIDTH_MD,
            }) || avatarPlaceholder.src
          }
          name="placeholder"
          bg={'white'}
          showBorder
        />

        <Flex direction="column" mr={2} justifyContent="center">
          <Text
            fontSize="14px"
            textAlign="left"
          >{`${ad.owner.firstName} ${ad.owner.lastName}`}</Text>
        </Flex>
      </Flex>
    </Td>
  )
}
