import React, { Dispatch, SetStateAction, useMemo, useState } from 'react'

import {
  Text,
  Flex,
  Box,
  Tbody,
  Th,
  Thead,
  Tr,
  Table,
  Td,
  InputGroup,
  InputRightElement,
  Input,
  Badge,
  Link,
  useToast,
  useBreakpointValue,
  Stack,
} from '@chakra-ui/react'
import {
  useMutation,
  useQuery,
  useQueryClient,
  keepPreviousData,
} from '@tanstack/react-query'

import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'
import { CustomPagination } from '../Pagination/Pagination'
import { ThWithSort } from './ThWithSort'
import { SortProps } from '@/types/common'
import { useSearchParams } from 'next/navigation'
import { useDebounce } from 'use-debounce'
import { Formik } from 'formik'
import {
  EXPENSE_TRANSACTION_STATUS,
  EXPENSE_TRANSACTION_TYPE,
  ExpenseTransactionFilterParams,
  ExpenseTransactionRead,
  ExpenseTransactionTargetEntity,
  ExpenseTransactionTargetRead,
  ExpenseTransactionTargetsProps,
} from '@/types/expense'
import useFilter from '@/hooks/useFilter'
import {
  getStripeCardDetails,
  listExpenseTransactions,
  removeCard,
} from '@/queries/expense'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import { CustomAvatar } from '../Avatar/CustomAvatar'
import { IMAGE_SIZE_AVATAR_WIDTH_MD } from '@/utils/constants'
import avatarPlaceholder from '@/cache/images/profile.png'
import { getFormattedImageUrl } from '@/utils/getFormattedImageUrl'
import { ConnectCardBanner } from '../ExpenseTransaction/ConnectCardBanner'
import { ExpenseSummaryBanner } from '../ExpenseTransaction/ExpenseSummaryBanner'
import { formatDate } from '@/utils/date'
import { AdvertisementsDetailsModal } from '../Advertisements/modal/Details'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import ExpenseTablePlaceholder from '../ExpenseTransaction/ExpenseTablePlaceholder'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { useIsFinland } from '@/hooks/useIsFinland'

const normalizeValueToArray = (value?: string | string[]) => {
  if (typeof value === 'string') return [value]
  if (Array.isArray(value)) return value
  return []
}

const StatusCell = ({ status }: { status: EXPENSE_TRANSACTION_STATUS }) => {
  const { t } = useTranslation(['common'])
  const badgeColor = {
    [EXPENSE_TRANSACTION_STATUS.CANCELLED]: 'badge.cancelled.bg',
    [EXPENSE_TRANSACTION_STATUS.FAILED]: 'badge.cancelled.bg',
    [EXPENSE_TRANSACTION_STATUS.PROCESSING]: 'badge.pending.bg',
    [EXPENSE_TRANSACTION_STATUS.REQUIRES_ACTION]: '#BE6B3C',
    [EXPENSE_TRANSACTION_STATUS.SUCCEEDED]: 'badge.published.bg',
    [EXPENSE_TRANSACTION_STATUS.ON_HOLD]: 'badge.pending.bg',
  }[status]

  const statusTranslations = {
    [EXPENSE_TRANSACTION_STATUS.SUCCEEDED]: t('expenses.status.succeeded'),
    [EXPENSE_TRANSACTION_STATUS.CANCELLED]: t('expenses.status.cancelled'),
    [EXPENSE_TRANSACTION_STATUS.FAILED]: t('expenses.status.failed'),
    [EXPENSE_TRANSACTION_STATUS.PROCESSING]: t('expenses.status.processing'),
    [EXPENSE_TRANSACTION_STATUS.REQUIRES_ACTION]: t(
      'expenses.status.requires_action'
    ),
    [EXPENSE_TRANSACTION_STATUS.ON_HOLD]: t('expenses.status.on_hold'),
  }

  return (
    <Td>
      <Badge
        variant="solid"
        backgroundColor={badgeColor}
        fontWeight="small"
        p={2}
      >
        {statusTranslations[status]}
      </Badge>
    </Td>
  )
}

const ConnectionCell = ({ data }: { data: ExpenseTransactionRead }) => {
  const { t } = useTranslation(['common'])
  const [adToOpen, setAdToOpen] = useState<number | null>(null)
  const router = useRouter()
  const { user, setOrganizationId } = useUserAndOrganization()
  const isFinland = useIsFinland()

  const handlePropertyNavigation = (
    reference: string,
    targetOrgCountryCode: 'FI' | 'ES'
  ) => {
    const targetOrg = user?.organizations.find(
      (org) => org.countryCode === targetOrgCountryCode
    )
    if (!targetOrg) return

    const needsOrgSwitch = (isFinland ? 'FI' : 'ES') !== targetOrgCountryCode

    if (needsOrgSwitch) setOrganizationId(targetOrg.id)

    const navigate = () => router.push(`/properties/${reference}`)
    needsOrgSwitch ? setTimeout(navigate, 100) : navigate()
  }

  const handleCustomRoute = (target: ExpenseTransactionTargetRead) => {
    switch (target.targetType) {
      case ExpenseTransactionTargetEntity.ADVERTISEMENT:
        setAdToOpen(target.targetId)
        break
      case ExpenseTransactionTargetEntity.PROPERTY:
        handlePropertyNavigation(
          target.reference || target.targetId.toString(),
          'ES'
        )
        break
      case ExpenseTransactionTargetEntity.FI_PROPERTY:
        handlePropertyNavigation(
          target.reference || target.targetId.toString(),
          'FI'
        )
        break
      default:
        break
    }
  }
  return (
    <>
      <Td>
        {(data.targets || []).map((target, index) => (
          <React.Fragment key={index}>
            {index > 0 && ', '}
            <Link
              fontWeight="bold"
              color="black"
              textDecoration="underline"
              _hover={{ textDecoration: 'underline' }}
              target="_blank"
              href={
                ExpenseTransactionTargetsProps[target.targetType]?.route?.() ??
                '#'
              }
              onClick={(e) => {
                if (!ExpenseTransactionTargetsProps[target.targetType]?.route) {
                  e.preventDefault()
                  handleCustomRoute(target)
                }
              }}
            >
              {t(
                ExpenseTransactionTargetsProps[target.targetType]
                  ?.translationKey ?? ''
              )}
              {(target.reference || target.targetId) &&
                `: ${target.reference || target.targetId}`}
            </Link>
          </React.Fragment>
        ))}
      </Td>
      {adToOpen && (
        <AdvertisementsDetailsModal
          id={adToOpen}
          onClose={() => setAdToOpen(null)}
        />
      )}
    </>
  )
}

const TypeCell = ({ type }: { type: EXPENSE_TRANSACTION_TYPE }) => {
  const { t } = useTranslation(['common'])

  const typeTranslations = {
    [EXPENSE_TRANSACTION_TYPE.EXPENSE]: t('expenses.type.expense'),
    [EXPENSE_TRANSACTION_TYPE.DEPOSIT]: t('expenses.type.deposit'),
    [EXPENSE_TRANSACTION_TYPE.PAYOUT]: t('expenses.type.payout'),
    [EXPENSE_TRANSACTION_TYPE.GIFT]: t('expenses.type.gift'),
  }

  return <Td fontWeight="bold">{typeTranslations[type]}</Td>
}

interface ExpenseTransactionTableProps extends SortProps {
  filterByMe: boolean
  setShowStripe?: Dispatch<SetStateAction<boolean>>
}

const ExpenseTransactionTable = ({
  filterByMe = false,
  setShowStripe,
}: ExpenseTransactionTableProps) => {
  const router = useRouter()
  const { t } = useTranslation(['common'])
  const searchParams = useSearchParams()
  const params = useMemo(
    () => new URLSearchParams(searchParams),
    [searchParams]
  )

  const PAGE_SIZE = 25
  const { onQueryWithParamsUrl, filter } =
    useFilter<ExpenseTransactionFilterParams>({
      page: '1',
      pageSize: PAGE_SIZE.toString(),
    })
  const [search, setSearch] = useState(params.get('keyword') ?? '')
  const [showRemoveCardModal, setShowRemoveCardModal] = useState<boolean>(false)
  const [debouncedSearch] = useDebounce(search, 1000)
  const toast = useToast()
  const isMobile = useBreakpointValue({ base: true, md: false })
  const queryClient = useQueryClient()

  const querySearchKeyword = router.query.searchKeyword?.toString()

  const { data, isPending } = useQuery({
    queryKey: [
      'advertisements',
      querySearchKeyword,
      debouncedSearch,
      filter.page,
      filter.pageSize,
      filter.statuses,
      filter.types,
      filter.languages,
      filter.keyword,
      filter.sortColumn,
      filter.sortDirection,
    ],
    queryFn: () =>
      listExpenseTransactions({
        ...filter,
        filterByMe: String(filterByMe),
        keyword: debouncedSearch || querySearchKeyword,
      }),
    placeholderData: keepPreviousData,
    enabled: !!filter,
  })

  const { mutate: mutationRemoveCard } = useMutation({
    mutationKey: ['removeCard'],
    mutationFn: removeCard,
    onSuccess: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('expenses.messages.cardRemoved.title'),
        description: t('expenses.messages.cardRemoved.description'),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
      queryClient.invalidateQueries({
        queryKey: ['getStripeCardDetails'],
      })
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        id: 'adDuplicate',
        title: t('expenses.messages.cardRemoved.error'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const handleSortChange = (sort: SortProps) => {
    onQueryWithParamsUrl({
      ...filter,
      sortColumn: sort.sortColumn,
      sortDirection: sort.sortDirection,
    })
  }

  const handleSetCurrentPage = (page: number) => {
    params.set('page', `${page}`)
    router.replace(`${router.pathname}?${params.toString()}`, undefined, {
      shallow: true,
    })
  }

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value
    params.set('keyword', val)
    setSearch(val)
    handleSetCurrentPage(1)
  }

  const handleClearSearch = () => {
    setSearch('')
    params.delete('keyword')
    handleSetCurrentPage(1)
  }

  const statusList = [
    {
      value: EXPENSE_TRANSACTION_STATUS.SUCCEEDED,
      label: t('expenses.status.succeeded'),
    },
    {
      value: EXPENSE_TRANSACTION_STATUS.CANCELLED,
      label: t('expenses.status.cancelled'),
    },
    {
      value: EXPENSE_TRANSACTION_STATUS.FAILED,
      label: t('expenses.status.failed'),
    },
    {
      value: EXPENSE_TRANSACTION_STATUS.PROCESSING,
      label: t('expenses.status.processing'),
    },
    {
      value: EXPENSE_TRANSACTION_STATUS.REQUIRES_ACTION,
      label: t('expenses.status.requires_action'),
    },
  ]

  const typesList = [
    {
      value: EXPENSE_TRANSACTION_TYPE.EXPENSE,
      label: t('expenses.type.expense'),
    },
    {
      value: EXPENSE_TRANSACTION_TYPE.DEPOSIT,
      label: t('expenses.type.deposit'),
    },
    {
      value: EXPENSE_TRANSACTION_TYPE.PAYOUT,
      label: t('expenses.type.payout'),
    },
    {
      value: EXPENSE_TRANSACTION_TYPE.GIFT,
      label: t('expenses.type.gift'),
    },
  ]

  const formatToEuro = (amount: number) => {
    return new Intl.NumberFormat(undefined, {
      style: 'currency',
      currency: 'EUR',
    }).format(amount / 100)
  }

  const hasFilter = useMemo(() => {
    return (
      search.trim() !== '' ||
      (filter.statuses && filter.statuses.length > 0) ||
      (filter.types && filter.types.length > 0)
    )
  }, [search, filter.statuses, filter.types])

  const { deposited, expensed, onHold } = useMemo(() => {
    if (!filterByMe || !data?.records?.length) {
      return { deposited: 0, expensed: 0, onHold: 0 }
    }

    return data.records.reduce(
      (totals, { type, amount, status }) => {
        if (
          [
            EXPENSE_TRANSACTION_TYPE.DEPOSIT,
            EXPENSE_TRANSACTION_TYPE.GIFT,
          ].includes(type)
        ) {
          totals.deposited += amount
        } else {
          // For expense/payout types, check status
          if (
            [
              EXPENSE_TRANSACTION_STATUS.PROCESSING,
              EXPENSE_TRANSACTION_STATUS.REQUIRES_ACTION,
              EXPENSE_TRANSACTION_STATUS.ON_HOLD,
            ].includes(status)
          ) {
            totals.onHold += amount
          } else if (status === EXPENSE_TRANSACTION_STATUS.SUCCEEDED) {
            totals.expensed += amount
          }
          // Note: CANCELLED and FAILED are excluded from all calculations
        }
        return totals
      },
      { deposited: 0, expensed: 0, onHold: 0 }
    )
  }, [data?.records, filterByMe])

  const { data: cardDetails, isPending: cardDetailsIsPending } = useQuery({
    queryKey: ['getStripeCardDetails'],
    queryFn: () => getStripeCardDetails(),
  })

  return (
    <Flex direction="column" px={['4', '8']} height="100%" gap={4} mt={4}>
      {filterByMe && setShowStripe ? (
        cardDetails ? (
          <ExpenseSummaryBanner
            deposited={deposited}
            expensed={expensed}
            onHold={onHold}
            cardDetails={cardDetails || ''}
            onRemoveCard={() => setShowRemoveCardModal(true)}
          />
        ) : (
          <ConnectCardBanner onClick={() => setShowStripe(true)} />
        )
      ) : (
        <></>
      )}
      <Flex width="100%" alignItems="flex-end" wrap="wrap" gap="4">
        {(data?.metadata?.totalCount || hasFilter) && !isPending && (
          <Flex gap="2">
            <Flex width={['100%', '100%', '250px']}>
              <InputGroup size={['md', 'md']} width="100%">
                <Input
                  border="1px solid"
                  borderColor="common.black"
                  placeholder={t('search') ?? undefined}
                  value={search}
                  onChange={handleSearchInput}
                  height={['45px', '40px']}
                  fontSize={['16px', '14px']}
                  paddingRight="40px"
                />
                <InputRightElement
                  height="100%"
                  width="40px"
                  top="50%"
                  transform="translateY(-50%)"
                >
                  <Stack
                    width="100%"
                    height="100%"
                    alignItems="center"
                    justifyContent="center"
                    direction="row"
                  >
                    {search.length === 0 ? (
                      <span className="material-symbols-outlined">search</span>
                    ) : (
                      <Box
                        as="button"
                        type="button"
                        onClick={handleClearSearch}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        width="100%"
                        height="100%"
                      >
                        <span className="material-symbols-outlined">close</span>
                      </Box>
                    )}
                  </Stack>
                </InputRightElement>
              </InputGroup>
            </Flex>
            {hasFilter && (
              <Flex>
                <Formik
                  initialValues={{
                    statuses: normalizeValueToArray(filter.statuses),
                    types: normalizeValueToArray(filter.types),
                    languages: normalizeValueToArray(filter.languages),
                  }}
                  onSubmit={(values) => {
                    const trimmed = search.trim() || undefined
                    onQueryWithParamsUrl({
                      ...filter,
                      ...values,
                      keyword: trimmed,
                      page: '1',
                    })
                  }}
                >
                  {({ values, submitForm }) => (
                    <Flex
                      direction="row"
                      gap="4"
                      overflowX="visible"
                      overflowY="visible"
                      alignItems="flex-end"
                      flexWrap="wrap"
                    >
                      <Flex width="200px">
                        <MultiSelectDropdown
                          data={statusList}
                          name="statuses"
                          value={values.statuses}
                          searchable={false}
                          placement="bottomStart"
                          placeholder="Any"
                          label={t('status')}
                          onClose={submitForm}
                          onClean={submitForm}
                        />
                      </Flex>
                      <Flex width="200px">
                        <MultiSelectDropdown
                          data={typesList}
                          name="types"
                          value={values.types}
                          searchable={false}
                          placement="bottomStart"
                          placeholder="Any"
                          label={t('type')}
                          onClose={submitForm}
                          onClean={submitForm}
                        />
                      </Flex>
                    </Flex>
                  )}
                </Formik>
              </Flex>
            )}
          </Flex>
        )}
      </Flex>
      {data?.records?.length && data?.metadata ? (
        <Box pb={['60px', '0']} width="100%" height="100%" overflowX="hidden">
          <Box width="100%" overflowX="auto">
            <Table
              border="1px solid"
              borderColor="grays.grayBorder"
              minWidth="800px"
            >
              <Thead>
                <Tr>
                  <ThWithSort
                    value="description"
                    sortColumn={filter.sortColumn}
                    sortDirection={filter.sortDirection}
                    onSort={handleSortChange}
                    minWidth="100px"
                  >
                    {t('reason')}
                  </ThWithSort>
                  <ThWithSort
                    value="amount"
                    sortColumn={filter.sortColumn}
                    sortDirection={filter.sortDirection}
                    onSort={handleSortChange}
                    minWidth="100px"
                  >
                    {t('amount')}
                  </ThWithSort>
                  <ThWithSort
                    value="type"
                    sortColumn={filter.sortColumn}
                    sortDirection={filter.sortDirection}
                    onSort={handleSortChange}
                    minWidth="100px"
                  >
                    {t('type')}
                  </ThWithSort>
                  <ThWithSort
                    value="status"
                    sortColumn={filter.sortColumn}
                    sortDirection={filter.sortDirection}
                    onSort={handleSortChange}
                    minWidth="100px"
                  >
                    {t('status')}
                  </ThWithSort>
                  <ThWithSort
                    value="created_at"
                    sortColumn={filter.sortColumn}
                    sortDirection={filter.sortDirection}
                    onSort={handleSortChange}
                    minWidth="100px"
                  >
                    {t('date')}
                  </ThWithSort>
                  {!filterByMe && (
                    <ThWithSort
                      value="user_id"
                      sortColumn={filter.sortColumn}
                      sortDirection={filter.sortDirection}
                      onSort={handleSortChange}
                      minWidth="100px"
                    >
                      {t('user')}
                    </ThWithSort>
                  )}
                  <Th minWidth="100px">{t('connection')}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {data.records.map((transaction, index) => (
                  <Tr key={index} cursor="pointer" height="50px">
                    <Td fontWeight="bold">{transaction.description}</Td>
                    <Td>{formatToEuro(transaction.amount)}</Td>
                    <TypeCell type={transaction.type} />
                    <StatusCell status={transaction.status} />
                    <Td>{formatDate(transaction.createdAt)}</Td>
                    {!filterByMe && (
                      <Td>
                        <Flex justifyContent="left" gap={2} textAlign="center">
                          <CustomAvatar
                            src={
                              getFormattedImageUrl({
                                src: transaction.user?.photoUrl,
                                width: IMAGE_SIZE_AVATAR_WIDTH_MD,
                              }) || avatarPlaceholder.src
                            }
                            name="placeholder"
                            bg="white"
                            showBorder
                          />

                          <Flex
                            direction={'column'}
                            mr={2}
                            justifyContent="left"
                          >
                            <Text
                              fontSize="14px"
                              textAlign="left"
                            >{`${transaction.user.firstName} ${transaction.user.lastName}`}</Text>
                          </Flex>
                        </Flex>
                      </Td>
                    )}
                    <ConnectionCell data={transaction} />
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
          <Flex
            border="1px solid"
            borderTop="none"
            borderColor="grays.grayBorder"
            justifyContent="space-between"
            alignItems="center"
            height="64px"
            p={['2', '4']}
            direction={['column', 'row']}
          >
            <Text fontSize="15px" color="grays.darkGray">{`${t(
              'viewing'
            )} ${Math.min(
              Number(params.get('page') || 1) * data.metadata.pageSize,
              data.metadata.totalCount
            )} / ${data.metadata.totalCount} ${t(
              'expenses.transactions'
            )}`}</Text>
            <CustomPagination
              totalItems={data.metadata.totalCount}
              pageSize={data.metadata.pageSize}
              currentPage={Number(params.get('page'))}
              handlePageChange={handleSetCurrentPage}
            />
          </Flex>
        </Box>
      ) : (
        <Flex
          flex={1}
          width="100%"
          alignItems="center"
          justifyContent="center"
          minHeight="300px"
        >
          <ExpenseTablePlaceholder
            buttonAction={() => setShowStripe?.(true)}
            hasCard={!!cardDetails}
            filterByMe={filterByMe}
            isPending={cardDetailsIsPending}
          />
        </Flex>
      )}
      <ConfirmationModal
        isOpen={showRemoveCardModal}
        title={t('expenses.modal.removeCard.title')}
        message={t('expenses.modal.removeCard.message')}
        isCentered={true}
        onAccept={() => {
          setShowRemoveCardModal(false)
          mutationRemoveCard(undefined)
        }}
        onReject={() => setShowRemoveCardModal(false)}
        hideText={false}
      />
    </Flex>
  )
}

export default ExpenseTransactionTable
