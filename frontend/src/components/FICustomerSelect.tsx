import { ContactType, SchemaContactListRead } from '@/generated-types/api'
import { useIsFinland } from '@/hooks/useIsFinland'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { getContact, getContacts } from '@/queries/contact'
import { getUser, getUsers } from '@/queries/users'
import { getUserFullName } from '@/utils/getUserFullName'
import { CloseIcon, SearchIcon } from '@chakra-ui/icons'
import {
  Badge,
  Box,
  BoxProps,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  IconButton,
  Input,
  InputGroup,
  InputRightElement,
  List,
  ListItem,
  Spinner,
  Stack,
  Text,
  useOutsideClick,
} from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { useField } from 'formik'
import { debounce } from 'lodash'
import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'
import AnimatedContainer from './AnimatedContainer/AnimatedContainer'
import { CreateContactV2 } from './Contact/ContactFormV2'
import { EditContactForm } from './Contact/EditContactForm'
import { EditContactFormV2 } from './Contact/EditContactFormV2'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'

interface Props {
  label: string
  name: string
  querySource: 'contacts' | 'users'
  filterContactFn?: (contact) => boolean
  extraInput?: React.FC<{
    index: number
  }>
  singleSelect?: boolean
  containerProps?: BoxProps
  required?: boolean
}

type IdOnly = number
type ObjectWithId = { id: number; [key: string]: string | number }

const FiCustomerSelect = ({
  label,
  name,
  querySource,
  filterContactFn,
  extraInput: ExtraInput,
  singleSelect = false,
  containerProps,
  required = false,
}: Props) => {
  const { organization } = useUserAndOrganization()
  const { t } = useTranslation(['common'])
  const [field, meta, helpers] = useField<(number | ObjectWithId)[]>(name)
  const { value: values = [] } = field
  const { setValue, setTouched } = helpers

  const extractId = (item: IdOnly | ObjectWithId) =>
    typeof item === 'object' ? item.id : item

  const addItem = (id: number) => {
    setTouched(true)
    if (singleSelect) {
      setValue([ExtraInput ? { id } : id], true)
      return
    }

    const exists = values.some((v) =>
      typeof v === 'object' ? v.id === id : v === id
    )
    if (!exists) {
      setValue([...values, ExtraInput ? { id } : id], true)
    }
  }

  const removeItem = (id: number) => {
    setTouched(true)
    setValue(
      values.filter((v) => (typeof v === 'object' ? v.id !== id : v !== id)),
      true
    )
  }
  const contactQueryFn = async (params) => {
    const response =
      querySource === 'contacts'
        ? await getContacts({ ...params, organizationId: organization?.id })
        : await getUsers({ ...params, organizationId: organization?.id })

    const records = response.records.map((item) =>
      querySource === 'contacts'
        ? {
            id: item.id,
            name:
              item.type === ContactType.Person
                ? `${item.firstName} ${item.lastName}`
                : item.name,
            email: item.email,
            phoneNumbers: item.phoneNumbers,
            type: item.type ?? 'Person',
          }
        : {
            id: item.id,
            name: `${item.firstName} ${item.lastName}`,
            email: item.email,
            phoneNumbers: [item.phoneNumber],
            type: 'user',
          }
    )

    return {
      records:
        querySource === 'contacts' && filterContactFn
          ? records.filter(filterContactFn)
          : records,
      metadata: response.metadata,
    }
  }

  const { paginationProps: resultPaginationProps } = usePaginationDropdownData({
    queryKey: ['contacts-or-users', querySource],
    queryFn: (params) => contactQueryFn(params),
    defaultParams: {
      keyword: '',
      page: 1,
      pageSize: 10,
    },
  })

  const {
    loading,
    data: results,
    onSearch: onSearchSellers,
    loadMore,
  } = resultPaginationProps

  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [createContactFormVisible, setCreateContactFormVisible] =
    useState(false)

  const menuRef = useRef<HTMLDivElement>(null)
  const listRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleScroll = () => {
    if (!listRef.current) return
    const { scrollTop, scrollHeight, clientHeight } = listRef.current

    if (scrollTop + clientHeight >= scrollHeight - 20) {
      loadMore()
    }
  }

  useOutsideClick({
    ref: containerRef,
    handler: () => {
      if (!createContactFormVisible) setIsOpen(false)
    },
  })

  const search = debounce(async (q) => {
    await onSearchSellers(q)
  }, 300)

  useEffect(() => {
    search(query)
    return () => search.cancel()
  }, [query, search])

  const handleCreateContactFormSuccess = (contact: SchemaContactListRead) => {
    addItem(contact.id)
    setCreateContactFormVisible(false)
    setIsOpen(false)
  }

  const handleMenuBlur = (e: React.FocusEvent) => {
    const focusMovingTo = e.relatedTarget as HTMLElement
    const isMovingToContainer = menuRef.current?.contains(focusMovingTo)

    if (!isMovingToContainer) {
      setIsOpen(false)
    }
  }

  const isInvalid = !!meta.error && meta.touched

  return (
    <>
      <Box
        width="100%"
        padding="1rem 0"
        bg="primary.main"
        borderRadius="md"
        ref={containerRef}
        {...containerProps}
      >
        <FormControl
          padding="0 1rem"
          isInvalid={isInvalid}
          isRequired={required}
        >
          <FormLabel>{label}</FormLabel>
          <InputGroup>
            <Input
              name={name}
              placeholder={t('searchByNameEmailOrNumber') ?? ''}
              value={query}
              autoComplete="off"
              onChange={(e) => setQuery(e.target.value)}
              onFocus={() => setIsOpen(true)}
              onBlur={handleMenuBlur}
              style={{ paddingRight: 35 }}
            />
            <InputRightElement height="100%" width="fit-content" mr="4">
              {loading ? (
                <Spinner size="sm" />
              ) : (
                <SearchIcon cursor="pointer" />
              )}
            </InputRightElement>
          </InputGroup>

          <Box
            position="relative"
            mt="0.5rem"
            onBlur={handleMenuBlur}
            ref={menuRef}
          >
            <AnimatedContainer expanded={isOpen}>
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bg="white"
                borderRadius="md"
                boxShadow="md"
                border="1px solid"
                borderColor="gray.200"
                zIndex="9999"
                maxHeight="15rem"
                display="flex"
                flexDirection="column"
              >
                <Box
                  ref={listRef}
                  onScroll={handleScroll}
                  overflowY="auto"
                  flex="1 1 auto"
                >
                  <List>
                    {results.length > 0 &&
                      results.map((row) => {
                        const isSelected = values.some(
                          (v) => extractId(v) === row.id
                        )
                        return (
                          <SearchResultListItem
                            key={row.id}
                            id={row.id}
                            name={row.name}
                            email={row.email}
                            phoneNumbers={row.phoneNumbers}
                            type={row.type}
                            isSelected={isSelected}
                            onClick={() => {
                              setQuery('')
                              addItem(row.id)
                              setIsOpen(false)
                            }}
                          />
                        )
                      })}
                    {results.length === 0 && !loading && (
                      <ListItem pt="1rem" textAlign="center">
                        <Text>{t('noSearchResults')}</Text>
                      </ListItem>
                    )}
                  </List>
                </Box>
                <Box
                  borderTop="1px solid"
                  borderColor="gray.200"
                  bg="white"
                  p="0.5rem"
                  bottom="0"
                  minHeight="3rem"
                  display="flex"
                >
                  {loading ? (
                    <Spinner size="sm" m="auto" />
                  ) : (
                    querySource === 'contacts' && (
                      <Button
                        variant="transparent"
                        border="none"
                        color="#BE6B3C"
                        onClick={() => setCreateContactFormVisible(true)}
                        fontWeight="bold"
                      >
                        {t('createContact.title')}
                      </Button>
                    )
                  )}
                </Box>
              </Box>
            </AnimatedContainer>
          </Box>

          <Stack gap="0.5rem">
            {values.map((v, index) => {
              const id = extractId(v)
              return querySource === 'contacts' ? (
                <SelectedContactItem
                  key={id}
                  contactId={id}
                  onRemove={() => removeItem(id)}
                  name={name}
                  index={index}
                >
                  {ExtraInput && <ExtraInput index={index} />}
                </SelectedContactItem>
              ) : (
                <SelectedUserItem
                  key={id}
                  userId={id}
                  onRemove={() => removeItem(id)}
                />
              )
            })}
          </Stack>
          {isInvalid && renderError(meta.error)}
        </FormControl>
      </Box>

      {createContactFormVisible && (
        <CreateContactV2
          onClose={() => setCreateContactFormVisible(false)}
          onSuccess={handleCreateContactFormSuccess}
        />
      )}
    </>
  )
}

const SearchResultListItem = ({
  id,
  name,
  email,
  phoneNumbers,
  type,
  isSelected,
  onClick,
  onBlur,
}: {
  id: number
  name: string
  email: string
  phoneNumbers: string[]
  type: ContactType | 'user'
  isSelected: boolean
  onClick?: () => void
  onBlur?: (e: React.FocusEvent) => void
}) => (
  <ListItem
    opacity={isSelected ? 0.5 : 1}
    tabIndex={isSelected ? -1 : 0}
    key={`${type}-${id}`}
    p="0.5rem 1rem"
    _hover={{ bg: 'gray.100', cursor: 'pointer' }}
    _focus={{ bg: 'gray.100', outline: '0px' }}
    _notLast={{
      borderBottom: '1px solid',
      borderColor: 'gray.200',
    }}
    onClick={isSelected ? undefined : onClick}
    onBlur={onBlur}
    onKeyDown={(e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        if (!isSelected) onClick?.()
      }
    }}
  >
    <ResultListItemContent
      name={name}
      type={type}
      email={email}
      phoneNumbers={phoneNumbers}
    />
  </ListItem>
)

const SelectedContactItem = ({
  contactId,
  onRemove,
  children,
}: {
  contactId: number
  name: string
  index: number
  onRemove: (contactId: number) => void
  extraInput?: React.FC<{ name: string }>
  children?: React.ReactNode
}) => {
  const isFinland = useIsFinland()
  const { data: contact, isPending } = useQuery({
    queryKey: ['contact', contactId],
    queryFn: () => getContact(String(contactId)),
  })

  if (isPending || !contact) return null

  return (
    <AnimatedContainer expanded={true}>
      <Box borderRadius="md" bg="white" p="1rem">
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <ResultListItemContent
            name={contact.name}
            type={contact.type}
            email={contact.email ?? ''}
            phoneNumbers={contact.phoneNumbers}
          />
          <Stack direction="row" alignItems="center" gap="none">
            {isFinland ? (
              <EditContactFormV2 contactData={contact} iconButton />
            ) : (
              <EditContactForm contactData={contact} iconButton />
            )}
            <IconButton
              aria-label="delete"
              variant="transparent"
              border="none"
              icon={<CloseIcon />}
              onClick={() => onRemove(contact.id)}
            />
          </Stack>
        </Stack>
        {children}
      </Box>
    </AnimatedContainer>
  )
}

const ResultListItemContent = ({
  name,
  type,
  email,
  phoneNumbers,
}: Pick<SchemaContactListRead, 'name' | 'email' | 'phoneNumbers'> & {
  type: ContactType | 'user' | null
}) => {
  const { t } = useTranslation(['common'])
  return (
    <Stack gap="0.125rem">
      <Flex gap="0.5rem">
        <Text fontWeight={600}>{name}</Text>
        <Badge color="primary" p="0.5rem" fontWeight="light">
          {type === 'user'
            ? t('user')
            : t(`enums.contactType.${type ?? 'notSpecified'}`)}
        </Badge>
      </Flex>
      <Flex alignItems="center" gap="0.5rem">
        <Text fontSize="xs">{email}</Text>
        <Text fontSize="xs" marginTop="0">
          {phoneNumbers.join(', ') ?? ''}
        </Text>
      </Flex>
    </Stack>
  )
}

const SelectedUserItem = ({
  userId,
  onRemove,
}: {
  userId: number
  onRemove: (userId: number) => void
}) => {
  const { t } = useTranslation(['common'])
  const { data: user, isPending } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => getUser(String(userId)),
  })

  if (isPending || !user) return null

  const name = getUserFullName(user) ?? t('missingName')
  return (
    <AnimatedContainer expanded={true}>
      <Box borderRadius="md" bg="white" p="1rem">
        <Flex justifyContent="space-between" alignItems="center">
          <Stack>
            <Flex gap="0.5rem">
              <Heading size="md" fontFamily="Avenir">
                {name}
              </Heading>
              <Badge p={2} fontWeight="light">
                {t('realtor')}
              </Badge>
            </Flex>
            <Flex alignItems="center" gap="0.5rem">
              <Text fontFamily="Avenir" fontSize="xs">
                {user.email}
              </Text>
              <Text fontFamily="Avenir" fontSize="xs">
                {user.phoneNumber ?? ''}
              </Text>
            </Flex>
          </Stack>
          <IconButton
            width="1rem"
            aria-label="delete"
            variant="transparent"
            border="none"
            icon={<CloseIcon />}
            onClick={() => onRemove(user.id)}
          />
        </Flex>
      </Box>
    </AnimatedContainer>
  )
}

export default FiCustomerSelect

const renderError = (error) => {
  return error && typeof error === 'string' ? (
    <FormErrorMessage
      sx={{
        color: 'red.500',
        fontSize: 'sm',
        mt: 1,
        fontWeight: 'medium',
      }}
    >
      {error}
    </FormErrorMessage>
  ) : null
}
