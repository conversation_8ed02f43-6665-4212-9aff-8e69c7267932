import React from 'react'
import { Box, Button, Stack, Text } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'

interface ActionButton {
  label: string
  onClick: () => void
  disabled?: boolean
  isLoading?: boolean
  buttonVariant?: string
}

export interface FormFooterProps {
  actions: ActionButton[]
  nextStep?: string
  description?: string
  goToPreviousStep?: () => void
  canGoToPreviousStep?: boolean
}

export const FormFooter = ({
  actions,
  nextStep,
  description,
  goToPreviousStep,
  canGoToPreviousStep = true,
}: FormFooterProps) => {
  const { t } = useTranslation(['common'])
  return (
    <Box
      zIndex={999}
      width={'100%'}
      position="fixed"
      bottom="0"
      right="0"
      padding="1.5rem"
      background="white"
      borderTop={'1px solid #E2E2E2'}
    >
      <Stack direction="row" justifyContent="space-between">
        <Stack direction="column" flex="5" spacing="none">
          {goToPreviousStep && (
            <Box>
              <Button
                onClick={goToPreviousStep}
                isDisabled={!canGoToPreviousStep}
                p={5}
              >
                {t('back')}
              </Button>
            </Box>
          )}
          {nextStep && (
            <Text fontWeight="bold">
              {t('nextStep')}: {nextStep}
            </Text>
          )}
          {description && <Text mt={0}>{description}</Text>}
        </Stack>
        <Stack direction="row" alignItems="center">
          {actions.map(
            ({ label, onClick, disabled, isLoading, buttonVariant }, index) => (
              <Button
                key={index}
                onClick={onClick}
                isLoading={isLoading}
                isDisabled={disabled}
                mr={index < actions.length - 1 ? 4 : 0}
                p={5}
                variant={buttonVariant}
              >
                {label}
              </Button>
            )
          )}
        </Stack>
      </Stack>
    </Box>
  )
}

export default FormFooter
