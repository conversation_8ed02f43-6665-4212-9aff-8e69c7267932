import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { PropsWithChildren } from 'react'
import { useTranslation } from 'next-i18next'
import { Stack, StackProps } from '@chakra-ui/react'
import FormFooter, { FormFooterProps } from '../Footer/FormFooter'

export const StepContentWithFooter = ({
  children,
  footerProps,
  ...props
}: PropsWithChildren<{
  footerProps?: Partial<FormFooterProps>
}> &
  StackProps) => {
  const { activeStep, goToNextStep, goToPreviousStep, allowedSteps } =
    useMultiStepForm()
  const { t } = useTranslation(['common'])

  const footerActions = footerProps?.actions || [
    {
      label: t('next'),
      disabled: !allowedSteps.includes(activeStep + 1),
      onClick: goToNextStep,
    },
  ]

  return (
    <Stack paddingBottom={140} {...props}>
      {children}
      <FormFooter
        goToPreviousStep={activeStep > 0 ? goToPreviousStep : undefined}
        canGoToPreviousStep={allowedSteps.includes(activeStep - 1)}
        actions={footerActions}
        {...footerProps}
      />
    </Stack>
  )
}
