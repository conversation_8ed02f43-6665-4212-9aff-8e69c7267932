import { Box, Stack, useSteps } from '@chakra-ui/react'
import { ProgressStepper } from '../Stepper/ProgressStepper'
import { useMobile } from '@/hooks/useMobile'
import {
  MultiStepFormContext,
  MultiStepFormProvider,
} from '@/hooks/useMultiStepForm'
import { PropsWithChildren, useCallback, useMemo } from 'react'

export const MultiStepFormContainer = ({
  children,
  steps,
  initialStep = 0,
  allowedSteps,
}: PropsWithChildren<MultiStepFormContainerProps>) => {
  const isMobile = useMobile()

  const { activeStep, setActiveStep } = useSteps({
    index: getValidInitialStep(steps, initialStep),
    count: steps.length,
  })

  const setActiveStepIfAllowed = useCallback(
    (step: number, overrideAllowedSteps?: number[]) => {
      const stepsToCheck = overrideAllowedSteps ?? allowedSteps
      if (!stepsToCheck.includes(step)) {
        return
      }
      setActiveStep(step)
    },
    [allowedSteps, setActiveStep]
  )

  const goToNextStep = useCallback<MultiStepFormContext['goToNextStep']>(
    (options?: { overrideAllowedSteps?: number[] }) => {
      const nextStep = nextStepOrMax(steps, activeStep)
      setActiveStepIfAllowed(nextStep, options?.overrideAllowedSteps)
    },
    [activeStep, steps, setActiveStepIfAllowed]
  )

  const goToPreviousStep = useCallback<
    MultiStepFormContext['goToPreviousStep']
  >(() => {
    const previousStep = previousStepOrMin(activeStep)
    setActiveStepIfAllowed(previousStep)
  }, [activeStep, setActiveStepIfAllowed])

  const contextValue = useMemo<MultiStepFormContext>(
    () => ({
      activeStep,
      goToNextStep,
      goToPreviousStep,
      allowedSteps,
    }),
    [activeStep, goToNextStep, goToPreviousStep, allowedSteps]
  )

  if (steps.length === 0) {
    throw new Error('Empty steps array')
  }

  return (
    <MultiStepFormProvider value={contextValue}>
      <Stack direction={isMobile ? 'column' : 'row'} padding="0.5rem">
        <Stack direction="column" flex="2">
          <Box padding="1rem">
            <ProgressStepper
              steps={steps}
              activeStep={activeStep}
              allowedSteps={allowedSteps}
              onClickStep={setActiveStepIfAllowed}
            />
          </Box>
        </Stack>
        <Stack direction="column" flex="8">
          {children}
        </Stack>
      </Stack>
    </MultiStepFormProvider>
  )
}

interface Step {
  title: string
}
interface MultiStepFormContainerProps {
  steps: Step[]
  allowedSteps: number[]
  initialStep?: number
}

const getValidInitialStep = (steps: Step[], initialStep: number) =>
  Math.max(0, Math.min(initialStep, steps.length - 1))

const nextStepOrMax = (steps: Step[], activeStep: number) =>
  Math.min(activeStep + 1, steps.length - 1)

const previousStepOrMin = (activeStep: number) => Math.max(activeStep - 1, 0)
