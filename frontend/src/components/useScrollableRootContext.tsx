import { createContext, PropsWith<PERSON>hildren, useContext, useRef } from 'react'

type ScrollableRootContextValue = {
  rootRef: React.RefObject<HTMLElement>
}

const scrollableRootContext = createContext<ScrollableRootContextValue>({
  rootRef: { current: null },
})

export const ScrollableRootContextProvider = ({
  children,
}: PropsWithChildren) => {
  const rootRef = useRef<HTMLDivElement>(null)
  return (
    <scrollableRootContext.Provider value={{ rootRef }}>
      <div ref={rootRef} style={{ position: 'relative' }}>
        {children}
      </div>
    </scrollableRootContext.Provider>
  )
}

export const useScrollableRootContext = () => {
  const context = useContext(scrollableRootContext)
  if (!context) {
    throw new Error(
      'useScrollableRootContext must be used within an ScrollableRootContextProvider'
    )
  }
  return context
}
