import { ReactN<PERSON>, SyntheticEvent, useMemo, useRef, useState } from 'react'
import { useField } from 'formik'
import 'rsuite/dist/rsuite.min.css'
import {
  Flex,
  FormControl,
  FormErrorMessage,
  Spinner,
  useBreakpointValue,
} from '@chakra-ui/react'
import { CheckPicker, CheckPickerProps } from 'rsuite'
import { ItemDataType } from 'rsuite/esm/MultiCascadeTree'
import { get, unionWith } from 'lodash'
import { ListOnItemsRenderedProps, ListOnScrollProps } from 'react-window'
import { sortByAlphabet } from '@/utils/treeNode'
import { useLocaleInfo } from '@/hooks/useLocaleInfo'
import { useScrollableRootContext } from '../useScrollableRootContext'
import { useTranslation } from 'next-i18next'
interface Props<T>
  extends Omit<CheckPickerProps<T>, 'onChange' | 'onSelect' | 'data'> {
  dataTestid?: string
  error?: string | string[] | undefined
  onChange?: (event: SyntheticEvent<Element, Event>, value: T[]) => void
  onSelect?: (
    newValue: T[],
    item: ItemDataType<T>,
    e: SyntheticEvent<Element, Event>
  ) => void
  loadMore?: () => void
  selectionLimit?: number
  data?: ItemDataType<T>[]

  // preOptions use in case component in update Page or modal and data is pagination data
  // preOptions is options of default value, ensure them don't miss
  // because in first render data is not sure have all options of default value

  preOptions?: ItemDataType<T>[]
  itemSize?: number
  sortLabelByAlphabet?: boolean
}

const defaultRenderMenuItem = (label: ReactNode) => {
  return (
    <div
      data-testid={label}
      style={{
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
      }}
    >
      {label}
    </div>
  )
}

export const MultiSelectDropdown = <T extends NonNullable<unknown>>({
  data: dataProps = [],
  selectionLimit = Infinity,
  name,
  dataTestid,
  error,
  preOptions = [],
  placement,
  loadMore,
  renderMenuItem = defaultRenderMenuItem,
  itemSize = 36,
  sortLabelByAlphabet = true,
  ...rest
}: Props<T>) => {
  const {
    onSearch,
    onOpen,
    valueKey = 'value',
    labelKey = 'label',
    onChange,
    onSelect,
    loading,
    onClose,
    onClean,
  } = rest
  const { rootRef } = useScrollableRootContext()
  const isMobile = useBreakpointValue({ base: true, md: false }) || false
  const [field, meta, helpers] = useField<T[] | undefined>({
    name: name || '',
  })
  const isInvalid = error || (meta.touched && meta.error)
  const [optionSelected, setOptionSelected] = useState<
    Record<string, unknown>[]
  >([])
  const { t } = useTranslation()
  const placeholder = rest.placeholder || t('select')
  const initialScrollOffset = useRef(0)
  const localeInfo = useLocaleInfo()

  const data = useMemo(() => {
    const newOptions = optionSelected.concat(preOptions, dataProps)
    return unionWith(newOptions, (a, b) => a[valueKey] === b[valueKey])
  }, [dataProps, optionSelected, preOptions, valueKey])

  const disabledItemValues = (
    selectionLimit > 0 && meta.value?.length === selectionLimit
      ? data
          .map((item) => item[valueKey])
          .filter((item) => !meta.value?.includes(item as T))
      : []
  ) as T[]

  const handleOpen = () => {
    !dataProps.length && onSearch?.('')
    onOpen?.()
  }

  const handleClose = () => {
    initialScrollOffset.current = 0
    onClose?.()
  }

  const handleClean = (e: SyntheticEvent<Element, Event>) => {
    helpers.setTouched(true)
    helpers.setValue([], true)
    setOptionSelected([])
    onClean?.(e)
  }

  const handleSelect = (
    newValue: T[],
    // i think it is issue of library ItemDataType<string | number> must be ItemDataType<T>
    item: ItemDataType<string | number>,
    e: SyntheticEvent<Element, Event>
  ) => {
    if (selectionLimit && newValue.length > selectionLimit) return
    setOptionSelected((pre) =>
      newValue.length > pre.length + preOptions.length
        ? [...pre, item]
        : pre.filter((preItem) => preItem[valueKey] !== item[valueKey])
    )
    onSelect?.(newValue, item as unknown as ItemDataType<T>, e)
    helpers.setTouched(true)
    helpers.setValue(newValue, true)
  }

  const handleChange = (value: T[], event: SyntheticEvent<Element, Event>) => {
    // switch event and value to formik can read onChange/

    const newEvent = {
      ...event,
      target: { ...event.target, name: name || rest.id, value },
    }
    onChange?.(newEvent, value)
  }

  const onItemsRendered = (props: ListOnItemsRenderedProps) => {
    if (props.visibleStopIndex >= data.length - 1) {
      !loading && loadMore?.()
    }
  }

  // keep position scroll when new data append
  const handleScroll = (props: ListOnScrollProps) => {
    initialScrollOffset.current = props.scrollOffset
  }

  const sort = (isGroup: boolean) => {
    if (rest.sort) return rest.sort?.(isGroup)
    return (a: T, b: T) =>
      sortLabelByAlphabet
        ? sortByAlphabet(get(a, labelKey), get(b, labelKey), localeInfo?.locale)
        : 0
  }

  return (
    <FormControl isInvalid={Boolean(isInvalid)}>
      <Flex flexDir="column">
        <CheckPicker
          container={rootRef.current || undefined}
          value={field.value || []}
          {...rest}
          data={data}
          onOpen={handleOpen}
          onSelect={handleSelect}
          onChange={handleChange}
          onClose={handleClose}
          disabledItemValues={disabledItemValues}
          onClean={handleClean}
          style={{ touchAction: 'manipulation' }}
          menuStyle={{ width: '300px' }}
          placement={placement || (isMobile ? 'auto' : 'autoVerticalEnd')}
          loading={loading}
          sticky
          virtualized={!!loadMore}
          renderMenu={(menu) => {
            if (loading) {
              return (
                <>
                  {menu}
                  <div
                    style={{
                      padding: 10,
                      color: '#999',
                      textAlign: 'center',
                    }}
                  >
                    <Spinner />
                  </div>
                </>
              )
            }
            return menu
          }}
          listProps={{
            onItemsRendered,
            initialScrollOffset: initialScrollOffset.current,
            onScroll: handleScroll,
            itemSize,
          }}
          renderMenuItem={renderMenuItem}
          data-testid={dataTestid}
          sort={sort}
          placeholder={placeholder}
        />
        {!!isInvalid && (
          <Flex mt={'8px'}>
            <FormErrorMessage margin={0}>{isInvalid}</FormErrorMessage>
          </Flex>
        )}
      </Flex>
    </FormControl>
  )
}
