/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DescriptionType,
  EditPropertyMutationProps,
  EditPropertyFormTypes,
  LanguageCodeEnum,
  ReadProperty,
  ListingTypeEnum,
  PropertyListingType,
  CommissionTypeEnum,
  IVATaxEnum,
} from '@/types/property'
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerHeader,
  Button,
  DrawerBody,
  Flex,
  FormLabel,
  FormControl,
  Box,
  Text,
  Heading,
  Textarea,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  useBreakpointValue,
  DrawerFooter,
  useToast,
  Divider,
} from '@chakra-ui/react'
import { Formik, FormikErrors, FormikHelpers } from 'formik'
import { useTranslation } from 'next-i18next'
import { InputWithLabel } from '@/components/Form/Input'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import * as Yup from 'yup'
import Radio from '../Form/Radio'
import { MutableRefObject, ReactNode, useEffect, useRef, useState } from 'react'
import {
  ContractTypeField,
  LocationsField,
  PropertyConditionField,
  PropertyTypeField,
  CertificateRatingField,
} from '../Form/PropertyFields'
import {
  getPropertyGarageTypes,
  getPropertyGardenTypes,
  getPropertyPoolTypes,
  getPropertyListingTypes,
  updateProperty,
} from '@/queries/property'
import { getDescriptionsGroupedByLanguage } from '@/utils/getDescriptionsGroupedByLanguage'
import LabeledField from '../Form/LabeledField'
import { MultiSelectDropdown } from '../DropdownMenu/MultiSelectDropdown'
import _ from 'lodash'
import { getFeatures } from '@/queries/feature'
import { AddIcon, MinusIcon } from '@chakra-ui/icons'
import { Checkbox } from 'rsuite'
import { ChangesDetectedToast } from '../ChangesDetectedToast'
import { getUsers } from '@/queries/users'
import { ConfirmationModal } from '../Modal/ConfirmationModal'
import { GenerateOption } from '@/types/common'
import { usePaginationDropdownData } from '@/hooks/usePaginationDropdownData'
import { getContacts } from '@/queries/contact'
import { getFormattedNotes } from './GetFormattedNotes'
import { coordinatesRegex } from '@/utils/regex'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { getFullName } from '@/types/users'

const languages = [
  LanguageCodeEnum.EN,
  LanguageCodeEnum.ES,
  LanguageCodeEnum.FI,
  LanguageCodeEnum.DE,
  LanguageCodeEnum.SV,
] as const

const AccordionHeading = ({
  children,
  isExpanded,
  small,
}: {
  children: ReactNode
  isExpanded: boolean
  small?: boolean
}) => (
  <Flex px={small ? 0 : '6'} py="4" width="full" justifyContent="space-between">
    <Flex alignItems="center" fontSize="20px">
      <Heading whiteSpace="nowrap" variant={small ? 'H3' : 'H2'}>
        {children}
      </Heading>
    </Flex>
    <AccordionButton
      width="fit-content"
      fontSize="16px"
      padding="5px"
      _hover={{ backgroundColor: 'transparent' }}
    >
      {isExpanded ? <MinusIcon /> : <AddIcon />}
    </AccordionButton>
  </Flex>
)

const setUpdatedTranslation = (values: InitialValues) => {
  const translations = [
    { ...values.en, lang: LanguageCodeEnum.EN },
    { ...values.es, lang: LanguageCodeEnum.ES },
    { ...values.fi, lang: LanguageCodeEnum.FI },
    { ...values.de, lang: LanguageCodeEnum.DE },
    { ...values.sv, lang: LanguageCodeEnum.SV },
  ]

  const result = translations?.map((item) => {
    return {
      description: item.description || '',
      language: item.lang,
      tagline: item.tagline || '',
      type: DescriptionType.FULL,
    }
  })

  return result || []
}

const getNestedAreasForLocationsDropdown = (propertyData: ReadProperty) => {
  let nested = ''

  if (propertyData.areaLevel1Id) {
    nested += `${propertyData.areaLevel1Id}:1`
  }

  if (propertyData.areaLevel2Id) {
    nested += `,${propertyData.areaLevel2Id}:2`
  }

  if (propertyData.areaLevel3Id) {
    nested += `,${propertyData.areaLevel3Id}:3`
  }

  if (propertyData.areaLevel4Id) {
    nested += `,${propertyData.areaLevel4Id}:4`
  }

  if (propertyData.areaLevel5Id) {
    nested += `,${propertyData.areaLevel5Id}:5`
  }

  if (nested.length) return [nested]
  return []
}

type TranslationsFormValue = {
  tagline: string
  description: string
}

type FormTypeValues = {
  es: TranslationsFormValue
  fi: TranslationsFormValue
  en: TranslationsFormValue
  de: TranslationsFormValue
  sv: TranslationsFormValue
  nestedAreas: string[]
  coordinates: string
  listingTypes: PropertyListingType[]
  contactsToShowOnDropdown: { id: number; name: string }[]
}

type InitialValues = EditPropertyFormTypes & FormTypeValues

const formatCoordinates = (lat: number | null, long: number | null) => {
  const latitude = lat ? `${lat}` : ''
  const longitude = long ? `, ${long}` : ''
  return `${latitude}${longitude}`
}

const getLatAndLongitudeFromCoordinates = (
  coordinates: string
): { latitude: number | null; longitude: number | null } => {
  const [latitude, longitude] = coordinates.split(',')

  return {
    latitude: parseFloat(latitude) ?? null,
    longitude: parseFloat(longitude) ?? null,
  }
}

const mapListingTypeToOption = (
  listingTypes?: PropertyListingType[]
): GenerateOption[] => {
  if (!listingTypes) {
    return []
  }

  return listingTypes.map((listingType) => ({
    label: listingType.name,
    value: listingType.id,
  }))
}

export const EditPropertyForm = ({
  propertyData,
  mainLanguage,
  isOpen,
  onClose,
  onSuccess,
}: {
  propertyData: ReadProperty
  mainLanguage: LanguageCodeEnum
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}) => {
  const { t } = useTranslation(['common'])
  const toast = useToast()
  const isMobile = useBreakpointValue({ base: true, md: false }, { ssr: true })
  const { isUserAdmin, user } = useUserAndOrganization()

  const { changesDetectedToast } = ChangesDetectedToast()

  const { data: featuresData } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
  })

  const { data: garageTypes } = useQuery({
    queryKey: ['propertyGarageTypes'],
    queryFn: getPropertyGarageTypes,
  })

  const { data: poolTypes } = useQuery({
    queryKey: ['propertyPoolTypes'],
    queryFn: getPropertyPoolTypes,
  })

  const { data: gardenTypes } = useQuery({
    queryKey: ['propertyGardenTypes'],
    queryFn: getPropertyGardenTypes,
  })

  const { data: listingTypesData, isPending: isLoadingListingTypesData } =
    useQuery({
      queryKey: ['propertyListingTypes'],
      queryFn: getPropertyListingTypes,
    })

  const { paginationProps: usersPaginationProps } = usePaginationDropdownData({
    queryKey: ['users'],
    queryFn: (params) =>
      getUsers({
        ...params,
        pageSize: 120,
        onlyActive: true,
      }),
  })

  const { paginationProps: sellersPaginationProps } = usePaginationDropdownData(
    {
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
        }),
    }
  )

  const remainingLanguagesWithoutMainLanguage = languages.filter(
    (lang) => lang !== mainLanguage
  )

  const validationSchema = Yup.object().shape({
    // Property
    reference: Yup.string().required(),
    legacyData: Yup.object()
      .shape({
        newDevelopment: Yup.number().oneOf([0, 1]).nullable(),
      })
      .notRequired(),
    propertyTypeId: Yup.number().required('Building type is required'),
    areaLevel1Id: Yup.number().required('Location is required').nullable(),
    isExclusive: Yup.boolean(),
    isStrandified: Yup.boolean(),
    builtArea: Yup.number().nullable(),
    plotArea: Yup.number().nullable(),
    terraceArea: Yup.number().nullable(),
    interiorArea: Yup.number().nullable(),
    bedrooms: Yup.number().nullable(),
    bathrooms: Yup.number().nullable(),
    // Main description
    es: Yup.object()
      .shape({
        tagline: Yup.string(),
        description: Yup.string(),
      })
      .notRequired(),
    fi: Yup.object()
      .shape({
        tagline: Yup.string(),
        description: Yup.string(),
      })
      .notRequired(),
    en: Yup.object()
      .shape({
        tagline: Yup.string(),
        description: Yup.string(),
      })
      .notRequired(),
    de: Yup.object()
      .shape({
        tagline: Yup.string(),
        description: Yup.string(),
      })
      .notRequired(),
    sv: Yup.object()
      .shape({
        tagline: Yup.string(),
        description: Yup.string(),
      })
      .notRequired(),
    // Sale info
    priceSale: Yup.number()
      .min(1, 'Listing price - Sale should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Sale should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find((e) => e.name === ListingTypeEnum.SALE)?.id
          )
        ) {
          return schema.required('Listing price - Sale is required')
        }
        return schema.nullable()
      }),
    priceRentShortTerm: Yup.number()
      .min(1, 'Listing price - Rent Short Term should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Rent Short Term should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find(
                (e) => e.name === ListingTypeEnum.RENT_SHORT
              )?.id
          )
        ) {
          return schema.required('Listing price - Rent Short Term is required')
        }
        return schema.nullable()
      }),
    priceRentLongTerm: Yup.number()
      .min(1, 'Listing price - Rent Long Term should be a minimum of 1€')
      .max(
        99999999,
        'Listing price - Rent Long Term should be less than or equal to 99 999 999 €'
      )
      .when('listingTypeIds', ([listingTypeIds], schema) => {
        if (
          listingTypeIds?.find(
            (item: number) =>
              item ===
              listingTypesData?.find(
                (e) => e.name === ListingTypeEnum.RENT_LONG
              )?.id
          )
        ) {
          return schema.required('Listing price - Rent Long Term is required')
        }
        return schema.nullable()
      }),

    commissionType: Yup.string().required('Commission type is required'),
    commission: Yup.number().when(
      'commissionType',
      ([commissionType], schema) => {
        if (commissionType === CommissionTypeEnum.PERCENT) {
          return schema
            .min(0, 'Commission has to be between 0% and 100%')
            .max(100, 'Commission has to be between 0% and 100%')
            .required('Commission is required')
        }
        return schema.required('Commission is required')
      }
    ),
    realtorUserIds: Yup.array()
      .of(Yup.number().required())
      .min(1, 'At least one agent is required')
      .test(
        'has-it',
        `${user?.firstName} ${user?.lastName} should be selected`,
        (value) => {
          const isAdminOrAssign =
            value && user && (isUserAdmin || value.includes(user.id))

          return isAdminOrAssign
        }
      ),
    listingTypeIds: Yup.array()
      .of(Yup.number().required())
      .min(1, 'At least one listing type is required'),
    contactIds: Yup.array()
      .of(Yup.number().required())
      .min(1, 'At least one seller is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    postCode: Yup.string().required('Postal code is required'),
    cadastralReference: Yup.string().nullable(),
    // Legal Representative
    legalRepresentative: Yup.object()
      .shape({
        name: Yup.string(),
        mobile: Yup.string(),
        email: Yup.string().email(),
      })
      .notRequired(),
    // Coordinates
    latitude: Yup.number().nullable(),
    longitude: Yup.number().nullable(),
    coordinates: Yup.string()
      .matches(coordinatesRegex, {
        message: t('errors.fieldDoesntMatchTheFormat'),
      })
      .label('Coordinates'),
  })

  const initialValues: InitialValues = {
    ...propertyData,
    featureIds: propertyData.features?.map((feature) => feature.id) ?? [],
    orientationIds:
      propertyData.orientations?.map((orientation) => orientation.id) ?? [],
    listingTypeIds:
      propertyData.listingTypes?.map((listingType) => listingType.id) ?? [],
    garageTypeIds: propertyData.garageTypes?.map((type) => type.id) ?? [],
    poolTypeIds: propertyData.poolTypes?.map((type) => type.id) ?? [],
    gardenTypeIds: propertyData.gardenTypes?.map((type) => type.id) ?? [],
    settingIds: propertyData.settings?.map((setting) => setting.id) ?? [],
    viewIds: propertyData.views?.map((view) => view.id) ?? [],
    es: {
      description:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['es']
          ?.description ?? '',
      tagline:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['es']
          ?.tagline ?? '',
    },
    en: {
      description:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['en']
          ?.description ?? '',
      tagline:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['en']
          ?.tagline ?? '',
    },
    fi: {
      description:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['fi']
          ?.description ?? '',
      tagline:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['fi']
          ?.tagline ?? '',
    },
    de: {
      description:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['de']
          ?.description ?? '',
      tagline:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['de']
          ?.tagline ?? '',
    },
    sv: {
      description:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['sv']
          ?.description ?? '',
      tagline:
        getDescriptionsGroupedByLanguage(propertyData.descriptions)?.['sv']
          ?.tagline ?? '',
    },
    nestedAreas: getNestedAreasForLocationsDropdown(propertyData),
    realtorUserIds:
      propertyData.realtorUsers?.map((realtor) => realtor.id) ?? [],
    city: propertyData.city ?? '',
    postCode: propertyData.privateInfo?.location?.postCode ?? '',
    address: propertyData.privateInfo?.location?.address ?? '',
    contactIds: propertyData.contacts?.map((contact) => contact.id) ?? [],
    coordinates: formatCoordinates(
      propertyData.latitude,
      propertyData.longitude
    ),
    isPublicCoordinates: propertyData.isPublicCoordinates,
    propertyHasCertificate: propertyData.propertyHasCertificate ? 'yes' : 'no',
    keysAndHandoff: {
      ...propertyData.keysAndHandoff,
      hasElectricity: propertyData.keysAndHandoff?.hasElectricity
        ? 'yes'
        : 'no',
      hasLights: propertyData.keysAndHandoff?.hasLights ? 'yes' : 'no',
    },
    renovations: {
      ...propertyData.renovations,
      majorRenovationsPerformed: propertyData.renovations
        ?.majorRenovationsPerformed
        ? 'Renovations have been performed'
        : 'None',
      plannedRenovations: propertyData.renovations?.plannedRenovations
        ? 'There are planned renovations'
        : 'None',
    },
    damagesAndDefects: {
      ...propertyData.damagesAndDefects,
      defectsDamagesRepairObserved: propertyData.damagesAndDefects
        ?.defectsDamagesRepairObserved
        ? 'Defects found'
        : 'None',
      officialPermitsAcquired: propertyData.damagesAndDefects
        ?.officialPermitsAcquired
        ? 'yes'
        : 'no',
      finalInspectionOfChanges: propertyData.damagesAndDefects
        ?.finalInspectionOfChanges
        ? 'yes'
        : 'no',
    },
    otherDamages: {
      ...propertyData.otherDamages,
      waterDamage: propertyData.otherDamages?.waterDamage ? 'yes' : 'no',
      moistureDamage: propertyData.otherDamages?.moistureDamage ? 'yes' : 'no',
      moldOrFungalProblems: propertyData.otherDamages?.moldOrFungalProblems
        ? 'yes'
        : 'no',
      otherSpecialDamages: propertyData.otherDamages?.otherSpecialDamages
        ? 'yes'
        : 'no',
    },
    listingTypes: propertyData.listingTypes ?? [],
    contactsToShowOnDropdown:
      propertyData.contacts?.map((contact) => ({
        id: contact.id,
        name: contact.name,
      })) ?? [],
    videoTours: null,
    videoStreams: null,
  }

  const isFormChanged = (values: EditPropertyFormTypes) => {
    return !_.isEqual(initialValues, values)
  }

  const handleCheckboxesChange = (
    value: boolean,
    fieldName: string,
    id: number,
    name: string,
    currentIds: number[],
    setFieldValue: (
      field: string,
      value: number[],
      shouldValidate?: boolean | undefined
    ) => Promise<void | FormikErrors<InitialValues>>
  ) => {
    if (value) {
      setFieldValue(fieldName, [...currentIds, id])
    } else {
      setFieldValue(
        fieldName,
        [...currentIds].filter((e) => e !== id)
      )
    }
  }

  const handleFeaturesChange = (
    value: boolean,
    featureId: number,
    currentFeatureIds: number[],
    setFieldValue: (
      field: string,
      value: number[],
      shouldValidate?: boolean | undefined
    ) => Promise<void | FormikErrors<InitialValues>>
  ) => {
    if (value) {
      setFieldValue('featureIds', [...currentFeatureIds, featureId])
    } else {
      setFieldValue(
        'featureIds',
        [...currentFeatureIds].filter((e) => e !== featureId)
      )
    }
  }

  const handleOrientationsChange = (
    value: boolean,
    orientationId: number,
    currentOrientationIds: number[],
    setFieldValue: (
      field: string,
      value: number[],
      shouldValidate?: boolean | undefined
    ) => Promise<void | FormikErrors<InitialValues>>
  ) => {
    if (value) {
      setFieldValue('orientationIds', [...currentOrientationIds, orientationId])
    } else {
      setFieldValue(
        'orientationIds',
        [...currentOrientationIds].filter((e) => e !== orientationId)
      )
    }
  }

  const [currentRef, setCurrentRef] = useState('generalInformation')
  const generalInformationRef = useRef<any>()
  const financialInformationRef = useRef<any>()
  const descriptionRef = useRef<any>()
  const additionalDetailsRef = useRef<any>()
  const amenitiesRef = useRef<any>()
  const conditionAndHistoryRef = useRef<any>()

  const scrollerRef = useRef<HTMLDivElement>(null)
  const categoryRef = useRef<string>(currentRef)

  const orientations = [
    {
      name: 'North',
      id: 1,
    },
    {
      name: 'North East',
      id: 2,
    },
    {
      name: 'East',
      id: 3,
    },
    {
      name: 'South East',
      id: 4,
    },
    {
      name: 'South',
      id: 5,
    },
    {
      name: 'South West',
      id: 6,
    },
    {
      name: 'West',
      id: 7,
    },
    {
      name: 'North West',
      id: 8,
    },
  ]

  const scrollToRef = (ref: MutableRefObject<any>) => {
    if (ref.current) {
      ref.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleOnClose = () => {
    setCurrentRef('generalInformation')
    onClose()
  }

  const queryClient = useQueryClient()
  const { mutateAsync: mutateUpdateProperty, isPending } = useMutation({
    mutationFn: updateProperty,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['property', propertyData.reference],
      })
      queryClient.invalidateQueries({
        queryKey: ['propertyValidation'],
      })
      queryClient.invalidateQueries({
        queryKey: ['propertyActivityEvents', propertyData.reference, ''],
      })
      changesDetectedToast()
      onSuccess()
      handleOnClose()
    },
    onError: () => {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        title: t('errors.title'),
        description: t('errors.propertyNotUpdated'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
    },
  })

  const handleScroll = ({ target }: any) => {
    const accordion = target.querySelector('.chakra-accordion')
    const items = accordion.childNodes
    const targetBounds = target.getBoundingClientRect()
    const gap =
      Number(
        window
          .getComputedStyle(accordion)
          .getPropertyValue('row-gap')
          .replace('px', '')
      ) || 20

    for (const item of items) {
      const bounds = item.getBoundingClientRect()
      const scrolled = Math.floor(bounds.y - targetBounds.y)
      const id = item.dataset.id

      if (
        id &&
        categoryRef.current !== id &&
        scrolled <= gap * 0.5 &&
        scrolled > -1 * bounds.height - gap * 0.5
      ) {
        categoryRef.current = id
        setCurrentRef(id)
        break
      }
    }
  }

  useEffect(() => {
    categoryRef.current = currentRef
  }, [currentRef])

  useEffect(() => {
    setTimeout(() => {
      if (scrollerRef.current && isOpen) {
        scrollerRef.current.addEventListener('scroll', handleScroll)
      }
      return () => {
        if (scrollerRef.current) {
          scrollerRef.current.removeEventListener('scroll', handleScroll)
        }
      }
    }, 1000)
  }, [isOpen])

  const onSubmit = async (
    values: InitialValues,
    actions: FormikHelpers<InitialValues>
  ) => {
    actions.setSubmitting(true)
    const newDevelopment = Number(values.legacyData.newDevelopment)

    const { latitude, longitude } = getLatAndLongitudeFromCoordinates(
      values.coordinates
    )

    const descriptions = setUpdatedTranslation(values)
    const formattedValues: EditPropertyMutationProps = {
      ...values,
      descriptions,
      latitude,
      longitude,
      privateInfo: {
        location: {
          address: values.address ?? undefined,
          city: values.city ?? undefined,
          postCode: values.postCode ?? undefined,
        },
      },
      legacyData: { ...values.legacyData, newDevelopment },
      propertyHasCertificate: values.propertyHasCertificate === 'yes',
      keysAndHandoff: {
        ...values.keysAndHandoff,
        hasElectricity: values.keysAndHandoff?.hasElectricity === 'yes',
        hasLights: values.keysAndHandoff?.hasLights === 'yes',
      },
      renovations: {
        ...values.renovations,
        majorRenovationsPerformed:
          values.renovations?.majorRenovationsPerformed ===
          'Renovations have been performed',
        plannedRenovations:
          values.renovations?.plannedRenovations ===
          'There are planned renovations',
      },
      damagesAndDefects: {
        ...values.damagesAndDefects,
        defectsDamagesRepairObserved:
          values.damagesAndDefects?.defectsDamagesRepairObserved ===
          'Defects found',
        officialPermitsAcquired:
          values.damagesAndDefects?.officialPermitsAcquired === 'yes',
        finalInspectionOfChanges:
          values.damagesAndDefects?.finalInspectionOfChanges === 'yes',
      },
      otherDamages: {
        ...values.otherDamages,
        waterDamage: values.otherDamages?.waterDamage === 'yes',
        moistureDamage: values.otherDamages?.moistureDamage === 'yes',
        moldOrFungalProblems:
          values.otherDamages?.moldOrFungalProblems === 'yes',
        otherSpecialDamages: values.otherDamages?.otherSpecialDamages === 'yes',
      },
      isPublicCoordinates: values.isPublicCoordinates,
    }

    mutateUpdateProperty(formattedValues)
      .catch(() => {
        // Handle error if needed
      })
      .finally(() => {
        actions.setSubmitting(false)
      })
  }

  const [showConfirmationModal, setShowConfirmationModal] = useState(false)
  const [confirmationModalAcceptFunction, setConfirmationModalAcceptFunction] =
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useState<() => void>(() => {})

  const handleConfirmationModal = (fn: () => void) => {
    setConfirmationModalAcceptFunction(fn)
    setShowConfirmationModal(true)
  }

  const handleSubmitForm = (
    isValid: boolean,
    submitForm: (() => Promise<void>) & (() => Promise<any>)
  ) => {
    if (!isValid) {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        title: t('errors.title'),
        description: t('errors.missingRequiredInformation'),
        status: 'error',
        duration: 4000,
        isClosable: true,
      })
      return
    }

    submitForm()
  }

  return (
    <>
      <Drawer
        placement="bottom"
        onClose={() => handleConfirmationModal(() => handleOnClose)}
        isOpen={isOpen}
        closeOnOverlayClick={false}
      >
        <DrawerOverlay />
        <Formik
          validationSchema={validationSchema}
          validateOnChange
          validateOnMount
          initialValues={initialValues}
          onSubmit={onSubmit}
        >
          {({
            setFieldValue,
            submitForm,
            isValid,
            values,
            errors,
            touched,
            isSubmitting,
          }) => {
            const showPriceFieldsBasedOnListingType = () => {
              const pricesInputsToShow: JSX.Element[] = []

              for (const listingTypeId of values.listingTypeIds ?? []) {
                switch (
                  listingTypesData?.find((e) => e.id === listingTypeId)?.name
                ) {
                  case ListingTypeEnum.SALE: {
                    pricesInputsToShow.push(
                      <InputWithLabel
                        width={['100%', '100%', '33%']}
                        label={`${t('listingPrice')} - Sale*`}
                        name="priceSale"
                        defaultValue={values.priceSale}
                        rightElement="€"
                        // TODO: add dynamic currency support
                        type="currency"
                        min={1}
                        max={99999999}
                        dataTestId="listingPriceSale-value-input"
                        error={errors.priceSale}
                        touched={touched.priceSale}
                        key="priceSaleKey"
                      />
                    )
                    break
                  }
                  case ListingTypeEnum.RENT_LONG: {
                    pricesInputsToShow.push(
                      <InputWithLabel
                        width={['100%', '100%', '33%']}
                        label={`${t('listingPrice')} - Rent Long Term*`}
                        name="priceRentLongTerm"
                        defaultValue={values.priceRentLongTerm}
                        rightElement="€"
                        // TODO: add dynamic currency support
                        type="currency"
                        min={1}
                        max={99999999}
                        dataTestId="listingPriceRentLongTerm-value-input"
                        error={errors.priceRentLongTerm}
                        touched={touched.priceRentLongTerm}
                        key="priceRentLongTermKey"
                      />
                    )
                    break
                  }
                  case ListingTypeEnum.RENT_SHORT: {
                    pricesInputsToShow.push(
                      <InputWithLabel
                        width={['100%', '100%', '33%']}
                        label={`${t('listingPrice')} - Rent Short Term*`}
                        name="priceRentShortTerm"
                        defaultValue={values.priceRentShortTerm}
                        rightElement="€"
                        // TODO: add dynamic currency support
                        type="currency"
                        min={1}
                        max={99999999}
                        dataTestId="listingPriceRentShortTerm-value-input"
                        error={errors.priceRentShortTerm}
                        touched={touched.priceRentShortTerm}
                        key="priceRentShortTermKey"
                      />
                    )
                    break
                  }
                }
              }

              return (
                <Flex
                  alignItems="flex-start"
                  gap="4"
                  direction={['column', 'row']}
                  wrap={['wrap', 'wrap', 'wrap', 'nowrap']}
                >
                  {pricesInputsToShow.map((priceInput) => priceInput)}
                </Flex>
              )
            }

            return (
              <DrawerContent
                borderRadius="10px 10px 0 0"
                width={['100vw', '1300px']}
                margin={[0, '0 auto']}
                height="90vh"
              >
                <DrawerHeader
                  justifyContent="space-between"
                  alignItems="center"
                  display="flex"
                  borderBottom="1px solid"
                  borderColor="grays.gray6"
                  height="58px"
                >
                  <Heading variant="H2">{`${t(
                    'edit'
                  )} ${values.reference.toUpperCase()}`}</Heading>
                  <div>
                    <Button
                      onClick={() =>
                        handleConfirmationModal(() => handleOnClose)
                      }
                      p="7px"
                      variant="transparent"
                      borderColor="transparent"
                      fontWeight={500}
                      fontSize="16px"
                    >
                      {isMobile ? (
                        <span
                          style={{ fontSize: '30px', marginRight: '-20px' }}
                          className="material-symbols-outlined"
                        >
                          close
                        </span>
                      ) : (
                        t('cancel')
                      )}
                    </Button>
                    {!isMobile && (
                      <Button
                        fontWeight={500}
                        fontSize="16px"
                        width="106px"
                        ml="30px"
                        onClick={() => handleSubmitForm(isValid, submitForm)}
                        isDisabled={
                          !isFormChanged(values) || isPending || isSubmitting
                        }
                      >
                        {t('save')}
                      </Button>
                    )}
                  </div>
                </DrawerHeader>
                <DrawerBody
                  p={['0', '0px 0 0px 20px']}
                  backgroundColor="primary.main"
                  overflow="unset"
                  height="full"
                >
                  <Flex direction="row" height="full">
                    <Flex
                      display={['none', 'flex']}
                      minW={['0', '212px']}
                      pr="4"
                      pt="20px"
                      flexDirection="column"
                    >
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'generalInformation' ? 'black' : 'gray'
                        }
                        opacity={currentRef === 'generalInformation' ? 1 : 0.4}
                        onClick={() => scrollToRef(generalInformationRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.generalInformation')}
                        </Text>
                      </Flex>
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'financialInformation'
                            ? 'black'
                            : 'gray'
                        }
                        opacity={
                          currentRef === 'financialInformation' ? 1 : 0.4
                        }
                        onClick={() => scrollToRef(financialInformationRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.financialInformation')}
                        </Text>
                      </Flex>
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'description' ? 'black' : 'gray'
                        }
                        opacity={currentRef === 'description' ? 1 : 0.4}
                        onClick={() => scrollToRef(descriptionRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.description')}
                        </Text>
                      </Flex>
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'additionalDetails' ? 'black' : 'gray'
                        }
                        opacity={currentRef === 'additionalDetails' ? 1 : 0.4}
                        onClick={() => scrollToRef(additionalDetailsRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.additionalDetails')}
                        </Text>
                      </Flex>
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'amenities' ? 'black' : 'gray'
                        }
                        opacity={currentRef === 'amenities' ? 1 : 0.4}
                        onClick={() => scrollToRef(amenitiesRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.amenities')}
                        </Text>
                      </Flex>
                      <Flex
                        p="5px 15px"
                        cursor="pointer"
                        borderLeft="2px solid"
                        borderColor={
                          currentRef === 'conditionAndHistory'
                            ? 'black'
                            : 'gray'
                        }
                        opacity={currentRef === 'conditionAndHistory' ? 1 : 0.4}
                        onClick={() => scrollToRef(conditionAndHistoryRef)}
                      >
                        <Text variant="verticalNav">
                          {t('editProperty.conditionAndHistory')}
                        </Text>
                      </Flex>
                    </Flex>
                    <Flex
                      position="relative"
                      className="chakra-modal__body__inner"
                      ref={scrollerRef}
                      width="full"
                      pr={['0', '4']}
                      overflowY="scroll"
                      mb={['0', '14']}
                    >
                      <Accordion
                        defaultIndex={[0, 1, 2, 3, 4, 5]}
                        allowMultiple
                        width="full"
                        display="flex"
                        flexDirection="column"
                        pt={[0, 0, 0, '20px']}
                        gap={['10px', '10px', '20px']}
                      >
                        {/* General Information */}
                        <AccordionItem
                          ref={generalInformationRef}
                          data-id="generalInformation"
                          backgroundColor="white"
                          width="full"
                          borderTopWidth={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                {t('editProperty.generalInformation')}
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex flexDirection="column" gap="4">
                                  <Flex
                                    wrap="wrap"
                                    alignItems="flex-start"
                                    gap="4"
                                  >
                                    <InputWithLabel
                                      name="reference"
                                      label={t('editProperty.reference')}
                                      width={['100%', '220px']}
                                      defaultValue={values.reference}
                                      readOnly
                                      onlyUpdateOnBlur
                                    />
                                    <Box width={['100%', '220px']}>
                                      <ContractTypeField placement="bottomStart" />
                                    </Box>
                                    <Box width={['100%', '220px']}>
                                      <LabeledField
                                        label={`${t(
                                          'createProperty.listingType'
                                        )}*`}
                                        width="100%"
                                      >
                                        <MultiSelectDropdown
                                          data={mapListingTypeToOption(
                                            listingTypesData
                                          )}
                                          name="listingTypeIds"
                                          searchable={false}
                                          loading={isLoadingListingTypesData}
                                          error={errors.listingTypeIds}
                                          placement="bottomEnd"
                                        />
                                      </LabeledField>
                                    </Box>
                                    <Box width={['100%', '220px']}>
                                      <LabeledField
                                        label={`${t(
                                          'createProperty.realtors'
                                        )}*`}
                                        width="100%"
                                      >
                                        <MultiSelectDropdown
                                          name="realtorUserIds"
                                          placement="bottomStart"
                                          valueKey="id"
                                          labelKey="name"
                                          {...usersPaginationProps}
                                          preOptions={
                                            propertyData.realtorUsers
                                              ? propertyData.realtorUsers.map(
                                                  (user) => ({
                                                    ...user,
                                                    name: getFullName(user),
                                                  })
                                                )
                                              : []
                                          }
                                          data={usersPaginationProps.data.map(
                                            (user) => ({
                                              ...user,
                                              name: getFullName(user),
                                            })
                                          )}
                                        />
                                      </LabeledField>
                                    </Box>
                                    <Box width={['100%', '220px']}>
                                      <LabeledField label={`${t('sellers')}*`}>
                                        <MultiSelectDropdown
                                          name="contactIds"
                                          placement="bottomStart"
                                          dataTestid="sellers-dropdown"
                                          valueKey="id"
                                          labelKey="name"
                                          selectionLimit={10}
                                          preOptions={
                                            values.contactsToShowOnDropdown ||
                                            []
                                          }
                                          {...sellersPaginationProps}
                                        />
                                      </LabeledField>
                                    </Box>
                                    <Box width={['100%', '220px']}>
                                      <PropertyConditionField
                                        fieldName="condition"
                                        value={values.condition || ''}
                                        placement="bottomEnd"
                                      />
                                    </Box>
                                    <Box width={['100%', '220px']}>
                                      <InputWithLabel
                                        width="100%"
                                        name="hostawayPropertyId"
                                        defaultValue={
                                          values.hostawayPropertyId ?? ''
                                        }
                                        label={`${t(
                                          'editProperty.hostawayPropertyId'
                                        )}`}
                                        error={errors.hostawayPropertyId}
                                        touched={touched.hostawayPropertyId}
                                        onlyUpdateOnBlur
                                      />
                                    </Box>
                                  </Flex>
                                  <Flex>
                                    <Checkbox
                                      name="isStrandified"
                                      checked={values.isStrandified}
                                      onChange={(_event, value) => {
                                        setFieldValue('isStrandified', value)
                                      }}
                                    >
                                      {t('propertyStrandified')}
                                    </Checkbox>
                                  </Flex>
                                  <Flex>
                                    <FormControl>
                                      <FormLabel mb="0">
                                        {t('internalNotes')}
                                      </FormLabel>
                                      <Textarea
                                        width="100%"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Internal notes"
                                        defaultValue={
                                          getFormattedNotes(
                                            values.internalNotes
                                          ) ?? ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'internalNotes',
                                            e.currentTarget.value
                                          )
                                        }
                                      />
                                    </FormControl>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.locationAndAddress')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Box width={['100%', '300px']}>
                                      <LocationsField
                                        isMultiSelect={false}
                                        defaultValue={values.nestedAreas}
                                        error={errors.areaLevel1Id}
                                        placement="bottomStart"
                                        defaultKeyword={
                                          values.city ?? undefined
                                        }
                                      />
                                    </Box>
                                    <Flex
                                      direction={['column', 'row']}
                                      gap="4"
                                      width="100%"
                                    >
                                      <InputWithLabel
                                        width={['100%', '300px']}
                                        name="address"
                                        defaultValue={values.address ?? ''}
                                        label={`${t('streetAddress')}*`}
                                        error={errors.address}
                                        touched={touched.address}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        name="postCode"
                                        defaultValue={values.postCode ?? ''}
                                        label={`${t('postalCode')}*`}
                                        error={errors.postCode}
                                        touched={touched.postCode}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '220px']}
                                        name="city"
                                        value={values.city ?? ''}
                                        label={`${t('city')}*`}
                                        error={errors.city}
                                        touched={touched.city}
                                        readOnly
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>
                                    <InputWithLabel
                                      width={['100%', '300px']}
                                      name="developmentName"
                                      defaultValue={
                                        values.developmentName ?? ''
                                      }
                                      label={`${t('developmentName')}`}
                                      dataTestId="developmentName-value-input"
                                    />
                                  </Flex>
                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.coordinates')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <InputWithLabel
                                      width={['100%', '180px']}
                                      name="coordinates"
                                      placeholder="0.000.000, 0.000.000"
                                      defaultValue={formatCoordinates(
                                        values.latitude,
                                        values.longitude
                                      )}
                                      label={t(
                                        'editProperty.latitudeAndLongitude'
                                      )}
                                      type="text"
                                      error={errors.coordinates}
                                      touched={touched.coordinates}
                                      onlyUpdateOnBlur
                                    />

                                    <Checkbox
                                      style={{ width: '100%' }}
                                      name="isPublicCoordinates"
                                      checked={
                                        values.isPublicCoordinates ?? false
                                      }
                                      onChange={(event, value) =>
                                        setFieldValue(
                                          'isPublicCoordinates',
                                          value
                                        )
                                      }
                                    >
                                      {`${t('editProperty.publicLatLng')}`}
                                    </Checkbox>
                                  </Flex>
                                </Flex>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {/* Financial Information */}
                        <AccordionItem
                          ref={financialInformationRef}
                          data-id="financialInformation"
                          width="full"
                          backgroundColor="white"
                          borderColor="grays.borderColor"
                          borderTop={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                <Text whiteSpace="nowrap">
                                  {t('editProperty.financialInformation')}
                                </Text>
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex flexDirection="column" gap="4">
                                  <Box>
                                    {showPriceFieldsBasedOnListingType()}
                                  </Box>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.commission')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex alignItems="center" gap="4">
                                      <Radio
                                        label={t('editProperty.commissionType')}
                                        name="commissionType"
                                        values={[
                                          {
                                            value: CommissionTypeEnum.PERCENT,
                                            label: t(
                                              'editProperty.percentOfSalePrice'
                                            ),
                                          },
                                          {
                                            value: CommissionTypeEnum.FIXED,
                                            label: t(
                                              'editProperty.fixedAmount'
                                            ),
                                          },
                                        ]}
                                        direction={{
                                          base: 'row',
                                          md: 'row',
                                        }}
                                        defaultValue={
                                          initialValues.commissionType ??
                                          undefined
                                        }
                                      />
                                    </Flex>
                                    <Flex
                                      direction="column"
                                      width="100%"
                                      gap="4"
                                    >
                                      <Flex
                                        direction={['column', 'row']}
                                        gap={4}
                                      >
                                        <InputWithLabel
                                          width={['100%', '140px']}
                                          label={
                                            values.commissionType ===
                                            CommissionTypeEnum.PERCENT
                                              ? `${t('commission')} %`
                                              : `${t('commission')} ${
                                                  values.currency?.toUpperCase() ||
                                                  'EUR'
                                                }`
                                          }
                                          name="commission"
                                          defaultValue={values.commission}
                                          rightElement={
                                            values.commissionType ===
                                            CommissionTypeEnum.PERCENT
                                              ? `%`
                                              : `${
                                                  values.currency?.toUpperCase() ||
                                                  'EUR'
                                                }`
                                          }
                                          type={
                                            values.commissionType ===
                                            CommissionTypeEnum.PERCENT
                                              ? 'number'
                                              : 'currency'
                                          }
                                          error={errors.commission}
                                          touched={
                                            touched.commission ||
                                            !!values.commission
                                          }
                                        />
                                        <Flex alignItems="center" gap="4">
                                          <Radio
                                            label={t('editProperty.ivaTax')}
                                            name="ivaTax"
                                            values={[
                                              {
                                                value: IVATaxEnum.TAX_INCLUDED,
                                                label: t(
                                                  'editProperty.taxIncluded'
                                                ),
                                              },
                                              {
                                                value: IVATaxEnum.TAX_ADDED,
                                                label: t(
                                                  'editProperty.taxAdded'
                                                ),
                                              },
                                              {
                                                value: IVATaxEnum.NO_TAX,
                                                label: t('editProperty.noTax'),
                                              },
                                            ]}
                                            direction={{
                                              base: 'row',
                                              md: 'row',
                                            }}
                                            defaultValue={
                                              initialValues.ivaTax ?? undefined
                                            }
                                          />
                                        </Flex>
                                      </Flex>
                                      <FormControl>
                                        <FormLabel mb="0">
                                          {t('commissionNotes')}
                                        </FormLabel>
                                        <Textarea
                                          width="100%"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Commission notes"
                                          defaultValue={
                                            getFormattedNotes(
                                              values.commissionNotes
                                            ) ?? ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              `commissionNotes`,
                                              e.currentTarget.value
                                            )
                                          }
                                        />
                                      </FormControl>
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.propertyFees')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex
                                      wrap="wrap"
                                      alignItems="center"
                                      gap="4"
                                    >
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t(
                                          'editProperty.communalFees'
                                        )}`}
                                        name="communalFees"
                                        defaultValue={values.communalFees}
                                        rightElement="€/month"
                                        type="number"
                                        min={0}
                                        max={99999999}
                                        error={errors.communalFees}
                                        touched={touched.communalFees}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t('editProperty.ibi')}`}
                                        name="ibi"
                                        defaultValue={values.ibi}
                                        rightElement="€/year"
                                        type="number"
                                        min={0}
                                        max={99999999}
                                        error={errors.ibi}
                                        touched={touched.ibi}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t(
                                          'editProperty.garbageTax'
                                        )}`}
                                        name="garbageTax"
                                        defaultValue={values.garbageTax}
                                        rightElement="€/year"
                                        type="number"
                                        min={0}
                                        max={99999999}
                                        error={errors.garbageTax}
                                        touched={touched.garbageTax}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t('editProperty.waterFee')}`}
                                        name="waterFee"
                                        defaultValue={values.waterFee}
                                        rightElement="€/year"
                                        type="number"
                                        min={0}
                                        max={99999999}
                                        error={errors.waterFee}
                                        touched={touched.waterFee}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t(
                                          'editProperty.electricity'
                                        )}`}
                                        name="electricity"
                                        defaultValue={values.electricity}
                                        rightElement="€/year"
                                        type="number"
                                        min={0}
                                        max={99999999}
                                        error={errors.electricity}
                                        touched={touched.electricity}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>
                                  </Flex>
                                </Flex>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {/* Description */}
                        <AccordionItem
                          ref={descriptionRef}
                          data-id="description"
                          width="full"
                          backgroundColor="white"
                          borderColor="grays.borderColor"
                          borderTop={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                <Text whiteSpace="nowrap">
                                  {t('editProperty.description')}
                                </Text>
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex
                                  rowGap={['10px', '16px']}
                                  direction="column"
                                >
                                  <InputWithLabel
                                    name={`${mainLanguage}.tagline`}
                                    defaultValue={values[mainLanguage].tagline}
                                    label={t('editProperty.title')}
                                    placeholder="Property tagline..."
                                    onlyUpdateOnBlur
                                  />
                                  <FormControl>
                                    <FormLabel mb="0">
                                      {t('description')}
                                    </FormLabel>
                                    <Textarea
                                      width="100%"
                                      whiteSpace="pre-wrap"
                                      overflowWrap="break-word"
                                      placeholder="Full description"
                                      defaultValue={
                                        values[mainLanguage].description
                                      }
                                      onBlur={(e) =>
                                        setFieldValue(
                                          `${mainLanguage}.description`,
                                          e.currentTarget.value
                                        )
                                      }
                                    />
                                  </FormControl>
                                </Flex>
                                <Accordion allowMultiple>
                                  {remainingLanguagesWithoutMainLanguage.map(
                                    (language) => (
                                      <AccordionItem
                                        key={language}
                                        borderColor="transparent"
                                        p="0"
                                      >
                                        {({ isExpanded }) => (
                                          <>
                                            <AccordionHeading
                                              isExpanded={isExpanded}
                                              small={true}
                                            >
                                              <Flex
                                                as="span"
                                                alignItems="center"
                                                textAlign="left"
                                              >
                                                <span>
                                                  {`${t('translation')} ${t(
                                                    language
                                                  )}`}
                                                </span>
                                              </Flex>
                                            </AccordionHeading>

                                            <AccordionPanel pb={4} px="0">
                                              <Flex
                                                rowGap={['10px', '16px']}
                                                direction="column"
                                              >
                                                <InputWithLabel
                                                  name={`${language}.tagline`}
                                                  defaultValue={
                                                    values[language].tagline
                                                  }
                                                  label={t(
                                                    'editProperty.title'
                                                  )}
                                                  placeholder="Property tagline..."
                                                  onlyUpdateOnBlur
                                                />

                                                <FormControl>
                                                  <FormLabel m="0">
                                                    {t('description')}
                                                  </FormLabel>
                                                  <Textarea
                                                    defaultValue={
                                                      values[language]
                                                        .description
                                                    }
                                                    placeholder="Full description"
                                                    onBlur={(e) =>
                                                      setFieldValue(
                                                        `${language}.description`,
                                                        e.currentTarget.value
                                                      )
                                                    }
                                                    whiteSpace="pre-wrap"
                                                    overflowWrap="break-word"
                                                  />
                                                </FormControl>
                                              </Flex>
                                            </AccordionPanel>
                                          </>
                                        )}
                                      </AccordionItem>
                                    )
                                  )}
                                </Accordion>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {/* Additional Information */}
                        <AccordionItem
                          ref={additionalDetailsRef}
                          data-id="additionalDetails"
                          width="full"
                          backgroundColor="white"
                          borderColor="grays.borderColor"
                          borderTop={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                <Text whiteSpace="nowrap">
                                  {t('editProperty.additionalDetails')}
                                </Text>
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex flexDirection="column" gap="4">
                                  <Flex gap="4">
                                    <InputWithLabel
                                      width={['100%', '220px']}
                                      name="cadastralReference"
                                      defaultValue={
                                        values.cadastralReference ?? ''
                                      }
                                      label={t(
                                        'editProperty.cadastralReference'
                                      )}
                                      placeholder={
                                        t('editProperty.cadastralReference') ??
                                        undefined
                                      }
                                      onlyUpdateOnBlur
                                    />
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    gap="2"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t(
                                          'editProperty.buildingSpecifications'
                                        )}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Flex
                                      wrap="wrap"
                                      alignItems="flex-start"
                                      gap="4"
                                    >
                                      <PropertyTypeField
                                        formikFieldName="propertyTypeId"
                                        value={
                                          values.propertyTypeId
                                            ? [values.propertyTypeId.toString()]
                                            : undefined
                                        }
                                        width={['100%', '220px']}
                                        error={errors.propertyTypeId}
                                        isRequiredField
                                        placement="bottomStart"
                                      />
                                      <InputWithLabel
                                        width={['100%', '140px']}
                                        label={`${t(
                                          'editProperty.constructionYear'
                                        )}`}
                                        name="builtYear"
                                        defaultValue={values.builtYear}
                                        error={errors.builtYear}
                                        touched={touched.builtYear}
                                      />
                                      <InputWithLabel
                                        width={['100%', '220px']}
                                        label={`${t(
                                          'editProperty.buildingConstructor'
                                        )}`}
                                        name="buildingConstructor"
                                        defaultValue={
                                          values.buildingConstructor ?? ''
                                        }
                                        error={errors.buildingConstructor}
                                        touched={touched.buildingConstructor}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>

                                    <Flex
                                      wrap="wrap"
                                      alignItems="center"
                                      gap="4"
                                    >
                                      <InputWithLabel
                                        width={['50%', '64px']}
                                        label={`${t('editProperty.floor')}`}
                                        name="floor"
                                        defaultValue={values.floor}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        error={errors.floor}
                                        touched={touched.floor}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['50%', '64px']}
                                        label={`${t(
                                          'editProperty.totalFloors'
                                        )}`}
                                        name="totalFloors"
                                        defaultValue={values.totalFloors}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        error={errors.totalFloors}
                                        touched={touched.totalFloors}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>

                                    <Checkbox
                                      name="buildingHasElevator"
                                      checked={
                                        values.buildingHasElevator ?? false
                                      }
                                      onChange={(event, value) =>
                                        setFieldValue(
                                          'buildingHasElevator',
                                          value
                                        )
                                      }
                                    >
                                      {`${t(
                                        'editProperty.buildingHasElevator'
                                      )}`}
                                    </Checkbox>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    gap="2"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.buildingMaterials')}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Flex
                                      wrap="wrap"
                                      alignItems="center"
                                      gap="4"
                                    >
                                      <InputWithLabel
                                        width={['100%', '220px']}
                                        label={`${t(
                                          'editProperty.foundationAndStructure'
                                        )}`}
                                        name="foundationAndStructure"
                                        defaultValue={
                                          values.foundationAndStructure ?? ''
                                        }
                                        error={errors.foundationAndStructure}
                                        touched={touched.foundationAndStructure}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '220px']}
                                        label={`${t('editProperty.roof')}`}
                                        name="roof"
                                        defaultValue={values.roof ?? ''}
                                        error={errors.roof}
                                        touched={touched.roof}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['100%', '220px']}
                                        label={`${t(
                                          'editProperty.exteriorWalls'
                                        )}`}
                                        name="exteriorWalls"
                                        defaultValue={
                                          values.exteriorWalls ?? ''
                                        }
                                        error={errors.exteriorWalls}
                                        touched={touched.exteriorWalls}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    gap="2"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.energyCertificate')}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Flex alignItems="center" gap="4">
                                      <Radio
                                        label=""
                                        name="propertyHasCertificate"
                                        values={[
                                          {
                                            value: 'no',
                                            label: t(
                                              'editProperty.notApplicable'
                                            ),
                                          },
                                          {
                                            value: 'yes',
                                            label: t(
                                              'editProperty.propertyHasCertificate'
                                            ),
                                          },
                                        ]}
                                        direction={{
                                          base: 'column',
                                          md: 'row',
                                        }}
                                        defaultValue={
                                          initialValues.propertyHasCertificate ??
                                          undefined
                                        }
                                      />
                                    </Flex>

                                    <Flex
                                      justifyContent="start"
                                      alignItems="start"
                                      gap="4"
                                      wrap="wrap"
                                      mt="4"
                                    >
                                      <CertificateRatingField
                                        label={t(
                                          'certificateConsumptionRating'
                                        )}
                                        fieldName="certificateConsumptionRating"
                                        placement="bottomStart"
                                        disabled={
                                          values.propertyHasCertificate === 'no'
                                        }
                                      />
                                      <InputWithLabel
                                        width={['100%', '180px']}
                                        label={`${t(
                                          'editProperty.certificateConsumptionValue'
                                        )}`}
                                        name="certificateConsumptionValue"
                                        defaultValue={
                                          values.certificateConsumptionValue ??
                                          ''
                                        }
                                        error={
                                          errors.certificateConsumptionValue
                                        }
                                        touched={
                                          touched.certificateConsumptionValue
                                        }
                                        rightElement=" Kwh/m² Year"
                                        onlyUpdateOnBlur
                                        readOnly={
                                          values.propertyHasCertificate === 'no'
                                        }
                                      />
                                      <CertificateRatingField
                                        label={t('certificateEmissionRating')}
                                        fieldName="certificateEmissionRating"
                                        placement="bottomStart"
                                        disabled={
                                          values.propertyHasCertificate === 'no'
                                        }
                                      />
                                      <InputWithLabel
                                        width={['100%', '180px']}
                                        label={`${t(
                                          'editProperty.certificateEmissionValue'
                                        )}`}
                                        name="certificateEmissionValue"
                                        defaultValue={
                                          values.certificateEmissionValue ?? ''
                                        }
                                        error={errors.certificateEmissionValue}
                                        touched={
                                          touched.certificateEmissionValue
                                        }
                                        rightElement=" KgCO2/m² Year"
                                        onlyUpdateOnBlur
                                        readOnly={
                                          values.propertyHasCertificate === 'no'
                                        }
                                      />
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.spaceAndSize')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex gap="4" wrap="wrap">
                                      <Flex gap="4" width="100%">
                                        <InputWithLabel
                                          width={['100%', '140px']}
                                          name="builtArea"
                                          defaultValue={values.builtArea}
                                          label={t('editProperty.builtSize')}
                                          type="number"
                                          noDecimals
                                          rightElement={[
                                            'm',
                                            <sup key="interiorRight">2</sup>,
                                          ]}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                        <InputWithLabel
                                          width={['100%', '140px']}
                                          name="plotArea"
                                          defaultValue={values.plotArea}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.plotSize')}
                                          rightElement={[
                                            'm',
                                            <sup key="interiorRight">2</sup>,
                                          ]}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                      <Flex gap="4" width="100%">
                                        <InputWithLabel
                                          width={['100%', '140px']}
                                          name="terraceArea"
                                          defaultValue={values.terraceArea}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.terraceSize')}
                                          rightElement={[
                                            'm',
                                            <sup key="interiorRight">2</sup>,
                                          ]}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                        <InputWithLabel
                                          width={['100%', '140px']}
                                          name="interiorArea"
                                          defaultValue={values.interiorArea}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.interiorSize')}
                                          rightElement={[
                                            'm',
                                            <sup key="interiorRight">2</sup>,
                                          ]}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.rooms')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex gap="4" wrap="wrap">
                                      <Flex gap="4" width="100%">
                                        <InputWithLabel
                                          width={['50%', '140px']}
                                          name="roomsTotal"
                                          defaultValue={values.roomsTotal}
                                          label={t('editProperty.roomsTotal')}
                                          type="number"
                                          noDecimals
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                        <InputWithLabel
                                          width={['50%', '64px']}
                                          name="pax"
                                          defaultValue={values.pax}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.pax')}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                      <Flex gap="4" width="100%">
                                        <InputWithLabel
                                          width={['50%', '64px']}
                                          name="toilets"
                                          defaultValue={values.toilets}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.toilets')}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                        <InputWithLabel
                                          width={['50%', '64px']}
                                          name="suiteBaths"
                                          defaultValue={values.suiteBaths}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.suiteBaths')}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                      <Flex gap="4" width="100%">
                                        <InputWithLabel
                                          width={['50%', '64px']}
                                          name="bathrooms"
                                          defaultValue={values.bathrooms}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.bathrooms')}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                        <InputWithLabel
                                          width={['50%', '64px']}
                                          name="bedrooms"
                                          defaultValue={values.bedrooms}
                                          type="number"
                                          noDecimals
                                          label={t('editProperty.bedrooms')}
                                          min={0}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                    </Flex>
                                  </Flex>

                                  <Flex direction="column" rowGap="2">
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.garage')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex
                                      direction={['column', 'row']}
                                      wrap="wrap"
                                      rowGap="0"
                                      columnGap="8"
                                    >
                                      {garageTypes?.map((garageType) => (
                                        <Flex key={garageType.id}>
                                          <Checkbox
                                            name="garageTypeIds"
                                            checked={values.garageTypeIds?.includes(
                                              garageType.id
                                            )}
                                            onChange={(event, value) =>
                                              handleCheckboxesChange(
                                                value,
                                                'garageTypeIds',
                                                garageType.id,
                                                garageType.name,
                                                values.garageTypeIds ?? [],
                                                setFieldValue
                                              )
                                            }
                                          >
                                            {garageType.name}
                                          </Checkbox>
                                        </Flex>
                                      ))}
                                    </Flex>
                                    <InputWithLabel
                                      width={['100%', '140px']}
                                      label={t('editProperty.parkingSpaces')}
                                      name="parkingSpaces"
                                      defaultValue={values.parkingSpaces}
                                      type="number"
                                      noDecimals
                                      min={0}
                                      max={100}
                                      error={errors.parkingSpaces}
                                      touched={touched.parkingSpaces}
                                      onlyUpdateOnBlur
                                    />
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.pool')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex
                                      direction={['column', 'row']}
                                      wrap="wrap"
                                      rowGap="0"
                                      columnGap="8"
                                    >
                                      {poolTypes?.map((poolType) => (
                                        <Flex key={poolType.id}>
                                          <Checkbox
                                            name="poolTypeIds"
                                            checked={values.poolTypeIds?.includes(
                                              poolType.id
                                            )}
                                            onChange={(event, value) =>
                                              handleCheckboxesChange(
                                                value,
                                                'poolTypeIds',
                                                poolType.id,
                                                poolType.name,
                                                values.poolTypeIds ?? [],
                                                setFieldValue
                                              )
                                            }
                                          >
                                            {poolType.name}
                                          </Checkbox>
                                        </Flex>
                                      ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.garden')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex
                                      direction={['column', 'row']}
                                      wrap="wrap"
                                      rowGap="0"
                                      columnGap="8"
                                    >
                                      {gardenTypes?.map((gardenType) => (
                                        <Flex key={gardenType.id}>
                                          <Checkbox
                                            name="gardenTypeIds"
                                            checked={values.gardenTypeIds?.includes(
                                              gardenType.id
                                            )}
                                            onChange={(event, value) =>
                                              handleCheckboxesChange(
                                                value,
                                                'gardenTypeIds',
                                                gardenType.id,
                                                gardenType.name,
                                                values.gardenTypeIds ?? [],
                                                setFieldValue
                                              )
                                            }
                                          >
                                            {gardenType.name}
                                          </Checkbox>
                                        </Flex>
                                      ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.views')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Views'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="160px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.orientation')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {orientations.map((orientation) => (
                                        <Flex
                                          key={orientation.id}
                                          width="160px"
                                        >
                                          <Checkbox
                                            name="orientations"
                                            checked={values.orientationIds?.includes(
                                              orientation.id
                                            )}
                                            onChange={(event, value) =>
                                              handleOrientationsChange(
                                                value,
                                                orientation.id,
                                                values.orientationIds ?? [],
                                                setFieldValue
                                              )
                                            }
                                          >
                                            {orientation.name}
                                          </Checkbox>
                                        </Flex>
                                      ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="4"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.keysAndHandoff')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex width="100%">
                                      <Text fontSize="16px">
                                        {t('editProperty.keysInExistince')}
                                      </Text>
                                    </Flex>
                                    <Flex width="100%" gap="4">
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.protectedKeysTotal"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.protectedKeysTotal
                                        }
                                        label={t(
                                          'editProperty.protectedKeysTotal'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.protectedKeysDelivered"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.protectedKeysDelivered
                                        }
                                        label={t(
                                          'editProperty.protectedKeysDelivered'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.protectedKeysExisting"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.protectedKeysExisting
                                        }
                                        label={t(
                                          'editProperty.protectedKeysExisting'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>
                                    <Flex width="100%" gap="4">
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.unprotectedKeysTotal"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.unprotectedKeysTotal
                                        }
                                        label={t(
                                          'editProperty.unprotectedKeysTotal'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.unprotectedKeysDelivered"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.unprotectedKeysDelivered
                                        }
                                        label={t(
                                          'editProperty.unprotectedKeysDelivered'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                      <InputWithLabel
                                        width={['50%', '140px']}
                                        name="keysAndHandoff.unprotectedKeysExisting"
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.unprotectedKeysExisting
                                        }
                                        label={t(
                                          'editProperty.unprotectedKeysExisting'
                                        )}
                                        type="number"
                                        noDecimals
                                        min={0}
                                        onlyUpdateOnBlur
                                      />
                                    </Flex>
                                    <Flex width="100%" mb="1">
                                      <Text fontSize="16px">
                                        {t('editProperty.whereKeysCanBeFound')}
                                      </Text>
                                    </Flex>
                                    <Flex
                                      width="100%"
                                      direction="column"
                                      gap="2"
                                      mb="6"
                                    >
                                      <Checkbox
                                        name="additionalInformation"
                                        checked={Boolean(
                                          values.keysAndHandoff?.otherKeysInfo
                                        )}
                                        onChange={(_event, value) => {
                                          setFieldValue(
                                            'keysAndHandoff.otherKeysInfo',
                                            value
                                          )
                                        }}
                                      >
                                        {`${t('editProperty.otherKeysInfo')}`}
                                      </Checkbox>
                                      <InputWithLabel
                                        name="keysAndHandoff.otherKeysInfoPhoneNumber"
                                        width={['100%', '290px']}
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.otherKeysInfoPhoneNumber ?? ''
                                        }
                                        label={t(
                                          'editProperty.otherKeysInfoPhoneNumber'
                                        )}
                                        placeholder="Phone number"
                                        readOnly={
                                          !Boolean(
                                            values.keysAndHandoff?.otherKeysInfo
                                          )
                                        }
                                        onlyUpdateOnBlur
                                      />
                                      <Textarea
                                        width={['100%', '450px']}
                                        height="70px"
                                        size="md"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Describe where and who to contact..."
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.otherKeysInfoDescription ?? ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'keysAndHandoff.otherKeysInfoDescription',
                                            e.currentTarget.value
                                          )
                                        }
                                        disabled={
                                          !Boolean(
                                            values.keysAndHandoff?.otherKeysInfo
                                          )
                                        }
                                      />
                                    </Flex>
                                    <Flex
                                      width="100%"
                                      direction="column"
                                      gap="2"
                                      mb="6"
                                    >
                                      <Checkbox
                                        name="keysAndHandoff.strandPropertiesKeysInfo"
                                        checked={Boolean(
                                          values.keysAndHandoff
                                            ?.strandPropertiesKeysInfo
                                        )}
                                        onChange={(_event, value) => {
                                          setFieldValue(
                                            'keysAndHandoff.strandPropertiesKeysInfo',
                                            value
                                          )
                                        }}
                                      >
                                        {`${t(
                                          'editProperty.strandPropertiesKeysInfo'
                                        )}`}
                                      </Checkbox>
                                      <InputWithLabel
                                        name="keysAndHandoff.strandPropertiesKeysInfoOffice"
                                        width={['100%', '290px']}
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.strandPropertiesKeysInfoOffice ??
                                          ''
                                        }
                                        label={t(
                                          'editProperty.strandPropertiesKeysInfoOffice'
                                        )}
                                        placeholder="Office name"
                                        readOnly={
                                          !Boolean(
                                            values.keysAndHandoff
                                              ?.strandPropertiesKeysInfo
                                          )
                                        }
                                        onlyUpdateOnBlur
                                      />
                                      <Textarea
                                        width={['100%', '450px']}
                                        height="70px"
                                        size="md"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Other notes or comments..."
                                        defaultValue={
                                          values.keysAndHandoff
                                            ?.strandPropertiesKeysInfoNotes ??
                                          ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'keysAndHandoff.strandPropertiesKeysInfoNotes',
                                            e.currentTarget.value
                                          )
                                        }
                                        disabled={
                                          !Boolean(
                                            values.keysAndHandoff
                                              ?.strandPropertiesKeysInfo
                                          )
                                        }
                                      />
                                    </Flex>
                                    <Flex width="100%">
                                      <Text fontSize="16px">
                                        {t('editProperty.propertyHas')}
                                      </Text>
                                    </Flex>
                                    <Flex gap="10">
                                      <Radio
                                        label={t('editProperty.electricity')}
                                        direction={['column', 'row']}
                                        name="keysAndHandoff.hasElectricity"
                                        defaultValue={
                                          values.keysAndHandoff.hasElectricity
                                        }
                                        values={[
                                          {
                                            value: 'yes',
                                            label: t('yes'),
                                          },
                                          {
                                            value: 'no',
                                            label: t('no'),
                                          },
                                        ]}
                                      />
                                      <Radio
                                        label={t('editProperty.lights')}
                                        direction={['column', 'row']}
                                        name="keysAndHandoff.hasLights"
                                        defaultValue={
                                          values.keysAndHandoff.hasLights
                                        }
                                        values={[
                                          {
                                            value: 'yes',
                                            label: t('yes'),
                                          },
                                          {
                                            value: 'no',
                                            label: t('no'),
                                          },
                                        ]}
                                      />
                                    </Flex>
                                  </Flex>
                                </Flex>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {/* Amenities */}
                        <AccordionItem
                          ref={amenitiesRef}
                          data-id="amenities"
                          width="full"
                          backgroundColor="white"
                          borderColor="grays.borderColor"
                          borderTop={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                <Text whiteSpace="nowrap">
                                  {t('editProperty.amenities')}
                                </Text>
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex direction="column" gap="4">
                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.location')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Location'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="260px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.climateControl')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name ===
                                            'Climate control'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="260px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.features')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Features'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="260px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.furniture')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Furniture'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="200px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.rooms')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Rooms'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="180px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.security')}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex wrap="wrap" rowGap="0" columnGap="2">
                                      {featuresData
                                        ?.find(
                                          (featureGroup) =>
                                            featureGroup.name === 'Security'
                                        )
                                        ?.features.map((feature) => (
                                          <Flex key={feature.id} width="200px">
                                            <Checkbox
                                              name="featureIds"
                                              checked={values.featureIds?.includes(
                                                feature.id
                                              )}
                                              onChange={(_event, value) =>
                                                handleFeaturesChange(
                                                  value,
                                                  feature.id,
                                                  values.featureIds ?? [],
                                                  setFieldValue
                                                )
                                              }
                                            >
                                              {feature.name}
                                            </Checkbox>
                                          </Flex>
                                        ))}
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    alignItems="center"
                                    rowGap="2"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t(
                                          'editProperty.telecommunicationSystem'
                                        )}
                                      </Heading>
                                      <Divider />
                                    </Flex>
                                    <Flex
                                      direction="column"
                                      rowGap="0"
                                      columnGap="2"
                                    >
                                      <Flex width="200px" whiteSpace="nowrap">
                                        <Checkbox
                                          name="telecommunicationSystems"
                                          checked={Boolean(
                                            values.telecommunicationSystems
                                              ?.telephoneNetwork
                                          )}
                                          onChange={(_event, value) =>
                                            setFieldValue(
                                              'telecommunicationSystems.telephoneNetwork',
                                              value
                                            )
                                          }
                                        >
                                          {t('editProperty.telephoneNetwork')}
                                        </Checkbox>
                                      </Flex>
                                      <Flex width="200px" whiteSpace="nowrap">
                                        <Checkbox
                                          name="telecommunicationSystems"
                                          checked={Boolean(
                                            values.telecommunicationSystems
                                              ?.generalCabling
                                          )}
                                          onChange={(event, value) =>
                                            setFieldValue(
                                              'telecommunicationSystems.generalCabling',
                                              value
                                            )
                                          }
                                        >
                                          {t('editProperty.generalCabling')}
                                        </Checkbox>
                                      </Flex>
                                      <Flex width="200px" whiteSpace="nowrap">
                                        <Checkbox
                                          name="telecommunicationSystems"
                                          checked={Boolean(
                                            values.telecommunicationSystems
                                              ?.fiberCable
                                          )}
                                          onChange={(_event, value) =>
                                            setFieldValue(
                                              'telecommunicationSystems.fiberCable',
                                              value
                                            )
                                          }
                                        >
                                          {t('editProperty.fiberCable')}
                                        </Checkbox>
                                      </Flex>
                                      <Flex width="200px" whiteSpace="nowrap">
                                        <Checkbox
                                          name="telecommunicationSystems"
                                          checked={Boolean(
                                            values.telecommunicationSystems
                                              ?.unknown
                                          )}
                                          onChange={(_event, value) =>
                                            setFieldValue(
                                              'telecommunicationSystems.unknown',
                                              value
                                            )
                                          }
                                        >
                                          {t('editProperty.unknown')}
                                        </Checkbox>
                                      </Flex>
                                    </Flex>
                                  </Flex>
                                </Flex>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {/* Condition & History */}
                        <AccordionItem
                          ref={conditionAndHistoryRef}
                          data-id="conditionAndHistory"
                          width="full"
                          backgroundColor="white"
                          borderColor="grays.borderColor"
                          borderTop={0}
                        >
                          {({ isExpanded }) => (
                            <>
                              <AccordionHeading isExpanded={isExpanded}>
                                <Text whiteSpace="nowrap">
                                  {t('editProperty.conditionAndHistory')}
                                </Text>
                              </AccordionHeading>
                              <AccordionPanel px="6" pb="6">
                                <Flex direction="column" gap="8">
                                  <Flex
                                    wrap="wrap"
                                    gap="4"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="space-between"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.renovations')}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Flex
                                      justifyContent="start"
                                      wrap="wrap"
                                      gap="4"
                                    >
                                      <Flex direction="column">
                                        <Flex alignItems="center" gap="4">
                                          <Radio
                                            label={t(
                                              'editProperty.majorRenovationsPerformed'
                                            )}
                                            name="renovations.majorRenovationsPerformed"
                                            values={[
                                              {
                                                value: 'None',
                                                label: t('editProperty.none'),
                                              },
                                              {
                                                value:
                                                  'Renovations have been performed',
                                                label: t(
                                                  'editProperty.renovationsHaveBeenPerformed'
                                                ),
                                              },
                                            ]}
                                            direction={{
                                              base: 'column',
                                              md: 'row',
                                            }}
                                            defaultValue={
                                              values.renovations
                                                .majorRenovationsPerformed
                                            }
                                          />
                                        </Flex>
                                        <Text fontSize="12px">
                                          {t(
                                            'editProperty.describePerformedRenovations'
                                          )}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.renovations
                                              ?.describePerformedRenovations ??
                                            ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'renovations.describePerformedRenovations',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.renovations
                                              .majorRenovationsPerformed !==
                                            'Renovations have been performed'
                                          }
                                        />
                                      </Flex>
                                      <Flex direction="column">
                                        <Flex alignItems="center" gap="4">
                                          <Radio
                                            label={t(
                                              'editProperty.plannedRenovations'
                                            )}
                                            name="renovations.plannedRenovations"
                                            values={[
                                              {
                                                value: 'None',
                                                label: t('editProperty.none'),
                                              },
                                              {
                                                value:
                                                  'There are planned renovations',
                                                label: t(
                                                  'editProperty.thereArePlannedRenovations'
                                                ),
                                              },
                                            ]}
                                            direction={{
                                              base: 'column',
                                              md: 'row',
                                            }}
                                            defaultValue={
                                              values.renovations
                                                .plannedRenovations
                                            }
                                          />
                                        </Flex>
                                        <Text fontSize="12px">
                                          {t(
                                            'editProperty.describePerformedRenovations'
                                          )}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.renovations
                                              ?.describePlannedRenovations ?? ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'renovations.describePlannedRenovations',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.renovations
                                              .plannedRenovations !==
                                            'There are planned renovations'
                                          }
                                        />
                                      </Flex>
                                    </Flex>
                                    <Flex direction="column">
                                      <Flex alignItems="center" gap="4">
                                        <Radio
                                          label={t(
                                            'editProperty.renovationsPerformedBeforeSellerOwnership'
                                          )}
                                          name="renovations.renovationsPerformedBeforeSellerOwnership"
                                          values={[
                                            {
                                              value: 'Not performed',
                                              label: t(
                                                'editProperty.notPerformed'
                                              ),
                                            },
                                            {
                                              value: 'Yes',
                                              label: t('editProperty.yes'),
                                            },
                                            {
                                              value: 'Unknown',
                                              label: t('editProperty.unknown'),
                                            },
                                          ]}
                                          direction={{
                                            base: 'column',
                                            md: 'row',
                                          }}
                                          defaultValue={
                                            values.renovations
                                              ?.renovationsPerformedBeforeSellerOwnership ??
                                            'Not performed'
                                          }
                                        />
                                      </Flex>
                                      <Text fontSize="12px">
                                        {t(
                                          'editProperty.describePerformedRenovations'
                                        )}
                                      </Text>
                                      <Textarea
                                        width={['100%', '450px']}
                                        height="110px"
                                        size="md"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Description"
                                        defaultValue={
                                          values.renovations
                                            ?.describePreviousPerformedRenovations ??
                                          ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'renovations.describePreviousPerformedRenovations',
                                            e.currentTarget.value
                                          )
                                        }
                                        isDisabled={
                                          values.renovations
                                            ?.renovationsPerformedBeforeSellerOwnership !==
                                          'Yes'
                                        }
                                      />
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    gap="4"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="start"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.damagesAndDefects')}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Flex direction="column">
                                      <Flex alignItems="center" gap="4">
                                        <Radio
                                          label={t(
                                            'editProperty.defectsDamagesRepairObserved'
                                          )}
                                          name="damagesAndDefects.defectsDamagesRepairObserved"
                                          values={[
                                            {
                                              value: 'None',
                                              label: t('editProperty.none'),
                                            },
                                            {
                                              value: 'Defects found',
                                              label: t(
                                                'editProperty.defectsFound'
                                              ),
                                            },
                                          ]}
                                          direction={{
                                            base: 'column',
                                            md: 'row',
                                          }}
                                          defaultValue={
                                            values.damagesAndDefects
                                              .defectsDamagesRepairObserved
                                          }
                                        />
                                      </Flex>
                                      <Text fontSize="12px">
                                        {t(
                                          'editProperty.describeDefectsDamagesRepairs'
                                        )}
                                      </Text>
                                      <Textarea
                                        width={['100%', '450px']}
                                        height="110px"
                                        size="md"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Description"
                                        defaultValue={
                                          values.damagesAndDefects
                                            ?.describeDefectsDamagesRepairs ??
                                          ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'damagesAndDefects.describeDefectsDamagesRepairs',
                                            e.currentTarget.value
                                          )
                                        }
                                        isDisabled={
                                          values.damagesAndDefects
                                            .defectsDamagesRepairObserved !==
                                          'Defects found'
                                        }
                                      />
                                    </Flex>
                                    <Flex direction="column">
                                      <Flex gap="10" mb="4">
                                        <Radio
                                          label={t(
                                            'editProperty.officialPermitsAcquired'
                                          )}
                                          direction={['column', 'row']}
                                          name="damagesAndDefects.officialPermitsAcquired"
                                          defaultValue={
                                            values.damagesAndDefects
                                              .officialPermitsAcquired
                                          }
                                          values={[
                                            {
                                              value: 'yes',
                                              label: t('yes'),
                                            },
                                            {
                                              value: 'no',
                                              label: t('no'),
                                            },
                                          ]}
                                          onlyUpdateOnBlur
                                        />
                                        <Radio
                                          label={t(
                                            'editProperty.finalInspectionOfChanges'
                                          )}
                                          direction={['column', 'row']}
                                          name="damagesAndDefects.finalInspectionOfChanges"
                                          defaultValue={
                                            values.damagesAndDefects
                                              .finalInspectionOfChanges
                                          }
                                          values={[
                                            {
                                              value: 'yes',
                                              label: t('yes'),
                                            },
                                            {
                                              value: 'no',
                                              label: t('no'),
                                            },
                                          ]}
                                          onlyUpdateOnBlur
                                        />
                                      </Flex>
                                      <Text
                                        width={['100%', '450px']}
                                        fontSize="14px"
                                      >
                                        {t(
                                          'editProperty.detailedAccountOfDamagesOrFault'
                                        )}
                                      </Text>
                                      <Textarea
                                        width={['100%', '450px']}
                                        height="110px"
                                        size="md"
                                        whiteSpace="pre-wrap"
                                        overflowWrap="break-word"
                                        placeholder="Description"
                                        defaultValue={
                                          values.damagesAndDefects
                                            ?.describeRepairWork ?? ''
                                        }
                                        onBlur={(e) =>
                                          setFieldValue(
                                            'damagesAndDefects.describeRepairWork',
                                            e.currentTarget.value
                                          )
                                        }
                                      />
                                    </Flex>
                                  </Flex>

                                  <Flex
                                    wrap="wrap"
                                    gap="4"
                                    flexDirection="column"
                                  >
                                    <Flex
                                      justifyContent="start"
                                      alignItems="center"
                                      gap="4"
                                      width="100%"
                                    >
                                      <Heading whiteSpace="nowrap" variant="H3">
                                        {t('editProperty.otherDamages')}
                                      </Heading>
                                      <Divider />
                                    </Flex>

                                    <Text fontSize="14px">
                                      {t(
                                        'editProperty.suspectedDamagesOrProblems'
                                      )}
                                    </Text>
                                    <Flex
                                      justifyContent="start"
                                      wrap="wrap"
                                      gap="4"
                                    >
                                      <Flex direction="column">
                                        <Flex gap="10">
                                          <Radio
                                            label={t(
                                              'editProperty.waterDamage'
                                            )}
                                            direction={['column', 'row']}
                                            name="otherDamages.waterDamage"
                                            defaultValue={
                                              values.otherDamages.waterDamage
                                            }
                                            values={[
                                              {
                                                value: 'yes',
                                                label: t('yes'),
                                              },
                                              {
                                                value: 'no',
                                                label: t('no'),
                                              },
                                            ]}
                                          />
                                          <Radio
                                            label={t(
                                              'editProperty.moistureDamage'
                                            )}
                                            direction={['column', 'row']}
                                            name="otherDamages.moistureDamage"
                                            defaultValue={
                                              values.otherDamages.moistureDamage
                                            }
                                            values={[
                                              {
                                                value: 'yes',
                                                label: t('yes'),
                                              },
                                              {
                                                value: 'no',
                                                label: t('no'),
                                              },
                                            ]}
                                          />
                                        </Flex>
                                        <Text fontSize="12px">
                                          {t('editProperty.timeOfDamage')}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          mb="4"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.otherDamages?.timeOfDamage ??
                                            ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'otherDamages.timeOfDamage',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.otherDamages.waterDamage !==
                                              'yes' &&
                                            values.otherDamages
                                              .moistureDamage !== 'yes'
                                          }
                                        />
                                        <Text fontSize="12px">
                                          {t('editProperty.scopeOfDamage')}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.otherDamages
                                              ?.scopeOfDamage ?? ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'otherDamages.scopeOfDamage',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.otherDamages.waterDamage !==
                                              'yes' &&
                                            values.otherDamages
                                              .moistureDamage !== 'yes'
                                          }
                                        />
                                      </Flex>
                                      <Flex direction="column">
                                        <Flex gap="10">
                                          <Radio
                                            label={t(
                                              'editProperty.moldOrFungalProblems'
                                            )}
                                            direction={['column', 'row']}
                                            name="otherDamages.moldOrFungalProblems"
                                            defaultValue={
                                              values.otherDamages
                                                .moldOrFungalProblems
                                            }
                                            values={[
                                              {
                                                value: 'yes',
                                                label: t('yes'),
                                              },
                                              {
                                                value: 'no',
                                                label: t('no'),
                                              },
                                            ]}
                                          />
                                          <Radio
                                            label={t(
                                              'editProperty.otherSpecialDamages'
                                            )}
                                            direction={['column', 'row']}
                                            name="otherDamages.otherSpecialDamages"
                                            defaultValue={
                                              values.otherDamages
                                                .otherSpecialDamages
                                            }
                                            values={[
                                              {
                                                value: 'yes',
                                                label: t('yes'),
                                              },
                                              {
                                                value: 'no',
                                                label: t('no'),
                                              },
                                            ]}
                                          />
                                        </Flex>
                                        <Text fontSize="12px">
                                          {t('editProperty.causeOfDamage')}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          mb="4"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.otherDamages
                                              ?.causeOfDamage ?? ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'otherDamages.causeOfDamage',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.otherDamages
                                              .moldOrFungalProblems !== 'yes' &&
                                            values.otherDamages
                                              .otherSpecialDamages !== 'yes'
                                          }
                                        />
                                        <Text fontSize="12px">
                                          {t('editProperty.repairMethod')}
                                        </Text>
                                        <Textarea
                                          width={['100%', '450px']}
                                          height="110px"
                                          size="md"
                                          whiteSpace="pre-wrap"
                                          overflowWrap="break-word"
                                          placeholder="Description"
                                          defaultValue={
                                            values.otherDamages?.repairMethod ??
                                            ''
                                          }
                                          onBlur={(e) =>
                                            setFieldValue(
                                              'otherDamages.repairMethod',
                                              e.currentTarget.value
                                            )
                                          }
                                          isDisabled={
                                            values.otherDamages
                                              .moldOrFungalProblems !== 'yes' &&
                                            values.otherDamages
                                              .otherSpecialDamages !== 'yes'
                                          }
                                        />
                                      </Flex>
                                    </Flex>
                                  </Flex>
                                </Flex>
                              </AccordionPanel>
                            </>
                          )}
                        </AccordionItem>

                        {isMobile && (
                          <DrawerFooter
                            mt="100px"
                            zIndex={5}
                            position="relative"
                          >
                            <Flex
                              position="fixed"
                              width="100vw"
                              height="91px"
                              alignItems="center"
                              justifyContent="center"
                              bottom="0"
                              left="0"
                              background="common.white"
                              borderTop="1px solid"
                              borderColor="grays.gray6"
                            >
                              <Button
                                onClick={() =>
                                  handleConfirmationModal(() => handleOnClose)
                                }
                                variant="transparent"
                                borderColor="transparent"
                                fontWeight={500}
                                fontSize="16px"
                                size="lg"
                                width="45%"
                              >
                                {t('cancel')}
                              </Button>
                              <Button
                                onClick={() =>
                                  handleSubmitForm(isValid, submitForm)
                                }
                                fontWeight={500}
                                fontSize="16px"
                                size="lg"
                                width="45%"
                                isDisabled={!isFormChanged(values)}
                              >
                                {t('save')}
                              </Button>
                            </Flex>
                          </DrawerFooter>
                        )}
                      </Accordion>
                    </Flex>
                  </Flex>
                </DrawerBody>
              </DrawerContent>
            )
          }}
        </Formik>
      </Drawer>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        isCentered={true}
        onAccept={() => {
          setShowConfirmationModal(false)
          confirmationModalAcceptFunction()
        }}
        onReject={() => setShowConfirmationModal(false)}
        hideText={false}
      />
    </>
  )
}
