import {
  editFISalesAgreement,
  getFISalesAgreementsQueryKey,
  useFISalesAgreement,
} from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Formik, FormikProps } from 'formik'
import { SalesAgreementFormBody, FISalesAgreementSchema } from './common'
import { FISalesAgreementCreate } from '@/modules/fi-properties'
import { omit } from 'lodash'
import { useToast } from '@chakra-ui/react'
import { useRef } from 'react'
import { useTranslation } from 'next-i18next'
import { FISalesAgreementStatusEnum } from '@/generated-types/api'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { mapStatusToAllowedSteps } from './common'

export const EditSalesAgreementForm = ({
  salesAgreementId,
}: {
  salesAgreementId: string
}) => {
  const { goToNextStep } = useMultiStepForm()
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const validationSchema = FISalesAgreementSchema(t)
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FISalesAgreementCreate>()
  const toast = useToast()
  const formRef = useRef<FormikProps<FISalesAgreementCreate>>(null)

  const queryClient = useQueryClient()
  const { data } = useFISalesAgreement(salesAgreementId)

  const { isPending: isEditingFISalesAgreement, mutate: editSalesAgreement } =
    useMutation({
      mutationFn: editFISalesAgreement,
      onSuccess: async (result) => {
        queryClient.invalidateQueries({
          queryKey: getFISalesAgreementsQueryKey(salesAgreementId),
        })
        if (result.status !== FISalesAgreementStatusEnum.draft) {
          goToNextStep({
            overrideAllowedSteps: mapStatusToAllowedSteps(result.status),
          })
        }
        toast(
          getToastProps({
            isMobile,
            title: t('fiSalesAgreement.updated'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
    })

  const handleOnSave = async (validate?: boolean) => {
    if (!formRef.current) {
      return
    }

    if (
      validate &&
      !formRef.current.dirty &&
      data?.status === FISalesAgreementStatusEnum.validated
    ) {
      return goToNextStep()
    }

    if (
      validate &&
      !(await validateAndSetErrors(
        formRef.current,
        t('fiSalesAgreement.form.validation.errorToast')
      ))
    ) {
      return
    }

    await editSalesAgreement({
      ...formRef.current.values,
      salesAgreementId,
      status: data?.status,
      validate,
    })
  }

  if (!data) {
    return null
  }

  const { realtorUsers, contacts, consenters, ...rest } = data

  const mappedData = {
    ...omit(rest, ['id', 'createdAt']),
    realtorUserIds: realtorUsers.map((ru) => ru.id),
    contactIds: contacts.map((c) => c.id),
    consenterIds: consenters.map((c) => c.id),
  } satisfies FISalesAgreementCreate

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('fiSalesAgreement.saveAsDraft'),
            onClick: () => handleOnSave(),
            isLoading: isEditingFISalesAgreement,
            buttonVariant: 'transparent',
          },
          {
            label: t('next'),
            onClick: async () => await handleOnSave(true),
            isLoading: isEditingFISalesAgreement,
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={mappedData}
        enableReinitialize
        validationSchema={validationSchema}
        validateOnBlur
        validateOnMount
        onSubmit={() => handleOnSave(true)}
      >
        {(formikProps) => (
          <SalesAgreementFormBody
            {...formikProps}
            preContactsOptions={data.contacts.map(({ name, id }) => ({
              id,
              name,
            }))}
          />
        )}
      </Formik>
    </StepContentWithFooter>
  )
}
