import {
  getFISalesAgreementsQueryKey,
  sendSalesAgreementForSigning,
  useFISalesAgreement,
} from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useSalesAgreementPreviewSections } from './common/previewUtils'
import { Stack, useToast } from '@chakra-ui/react'
import { useState } from 'react'
import { useTranslation } from 'next-i18next'
import {
  CreateFISignatureModal,
  SigningFormProps,
} from '@/modules/fi-properties/components/FISignaturesModal/CreateFISignatureModal'
import {
  mapContactToParticipant,
  mapUserToParticipant,
  ParticipantFieldOptions,
  ParticipantCard,
  FIPropertyCard,
} from '@/components/Card'
import { DataGroup } from '@/components/DataGroup/DataGroup'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import { ContactType, FISalesAgreementStatusEnum } from '@/generated-types/api'
import {
  mapContactIdsToSigners,
  mapRealtorUserIdsToSigners,
} from '@/utils/signingUtils'
import { useMobile } from '@/hooks/useMobile'
import PreviewHtmlModal from '@/components/Modal/PreviewHtmlModal'
import { getToastProps } from '@/utils/toastProps'
import { getErrorLabel } from '@/lib/errorLabel'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { mapStatusToAllowedSteps } from './common'

const contactRequiredFields = [
  'email',
  'phoneNumbers',
  'address',
  'socialSecurityNumber',
]

export const PreviewSalesAgreement = ({
  salesAgreementId,
}: {
  salesAgreementId: string
}) => {
  const { goToNextStep } = useMultiStepForm()

  const queryClient = useQueryClient()
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const toast = useToast()
  const { data } = useFISalesAgreement(salesAgreementId)
  const previewSections = useSalesAgreementPreviewSections(data)
  const [showPreviewHtml, setShowPreviewHtml] = useState(false)
  const [isCreateSignatureModalOpen, setIsCreateSignatureModalOpen] =
    useState(false)

  const { isPending: isSendingForSigning, mutate: sendForSigning } =
    useMutation({
      mutationFn: sendSalesAgreementForSigning,
      onSuccess: async (res) => {
        queryClient.invalidateQueries({
          queryKey: getFISalesAgreementsQueryKey(salesAgreementId),
        })
        setIsCreateSignatureModalOpen(false)
        goToNextStep({
          overrideAllowedSteps: mapStatusToAllowedSteps(res.status),
        })
        toast(
          getToastProps({
            isMobile,
            title: t('fiSalesAgreement.sentForSigning'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
      onError: (error) => {
        toast(
          getToastProps({
            isMobile,
            title: t('errors.title'),
            description: getErrorLabel(t, error),
            status: 'error',
            variant: 'customError',
          })
        )
      },
    })

  const handleOnSave = async (values: SigningFormProps) => {
    await sendForSigning({
      ...values,
      signers: [
        ...mapRealtorUserIdsToSigners(values.realtorUserIds),
        ...mapContactIdsToSigners(values.contactIds),
      ],
      salesAgreementId,
    })
  }

  if (!data) {
    return null
  }

  function getParticipantAdditionalFields(
    type: ContactType | null
  ): ParticipantFieldOptions {
    const defaultFields: ParticipantFieldOptions = ['email', 'phone', 'address']

    if (type === ContactType.Person) {
      return defaultFields.concat(['ssn'])
    }
    if (type === ContactType.Organization) {
      return defaultFields.concat('companyId', 'contactPersonName')
    }
    if (type === ContactType.Estate) {
      return defaultFields.concat(['contactPersonName'])
    }

    return defaultFields
  }

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('fiSalesAgreement.previewContract'),
            onClick: () => setShowPreviewHtml(true),
            buttonVariant: 'transparent',
          },
          {
            label: t('next'),
            onClick: () => {
              if (
                data.status === FISalesAgreementStatusEnum.pending_signatures
              ) {
                goToNextStep()
              }
              if (data.status === FISalesAgreementStatusEnum.validated) {
                const contactsHaveMissingFields = data.contacts.some(
                  (contact) =>
                    contactRequiredFields.some((field) => !contact[field])
                )
                if (contactsHaveMissingFields) {
                  toast(
                    getToastProps({
                      isMobile,
                      title: t('fiSalesAgreement.missingFieldsTitle'),
                      description: t('fiSalesAgreement.missingFields'),
                      status: 'error',
                    })
                  )
                  return
                }
                setIsCreateSignatureModalOpen(true)
              }
            },
          },
        ],
      }}
    >
      <Stack gap={'1rem'} maxW={isMobile ? '100%' : '80%'}>
        <FIPropertyCard
          header={t('property') ?? ''}
          property={data.property}
          onModalClose={() =>
            queryClient.invalidateQueries({
              queryKey: ['fi-sales-agreement'],
            })
          }
        />
        <Stack spacing={4}>
          <HeaderRow title={t('fiSalesAgreement.form.realtor')} />
          <Stack direction="row">
            {data.realtorUsers.map((realtor) => (
              <ParticipantCard
                key={realtor.id}
                participant={mapUserToParticipant(realtor)}
                onEditSuccess={() =>
                  queryClient.invalidateQueries({
                    queryKey: ['fi-sales-agreement'],
                  })
                }
              />
            ))}
          </Stack>
        </Stack>
        <Stack spacing={4}>
          <HeaderRow title={t('seller')} />
          <Stack direction="row" w={'100%'}>
            {data.contacts.map((contact) => (
              <ParticipantCard
                key={contact.id}
                participant={mapContactToParticipant(contact)}
                onEditSuccess={() =>
                  queryClient.invalidateQueries({
                    queryKey: ['fi-sales-agreement'],
                  })
                }
                additionalFields={getParticipantAdditionalFields(contact.type)}
                highlightMissingFields
              />
            ))}
          </Stack>
        </Stack>
        <Stack spacing={'2rem'}>
          {previewSections.map(({ title, values }) => (
            <DataGroup key={title} title={title} values={values} />
          ))}
        </Stack>
        {showPreviewHtml && (
          <PreviewHtmlModal
            type="sales-agreement"
            previewIsOpen={showPreviewHtml}
            entityId={salesAgreementId}
            setPreviewIsOpen={setShowPreviewHtml}
          />
        )}
        {isCreateSignatureModalOpen && (
          <CreateFISignatureModal
            onSubmitMutation={handleOnSave}
            isSaving={isSendingForSigning}
            onClose={() => setIsCreateSignatureModalOpen(false)}
            signers={{
              realtors: true,
              contacts: true,
            }}
            initialValues={{
              realtorUserIds: data.realtorUsers.map((realtor) => realtor.id),
              contactIds: Array.from(
                new Set([
                  ...data.contacts
                    .filter((c) => c.type === ContactType.Person)
                    .map((contact) => contact.id),
                  ...data.consenters.map((consenter) => consenter.id),
                ])
              ),
              comment: t('fiSalesAgreement.defaultComment') ?? undefined,
            }}
          />
        )}
      </Stack>
    </StepContentWithFooter>
  )
}
