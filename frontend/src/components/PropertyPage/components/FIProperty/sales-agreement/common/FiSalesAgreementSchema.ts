import {
  FISalesAgreementAvailabilityEnum,
  FISalesAgreementCommissionBasisEnum,
  FISalesAgreementCommissionTypeEnum,
  FISalesAgreementLeaseAgreementEnum,
  FISalesAgreementShareRegisterFormatEnum,
  FISalesAgreementPaymentTermsEnum,
  FISalesAgreementTermEnum,
  SchemaFiSalesAgreementContactAcquisitionBase,
} from '@/generated-types/api'
import * as yup from 'yup'
import { fiSalesAgreementMaxNumberSize } from './utils'

const getMessageFn = (t: (key: string) => string) => (name: string) =>
  t(`fiSalesAgreement.form.validation.${name}`)

export const FISalesAgreementSchema = (t) => {
  const getMessage = getMessageFn(t)
  return yup.object().shape({
    propertyId: yup.number().required(getMessage('propertyId')),
    contactIds: yup
      .array()
      .of(yup.number())
      .min(1, getMessage('contactIds'))
      .required(getMessage('contactIds')),
    realtorUserIds: yup
      .array()
      .of(yup.number())
      .min(1, getMessage('realtorUserIds'))
      .required(getMessage('realtorUserIds')),
    separateMarketingAppendix: yup.boolean().nullable(),
    agreedMarketingMethods: yup.string().nullable(),
    unencumberedPriceRequest: yup
      .number()
      .required(getMessage('unencumberedPriceRequest'))
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError')),
    unencumberedPriceRequestEstimate: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .required(getMessage('unencumberedPriceRequestEstimate')),
    sharesIncludeLoan: yup.boolean().required(getMessage('sharesIncludeLoan')),
    loanAmount: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable()
      .when('sharesIncludeLoan', {
        is: true,
        then: (schema) => schema.required(getMessage('loanAmount')),
      }),
    loanDetails: yup.string().nullable(),
    priceIncludingLoan: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable()
      .when('sharesIncludeLoan', {
        is: true,
        then: (schema) => schema.required(getMessage('priceIncludingLoan')),
      }),
    paymentTerms: yup
      .mixed<FISalesAgreementPaymentTermsEnum>()
      .required(getMessage('paymentTerms')),
    paymentTermOther: yup
      .string()
      .nullable()
      .when('paymentTerms', {
        is: FISalesAgreementPaymentTermsEnum.other,
        then: (schema) => schema.required(getMessage('paymentTermOther')),
      }),
    averageSellingTimeEstimate: yup.string().nullable(),
    factorsAffectingSales: yup.string().nullable(),
    availability: yup
      .mixed<FISalesAgreementAvailabilityEnum>()
      .required(getMessage('availability')),
    dateWhenAvailable: yup
      .date()
      .nullable()
      .when('availability', {
        is: FISalesAgreementAvailabilityEnum.date,
        then: (schema) => schema.required(getMessage('dateWhenAvailable')),
      }),
    availabilityDetails: yup.string().nullable(),
    tenantName: yup
      .string()
      .nullable()
      .when('availability', {
        is: FISalesAgreementAvailabilityEnum.rented,
        then: (schema) => schema.required(getMessage('tenantName')),
      }),
    tenantContactDetails: yup.string().nullable(),
    leaseAgreement: yup.mixed<FISalesAgreementLeaseAgreementEnum>().nullable(),

    leaseAgreementTerm: yup.mixed<FISalesAgreementTermEnum>().nullable(),
    leaseStartDate: yup.date().nullable(),
    leaseEndDate: yup.date().nullable(),
    leaseTerminated: yup.boolean().nullable(),
    tenantHasPaidRentOnTime: yup.boolean().nullable(),
    leaseAmount: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable(),
    leaseDeposit: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable(),
    tenantPayingRentDetails: yup.string().nullable(),
    leaseAgreementDetails: yup.string().nullable(),
    restrictiveRightOfUser: yup.boolean().nullable(),
    restrictiveRightOfUserDetails: yup
      .string()
      .nullable()
      .when('restrictiveRightOfUser', {
        is: true,
        then: (schema) =>
          schema.required(getMessage('restrictiveRightOfUserDetails')),
      }),
    writtenConsentToTransfer: yup.boolean().nullable(),
    belongsToBusinessActivities: yup
      .boolean()
      .required(getMessage('belongsToBusinessActivities')),
    assignmentValidity: yup
      .mixed<FISalesAgreementTermEnum>()
      .required(getMessage('assignmentValidity')),
    assignmentValidityRenewalPeriod: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable()
      .when('assignmentValidity', {
        is: FISalesAgreementTermEnum.indefinite,
        then: (schema) =>
          schema.required(getMessage('assignmentValidityRenewalPeriod')),
      }),
    startDate: yup.date().required(getMessage('startDate')),
    endDate: yup
      .date()
      .required(getMessage('endDate'))
      .when('startDate', {
        is: (startDate) => startDate != null,
        then: (schema) =>
          schema.test(
            'is-greater-than-start-date',
            t('fiDetailsOfSale.form.fields.endDateGreaterThanStartDate'),
            function (value) {
              const { startDate } = this.parent
              return new Date(value) > new Date(startDate)
            }
          ),
      }),
    commissionBasisCode: yup
      .mixed<FISalesAgreementCommissionBasisEnum>()
      .required(getMessage('commissionBasisCode')),
    commissionType: yup
      .mixed<FISalesAgreementCommissionTypeEnum>()
      .required(getMessage('commissionType')),
    vat: yup.boolean().required(getMessage('vat')),
    commissionFixed: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable()
      .when('commissionType', {
        is: FISalesAgreementCommissionTypeEnum.fixed,
        then: (schema) => schema.required(getMessage('commissionFixed')),
      }),
    commissionPercentage: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable()
      .when('commissionType', {
        is: FISalesAgreementCommissionTypeEnum.percentage,
        then: (schema) => schema.required(getMessage('commissionPercentage')),
      }),
    commissionDetails: yup.string().nullable(),
    otherExpensesDetails: yup.string().nullable(),
    expenseIfNoCommission: yup.string().nullable(),
    shareRegisterFormat: yup
      .mixed<FISalesAgreementShareRegisterFormatEnum>()
      .required(getMessage('shareRegisterFormat')),
    shareRegisterStorage: yup.string().nullable(),
    unpaidMaintenanceCharge: yup.boolean().nullable(),
    unpaidMaintenanceChargeAmount: yup
      .number()
      .max(fiSalesAgreementMaxNumberSize, getMessage('valueMaxSizeError'))
      .nullable(),
    digitalTradingAllowed: yup.boolean().nullable(),
    isDomesticSale: yup.boolean().required(getMessage('isDomesticSale')),
    startAssignmentImmediately: yup
      .boolean()
      .nullable()
      .when('isDomesticSale', {
        is: true,
        then: (schema) =>
          schema.required(getMessage('startAssignmentImmediately')),
      }),

    startMarketingAfterCancelPeriod: yup
      .boolean()
      .nullable()
      .when('isDomesticSale', {
        is: true,
        then: (schema) =>
          schema.required(getMessage('startMarketingAfterCancelPeriod')),
      }),
    customerAskedToReadPrivacyPolicy: yup.boolean().nullable(),
    previousExternalSalesAgreement: yup.boolean().nullable(),
    previousExternalSalesAgreementDetails: yup.string().nullable(),
    additionalDetails: yup.string().nullable(),
    contactAcquisitions: yup
      .array(yup.object<SchemaFiSalesAgreementContactAcquisitionBase>())
      .nullable(),
  })
}
