import { Grid, GridItem, Heading, Stack } from '@chakra-ui/react'
import { FormikProps } from 'formik'
import { useTranslation } from 'next-i18next'
import { BooleanBadgeGroupField } from '.'
import { ContactAcquisitionFields } from './ContactAcquisitionFields'
import { InputWithLabel } from '@/components/Form/Input'
import { FISalesAgreementCreate } from '@/modules/fi-properties'
import FICustomerSelect from '@/components/FICustomerSelect'
import AnimatedContainer from '@/components/AnimatedContainer/AnimatedContainer'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import {
  FISalesAgreementShareRegisterFormatEnum,
  SchemaContactListRead,
} from '@/generated-types/api'
import { EnumBadgeGroupField } from '@/components/Badge/BadgeGroupFieldWrappers'

export const SalesAgreementTermsSection = (
  props: FormikProps<FISalesAgreementCreate> & {
    selectedContacts?: Pick<SchemaContactListRead, 'id' | 'name'>[]
  }
) => {
  const { values, errors, touched } = props
  const { t } = useTranslation(['common'])

  return (
    <Grid
      gap="1rem"
      templateColumns={{ base: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }}
    >
      <GridItem colSpan={3}>
        <Stack>
          <Heading variant="H1">
            {t('fiSalesAgreement.form.termsHeading')}
          </Heading>
          <HeaderRow
            title={t('fiSalesAgreement.form.announcementsHeaderRow')}
          />

          {values.contactAcquisitions?.map((contactAcquisition, index) => (
            <Stack
              key={`contact-acquisition-${index}-${contactAcquisition.contactId}`}
              gap="1rem"
              p={4}
              border="1px solid"
              borderColor="gray.200"
              borderRadius="md"
            >
              <ContactAcquisitionFields
                contactAcquisition={contactAcquisition}
                contactIndex={index}
                contactName={
                  props.selectedContacts?.find(
                    (contact) => contact.id === contactAcquisition.contactId
                  )?.name
                }
              />
            </Stack>
          ))}
        </Stack>
      </GridItem>
      <GridItem colSpan={2}>
        <Stack>
          <HeaderRow title={t('fiSalesAgreement.form.consentHeaderRow')} />
          <FICustomerSelect
            name="consenterIds"
            label={t('fiSalesAgreement.form.consentersLabel')}
            querySource="contacts"
          />
          <HeaderRow title={t('fiSalesAgreement.form.otherHeaderRow')} />
          <GridItem colSpan={2}>
            <EnumBadgeGroupField
              toggle
              name="shareRegisterFormat"
              label={t(`fiSalesAgreement.form.shareRegisterFormatLabel`)}
              enum={FISalesAgreementShareRegisterFormatEnum}
              itemLabelPrefix="fiSalesAgreement.form.enums.shareRegisterFormat"
            />
          </GridItem>
          <GridItem colSpan={2}>
            <InputWithLabel
              label={t('fiSalesAgreement.form.shareRegisterStorageLabel')}
              name="shareRegisterStorage"
              defaultValue={values.shareRegisterStorage}
              type="text"
            />
          </GridItem>
          <BooleanBadgeGroupField
            toggle
            label={t('fiSalesAgreement.form.unpaidMaintenanceChargeLabel')}
            name="unpaidMaintenanceCharge"
          />
          <AnimatedContainer expanded={values.unpaidMaintenanceCharge}>
            <InputWithLabel
              name="unpaidMaintenanceChargeAmount"
              label={t(
                'fiSalesAgreement.form.unpaidMaintenanceChargeAmountLabel'
              )}
              defaultValue={values.unpaidMaintenanceChargeAmount}
              type="currency"
              rightElement="EUR"
              error={errors.unpaidMaintenanceChargeAmount}
              touched={touched.unpaidMaintenanceChargeAmount}
            />
          </AnimatedContainer>
          <HeaderRow title={t('fiSalesAgreement.form.otherTermsHeaderRow')} />
          <BooleanBadgeGroupField
            toggle
            label={t('fiSalesAgreement.form.digitalTradingAllowedLabel')}
            name="digitalTradingAllowed"
          />
          <InputWithLabel
            label={t('fiSalesAgreement.form.agreedMarketingMethodsLabel')}
            name="agreedMarketingMethods"
            defaultValue={values.agreedMarketingMethods}
            error={errors.agreedMarketingMethods}
            touched={touched.agreedMarketingMethods}
          />
          <BooleanBadgeGroupField
            label={t('fiSalesAgreement.form.separateMarketingAppendixLabel')}
            name="separateMarketingAppendix"
          />
          <BooleanBadgeGroupField
            label={t('fiSalesAgreement.form.isDomesticSaleLabel')}
            name="isDomesticSale"
            required={true}
          />
          <AnimatedContainer expanded={values.isDomesticSale}>
            <BooleanBadgeGroupField
              label={t('fiSalesAgreement.form.startAssignmentImmediatelyLabel')}
              name="startAssignmentImmediately"
              required={!!values.isDomesticSale}
            />
            <BooleanBadgeGroupField
              label={t(
                'fiSalesAgreement.form.startMarketingAfterCancelPeriodLabel'
              )}
              name="startMarketingAfterCancelPeriod"
              required={!!values.isDomesticSale}
            />
          </AnimatedContainer>
          <BooleanBadgeGroupField
            label={t(
              'fiSalesAgreement.form.customerAskedToReadPrivacyPolicyLabel'
            )}
            name="customerAskedToReadPrivacyPolicy"
          />
          <BooleanBadgeGroupField
            label={t(
              'fiSalesAgreement.form.previousExternalSalesAgreementLabel'
            )}
            name="previousExternalSalesAgreement"
          />
          <AnimatedContainer expanded={values.previousExternalSalesAgreement}>
            <InputWithLabel
              label={t(
                'fiSalesAgreement.form.previousExternalSalesAgreementDetailsLabel'
              )}
              name="previousExternalSalesAgreementDetails"
              defaultValue={values.previousExternalSalesAgreementDetails}
              error={errors.previousExternalSalesAgreementDetails}
              touched={touched.previousExternalSalesAgreementDetails}
            />
          </AnimatedContainer>
          <InputWithLabel
            label={t('fiSalesAgreement.form.additionalDetailsLabel')}
            name="additionalDetails"
            defaultValue={values.additionalDetails}
          />
        </Stack>
      </GridItem>
    </Grid>
  )
}
