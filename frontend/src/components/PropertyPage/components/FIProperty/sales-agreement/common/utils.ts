import {
  FISalesAgreementStatusEnum,
  SchemaContactListRead,
  SchemaUserListRead,
} from '@/generated-types/api'

export const setBooleanValueAndClearFieldsIfFalse =
  (setFieldFn) =>
  (name: string, value: boolean | string | null, clear: string[]) => {
    setFieldFn(name, value)
    if (!value) {
      clear.forEach((c) => setFieldFn(c, null))
    }
  }

export const mapUserToValues = (user: SchemaUserListRead) => {
  return [
    { label: 'name', value: `${user.firstName} ${user.lastName}` },
    { label: 'email', value: user.email },
    { label: 'phone', value: user.phoneNumber ?? '-' },
  ]
}
export const mapContactToValues = (user: SchemaContactListRead) => {
  return [
    { label: 'name', value: user.name },
    { label: 'email', value: user.email ?? '-' },
    { label: 'phone', value: user.phoneNumbers.toString() ?? '-' },
  ]
}

export const FILL_FORM_STEP = 0
export const PREVIEW_STEP = 1
export const SIGNATURES_STEP = 2
export const COMPLETED_STEP = 3

export const mapStatusToAllowedSteps = (
  status: FISalesAgreementStatusEnum | null | undefined
) => {
  switch (status) {
    case FISalesAgreementStatusEnum.draft:
      return [FILL_FORM_STEP]
    case FISalesAgreementStatusEnum.validated:
      return [FILL_FORM_STEP, PREVIEW_STEP]
    case FISalesAgreementStatusEnum.pending_signatures:
      return [PREVIEW_STEP, SIGNATURES_STEP]
    case FISalesAgreementStatusEnum.completed:
      return [PREVIEW_STEP, SIGNATURES_STEP, COMPLETED_STEP]
    default:
      return [FILL_FORM_STEP]
  }
}

export const mapStatusToAllowedStep = (
  status: FISalesAgreementStatusEnum | null | undefined
) => {
  switch (status) {
    case FISalesAgreementStatusEnum.draft:
      return FILL_FORM_STEP
    case FISalesAgreementStatusEnum.validated:
      return PREVIEW_STEP
    case FISalesAgreementStatusEnum.pending_signatures:
      return SIGNATURES_STEP
    case FISalesAgreementStatusEnum.completed:
      return COMPLETED_STEP
    default:
      return FILL_FORM_STEP
  }
}

export const fiSalesAgreementMaxNumberSize = 2147483647
