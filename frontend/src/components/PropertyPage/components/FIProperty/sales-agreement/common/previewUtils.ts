import {
  SchemaFiSalesAgreementRead,
  FISalesAgreementPaymentTermsEnum,
  FISalesAgreementAvailabilityEnum,
  FISalesAgreementTermEnum,
  FISalesAgreementCommissionTypeEnum,
  SchemaFiSalesAgreementContactAcquisitionBase,
} from '@/generated-types/api'
import { TFunction, useTranslation } from 'next-i18next'
import { format, parseISO } from 'date-fns'
import { pick } from 'lodash'

export const useSalesAgreementPreviewSections = (
  data?: SchemaFiSalesAgreementRead
): {
  title: string
  values: { label: string; value: string }[]
}[] => {
  const { t } = useTranslation(['common'])
  if (!data) return []

  const sections = [
    {
      title: t('fiSalesAgreement.form.priceHeading'),
      values: mapSalesAgreementToPreviewValues(t)(
        pick(data, buildPricePreviewFields(data))
      ),
    },
    {
      title: t('fiSalesAgreement.form.availabilityHeading'),
      values: mapSalesAgreementToPreviewValues(t)(
        pick(data, buildAvailabilityPreviewFields(data))
      ),
    },
    {
      title: t('fiSalesAgreement.form.validityHeading'),
      values: mapSalesAgreementToPreviewValues(t)(
        pick(data, buildValidityPreviewFields(data))
      ),
    },
    {
      title: t('fiSalesAgreement.form.commissionHeading'),
      values: mapSalesAgreementToPreviewValues(t)(
        pick(data, buildCommissionPreviewFields(data))
      ),
    },
  ]

  // Add contact acquisitions sections
  if (data.contactAcquisitions && data.contactAcquisitions.length > 0) {
    data.contactAcquisitions.forEach((contactAcquisition, index) => {
      sections.push({
        title: `${t('fiSalesAgreement.form.termsHeading')} - ${t(
          'fiSalesAgreement.form.contact'
        )} ${index + 1}`,
        values: mapContactAcquisitionToPreviewValues(t)(contactAcquisition),
      })
    })
  }

  // Add general terms section (non-acquisition fields)
  sections.push({
    title: t('fiSalesAgreement.form.otherTermsHeading'),
    values: mapSalesAgreementToPreviewValues(t)(
      pick(data, buildGeneralTermsPreviewFields(data))
    ),
  })

  return sections
}

const mapSalesAgreementToPreviewValues =
  (t: TFunction) => (salesAgreement: Partial<SchemaFiSalesAgreementRead>) => {
    return Object.entries(salesAgreement).map(([key, value]) => ({
      label: t(`fiSalesAgreement.form.${key}Label`) ?? '',
      value: formatPreviewValue(t)(key, value),
    }))
  }

const buildPricePreviewFields = (
  data: SchemaFiSalesAgreementRead
): (keyof SchemaFiSalesAgreementRead)[] => {
  const fields: (keyof SchemaFiSalesAgreementRead)[] = [
    'unencumberedPriceRequest',
    'unencumberedPriceRequestEstimate',
    'sharesIncludeLoan',
  ]

  if (data.sharesIncludeLoan) {
    fields.push('loanAmount', 'loanDetails')
  }

  fields.push('priceIncludingLoan', 'paymentTerms')

  if (data.paymentTerms === FISalesAgreementPaymentTermsEnum.other) {
    fields.push('paymentTermOther')
  }

  fields.push('averageSellingTimeEstimate', 'factorsAffectingSales')

  return fields
}

const buildAvailabilityPreviewFields = (
  data: SchemaFiSalesAgreementRead
): (keyof SchemaFiSalesAgreementRead)[] => {
  const fields: (keyof SchemaFiSalesAgreementRead)[] = ['availability']

  if (data.availability === FISalesAgreementAvailabilityEnum.date) {
    fields.push('dateWhenAvailable')
  }

  if (
    data.availability === FISalesAgreementAvailabilityEnum.other ||
    data.availability === FISalesAgreementAvailabilityEnum.negotiable
  ) {
    fields.push('availabilityDetails')
  }

  if (data.availability === FISalesAgreementAvailabilityEnum.rented) {
    fields.push(
      'tenantName',
      'tenantContactDetails',
      'leaseAgreement',
      'leaseAgreementTerm',
      'leaseStartDate',
      'leaseEndDate',
      'leaseTerminated',
      'tenantHasPaidRentOnTime',
      'leaseAmount',
      'leaseDeposit',
      'tenantPayingRentDetails',
      'leaseAgreementDetails'
    )
  }

  fields.push('restrictiveRightOfUser')

  if (data.restrictiveRightOfUser) {
    fields.push('restrictiveRightOfUserDetails', 'writtenConsentToTransfer')
  }

  return fields
}

const buildValidityPreviewFields = (
  data: SchemaFiSalesAgreementRead
): (keyof SchemaFiSalesAgreementRead)[] => {
  const fields: (keyof SchemaFiSalesAgreementRead)[] = [
    'belongsToBusinessActivities',
    'assignmentValidity',
  ]

  if (data.assignmentValidity === FISalesAgreementTermEnum.indefinite) {
    fields.push('assignmentValidityRenewalPeriod')
  }

  fields.push('startDate', 'endDate')

  return fields
}

const buildCommissionPreviewFields = (
  data: SchemaFiSalesAgreementRead
): (keyof SchemaFiSalesAgreementRead)[] => {
  const fields: (keyof SchemaFiSalesAgreementRead)[] = [
    'commissionBasisCode',
    'commissionType',
    'vat',
  ]

  if (data.commissionType === FISalesAgreementCommissionTypeEnum.percentage) {
    fields.push('commissionPercentage')
  }

  if (data.commissionType === FISalesAgreementCommissionTypeEnum.fixed) {
    fields.push('commissionFixed')
  }

  fields.push(
    'commissionDetails',
    'otherExpensesDetails',
    'expenseIfNoCommission'
  )

  return fields
}
const buildGeneralTermsPreviewFields = (
  data: SchemaFiSalesAgreementRead
): (keyof SchemaFiSalesAgreementRead)[] => {
  const fields: (keyof SchemaFiSalesAgreementRead)[] = [
    'unpaidMaintenanceCharge',
  ]

  if (data.unpaidMaintenanceCharge) {
    fields.push('unpaidMaintenanceChargeAmount')
  }

  fields.push(
    'digitalTradingAllowed',
    'agreedMarketingMethods',
    'separateMarketingAppendix',
    'isDomesticSale'
  )

  if (data.isDomesticSale) {
    fields.push('startAssignmentImmediately', 'startMarketingAfterCancelPeriod')
  }

  fields.push(
    'customerAskedToReadPrivacyPolicy',
    'previousExternalSalesAgreement'
  )

  if (data.previousExternalSalesAgreement) {
    fields.push('previousExternalSalesAgreementDetails')
  }

  fields.push('additionalDetails')

  return fields
}

const mapContactAcquisitionToPreviewValues =
  (t: TFunction) =>
  (contactAcquisition: SchemaFiSalesAgreementContactAcquisitionBase) => {
    const fields: { label: string; value: string }[] = []
    if (contactAcquisition.acquisition) {
      fields.push({
        label: t('fiSalesAgreement.form.acquisitionLabel'),
        value: t(
          `fiSalesAgreement.form.enums.acquisition.${contactAcquisition.acquisition}`
        ),
      })
    }

    if (contactAcquisition.acquisitionDate) {
      fields.push({
        label: t('fiSalesAgreement.form.acquisitionDateLabel'),
        value: format(
          parseISO(contactAcquisition.acquisitionDate),
          'dd.MM.yyyy'
        ),
      })
    }

    if (contactAcquisition.acquisitionCost) {
      fields.push({
        label: t('fiSalesAgreement.form.acquisitionCostLabel'),
        value: contactAcquisition.acquisitionCost,
      })
    }

    if (contactAcquisition.taxConsequence) {
      fields.push({
        label: t('fiSalesAgreement.form.taxConsequencesLabel'),
        value: t(
          `fiSalesAgreement.form.enums.taxConsequences.${contactAcquisition.taxConsequence}`
        ),
      })
    }

    if (contactAcquisition.sellerIsMarriedOrInRegisteredRelationship !== null) {
      fields.push({
        label: t(
          'fiSalesAgreement.form.sellerIsMarriedOrInRegisteredRelationshipLabel'
        ),
        value: contactAcquisition.sellerIsMarriedOrInRegisteredRelationship
          ? t('yes')
          : t('no'),
      })
    }

    if (contactAcquisition.sellerHasSpousesConsent !== null) {
      fields.push({
        label: t('fiSalesAgreement.form.sellerHasSpousesConsentLabel'),
        value: contactAcquisition.sellerHasSpousesConsent ? t('yes') : t('no'),
      })
    }

    if (
      contactAcquisition.sellerHasBeenMarriedOrInRegisteredRelationship !== null
    ) {
      fields.push({
        label: t(
          'fiSalesAgreement.form.sellerHasBeenMarriedOrInRegisteredRelationshipLabel'
        ),
        value: contactAcquisition.sellerHasBeenMarriedOrInRegisteredRelationship
          ? t('yes')
          : t('no'),
      })
    }

    if (contactAcquisition.divorceLegallyBinding !== null) {
      fields.push({
        label: t('fiSalesAgreement.form.divorceLegallyBindingLabel'),
        value: contactAcquisition.divorceLegallyBinding ? t('yes') : t('no'),
      })
    }

    if (contactAcquisition.legalPartitioningIsComplete !== null) {
      fields.push({
        label: t('fiSalesAgreement.form.legalPartitioningIsCompleteLabel'),
        value: contactAcquisition.legalPartitioningIsComplete
          ? t('yes')
          : t('no'),
      })
    }

    if (contactAcquisition.clientHasUsedResidenceAsResidence !== null) {
      fields.push({
        label: t(
          'fiSalesAgreement.form.clientHasUsedResidenceAsResidenceLabel'
        ),
        value: contactAcquisition.clientHasUsedResidenceAsResidence
          ? t('yes')
          : t('no'),
      })
    }

    if (contactAcquisition.residencyStartDate) {
      fields.push({
        label: t('fiSalesAgreement.form.residencyStartDateLabel'),
        value: format(
          parseISO(contactAcquisition.residencyStartDate),
          'dd.MM.yyyy'
        ),
      })
    }

    if (contactAcquisition.residencyEndDate) {
      fields.push({
        label: t('fiSalesAgreement.form.residencyEndDateLabel'),
        value: format(
          parseISO(contactAcquisition.residencyEndDate),
          'dd.MM.yyyy'
        ),
      })
    }

    return fields
  }

const formatPreviewValue = (t: TFunction) => (key, value) => {
  if (value == null) {
    return '-'
  }

  if (typeof value === 'boolean') {
    return value ? t('yes') : t('no')
  }

  if (
    [
      'dateWhenAvailable',
      'leaseStartDate',
      'leaseEndDate',
      'startDate',
      'endDate',
      'residencyStartDate',
      'residencyEndDate',
    ].includes(key)
  ) {
    return format(parseISO(value), 'dd.MM.yyyy')
  }

  if (key === 'commissionPercentage') {
    return `${value}%`
  }

  if (key === 'assignmentValidity') {
    return t(`fiSalesAgreement.form.enums.period.${value}`)
  }

  if (
    [
      'paymentTerms',
      'availability',
      'leaseAgreement',
      'leaseAgreementTerm',
      'commissionBasisCode',
      'commissionType',
      'vat',
      'acquisition',
      'shareRegisterFormat',
      'taxConsequences',
    ].includes(key)
  ) {
    return t(`fiSalesAgreement.form.enums.${key}.${value}`)
  }

  if (typeof value === 'number') {
    return new Intl.NumberFormat('fi-FI', {
      style: 'currency',
      currency: 'EUR',
    }).format(value)
  }

  return value.toString()
}
