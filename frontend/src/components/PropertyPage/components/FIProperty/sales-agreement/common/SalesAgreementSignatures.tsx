import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  Divider,
  Stack,
  Tag,
  Text,
  useToast,
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import {
  SchemaDocumentEvent,
  SchemaDocumentSigner,
  SchemaFiSalesAgreementRead,
} from '@/generated-types/api'
import { sendReminderForSigners } from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { formatDateAndTime } from '@/utils/date'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import { mapSignerToParticipant, ParticipantCard } from '@/components/Card'
import { FiAlertTriangle } from 'react-icons/fi'
import { useState } from 'react'
import { AddSignerModal } from '@/modules/fi-properties/components/FISignaturesModal/AddSignerModal'
import {
  deleteSigning,
  deleteSigner,
} from '@/modules/fi-properties/queries/queryFIDocumentSigning'
import { getToastProps } from '@/utils/toastProps'
import { ConfirmationModal } from '@/components/Modal/ConfirmationModal'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { useMobile } from '@/hooks/useMobile'

export const SalesAgreementSignatures = ({
  salesAgreement,
}: {
  salesAgreement: SchemaFiSalesAgreementRead
}) => {
  const queryClient = useQueryClient()
  const { t } = useTranslation(['common'])
  const toast = useToast()
  const [isAddSignerModalOpen, setIsAddSignerModalOpen] = useState(false)

  const [isDeleteSigningModalOpen, setIsDeleteSigningModalOpen] =
    useState(false)

  const isMobile = useMobile()

  const { mutate: sendReminder, isPending: isSendingReminder } = useMutation({
    mutationFn: sendReminderForSigners,
    onSuccess: () => {
      toast(
        getToastProps({
          title: t('signing.reminderSent'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
    },
    onError: (_error) => {
      toast(
        getToastProps({
          title: t('error'),
          description: t('signing.failedSendingReminder'),
          status: 'error',
          isMobile,
        })
      )
    },
  })
  const { mutate: deleteSigningMutation } = useMutation({
    mutationFn: deleteSigning,
    onSuccess: () => {
      setIsDeleteSigningModalOpen(false)
      void queryClient.invalidateQueries({
        queryKey: ['fi-sales-agreement'],
      })
      toast(
        getToastProps({
          title: t('signing.signingDeleted'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
    },
    onError: (_error) => {
      toast(
        getToastProps({
          title: t('error'),
          description: t('signing.failedDeletingSigning'),
          status: 'error',
          isMobile,
        })
      )
    },
  })
  const { mutate: deleteSignerMutation } = useMutation({
    mutationFn: deleteSigner,
    onSuccess: () => {
      toast(
        getToastProps({
          title: t('signing.signerRemoved'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
      void queryClient.invalidateQueries({
        queryKey: ['fi-sales-agreement'],
      })
    },
    onError: (_error) => {
      toast(
        getToastProps({
          title: t('error'),
          description: t('signing.failedRemovingSigner'),
          status: 'error',
          isMobile,
        })
      )
    },
  })
  const signing = salesAgreement.signings[0]
  if (!signing) return null
  const allSignees = signing ? signing.signers.length : 0
  const signed = signing
    ? signing.signers.filter((s) => s.status === 'signed').length
    : 0

  const contactIds = new Set(salesAgreement.contacts.map(({ id }) => id))
  const realtorIds = new Set(salesAgreement.realtorUsers.map(({ id }) => id))
  const consenterIds = new Set(salesAgreement.consenters.map(({ id }) => id))

  const { contactSigners, realtorSigners, consenterSigners, otherSigners } =
    signing.signers.reduce<{
      contactSigners: SchemaDocumentSigner[]
      realtorSigners: SchemaDocumentSigner[]
      consenterSigners: SchemaDocumentSigner[]
      otherSigners: SchemaDocumentSigner[]
    }>(
      (acc, signer) => {
        const { userId, userType } = signer

        if (contactIds.has(userId) && userType === 'contact') {
          acc.contactSigners.push(signer)
        } else if (realtorIds.has(userId) && userType === 'user') {
          acc.realtorSigners.push(signer)
        } else if (consenterIds.has(userId) && userType === 'contact') {
          acc.consenterSigners.push(signer)
        } else {
          acc.otherSigners.push(signer)
        }

        return acc
      },
      {
        contactSigners: [],
        realtorSigners: [],
        consenterSigners: [],
        otherSigners: [],
      }
    )

  return (
    <StepContentWithFooter>
      <Stack maxW={isMobile ? '100%' : '66%'}>
        <HeaderRow title={t('fiSalesAgreement.digitalSigners')} />
        <Stack direction="row" justifyContent="flex-end" align="center" mb={4}>
          <Button
            variant="transparent"
            backgroundColor="white"
            fontWeight={500}
            fontSize="16px"
            onClick={() => setIsAddSignerModalOpen(true)}
          >
            {t('signing.addSigner')}
          </Button>
          <Button
            variant="transparent"
            backgroundColor="white"
            color="red"
            fontWeight={500}
            fontSize="16px"
            onClick={() => setIsDeleteSigningModalOpen(true)}
          >
            {t('signing.deleteSigning')}
          </Button>
        </Stack>
        {signing.status !== 'completed' && (
          <Tag colorScheme="red" p={4} fontSize="md">
            <Box mr={2}>
              <FiAlertTriangle />
            </Box>
            {` ${t('fiSalesAgreement.signingDeadline')} ${formatDateAndTime(
              signing.deadline
            )}`}
          </Tag>
        )}
        {signed < allSignees && (
          <Stack bg="#F6F1ED" p={4} borderRadius="lg">
            <Stack
              direction="row"
              justifyContent="space-between"
              align="center"
            >
              <Text fontSize="lg" fontWeight="bold">
                {`${t('fiSalesAgreement.missingSignatures')} ${
                  allSignees - signed
                }`}
              </Text>
              <Button
                onClick={async () =>
                  await sendReminder({
                    salesAgreementId: salesAgreement.id.toString(),
                  })
                }
                variant="transparent"
                backgroundColor="white"
                fontWeight={500}
                fontSize="16px"
                isLoading={isSendingReminder}
              >
                {t('fiSalesAgreement.sendReminder')}
              </Button>
            </Stack>
          </Stack>
        )}
        <HeaderRow title={t('realtors')} />
        {realtorSigners.map((signer) => (
          <ParticipantCard
            participant={mapSignerToParticipant(signer)}
            key={signer.id}
            variant="signing"
            signingStatus={signer.status}
            onRemove={() => {
              deleteSignerMutation({
                documentSigningId: signing.id,
                documentSignerId: signer.id,
              })
            }}
          />
        ))}
        <HeaderRow title={t('contacts')} />
        {contactSigners.map((signer) => (
          <ParticipantCard
            participant={mapSignerToParticipant(signer)}
            key={signer.id}
            variant="signing"
            signingStatus={signer.status}
            onRemove={() => {
              deleteSignerMutation({
                documentSigningId: signing.id,
                documentSignerId: signer.id,
              })
            }}
          />
        ))}
        {consenterSigners.length > 0 && (
          <>
            <HeaderRow title={t('consenters')} />
            {consenterSigners.map((signer) => (
              <ParticipantCard
                participant={mapSignerToParticipant(signer)}
                key={signer.id}
                variant="signing"
                signingStatus={signer.status}
                onRemove={() => {
                  deleteSignerMutation({
                    documentSigningId: signing.id,
                    documentSignerId: signer.id,
                  })
                }}
              />
            ))}
          </>
        )}
        {otherSigners.length > 0 && (
          <>
            <HeaderRow title={t('signing.otherSigners')} />
            {otherSigners.map((signer) => (
              <ParticipantCard
                participant={mapSignerToParticipant(signer)}
                key={signer.id}
                variant="signing"
                signingStatus={signer.status}
                onRemove={() => {
                  deleteSignerMutation({
                    documentSigningId: signing.id,
                    documentSignerId: signer.id,
                  })
                }}
              />
            ))}
          </>
        )}
        <Stack>
          <SigningHistory events={signing.events} />
        </Stack>
      </Stack>
      {isAddSignerModalOpen && (
        <AddSignerModal
          isOpen={isAddSignerModalOpen}
          onClose={() => {
            void queryClient.invalidateQueries({
              queryKey: ['fi-sales-agreement'],
            })
            setIsAddSignerModalOpen(false)
          }}
          documentSigningId={signing.id}
        />
      )}
      {isDeleteSigningModalOpen && (
        <ConfirmationModal
          isOpen={isDeleteSigningModalOpen}
          isCentered={true}
          title={t('signing.deleteSigning')}
          message={t('signing.deleteSigningDescription')}
          onAccept={() => {
            deleteSigningMutation({
              documentSigningId: signing.id,
            })
          }}
          onReject={() => setIsDeleteSigningModalOpen(false)}
          hideText={false}
        />
      )}
    </StepContentWithFooter>
  )
}

export const SigningHistory = ({
  events,
}: {
  events: SchemaDocumentEvent[]
}) => {
  const { t } = useTranslation(['common'])

  const parsedEvents = events
    .map((event) => parseSigningPayload(event.rawPayload))
    .filter((e): e is ParsedSigningEvent => !!e)

  return (
    <Box>
      <HeaderRow title={t('history')} />

      <Stack spacing={0}>
        {parsedEvents.map((event, i) => (
          <>
            <SigningHistoryRow key={i} event={event} />
            <Divider m={0} />
          </>
        ))}
      </Stack>
    </Box>
  )
}
export const SigningHistoryRow = ({ event }: { event: ParsedSigningEvent }) => {
  const { t } = useTranslation(['common'])

  return (
    <Stack direction="row" alignItems="center" py={3} gap={20}>
      <Text fontWeight={600} fontSize="sm">
        {formatDateAndTime(event.signing_time)}
      </Text>
      <Text mt={0} fontSize="sm">{`${event.first_name} ${event.last_name}  ${t(
        `signing.signingEvents.${event.action}`
      ).toLowerCase()}`}</Text>
    </Stack>
  )
}

interface ParsedSigningEvent {
  action: string
  signer_token: string
  first_name: string | null
  last_name: string | null
  signing_time: string | null
}

function parseSigningPayload(payload): ParsedSigningEvent | null {
  const { action, signer_token, signers = [] } = payload

  const matchingSigner = signers.find((signer) => signer.token === signer_token)

  if (!matchingSigner) {
    return null
  }

  const firstName = matchingSigner.first_name || null
  const lastName = matchingSigner.last_name || null
  const signingTime = matchingSigner.signature?.signing_time || null

  return {
    action,
    signer_token,
    first_name: firstName,
    last_name: lastName,
    signing_time: signingTime,
  }
}
