import { Grid, GridItem, Stack } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { BooleanBadgeGroupField, getBadgeGroupItemLabel } from '.'
import BadgeGroupField from '@/components/Badge/BadgeGroupField'
import { InputWithLabel } from '@/components/Form/Input'
import {
  FISalesAgreementAcquisitionEnum,
  FISalesAgreementTaxConsequenceEnum,
  SchemaFiSalesAgreementContactAcquisitionBase,
} from '@/generated-types/api'
import Instructions from '@/components/Instructions/Instructions'
import AnimatedContainer from '@/components/AnimatedContainer/AnimatedContainer'
import { useMobile } from '@/hooks/useMobile'

export const ContactAcquisitionFields = ({
  contactAcquisition,
  contactIndex,
  contactName,
}: {
  contactAcquisition: SchemaFiSalesAgreementContactAcquisitionBase
  contactIndex: number
  contactName?: string
}) => {
  const { t } = useTranslation(['common'])
  const isMobile = useMobile()

  return (
    <Grid
      gap="1rem"
      templateColumns={{ base: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }}
    >
      <GridItem colSpan={2}>
        <Stack>
          <BadgeGroupField
            toggle
            name={`contactAcquisitions.[${contactIndex}].acquisition`}
            label={getBadgeGroupItemLabel(
              `${t('fiSalesAgreement.form.acquisitionLabel')} ${
                contactName || contactAcquisition.contactId.toString()
              }`
            )}
            items={Object.entries(FISalesAgreementAcquisitionEnum)}
            itemLabels={([key]) =>
              t(`fiSalesAgreement.form.enums.acquisition.${key}`)
            }
            type="enum"
          />
        </Stack>
      </GridItem>
      <GridItem colSpan={2}>
        <Stack>
          <BooleanBadgeGroupField
            toggle
            label={t(
              'fiSalesAgreement.form.sellerIsMarriedOrInRegisteredRelationshipLabel'
            )}
            name={`contactAcquisitions.[${contactIndex}].sellerIsMarriedOrInRegisteredRelationship`}
          />
          <BooleanBadgeGroupField
            toggle
            label={t('fiSalesAgreement.form.sellerHasSpousesConsentLabel')}
            name={`contactAcquisitions.[${contactIndex}].sellerHasSpousesConsent`}
          />
          <BooleanBadgeGroupField
            toggle
            label={t(
              'fiSalesAgreement.form.sellerHasBeenMarriedOrInRegisteredRelationshipLabel'
            )}
            name={`contactAcquisitions.[${contactIndex}].sellerHasBeenMarriedOrInRegisteredRelationship`}
          />
          <BooleanBadgeGroupField
            toggle
            label={t('fiSalesAgreement.form.divorceLegallyBindingLabel')}
            name={`contactAcquisitions.[${contactIndex}].divorceLegallyBinding`}
          />
          <BooleanBadgeGroupField
            toggle
            label={t('fiSalesAgreement.form.legalPartitioningIsCompleteLabel')}
            name={`contactAcquisitions.[${contactIndex}].legalPartitioningIsComplete`}
          />
          <InputWithLabel
            label={t('fiSalesAgreement.form.acquisitionDateLabel')}
            name={`contactAcquisitions.[${contactIndex}].acquisitionDate`}
            defaultValue={contactAcquisition?.acquisitionDate}
            type="date"
            max="9999-12-31"
          />
          <InputWithLabel
            label={t('fiSalesAgreement.form.acquisitionCostLabel')}
            name={`contactAcquisitions.[${contactIndex}].acquisitionCost`}
            defaultValue={contactAcquisition?.acquisitionCost}
          />
        </Stack>
      </GridItem>
      <GridItem colSpan={2}>
        <BooleanBadgeGroupField
          toggle
          label={t(
            'fiSalesAgreement.form.clientHasUsedResidenceAsResidenceLabel'
          )}
          name={`contactAcquisitions.[${contactIndex}].clientHasUsedResidenceAsResidence`}
        />
      </GridItem>
      <GridItem colSpan={2}>
        <AnimatedContainer
          expanded={
            contactAcquisition?.clientHasUsedResidenceAsResidence || false
          }
        >
          <Stack gap={'1rem'} direction={isMobile ? 'column' : 'row'}>
            <InputWithLabel
              label={t('fiSalesAgreement.form.residencyStartDateLabel')}
              name={`contactAcquisitions.[${contactIndex}].residencyStartDate`}
              type="date"
              defaultValue={contactAcquisition?.residencyStartDate}
              max="9999-12-31"
            />
            <InputWithLabel
              label={t('fiSalesAgreement.form.residencyEndDateLabel')}
              name={`contactAcquisitions.[${contactIndex}].residencyEndDate`}
              type="date"
              defaultValue={contactAcquisition?.residencyEndDate}
              max="9999-12-31"
            />
          </Stack>
        </AnimatedContainer>
      </GridItem>
      <GridItem colSpan={2}>
        <BadgeGroupField
          toggle
          name={`contactAcquisitions.[${contactIndex}].taxConsequence`}
          label={getBadgeGroupItemLabel(
            t('fiSalesAgreement.form.taxConsequencesLabel')
          )}
          items={Object.entries(FISalesAgreementTaxConsequenceEnum)}
          itemLabels={([key]) =>
            t(`fiSalesAgreement.form.enums.taxConsequences.${key}`)
          }
          type="enum"
        />
      </GridItem>
      {!isMobile && (
        <GridItem colSpan={1} rowSpan={2}>
          <Instructions
            instruction={t('fiSalesAgreement.form.instructions.tax')}
          />
        </GridItem>
      )}
    </Grid>
  )
}
