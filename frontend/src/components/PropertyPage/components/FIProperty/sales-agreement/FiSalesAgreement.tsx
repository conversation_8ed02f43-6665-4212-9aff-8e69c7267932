import { Box } from '@chakra-ui/react'
import { EditSalesAgreementForm } from './EditSalesAgreementForm'
import { CreateFiSalesAgreement } from './CreateSalesAgreementForm'
import { useTranslation } from 'next-i18next'
import { PreviewSalesAgreement } from './PreviewSalesAgreement'
import { Signatures } from './Signatures'
import { MultiStepFormContainer } from '@/components/MultiStepForm/MultiStepFormContainer'
import { Step } from '@/components/MultiStepForm/Step'
import {
  COMPLETED_STEP,
  FILL_FORM_STEP,
  mapStatusToAllowedStep,
  mapStatusToAllowedSteps,
  PREVIEW_STEP,
  SIGNATURES_STEP,
} from './common'
import { useFISalesAgreement } from '@/modules/fi-properties/queries/queryFISalesAgreements'

const FISalesAgreement = ({
  salesAgreementId,
  create,
}: {
  salesAgreementId: string | null
  create: string | null
}) => {
  const { t } = useTranslation(['common'])
  const { data } = useFISalesAgreement(salesAgreementId ?? '')

  const steps = [
    { title: t('fiSalesAgreement.editStep') },
    { title: t('fiSalesAgreement.validateStep') },
    { title: t('fiSalesAgreement.signatureStep') },
  ]

  const renderForm = () => {
    if (salesAgreementId) {
      return <EditSalesAgreementForm salesAgreementId={salesAgreementId} />
    }
    if (create) {
      return <CreateFiSalesAgreement />
    }
  }

  if (salesAgreementId && !data) {
    return null
  }

  return (
    <MultiStepFormContainer
      steps={steps}
      initialStep={mapStatusToAllowedStep(data?.status)}
      allowedSteps={mapStatusToAllowedSteps(data?.status)}
    >
      <Step stepIndex={FILL_FORM_STEP}>
        <Box padding="1rem">{renderForm()}</Box>
      </Step>
      <Step stepIndex={PREVIEW_STEP}>
        {salesAgreementId && (
          <Box padding="1rem">
            <PreviewSalesAgreement salesAgreementId={salesAgreementId} />
          </Box>
        )}
      </Step>
      <Step stepIndex={SIGNATURES_STEP}>
        {salesAgreementId && (
          <Box padding="1rem">
            <Signatures salesAgreementId={salesAgreementId} />
          </Box>
        )}
      </Step>
      <Step stepIndex={COMPLETED_STEP}>
        {salesAgreementId && (
          <Box padding="1rem">
            <Signatures salesAgreementId={salesAgreementId} />
          </Box>
        )}
      </Step>
    </MultiStepFormContainer>
  )
}

export default FISalesAgreement
