import { useFISalesAgreement } from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { Stack } from '@chakra-ui/react'
import { FISalesAgreementStatusEnum } from '@/generated-types/api'
import { SalesAgreementSignatures } from './common/SalesAgreementSignatures'

export const Signatures = ({
  salesAgreementId,
}: {
  salesAgreementId: string
}) => {
  const { data } = useFISalesAgreement(salesAgreementId)

  if (!data) {
    return null
  }

  return (
    <Stack>
      {(data.status === FISalesAgreementStatusEnum.pending_signatures ||
        data.status === FISalesAgreementStatusEnum.completed) && (
        <SalesAgreementSignatures salesAgreement={data} />
      )}
    </Stack>
  )
}
