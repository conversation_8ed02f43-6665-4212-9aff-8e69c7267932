import {
  createEmptyContactAcquisition,
  emptyFiSalesAgreement,
  FISalesAgreementCreate,
} from '@/modules/fi-properties'
import { Formik, FormikProps } from 'formik'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { getCurrentUser } from '@/queries/users'
import { createFISalesAgreement } from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { useToast } from '@chakra-ui/react'
import { useRef } from 'react'
import {
  FISalesAgreementSchema,
  mapStatusToAllowedSteps,
  SalesAgreementFormBody,
} from './common'
import { useTranslation } from 'next-i18next'
import { useInPropertyDetailPage } from '@/hooks/useInPropertyPage'
import { useRouter } from 'next/router'
import { FISalesAgreementStatusEnum } from '@/generated-types/api'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

export const CreateFiSalesAgreement = () => {
  const { goToNextStep } = useMultiStepForm()
  const isMobile = useMobile()
  const formRef = useRef<FormikProps<FISalesAgreementCreate>>(null)
  const { t } = useTranslation(['common'])
  const validationSchema = FISalesAgreementSchema(t)
  const toast = useToast()
  const router = useRouter()
  const { referenceCode, property } = useInPropertyDetailPage()

  const queryClient = useQueryClient()
  const { data: currentUser } = useQuery({
    queryKey: ['me'],
    queryFn: () => getCurrentUser(),
  })

  const {
    isPending: isCreatingFISalesAgreement,
    mutate: createSalesAgreement,
  } = useMutation({
    mutationFn: createFISalesAgreement,
    onSuccess: async (result) => {
      queryClient.invalidateQueries({
        queryKey: ['fi-sales-agreements'],
      })

      if (result.status !== FISalesAgreementStatusEnum.draft) {
        goToNextStep({
          overrideAllowedSteps: mapStatusToAllowedSteps(result.status),
        })
      }
      toast(
        getToastProps({
          isMobile,
          title: t('fiSalesAgreement.created'),
          status: 'success',
          variant: 'customSuccess',
        })
      )

      router.push(
        {
          pathname: router.pathname,
          query: {
            referenceCode,
            salesAgreementId: result.id,
          },
        },
        undefined,
        { shallow: true }
      )
    },
  })

  const handleOnSave = async (validate?: boolean) => {
    if (!formRef.current) {
      return
    }

    if (validate) {
      const validation = await formRef.current.validateForm()
      if (Object.keys(validation).length > 0) {
        formRef.current.setErrors(validation)
        await formRef.current.setTouched(
          Object.keys(validation).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {})
        )
        toast(
          getToastProps({
            isMobile,
            title: t('fiSalesAgreement.form.validation.errorToast'),
            status: 'error',
          })
        )
        return
      }
    }

    await createSalesAgreement({
      ...formRef.current.values,
      validate,
    })
  }

  if (!property) {
    return null
  }

  // auto add consenters where applicable
  const consenterIds =
    property.contacts?.flatMap((contact) =>
      contact.relatedParties?.map(({ id }) => id)
    ) || []

  const emptySalesAgreement: FISalesAgreementCreate = {
    ...emptyFiSalesAgreement,
    propertyId: property.id,
    realtorUserIds: currentUser?.id ? [currentUser.id] : [],
    contactIds: (property.contacts || []).map((c) => c.id),
    consenterIds,
    status: FISalesAgreementStatusEnum.draft,
    contactAcquisitions:
      property.contacts?.map((contact) =>
        createEmptyContactAcquisition(contact.id)
      ) || [],
  }

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('fiSalesAgreement.saveAsDraft'),
            onClick: async () => handleOnSave(),
            isLoading: isCreatingFISalesAgreement,
            buttonVariant: 'transparent',
          },
          {
            label: t('next'),
            onClick: async () => handleOnSave(true),
            isLoading: isCreatingFISalesAgreement,
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={emptySalesAgreement}
        enableReinitialize
        validationSchema={validationSchema}
        validateOnBlur
        validateOnMount
        onSubmit={() => handleOnSave(true)}
      >
        {(formikProps) => (
          <SalesAgreementFormBody
            {...formikProps}
            preContactsOptions={
              property?.contacts?.map(({ name, id }) => ({
                id,
                name,
              })) || []
            }
          />
        )}
      </Formik>
    </StepContentWithFooter>
  )
}
