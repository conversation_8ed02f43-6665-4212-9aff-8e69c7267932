import React from 'react'
import {
  Box,
  Heading,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useToast,
} from '@chakra-ui/react'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import {
  createFISalesAgreement,
  fetchDocumentSignignStatus,
  getFISalesAgreementAsPdf,
  getFISalesAgreementsForProperty,
  sendReminderForSigners,
} from '@/modules/fi-properties/queries/queryFISalesAgreements'
import { useInPropertyDetailPage } from '@/hooks/useInPropertyPage'
import { ReadProperty } from '@/types/property'
import { useTranslation } from 'next-i18next'
import {
  FI_DATE_TIME_FORMAT,
  FI_DATE_TIME_OUTPUT,
  formatDateAndTime,
} from '@/utils/date'
import { useRouter } from 'next/router'
import { Dropdown, Loader } from 'rsuite'
import { MoreOptionsButton } from '@/components/IconButtons/MoreOptionsButton'
import {
  FISalesAgreementStatusEnum,
  SchemaFiSalesAgreementRead,
} from '@/generated-types/api'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'

export const InfoBox = ({
  errorMessage,
}: {
  errorMessage: string | undefined
}) => {
  const { t } = useTranslation(['common'])
  return (
    <Box bg="#F6F1ED" p={4} borderRadius="lg" mb={0} mt={4}>
      <Text fontSize="lg" fontWeight="bold">
        {t('shareTrade.preparation.missingRequiredData')}
      </Text>
      <Text fontSize="md">
        {t('shareTrade.preparation.missingRequiredDataDescription')}
      </Text>
      <Text fontSize="md">{errorMessage}</Text>
    </Box>
  )
}
const FISalesAgreementList = () => {
  const { t } = useTranslation(['common'])
  const { property } = useInPropertyDetailPage()
  const { data } = useQuery({
    queryKey: ['fi-sales-agreements'],
    queryFn: () => {
      return getFISalesAgreementsForProperty({
        propertyId: (property as ReadProperty).id?.toString(),
      })
    },
  })
  if (!property) {
    return null
  }
  if (!data) {
    return null
  }

  if (data.items.length === 0) {
    return (
      <Stack p={4}>
        <Stack bg="#F6F1ED" p={4} borderRadius="lg" gap="0">
          <Text fontSize="lg" fontWeight="bold">
            {t('fiSalesAgreement.noSalesAgreements')}
          </Text>
          <Text fontSize="md">
            {t('fiSalesAgreement.noSalesAgreementsDescription')}
          </Text>
        </Stack>
      </Stack>
    )
  }
  return (
    <Stack overflowX="auto" p={6}>
      <Heading variant="H1">{t('fiSalesAgreement.salesAgreements')}</Heading>
      <Table
        border="1px solid"
        borderColor="grays.grayBorder"
        position="relative"
        style={{ verticalAlign: 'top' }}
      >
        <Thead style={{ verticalAlign: 'top' }}>
          <Tr pt="10px">
            <Th p={'1rem'}>{t('name')}</Th>
            <Th p={'1rem'}>{t('status')}</Th>
            <Th></Th>
          </Tr>
        </Thead>
        <Tbody position="relative" style={{ verticalAlign: 'top' }}>
          {data.items.map((salesAgreement, index) => (
            <FISalesAgreementTableRow
              key={index}
              salesAgreement={salesAgreement}
            />
          ))}
        </Tbody>
      </Table>
    </Stack>
  )
}

export const FISalesAgreementTableRow = ({
  salesAgreement,
}: {
  salesAgreement: SchemaFiSalesAgreementRead
}) => {
  const router = useRouter()
  const { t } = useTranslation(['common'])
  const queryClient = useQueryClient()
  const toast = useToast()
  const isMobile = useMobile()

  const navigateToSalesAgreement = () =>
    router.push({
      query: {
        ...router.query,
        salesAgreementId: salesAgreement.id,
        callbackUrl: router.asPath,
      },
    })

  const {
    isPending: isCreatingFISalesAgreement,
    mutate: createSalesAgreement,
  } = useMutation({
    mutationFn: createFISalesAgreement,
    onSuccess: async (_result) => {
      queryClient.invalidateQueries({
        queryKey: ['fi-sales-agreements'],
      })

      toast(
        getToastProps({
          title: t('fiSalesAgreement.created'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
    },
  })

  const { mutate: updateDocumentSignignStatus } = useMutation({
    mutationFn: fetchDocumentSignignStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fi-sales-agreements'],
      })
    },
    onError: (error) => {
      console.error('Failed to fetch document signing status:', error)
    },
  })

  const { mutate: sendReminder } = useMutation({
    mutationFn: sendReminderForSigners,
    onSuccess: () => {
      toast(
        getToastProps({
          title: t('fiSalesAgreement.reminderSent'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
    },
    onError: (error) => {
      console.error(
        'Failed to send reminders for sales agreement signers:',
        error
      )
    },
  })

  return (
    <Tr _hover={{ bg: 'gray.50' }} verticalAlign="middle">
      <Td p={'1rem'} cursor="pointer" onClick={navigateToSalesAgreement}>
        <Stack gap="0">
          <Text fontWeight={500}>{`${t('fiSalesAgreement.salesAgreement')} ${
            salesAgreement.id
          }`}</Text>
          <Text variant="small" textTransform="capitalize">
            {t('created')}
            {': '}
            {formatDateAndTime(
              salesAgreement.createdAt,
              FI_DATE_TIME_FORMAT,
              FI_DATE_TIME_OUTPUT
            )}
          </Text>
        </Stack>
      </Td>
      <Td p={'1rem'} cursor="pointer" onClick={navigateToSalesAgreement}>
        {t(`fiSalesAgreement.form.enums.status.${salesAgreement.status}`)}
      </Td>
      <Td p={'1rem'} cursor="pointer" textAlign="right">
        <Dropdown
          placement="bottomEnd"
          renderToggle={(props, ref) =>
            isCreatingFISalesAgreement ? (
              <Loader />
            ) : (
              <MoreOptionsButton {...props} ref={ref}></MoreOptionsButton>
            )
          }
          style={{ position: 'relative' }}
        >
          <Dropdown.Item
            onClick={async () => {
              await getFISalesAgreementAsPdf(salesAgreement.id.toString())
            }}
          >
            {t('downloadPDF')}
          </Dropdown.Item>
          <Dropdown.Item
            onClick={async () => {
              await createSalesAgreement({
                ...salesAgreement,
                contactIds: salesAgreement.contacts.map((c) => c.id),
                realtorUserIds: salesAgreement.realtorUsers.map((r) => r.id),
                consenterIds: salesAgreement.consenters.map((c) => c.id),
                validate: false,
              })
            }}
          >
            {t('fiSalesAgreement.copyAsNew')}
          </Dropdown.Item>
          {salesAgreement.status ===
            FISalesAgreementStatusEnum.pending_signatures && (
            <>
              <Dropdown.Item
                onClick={async () => {
                  await updateDocumentSignignStatus({
                    salesAgreementId: salesAgreement.id.toString(),
                  })
                }}
              >
                {t('fiSalesAgreement.fetchSigningStatus')}
              </Dropdown.Item>
              <Dropdown.Item
                onClick={async () => {
                  await sendReminder({
                    salesAgreementId: salesAgreement.id.toString(),
                  })
                }}
              >
                {t('fiSalesAgreement.sendReminder')}
              </Dropdown.Item>
            </>
          )}
        </Dropdown>
      </Td>
    </Tr>
  )
}

export default FISalesAgreementList
