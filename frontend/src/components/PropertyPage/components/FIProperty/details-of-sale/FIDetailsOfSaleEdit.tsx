import { useToast } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useRef } from 'react'
import { useTranslation } from 'next-i18next'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { omit } from 'lodash'
import {
  formatDetailsOfSaleApiToFormValues,
  formatDetailsOfSaleFormValuesToApi,
} from './common/utils'
import { FIDetailsOfSaleSchema } from './common/FIDetailsOfSaleSchema'
import {
  editFIDetailsOfSale,
  getFiDetailsOfSalePrefill,
  useFiDetailsOfSale,
} from '@/modules/fi-properties/queries/queryFIDetailsOfSale'
import { FIDetailsOfSaleCreate } from '@/modules/fi-properties/types/FIDetailsOfSale'
import { FIDetailsOfSaleFormBody } from './FIDetailsOfSaleFormBody'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'

export const FIDetailsOfSaleEdit = ({
  detailsOfSaleId,
}: {
  detailsOfSaleId: string
}) => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const validationSchema = FIDetailsOfSaleSchema(t)
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FIDetailsOfSaleCreate>()
  const formRef = useRef<FormikProps<FIDetailsOfSaleCreate>>(null)
  const toast = useToast()
  const queryClient = useQueryClient()
  const { goToNextStep } = useMultiStepForm()

  const { data } = useFiDetailsOfSale(detailsOfSaleId)
  const { data: prefillData } = useQuery({
    queryKey: ['fi-details-of-sale-prefill', data?.propertyId],
    queryFn: () => getFiDetailsOfSalePrefill(data?.propertyId ?? 0),
    enabled: !!data?.propertyId,
  })

  const { isPending, mutate: editDetailsOfSale } = useMutation({
    mutationFn: editFIDetailsOfSale,
    onSuccess: async (_) => {
      queryClient.invalidateQueries({
        queryKey: ['fi-details-of-sale', detailsOfSaleId],
      })
      goToNextStep()
      toast(
        getToastProps({
          isMobile,
          title: t('fiDetailsOfSale.updated'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
    },
  })

  const handleOnSave = async (validate = false) => {
    if (
      !formRef.current ||
      !(await validateAndSetErrors(
        formRef.current,
        t('fiDetailsOfSale.form.validation.editErrorToast')
      ))
    ) {
      return
    }

    const values = formRef.current.values
    const formattedValues = {
      ...values,
      ...formatDetailsOfSaleFormValuesToApi(values),
    }

    await editDetailsOfSale({
      ...formattedValues,
      detailsOfSaleId,
      validate,
    })
  }

  if (!data) {
    return null
  }

  const formattedData = { ...data, ...formatDetailsOfSaleApiToFormValues(data) }

  const mappedData = omit(formattedData, [
    'id',
    'createdAt',
  ]) satisfies FIDetailsOfSaleCreate

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('saveAsDraft'),
            onClick: () => handleOnSave(),
            isLoading: isPending,
          },
          {
            label: t('next'),
            onClick: () => handleOnSave(true),
            isLoading: isPending,
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={mappedData}
        validationSchema={validationSchema}
        validateOnBlur={false}
        validateOnChange={false}
        validateOnMount
        onSubmit={() => handleOnSave()}
      >
        {(formikProps) => (
          <FIDetailsOfSaleFormBody
            formProps={formikProps}
            commissionBasisCode={prefillData?.commissionBasisCode ?? undefined}
          />
        )}
      </Formik>
    </StepContentWithFooter>
  )
}
