import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { pick } from 'lodash'

import { Heading, Stack, Text } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'

import {
  getFIDetailsOfSale,
  updateFiDetailsOfSaleStatus,
} from '@/modules/fi-properties/queries/queryFIDetailsOfSale'
import { FIPropertyCard } from '@/components/Card'
import { ContactCard } from './common/ContactCard'

import {
  FIDetailsOfSaleStatus,
  SchemaFiDetailsOfSaleRead,
  SchemaUserRead,
} from '@/generated-types/api'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import { DataGroup } from '@/components/DataGroup/DataGroup'
import { getUserFullName } from '@/utils/getUserFullName'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { formatCurrency } from '@/utils/formatNumber'
import { TFunction } from 'i18next'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { useMobile } from '@/hooks/useMobile'

const mapDetailsOfSalePreviewValues =
  (t: TFunction) =>
  (
    agreement: Partial<SchemaFiDetailsOfSaleRead>
  ): { label: string; value: string }[] => {
    return Object.entries(agreement).map(([key, value]) => ({
      label: t(`fiDetailsOfSale.form.fields.${key}`),
      value: value ? value.toString() : '-',
    }))
  }

export const FIDetailsOfSalePreview = ({
  detailsOfSaleId,
}: {
  detailsOfSaleId: string
}) => {
  const isMobile = useMobile()
  const queryClient = useQueryClient()
  const { t } = useTranslation(['common'])
  const { data, isPending } = useQuery({
    queryKey: ['fi-details-of-sale', detailsOfSaleId],
    queryFn: () => {
      return getFIDetailsOfSale(detailsOfSaleId)
    },
  })

  const { isPending: isUpdating, mutate: updateStatus } = useMutation({
    mutationFn: updateFiDetailsOfSaleStatus,
    onSuccess: async (_) => {
      queryClient.invalidateQueries({
        queryKey: ['fi-details-of-sale', detailsOfSaleId],
      })
    },
  })
  if (!data) {
    return null
  }

  const action =
    data.status === FIDetailsOfSaleStatus.validated
      ? {
          label: t('fiDetailsOfSale.checkAndLockButton'),
          onClick: async () => {
            await updateStatus({
              detailsOfSaleId,
              status: FIDetailsOfSaleStatus.locked,
            })
          },
          disabled: isUpdating || isPending,
        }
      : {
          label: t('fiDetailsOfSale.unlockButton'),
          onClick: async () => {
            await updateStatus({
              detailsOfSaleId,
              status: FIDetailsOfSaleStatus.validated,
            })
          },
          disabled: isUpdating || isPending,
        }

  return (
    <StepContentWithFooter
      maxW={isMobile ? '100%' : '66%'}
      footerProps={{
        actions: [action],
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Heading fontSize="xx-large" minH={'35px'}>
          {t('fiDetailsOfSale.previewHeading')}
        </Heading>
      </Stack>
      <Stack spacing={4}>
        <HeaderRow title={t('sellers')} />
        <Stack direction="row">
          {data &&
            data.sellers != null &&
            data.sellers.map((seller) => (
              <ContactCard key={seller.sellerId} contactId={seller.sellerId} />
            ))}
        </Stack>
      </Stack>
      <Stack spacing={4}>
        <HeaderRow title={t('buyers')} />
        <Stack direction="row">
          {data &&
            data.buyers != null &&
            data.buyers.map((buyer) => (
              <ContactCard key={buyer.buyerId} contactId={buyer.buyerId} />
            ))}
        </Stack>
      </Stack>
      <FIPropertyCard
        header={t('property') ?? ''}
        property={data.property}
        onModalClose={() =>
          queryClient.invalidateQueries({
            queryKey: ['fi-sales-agreement'],
          })
        }
      />
      <DataGroup
        title={t('fiDetailsOfSale.title')}
        values={mapDetailsOfSalePreviewValues(t)(
          pick(data, [
            'transActionMethod',
            'propertyPublishedAt',
            'estimatedTransactionDate',
            'saleDurationDays',
            'offerCount',
            'highestRejectedOffer',
            'salePrice',
            'debtFreePrice',
            'mortgageBank',
            'notes',
          ])
        )}
      />
      <DataGroup
        title={t('fiDetailsOfSale.commission')}
        values={mapDetailsOfSalePreviewValues(t)(
          pick(data, [
            'commissionAmountTotal',
            'commissionPercent',
            'commissionVatPercent',
            'commissionVatIncluded',
            'commissionAmountWithoutVat',
            'commissionVatAmount',
            'commissionAmountWithVat',
          ])
        )}
      />
      {data.recipients && (
        <Stack flex={1}>
          <HeaderRow title={t('fiDetailsOfSale.commissionDistribution')} />
          {data.recipients.map((recipient) => (
            <CommisionRecipientRow
              key={recipient.userId}
              user={recipient.user}
              role={recipient.role}
              comissionPercentage={recipient.commissionPercent}
              comissionAmount={recipient.commissionAmount}
              vatIncluded={data.commissionVatIncluded}
              vatPercentage={data.commissionVatPercent}
            />
          ))}
        </Stack>
      )}
    </StepContentWithFooter>
  )
}

const CommisionRecipientRow = ({
  user,
  role,
  comissionPercentage,
  comissionAmount,
  vatIncluded,
  vatPercentage,
}: {
  user: SchemaUserRead | null
  role: string
  comissionPercentage: number | null
  comissionAmount: number | null
  vatIncluded: boolean | null
  vatPercentage: number | null
}) => {
  const { currency, locale } = useUserAndOrganization()

  const { t } = useTranslation(['common'])

  if (!user) return null

  const name = getUserFullName(user) ?? t('missingName')

  return (
    <Stack direction="row" alignItems="end" borderBottom="1px solid #F2F2F2">
      <Text flex="4" fontSize="medium" fontWeight={700}>
        {`${comissionPercentage}% - ${role}, ${name}`}
      </Text>
      <Text flex="8" fontSize="medium">
        {formatCurrency({
          value: comissionAmount,
          currency,
          locale,
        })}{' '}
        {vatIncluded
          ? t('commissionSummary.includingVat', {
              vatPercentage,
            })
          : ''}
      </Text>
    </Stack>
  )
}
