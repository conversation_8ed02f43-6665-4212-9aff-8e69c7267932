import { Center, Spinner, Stack, Text, useToast } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useRef } from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  formatDetailsOfSaleFormValuesToApi,
  mapOwnersWithEqualShare,
} from './common/utils'
import {
  emptyDetailsOfSale,
  FIDetailsOfSaleCreate,
} from '@/modules/fi-properties/types/FIDetailsOfSale'
import { FIDetailsOfSaleSchema } from './common/FIDetailsOfSaleSchema'
import {
  createFIDetailsOfSale,
  getFiDetailsOfSalePrefill,
} from '@/modules/fi-properties/queries/queryFIDetailsOfSale'
import { FIDetailsOfSaleFormBody } from './FIDetailsOfSaleFormBody'
import { useInFIPropertyDetailPage } from '@/hooks/useInFiPropertyPage'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'
import { SchemaFiDetailsOfSalePrefillRead } from '@/generated-types/api'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

const FICreateDetailsOfSaleForm = ({
  prefillData,
}: {
  prefillData: SchemaFiDetailsOfSalePrefillRead
}) => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const validationSchema = FIDetailsOfSaleSchema(t)
  const formRef = useRef<FormikProps<FIDetailsOfSaleCreate>>(null)
  const toast = useToast()
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FIDetailsOfSaleCreate>()
  const { property, referenceCode } = useInFIPropertyDetailPage()
  const router = useRouter()

  const queryClient = useQueryClient()

  const { isPending, mutate: createDetailsOfSale } = useMutation({
    mutationFn: createFIDetailsOfSale,
    onSuccess: async (res) => {
      queryClient.invalidateQueries({
        queryKey: ['fi-details-of-sale'],
      })

      toast(
        getToastProps({
          isMobile,
          title: t('fiDetailsOfSale.created'),
          status: 'success',
          variant: 'customSuccess',
        })
      )

      router.push(
        {
          pathname: router.pathname,
          query: {
            referenceCode: referenceCode,
            detailsOfSaleId: res.id,
            view: 'detailsOfSale',
            callbackUrl: router.asPath,
          },
        },
        undefined,
        { shallow: true }
      )
    },
  })

  const handleOnSave = async (validate = false) => {
    if (
      !formRef.current ||
      !(await validateAndSetErrors(
        formRef.current,
        t('fiDetailsOfSale.form.validation.createErrorToast')
      ))
    ) {
      return
    }

    const values = formRef.current.values
    const formattedValues = {
      ...values,
      ...formatDetailsOfSaleFormValuesToApi(values),
    }

    await createDetailsOfSale({ ...formattedValues, validate })
  }

  if (!property) {
    return null
  }
  const initialData: FIDetailsOfSaleCreate = {
    ...emptyDetailsOfSale,
    ...(prefillData ?? {}),
    strandCommissionPercent: 30,
    sellers: mapOwnersWithEqualShare(prefillData?.sellers ?? []),
    buyers: mapOwnersWithEqualShare(prefillData?.buyers ?? []),
    propertyId: property.id,
  }

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('saveAsDraft'),
            onClick: () => handleOnSave(),
            isLoading: isPending,
          },
          {
            label: t('next'),
            onClick: () => handleOnSave(true),
            isLoading: isPending,
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={initialData}
        validationSchema={validationSchema}
        validateOnBlur={false}
        validateOnChange={false}
        validateOnMount
        onSubmit={() => handleOnSave()}
      >
        {(formikProps) => (
          <FIDetailsOfSaleFormBody
            formProps={formikProps}
            commissionBasisCode={prefillData?.commissionBasisCode ?? undefined}
          />
        )}
      </Formik>
    </StepContentWithFooter>
  )
}

export const FICreateDetailsOfSaleFormWrapper = () => {
  const { property } = useInFIPropertyDetailPage()
  const { t } = useTranslation(['common'])

  const {
    data: prefillData,
    isPending,
    isError,
    isSuccess,
  } = useQuery({
    queryKey: ['fi-details-of-sale-prefill', property?.id],
    queryFn: () => getFiDetailsOfSalePrefill(property?.id ?? 0),
    enabled: !!property?.id,
  })

  return (
    <>
      {isPending && (
        <Center w="100%" flex={1}>
          <Spinner />
        </Center>
      )}
      {isError && (
        <Stack pt={10}>
          <Text>{t('fiDetailsOfSale.failedToLoadPrefillData')}</Text>
        </Stack>
      )}
      {isSuccess && <FICreateDetailsOfSaleForm prefillData={prefillData} />}
    </>
  )
}
