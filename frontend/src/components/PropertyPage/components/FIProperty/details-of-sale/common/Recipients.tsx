import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@chakra-ui/react'
import {
  FieldArray,
  useField,
  useFormikContext,
  FieldArrayRenderProps,
} from 'formik'
import { InputWithLabel } from '@/components/Form/Input'
import { useTranslation } from 'next-i18next'

import { FIDetailsOfSaleCreate } from '@/modules/fi-properties/types/FIDetailsOfSale'
import { SchemaFiRecipientDetailsOfSaleSchema } from '@/generated-types/api'
import FiCustomerSelect from '@/components/FICustomerSelect'
import LabeledField from '@/components/Form/LabeledField'
import { useEffect, useMemo } from 'react'
import { SimpleDropdown } from '@/components/DropdownMenu/SimpleDropdown'
import { useMobile } from '@/hooks/useMobile'
import { calculateRealtorCommissionPercent } from './utils'

const parseCommissionPercentErrors = (error: unknown): string | undefined => {
  if (typeof error === 'string') return error
  if (error && typeof error === 'object' && 'commissionPercent' in error) {
    return (error as { commissionPercent: string }).commissionPercent
  }
  return undefined
}

export const Recipients = () => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const [field, , helpers] =
    useField<SchemaFiRecipientDetailsOfSaleSchema[]>('recipients')
  const { value = [] } = field
  const { values, errors, touched } = useFormikContext<FIDetailsOfSaleCreate>()

  const roles = [
    {
      value: 'selling',
      label: t(`fiDetailsOfSale.form.enums.recipientRole.selling`),
    },
    {
      value: 'brokerage_firm',
      label: t(`fiDetailsOfSale.form.enums.recipientRole.brokerage_firm`),
    },
  ]

  const realtorCommissionPercent = useMemo(
    () =>
      calculateRealtorCommissionPercent(
        Number(values.strandCommissionPercent ?? 0),
        values.leads
      ),
    [values.strandCommissionPercent, values.leads]
  )

  const realtorBaseAmount = useMemo(() => {
    const totalAmount = values.commissionAmountWithVat ?? 0
    const safeRealtorPercent = Math.max(0, realtorCommissionPercent)
    return Number(((totalAmount * safeRealtorPercent) / 100).toFixed(2))
  }, [values.commissionAmountWithVat, realtorCommissionPercent])

  // Calculate recipient commission amounts when total amount or percentages change
  useEffect(() => {
    if (realtorBaseAmount > 0 && value.length > 0) {
      const hasChanges = value.some((recipient) => {
        const currentAmount = recipient.commissionAmount ?? 0
        const expectedAmount = recipient.commissionPercent
          ? Number(
              (
                (realtorBaseAmount * Number(recipient.commissionPercent)) /
                100
              ).toFixed(2)
            )
          : 0
        return currentAmount !== expectedAmount
      })

      if (hasChanges) {
        const updatedRecipients = value.map((recipient) => {
          if (recipient.commissionPercent) {
            const commissionAmount = Number(
              (
                (realtorBaseAmount * Number(recipient.commissionPercent)) /
                100
              ).toFixed(2)
            )
            return {
              ...recipient,
              commissionAmount,
            }
          }
          return recipient
        })
        // only update if the array actually changed to avoid render loops
        if (JSON.stringify(updatedRecipients) !== JSON.stringify(value)) {
          helpers.setValue(updatedRecipients, false)
        }
      }
    }
  }, [realtorBaseAmount, value, helpers])

  const handleCommissionPercentChange = (
    index: number,
    newPercent: string,
    arrayHelpers: FieldArrayRenderProps
  ) => {
    const currentRecipients = [...value]

    const commissionAmount =
      realtorBaseAmount > 0
        ? Number(((realtorBaseAmount * Number(newPercent)) / 100).toFixed(2))
        : 0

    arrayHelpers.replace(index, {
      ...currentRecipients[index],
      commissionPercent: newPercent,
      commissionAmount,
    })
  }

  return (
    <FieldArray
      name="recipients"
      render={(arrayHelpers) => (
        <Stack spacing={4}>
          {value.map((recipient, index) => {
            const comissionError = parseCommissionPercentErrors(
              errors.recipients?.[index]
            )
            return (
              <Stack
                key={`${recipient.userId}-${index}`}
                spacing={3}
                border="1px solid #ccc"
                padding="1rem"
                borderRadius="md"
              >
                <Stack direction="row" justifyContent="space-between">
                  <Text>
                    {t('fiDetailsOfSale.form.fields.recipient')} {index + 1}
                  </Text>
                  <Button
                    variant={['transparent']}
                    color="red"
                    onClick={() => arrayHelpers.remove(index)}
                    textTransform="capitalize"
                  >
                    {t('delete')}
                  </Button>
                </Stack>
                <Stack direction={isMobile ? 'column' : 'row'}>
                  <LabeledField
                    label={t('fiDetailsOfSale.form.fields.role')}
                    required={true}
                  >
                    <SimpleDropdown
                      data={roles}
                      name={`recipients[${index}].role`}
                      value={value[index].role}
                      onSelect={(value) => {
                        arrayHelpers.replace(index, {
                          ...recipient,
                          role: value,
                        })
                      }}
                      searchable={false}
                    />
                  </LabeledField>
                  <InputWithLabel
                    name={`recipients[${index}].commissionPercent`}
                    value={value[index].commissionPercent}
                    error={comissionError}
                    touched={touched.recipients?.[index]?.commissionPercent}
                    label={t('fiDetailsOfSale.form.fields.commissionPercent')}
                    type="number"
                    rightElement="%"
                    required={true}
                    onChange={(e) =>
                      handleCommissionPercentChange(
                        index,
                        e.target.value,
                        arrayHelpers
                      )
                    }
                  />
                  <InputWithLabel
                    name={`recipients[${index}].commissionVatPercent`}
                    value={value[index].commissionVatPercent}
                    touched={touched.recipients?.[index]?.commissionVatPercent}
                    label={t('vatPercent')}
                    type="number"
                    rightElement="%"
                    required={true}
                  />
                  <InputWithLabel
                    name={`recipients[${index}].commissionAmount`}
                    value={value[index].commissionAmount}
                    label={t('fiDetailsOfSale.form.fields.sum')}
                    type="currency"
                    rightElement="EUR"
                    required={true}
                    readOnly={true}
                  />
                </Stack>

                <FiCustomerSelect
                  name={`recipients[${index}].userId`}
                  label={t('fiDetailsOfSale.addRecipient')}
                  querySource="users"
                  singleSelect={true}
                />
              </Stack>
            )
          })}
          <Button
            variant="transparent"
            width="fit-content"
            onClick={() => {
              arrayHelpers.push({
                role: '',
                commissionPercent: '',
                commissionAmount: 0,
                userId: [],
                type: 'user',
              })
            }}
          >
            + {t('fiDetailsOfSale.addRecipient')}
          </Button>
        </Stack>
      )}
    />
  )
}
