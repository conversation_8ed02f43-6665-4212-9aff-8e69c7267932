import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@chakra-ui/react'
import {
  FieldArray,
  useField,
  useFormikContext,
  FieldArrayRenderProps,
} from 'formik'
import { InputWithLabel } from '@/components/Form/Input'
import { useTranslation } from 'next-i18next'

import { FIDetailsOfSaleCreate } from '@/modules/fi-properties/types/FIDetailsOfSale'
import {
  SchemaFiLeadDetailsOfSaleSchema,
  FIDetailsOfSaleLeadBasis,
} from '@/generated-types/api'
import FiCustomerSelect from '@/components/FICustomerSelect'
import { useEffect } from 'react'
import { useMobile } from '@/hooks/useMobile'
import { EnumBadgeGroupField } from '@/components/Badge/BadgeGroupFieldWrappers'
import { StaticInputField } from '@/components/Form/StaticInputField'

const parseCommissionPercentErrors = (error: unknown): string | undefined => {
  if (typeof error === 'string') return error
  if (error && typeof error === 'object' && 'commissionPercent' in error) {
    return (error as { commissionPercent: string }).commissionPercent
  }
  return undefined
}

export const Leads = ({
  totalComissionAndExpenses,
}: {
  totalComissionAndExpenses: number
}) => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const [field, , helpers] =
    useField<SchemaFiLeadDetailsOfSaleSchema[]>('leads')
  const { value = [] } = field
  const { values, errors, touched } = useFormikContext<FIDetailsOfSaleCreate>()

  // Calculate recipient commission amounts when total amount or percentages change
  useEffect(() => {
    const totalAmount = values.commissionAmountWithVat ?? 0
    if (totalAmount > 0 && value.length > 0) {
      const hasChanges = value.some((recipient) => {
        const currentAmount = recipient.commissionAmount ?? 0
        const expectedAmount = recipient.commissionPercent
          ? Number(
              (
                (totalAmount * Number(recipient.commissionPercent)) /
                100
              ).toFixed(2)
            )
          : 0
        return currentAmount !== expectedAmount
      })

      if (hasChanges) {
        const updatedRecipients = value.map((recipient) => {
          if (recipient.commissionPercent) {
            const commissionAmount = Number(
              (
                (totalAmount * Number(recipient.commissionPercent)) /
                100
              ).toFixed(2)
            )
            return {
              ...recipient,
              commissionAmount,
            }
          }
          return recipient
        })
        if (JSON.stringify(updatedRecipients) !== JSON.stringify(value)) {
          helpers.setValue(updatedRecipients, false)
        }
      }
    }
  }, [values.commissionAmountWithVat, value, helpers])

  const handleCommissionPercentChange = (
    index: number,
    newPercent: string,
    arrayHelpers: FieldArrayRenderProps
  ) => {
    const totalAmount = values.commissionAmountWithVat ?? 0
    const currentRecipients = [...value]

    const commissionAmount =
      totalAmount > 0
        ? Number(((totalAmount * Number(newPercent)) / 100).toFixed(2))
        : 0

    arrayHelpers.replace(index, {
      ...currentRecipients[index],
      commissionPercent: newPercent,
      commissionAmount,
    })
  }

  return (
    <FieldArray
      name="leads"
      render={(arrayHelpers) => (
        <Stack spacing={4}>
          {value.map((recipient, index) => {
            const comissionError = parseCommissionPercentErrors(
              errors.leads?.[index]
            )
            return (
              <Stack
                key={`${recipient.userId}-${index}`}
                spacing={3}
                border="1px solid #ccc"
                padding="1rem"
                borderRadius="md"
              >
                <Stack direction="row" justifyContent="space-between">
                  <Text>
                    {t('fiDetailsOfSale.form.fields.leadCommission')}{' '}
                    {index + 1}
                  </Text>
                  <Button
                    variant={['transparent']}
                    color="red"
                    onClick={() => arrayHelpers.remove(index)}
                    textTransform="capitalize"
                  >
                    {t('delete')}
                  </Button>
                </Stack>
                <EnumBadgeGroupField
                  toggle
                  name={`leads[${index}].leadBasis`}
                  label={t(`fiDetailsOfSale.form.fields.leadBasis`)}
                  enum={FIDetailsOfSaleLeadBasis}
                  itemLabelPrefix="fiDetailsOfSale.form.enums.leadBasis"
                />
                <Stack direction={isMobile ? 'column' : 'row'}>
                  <StaticInputField
                    value={totalComissionAndExpenses}
                    label={t(
                      'fiDetailsOfSale.form.fields.totalCommissionAndExpenses'
                    )}
                    type="currency"
                    rightElement="EUR"
                  />
                  <InputWithLabel
                    name={`leads[${index}].commissionPercent`}
                    value={value[index].commissionPercent}
                    error={comissionError}
                    touched={touched.leads?.[index]?.commissionPercent}
                    label={t('fiDetailsOfSale.form.fields.commissionPercent')}
                    type="number"
                    rightElement="%"
                    required={true}
                    onChange={(e) =>
                      handleCommissionPercentChange(
                        index,
                        e.target.value,
                        arrayHelpers
                      )
                    }
                  />
                  <InputWithLabel
                    name={`leads[${index}].commissionVatPercent`}
                    value={value[index].commissionVatPercent}
                    error={comissionError}
                    touched={touched.leads?.[index]?.commissionVatPercent}
                    label={t('vatPercent')}
                    type="number"
                    rightElement="%"
                    required={true}
                  />
                  <InputWithLabel
                    name={`leads[${index}].commissionAmount`}
                    value={value[index].commissionAmount}
                    label={t('fiDetailsOfSale.form.fields.sum')}
                    type="currency"
                    rightElement="EUR"
                    required={true}
                    readOnly={true}
                  />
                </Stack>

                <FiCustomerSelect
                  name={`leads[${index}].userId`}
                  label={t('fiDetailsOfSale.addLead')}
                  querySource="users"
                  singleSelect={true}
                />
              </Stack>
            )
          })}
          <Button
            variant="transparent"
            width="fit-content"
            onClick={() => {
              arrayHelpers.push({
                leadBasis: null,
                commissionPercent: '',
                commissionAmount: 0,
                userId: [],
                type: 'user',
              })
            }}
          >
            + {t('fiDetailsOfSale.addLead')}
          </Button>
        </Stack>
      )}
    />
  )
}
