import { But<PERSON>, <PERSON>ack, Text, Grid, GridItem, Divider } from '@chakra-ui/react'
import { FieldArray, useField } from 'formik'
import { InputWithLabel } from '@/components/Form/Input'
import { useTranslation } from 'next-i18next'

import { SchemaFiDetailsOfSaleExpense } from '@/generated-types/api'
import { useMobile } from '@/hooks/useMobile'
import { formatCurrency } from '@/utils/formatNumber'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import { Fragment, useMemo } from 'react'

export const Expenses = () => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])

  const [field] = useField<SchemaFiDetailsOfSaleExpense[] | undefined>(
    'expenses'
  )
  const value = field.value ?? []

  return (
    <FieldArray
      name="expenses"
      render={(arrayHelpers) => (
        <Stack spacing={4}>
          {value.map((expense, index) => (
            <Stack key={`${expense.name}-${expense.amount}-${index}`}>
              <Stack
                direction={isMobile ? 'column' : 'row'}
                alignItems="center"
                gap={2}
              >
                <Text pt={18}>{index + 1}.</Text>

                <InputWithLabel
                  name={`expenses[${index}].name`}
                  defaultValue={expense.name}
                  label={t('reason')}
                  type="text"
                  required={true}
                  onlyUpdateOnBlur
                />
                <InputWithLabel
                  name={`expenses[${index}].amount`}
                  defaultValue={expense.amount}
                  label={t('amount')}
                  type="currency"
                  rightElement="EUR"
                  required={true}
                  onlyUpdateOnBlur
                />
                <InputWithLabel
                  name={`expenses[${index}].taxPercentage`}
                  defaultValue={expense.taxPercentage}
                  label={t('fiDetailsOfSale.form.fields.commissionVatPercent')}
                  type="number"
                  rightElement="%"
                  required={true}
                  onlyUpdateOnBlur
                />
                <Button
                  variant={['transparent']}
                  color="red"
                  onClick={() => arrayHelpers.remove(index)}
                  textTransform="capitalize"
                  minW="fit-content"
                  mt={18}
                >
                  {t('delete')}
                </Button>
              </Stack>
            </Stack>
          ))}
          <Button
            variant="transparent"
            width="fit-content"
            onClick={() => {
              arrayHelpers.push({
                name: '',
                amount: null,
                taxPercentage: null,
              })
            }}
          >
            + {t('fiDetailsOfSale.addExpense')}
          </Button>
          <Divider m={0} />
          <ExpensesSummary expenses={value} />
        </Stack>
      )}
    />
  )
}

const ExpensesSummary = ({
  expenses,
}: {
  expenses: SchemaFiDetailsOfSaleExpense[] | undefined
}) => {
  const { t } = useTranslation(['common'])
  const { currency, locale } = useUserAndOrganization()

  const groups = useMemo(() => formatSummary(expenses), [expenses])
  if (!groups) return null
  return (
    <Stack>
      {Object.keys(groups.byVat).length > 0 && (
        <Grid templateColumns="1fr 1fr 1fr 1fr" gap={2} pt={4}>
          <GridItem fontWeight="bold">{t('fiDetailsOfSale.vat')}</GridItem>
          <GridItem fontWeight="bold">{t('fiDetailsOfSale.net')}</GridItem>
          <GridItem fontWeight="bold">{t('fiDetailsOfSale.tax')}</GridItem>
          <GridItem fontWeight="bold">{t('fiDetailsOfSale.total')}</GridItem>

          {Object.values(groups.byVat)
            .sort((a, b) => a.pct - b.pct)
            .map((row, index) => (
              <Fragment key={`${row.pct}-${index}`}>
                <GridItem>{row.pct}%</GridItem>
                <GridItem>
                  {formatCurrency({ value: row.net, currency, locale })}
                </GridItem>
                <GridItem>
                  {formatCurrency({ value: row.vat, currency, locale })}
                </GridItem>
                <GridItem>
                  {formatCurrency({ value: row.gross, currency, locale })}
                </GridItem>
              </Fragment>
            ))}

          <GridItem colSpan={1} fontWeight="bold" pt={2}>
            {t('fiDetailsOfSale.otherExpensesTotal')}
          </GridItem>
          <GridItem fontWeight="bold" pt={2}>
            {formatCurrency({ value: groups.total.net, currency, locale })}
          </GridItem>
          <GridItem fontWeight="bold" pt={2}>
            {formatCurrency({ value: groups.total.vat, currency, locale })}
          </GridItem>
          <GridItem fontWeight="bold" pt={2}>
            {formatCurrency({ value: groups.total.gross, currency, locale })}
          </GridItem>
        </Grid>
      )}
    </Stack>
  )
}

const formatSummary = (values: SchemaFiDetailsOfSaleExpense[] | undefined) => {
  return values?.reduce(
    (acc, exp) => {
      const gross = Number(exp.amount) || 0
      const pct = Number(exp.taxPercentage) || 0
      if (gross <= 0) return acc
      const net = Number((gross / (1 + pct / 100)).toFixed(2))
      const vat = Number((gross - net).toFixed(2))
      const key = pct.toString()
      if (!acc.byVat[key]) {
        acc.byVat[key] = { pct, net: 0, vat: 0, gross: 0 }
      }
      acc.byVat[key].net += net
      acc.byVat[key].vat += vat
      acc.byVat[key].gross += gross
      acc.total.net += net
      acc.total.vat += vat
      acc.total.gross += gross
      return acc
    },
    {
      byVat: {} as Record<
        string,
        { pct: number; net: number; vat: number; gross: number }
      >,
      total: { net: 0, vat: 0, gross: 0 },
    }
  )
}
