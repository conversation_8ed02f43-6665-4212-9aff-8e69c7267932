import {
  FIDetailsOfSaleStatus,
  SchemaFiDetailsOfSaleCreate,
  SchemaFiRecipientDetailsOfSaleSchema,
} from '@/generated-types/api'
import {
  FIDetailsOfSaleCreate,
  FIDetailsOfSaleLeadFormModel,
} from '@/modules/fi-properties/types/FIDetailsOfSale'

export const mapStatusToStep = (status?: FIDetailsOfSaleStatus | null) => {
  switch (status) {
    case FIDetailsOfSaleStatus.draft:
      return 0
    case FIDetailsOfSaleStatus.validated:
      return 1
    case FIDetailsOfSaleStatus.locked:
      return 1
    default:
      return 0
  }
}

export const mapStatusToAllowedSteps = (
  status?: FIDetailsOfSaleStatus | null
) => {
  switch (status) {
    case FIDetailsOfSaleStatus.draft:
      return [0]
    case FIDetailsOfSaleStatus.validated:
      return [0, 1]
    case FIDetailsOfSaleStatus.locked:
      return [1]
    default:
      return [0]
  }
}

export const formatDetailsOfSaleFormValuesToApi = (
  values: FIDetailsOfSaleCreate
): Pick<
  SchemaFiDetailsOfSaleCreate,
  'sellers' | 'buyers' | 'recipients' | 'leads'
> => {
  const buyers =
    values.buyers?.map(({ id, ownershipSharePercent }) => ({
      buyerId: id,
      ownershipSharePercent,
    })) || []

  const sellers =
    values.sellers?.map(({ id, ownershipSharePercent }) => ({
      sellerId: id,
      ownershipSharePercent,
    })) || []

  const recipients =
    values.recipients?.map(
      ({
        userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        role,
        type,
      }) => ({
        userId: Array.isArray(userId) ? userId[0] : userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        role,
        type,
        user: null,
      })
    ) || []

  const leads =
    values.leads?.map(
      ({
        userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        leadBasis,
      }) => ({
        userId: Array.isArray(userId) ? userId[0] : userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        leadBasis,
        user: null,
      })
    ) || []

  return { buyers, sellers, recipients, leads }
}

export const formatDetailsOfSaleApiToFormValues = (
  apiData: Pick<
    SchemaFiDetailsOfSaleCreate,
    'sellers' | 'buyers' | 'recipients' | 'leads'
  >
): Pick<
  FIDetailsOfSaleCreate,
  'buyers' | 'sellers' | 'recipients' | 'leads'
> => {
  const buyers =
    apiData.buyers?.map(({ buyerId, ownershipSharePercent }) => ({
      id: buyerId,
      ownershipSharePercent,
    })) || []

  const sellers =
    apiData.sellers?.map(({ sellerId, ownershipSharePercent }) => ({
      id: sellerId,
      ownershipSharePercent,
    })) || []

  const recipients =
    apiData.recipients?.map(
      ({
        userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        role,
        type,
        user,
      }) => ({
        userId: userId !== null ? [userId] : [],
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        role,
        type,
        user,
      })
    ) || []

  const leads =
    apiData.leads?.map(
      ({
        userId,
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        leadBasis,
        user,
      }) => ({
        userId: userId !== null ? [userId] : [],
        commissionPercent,
        commissionAmount,
        commissionVatPercent,
        leadBasis,
        user,
      })
    ) || []

  return {
    buyers,
    sellers,
    recipients,
    leads,
  }
}

export const calculateDurationDays = (
  startDate: string,
  endDate: string
): number => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export const calculateRecipientsTotalPercentage = (
  recipients: SchemaFiRecipientDetailsOfSaleSchema[]
) => {
  return recipients.reduce((total, recipient) => {
    const percent = Number(recipient.commissionPercent) || 0
    return total + percent
  }, 0)
}

// Splits ownership equally across ids with two-decimal precision.
// Any rounding remainder is assigned to the FIRST owner so the total sums to 100%.
export const mapOwnersWithEqualShare = (
  ids: number[] | null | undefined
): Array<{ id: number; ownershipSharePercent: number }> => {
  if (!ids || ids.length === 0) return []

  const count = ids.length
  const baseShareCents = Math.floor(10000 / count)
  const baseShare = baseShareCents / 100
  const remainder = (10000 - baseShareCents * count) / 100

  return ids.map((id, index) => ({
    id,
    ownershipSharePercent:
      index === 0 ? Number((baseShare + remainder).toFixed(2)) : baseShare,
  }))
}

export const calculateRealtorCommissionPercent = (
  strandCommissionPercent: number,
  leads: FIDetailsOfSaleLeadFormModel[] | undefined
) => {
  const leadsCommissionPercent =
    leads?.reduce((acc, lead) => acc + (lead.commissionPercent ?? 0), 0) ?? 0

  return 100 - strandCommissionPercent - leadsCommissionPercent
}

export const calculateSumNetAndTax = (
  amountWithVat: number,
  vatPercent: number
): { amountWithVat: number; net: number; tax: number } => {
  const net = amountWithVat / (1 + vatPercent / 100)
  const tax = amountWithVat - net
  return { amountWithVat, net, tax }
}
