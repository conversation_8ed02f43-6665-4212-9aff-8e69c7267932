import { useTranslation } from 'next-i18next'
import { FICreateDetailsOfSaleFormWrapper } from './FIDetailsOfSaleCreate'
import { FIDetailsOfSaleEdit } from './FIDetailsOfSaleEdit'
import { FIDetailsOfSalePreview } from './FIDetailsOfSalePreview'
import { MultiStepFormContainer } from '@/components/MultiStepForm/MultiStepFormContainer'
import { Step } from '@/components/MultiStepForm/Step'
import { useFiDetailsOfSale } from '@/modules/fi-properties/queries/queryFIDetailsOfSale'
import { mapStatusToAllowedSteps, mapStatusToStep } from './common/utils'

export const FIDetailsOfSale = ({
  detailsOfSaleId,
  create,
}: {
  detailsOfSaleId: string | null
  create: string | null
}) => {
  const { t } = useTranslation(['common'])
  const { data } = useFiDetailsOfSale(detailsOfSaleId ?? '')

  const steps = [
    { title: t('fiDetailsOfSale.steps.fill') },
    { title: t('fiDetailsOfSale.steps.check') },
  ]

  if (detailsOfSaleId && !data) {
    return null
  }

  return (
    <MultiStepFormContainer
      steps={steps}
      allowedSteps={mapStatusToAllowedSteps(data?.status)}
      initialStep={mapStatusToStep(data?.status)}
    >
      <Step stepIndex={0}>
        {create ? (
          <FICreateDetailsOfSaleFormWrapper />
        ) : (
          <FIDetailsOfSaleEdit detailsOfSaleId={String(detailsOfSaleId)} />
        )}
      </Step>
      <Step stepIndex={1}>
        {detailsOfSaleId && (
          <FIDetailsOfSalePreview detailsOfSaleId={detailsOfSaleId} />
        )}
      </Step>
    </MultiStepFormContainer>
  )
}

export default FIDetailsOfSale
