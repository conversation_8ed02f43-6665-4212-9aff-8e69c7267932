import { useInPropertyDetailPage } from '@/hooks/useInPropertyPage'
import { useToast } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useRef } from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'
import { PurchaseOfferFormBody } from './PurchaseOfferFormBody'
import { FIPurchaseOfferSchema } from './common/PurchaseOfferSchema'

import {
  FIPurchaseOfferCreate,
  emptyPurchaseOffer,
} from '@/modules/fi-properties/types/FIPurchaseOffer'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { useCreateFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

export const CreatePurchaseOfferForm = () => {
  const { goToNextStep } = useMultiStepForm()
  const { t } = useTranslation(['common'])
  const validationSchema = FIPurchaseOfferSchema(t)
  const formRef = useRef<FormikProps<FIPurchaseOfferCreate>>(null)
  const toast = useToast()
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FIPurchaseOfferCreate>()
  const { property, referenceCode } = useInPropertyDetailPage()
  const router = useRouter()
  const isMobile = useMobile()

  const { isPending, mutate: createPurchaseOffer } = useCreateFIPurchaseOffer({
    onSuccess: async (res) => {
      goToNextStep()
      toast(
        getToastProps({
          isMobile,
          title: t('fiPurchaseOffer.created'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
      router.push(
        {
          pathname: router.pathname,
          query: {
            referenceCode: referenceCode,
            view: 'purchaseOffer',
            purchaseOfferId: res.id,
            callbackUrl: router.asPath,
          },
        },
        undefined,
        { shallow: true }
      )
    },
    onError: () => {
      toast(
        getToastProps({
          isMobile,
          title: t('fiPurchaseOffer.failedToCreate'),
          status: 'error',
        })
      )
    },
  })

  const handleOnSave = async (validate?: boolean) => {
    if (
      !formRef.current ||
      (validate &&
        !(await validateAndSetErrors(
          formRef.current,
          t('fiPurchaseOffer.form.validation.errorToast')
        )))
    ) {
      return
    }

    await createPurchaseOffer({
      ...formRef.current.values,
      validate: validate,
    })
  }

  if (!property) {
    return null
  }
  const initialData: FIPurchaseOfferCreate = {
    ...emptyPurchaseOffer,
    propertyId: property.id,
    sellerIds: (property.contacts || []).map(({ id }) => id),
  }

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('saveAsDraft'),
            onClick: () => handleOnSave(),
            isLoading: isPending,
            buttonVariant: 'transparent',
          },
          {
            label: t('next'),
            onClick: () => handleOnSave(true),
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={initialData}
        validationSchema={validationSchema}
        validateOnBlur
        validateOnMount
        onSubmit={() => handleOnSave(true)}
      >
        {(formikProps) => <PurchaseOfferFormBody {...formikProps} />}
      </Formik>
    </StepContentWithFooter>
  )
}
