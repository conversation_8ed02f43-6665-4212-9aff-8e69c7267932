import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import {
  sendPurchaseOfferForSigning,
  useFIPurchaseOffer,
  sendReminderForSigners,
  useInvalidateFiPurchaseOfferQuery,
  useCreateFIPurchaseOffer,
  useSetFIPurchaseOfferStatus,
} from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { Button, Flex, Spinner, Text, useToast } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import {
  mapContactToParticipant,
  mapSignerToParticipant,
  ParticipantCard,
} from '@/components/Card'
import { PurchaseOfferCard } from './common/PurchaseOfferCard'
import { FiCopy, FiMail, FiPlus } from 'react-icons/fi'
import { PiCheckCircle, PiSignature, PiXCircle } from 'react-icons/pi'
import { useRouter } from 'next/router'
import { useMobile } from '@/hooks/useMobile'
import {
  SigningFormProps,
  CreateFISignatureModal,
} from '@/modules/fi-properties/components/FISignaturesModal/CreateFISignatureModal'
import { useMemo, useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import {
  FIPurchaseOfferStatusEnum,
  SchemaDocumentSigner,
} from '@/generated-types/api'
import { getToastProps } from '@/utils/toastProps'
import { mapContactIdsToSigners } from '@/utils/signingUtils'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

export const FIPurchaseOfferSignatures = ({
  purchaseOfferId,
}: {
  purchaseOfferId: string
}) => {
  const invalidateFiPurchaseOfferQuery =
    useInvalidateFiPurchaseOfferQuery(purchaseOfferId)
  const router = useRouter()
  const isMobile = useMobile()
  const toast = useToast()
  const { t } = useTranslation(['common'])
  const { data } = useFIPurchaseOffer(purchaseOfferId)
  const { mutate: createPurchaseOffer } = useCreateFIPurchaseOffer({
    onSuccess: (result) => {
      toast(
        getToastProps({
          isMobile,
          title: t('fiPurchaseOffer.copied'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
      router.push({
        pathname: router.pathname,
        query: {
          ...router.query,
          purchaseOfferId: result.id,
        },
      })
    },
    onError: () => {
      toast(
        getToastProps({
          isMobile,
          title: t('error'),
          description: t('fiPurchaseOffer.failedToCopy'),
          status: 'error',
        })
      )
    },
  })
  const [isCreateSignatureModalOpen, setIsCreateSignatureModalOpen] =
    useState(false)

  const { isPending: isSendingForSigning, mutate: sendForSigning } =
    useMutation({
      mutationFn: sendPurchaseOfferForSigning,
      onSuccess: async (_result) => {
        invalidateFiPurchaseOfferQuery()
        setIsCreateSignatureModalOpen(false)

        toast(
          getToastProps({
            isMobile,
            title: t('fiPurchaseOffer.sentForSigning'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
    })

  const { mutate: sendReminder, isPending: isSendingReminder } = useMutation({
    mutationFn: sendReminderForSigners,
    onSuccess: () => {
      toast(
        getToastProps({
          isMobile,
          title: t('fiSalesAgreement.reminderSent'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
    },
    onError: (_error) => {
      toast(
        getToastProps({
          title: t('error'),
          description: t('fiSalesAgreement.failedSendingReminder'),
          status: 'error',
        })
      )
    },
  })

  const { mutate: setFIPurchaseOfferStatus, isPending: isSettingStatus } =
    useSetFIPurchaseOfferStatus({
      onSuccess: (_) => {
        toast(
          getToastProps({
            isMobile,
            title: t('fiPurchaseOffer.statusUpdated'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
      onError: () => {
        toast(
          getToastProps({
            title: t('error'),
            description: t('fiPurchaseOffer.failedToUpdateStatus'),
            status: 'error',
          })
        )
      },
    })

  const actions = useMemo(
    () => ({
      sendForSigning:
        data?.status === FIPurchaseOfferStatusEnum.validated ||
        data?.status === FIPurchaseOfferStatusEnum.offeror_signed,
      sendReminder:
        data?.status === FIPurchaseOfferStatusEnum.pending_offeror_signatures ||
        data?.status === FIPurchaseOfferStatusEnum.pending_offeree_signatures,
      createCounterOffer:
        data?.status !== FIPurchaseOfferStatusEnum.accepted &&
        data?.status !== FIPurchaseOfferStatusEnum.expired,
      copyPurchaseOffer: data?.status !== FIPurchaseOfferStatusEnum.accepted,
      rejectPurchaseOffer:
        data?.status !== FIPurchaseOfferStatusEnum.accepted &&
        data?.status !== FIPurchaseOfferStatusEnum.rejected &&
        data?.status !== FIPurchaseOfferStatusEnum.expired,
      acceptOffer: data?.status === FIPurchaseOfferStatusEnum.offeree_signed,
    }),
    [data?.status]
  )

  const sendForSigningLabel = useMemo(() => {
    switch (data?.status) {
      case FIPurchaseOfferStatusEnum.validated:
        return t('fiPurchaseOffer.sendForBuyerSigning')
      case FIPurchaseOfferStatusEnum.offeror_signed:
        return t('fiPurchaseOffer.sendForSellerSigning')
      case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
      case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
        return t('fiPurchaseOffer.sentForSigning')
      case FIPurchaseOfferStatusEnum.offeree_signed:
        return t('fiPurchaseOffer.signed')
    }
  }, [data?.status, t])

  if (!data) {
    return null
  }

  const initialSigningFormValues: Partial<SigningFormProps> = {
    lastSigningDate:
      data.status === FIPurchaseOfferStatusEnum.offeror_signed
        ? data.validUntil ?? undefined
        : new Date().toISOString().split('T')[0],
    contactIds:
      data.status === FIPurchaseOfferStatusEnum.validated
        ? data.buyers?.map((buyer) => buyer.id)
        : data.status === FIPurchaseOfferStatusEnum.offeror_signed
        ? data.sellers?.map((seller) => seller.id)
        : [],
  }

  const handleCreateCounterOffer = () => {
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          createCounterOffer: true,
        },
      },
      undefined,
      { shallow: true }
    )
  }

  const handleCreateSigning = (values) => {
    sendForSigning({
      ...values,
      signers: mapContactIdsToSigners(values.contactIds),
      purchaseOfferId,
    })
  }

  const handleCopyPurchaseOffer = () => {
    createPurchaseOffer({
      ...data,
      buyerIds: (data.buyers || []).map(({ id }) => id),
      sellerIds: (data.sellers || []).map(({ id }) => id),
      validUntil: null,
      status: FIPurchaseOfferStatusEnum.draft,
    })
  }

  const handleRejectPurchaseOffer = () => {
    setFIPurchaseOfferStatus({
      purchaseOfferId,
      status: FIPurchaseOfferStatusEnum.rejected,
    })
  }

  const handleAcceptOffer = () => {
    setFIPurchaseOfferStatus({
      purchaseOfferId,
      status: FIPurchaseOfferStatusEnum.accepted,
    })
  }

  const buyerIds = new Set(data.buyers?.map(({ id }) => id))
  const sellerIds = new Set(data.sellers?.map(({ id }) => id))

  const signersByParty =
    data.signings?.reduce<{
      [participantId: string]: {
        signers: SchemaDocumentSigner[]
      }
    }>((acc, signing) => {
      signing.signers?.forEach((signer) => {
        const { userId, userType } = signer

        if (
          userType === 'contact' &&
          (buyerIds.has(userId) || sellerIds.has(userId))
        ) {
          if (!acc[userId]) {
            acc[userId] = {
              signers: [],
            }
          }
          acc[userId].signers.push(signer)
        }
      })
      return acc
    }, {}) || {}

  return (
    <StepContentWithFooter maxW={isMobile ? '100%' : '66%'}>
      <Flex gap={2}>
        {actions.sendForSigning && (
          <Button
            variant="transparent"
            maxW="fit-content"
            padding="4px 8px"
            onClick={() => {
              setIsCreateSignatureModalOpen(true)
            }}
          >
            <PiSignature />
            <Text fontSize="sm" ml={2}>
              {sendForSigningLabel}
            </Text>
          </Button>
        )}
        {actions.sendReminder && (
          <Button
            variant="transparent"
            maxW="fit-content"
            padding="4px 8px"
            onClick={() => sendReminder(purchaseOfferId)}
          >
            {isSendingReminder ? <Spinner size="sm" /> : <FiMail />}
            <Text fontSize="sm" ml={2}>
              {t('fiPurchaseOffer.sendReminder')}
            </Text>
          </Button>
        )}
        {actions.createCounterOffer && (
          <Button
            variant="transparent"
            marginBottom="1rem"
            maxW="fit-content"
            padding="4px 8px"
            onClick={handleCreateCounterOffer}
          >
            <FiPlus />
            <Text fontSize="sm" ml={2}>
              {t('createCounterOffer')}
            </Text>
          </Button>
        )}
        <Flex ml="auto" gap={2}>
          {actions.copyPurchaseOffer && (
            <Button
              variant="transparent"
              maxW="fit-content"
              padding="4px 8px"
              onClick={handleCopyPurchaseOffer}
            >
              <FiCopy />
              <Text fontSize="sm" ml={2}>
                {t('Copy')}
              </Text>
            </Button>
          )}
          {actions.rejectPurchaseOffer && (
            <Button
              variant="transparent"
              maxW="fit-content"
              padding="4px 8px"
              isLoading={isSettingStatus}
              onClick={handleRejectPurchaseOffer}
            >
              <PiXCircle />
              <Text fontSize="sm" ml={2}>
                {t('fiPurchaseOffer.reject')}
              </Text>
            </Button>
          )}
          {actions.acceptOffer && (
            <Button
              maxW="fit-content"
              padding="4px 8px"
              isLoading={isSettingStatus}
              onClick={handleAcceptOffer}
            >
              <PiCheckCircle />
              <Text fontSize="sm" ml={2}>
                {t('fiPurchaseOffer.accept')}
              </Text>
            </Button>
          )}
        </Flex>
      </Flex>
      <PurchaseOfferCard purchaseOffer={data} />
      <HeaderRow title={t('fiPurchaseOffer.form.headers.buyers')} />
      {data.buyers?.map((buyer) => {
        const signer = signersByParty[buyer.id]?.signers.pop()

        const participant = signer
          ? mapSignerToParticipant(signer)
          : mapContactToParticipant(buyer)

        return (
          <ParticipantCard
            key={buyer.id}
            participant={participant}
            variant="signing"
            signingStatus={signer?.status}
          />
        )
      })}
      <HeaderRow title={t('fiPurchaseOffer.form.headers.sellers')} />
      {data.sellers?.map((seller) => {
        const signer = signersByParty[seller.id]?.signers.pop()

        const participant = signer
          ? mapSignerToParticipant(signer)
          : mapContactToParticipant(seller)

        return (
          <ParticipantCard
            key={seller.id}
            participant={participant}
            variant="signing"
            signingStatus={signer?.status}
          />
        )
      })}
      {isCreateSignatureModalOpen && (
        <CreateFISignatureModal
          onSubmitMutation={handleCreateSigning}
          isSaving={isSendingForSigning}
          onClose={() => setIsCreateSignatureModalOpen(false)}
          signers={{
            realtors: false,
            contacts: true,
          }}
          dateMode={
            data.status === FIPurchaseOfferStatusEnum.offeror_signed
              ? 'datetime'
              : 'date'
          }
          initialValues={initialSigningFormValues}
        />
      )}
    </StepContentWithFooter>
  )
}

export default FIPurchaseOfferSignatures
