import { CreatePurchaseOfferForm } from './PurchaseOfferCreate'
import { EditPurchaseOfferForm } from './PurchaseOfferEdit'
import { useTranslation } from 'next-i18next'
import FIPurchaseOfferPreview from './FIPurchaseOfferPreview'
import FIPurchaseOfferSignatures from './FIPurchaseOfferSignatures'
import {
  FILL_FORM_STEP,
  VALIDATE_STEP,
  SIGNATURES_STEP,
  mapStatusToStep,
  mapStatusToAllowedSteps,
} from './common/utils'
import { MultiStepFormContainer } from '@/components/MultiStepForm/MultiStepFormContainer'
import { Step } from '@/components/MultiStepForm/Step'
import { useFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'

export const FIPurchaseOffer = ({
  purchaseOfferId,
  create,
}: {
  purchaseOfferId: string | null
  create: string | null
}) => {
  const { t } = useTranslation(['common'])
  const { data } = useFIPurchaseOffer(purchaseOfferId ?? '')

  const steps = [
    { title: t('fiPurchaseOffer.steps.fill') },
    { title: t('fiPurchaseOffer.steps.validate') },
    { title: t('fiPurchaseOffer.steps.sign') },
  ]

  if (purchaseOfferId && !data) {
    return null
  }

  return (
    <MultiStepFormContainer
      steps={steps}
      initialStep={mapStatusToStep(data?.status)}
      allowedSteps={mapStatusToAllowedSteps(data?.status)}
    >
      <Step stepIndex={FILL_FORM_STEP}>
        {create && <CreatePurchaseOfferForm />}

        {!create && (
          <EditPurchaseOfferForm purchaseOfferId={String(purchaseOfferId)} />
        )}
      </Step>
      <Step stepIndex={VALIDATE_STEP}>
        {!!purchaseOfferId && (
          <FIPurchaseOfferPreview purchaseOfferId={purchaseOfferId} />
        )}
      </Step>
      <Step stepIndex={SIGNATURES_STEP}>
        {!!purchaseOfferId && (
          <FIPurchaseOfferSignatures purchaseOfferId={purchaseOfferId} />
        )}
      </Step>
    </MultiStepFormContainer>
  )
}

export default FIPurchaseOffer
