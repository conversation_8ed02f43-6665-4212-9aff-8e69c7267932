import { useToast } from '@chakra-ui/react'
import { Formik, FormikProps } from 'formik'
import { useRef } from 'react'
import { useTranslation } from 'next-i18next'
import { PurchaseOfferFormBody } from './PurchaseOfferFormBody'
import { FIPurchaseOfferSchema } from './common/PurchaseOfferSchema'

import { FIPurchaseOfferCreate } from '@/modules/fi-properties/types/FIPurchaseOffer'
import { useMutation } from '@tanstack/react-query'
import {
  editFIPurchaseOffer,
  useFIPurchaseOffer,
  useInvalidateFiPurchaseOfferQuery,
} from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { omit } from 'lodash'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { FIPurchaseOfferStatusEnum } from '@/generated-types/api'
import { mapStatusToAllowedSteps } from './common/utils'
import { getToastProps } from '@/utils/toastProps'
import { useMobile } from '@/hooks/useMobile'

export const EditPurchaseOfferForm = ({
  purchaseOfferId,
}: {
  purchaseOfferId: string
}) => {
  const { goToNextStep } = useMultiStepForm()
  const { t } = useTranslation(['common'])
  const formRef = useRef<FormikProps<FIPurchaseOfferCreate>>(null)
  const toast = useToast()
  const validationSchema = FIPurchaseOfferSchema(t)
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FIPurchaseOfferCreate>()
  const { data } = useFIPurchaseOffer(purchaseOfferId)
  const invalidateFiPurchaseOfferQuery =
    useInvalidateFiPurchaseOfferQuery(purchaseOfferId)

  const isMobile = useMobile()

  const { mutate: editPurchaseOffer, isPending } = useMutation({
    mutationFn: editFIPurchaseOffer,
    onSuccess: async (res) => {
      invalidateFiPurchaseOfferQuery()
      if (res.status !== FIPurchaseOfferStatusEnum.draft) {
        goToNextStep({
          overrideAllowedSteps: mapStatusToAllowedSteps(res.status),
        })
      }
      toast(
        getToastProps({
          title: t('fiPurchaseOffer.updated'),
          status: 'success',
          variant: 'customSuccess',
          isMobile,
        })
      )
    },
  })

  const handleOnSave = async (validate?: boolean) => {
    if (!formRef.current) {
      return
    }

    if (
      validate &&
      !formRef.current.dirty &&
      data?.status !== FIPurchaseOfferStatusEnum.draft
    ) {
      // user hasn't touched the form, navigate without calling the api
      goToNextStep()
      return
    }

    if (
      validate &&
      !(await validateAndSetErrors(
        formRef.current,
        t('fiPurchaseOffer.form.validation.errorToast')
      ))
    ) {
      return
    }

    await editPurchaseOffer({
      ...formRef.current.values,
      status: data?.status,
      purchaseOfferId,
      validate: validate,
    })
  }

  if (!data) {
    return null
  }

  const mappedData = {
    ...omit(data, ['id', 'createdAt', 'sellers', 'buyers']),
    sellerIds: (data.sellers || []).map((seller) => seller.id),
    buyerIds: (data.buyers || []).map((buyer) => buyer.id),
  } satisfies FIPurchaseOfferCreate

  return (
    <StepContentWithFooter
      footerProps={{
        actions: [
          {
            label: t('saveAsDraft'),
            onClick: () => handleOnSave(),
            isLoading: isPending,
            buttonVariant: 'transparent',
          },
          {
            label: t('next'),
            onClick: () => handleOnSave(true),
            isLoading: isPending,
          },
        ],
      }}
    >
      <Formik
        innerRef={formRef}
        initialValues={mappedData}
        validationSchema={validationSchema}
        validateOnBlur
        validateOnMount
        onSubmit={() => handleOnSave(true)}
      >
        {(formikProps) => <PurchaseOfferFormBody {...formikProps} />}
      </Formik>
    </StepContentWithFooter>
  )
}
