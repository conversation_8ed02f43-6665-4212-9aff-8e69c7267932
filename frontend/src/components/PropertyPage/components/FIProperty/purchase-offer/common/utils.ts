import { FIPurchaseOfferStatusEnum } from '@/generated-types/api'
import { TFunction } from 'next-i18next'

export const FILL_FORM_STEP = 0
export const VALIDATE_STEP = 1
export const SIGNATURES_STEP = 2

export const mapStatusToAllowedSteps = (
  status: FIPurchaseOfferStatusEnum | null | undefined
) => {
  switch (status) {
    case FIPurchaseOfferStatusEnum.draft:
      return [FILL_FORM_STEP]
    case FIPurchaseOfferStatusEnum.validated:
      return [FILL_FORM_STEP, VALIDATE_STEP, SIGNATURES_STEP]
    case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
    case FIPurchaseOfferStatusEnum.offeree_signed:
    case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
    case FIPurchaseOfferStatusEnum.offeror_signed:
    case FIPurchaseOfferStatusEnum.accepted:
    case FIPurchaseOfferStatusEnum.rejected:
      return [VALIDATE_STEP, SIGNATURES_STEP]
    default:
      return [FILL_FORM_STEP]
  }
}

export const mapStatusToStep = (
  status: FIPurchaseOfferStatusEnum | null | undefined
) => {
  switch (status) {
    case FIPurchaseOfferStatusEnum.draft:
      return FILL_FORM_STEP
    case FIPurchaseOfferStatusEnum.validated:
      return VALIDATE_STEP
    case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
    case FIPurchaseOfferStatusEnum.offeree_signed:
    case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
    case FIPurchaseOfferStatusEnum.offeror_signed:
    case FIPurchaseOfferStatusEnum.accepted:
    case FIPurchaseOfferStatusEnum.rejected:
    case FIPurchaseOfferStatusEnum.expired:
      return SIGNATURES_STEP
    default:
      return FILL_FORM_STEP
  }
}

export const getCounterOfferStatusLabel =
  (t: TFunction) => (status: FIPurchaseOfferStatusEnum | null) => {
    switch (status) {
      case FIPurchaseOfferStatusEnum.draft:
        return t('fiPurchaseOffer.form.enums.status.draft')
      case FIPurchaseOfferStatusEnum.validated:
        return t('fiPurchaseOffer.form.enums.status.validated')
      case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
        return t('fiPurchaseOffer.form.enums.status.pending_offeror_signatures')
      case FIPurchaseOfferStatusEnum.offeror_signed:
        return t('fiPurchaseOffer.form.enums.status.offeror_signed')
      case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
        return t('fiPurchaseOffer.form.enums.status.pending_offeree_signatures')
      case FIPurchaseOfferStatusEnum.offeree_signed:
        return t('fiPurchaseOffer.form.enums.status.offeree_signed')
      case FIPurchaseOfferStatusEnum.accepted:
        return t('fiPurchaseOffer.form.enums.status.accepted')
      case FIPurchaseOfferStatusEnum.rejected:
        return t('fiPurchaseOffer.form.enums.status.rejected')
      case FIPurchaseOfferStatusEnum.expired:
        return t('fiPurchaseOffer.form.enums.status.expired')
      default:
        return t('unknown')
    }
  }

export const getPurchaseOfferStatusLabel =
  (t: TFunction) => (status: FIPurchaseOfferStatusEnum | null) => {
    switch (status) {
      case FIPurchaseOfferStatusEnum.draft:
        return t('fiPurchaseOffer.form.enums.status.draft')
      case FIPurchaseOfferStatusEnum.validated:
        return t('fiPurchaseOffer.form.enums.status.validated')
      case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
        return t('fiPurchaseOffer.form.enums.status.pending_buyer_signatures')
      case FIPurchaseOfferStatusEnum.offeror_signed:
        return t('fiPurchaseOffer.form.enums.status.buyer_signed')
      case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
        return t('fiPurchaseOffer.form.enums.status.pending_seller_signatures')
      case FIPurchaseOfferStatusEnum.offeree_signed:
        return t('fiPurchaseOffer.form.enums.status.seller_signed')
      case FIPurchaseOfferStatusEnum.accepted:
        return t('fiPurchaseOffer.form.enums.status.accepted')
      case FIPurchaseOfferStatusEnum.rejected:
        return t('fiPurchaseOffer.form.enums.status.rejected')
      case FIPurchaseOfferStatusEnum.expired:
        return t('fiPurchaseOffer.form.enums.status.expired')
      default:
        return t('unknown')
    }
  }

export const purchaseOfferStatusColors = {
  [FIPurchaseOfferStatusEnum.draft]: 'badge.draft.bg',
  [FIPurchaseOfferStatusEnum.validated]: 'badge.pending.bg',
  [FIPurchaseOfferStatusEnum.pending_offeree_signatures]: 'badge.pending.bg',
  [FIPurchaseOfferStatusEnum.offeree_signed]: 'badge.published.bg',
  [FIPurchaseOfferStatusEnum.pending_offeror_signatures]: 'badge.pending.bg',
  [FIPurchaseOfferStatusEnum.offeror_signed]: 'badge.published.bg',
  [FIPurchaseOfferStatusEnum.accepted]: 'badge.published.bg',
  [FIPurchaseOfferStatusEnum.rejected]: 'badge.declined.bg',
  [FIPurchaseOfferStatusEnum.expired]: 'badge.declined.bg',
}

export const getPurchaseOfferStatusColor = (
  status: FIPurchaseOfferStatusEnum | null
) => (status ? purchaseOfferStatusColors[status] || 'gray.200' : 'gray.200')
