import { useMobile } from '@/hooks/useMobile'
import { <PERSON><PERSON>, <PERSON>lex, Spinner, Stack, Text, useToast } from '@chakra-ui/react'
import { useMemo, useState } from 'react'
import {
  CreateFISignatureModal,
  SigningFormProps,
} from '@/modules/fi-properties/components/FISignaturesModal/CreateFISignatureModal'
import { mapContactIdsToSigners } from '@/utils/signingUtils'
import { useMutation } from '@tanstack/react-query'
import { useTranslation } from 'next-i18next'
import { getToastProps } from '@/utils/toastProps'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import {
  mapContactToParticipant,
  mapSignerToParticipant,
  ParticipantCard,
} from '@/components/Card/ParticipantCard'
import {
  FIPurchaseOfferStatusEnum,
  SchemaDocumentSigner,
} from '@/generated-types/api'
import { CounterOfferCard } from './CounterOfferCard'
import { PiCheckCircle, PiSignature, PiXCircle } from 'react-icons/pi'
import { FiMail } from 'react-icons/fi'
import {
  useFICounterOffer,
  useInvalidateFiCounterOfferQuery,
  sendCounterOfferForSigning,
  sendReminderForCounterOfferSigners,
  useSetCounterOfferStatus,
} from '@/modules/fi-properties/queries/queryFiCounterOffers'
import { useFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

export const CounterOfferSignatures = ({
  purchaseOfferId,
  counterOfferId,
}: {
  purchaseOfferId: string
  counterOfferId: string
}) => {
  const [isCreateSignatureModalOpen, setIsCreateSignatureModalOpen] =
    useState(false)
  const isMobile = useMobile()
  const toast = useToast()
  const { t } = useTranslation(['common'])
  const { data: purchaseOffer } = useFIPurchaseOffer(purchaseOfferId)
  const { data: counterOffer } = useFICounterOffer(counterOfferId)
  const invalidateQuery = useInvalidateFiCounterOfferQuery(counterOfferId)

  const { isPending: isSendingForSigning, mutate: sendForSigning } =
    useMutation({
      mutationFn: sendCounterOfferForSigning,
      onSuccess: (_) => {
        invalidateQuery()
        setIsCreateSignatureModalOpen(false)

        toast(
          getToastProps({
            isMobile,
            title: t('counterOffer.counterOfferSentForSigning'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
    })

  const { mutate: sendReminder, isPending: isSendingReminder } = useMutation({
    mutationFn: sendReminderForCounterOfferSigners,
    onSuccess: () => {
      invalidateQuery()
      toast(
        getToastProps({
          isMobile,
          title: t('fiSalesAgreement.reminderSent'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
    },
    onError: (_error) => {
      toast(
        getToastProps({
          isMobile,
          title: t('error'),
          description: t('fiSalesAgreement.failedSendingReminder'),
          status: 'error',
        })
      )
    },
  })

  const { mutate: setCounterOfferStatus, isPending: isSettingStatus } =
    useSetCounterOfferStatus({
      onSuccess: () => {
        toast(
          getToastProps({
            isMobile,
            title: t('fiPurchaseOffer.statusUpdated'),
            status: 'success',
            variant: 'customSuccess',
          })
        )
      },
      onError: () => {
        toast(
          getToastProps({
            isMobile,
            title: t('error'),
            description: t('fiPurchaseOffer.failedToUpdateStatus'),
            status: 'error',
          })
        )
      },
    })

  const actions = useMemo(
    () => ({
      sendForSigning:
        counterOffer?.status === FIPurchaseOfferStatusEnum.validated ||
        counterOffer?.status === FIPurchaseOfferStatusEnum.offeror_signed,
      sendReminder:
        counterOffer?.status ===
          FIPurchaseOfferStatusEnum.pending_offeror_signatures ||
        counterOffer?.status ===
          FIPurchaseOfferStatusEnum.pending_offeree_signatures,
      rejectCounterOffer:
        counterOffer?.status !== FIPurchaseOfferStatusEnum.accepted &&
        counterOffer?.status !== FIPurchaseOfferStatusEnum.rejected &&
        counterOffer?.status !== FIPurchaseOfferStatusEnum.expired,
      acceptOffer:
        counterOffer?.status === FIPurchaseOfferStatusEnum.offeree_signed,
    }),
    [counterOffer?.status]
  )

  const sendForSigningLabel = useMemo(() => {
    switch (counterOffer?.status) {
      case FIPurchaseOfferStatusEnum.validated:
        return t('counterOffer.sendForOfferorSigning')
      case FIPurchaseOfferStatusEnum.offeror_signed:
        return t('counterOffer.sendForOffereeSigning')
      case FIPurchaseOfferStatusEnum.pending_offeror_signatures:
      case FIPurchaseOfferStatusEnum.pending_offeree_signatures:
        return t('fiPurchaseOffer.sentForSigning')
      case FIPurchaseOfferStatusEnum.offeree_signed:
        return t('fiPurchaseOffer.signed')
      default:
        return t('counterOffer.sendForOfferorSigning')
    }
  }, [counterOffer?.status, t])

  if (!purchaseOffer || !counterOffer) return null

  const handleCreateSigning = async (values) => {
    await sendForSigning({
      ...values,
      signers: mapContactIdsToSigners(values.contactIds),
      counterOfferId: counterOffer.id,
    })
  }

  const handleRejectCounterOffer = () => {
    setCounterOfferStatus({
      counterOfferId: counterOffer.id,
      status: FIPurchaseOfferStatusEnum.rejected,
    })
  }

  const handleAcceptOffer = () => {
    setCounterOfferStatus({
      counterOfferId: counterOffer.id,
      status: FIPurchaseOfferStatusEnum.accepted,
    })
  }

  const offereeIds = new Set(purchaseOffer.buyers?.map(({ id }) => id))
  const offerorIds = new Set(purchaseOffer.sellers?.map(({ id }) => id))

  const signersByParty =
    counterOffer.signings?.reduce<{
      [participantId: string]: {
        signers: SchemaDocumentSigner[]
      }
    }>((acc, signing) => {
      signing.signers?.forEach((signer) => {
        const { userId, userType } = signer

        if (
          userType === 'contact' &&
          (offerorIds.has(userId) || offereeIds.has(userId))
        ) {
          if (!acc[userId]) {
            acc[userId] = {
              signers: [],
            }
          }
          acc[userId].signers.push(signer)
        }
      })
      return acc
    }, {}) || {}

  const initialSigningFormValues: Partial<SigningFormProps> = {
    lastSigningDate:
      counterOffer.status === FIPurchaseOfferStatusEnum.offeror_signed
        ? counterOffer.validUntil
        : new Date().toISOString().split('T')[0],
    contactIds:
      counterOffer.status === FIPurchaseOfferStatusEnum.validated
        ? counterOffer.offerors.map(({ id }) => id)
        : counterOffer.status === FIPurchaseOfferStatusEnum.offeror_signed
        ? counterOffer.offerees.map(({ id }) => id)
        : [],
  }

  return (
    <StepContentWithFooter width={isMobile ? '100%' : '66%'}>
      <Stack direction="row" gap={2}>
        {actions.sendForSigning && (
          <Button
            variant="transparent"
            maxW="fit-content"
            padding="4px 8px"
            onClick={() => {
              setIsCreateSignatureModalOpen(true)
            }}
          >
            <PiSignature />
            <Text fontSize="sm" ml={2}>
              {sendForSigningLabel}
            </Text>
          </Button>
        )}
        {actions.sendReminder && (
          <Button
            variant="transparent"
            maxW="fit-content"
            padding="4px 8px"
            onClick={() => sendReminder(counterOffer.id.toString())}
          >
            {isSendingReminder ? <Spinner size="sm" /> : <FiMail />}
            <Text fontSize="sm" ml={2}>
              {t('fiPurchaseOffer.sendReminder')}
            </Text>
          </Button>
        )}
        <Flex ml="auto" gap={2}>
          {actions.rejectCounterOffer && (
            <Button
              variant="transparent"
              padding="4px 8px"
              isLoading={isSettingStatus}
              onClick={handleRejectCounterOffer}
            >
              <PiXCircle />
              <Text fontSize="sm" ml={2}>
                {t('fiPurchaseOffer.reject')}
              </Text>
            </Button>
          )}
          {actions.acceptOffer && (
            <Button
              padding="4px 8px"
              isLoading={isSettingStatus}
              onClick={handleAcceptOffer}
            >
              <PiCheckCircle />
              <Text fontSize="sm" ml={2}>
                {t('fiPurchaseOffer.accept')}
              </Text>
            </Button>
          )}
        </Flex>
      </Stack>
      <CounterOfferCard counterOffer={counterOffer} />
      <HeaderRow title={t('counterOffer.offerors')} />
      {purchaseOffer.sellers?.map((seller) => {
        const signer = signersByParty[seller.id]?.signers.pop()
        const participant = signer
          ? mapSignerToParticipant(signer)
          : mapContactToParticipant(seller)
        return (
          <ParticipantCard
            key={seller.id}
            participant={participant}
            variant="signing"
            signingStatus={signer?.status}
          />
        )
      })}

      <HeaderRow title={t('counterOffer.offerees')} />
      {purchaseOffer.buyers?.map((buyer) => {
        const signer = signersByParty[buyer.id]?.signers.pop()
        const participant = signer
          ? mapSignerToParticipant(signer)
          : mapContactToParticipant(buyer)
        return (
          <ParticipantCard
            key={buyer.id}
            participant={participant}
            variant="signing"
            signingStatus={signer?.status}
          />
        )
      })}
      {isCreateSignatureModalOpen && (
        <CreateFISignatureModal
          onSubmitMutation={handleCreateSigning}
          isSaving={isSendingForSigning}
          onClose={() => setIsCreateSignatureModalOpen(false)}
          signers={{
            realtors: false,
            contacts: true,
          }}
          dateMode={
            counterOffer.status === FIPurchaseOfferStatusEnum.offeror_signed
              ? 'datetime'
              : 'date'
          }
          initialValues={initialSigningFormValues}
        />
      )}
    </StepContentWithFooter>
  )
}
