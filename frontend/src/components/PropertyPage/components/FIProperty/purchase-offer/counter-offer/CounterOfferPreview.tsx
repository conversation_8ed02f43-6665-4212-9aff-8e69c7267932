import { useFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { Stack } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { mapContactToParticipant, ParticipantCard } from '@/components/Card'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import { DataGroup } from '@/components/DataGroup/DataGroup'
import { pick } from 'lodash'
import { useMapCounterOfferToPreviewValues } from './utils'
import { PurchaseOfferCard } from '../common/PurchaseOfferCard'
import { useMobile } from '@/hooks/useMobile'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import {
  useFICounterOffer,
  useInvalidateFiCounterOfferQuery,
} from '@/modules/fi-properties/queries/queryFiCounterOffers'

export const CounterOfferPreview = ({
  counterOfferId,
  purchaseOfferId,
}: {
  counterOfferId: string
  purchaseOfferId: string
}) => {
  const isMobile = useMobile()
  const { t } = useTranslation(['common'])
  const invalidateQuery = useInvalidateFiCounterOfferQuery(counterOfferId)
  const mapCounterOfferToPreviewValues = useMapCounterOfferToPreviewValues(t)

  const { data: purchaseOffer } = useFIPurchaseOffer(purchaseOfferId)
  const { data: counterOffer } = useFICounterOffer(counterOfferId)

  if (!counterOffer || !purchaseOffer) {
    return null
  }

  return (
    <StepContentWithFooter width={isMobile ? '100%' : '66%'}>
      <HeaderRow title={t('counterOffer.originalOffer')} />
      <PurchaseOfferCard purchaseOffer={purchaseOffer} />
      <Stack spacing={4}>
        <HeaderRow title={t('counterOffer.offerors')} />
        <Stack direction="row">
          {counterOffer.offerors?.map((offeror) => (
            <ParticipantCard
              key={offeror.id}
              participant={mapContactToParticipant(offeror)}
              onEditSuccess={invalidateQuery}
              additionalFields={['email', 'phone', 'address']}
            />
          ))}
        </Stack>
      </Stack>
      <Stack spacing={4}>
        <HeaderRow title={t('counterOffer.offerees')} />
        <Stack direction="row">
          {counterOffer.offerees?.map((offeree) => (
            <ParticipantCard
              key={offeree.id}
              participant={mapContactToParticipant(offeree)}
              onEditSuccess={invalidateQuery}
              additionalFields={['email', 'phone', 'address']}
            />
          ))}
        </Stack>
      </Stack>
      <DataGroup
        title={t('counterOffer.terms')}
        values={mapCounterOfferToPreviewValues(
          pick(counterOffer, [
            'unencumberedPrice',
            'validUntil',
            'additionalDetails',
          ])
        )}
      />
    </StepContentWithFooter>
  )
}

export default CounterOfferPreview
