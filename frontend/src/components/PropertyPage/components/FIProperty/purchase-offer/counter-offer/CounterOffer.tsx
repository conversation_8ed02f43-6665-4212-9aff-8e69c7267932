import { useTranslation } from 'next-i18next'
import {
  FILL_COUNTER_OFFER_STEP,
  mapStatusToAllowedSteps,
  mapStatusToStep,
  PREVIEW_COUNTER_OFFER_STEP,
  SIGN_COUNTER_OFFER_STEP,
} from './utils'
import CounterOfferCreateEdit from './CounterOfferCreateEdit'
import CounterOfferPreview from './CounterOfferPreview'
import { CounterOfferSignatures } from './CounterOfferSignatures'
import { useFICounterOffer } from '@/modules/fi-properties/queries/queryFiCounterOffers'
import { Step } from '@/components/MultiStepForm/Step'
import { MultiStepFormContainer } from '@/components/MultiStepForm/MultiStepFormContainer'

export const CounterOffer = ({
  purchaseOfferId,
  counterOfferId,
}: {
  purchaseOfferId: string
  counterOfferId: string | null
}) => {
  const { t } = useTranslation(['common'])
  const { data: counterOffer } = useFICounterOffer(counterOfferId ?? '')

  const steps = [
    { title: t('fiPurchaseOffer.steps.fill') },
    { title: t('fiPurchaseOffer.steps.validate') },
    { title: t('fiPurchaseOffer.steps.sign') },
  ]

  if (counterOfferId && !counterOffer) return null

  return (
    <MultiStepFormContainer
      steps={steps}
      initialStep={mapStatusToStep(counterOffer?.status)}
      allowedSteps={mapStatusToAllowedSteps(counterOffer?.status)}
    >
      <>
        <Step stepIndex={FILL_COUNTER_OFFER_STEP}>
          <CounterOfferCreateEdit
            purchaseOfferId={purchaseOfferId}
            counterOfferId={counterOfferId}
          />
        </Step>
        {counterOfferId && (
          <>
            <Step stepIndex={PREVIEW_COUNTER_OFFER_STEP}>
              <CounterOfferPreview
                purchaseOfferId={purchaseOfferId}
                counterOfferId={counterOfferId}
              />
            </Step>

            <Step stepIndex={SIGN_COUNTER_OFFER_STEP}>
              <CounterOfferSignatures
                purchaseOfferId={purchaseOfferId}
                counterOfferId={counterOfferId}
              />
            </Step>
          </>
        )}
      </>
    </MultiStepFormContainer>
  )
}

export default CounterOffer
