import {
  useCreateFICounterOffer,
  useEditFICounterOffer,
} from '@/modules/fi-properties/queries/queryFiCounterOffers'
import { useFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import {
  Heading,
  Stack,
  Text,
  useBreakpointValue,
  useToast,
} from '@chakra-ui/react'
import { PurchaseOfferCard } from '../common/PurchaseOfferCard'
import { Form, Formik, FormikProps } from 'formik'
import {
  emptyCounterOffer,
  FICounterOfferCreate,
} from '@/modules/fi-properties/types/FICounterOffer'
import { InputWithLabel } from '@/components/Form/Input'
import TitleWithDivider from '@/components/TitleWithDivider'
import TextareaField from '@/components/Form/TextareaField'
import { useTranslation } from 'next-i18next'
import { CounterOfferSchema } from './CounterOfferSchema'
import { useRef } from 'react'
import { useValidateFormAndSetErrors } from '@/hooks/useValidateFormAndSetErrors'
import { useRouter } from 'next/router'

import { getToastProps } from '@/utils/toastProps'
import { useFICounterOffer } from '@/modules/fi-properties/queries/queryFiCounterOffers'
import { useMultiStepForm } from '@/hooks/useMultiStepForm'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'

export const CounterOfferEditCreate = ({
  purchaseOfferId,
  counterOfferId,
}: {
  purchaseOfferId: string
  counterOfferId: string | null
}) => {
  const { goToNextStep } = useMultiStepForm()
  const formRef = useRef<FormikProps<FICounterOfferCreate>>(null)
  const isMobile = useBreakpointValue({ base: true, md: false }) || false
  const { t } = useTranslation(['common'])
  const schema = CounterOfferSchema(t)
  const { validateAndSetErrors } =
    useValidateFormAndSetErrors<FICounterOfferCreate>()
  const router = useRouter()
  const toast = useToast()

  const { data: purchaseOffer } = useFIPurchaseOffer(purchaseOfferId)
  const { data: counterOffer } = useFICounterOffer(counterOfferId ?? '')

  const { mutate: createCounterOffer } = useCreateFICounterOffer({
    onSuccess: (data) => {
      goToNextStep()
      toast(
        getToastProps({
          isMobile,
          title: t('counterOffer.counterOfferCreated'),
          status: 'success',
          variant: 'customSuccess',
        })
      )

      router.push(
        {
          pathname: router.pathname,
          query: {
            ...router.query,
            counterOfferId: data.id,
            createdCounterOffer: undefined,
          },
        },
        undefined,
        { shallow: true }
      )
    },
    onError: (error) => {
      toast(
        getToastProps({
          title: t('counterOffer.creationFailed'),
          status: 'error',
          description: error.message,
        })
      )
    },
  })

  const { mutate: editCounterOffer } = useEditFICounterOffer({
    onSuccess: (_) => {
      goToNextStep()
      toast(
        getToastProps({
          isMobile,
          title: t('counterOffer.counterOfferUpdated'),
          status: 'success',
          variant: 'customSuccess',
        })
      )
    },
    onError: (error) => {
      toast(
        getToastProps({
          title: t('counterOffer.updateFailed'),
          status: 'error',
          description: error.message,
        })
      )
    },
  })

  if (!purchaseOffer) return null

  const handleSubmit = async () => {
    if (!formRef.current) {
      return
    }
    if (
      !(await validateAndSetErrors(
        formRef.current,
        t('counterOffer.validationFailed')
      ))
    ) {
      return
    }

    if (counterOfferId) {
      editCounterOffer({
        ...formRef.current.values,
        id: counterOfferId,
      })
    } else {
      const offerorIds = counterOffer?.previousCounterOffer
        ? counterOffer?.previousCounterOffer.offerees?.map(
            (offeree) => offeree.id
          )
        : purchaseOffer?.sellers?.map(({ id }) => id) ?? []

      const offereeIds = counterOffer?.previousCounterOffer
        ? counterOffer?.previousCounterOffer.offerors?.map(({ id }) => id)
        : purchaseOffer?.buyers?.map(({ id }) => id) ?? []
      createCounterOffer({
        ...formRef.current.values,
        offereeIds,
        offerorIds,
      })
    }
  }

  const data: FICounterOfferCreate =
    counterOfferId && counterOffer
      ? {
          ...counterOffer,
          offerorIds: counterOffer.offerors.map(({ id }) => id),
          offereeIds: counterOffer.offerees.map(({ id }) => id),
          previousCounterOfferId: counterOffer.previousCounterOffer?.id || null,
          purchaseOfferId: parseInt(purchaseOfferId),
        }
      : {
          ...emptyCounterOffer,
          purchaseOfferId: parseInt(purchaseOfferId),
        }

  return (
    <StepContentWithFooter
      width={isMobile ? '100%' : '66%'}
      footerProps={{
        actions: [{ label: t('next'), onClick: handleSubmit }],
      }}
    >
      <Formik
        initialValues={data}
        validationSchema={schema}
        validateOnBlur
        innerRef={formRef}
        onSubmit={handleSubmit}
      >
        {({ touched, errors, values }) => (
          <Form>
            <Stack
              gap={'1rem'}
              sx={{
                '& > *:not(.guide)': {
                  maxWidth: isMobile ? '100%' : '66%',
                },
              }}
            >
              <Heading variant="H1">{t('counterOffer.fill')}</Heading>
              <Text color="gray.500" fontSize="sm">
                {t('counterOffer.fillSubtitle')}
              </Text>
              <Heading variant="H1">{t('counterOffer.originalOffer')}</Heading>
              <PurchaseOfferCard
                purchaseOffer={purchaseOffer}
                extraFields={[
                  {
                    label: t('fiPurchaseOffer.form.headers.buyers'),
                    value: (purchaseOffer.buyers || [])
                      ?.map(({ name }) => name)
                      .join(', '),
                  },
                  {
                    label: t('fiPurchaseOffer.form.headers.sellers'),
                    value: (purchaseOffer.sellers || [])
                      ?.map(({ name }) => name)
                      .join(', '),
                  },
                ]}
              />
              <Heading variant="H1">{t('counterOffer.terms')}</Heading>
              <TitleWithDivider title={t('counterOffer.validUntilHeader')} />
              <InputWithLabel
                label={t('counterOffer.validUntil')}
                name={'validUntil'}
                type={'datetime-local'}
                error={errors.validUntil}
                touched={touched.validUntil}
                defaultValue={values.validUntil}
                required
              />
              <TitleWithDivider title={t('price')} />
              <InputWithLabel
                label={t('counterOffer.unencumberedPrice')}
                name={'unencumberedPrice'}
                type={'currency'}
                error={errors.unencumberedPrice}
                touched={touched.unencumberedPrice}
                defaultValue={values.unencumberedPrice}
                rightElement={'EUR'}
                required
              />
              <TitleWithDivider title={t('counterOffer.otherTerms')} />
              <TextareaField
                label={t('counterOffer.additionalDetails')}
                name={'additionalDetails'}
                defaultValue={values.additionalDetails ?? ''}
              />
            </Stack>
          </Form>
        )}
      </Formik>
    </StepContentWithFooter>
  )
}

export default CounterOfferEditCreate
