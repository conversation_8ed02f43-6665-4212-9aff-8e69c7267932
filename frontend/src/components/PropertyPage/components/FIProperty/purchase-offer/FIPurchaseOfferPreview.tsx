import PreviewHtmlModal from '@/components/Modal/PreviewHtmlModal'
import { useFIPurchaseOffer } from '@/modules/fi-properties/queries/queryFiPurchaseOffers'
import { Stack } from '@chakra-ui/react'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useTranslation } from 'next-i18next'
import { useInFIPropertyDetailPage } from '@/hooks/useInFiPropertyPage'
import {
  mapContactToParticipant,
  ParticipantFieldOptions,
  ParticipantCard,
  FIPropertyCard,
} from '@/components/Card'
import { HeaderRow } from '@/components/HeaderRow/HeaderRow'
import { DataGroup } from '@/components/DataGroup/DataGroup'
import { ContactType } from '@/generated-types/api'
import { usePurchaseOfferPreviewSections } from './common/previewUtils'
import { StepContentWithFooter } from '@/components/MultiStepForm/StepContainerWithFooter'
import { useMobile } from '@/hooks/useMobile'

export const FIPurchaseOfferPreview = ({
  purchaseOfferId,
}: {
  purchaseOfferId: string
}) => {
  const isMobile = useMobile()
  const [showPreviewHtml, setShowPreviewHtml] = useState(false)
  const queryClient = useQueryClient()
  const { t } = useTranslation(['common'])
  const { property } = useInFIPropertyDetailPage()
  const { data } = useFIPurchaseOffer(purchaseOfferId)
  const previewSections = usePurchaseOfferPreviewSections(data)

  if (!data || !property) {
    return null
  }

  const getParticipantAdditionalFields = (
    contactType: ContactType | null
  ): ParticipantFieldOptions => {
    const base: ParticipantFieldOptions = ['address', 'phone', 'email']

    switch (contactType) {
      case ContactType.Person:
        return [...base, 'ssn']
      case ContactType.Estate:
        return [...base, 'contactPersonName']
      case ContactType.Organization:
        return [...base, 'companyId', 'contactPersonName']
      default:
        return []
    }
  }

  return (
    <StepContentWithFooter maxW={isMobile ? '100%' : '66%'}>
      <FIPropertyCard
        header={t('property') ?? ''}
        property={property}
        onModalClose={() =>
          queryClient.invalidateQueries({
            queryKey: ['fi-purchase-offer'],
          })
        }
      />
      <Stack spacing={4}>
        <HeaderRow title={t('seller')} />
        <Stack direction="row">
          {data.sellers?.map((seller) => (
            <ParticipantCard
              key={seller.id}
              participant={mapContactToParticipant(seller)}
              onEditSuccess={() =>
                queryClient.invalidateQueries({
                  queryKey: ['fi-purchase-offer'],
                })
              }
              additionalFields={getParticipantAdditionalFields(seller.type)}
              highlightMissingFields
            />
          ))}
        </Stack>
      </Stack>
      <Stack spacing={4}>
        <HeaderRow title={t('buyer')} />
        <Stack direction="row">
          {data.buyers?.map((buyer) => (
            <ParticipantCard
              key={buyer.id}
              participant={mapContactToParticipant(buyer)}
              onEditSuccess={() =>
                queryClient.invalidateQueries({
                  queryKey: ['fi-purchase-offer'],
                })
              }
              additionalFields={getParticipantAdditionalFields(buyer.type)}
              highlightMissingFields
            />
          ))}
        </Stack>
      </Stack>
      <Stack spacing={4} mt={4}>
        {previewSections.map((section) => {
          return <DataGroup key={section.title} {...section} />
        })}
      </Stack>
      {showPreviewHtml && (
        <PreviewHtmlModal
          type="purchase-offer"
          entityId={purchaseOfferId}
          previewIsOpen={showPreviewHtml}
          setPreviewIsOpen={setShowPreviewHtml}
        />
      )}
    </StepContentWithFooter>
  )
}
export default FIPurchaseOfferPreview
