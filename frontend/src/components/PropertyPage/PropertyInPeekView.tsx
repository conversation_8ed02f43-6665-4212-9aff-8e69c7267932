import {
  <PERSON>er,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>eader,
  <PERSON>,
  Drawer<PERSON>ody,
  Drawer<PERSON>ooter,
  Button,
  Box,
  DrawerCloseButton,
  Flex,
  Badge,
  Grid,
  GridItem,
} from '@chakra-ui/react'
import { useMobile } from '@/hooks/useMobile'
import { useInfiniteQuery } from '@tanstack/react-query'
import { ListProperties, StatusEnum } from '@/types/property'
import { ApiResponseWithMetadata } from '@/types/common'
import { useTranslation } from 'next-i18next'
import { ImageWithFallback } from '../ImageWithFallback'
import { StrandifiedTag } from '../StrandifiedTag'
import { IMAGE_SIZE_M_WIDTH } from '@/utils/constants'
import placeholder from '@/cache/images/property_placeholder.png'
import { Empty } from '../Empty'
import {
  getPropertyCommissionAndRent,
  getPropertyPrice,
  formatPropertyTypeInfo,
} from '@/modules/es-properties/utils/type.utils'
import { formatLocaleDate } from '@/utils/date'
import { CountryNameEnum } from '@/modules/country'

const PropertyInPeekView = ({
  isOpen,
  onClose,
  queryFn,
  onChangeProperty,
  reference,
  queryKey,
  openFullPage,
}: {
  isOpen: boolean
  onClose: () => void
  queryKey: string[]
  queryFn: (page: number) => Promise<ApiResponseWithMetadata<ListProperties>>
  onChangeProperty: (reference: string) => void
  reference: string
  openFullPage: () => void
}) => {
  const isMobile = useMobile()
  const { t } = useTranslation()
  const {
    data,
    fetchNextPage,
    fetchPreviousPage,
    hasNextPage,
    hasPreviousPage,
    isFetchingNextPage,
    isFetchingPreviousPage,
  } = useInfiniteQuery({
    queryKey,
    queryFn: (params) => queryFn(params?.pageParam),
    getNextPageParam: (lastPage) =>
      lastPage?.metadata.page !== lastPage?.metadata.pageCount
        ? lastPage?.metadata.page + 1
        : undefined,
    getPreviousPageParam: (firstPage) =>
      firstPage?.metadata.page !== 1 ? firstPage?.metadata.page - 1 : undefined,
    initialPageParam: 1,
    enabled: false,
  })

  const list = data?.pages.flatMap((page) => page.records) || []

  const currentPropertyIndex = list.findIndex(
    (property) => property.reference === reference
  )

  const property = list.find((property) => property.reference === reference)

  const handleGetPreviousProperty = async () => {
    if (currentPropertyIndex === 0) {
      const { data } = await fetchPreviousPage()
      const result = data?.pages.at(0)?.records.at(-1)
      onChangeProperty(result?.reference || '')
      return
    }
    const nextProperty = list[currentPropertyIndex - 1]
    onChangeProperty(nextProperty.reference)
  }

  const handleGetNextProperty = async () => {
    if (currentPropertyIndex === list.length - 1) {
      const { data } = await fetchNextPage()

      const result = data?.pages.at(-1)?.records.at(0)
      onChangeProperty(result?.reference || '')

      return
    }

    const nextProperty = list[currentPropertyIndex + 1]

    onChangeProperty(nextProperty.reference)
  }

  const mainImg = property?.mainImg
  const status = property?.status
  const soldBy = property?.soldBy
  const isStrandified = property?.isStrandified
  const city = property?.location ? `${property?.location?.split(', ')[0]}` : ''
  const country = CountryNameEnum.SPAIN

  const propertyInformation = [
    {
      column: 2,
      label: t('location'),
      value: property?.location,
    },
    {
      column: 1,
      label: t('city'),
      value: city,
    },
    {
      column: 1,
      label: t('country'),
      value: country,
    },
    {
      column: 2,
      label: t('specs'),
      value: property && formatPropertyTypeInfo(property, t),
    },
    {
      column: 1,
      label: t('price'),
      value: property && getPropertyPrice(property),
    },
    {
      column: 1,
      label: t('commission'),
      value: property && getPropertyCommissionAndRent(property),
    },
    {
      column: 2,
      label: t('listedSince'),
      value: formatLocaleDate({
        date: property?.createdAt,
      }),
    },
  ]

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      placement={isMobile ? 'bottom' : 'right'}
      size={'md'}
    >
      <DrawerOverlay />
      <DrawerContent borderRadius={isMobile ? undefined : 0}>
        {property && (
          <>
            <DrawerHeader
              p="4"
              borderBottom="1px solid"
              borderBottomColor="gray.200"
            >
              <Text>{property.propertyType}</Text>
              <Text fontSize="sm" fontWeight={350}>
                {city}
              </Text>
            </DrawerHeader>
            <DrawerBody>
              <Box position="relative" overflow="hidden">
                <Box height="60">
                  <ImageWithFallback
                    sizes="(max-height: 240px)"
                    alt="Property main image"
                    aria-hidden="true"
                    fallback={placeholder.src}
                    src={
                      mainImg
                        ? `${mainImg}?width=${IMAGE_SIZE_M_WIDTH}`
                        : placeholder.src
                    }
                  />
                </Box>

                <Flex
                  position="absolute"
                  bottom="3"
                  left="3"
                  gap="2"
                  zIndex={1}
                >
                  <Badge
                    justifyContent="center"
                    alignItems="center"
                    padding="2px 8px"
                    bgColor={
                      status
                        ? `badge.${status.toLowerCase()}.bg`
                        : 'transparent'
                    }
                  >
                    <Text fontSize="12px" fontWeight="normal" color="black">
                      {status === StatusEnum.SOLD && soldBy
                        ? `${status} (${soldBy})`
                        : status}
                    </Text>
                  </Badge>
                  {isStrandified && <StrandifiedTag />}
                </Flex>
              </Box>
              <Grid templateColumns="repeat(2, 1fr)" columnGap="4">
                {propertyInformation.map((item) => {
                  return (
                    <GridItem
                      borderBottom="1px solid"
                      borderColor="grays.grayDivider"
                      py="4"
                      key={item.label}
                      colSpan={item.column}
                    >
                      <Text variant="small" mr="2">
                        {item.label}
                      </Text>
                      {item.value ? (
                        <Text fontSize="sm" mt="0">
                          {item.value}
                        </Text>
                      ) : (
                        <Empty />
                      )}
                    </GridItem>
                  )
                })}
              </Grid>
            </DrawerBody>
          </>
        )}
        <DrawerCloseButton />
        <DrawerFooter justifyContent="space-between">
          <Button
            variant="ghost"
            mr={3}
            colorScheme="whiteAlpha"
            color="black"
            isLoading={isFetchingPreviousPage}
            disabled={
              (!hasPreviousPage && !currentPropertyIndex) || isFetchingNextPage
            }
            onClick={() => handleGetPreviousProperty()}
          >
            <Box
              as="span"
              className="material-symbols-outlined"
              mr="2"
              fontSize="xl"
            >
              arrow_back
            </Box>
            {t('previous')}
          </Button>
          <Button
            colorScheme="blue"
            onClick={() => openFullPage()}
            fontSize="sm"
          >
            <Box
              as="span"
              className="material-symbols-outlined"
              mr="2"
              fontSize="xl"
            >
              open_in_full
            </Box>
            {t('openInFullPage')}
          </Button>
          <Button
            variant="ghost"
            mr={3}
            isLoading={isFetchingNextPage}
            disabled={!hasNextPage || isFetchingNextPage}
            colorScheme="whiteAlpha"
            color="black"
            onClick={() => handleGetNextProperty()}
          >
            {t('next')}
            <Box
              as="span"
              className="material-symbols-outlined"
              ml="2"
              fontSize="xl"
            >
              arrow_forward
            </Box>
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default PropertyInPeekView
