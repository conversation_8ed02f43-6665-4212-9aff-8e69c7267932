import { Box, Flex, Tooltip } from '@chakra-ui/react'

interface IData {
  icon: string
  value: string
  label: string | null | undefined
}

interface Props {
  onChange: (value: string) => void
  data: IData[]
  value?: string | null
}

const Switch = ({ onChange, data, value }: Props) => {
  return (
    <Flex>
      <Tooltip label={data[0].label} hasArrow>
        <Flex
          borderColor="grays.grayDivider"
          borderWidth="1px"
          py="1.5"
          pr="3"
          pl="4"
          roundedLeft="2xl"
          onClick={() => onChange(data[0].value)}
          bg={value === data[0].value ? 'black' : 'white'}
          cursor="pointer"
        >
          <Box
            fontSize="xl"
            className="material-symbols-outlined"
            color={value === data[0].value ? 'white' : 'black'}
          >
            {data[0].icon || 'open_in_full'}
          </Box>
        </Flex>
      </Tooltip>
      <Tooltip label={data[1].label} hasArrow>
        <Flex
          borderColor="grays.grayDivider"
          borderWidth="1px"
          py="1.5"
          pr="4"
          pl="3"
          roundedRight="2xl"
          onClick={() => onChange(data[1].value)}
          bg={value === data[1].value ? 'black' : 'white'}
          cursor="pointer"
        >
          <Box
            fontSize="xl"
            className="material-symbols-outlined"
            color={value === data[1].value ? 'white' : 'black'}
          >
            {data[1].icon || 'dock_to_left'}
          </Box>
        </Flex>
      </Tooltip>
    </Flex>
  )
}

export default Switch
