import { Flex, Divider, Text, Heading, Button } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

const PieChart = ({
  text,
  deposited,
  expensed,
  onHold,
}: {
  text: string
  deposited: number
  expensed: number
  onHold: number
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const { t } = useTranslation()

  useEffect(() => {
    if (chartRef.current) {
      const chartInstance = echarts.init(chartRef.current)

      const option = {
        title: {
          text,
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: €{c} ({d}%)',
        },
        series: [
          {
            name: 'Budget Usage',
            type: 'pie',
            radius: ['90%', '100%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: { show: false },
            labelLine: { show: false },
            hoverAnimation: false,
            data: [
              {
                value: (deposited / 100).toFixed(2),
                name: t('expenses.deposited'),
                itemStyle: { color: '#4CAF50' },
              },
              {
                value: (onHold / 100).toFixed(2),
                name: t('expenses.on_hold'),
                itemStyle: { color: '#FFA500' },
              },
              {
                value: (expensed / 100).toFixed(2),
                name: t('expenses.expensed'),
                itemStyle: { color: '#EF7E84' },
              },
            ],
          },
        ],
      }

      chartInstance.setOption(option)

      const handleResize = () => {
        chartInstance.resize()
      }
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        chartInstance.dispose()
      }
    }
  }, [expensed, deposited, onHold, text, t])

  return <div ref={chartRef} style={{ width: '160px', height: '160px' }} />
}

export const ExpenseSummaryBanner = ({
  deposited,
  expensed,
  onHold,
  cardDetails,
  onRemoveCard,
}: {
  deposited: number
  expensed: number
  onHold: number
  cardDetails: string
  onRemoveCard: () => void
}) => {
  const { t } = useTranslation()

  const formatToEuro = (amount: number) => {
    return new Intl.NumberFormat(undefined, {
      style: 'currency',
      currency: 'EUR',
    }).format(amount / 100)
  }

  const cardDetailsFormatted = `•••• •••• •••• ${cardDetails}`
  const balanceFormatted = formatToEuro(deposited - expensed - onHold)

  return (
    <Flex
      backgroundColor="primary.main"
      height="100%"
      flexDirection="row"
      justifyContent="space-between"
      paddingX={8}
      paddingY={4}
      width="100%"
      alignItems="center"
      borderRadius="24px"
    >
      <Flex
        gap={6}
        alignItems="center"
        flexWrap="wrap"
        justifyContent={['center', 'flex-start']}
      >
        <Flex flexDirection="column" gap={1}>
          <Flex justifyContent="column" gap={2}>
            <Text fontSize="medium" marginTop={0} fontWeight="bold">
              {t('expenses.wallet_balance')}
            </Text>
            <Text fontSize="medium" marginTop={0} fontWeight="bold">
              {balanceFormatted}
            </Text>
          </Flex>
          <Divider orientation="horizontal" borderColor="primary.darker" />
          <Flex justifyContent="column" gap={2}>
            <Text fontSize="medium" marginTop={0} textColor="#4CAF50">
              •
            </Text>
            <Text fontSize="medium" marginTop={0}>
              {t('expenses.deposited')}
            </Text>
            <Text fontSize="medium" marginTop={0}>
              {formatToEuro(deposited)}
            </Text>
          </Flex>
          <Divider orientation="horizontal" borderColor="primary.darker" />
          <Flex justifyContent="column" gap={2}>
            <Text fontSize="medium" marginTop={0} textColor="#FFA500">
              •
            </Text>
            <Text fontSize="medium" marginTop={0}>
              {t('expenses.on_hold')}
            </Text>
            <Text fontSize="medium" marginTop={0}>
              -{formatToEuro(onHold)}
            </Text>
          </Flex>
          <Divider orientation="horizontal" borderColor="primary.darker" />
          <Flex justifyContent="column" gap={2}>
            <Text fontSize="medium" marginTop={0} textColor="#EF7E84">
              •
            </Text>
            <Text fontSize="medium" marginTop={0}>
              {t('expenses.expensed')}
            </Text>
            <Text fontSize="medium" marginTop={0}>
              -{formatToEuro(expensed)}
            </Text>
          </Flex>
        </Flex>
        <PieChart
          text={balanceFormatted}
          deposited={deposited}
          expensed={expensed}
          onHold={onHold}
        />
        <Divider
          orientation="vertical"
          borderColor="primary.darker"
          height="200px"
          display={['none', 'block']}
        />

        <Flex
          flexDirection="column"
          justifyContent="start"
          minHeight="100%"
          gap={4}
        >
          <Heading variant="H3">{t('expenses.connected_card')}</Heading>
          <span>{cardDetailsFormatted}</span>
          <Button
            fontWeight={500}
            fontSize="16px"
            size="lg"
            variant="smooth"
            onClick={onRemoveCard}
          >
            {t('expenses.remove_card')}
          </Button>
        </Flex>
      </Flex>
    </Flex>
  )
}
