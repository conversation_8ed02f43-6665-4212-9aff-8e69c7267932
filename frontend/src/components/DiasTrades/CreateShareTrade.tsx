import { Formik, FormikProps } from 'formik'
import { DeviceAdapter } from '../ActionDrawer/DeviceAdapter'
import { useTranslation } from 'next-i18next'
import { CreatEditForm } from './FormSections/CreatEditForm'
import {
  convertToShareTradeCreateForm,
  ShareCertificateStatus,
  SharesType,
  CreateShareTradeMutationProps,
  ShareTradeCreateEditForm,
  ShareTradeRead,
  UpdateShareTradeMutationProps,
} from '@/types/dias_share_trade'
import { InvoiceVerificationType } from '@/types/dias_common'
import {
  PaginationProps,
  usePaginationDropdownData,
} from '@/hooks/usePaginationDropdownData'
import { getContacts } from '@/queries/contact'
import { ContactOption } from '@/types/contact'
import { createShareTrade, updateShareTrade } from '@/queries/dias'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useBreakpointValue, useToast } from '@chakra-ui/react'
import { useAuth } from '@/hooks/useAuth'

interface CreateShareTradeProps {
  isOpen?: boolean
  onClose: () => void
  clonedValues?: ShareTradeCreateEditForm
}

enum ShareTradeModalMode {
  CREATE = 1,
  EDIT = 2,
}

interface ShareTradeModalProps extends CreateShareTradeProps {
  initialValues: ShareTradeCreateEditForm
  onSubmit: (values: ShareTradeCreateEditForm) => void
  isLoading: boolean
  mode?: ShareTradeModalMode
}

interface EditShareTradeProps extends CreateShareTradeProps {
  id: string
  initialValues: ShareTradeCreateEditForm
}

export const ShareTradeModal = ({
  isOpen = true,
  onClose,
  initialValues,
  onSubmit,
  isLoading,
  mode = ShareTradeModalMode.CREATE,
}: ShareTradeModalProps) => {
  const { t } = useTranslation(['common'])

  const handleAcceptClick = (formik: FormikProps<ShareTradeCreateEditForm>) => {
    formik.submitForm()
  }

  const { paginationProps: contactsPaginationProps } =
    usePaginationDropdownData({
      queryKey: ['contacts'],
      queryFn: (params) =>
        getContacts({
          ...params,
          pageSize: 25,
        }).then((data) => ({
          records: data.records.map((contact) => ({
            id: contact.id,
            name: contact.name,
            socialSecurityNumber: contact.socialSecurityNumber,
            businessId: contact.businessId,
            email: contact.email,
          })),
          metadata: data.metadata,
        })),
    })

  return (
    <Formik initialValues={initialValues} onSubmit={onSubmit}>
      {(formik) => (
        <DeviceAdapter
          size="md"
          isOpen={isOpen}
          isLoading={isLoading}
          disableAcceptButton={!formik.values.isFIPropertyLocked}
          title={
            mode === ShareTradeModalMode.CREATE
              ? t('shareTradeModal.createTitle')
              : t('shareTradeModal.editTitle')
          }
          onAccept={() => handleAcceptClick(formik)}
          onClose={onClose}
          onReject={onClose}
          dirty={formik.dirty}
          isSubmitting={formik.isSubmitting}
        >
          <CreatEditForm
            isOpen={isOpen}
            contactsPaginationProps={
              contactsPaginationProps as PaginationProps<ContactOption>
            }
          />
        </DeviceAdapter>
      )}
    </Formik>
  )
}

export const CreateShareTrade = ({
  isOpen = true,
  onClose,
  clonedValues,
}: CreateShareTradeProps) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const { currentUser } = useAuth().authStates

  const defaultValues: ShareTradeCreateEditForm = {
    isFIPropertyLocked: false,
    apartment: {
      address: {
        streetAddress: '',
        postalCode: '',
        city: '',
      },
      housingCompany: {
        name: '',
        businessId: '',
      },
      sharesType: SharesType.DIGITAL,
      shares: [],
      osakeryhmatunnukset: [],
    },
    sellerShareCertificateStatus: ShareCertificateStatus.UNKNOWN,
    internalSellers: [],
    internalBuyers: [],
    realtorBankAccount: undefined,
    internalRealtor: {},
    initiatorPersonId: String(currentUser?.id) || '',
    initiatorContactInfo: {
      firstName: currentUser?.firstName || '',
      lastName: currentUser?.lastName || '',
      email: currentUser?.email || '',
      phoneNumber: currentUser?.phoneNumber || '',
    },
    includeCommission: true,
    realtorSumCommission: 0,
    invoiceVerificationType: InvoiceVerificationType.INVOICE_REFERENCE_NUMBER,
    invoiceVerificationValue: '',
    existingAttachments: [],
    shareCertificates: [],
    billOfSale: [],
    houseManagersCertificate: [],
    ownerApartmentPrintout: [],
    companyStatements: [],
    spousesConsent: [],
    isBuyersMortgage: false,
    hasDeadlineForSigningBillOfSale: false,
    deadlineForSigningBillOfSale: new Date().toISOString(),
  }

  const initialValues: ShareTradeCreateEditForm = clonedValues
    ? { ...defaultValues, ...clonedValues }
    : defaultValues

  const onSubmit = (values: ShareTradeCreateEditForm) => {
    const shareTrade: CreateShareTradeMutationProps =
      convertToShareTradeCreateForm(values)
    mutationCreateShareTrade(shareTrade)
  }

  const { isPending: isCreatingShareTrade, mutate: mutationCreateShareTrade } =
    useMutation({
      mutationKey: ['shareTrade'],
      mutationFn: createShareTrade,
      onSuccess: (createdShareTrade: ShareTradeRead) => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('shareTradeModal.createTitle'),
          description: `${t('shareTradeModal.createdSuccess')} "id:${
            createdShareTrade.id
          }"`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        onClose()
        return createdShareTrade
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('shareTradeModal.createFailed'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  return (
    <ShareTradeModal
      isOpen={isOpen}
      onClose={onClose}
      initialValues={initialValues}
      onSubmit={onSubmit}
      isLoading={isCreatingShareTrade}
      mode={ShareTradeModalMode.CREATE}
    />
  )
}

export const EditShareTrade = ({
  isOpen = true,
  onClose,
  id,
  initialValues,
}: EditShareTradeProps) => {
  const { t } = useTranslation(['common'])
  const isMobile = useBreakpointValue({ base: true, md: false })
  const toast = useToast()
  const queryClient = useQueryClient()

  const onSubmit = (values: ShareTradeCreateEditForm) => {
    const shareTrade: CreateShareTradeMutationProps =
      convertToShareTradeCreateForm(values)
    const shareTradeData: UpdateShareTradeMutationProps = {
      id,
      ...shareTrade,
    }
    mutationUpdateShareTrade(shareTradeData)
  }

  const { isPending: isUpdatingShareTrade, mutate: mutationUpdateShareTrade } =
    useMutation({
      mutationKey: ['shareTrade'],
      mutationFn: updateShareTrade,
      onSuccess: (updatedShareTrade: ShareTradeRead) => {
        queryClient.invalidateQueries({
          queryKey: ['dias-shared-trade', id],
        })
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('shareTradeModal.editTitle'),
          description: t('shareTradeModal.updatedSuccess'),
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        onClose()
        return updatedShareTrade
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('shareTradeModal.updateFailed'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    })

  return (
    <ShareTradeModal
      isOpen={isOpen}
      onClose={onClose}
      initialValues={initialValues}
      onSubmit={onSubmit}
      isLoading={isUpdatingShareTrade}
      mode={ShareTradeModalMode.EDIT}
    />
  )
}
