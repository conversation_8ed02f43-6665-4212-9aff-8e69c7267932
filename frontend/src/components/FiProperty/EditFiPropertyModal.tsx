import { Formik } from 'formik'
import { DeviceAdapter } from '../ActionDrawer/DeviceAdapter'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'next-i18next'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Accordion, Box, IconButton, useToast } from '@chakra-ui/react'
import { useMobile } from '@/hooks/useMobile'
import {
  EditFiPropertyData,
  FIOwnershipTypeEnum,
  FIRealtyAvailabilityCodeEnum,
  FIResidentialPropertyBalconyTypeCodeEnum,
  FITypeCodeEnum,
} from '@/modules/fi-properties'
import { updateFIProperty } from '@/modules/fi-properties/queries/queryFIProperties'
import { EditFiPropertySchema } from './EditForm/schema'
import HousingCompany from './EditForm/HousingCompany'
import Descriptions from './EditForm/Description'
import PriceAndCost from './EditForm/PriceAndCost'
import SpaceAndMaterial from './EditForm/SpaceAndMaterial'
import Plot from './EditForm/Plot'
import AdditionalDetails from './EditForm/AdditionalDetails'
import BasicInformation from './EditForm/BasicInformation'
import { omit, uniqBy } from 'lodash'
import {
  DescriptionsRead,
  DescriptionType,
  LanguageCodeEnum,
} from '@/types/property'
import SectionWrapper from '../PropertyPage/components/SectionWrapper'
import LeftPanel from './LeftPanel'
import { EditIcon } from '@chakra-ui/icons'
import {
  BASIC_INFORMATION_SUB_SECTIONS,
  DEFECTS_AND_DAMAGES_SUB_SECTIONS,
  PROPERTY_SECTIONS,
  subPropertySections,
} from './EditFiPropertyModal.util'
import ServiceAndTransportation from './EditForm/ServicesAndTransportation'
import { useDebounceState } from '@/hooks/useDebouceState'

export const DEFAULT_CURRENCY = 'EUR'

interface Props {
  isOpen?: boolean
  onClose: (updateSuccess?: boolean) => void
  propertyReference: string
  showEditButton?: boolean
}

const sections = Object.values(PROPERTY_SECTIONS)

const getRoomsValue = <
  T extends { typeCode: FITypeCodeEnum | null | undefined | string }
>(
  values?: T[],
  key?: FITypeCodeEnum
): T[] => {
  return values?.filter((item) => item.typeCode === key) || []
}
const defaultDescription: DescriptionsRead[] = [
  LanguageCodeEnum.FI,
  LanguageCodeEnum.SV,
  LanguageCodeEnum.EN,
].map((item) => ({
  type: DescriptionType.FULL,
  language: item,
  description: '',
  tagline: '',
}))

const EditFiProperty = ({
  isOpen = true,
  onClose,
  propertyReference,
  showEditButton = false,
}: Props) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const toast = useToast()
  const isMobile = useMobile('lg')
  const queryClient = useQueryClient()
  const data = queryClient.getQueryData<EditFiPropertyData>([
    'property',
    propertyReference,
  ])

  const [subSection, setSubSection] = useDebounceState(
    BASIC_INFORMATION_SUB_SECTIONS.OVERVIEW,
    100
  )
  const [activeSection, setActiveSection] = useState(sections.at(0))

  const { t } = useTranslation(['common'])

  const handleScroll = useCallback(() => {
    let minTop = Infinity
    if (containerRef.current) {
      const section = containerRef.current.querySelectorAll<HTMLElement>(
        `[data-id="${activeSection}"]`
      )
      const navElements = section?.item(0)?.querySelectorAll('.sub-section')
      const arr = Array.from(navElements)

      arr.forEach((navElement) => {
        const rect = navElement.getBoundingClientRect()

        if (rect.top >= 0 && rect.top < minTop) {
          minTop = rect.top
          setSubSection(navElement.id)
        }
      })
    }
  }, [activeSection, setSubSection])

  useEffect(() => {
    const currentContainer = containerRef.current
    if (!isMobile && currentContainer) {
      currentContainer.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (currentContainer) {
        currentContainer.removeEventListener('scroll', handleScroll)
      }
    }
  }, [isMobile, handleScroll])
  const [formIsOpen, setFormIsOpen] = useState(isOpen)

  const schema = EditFiPropertySchema(t)

  const { mutate: mutationEditFiProperty, isPending: isCreating } = useMutation(
    {
      mutationKey: ['fi-property', propertyReference],
      mutationFn: updateFIProperty,
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ['property', propertyReference],
        })
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('editProperty.updatedTitle'),
          description: `${t(
            'editProperty.updatedDescription'
          )} ${propertyReference}`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
        setFormIsOpen(false)
        onClose(true)
      },
      onError: () => {
        toast({
          position: isMobile ? 'top' : 'bottom-left',
          title: t('errors.title'),
          description: t('errors.missingRequiredInformation'),
          status: 'error',
          duration: 9000,
          isClosable: true,
        })
      },
    }
  )

  const handleEditFiProperty = async (values: EditFiPropertyData) => {
    const { spaces, fiPlotsOverview, ...rest } = values

    const {
      kitchenAndDiningRoom,
      workRoomAndStudy,
      areaHalls,
      terraceBalconyAndYard,
      ...restSpace
    } = spaces
    mutationEditFiProperty({
      reference: propertyReference,

      propertyData: {
        ...rest,
        fiPlotOverview: fiPlotsOverview[0] || null,
        spacesAndMaterials: {
          ...restSpace,
          kitchen: getRoomsValue(kitchenAndDiningRoom, FITypeCodeEnum.KITCHEN),
          diningRoom: getRoomsValue(
            kitchenAndDiningRoom,
            FITypeCodeEnum.DINING_ROOM
          ),
          study: getRoomsValue(workRoomAndStudy, FITypeCodeEnum.STUDY),
          library: getRoomsValue(workRoomAndStudy, FITypeCodeEnum.LIBRARY),
          hall: getRoomsValue(areaHalls, FITypeCodeEnum.HALL),
          terrace: getRoomsValue(terraceBalconyAndYard, FITypeCodeEnum.TERRACE),
          balcony: getRoomsValue(
            terraceBalconyAndYard,
            FITypeCodeEnum.BALCONY
          ).map((item) => ({
            ...item,
            typeCode: item.balconyTypeCodes as unknown as FITypeCodeEnum,
            balconyTypeCodes: undefined,
          })),
          yard: getRoomsValue(terraceBalconyAndYard, FITypeCodeEnum.YARD),
          hallway: getRoomsValue(areaHalls, FITypeCodeEnum.HALLWAY),
          draughtLobby: getRoomsValue(areaHalls, FITypeCodeEnum.DRAUGHT_LOBBY),
        },
      },
    })
  }

  const scrollToErrorFields = (reValidate?: () => void) => {
    const errorElement = containerRef.current?.querySelector(
      "[class*='chakra-form__error-message']"
    )
    if (errorElement) {
      toast({
        position: isMobile ? 'top' : 'bottom-left',
        title: t('errors.missingRequiredInformation'),
        description: t('errors.pleaseFillRequiredFields'),
        status: 'error',
        duration: 9000,
        isClosable: true,
      })
      const section = errorElement.closest<HTMLElement>('[data-id]')
      const sectionError = section?.getAttribute('data-id') as PROPERTY_SECTIONS
      sectionError && setActiveSection(sectionError)
      setTimeout(() => {
        errorElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
      }, 300)
    } else {
      reValidate?.()
    }
  }

  const acceptTitle = t('save')

  const cancelTitle = t('cancel')

  const accordionIndex = activeSection && sections.indexOf(activeSection)
  const handleAccordionChange = (index: number) => {
    setActiveSection(sections[index])
  }

  const goToSection = (id: PROPERTY_SECTIONS) => {
    setActiveSection(id)
    setSubSection(subPropertySections[id][0])
    containerRef.current?.scrollTo(0, 0)
  }

  const handleChangeSubSection = (id: string) => {
    const element = containerRef.current?.querySelector<HTMLElement>(
      `[id="${id}"]`
    )

    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })
    }
  }

  const getListError = () => {
    const result = Array.from(
      containerRef.current?.children.item(0)?.children || []
    )
      .filter((item) => {
        const error = item.getElementsByClassName(
          'chakra-form__error-message'
        )?.length

        return !!error
      })
      .map((item) => item.getAttribute('data-id'))

    return result as string[]
  }

  if (data) {
    const restSpace = omit(data.spacesAndMaterials, [
      'kitchen',
      'diningRoom',
      'terrace',
      'balcony',
      'yard',
      'hall',
      'hallway',
      'study',
      'library',
    ])

    const defaultValue: EditFiPropertyData = {
      ...data,
      realtorUserIds: data?.realtorUsers.map((item) => item.id) || [],
      contactIds: data?.contacts.map((item) => item.id) || [],
      fiPlotsOverview: data?.fiPlotOverview ? [data?.fiPlotOverview] : [],
      spaces: {
        kitchenAndDiningRoom:
          data?.spacesAndMaterials.kitchen.concat(
            data?.spacesAndMaterials.diningRoom
          ) || [],
        terraceBalconyAndYard: (data?.spacesAndMaterials.terrace || [])
          .map((item) => ({ ...item, typeCode: FITypeCodeEnum.TERRACE }))
          .concat(
            (data?.spacesAndMaterials?.balcony || []).map((item) => ({
              ...item,
              balconyTypeCodes:
                item.typeCode as unknown as FIResidentialPropertyBalconyTypeCodeEnum,
              typeCode: FITypeCodeEnum.BALCONY,
            })),
            (data.spacesAndMaterials?.yard || []).map((item) => ({
              ...item,
              typeCode: FITypeCodeEnum.YARD,
            }))
          ),
        workRoomAndStudy: data?.spacesAndMaterials.study.concat(
          data?.spacesAndMaterials.library
        ),
        areaHalls:
          data?.spacesAndMaterials.hall.concat(
            data?.spacesAndMaterials.hallway,
            data?.spacesAndMaterials?.draughtLobby
          ) || [],
        ...restSpace,
      },
      fiHousingCompany:
        data.fiPropertyType.ownershipType === FIOwnershipTypeEnum.SHARE
          ? {
              ...(data.fiHousingCompany || {}),
              businessId: data?.fiHousingCompany?.businessId || '',
              name: data?.fiHousingCompany?.name || '',
            }
          : undefined,
      descriptions: uniqBy(
        [...(data?.descriptions || []), ...defaultDescription],
        'language'
      ),
      realtorUsers: data?.realtorUsers || [],
      reference: propertyReference || data?.reference || '',
      internalNote: data?.internalNote || '',
    }

    return (
      <>
        {showEditButton && (
          <IconButton
            aria-label="Click here to open modal for editing property details"
            variant="transparent"
            border="none"
            size={['xlg']}
            icon={<EditIcon />}
            onClick={() => setFormIsOpen(true)}
            height="auto"
          />
        )}
        <Formik
          initialValues={defaultValue}
          onSubmit={handleEditFiProperty}
          validationSchema={schema}
          validateOnChange={false}
          validateOnBlur={false}
        >
          {({ submitForm, isValid, isSubmitting, values, dirty }) => {
            const handleClickAccept = () => {
              if (isValid) {
                submitForm()
                setTimeout(() => {
                  scrollToErrorFields()
                }, 800)
                return
              }

              scrollToErrorFields(submitForm)
            }

            const displaySectionItem = (item) => {
              return item === PROPERTY_SECTIONS.HOUSING_COMPANY &&
                values.fiPropertyType.ownershipType ===
                  FIOwnershipTypeEnum.PROPERTY
                ? 'none'
                : 'flex'
            }

            const displaySubSectionItem = (item, sub) => {
              if (
                item === PROPERTY_SECTIONS.BASIC_INFORMATION &&
                sub === BASIC_INFORMATION_SUB_SECTIONS.RENT_DETAIL &&
                values.fiRealty.availability?.code !==
                  FIRealtyAvailabilityCodeEnum.RENTED
              )
                return 'none'
              if (
                item === PROPERTY_SECTIONS.BASIC_INFORMATION &&
                values.fiPropertyType.ownershipType ===
                  FIOwnershipTypeEnum.PROPERTY &&
                [
                  BASIC_INFORMATION_SUB_SECTIONS.REDEMPTION,
                  BASIC_INFORMATION_SUB_SECTIONS.ADMINISTRATION,
                ].includes(sub)
              )
                return 'none'
              if (
                item === PROPERTY_SECTIONS.DEFECTS_DAMAGES &&
                values.fiPropertyType.ownershipType ===
                  FIOwnershipTypeEnum.PROPERTY &&
                [
                  DEFECTS_AND_DAMAGES_SUB_SECTIONS.INSPECTIONS,
                  DEFECTS_AND_DAMAGES_SUB_SECTIONS.ASBESTOS_MAPPING,
                ].includes(sub)
              )
                return 'none'
              return 'block'
            }

            return (
              <DeviceAdapter
                noPadding
                onlyDrawer
                size="full"
                isOpen={formIsOpen}
                onAccept={handleClickAccept}
                onClose={onClose}
                onReject={onClose}
                isLoading={isCreating || isSubmitting}
                title={`${t('edit')} ${data.reference.toUpperCase()}`}
                acceptTitle={acceptTitle}
                cancelTitle={cancelTitle}
                disableAcceptButton={isSubmitting}
                dirty={dirty}
                isSubmitting={isSubmitting}
              >
                <Box
                  h="full"
                  display={['block', null, null, 'flex']}
                  overflow="unset"
                  backgroundColor="primary.main"
                >
                  <LeftPanel
                    activeSection={activeSection}
                    displaySectionItem={displaySectionItem}
                    displaySubSectionItem={displaySubSectionItem}
                    goToSection={goToSection}
                    sections={sections}
                    getListSectionErrors={getListError}
                    setSubSection={handleChangeSubSection}
                    subSection={subSection}
                  />

                  <Box
                    position="relative"
                    w="100%"
                    overflow="scroll"
                    ref={containerRef}
                    id="edit-fi-property-container"
                  >
                    <Accordion
                      index={
                        typeof accordionIndex === 'number'
                          ? [accordionIndex]
                          : []
                      }
                      allowToggle
                      onChange={handleAccordionChange}
                      display="flex"
                      flexDirection="column"
                      bg={{ base: 'primary.main', lg: 'white' }}
                      gap={{ base: 2, lg: 0 }}
                      h="full"
                    >
                      <SectionWrapper
                        id={PROPERTY_SECTIONS.BASIC_INFORMATION}
                        title="editProperty.generalInformation"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.BASIC_INFORMATION
                        }
                      >
                        <BasicInformation />
                      </SectionWrapper>

                      <SectionWrapper
                        id={PROPERTY_SECTIONS.DESCRIPTIONS}
                        title="fiProperty.sections.descriptions"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.DESCRIPTIONS
                        }
                      >
                        <Descriptions />
                      </SectionWrapper>

                      <SectionWrapper
                        id={PROPERTY_SECTIONS.PRICES_AND_COSTS}
                        title="fiProperty.sections.prices_and_costs"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.PRICES_AND_COSTS
                        }
                      >
                        <PriceAndCost />
                      </SectionWrapper>

                      <SectionWrapper
                        id={PROPERTY_SECTIONS.DEFECTS_DAMAGES}
                        title="fiProperty.sections.defects_damages"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.DEFECTS_DAMAGES
                        }
                      >
                        <AdditionalDetails />
                      </SectionWrapper>

                      <SectionWrapper
                        conditions={
                          values.fiPropertyType.ownershipType !==
                          FIOwnershipTypeEnum.PROPERTY
                        }
                        id={PROPERTY_SECTIONS.HOUSING_COMPANY}
                        title="fiProperty.sections.housing_company"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.HOUSING_COMPANY
                        }
                      >
                        <HousingCompany />
                      </SectionWrapper>

                      <SectionWrapper
                        id={PROPERTY_SECTIONS.PLOT}
                        title="fiProperty.sections.plot"
                        isActive={activeSection === PROPERTY_SECTIONS.PLOT}
                      >
                        <Plot name="fiPlotsOverview" />
                      </SectionWrapper>

                      <SectionWrapper
                        id={PROPERTY_SECTIONS.SPACES_MATERIALS}
                        title="fiProperty.sections.spaces_materials"
                        isActive={
                          activeSection === PROPERTY_SECTIONS.SPACES_MATERIALS
                        }
                      >
                        <SpaceAndMaterial />
                      </SectionWrapper>
                      <SectionWrapper
                        id={PROPERTY_SECTIONS.SERVICES_TRANSPORTATION}
                        title="fiProperty.sections.services_transportation"
                        isActive={
                          activeSection ===
                          PROPERTY_SECTIONS.SERVICES_TRANSPORTATION
                        }
                      >
                        <ServiceAndTransportation />
                      </SectionWrapper>
                    </Accordion>
                  </Box>
                </Box>
              </DeviceAdapter>
            )
          }}
        </Formik>
      </>
    )
  }
  return null
}

export default EditFiProperty
