import {
  EditFiPropertyData,
  FIApartmentRoom,
  FIBathRoomFeatureCode,
  FICeilingSurfaceMaterialCodeEnum,
  FIFloorSurfaceMaterialCodeEnum,
  FIKitchenFeatureCode,
  FILivingRoomFeatureCode,
  FISaunaFeatureCode,
  FIToiletFeatureCode,
  FITypeCodeEnum,
  FIWallSurfaceMaterialCodeEnum,
} from '@/modules/fi-properties'
import {
  Button,
  Flex,
  FormLabel,
  Grid,
  GridItem,
  Stack,
} from '@chakra-ui/react'
import { FieldArray, useFormikContext } from 'formik'
import { get } from 'lodash'
import TextareaWithMultiLanguage from '@/components/TextAreaMultiLangue'
import BadgeGroupField from '@/components/Badge/BadgeGroupField'
import { useTranslation } from 'next-i18next'
import LabeledField from '@/components/Form/LabeledField'
import TitleWithDivider from '@/components/TitleWithDivider'
import CardItem from '@/components/PropertyPage/components/CardItem'
import { LanguageCodeEnum } from '@/types/property'
import { useCallback } from 'react'

const initFeatureDescription = {
  featureCode: null,
  description: [
    {
      languageCode: LanguageCodeEnum.FI,
      text: '',
    },
  ],
}

const SpaceItem = ({
  id,
  name,
  header,
  typeCodes,
  newItem,
  children,
}: {
  id: string
  name: string
  header: string
  newItem: (value: FIApartmentRoom['typeCode']) => unknown
  typeCodes: FIApartmentRoom['typeCode'][]
  children?: React.ReactNode
}) => {
  const { values, setFieldValue } = useFormikContext<EditFiPropertyData>()
  const { t } = useTranslation(['common'])
  const spaces: FIApartmentRoom[] = get(values, name) || []
  const featureCodes = useCallback(
    (type: string) => {
      const enums: Record<string, Record<string, string>> = {
        [FITypeCodeEnum.BATH_ROOM]: FIBathRoomFeatureCode,
        [FITypeCodeEnum.KITCHEN]: FIKitchenFeatureCode,
        [FITypeCodeEnum.LIVING_ROOM]: FILivingRoomFeatureCode,
        [FITypeCodeEnum.SAUNA]: FISaunaFeatureCode,
        [FITypeCodeEnum.TOILET]: FIToiletFeatureCode,
      }
      const option = Object.values(enums[type] || {}).map((value) => ({
        label: t(`fiProperty.enums.featureCode.${value}`),
        value,
      }))
      return option
    },
    [t]
  )

  const orderedFloorMaterialList: FIFloorSurfaceMaterialCodeEnum[] = [
    FIFloorSurfaceMaterialCodeEnum.LAMINATE,
    FIFloorSurfaceMaterialCodeEnum.PARQUET,
    FIFloorSurfaceMaterialCodeEnum.PLASTIC,
    FIFloorSurfaceMaterialCodeEnum.TILED,
    FIFloorSurfaceMaterialCodeEnum.BOARD,
    FIFloorSurfaceMaterialCodeEnum.STONE,
    FIFloorSurfaceMaterialCodeEnum.VINYL,
    FIFloorSurfaceMaterialCodeEnum.VINYL_CORK,
    FIFloorSurfaceMaterialCodeEnum.CONCRETE,
    FIFloorSurfaceMaterialCodeEnum.WALL_TO_WALL_CARPET,
    FIFloorSurfaceMaterialCodeEnum.CORK,
    FIFloorSurfaceMaterialCodeEnum.MICROCEMENT,
    FIFloorSurfaceMaterialCodeEnum.ACRYLIC_MASS,
    FIFloorSurfaceMaterialCodeEnum.OTHER,
  ]

  const orderedWallMaterialList: FIWallSurfaceMaterialCodeEnum[] = [
    FIWallSurfaceMaterialCodeEnum.PAINT,
    FIWallSurfaceMaterialCodeEnum.WALLPAPER,
    FIWallSurfaceMaterialCodeEnum.GLASS_FIBRE_TEXTILE_COVERED,
    FIWallSurfaceMaterialCodeEnum.PANEL,
    FIWallSurfaceMaterialCodeEnum.WAINSCOT,
    FIWallSurfaceMaterialCodeEnum.WOOD,
    FIWallSurfaceMaterialCodeEnum.CERAMIC_TILE,
    FIWallSurfaceMaterialCodeEnum.PARTIALLY_TILED,
    FIWallSurfaceMaterialCodeEnum.LOG,
    FIWallSurfaceMaterialCodeEnum.CONCRETE,
    FIWallSurfaceMaterialCodeEnum.MICROCEMENT,
    FIWallSurfaceMaterialCodeEnum.STONE,
    FIWallSurfaceMaterialCodeEnum.GLASS,
    FIWallSurfaceMaterialCodeEnum.PLASTIC,
    FIWallSurfaceMaterialCodeEnum.OTHER,
  ]
  return (
    <FieldArray
      name={name}
      render={({ push, remove }) => (
        <Stack id={id} className="sub-section">
          <TitleWithDivider title={header} />
          {children}
          {spaces.map((space, index) => {
            const wallMaterialCodes: string[] =
              get(values, `${name}.${index}.wallMaterialCodes`) || []
            const floorMaterialCodes: string[] =
              get(values, `${name}.${index}.floorMaterialCodes`) || []
            const ceilingMaterialCodes: string[] =
              get(values, `${name}.${index}.ceilingMaterialCodes`) || []

            return (
              <CardItem
                key={index}
                title={t(`fiProperty.enums.space.${space.typeCode}`)}
                onRemove={() => remove(index)}
              >
                <Stack>
                  <LabeledField label={t('fiProperty.fields.roomDescription')}>
                    <TextareaWithMultiLanguage
                      name={`${name}.${index}.description`}
                    />
                  </LabeledField>
                  {!!featureCodes(space.typeCode).length && (
                    <FieldArray
                      name={`${name}.${index}.features`}
                      render={({
                        push: addFeatureDescription,
                        remove: removeFeature,
                      }) => (
                        <Stack>
                          {space.features?.map((feature, featureIndex) => {
                            const featureCodeSelected: string[] =
                              feature.featureCodes || []
                            return (
                              <CardItem
                                title={t(
                                  'fiProperty.sections.featureDescription'
                                )}
                                key={featureIndex}
                                onRemove={() => removeFeature(index)}
                              >
                                <Grid templateColumns="repeat(2, 1fr)" gap="4">
                                  <GridItem colSpan={{ base: 2 }}>
                                    <LabeledField
                                      label={t('fiProperty.fields.featureName')}
                                    >
                                      <BadgeGroupField
                                        name={`${name}.${index}.features.${featureIndex}.featureCodes`}
                                        items={featureCodes(space.typeCode)}
                                        itemValues={(item) => item.value}
                                        itemLabels={(item) => item.label}
                                        type="array"
                                        isItemChecked={(value: string) =>
                                          featureCodeSelected.includes(value)
                                        }
                                        onValueChanged={(value) => {
                                          const newValue = [
                                            ...featureCodeSelected,
                                            value,
                                          ].filter(
                                            (item, index, self) =>
                                              self.indexOf(item) ===
                                              self.lastIndexOf(item)
                                          )
                                          setFieldValue(
                                            `${name}.${index}.features.${featureIndex}.featureCodes`,
                                            newValue
                                          )
                                        }}
                                      />
                                    </LabeledField>
                                  </GridItem>
                                  <GridItem colSpan={2}>
                                    <TextareaWithMultiLanguage
                                      label={t('fiProperty.fields.description')}
                                      name={`${name}.${index}.features.${featureIndex}.description`}
                                    />
                                  </GridItem>
                                </Grid>
                              </CardItem>
                            )
                          })}
                          <Flex justifyContent="end">
                            <Button
                              variant="transparent"
                              bg="white"
                              colorScheme="black"
                              onClick={() =>
                                addFeatureDescription(initFeatureDescription)
                              }
                            >
                              + {t(`fiProperty.add.featureDescription`)}
                            </Button>
                          </Flex>
                        </Stack>
                      )}
                    />
                  )}
                  <BadgeGroupField
                    name={`${name}.${index}.wallMaterialCodes`}
                    label={
                      <FormLabel mb="2">
                        {t('fiProperty.fields.wallMaterial')}
                      </FormLabel>
                    }
                    emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                    type="array"
                    items={orderedWallMaterialList}
                    itemLabels={(item) =>
                      t(`fiProperty.enums.wallSurfaceMaterial.${item}`)
                    }
                    isItemChecked={(value: string) =>
                      wallMaterialCodes.includes(value)
                    }
                    onValueChanged={(value) => {
                      const newValue = [...wallMaterialCodes, value].filter(
                        (item, index, self) =>
                          self.indexOf(item) === self.lastIndexOf(item)
                      )
                      setFieldValue(
                        `${name}.${index}.wallMaterialCodes`,
                        newValue
                      )
                    }}
                  />
                  <BadgeGroupField
                    name={`${name}.${index}.floorMaterialCodes`}
                    label={
                      <FormLabel mb="2">
                        {t('fiProperty.fields.floorMaterial')}
                      </FormLabel>
                    }
                    emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                    type="array"
                    items={orderedFloorMaterialList}
                    itemLabels={(key) =>
                      t(`fiProperty.enums.floorSurfaceMaterial.${key}`)
                    }
                    isItemChecked={(value: string) =>
                      floorMaterialCodes.includes(value)
                    }
                    onValueChanged={(value) => {
                      const newValue = [...floorMaterialCodes, value].filter(
                        (item, index, self) =>
                          self.indexOf(item) === self.lastIndexOf(item)
                      )

                      setFieldValue(
                        `${name}.${index}.floorMaterialCodes`,
                        newValue
                      )
                    }}
                  />
                  <BadgeGroupField
                    name={`${name}.${index}.ceilingMaterialCodes`}
                    label={
                      <FormLabel mb="2">
                        {t('fiProperty.fields.ceilingMaterial')}
                      </FormLabel>
                    }
                    emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                    type="enum"
                    items={Object.entries(FICeilingSurfaceMaterialCodeEnum)}
                    itemLabels={([key]) =>
                      t(`fiProperty.enums.ceilingSurfaceMaterial.${key}`)
                    }
                    isItemChecked={(value: string) =>
                      ceilingMaterialCodes.includes(value)
                    }
                    onValueChanged={(value) => {
                      const newValue = [...ceilingMaterialCodes, value].filter(
                        (item, index, self) =>
                          self.indexOf(item) === self.lastIndexOf(item)
                      )
                      setFieldValue(
                        `${name}.${index}.ceilingMaterialCodes`,
                        newValue
                      )
                    }}
                  />
                </Stack>
              </CardItem>
            )
          })}
          <Flex gap="2" flexWrap="wrap">
            {typeCodes.map((typeCode) => (
              <Button
                key={typeCode}
                variant="transparent"
                bg="white"
                colorScheme="black"
                onClick={() => push(newItem(typeCode))}
              >
                + {t(`fiProperty.add.${typeCode}`)}
              </Button>
            ))}
          </Flex>
        </Stack>
      )}
    />
  )
}

export default SpaceItem
