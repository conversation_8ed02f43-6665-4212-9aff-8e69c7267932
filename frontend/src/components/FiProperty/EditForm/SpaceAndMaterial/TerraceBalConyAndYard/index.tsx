import {
  EditFiPropertyData,
  FIApartmentRoom,
  FICompassPointEnum,
  FIOwnershipTypeEnum,
  FIResidentialPropertyBalconyTypeCodeEnum,
  FITypeCodeEnum,
} from '@/modules/fi-properties'
import {
  Button,
  Flex,
  FormLabel,
  SimpleGrid,
  Stack,
  Text,
} from '@chakra-ui/react'
import { FieldArray, useFormikContext } from 'formik'
import { findLastIndex, get } from 'lodash'
import TextareaWithMultiLanguage from '@/components/TextAreaMultiLangue'
import BadgeGroupField from '@/components/Badge/BadgeGroupField'
import { useTranslation } from 'next-i18next'
import LabeledField from '@/components/Form/LabeledField'
import TitleWithDivider from '@/components/TitleWithDivider'
import { MultiSelectDropdown } from '@/components/DropdownMenu/MultiSelectDropdown'
import { SimpleDropdown } from '@/components/DropdownMenu/SimpleDropdown'
import {
  FIBalconyGlassMaintenanceResponsibilityCodeEnum,
  FIBasisForPossessionCodeYardEnum,
} from '@/modules/fi-properties/types/FIResidentialShare'
import CardItem from '@/components/PropertyPage/components/CardItem'
import {
  COMMON_ENUM_ITEMS,
  SPACES_MATERIALS_SUB_SECTIONS,
} from '@/components/FiProperty/EditFiPropertyModal.util'

const TerraceBalConyAndPatioItem = ({
  name,
  codeType,
  isLastBalcony,
}: {
  name: string
  codeType: FITypeCodeEnum
  isLastBalcony?: boolean
}) => {
  const { t } = useTranslation(['common'])
  const { values } = useFormikContext<EditFiPropertyData>()

  const compassPoints = Object.entries(FICompassPointEnum).map(
    ([key, value]) => ({
      value,
      label: t(`fiProperty.enums.compassPoint.${key}`),
    })
  )

  const balconyTypeCodes = Object.entries(
    FIResidentialPropertyBalconyTypeCodeEnum
  ).map(([key, value]) => ({
    value,
    label: t(`fiProperty.enums.balconyType.${key}`),
  }))

  const glassMaintenanceResponsibilityOptions = Object.entries(
    FIBalconyGlassMaintenanceResponsibilityCodeEnum
  ).map(([key, value]) => ({
    value,
    label: t(`fiProperty.enums.balconyGlassMaintenanceResponsibility.${key}`),
  }))

  const basisForPossessionCodes = Object.entries(
    FIBasisForPossessionCodeYardEnum
  ).map(([key, value]) => ({
    value,
    label: t(`fiProperty.enums.basisForPossessionCodeYard.${key}`),
  }))

  const labelDescription: Record<string, string> = {
    [FITypeCodeEnum.TERRACE]: 'fiProperty.fields.terraceDescription',
    [FITypeCodeEnum.BALCONY]: 'fiProperty.fields.balconyDescription',
    [FITypeCodeEnum.YARD]: 'fiProperty.fields.yardDescription',
  }

  const residentialType =
    values.fiPropertyType?.ownershipType === FIOwnershipTypeEnum.SHARE
      ? 'fiResidentialShareOverview'
      : 'fiResidentialPropertyOverview'
  return (
    <Stack gap={4}>
      {codeType === FITypeCodeEnum.TERRACE && (
        <BadgeGroupField
          toggle
          label={
            <FormLabel mb="2">{t('fiProperty.fields.glazedTerrace')}</FormLabel>
          }
          name={`${name}.isGlazedTerrace`}
          type="array"
          items={COMMON_ENUM_ITEMS}
          itemLabels={(value) => t(`${value.toLocaleLowerCase()}`)}
        />
      )}
      {codeType === FITypeCodeEnum.BALCONY && (
        <>
          <SimpleGrid columns={{ base: 1, lg: 2 }} gap="4">
            <LabeledField label={t('fiProperty.fields.balconyType')}>
              <SimpleDropdown
                name={`${name}.balconyTypeCode`}
                data={balconyTypeCodes}
                searchable={false}
              />
            </LabeledField>
          </SimpleGrid>
          <TextareaWithMultiLanguage
            label={t('fiProperty.fields.otherTypeDescription')}
            name={`${name}.typeOtherDescription`}
          />
        </>
      )}
      {codeType === FITypeCodeEnum.YARD && (
        <>
          <BadgeGroupField
            toggle
            label={
              <FormLabel mb="2">
                {t('fiProperty.fields.hasPrivateYard')}
              </FormLabel>
            }
            name={`${name}.hasPrivateYard`}
            type="array"
            items={COMMON_ENUM_ITEMS}
            itemLabels={(value) => t(`${value.toLocaleLowerCase()}`)}
          />
          <SimpleGrid columns={{ base: 1, lg: 2 }} gap="4">
            <LabeledField label={t('fiProperty.fields.basisForPossession')}>
              <SimpleDropdown
                name={`${name}.basisForPossessionCode`}
                data={basisForPossessionCodes}
                searchable={false}
              />
            </LabeledField>
          </SimpleGrid>
          <TextareaWithMultiLanguage
            label={t('fiProperty.fields.basisForPossessionDescription')}
            name={`${name}.basisForPossessionOtherDescription`}
          />
        </>
      )}
      <TextareaWithMultiLanguage
        label={t(labelDescription[codeType])}
        name={`${name}.description`}
      />
      {[FITypeCodeEnum.TERRACE, FITypeCodeEnum.BALCONY].includes(codeType) && (
        <SimpleGrid columns={{ base: 1, lg: 2 }} gap="4">
          <LabeledField label={t('fiProperty.fields.compassPoint')}>
            <MultiSelectDropdown
              name={`${name}.compassPoints`}
              data={compassPoints}
              searchable={false}
            />
          </LabeledField>
          <LabeledField
            label={t('fiProperty.fields.glassMaintenanceResponsibility')}
          >
            <SimpleDropdown
              name={`${name}.${
                codeType === FITypeCodeEnum.BALCONY
                  ? 'balconyGlassMaintenanceResponsibilityCode'
                  : 'terraceGlassMaintenanceResponsibilityCode'
              }`}
              data={glassMaintenanceResponsibilityOptions}
              searchable={false}
            />
          </LabeledField>
        </SimpleGrid>
      )}
      {isLastBalcony && (
        <TextareaWithMultiLanguage
          label={t('fiProperty.fields.viewDescription')}
          name={`${residentialType}.apartment.viewDescription`}
        />
      )}
    </Stack>
  )
}

const TerraceBalConyAndYard = ({
  newItem,
}: {
  newItem: (value: FIApartmentRoom['typeCode']) => unknown
}) => {
  const { values } = useFormikContext<EditFiPropertyData>()
  const { t } = useTranslation(['common'])
  const spaces = get(values, 'spaces.terraceBalconyAndYard') || []
  const typeCodes = [
    FITypeCodeEnum.TERRACE,
    FITypeCodeEnum.BALCONY,
    FITypeCodeEnum.YARD,
  ]

  const residentialType =
    values.fiPropertyType?.ownershipType === FIOwnershipTypeEnum.SHARE
      ? 'fiResidentialShareOverview'
      : 'fiResidentialPropertyOverview'

  return (
    <FieldArray
      name={'spaces.terraceBalconyAndYard'}
      render={({ push, remove }) => (
        <Stack
          id={SPACES_MATERIALS_SUB_SECTIONS.TERRACE_BALCONY_AND_YARD}
          className="sub-section"
        >
          <TitleWithDivider
            title={'fiProperty.sections.balcony_terrace_yard'}
          />
          <BadgeGroupField
            toggle
            label={
              <FormLabel mb="2">{t('fiProperty.fields.hasBalcony')}</FormLabel>
            }
            name={`${residentialType}.apartment.hasBalcony`}
            type="array"
            items={COMMON_ENUM_ITEMS}
            itemLabels={(value) => t(`${value.toLocaleLowerCase()}`)}
          />
          <BadgeGroupField
            toggle
            label={
              <FormLabel mb="2">{t('fiProperty.fields.hasTerrace')}</FormLabel>
            }
            name={`${residentialType}.apartment.hasTerrace`}
            type="array"
            items={COMMON_ENUM_ITEMS}
            itemLabels={(value) => t(`${value.toLocaleLowerCase()}`)}
          />
          {spaces.map((space, index, arr) => {
            const isLastBalcony =
              index ===
              findLastIndex(
                arr,
                (item) => item.typeCode === FITypeCodeEnum.BALCONY
              )
            return (
              <CardItem
                key={index}
                title={t(`fiProperty.enums.space.${space.typeCode}`)}
                onRemove={() => remove(index)}
              >
                <TerraceBalConyAndPatioItem
                  name={`spaces.terraceBalconyAndYard.${index}`}
                  codeType={space.typeCode || FITypeCodeEnum.TERRACE}
                  isLastBalcony={isLastBalcony}
                />
              </CardItem>
            )
          })}
          <Flex gap={2}>
            {typeCodes.map((typeCode) => (
              <Button
                key={typeCode}
                variant="transparent"
                bg="white"
                mr="2"
                colorScheme="black"
                onClick={() => push(newItem(typeCode))}
              >
                + {t(`fiProperty.add.${typeCode}`)}
              </Button>
            ))}
          </Flex>
          <Text fontSize="medium" fontWeight="bold">
            {t('fiProperty.sections.patio')}
          </Text>
          <TextareaWithMultiLanguage
            name="spaces.patio"
            label={t('fiProperty.fields.patioDescription')}
          />
        </Stack>
      )}
    />
  )
}

export default TerraceBalConyAndYard
