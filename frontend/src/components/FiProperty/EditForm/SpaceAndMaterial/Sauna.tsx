import {
  EditFiPropertyData,
  FIApartmentRoom,
  FIBathRoomFeatureCode,
  FICeilingSurfaceMaterialCodeEnum,
  FIChoiceEnum,
  FIFloorSurfaceMaterialCodeEnum,
  FIKitchenFeatureCode,
  FILivingRoomFeatureCode,
  FIOwnershipTypeEnum,
  FISaunaFeatureCode,
  FIToiletFeatureCode,
  FITypeCodeEnum,
  FIWallSurfaceMaterialCodeEnum,
} from '@/modules/fi-properties'
import {
  Button,
  Flex,
  FormLabel,
  Grid,
  GridItem,
  Stack,
} from '@chakra-ui/react'
import { FieldArray, useFormikContext } from 'formik'
import { get } from 'lodash'
import TextareaWithMultiLanguage from '@/components/TextAreaMultiLangue'
import BadgeGroupField from '@/components/Badge/BadgeGroupField'
import { useTranslation } from 'next-i18next'
import LabeledField from '@/components/Form/LabeledField'
import TitleWithDivider from '@/components/TitleWithDivider'
import CardItem from '@/components/PropertyPage/components/CardItem'
import { LanguageCodeEnum } from '@/types/property'
import { useCallback } from 'react'
import {
  COMMON_ENUM_ITEMS,
  SPACES_MATERIALS_SUB_SECTIONS,
} from '../../EditFiPropertyModal.util'

const initFeatureDescription = {
  featureCode: null,
  description: [
    {
      languageCode: LanguageCodeEnum.FI,
      text: '',
    },
  ],
}

const SaunaItem = ({
  name,
  header,
  typeCodes,
  newItem,
}: {
  name: string
  header: string
  newItem: (value: FIApartmentRoom['typeCode']) => unknown
  typeCodes: FIApartmentRoom['typeCode'][]
}) => {
  const { values, setFieldValue } = useFormikContext<EditFiPropertyData>()
  const { t } = useTranslation(['common'])
  const spaces: FIApartmentRoom[] = get(values, name) || []
  const featureCodes = useCallback(
    (type: string) => {
      const enums: Record<string, Record<string, string>> = {
        [FITypeCodeEnum.BATH_ROOM]: FIBathRoomFeatureCode,
        [FITypeCodeEnum.KITCHEN]: FIKitchenFeatureCode,
        [FITypeCodeEnum.LIVING_ROOM]: FILivingRoomFeatureCode,
        [FITypeCodeEnum.SAUNA]: FISaunaFeatureCode,
        [FITypeCodeEnum.TOILET]: FIToiletFeatureCode,
      }
      const option = Object.values(enums[type] || {}).map((value) => ({
        label: t(`fiProperty.enums.featureCode.${value}`),
        value,
      }))
      return option
    },
    [t]
  )

  const floorMaterial = [
    FIFloorSurfaceMaterialCodeEnum.BOARD,
    FIFloorSurfaceMaterialCodeEnum.TILED,
    FIFloorSurfaceMaterialCodeEnum.STONE,
    FIFloorSurfaceMaterialCodeEnum.PLASTIC,
    FIFloorSurfaceMaterialCodeEnum.CONCRETE,
    FIFloorSurfaceMaterialCodeEnum.MICROCEMENT,
    FIFloorSurfaceMaterialCodeEnum.OTHER,
  ]

  const wallMaterials = [
    FIWallSurfaceMaterialCodeEnum.PANEL,
    FIWallSurfaceMaterialCodeEnum.PAINT,
    FIWallSurfaceMaterialCodeEnum.CONCRETE,
    FIWallSurfaceMaterialCodeEnum.WAINSCOT,
    FIWallSurfaceMaterialCodeEnum.WOOD,
    FIWallSurfaceMaterialCodeEnum.STONE,
    FIWallSurfaceMaterialCodeEnum.GLASS,
    FIWallSurfaceMaterialCodeEnum.MICROCEMENT,
    FIWallSurfaceMaterialCodeEnum.CERAMIC_TILE,
    FIWallSurfaceMaterialCodeEnum.PARTIALLY_TILED,
    FIWallSurfaceMaterialCodeEnum.LOG,
    FIWallSurfaceMaterialCodeEnum.OTHER,
  ]

  const ceilingMaterial = [
    FICeilingSurfaceMaterialCodeEnum.PANEL,
    FICeilingSurfaceMaterialCodeEnum.STONE,
    FICeilingSurfaceMaterialCodeEnum.PAINTED,
    FICeilingSurfaceMaterialCodeEnum.WOOD,
    FICeilingSurfaceMaterialCodeEnum.OTHER,
  ]
  const residentialType =
    values.fiPropertyType?.ownershipType === FIOwnershipTypeEnum.SHARE
      ? 'fiResidentialShareOverview'
      : 'fiResidentialPropertyOverview'

  const hasOwnSauna =
    get(values, `${residentialType}.apartment.hasOwnSauna`) === FIChoiceEnum.YES
  return (
    <FieldArray
      name={name}
      render={({ push, remove }) => (
        <Stack id={SPACES_MATERIALS_SUB_SECTIONS.SAUNA} className="sub-section">
          <TitleWithDivider title={header} />
          <BadgeGroupField
            toggle
            label={
              <FormLabel mb="2">{t('fiProperty.fields.hasSauna')}</FormLabel>
            }
            name={`${residentialType}.apartment.hasOwnSauna`}
            type="array"
            items={COMMON_ENUM_ITEMS}
            itemLabels={(value) => t(`${value.toLocaleLowerCase()}`)}
            onValueChanged={(value) => {
              if (value === FIChoiceEnum.NO) {
                setFieldValue(name, [])
              }
            }}
          />
          {hasOwnSauna && (
            <>
              {spaces.map((space, index) => {
                const wallMaterialCodes: string[] =
                  get(values, `${name}.${index}.wallMaterialCodes`) || []
                const floorMaterialCodes: string[] =
                  get(values, `${name}.${index}.floorMaterialCodes`) || []
                const ceilingMaterialCodes: string[] =
                  get(values, `${name}.${index}.ceilingMaterialCodes`) || []

                return (
                  <CardItem
                    key={index}
                    title={t(`fiProperty.enums.space.${space.typeCode}`)}
                    onRemove={() => remove(index)}
                  >
                    <Stack>
                      <LabeledField
                        label={t('fiProperty.fields.roomDescription')}
                      >
                        <TextareaWithMultiLanguage
                          name={`${name}.${index}.description`}
                        />
                      </LabeledField>
                      {!!featureCodes(space.typeCode).length && (
                        <FieldArray
                          name={`${name}.${index}.features`}
                          render={({
                            push: addFeatureDescription,
                            remove: removeFeature,
                          }) => (
                            <Stack>
                              {space.features?.map((feature, featureIndex) => {
                                const featureCodeSelected: string[] =
                                  feature.featureCodes || []
                                return (
                                  <CardItem
                                    title={t(
                                      'fiProperty.sections.featureDescription'
                                    )}
                                    key={featureIndex}
                                    onRemove={() => removeFeature(index)}
                                  >
                                    <Grid
                                      templateColumns="repeat(2, 1fr)"
                                      gap="4"
                                    >
                                      <GridItem colSpan={{ base: 2 }}>
                                        <LabeledField
                                          label={t(
                                            'fiProperty.fields.featureName'
                                          )}
                                        >
                                          <BadgeGroupField
                                            name={`${name}.${index}.features.${featureIndex}.featureCodes`}
                                            items={featureCodes(space.typeCode)}
                                            itemValues={(item) => item.value}
                                            itemLabels={(item) => item.label}
                                            type="array"
                                            isItemChecked={(value: string) =>
                                              featureCodeSelected.includes(
                                                value
                                              )
                                            }
                                            onValueChanged={(value) => {
                                              const newValue = [
                                                ...featureCodeSelected,
                                                value,
                                              ].filter(
                                                (item, index, self) =>
                                                  self.indexOf(item) ===
                                                  self.lastIndexOf(item)
                                              )
                                              setFieldValue(
                                                `${name}.${index}.features.${featureIndex}.featureCodes`,
                                                newValue
                                              )
                                            }}
                                          />
                                        </LabeledField>
                                      </GridItem>
                                      <GridItem colSpan={2}>
                                        <TextareaWithMultiLanguage
                                          label={t(
                                            'fiProperty.fields.description'
                                          )}
                                          name={`${name}.${index}.features.${featureIndex}.description`}
                                        />
                                      </GridItem>
                                    </Grid>
                                  </CardItem>
                                )
                              })}
                              <Flex justifyContent="end">
                                <Button
                                  variant="transparent"
                                  bg="white"
                                  colorScheme="black"
                                  onClick={() =>
                                    addFeatureDescription(
                                      initFeatureDescription
                                    )
                                  }
                                >
                                  + {t(`fiProperty.add.featureDescription`)}
                                </Button>
                              </Flex>
                            </Stack>
                          )}
                        />
                      )}
                      <BadgeGroupField
                        name={`${name}.${index}.wallMaterialCodes`}
                        label={
                          <FormLabel mb="2">
                            {t('fiProperty.fields.wallMaterial')}
                          </FormLabel>
                        }
                        emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                        type="array"
                        items={wallMaterials}
                        itemLabels={(key) =>
                          t(`fiProperty.enums.wallSurfaceMaterial.${key}`)
                        }
                        isItemChecked={(value: string) =>
                          wallMaterialCodes.includes(value)
                        }
                        onValueChanged={(value) => {
                          const newValue = [...wallMaterialCodes, value].filter(
                            (item, index, self) =>
                              self.indexOf(item) === self.lastIndexOf(item)
                          )
                          setFieldValue(
                            `${name}.${index}.wallMaterialCodes`,
                            newValue
                          )
                        }}
                        sortItemsByLabel
                      />
                      <BadgeGroupField
                        name={`${name}.${index}.floorMaterialCodes`}
                        label={
                          <FormLabel mb="2">
                            {t('fiProperty.fields.floorMaterial')}
                          </FormLabel>
                        }
                        emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                        type="array"
                        items={floorMaterial}
                        itemLabels={(key) =>
                          t(`fiProperty.enums.floorSurfaceMaterial.${key}`)
                        }
                        isItemChecked={(value: string) =>
                          floorMaterialCodes.includes(value)
                        }
                        onValueChanged={(value) => {
                          const newValue = [
                            ...floorMaterialCodes,
                            value,
                          ].filter(
                            (item, index, self) =>
                              self.indexOf(item) === self.lastIndexOf(item)
                          )

                          setFieldValue(
                            `${name}.${index}.floorMaterialCodes`,
                            newValue
                          )
                        }}
                        sortItemsByLabel
                      />
                      <BadgeGroupField
                        name={`${name}.${index}.ceilingMaterialCodes`}
                        label={
                          <FormLabel mb="2">
                            {t('fiProperty.fields.ceilingMaterial')}
                          </FormLabel>
                        }
                        emptyLabel={t('fiProperty.messages.propertyTypeEmpty')}
                        type="array"
                        items={ceilingMaterial}
                        itemLabels={(key) =>
                          t(`fiProperty.enums.ceilingSurfaceMaterial.${key}`)
                        }
                        isItemChecked={(value: string) =>
                          ceilingMaterialCodes.includes(value)
                        }
                        onValueChanged={(value) => {
                          const newValue = [
                            ...ceilingMaterialCodes,
                            value,
                          ].filter(
                            (item, index, self) =>
                              self.indexOf(item) === self.lastIndexOf(item)
                          )
                          setFieldValue(
                            `${name}.${index}.ceilingMaterialCodes`,
                            newValue
                          )
                        }}
                        sortItemsByLabel
                      />
                    </Stack>
                  </CardItem>
                )
              })}
              <Flex gap="2" flexWrap="wrap">
                {typeCodes.map((typeCode) => (
                  <Button
                    key={typeCode}
                    variant="transparent"
                    bg="white"
                    colorScheme="black"
                    onClick={() => push(newItem(typeCode))}
                  >
                    + {t(`fiProperty.add.${typeCode}`)}
                  </Button>
                ))}
              </Flex>
            </>
          )}
        </Stack>
      )}
    />
  )
}

export default SaunaItem
