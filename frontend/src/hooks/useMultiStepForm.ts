import { createContext, useContext } from 'react'

export interface MultiStepFormContext {
  activeStep: number
  allowedSteps: number[]
  goToNextStep: (options?: { overrideAllowedSteps?: number[] }) => void
  goToPreviousStep: () => void
}

const MultiStepFormContext = createContext<MultiStepFormContext | undefined>(
  undefined
)

export const MultiStepFormProvider = MultiStepFormContext.Provider

export const useMultiStepForm = () => {
  const context = useContext(MultiStepFormContext)
  if (!context) {
    throw new Error(
      'useMultiStepForm must be used within a MultiStepFormProvider'
    )
  }
  return context
}
