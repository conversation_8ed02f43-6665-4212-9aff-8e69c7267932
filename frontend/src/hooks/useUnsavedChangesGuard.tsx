import { useRef, useState, useEffect, useCallback } from 'react'

/**
 * Opens a confirmation modal when `enabled` is true and a guarded action is attempted.
 * Call `guard(fn)` instead of `fn()` for actions like close/cancel/navigation.
 */
export function useUnsavedChangesGuard(enabled: boolean): {
  isOpen: boolean
  guard: (next: () => void) => void
  confirm: () => void
  cancel: () => void
  setEnabled: (v: boolean) => void
} {
  const [isOpen, setOpen] = useState(false)
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const nextActionRef = useRef<() => void>(() => {})
  const [active, setActive] = useState(enabled)

  useEffect(() => setActive(enabled), [enabled])

  const guard = useCallback(
    (next: () => void) => {
      if (active) {
        nextActionRef.current = next
        setOpen(true)
      } else {
        next()
      }
    },
    [active]
  )

  const confirm = useCallback(() => {
    setOpen(false)
    nextActionRef.current?.()
  }, [])

  const cancel = useCallback(() => setOpen(false), [])

  return { isOpen, guard, confirm, cancel, setEnabled: setActive }
}
