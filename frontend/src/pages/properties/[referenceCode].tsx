import SecondaryLayout from '@/components/Layout/SecondaryLayout'
import {
  addTour,
  addVideo,
  archiveProperty,
  downloadAllPropertyMedia,
  getProperty,
  getPropertyImages,
} from '@/queries/property'
import {
  Box,
  Button,
  Flex,
  Heading,
  Input,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Spinner,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useBreakpointValue,
  useDisclosure,
  useToast,
} from '@chakra-ui/react'
import {
  useMutation,
  useQuery,
  useQueryClient,
  keepPreviousData,
} from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { GetServerSideProps } from 'next/types'
import { ReactElement, useEffect, useRef, useState } from 'react'
import {
  ESPropertyRead,
  LanguageCodeEnum,
  ReadProperty,
  StatusEnum,
} from '@/types/property'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { useTranslation } from 'next-i18next'
import PropertyDetails from '../../components/PropertyDetails/PropertyDetails'
import Media from '@/components/PropertyPage/Media'
import Documents from '@/components/PropertyPage/Documents'
import {
  DocumentLibrary,
  DocumentLibraryHandle,
} from '@/modules/document-library/components/DocumentLibrary'
import { OwnerType } from '@/modules/document-library/types/Owner'
import { ImageDropzone } from '@/components/ImageDropzone'
import { FileDropzone } from '@/components/FileDropzone'
import { ImageUploadType, useImageUpload } from '@/queries/upload'
import { DataSourceTag } from '@/components/SourceTag'
import ActionDrawer from '@/components/ActionDrawer/ActionDrawer'
import { useShouldRenderComponent } from '@/utils/useShouldRenderComponent'
import { useSyncNowButton } from '@/components/useSyncNowButton'
import { ChangesDetectedToast } from '@/components/ChangesDetectedToast'
import { StrandifiedTag } from '@/components/StrandifiedTag'
import { ChevronDownIcon } from '@chakra-ui/icons'
import { AxiosError } from 'axios'
import { STALE_TIME } from '@/utils/constants'
import { getEvents } from '@/queries/events'
import PropertyFiDetails from '@/components/PropertyPage/PropertyFiDetails'
import { useIsFinland } from '@/hooks/useIsFinland'
import { getFIProperty } from '@/modules/fi-properties/queries/queryFIProperties'
import { FIPropertyRead } from '@/modules/fi-properties'
import EditFiProperty from '@/components/FiProperty/EditFiPropertyModal'
import { EditPropertyForm } from '@/components/PropertyPage/EditPropertyForm'
import { ConfirmationModal } from '@/components/Modal/ConfirmationModal'
import { get } from 'lodash'
import { FiSalesAgreementPage } from '@/components/PropertyPage/components/FIProperty/sales-agreement'
import { useFeatureFlag } from '@/hooks/useFeatureFlag'
import PropertySidebar from '@/components/PropertySidebar/PropertySidebar'
import { getListAdvertisements } from '@/queries/advertisements'
import PropertyHistory from '@/components/PropertyPage/PropertyHistory'
import { ACTION_TYPE, OBJECT_TYPE } from '@/types/activityEventLogged'
import { setEventLogged } from '@/queries/eventLogged'
import { downloadBlob } from '@/utils/file'
import FITradePage from '@/components/PropertyPage/components/FIProperty/trade/FITradePage'
import { useUserAndOrganization } from '@/hooks/useUserAndOrganization'
import Photoshoots from '@/components/PropertyPage/Photoshoots'
// TODO: find a way to define the main language
const MAIN_LANGUAGE: LanguageCodeEnum = LanguageCodeEnum.EN

const tabFontSize = ['12px', '19px']

export const getServerSideProps: GetServerSideProps = async ({
  params,
  locale,
}) => {
  const { referenceCode } = params as { referenceCode: string }

  return {
    props: {
      referenceCode,
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  }
}

const PropertyDetailPage = ({
  referenceCode,
}: {
  referenceCode: string
  tabIndex: string
}) => {
  const queryClient = useQueryClient()
  const toast = useToast()
  const router = useRouter()
  const { verifyFeatureFlag } = useFeatureFlag()
  const photoShootsEnabled = verifyFeatureFlag('photoshoots')
  const fiSalesAgreementEnabled = verifyFeatureFlag('fiSalesAgreement')
  const documentLibraryEnabled = verifyFeatureFlag('documentLibrary')
  const fiPurchaseOfferEnabled = verifyFeatureFlag('fiPurchaseOffer')
  const fiDetailsOfSaleEnabled = verifyFeatureFlag('fiDetailsOfSale')
  const eventLoggedEnabled = verifyFeatureFlag('eventLogged')
  const { t, i18n } = useTranslation(['common'])
  const [tabIndex, setTabIndex] = useState(Number(router.query.activeTab) || 0)
  const documentLibraryRef = useRef<DocumentLibraryHandle>(null)

  const {
    isOpen: isEditFormOpen,
    onOpen: onEditFormOpen,
    onClose: onEditFormClose,
  } = useDisclosure()

  const {
    isOpen: isImageUploadOpen,
    onOpen: onImageUploadOpen,
    onClose: onImageUploadClose,
  } = useDisclosure()
  const {
    isOpen: isAddVideoOpen,
    onOpen: onAddVideoOpen,
    onClose: onAddVideoClose,
  } = useDisclosure()
  const {
    isOpen: isAddTourOpen,
    onOpen: onAddTourOpen,
    onClose: onAddTourClose,
  } = useDisclosure()

  const isFinland = useIsFinland()
  const {
    data,
    isPending,
    refetch: refetchProperty,
    isError,
    error,
  } = useQuery<FIPropertyRead | ReadProperty>({
    queryKey: ['property', referenceCode],
    queryFn: () =>
      isFinland ? getFIProperty(referenceCode) : getProperty(referenceCode),
    staleTime: STALE_TIME,
    enabled: isFinland !== undefined,
  })

  useQuery({
    queryKey: ['eventLogged', OBJECT_TYPE.PROPERTY, data?.id],

    queryFn: () =>
      setEventLogged({
        objectType: OBJECT_TYPE.PROPERTY,
        action: ACTION_TYPE.VIEWED,
        objectId: data?.id as number,
      }),

    enabled: !!data?.id,
  })

  const callbackUrl = router.query?.callbackUrl

  const { data: events, refetch: refetchEvents } = useQuery({
    queryKey: ['events', referenceCode],
    enabled: !!data?.id,
    queryFn: () => getEvents({ propertyIds: `${data?.id}` }),
  })

  useEffect(() => {
    if ((error as AxiosError)?.response?.status === 404) {
      // router.replace('/404')
    }
  }, [isError, error, router])

  useEffect(() => {
    if (router.query.activeTab) {
      setTabIndex(Number(router.query.activeTab))
    }
  }, [router.query.activeTab])

  const { data: propertyImages, isFetching: isFetchingImages } = useQuery({
    queryKey: ['media', referenceCode],
    queryFn: () => getPropertyImages(referenceCode),
    initialData: [],
  })

  const [propertyVideo, setPropertyVideo] = useState('')
  const [propertyTour, setPropertyTour] = useState('')
  const [salesAgreementId, setSalesAgreementId] = useState<string | undefined>()

  useEffect(() => {
    setSalesAgreementId(router.query.salesAgreementId as string)
  }, [router.query.salesAgreementId])

  const [createSalesAgreement, setCreateSalesAgreement] =
    useState<boolean>(false)
  useEffect(() => {
    setCreateSalesAgreement(router.query.createSalesAgreement === 'true')
  }, [router.query.createSalesAgreement])

  const { button: SyncNowButton } = useSyncNowButton(
    data?.id,
    (data as ReadProperty)?.portals,
    (data as ReadProperty)?.status
  )
  const { changesDetectedToast } = ChangesDetectedToast()

  const shouldRenderComponent = useShouldRenderComponent(
    data?.dataSource,
    data?.realtorUsers
  )

  const getCoverImage = () => {
    if (propertyImages.length === 0) {
      return undefined
    }

    const firstImageThatIsNotHidden = propertyImages.find(
      (item) => !item.isHidden
    )
    if (!firstImageThatIsNotHidden) {
      return undefined
    }

    return firstImageThatIsNotHidden
  }

  const coverImage = getCoverImage()

  const handleAddVideoOpen = () => {
    setPropertyVideo((data as ReadProperty)?.videoStreams[0]?.url || '')
    onAddVideoOpen()
  }

  const handleAddTourOpen = () => {
    setPropertyTour((data as ReadProperty)?.videoTours[0]?.url || '')
    onAddTourOpen()
  }

  useMutation({
    mutationFn: getProperty,
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: ['property', referenceCode],
      }),
  })

  const {
    isPending: isDownloadingAllDocuments,
    mutate: mutationDownloadAllDocuments,
  } = useMutation({
    mutationKey: ['download-all-media'],
    mutationFn: downloadAllPropertyMedia,
    onSuccess: async (res) => {
      downloadBlob(res, `Property-${referenceCode}-All media`)
    },
  })

  const { isPending: isAddVideoLoading, mutate: addVideoMutate } = useMutation({
    mutationFn: addVideo,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['property', referenceCode],
      })
      queryClient.invalidateQueries({
        queryKey: ['propertyValidation'],
      })

      toast({
        title: t('videoAddedTitle'),
        description: t('videoAddedDescription'),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
      changesDetectedToast()
      onAddVideoClose()
    },
  })

  const { isPending: isAddTourLoading, mutate: addTourMutate } = useMutation({
    mutationFn: addTour,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['property', referenceCode],
      })
      queryClient.invalidateQueries({
        queryKey: ['propertyValidation'],
      })

      toast({
        title: t('tourAddedTitle'),
        description: t('tourAddedDescription'),
        status: 'success',
        duration: 9000,
        isClosable: true,
        variant: 'customSuccess',
      })
      changesDetectedToast()
      onAddTourClose()
    },
  })

  const {
    isOpen: isConfirmArchiveOpen,
    onOpen: onConfirmArchiveOpen,
    onClose: onConfirmArchiveClose,
  } = useDisclosure()

  const { isPending: isArchiving, mutate: archivePropertyMutate } = useMutation(
    {
      mutationFn: archiveProperty,
      onSuccess: async () => {
        queryClient.invalidateQueries({
          queryKey: ['property', referenceCode],
        })
        toast({
          title: t('archiveProperty.confirmSuccess'),
          description: t('archiveProperty.confirmSuccessMessage'),
          status: 'success',
          duration: 9000,
          isClosable: true,
          variant: 'customSuccess',
        })
      },
      onError: (e) => {
        toast({
          title: t('archiveProperty.confirmError'),
          description: get(e, 'response.data.error'),
          status: 'error',
          duration: 9000,
          isClosable: true,
          variant: 'customError',
        })
      },
    }
  )

  const { onUploadImages } = useImageUpload({
    identifier: referenceCode,
    type: ImageUploadType.Property,
  })

  const handleTabsChange = (index: number) => {
    setTabIndex(index)
  }
  const isMobile = useBreakpointValue({
    base: true,
    sm: true,
    md: false,
    lg: false,
    xl: false,
  })

  const { isUserAdmin, isUserPhotographer, user } = useUserAndOrganization()

  const { data: ads } = useQuery({
    queryKey: ['advertisements', data?.id],
    queryFn: () =>
      data &&
      getListAdvertisements({
        propertyId: data.id,
      }),
    placeholderData: keepPreviousData,
  })

  if (isPending) {
    return (
      <Flex
        height="100%"
        width="100%"
        alignItems="center"
        justifyContent="center"
      >
        <Spinner size="xl" />
      </Flex>
    )
  }

  if (!data) {
    return <></>
  }

  const tradePageEnabled =
    isFinland && fiPurchaseOfferEnabled && fiDetailsOfSaleEnabled

  const tabs = [
    {
      label: t('details'),
      shouldRender: true,
      tabContent: (
        <TabPanel p={[0, 0, 0, '0 0 0 32px']} pr="0" key="details">
          <Flex justifyContent="space-between" width="100%">
            {isFinland ? (
              <PropertyFiDetails
                property={data as FIPropertyRead}
                setTabIndex={setTabIndex}
                mainLanguage={MAIN_LANGUAGE}
                coverImage={coverImage}
                propertyImages={propertyImages}
                isPhotographer={!!isUserPhotographer}
                events={events}
                refetchEvents={refetchEvents}
              />
            ) : (
              <PropertyDetails
                property={data as ESPropertyRead}
                setTabIndex={setTabIndex}
                mainLanguage={MAIN_LANGUAGE}
                coverImage={coverImage}
                propertyImages={propertyImages}
                isPhotographer={!!isUserPhotographer}
                events={events}
                ads={ads}
                refetchEvents={refetchEvents}
              />
            )}
            {(!!events?.length || (ads && !!ads.records.length)) &&
              !isMobile && (
                <PropertySidebar
                  events={events}
                  advertisements={ads?.records}
                  refreshEvents={refetchEvents}
                />
              )}
          </Flex>
        </TabPanel>
      ),
    },
    {
      label: t('media'),
      shouldRender: isUserPhotographer || shouldRenderComponent,
      tabContent: (
        <TabPanel bg="primary.main" p="0" key="media">
          <Flex
            width="100%"
            minHeight={['calc(100vh - 146px)', 'calc(100vh - 140px)']}
            justifyContent={['center', 'center', 'flex-start']}
            bg="primary.main"
          >
            <Media
              referenceCode={referenceCode}
              propertyImages={propertyImages}
              isFetchingImages={isFetchingImages}
              coverImage={coverImage}
              propertyId={data.id}
              propertyPortals={(data as ReadProperty).portals}
              propertyStatus={(data as ReadProperty).status}
              propertyVideo={data.videoStreams[0]}
              propertyTour={data.videoTours[0]}
            />
          </Flex>
        </TabPanel>
      ),
    },
    {
      label: t('resources'),
      shouldRender: !isUserPhotographer && shouldRenderComponent,
      tabContent: (
        <TabPanel p="0" key="resources">
          <Documents
            propertyData={data as ReadProperty}
            refresh={refetchProperty}
          />
        </TabPanel>
      ),
    },
    {
      label: t('photoshoots'),
      shouldRender: photoShootsEnabled,
      tabContent: (
        <TabPanel p="0" key="photoshoots">
          <Photoshoots />
        </TabPanel>
      ),
    },
    {
      label: t('fiSalesAgreement.tabTitle'),
      shouldRender:
        !isUserPhotographer && shouldRenderComponent && fiSalesAgreementEnabled,
      tabContent: (
        <TabPanel p="0" key="salesAgreement">
          <FiSalesAgreementPage />
        </TabPanel>
      ),
      buttons:
        !salesAgreementId && !createSalesAgreement ? (
          <Button
            onClick={async () => {
              router.push(
                {
                  pathname: router.pathname,
                  query: {
                    referenceCode,
                    activeTab: tabIndex,
                    createSalesAgreement: true,
                    callbackUrl,
                  },
                },
                undefined,
                { shallow: true }
              )
            }}
          >
            {t('fiSalesAgreement.create')}
          </Button>
        ) : null,
    },
    {
      label: t('history'),
      shouldRender: !isUserPhotographer && eventLoggedEnabled,
      tabContent: (
        <TabPanel p="0" key={'history'}>
          <PropertyHistory reference={referenceCode} language={i18n.language} />
        </TabPanel>
      ),
    },
    {
      label: t('trade'),
      shouldRender:
        !isUserPhotographer && eventLoggedEnabled && tradePageEnabled,
      tabContent: (
        <TabPanel p="0" key="trade">
          <FITradePage propertyId={data.id} />
        </TabPanel>
      ),
    },
    {
      label: t('documentLibrary.tabTitle'),
      shouldRender: isFinland && documentLibraryEnabled,
      tabContent: (
        <TabPanel p="0" key={'documentLibrary'}>
          <DocumentLibrary
            ref={documentLibraryRef}
            owner={{ type: OwnerType.FI_PROPERTY, id: data.id }}
          />
        </TabPanel>
      ),
      buttons: (
        <Button
          size={isMobile ? 'lg' : 'md'}
          onClick={() => {
            documentLibraryRef.current?.openUploadModal()
          }}
        >
          {t('documentLibrary.openUploadModal')}
        </Button>
      ),
    },
  ]

  const handleEditFormSuccess = () => {
    toast({
      position: isMobile ? 'top' : 'bottom-left',
      title: t('editProperty.updatedTitle'),
      description: `${t('editProperty.updatedDescription')} ${referenceCode}`,
      status: 'success',
      duration: 9000,
      isClosable: true,
      variant: 'customSuccess',
    })
    onEditFormClose()
  }

  const goBack = () => {
    router.push((callbackUrl as string) || '/properties')
  }
  const isAdminOrAssign =
    isUserAdmin ||
    (user && data.realtorUsers?.map((u) => u.id).includes(user.id))
  const EditDetailsAndSyncNowButton = () => {
    if (!isUserPhotographer) {
      return (
        <Flex>
          {isMobile ? (
            <Flex
              zIndex={10}
              position="fixed"
              width="100%"
              height="108px"
              alignItems="center"
              justifyContent="center"
              bottom="0"
              left="0"
              background="linear-gradient(180deg, rgba(246, 241, 237, 0) 0%, #F6F1ED 52.08%)"
            >
              <>
                <Flex direction={'column'} gap={2} alignItems="center">
                  {shouldRenderComponent && <SyncNowButton />}
                  {!isFinland &&
                    isAdminOrAssign &&
                    get(data, 'status') !== StatusEnum.ARCHIVED && (
                      <Button
                        variant={['smooth', 'smooth', 'transparent']}
                        onClick={onConfirmArchiveOpen}
                        aria-label="Click here to open modal for editing property details"
                        size={['xlgWide', 'xlgWide', 'md']}
                        borderColor="black"
                        isLoading={isArchiving}
                      >
                        {t('archiveProperty.label')}
                      </Button>
                    )}

                  {shouldRenderComponent && (
                    <Button
                      variant={['smooth', 'smooth', 'transparent']}
                      onClick={onEditFormOpen}
                      aria-label="Click here to open modal for editing property details"
                      size={['xlgWide', 'xlgWide', 'md']}
                    >
                      {t('editDetails')}
                    </Button>
                  )}
                </Flex>
              </>
              {/* Add Button */}
              <Flex right="10px" bottom="18px" p="1" position="absolute">
                <ActionDrawer buttonSize="roundedMd" />
              </Flex>
            </Flex>
          ) : (
            <Flex gap={2}>
              {shouldRenderComponent && <SyncNowButton />}
              {!isFinland &&
                isAdminOrAssign &&
                get(data, 'status') !== StatusEnum.ARCHIVED && (
                  <Button
                    variant={['smooth', 'smooth', 'transparent']}
                    onClick={onConfirmArchiveOpen}
                    aria-label="Click here to open modal for editing property details"
                    size={['xlgWide', 'xlgWide', 'md']}
                    borderColor="black"
                    isLoading={isArchiving}
                  >
                    {t('archiveProperty.label')}
                  </Button>
                )}
              {shouldRenderComponent && (
                <Button
                  colorScheme="black"
                  variant={['smooth', 'smooth', 'transparent']}
                  onClick={onEditFormOpen}
                  aria-label="Click here to open modal for editing property details"
                  size={['xlg', 'xlg', 'md']}
                >
                  {t('editDetails')}
                </Button>
              )}
            </Flex>
          )}
        </Flex>
      )
    }
    return <></>
  }

  const handleOnImageUpload = async (files: File[]): Promise<string[]> => {
    const res = await onUploadImages(files)

    // Inform user that the images changed, so if he wants that reflected
    // on API portals, he needs to click on "Sync Now"
    changesDetectedToast()

    return res
  }

  return (
    <Tabs
      position="relative"
      variant="unstyled"
      index={tabIndex}
      onChange={handleTabsChange}
      isLazy
    >
      <Flex
        alignItems="flex-start"
        as="header"
        direction={isMobile ? 'column' : 'column-reverse'}
        width="100%"
      >
        <Flex
          p={['0px 18px', '0 33px']}
          borderBottom="1px solid"
          borderColor="grays.grayBorder"
          width="inherit"
          alignItems="center"
        >
          {isMobile && (
            <button
              type="button"
              aria-label="go to back"
              onClick={goBack}
              style={{ height: '26px' }}
            >
              <span
                className="material-symbols-outlined"
                style={{ fontSize: '26px', fontWeight: '400' }}
              >
                arrow_back
              </span>
            </button>
          )}
          <Flex alignItems="center" justifyContent="space-between" width="100%">
            <Flex alignItems="center">
              <Text
                as="h1"
                pl={['14px', '14px', '0']}
                py={[0]}
                variant="heading"
                data-testid="property-reference-code-title"
              >
                <Flex direction="row" gap={2}>
                  <Flex>{referenceCode}</Flex>
                  <Flex alignItems="center" gap={2}>
                    {!!data.dataSource && (
                      <DataSourceTag dataSource={data.dataSource} />
                    )}
                    {data.isStrandified && <StrandifiedTag />}
                  </Flex>
                  {!!salesAgreementId && <Flex>{salesAgreementId}</Flex>}
                </Flex>
              </Text>
            </Flex>
            {tabIndex === 0 && (
              <Flex gap="2">
                <EditDetailsAndSyncNowButton />
              </Flex>
            )}
            {!isMobile && tabIndex === 1 && (
              <Flex gap={2}>
                <SyncNowButton />
                <Button
                  variant={['smooth', 'smooth', 'transparent']}
                  size={['xlgWide', 'xlgWide', 'md']}
                  borderColor="black"
                  onClick={() => mutationDownloadAllDocuments(referenceCode)}
                  aria-label="Click here to open modal for editing property details"
                  isLoading={isDownloadingAllDocuments}
                  isDisabled={!propertyImages.length}
                >
                  {t('downloadAllMedia')}
                </Button>
                <Menu size="sm">
                  <MenuButton
                    as={Button}
                    rightIcon={<ChevronDownIcon />}
                    bg="transparent"
                    color="black"
                    border="1px solid black"
                    fontWeight="normal"
                  >
                    {t('addMedia')}
                  </MenuButton>
                  <MenuList minWidth="145px" zIndex="10">
                    <MenuItem onClick={onImageUploadOpen}>
                      {t('uploadImages')}
                    </MenuItem>
                    <MenuItem
                      isDisabled={
                        (data as ReadProperty)?.videoStreams.length > 0
                      }
                      onClick={handleAddVideoOpen}
                    >
                      {t('addVideo')}
                    </MenuItem>
                    <MenuItem
                      isDisabled={(data as ReadProperty)?.videoTours.length > 0}
                      onClick={handleAddTourOpen}
                    >
                      {t('addTour')}
                    </MenuItem>
                  </MenuList>
                </Menu>
              </Flex>
            )}
            {isMobile && tabIndex === 1 && (
              <Stack
                position="fixed"
                height="108px"
                alignItems="center"
                justifyContent="center"
                bottom="0"
                left="50%"
                transform="translate(-50%, 0)"
                zIndex="20"
              >
                <Button
                  variant={['smooth', 'smooth', 'transparent']}
                  onClick={() => mutationDownloadAllDocuments(referenceCode)}
                  aria-label="Click here to open modal for editing property details"
                  size={['xlg', 'xlg', 'md']}
                  isLoading={isDownloadingAllDocuments}
                  disabled={!propertyImages.length}
                >
                  {t('downloadAllMedia')}
                </Button>
                <Menu>
                  <MenuButton
                    as={Button}
                    rightIcon={<ChevronDownIcon />}
                    bg="black"
                    color="white"
                    fontWeight="900"
                    size="lg"
                    px="8"
                  >
                    {t('addMedia')}
                  </MenuButton>
                  <MenuList minWidth="170px" zIndex="10">
                    <MenuItem onClick={onImageUploadOpen}>
                      {t('uploadImages')}
                    </MenuItem>
                    <MenuItem
                      isDisabled={
                        (data as ReadProperty)?.videoStreams.length > 0
                      }
                      onClick={handleAddVideoOpen}
                    >
                      {t('addVideo')}
                    </MenuItem>
                    <MenuItem
                      isDisabled={(data as ReadProperty)?.videoTours.length > 0}
                      onClick={handleAddTourOpen}
                    >
                      {t('addTour')}
                    </MenuItem>
                  </MenuList>
                </Menu>
              </Stack>
            )}
            {!isMobile && tabIndex === 2 && (
              <Flex gap={2}>
                <SyncNowButton />
                <FileDropzone
                  propertyReference={data.reference}
                  propertyId={data?.id}
                  buttonText={t('addDocuments')}
                  buttonSize="md"
                  buttonVariant="transparent"
                  refresh={refetchProperty}
                  propertyFiles={(data as ReadProperty).files}
                />
              </Flex>
            )}
            {tabs[tabIndex]?.shouldRender &&
              tabs[tabIndex]?.buttons &&
              (isMobile ? (
                <Stack
                  position="fixed"
                  height="108px"
                  alignItems="center"
                  justifyContent="center"
                  bottom="0"
                  left="50%"
                  transform="translate(-50%, 0)"
                  zIndex="20"
                >
                  {tabs[tabIndex].buttons}
                </Stack>
              ) : (
                <Flex gap={2}>{tabs[tabIndex].buttons}</Flex>
              ))}
          </Flex>
        </Flex>
        <Flex
          alignItems="center"
          justifyContent={['center', 'center', 'flex-start']}
          width="100%"
          borderBottom="1px solid"
          borderColor="grays.grayBorder"
        >
          {!isMobile && (
            <button
              type="button"
              aria-label="go to back"
              onClick={goBack}
              style={{
                height: '26px',
                paddingLeft: '21px',
                marginRight: '16px',
              }}
            >
              <span
                className="material-symbols-outlined"
                style={{ fontSize: '26px', fontWeight: '400' }}
              >
                arrow_back
              </span>
            </button>
          )}
          <TabList p={['0px 10px', '0']} overflowX="auto">
            {tabs
              .filter((t) => t.shouldRender)
              .map((tab, index) => {
                return (
                  <Tab
                    key={index}
                    fontSize={tabFontSize}
                    onClick={() => {
                      router.push(
                        {
                          pathname: router.pathname,
                          query: {
                            activeTab: index,
                            referenceCode,
                            callbackUrl,
                          },
                        },
                        undefined,
                        { shallow: true }
                      )
                    }}
                  >
                    {tab.label}
                  </Tab>
                )
              })}
          </TabList>
          {!isMobile && (
            <Box mr="20px" ml="auto">
              <ActionDrawer buttonSize="roundedXs" />
            </Box>
          )}
        </Flex>
      </Flex>
      <Flex>
        <Box width="100%" position="relative">
          <TabPanels>
            {tabs.map((tab) => (tab.shouldRender ? tab.tabContent : null))}
          </TabPanels>
        </Box>
      </Flex>

      <ImageDropzone
        isOpen={isImageUploadOpen}
        onClose={onImageUploadClose}
        onUploadImages={handleOnImageUpload}
      />
      <ConfirmationModal
        isOpen={isConfirmArchiveOpen}
        isCentered={true}
        onAccept={() => {
          archivePropertyMutate(referenceCode)
          onConfirmArchiveClose()
        }}
        title={t('archiveProperty.confirmTitle')}
        message={t('archiveProperty.confirmMessage')}
        onReject={() => onConfirmArchiveClose()}
        hideText={false}
      />

      <Modal isOpen={isAddVideoOpen} onClose={onAddVideoClose} size="3xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader
            justifyContent="space-between"
            alignItems="center"
            display="flex"
            borderBottom="1px solid"
            borderColor="grays.gray6"
            height="58px"
          >
            <Heading variant="brand">{t('addVideo')}</Heading>
            <Flex alignItems="center">
              <Button
                colorScheme="blue"
                mr={1}
                onClick={onAddVideoClose}
                variant="transparent"
                borderColor="transparent"
              >
                {t('close')}
              </Button>
              <Button
                height="34px"
                colorScheme="blue"
                onClick={() =>
                  addVideoMutate({
                    reference: data.reference,
                    videoUrl: propertyVideo,
                  })
                }
                isDisabled={propertyVideo === ''}
                isLoading={isAddVideoLoading}
              >
                {t('addVideo')}
              </Button>
            </Flex>
          </ModalHeader>

          <ModalBody py="8">
            <Text mb="8px">{t('videoUrl')}</Text>
            <Input
              variant="outlined"
              fontSize="18px"
              placeholder="http://www.youtube.com/watch?v=xxxxxxx"
              onChange={(e) => {
                setPropertyVideo(e.currentTarget.value)
              }}
              value={propertyVideo}
            />
          </ModalBody>
        </ModalContent>
      </Modal>

      <Modal isOpen={isAddTourOpen} onClose={onAddTourClose} size="3xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader
            justifyContent="space-between"
            alignItems="center"
            display="flex"
            borderBottom="1px solid"
            borderColor="grays.gray6"
            height="58px"
          >
            <Heading variant="brand">{t('addTour')}</Heading>
            <Flex alignItems="center">
              <Button
                colorScheme="blue"
                mr={1}
                onClick={onAddTourClose}
                variant="transparent"
                borderColor="transparent"
              >
                {t('close')}
              </Button>
              <Button
                height="34px"
                colorScheme="blue"
                onClick={() =>
                  addTourMutate({
                    reference: data.reference,
                    tourUrl: propertyTour,
                  })
                }
                isDisabled={propertyTour === ''}
                isLoading={isAddTourLoading}
              >
                {t('addTour')}
              </Button>
            </Flex>
          </ModalHeader>

          <ModalBody py="8">
            <Text mb="8px">{t('tourUrl')}</Text>
            <Input
              variant="outlined"
              fontSize="18px"
              placeholder="https://floorfy.com/tour/xxxxxxx"
              onChange={(e) => {
                setPropertyTour(e.currentTarget.value)
              }}
              value={propertyTour}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
      {isFinland !== undefined &&
        isEditFormOpen &&
        (isFinland ? (
          <EditFiProperty
            isOpen
            onClose={onEditFormClose}
            propertyReference={referenceCode}
          />
        ) : (
          <EditPropertyForm
            isOpen
            onClose={onEditFormClose}
            onSuccess={handleEditFormSuccess}
            propertyData={data as ReadProperty}
            mainLanguage={MAIN_LANGUAGE}
          />
        ))}
    </Tabs>
  )
}

PropertyDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <SecondaryLayout>{page}</SecondaryLayout>
}

export default PropertyDetailPage
