import NextAuth, {
  Account,
  AuthOptions,
  Profile,
  Session,
  User,
} from 'next-auth'
import CognitoProvider, { CognitoProfile } from 'next-auth/providers/cognito'
import { axiosInstance } from '@/lib/axiosConfig'
import { JWT } from 'next-auth/jwt'
import { OAuthConfig, Provider } from 'next-auth/providers'
import { UserCreateSSO } from '@/types/users'

type CognitoOAuthProvider = OAuthConfig<CognitoProfile>
type ChecksType = 'pkce' | 'state' | 'none' | 'nonce'

interface CreateCognitoProviderOptions {
  providerId: string
  identityProvider: string
  clientId?: string
  clientSecret?: string
  issuer?: string
}

const TOKEN_EXPIRATION_IN_SECONDS = 7 * 24 * 60 * 60 // 7 days

const createCognitoProvider = ({
  providerId,
  identityProvider,
  clientId = process.env.NEXT_PUBLIC_NEXTAUTH_COGNITO_CLIENT_ID || '',
  clientSecret = process.env.NEXTAUTH_COGNITO_CLIENT_SECRET || '',
  issuer = process.env.NEXTAUTH_COGNITO_ISSUER || '',
}: CreateCognitoProviderOptions): CognitoOAuthProvider => {
  const baseConfig = {
    id: providerId,
    clientId,
    clientSecret,
    issuer,
    checks: ['nonce'] as ChecksType[],
  }
  console.info(process.env.NEXTAUTH_COGNITO_CLIENT_SECRET)

  return CognitoProvider({
    ...baseConfig,
    authorization: {
      params: {
        identity_provider: identityProvider,
      },
    },
  })
}

const getProviders = (): Provider[] => [
  createCognitoProvider({
    providerId: 'cognito-azure',
    identityProvider: 'cognito-azure',
  }),
]

export const options: AuthOptions = {
  providers: getProviders(),
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: 'jwt',
    maxAge: TOKEN_EXPIRATION_IN_SECONDS,
  },
  pages: {
    error: '/login',
  },
  debug: true,
  jwt: {
    maxAge: TOKEN_EXPIRATION_IN_SECONDS,
  },
  callbacks: {
    async signIn({
      user,
      account,
    }: {
      user: User
      account: Account | null
      profile?: Profile
    }) {
      if (account?.provider !== 'cognito-azure') {
        return true
      }

      const email = user.email || ''
      const cognito_sub = user.id || ''

      const data: UserCreateSSO = {
        email,
        cognito_sub,
      }

      try {
        const res = await axiosInstance.post('/auth/get-user-with-sso', data, {
          headers: {
            'X-Organization-Id': 1,
          },
        })
        return !!res
      } catch (error) {
        console.error(error)
        return false
      }
    },
    async jwt({
      token,
      session,
      trigger,
      user,
      account,
    }: {
      token: JWT
      session?: { token: string }
      trigger?: 'signIn' | 'signUp' | 'update'
      user: User
      account?: Account | null
    }) {
      if (session && trigger === 'update') {
        token.accessToken = session.token
      } else if (user) {
        token.accessToken = user.token
      }
      if (account && account.provider === 'cognito-azure') {
        token.accessToken = account.access_token
        token.refreshToken = account.refresh_token

        token.idToken = account.id_token
        token.provider = account.provider
        token.expiresAt = account.expires_at
          ? new Date(account.expires_at * 1000).toISOString()
          : new Date(
              new Date().getTime() + TOKEN_EXPIRATION_IN_SECONDS * 1000
            ).toISOString()
      } else {
        // Sets the token expiration time to the duration of TOKEN_EXPIRATION_IN_SECONDS in order to show/hide the login page and prevent the hellish loop when credentials are expired.
        token.expiresAt = new Date(
          new Date().getTime() + TOKEN_EXPIRATION_IN_SECONDS * 1000
        ).toISOString()
      }

      return token
    },
    /// https://next-auth.js.org/configuration/callbacks#session-callback
    async session({ session, token }: { session: Session; token: JWT }) {
      session.expires = token.expiresAt as string

      return {
        ...session,
        token: token.accessToken,
      }
    },
  },
}

export default NextAuth(options)
