import SecondaryLayout from '@/components/Layout/SecondaryLayout'
import {
  QueryClient,
  dehydrate,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query'
import { ReactElement, useState } from 'react'
import {
  Badge,
  Box,
  Flex,
  Heading,
  Spinner,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { GetServerSideProps } from 'next/types'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { useRouter } from 'next/router'
import { UserDetails } from '@/components/User/UserDetails'
import { getCurrentUser, getUser } from '@/queries/users'
import { getFullName, UserRead } from '@/types/users'
import ActionDrawer from '@/components/ActionDrawer/ActionDrawer'
import { EditUserForm } from '@/components/User/EditUserForm'
import { CustomAvatar } from '@/components/Avatar/CustomAvatar'
import { getProperties } from '@/queries/property'
import PropertyTable from '@/components/Table/PropertyTable'
import { EditUserRoleForm } from '@/components/User/EditUserRoleForm'
import ExpenseTransactionTable from '@/components/Table/ExpenseTransactionTable'
import WrappedSetupPaymentMethodModal from '@/components/Expenses/SetupExpenseMethodModal'
import { FeatureFlag } from '@/components/FeatureFlag'

type UserId = string

export const getServerSideProps: GetServerSideProps = async ({
  params,
  locale,
}) => {
  const { id } = params as { id: string }
  const queryClient = new QueryClient()
  queryClient.invalidateQueries({ queryKey: ['user', id] })

  return {
    props: {
      dehydratedState: dehydrate(queryClient),
      id,
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  }
}

const UserDetailPage = ({ id }: { id: UserId }) => {
  const router = useRouter()
  const [currentPropertiesPage, setCurrentPropertiesPage] = useState(1)
  const [paymentModalOpen, setPaymentModalOpen] = useState(false)
  const queryClient = useQueryClient()
  const { data: me } = useQuery({
    queryKey: ['me'],
    queryFn: () => getCurrentUser(),
  })

  const { data: user, isPending: isGetCurrentUserLoading } = useQuery<UserRead>(
    {
      queryKey: ['user', id],
      queryFn: () => getUser(id),
    }
  )

  const {
    data: properties,
    isFetching,
    refetch,
  } = useQuery({
    enabled: !!user && user.properties.length > 0,
    queryKey: ['properties', currentPropertiesPage, id],
    queryFn: () =>
      getProperties({
        assignedTo: [Number(id)],
        ...{ pageSize: 10, page: currentPropertiesPage },
      }),
  })

  const { t } = useTranslation(['common'])

  const [tabIndex, setTabIndex] = useState(0)
  const handleTabsChange = (index: number) => {
    setTabIndex(index)
  }
  const isMobile = useBreakpointValue({
    base: true,
    sm: true,
    md: false,
    lg: false,
    xl: false,
  })

  const goBack = () => {
    const callbackUrl = router.query?.callbackUrl
    callbackUrl
      ? router.push(callbackUrl as string)
      : router.push('/properties')
  }

  if (isGetCurrentUserLoading) {
    return (
      <Flex
        height="100%"
        width="100%"
        alignItems="center"
        justifyContent="center"
      >
        <Spinner size="xl" />
      </Flex>
    )
  }

  if (!user) {
    return <></>
  }

  return (
    <Tabs
      position="relative"
      variant="unstyled"
      index={tabIndex}
      onChange={handleTabsChange}
      isLazy
    >
      <Flex
        alignItems="flex-start"
        as="header"
        direction={isMobile ? 'column' : 'column-reverse'}
        width="100%"
      >
        <Flex
          p={['22px 18px', '0 33px']}
          borderBottom="1px solid"
          borderColor="grays.grayBorder"
          width="inherit"
          height={'64px'}
          alignItems="center"
        >
          {isMobile && (
            <button
              type="button"
              aria-label="go to back"
              onClick={goBack}
              style={{ height: '26px', marginRight: '5px' }}
            >
              <span
                className="material-symbols-outlined"
                style={{ fontSize: '26px', fontWeight: '400' }}
              >
                arrow_back
              </span>
            </button>
          )}
          <Flex alignItems="center" justifyContent="space-between" width="100%">
            <Flex alignItems="center" gap="10px">
              <Text
                as="h1"
                py={[0, 0, '20px']}
                variant="heading"
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
                maxWidth="calc(100vw - 92px)"
                lineHeight="unset"
              >
                {user.firstName} {user.lastName}
              </Text>
              <Text
                pl={['14px', '14px', '0']}
                py={[0, 0, '20px']}
                color="secondary.text"
                fontSize="16px"
                textTransform="capitalize"
              >
                <Badge variant={user.isActive ? 'enabled' : 'disabled'}>
                  {user.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </Text>
            </Flex>
            <Flex gap="2">
              {!isMobile && tabIndex !== 1 && (
                <>
                  <EditUserForm user={user} /> <EditUserRoleForm user={user} />
                </>
              )}
            </Flex>
          </Flex>
        </Flex>
        <Flex
          alignItems="center"
          justifyContent="flex-start"
          width="100%"
          borderBottom="1px solid"
          borderColor="grays.grayBorder"
        >
          {!isMobile && (
            <button
              type="button"
              aria-label="go to back"
              onClick={goBack}
              style={{ height: '26px', paddingLeft: '21px' }}
            >
              <span
                className="material-symbols-outlined"
                style={{ fontSize: '26px', fontWeight: '400' }}
              >
                arrow_back
              </span>
            </button>
          )}
          <TabList pl={['21px', '31px']}>
            <Tab>{t('details')}</Tab>
            {me?.id === user.id && (
              <FeatureFlag featureFlag="expenses">
                <Tab>{t('expenses.tab')}</Tab>
              </FeatureFlag>
            )}
          </TabList>
          {!isMobile && (
            <Box mr="20px" ml="auto">
              <ActionDrawer buttonSize="roundedXs" />
            </Box>
          )}
        </Flex>
      </Flex>

      {/* Details */}
      <Flex>
        <Box width="100%" position="relative">
          <TabPanels>
            <TabPanel pt={[5, 5, 10]} pb={['120px', '120px', 10]} px="5%">
              <Flex alignItems="flex-start" width="100%">
                {!isMobile && (
                  <Flex flex="0 0 150px">
                    <CustomAvatar
                      size="2xl"
                      name={getFullName(user)}
                      src={user.photoUrl && `${user.photoUrl}?width=150`}
                      height={150}
                      width={150}
                      bg="common.black"
                      color="common.white"
                    />
                  </Flex>
                )}
                <Box flex="1 1 auto" pl={[0, 0, '5%']}>
                  <UserDetails user={user} />
                </Box>
              </Flex>
              {/* Properties */}
              {user.properties && user.properties.length > 0 && (
                <Box
                  width={'100%'}
                  mt={[5, 5, 10]}
                  pt={[5, 5, 10]}
                  borderTop="1px solid"
                  borderColor="grays.grayBorder"
                >
                  <Heading variant="display" mb={4}>
                    {t('associatedProperties')}
                  </Heading>
                  {isFetching ? (
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      height="50vh"
                    >
                      <Spinner size="lg" />
                    </Box>
                  ) : (
                    <>
                      {properties && (
                        <PropertyTable
                          totalItems={properties.metadata.totalCount}
                          pageSize={properties.metadata.pageSize}
                          allProperties={properties.records}
                          currentPage={currentPropertiesPage}
                          setCurrentPage={setCurrentPropertiesPage}
                          refresh={refetch}
                        />
                      )}
                    </>
                  )}
                </Box>
              )}
            </TabPanel>
            {/* Expenses */}

            <FeatureFlag featureFlag="expenses">
              <TabPanel pt={[5, 5, 10]} pb={['120px', '120px', 10]}>
                <ExpenseTransactionTable
                  filterByMe={true}
                  setShowStripe={setPaymentModalOpen}
                />
                <WrappedSetupPaymentMethodModal
                  isOpen={paymentModalOpen}
                  onClose={() => setPaymentModalOpen(false)}
                  onSuccess={() =>
                    queryClient.invalidateQueries({
                      queryKey: ['getStripeCardDetails'],
                    })
                  }
                />
              </TabPanel>
            </FeatureFlag>
          </TabPanels>
        </Box>
      </Flex>
    </Tabs>
  )
}

UserDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <SecondaryLayout>{page}</SecondaryLayout>
}

export default UserDetailPage
