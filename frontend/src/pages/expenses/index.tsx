import { Flex } from '@chakra-ui/react'
import Head from 'next/head'
import Layout from '@/components/Layout/Layout'
import { ReactElement } from 'react'
import { useTranslation } from 'next-i18next'
import ExpenseTransactionTable from '@/components/Table/ExpenseTransactionTable'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { GetServerSideProps } from 'next'

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale || 'en', ['common'])),
    },
  }
}

const Expenses = () => {
  const { t } = useTranslation(['common'])

  return (
    <Flex direction="column" gap="4" px={['4', '8']} height="100%">
      <Head>
        <title>{t('expenses.tab')}</title>
      </Head>
      <ExpenseTransactionTable filterByMe={false} />
    </Flex>
  )
}

Expenses.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default Expenses
