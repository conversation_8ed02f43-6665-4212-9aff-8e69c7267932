import { CountryNameEnum } from '@/modules/country'
import { TagName } from '@/types/tags'

export const filterCompanyKey = 'reactron'

/**
 * Enum for environments
 */
export enum Environment {
  LOCAL = 'local',
  DEV = 'dev',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

/**
 * Enum for feature flag levels
 */
export enum FeatureFlagLevel {
  EVERYONE = 'everyone',
  ONLY_ADMIN = 'admin',
  ONLY_DEV = 'developer',
  ONLY_BETA = 'beta',
  NONE = 'none',
}

/**
 * Map of feature flag levels to their corresponding tag names
 */
export const FEATURE_FLAG_LEVEL_TO_TAG: Partial<
  Record<FeatureFlagLevel, TagName>
> = {
  [FeatureFlagLevel.ONLY_DEV]: TagName.DEVELOPER,
  [FeatureFlagLevel.ONLY_BETA]: TagName.BETA_TESTER,
} as const

/**
 * Common feature flag name
 */
export const FEATURE_COMMON = '__common'

/**
 * Dev tools feature flag name
 */
export const FEATURE_DEV_TOOLS = '__devTools'

/**
 * Union type for feature flag names
 */
export type FeatureFlagName =
  | typeof FEATURE_COMMON
  | typeof FEATURE_DEV_TOOLS
  | 'cognitoUserPools'
  | 'createBrochureFI'
  | 'createBrokerageOffer'
  | 'createDoS'
  | 'createEvent'
  | 'createOffer'
  | 'createProperty'
  | 'createSalesAgreement'
  | 'createSellingOffer'
  | 'createShareTrade'
  | 'documentLibrary'
  | 'duplicateProperty'
  | 'eSignature'
  | 'fiPurchaseOffer'
  | 'fiDetailsOfSale'
  | 'fiSalesAgreement'
  | 'groupUser'
  | 'listProperties'
  | 'manualLead'
  | 'marketingConsent'
  | 'matchMaking'
  | 'organizationSwitch'
  | 'quickSearch'
  | 'report'
  | 'strandBot'
  | 'updatePropertyStatus'
  | 'advertisements'
  | 'importExportTranslations'
  | 'expenses'
  | 'eventLogged'
  | 'photoshoots'
  | 'propertyGridView'
// NOTE: REMEMBER TO SORT THE FEATURE FLAGS BY ALPHABETICAL ORDER!!!

/**
 * Feature flag dictionary
 */
export type FeatureFlagDictionary = Record<
  FeatureFlagName,
  readonly FeatureFlagLevel[]
>

const DEFAULT_FEATURE_FLAGS: Record<Environment, FeatureFlagDictionary> = {
  // NOTE: REMEMBER TO SORT THE FEATURE FLAGS BY ALPHABETICAL ORDER!!!
  local: {
    [FEATURE_COMMON]: [FeatureFlagLevel.EVERYONE], // Don't modify this value
    [FEATURE_DEV_TOOLS]: [FeatureFlagLevel.ONLY_DEV], // Don't modify this value
    cognitoUserPools: [FeatureFlagLevel.EVERYONE],
    createBrochureFI: [FeatureFlagLevel.ONLY_DEV],
    createBrokerageOffer: [FeatureFlagLevel.EVERYONE],
    createDoS: [FeatureFlagLevel.EVERYONE],
    createEvent: [FeatureFlagLevel.EVERYONE],
    createOffer: [FeatureFlagLevel.EVERYONE],
    createProperty: [FeatureFlagLevel.EVERYONE],
    createSalesAgreement: [FeatureFlagLevel.EVERYONE],
    createSellingOffer: [FeatureFlagLevel.ONLY_DEV],
    createShareTrade: [FeatureFlagLevel.NONE],
    documentLibrary: [FeatureFlagLevel.EVERYONE],
    duplicateProperty: [FeatureFlagLevel.EVERYONE],
    eSignature: [FeatureFlagLevel.EVERYONE],
    fiPurchaseOffer: [FeatureFlagLevel.NONE],
    fiDetailsOfSale: [FeatureFlagLevel.NONE],
    fiSalesAgreement: [FeatureFlagLevel.NONE],
    groupUser: [FeatureFlagLevel.EVERYONE],
    listProperties: [FeatureFlagLevel.EVERYONE],
    manualLead: [FeatureFlagLevel.EVERYONE],
    marketingConsent: [FeatureFlagLevel.EVERYONE],
    matchMaking: [FeatureFlagLevel.EVERYONE],
    organizationSwitch: [FeatureFlagLevel.EVERYONE],
    quickSearch: [FeatureFlagLevel.EVERYONE],
    strandBot: [FeatureFlagLevel.EVERYONE],
    report: [FeatureFlagLevel.EVERYONE],
    updatePropertyStatus: [FeatureFlagLevel.EVERYONE],
    advertisements: [FeatureFlagLevel.EVERYONE],
    importExportTranslations: [FeatureFlagLevel.ONLY_ADMIN],
    expenses: [FeatureFlagLevel.EVERYONE],
    eventLogged: [FeatureFlagLevel.EVERYONE],
    photoshoots: [FeatureFlagLevel.EVERYONE],
    propertyGridView: [FeatureFlagLevel.EVERYONE],
  },
  dev: {
    [FEATURE_COMMON]: [FeatureFlagLevel.EVERYONE], // Don't modify this value
    [FEATURE_DEV_TOOLS]: [FeatureFlagLevel.ONLY_DEV], // Don't modify this value
    cognitoUserPools: [FeatureFlagLevel.NONE],
    createBrochureFI: [FeatureFlagLevel.ONLY_DEV],
    createBrokerageOffer: [FeatureFlagLevel.NONE],
    createDoS: [FeatureFlagLevel.EVERYONE],
    createEvent: [FeatureFlagLevel.EVERYONE],
    createOffer: [FeatureFlagLevel.EVERYONE],
    createProperty: [FeatureFlagLevel.EVERYONE],
    createSalesAgreement: [FeatureFlagLevel.EVERYONE],
    createSellingOffer: [FeatureFlagLevel.ONLY_DEV],
    createShareTrade: [FeatureFlagLevel.NONE],
    documentLibrary: [FeatureFlagLevel.ONLY_ADMIN],
    duplicateProperty: [FeatureFlagLevel.EVERYONE],
    eSignature: [FeatureFlagLevel.EVERYONE],
    fiPurchaseOffer: [FeatureFlagLevel.NONE],
    fiDetailsOfSale: [FeatureFlagLevel.NONE],
    fiSalesAgreement: [FeatureFlagLevel.NONE],
    groupUser: [FeatureFlagLevel.EVERYONE],
    listProperties: [FeatureFlagLevel.EVERYONE],
    manualLead: [FeatureFlagLevel.ONLY_ADMIN],
    marketingConsent: [FeatureFlagLevel.ONLY_DEV, FeatureFlagLevel.ONLY_BETA],
    matchMaking: [FeatureFlagLevel.EVERYONE],
    organizationSwitch: [FeatureFlagLevel.EVERYONE],
    quickSearch: [FeatureFlagLevel.EVERYONE],
    strandBot: [FeatureFlagLevel.EVERYONE],
    report: [FeatureFlagLevel.ONLY_ADMIN],
    updatePropertyStatus: [FeatureFlagLevel.NONE],
    advertisements: [FeatureFlagLevel.ONLY_DEV, FeatureFlagLevel.ONLY_BETA],
    importExportTranslations: [FeatureFlagLevel.NONE],
    expenses: [FeatureFlagLevel.ONLY_ADMIN],
    eventLogged: [FeatureFlagLevel.ONLY_ADMIN],
    photoshoots: [FeatureFlagLevel.ONLY_ADMIN],
    propertyGridView: [FeatureFlagLevel.ONLY_ADMIN],
  },
  staging: {
    [FEATURE_COMMON]: [FeatureFlagLevel.EVERYONE], // Don't modify this value
    [FEATURE_DEV_TOOLS]: [FeatureFlagLevel.ONLY_DEV], // Don't modify this value
    cognitoUserPools: [FeatureFlagLevel.NONE],
    createBrochureFI: [FeatureFlagLevel.ONLY_DEV],
    createDoS: [FeatureFlagLevel.ONLY_ADMIN],
    createBrokerageOffer: [FeatureFlagLevel.NONE],
    createEvent: [FeatureFlagLevel.ONLY_DEV],
    createOffer: [FeatureFlagLevel.EVERYONE],
    createProperty: [FeatureFlagLevel.EVERYONE],
    createSalesAgreement: [FeatureFlagLevel.EVERYONE],
    createSellingOffer: [FeatureFlagLevel.ONLY_DEV],
    createShareTrade: [FeatureFlagLevel.NONE],
    documentLibrary: [FeatureFlagLevel.NONE],
    duplicateProperty: [FeatureFlagLevel.EVERYONE],
    eSignature: [FeatureFlagLevel.ONLY_ADMIN],
    fiPurchaseOffer: [FeatureFlagLevel.NONE],
    fiDetailsOfSale: [FeatureFlagLevel.NONE],
    fiSalesAgreement: [FeatureFlagLevel.NONE],
    groupUser: [FeatureFlagLevel.ONLY_DEV],
    listProperties: [FeatureFlagLevel.EVERYONE],
    manualLead: [FeatureFlagLevel.ONLY_ADMIN],
    marketingConsent: [FeatureFlagLevel.NONE],
    matchMaking: [FeatureFlagLevel.ONLY_ADMIN],
    organizationSwitch: [FeatureFlagLevel.EVERYONE],
    quickSearch: [FeatureFlagLevel.ONLY_DEV],
    strandBot: [FeatureFlagLevel.EVERYONE],
    report: [FeatureFlagLevel.NONE],
    updatePropertyStatus: [FeatureFlagLevel.NONE],
    advertisements: [FeatureFlagLevel.ONLY_DEV],
    importExportTranslations: [FeatureFlagLevel.NONE],
    expenses: [FeatureFlagLevel.ONLY_DEV],
    eventLogged: [FeatureFlagLevel.NONE],
    photoshoots: [FeatureFlagLevel.NONE],
    propertyGridView: [FeatureFlagLevel.NONE],
  },
  production: {
    [FEATURE_COMMON]: [FeatureFlagLevel.EVERYONE],
    [FEATURE_DEV_TOOLS]: [FeatureFlagLevel.ONLY_DEV],
    cognitoUserPools: [FeatureFlagLevel.NONE],
    createBrochureFI: [FeatureFlagLevel.ONLY_DEV],
    createDoS: [FeatureFlagLevel.EVERYONE],
    createBrokerageOffer: [FeatureFlagLevel.NONE],
    createEvent: [FeatureFlagLevel.ONLY_DEV],
    createOffer: [FeatureFlagLevel.EVERYONE],
    createProperty: [FeatureFlagLevel.EVERYONE],
    createSalesAgreement: [FeatureFlagLevel.EVERYONE],
    createSellingOffer: [FeatureFlagLevel.ONLY_DEV],
    createShareTrade: [FeatureFlagLevel.NONE],
    documentLibrary: [FeatureFlagLevel.NONE],
    duplicateProperty: [FeatureFlagLevel.EVERYONE],
    eSignature: [FeatureFlagLevel.ONLY_ADMIN],
    fiPurchaseOffer: [FeatureFlagLevel.NONE],
    fiDetailsOfSale: [FeatureFlagLevel.NONE],
    fiSalesAgreement: [FeatureFlagLevel.NONE],
    groupUser: [FeatureFlagLevel.ONLY_DEV],
    listProperties: [FeatureFlagLevel.EVERYONE],
    manualLead: [FeatureFlagLevel.ONLY_ADMIN],
    marketingConsent: [FeatureFlagLevel.NONE],
    matchMaking: [FeatureFlagLevel.ONLY_DEV],
    organizationSwitch: [FeatureFlagLevel.EVERYONE],
    quickSearch: [FeatureFlagLevel.ONLY_DEV],
    strandBot: [FeatureFlagLevel.ONLY_DEV],
    report: [FeatureFlagLevel.NONE],
    updatePropertyStatus: [FeatureFlagLevel.NONE],
    advertisements: [
      FeatureFlagLevel.ONLY_BETA,
      FeatureFlagLevel.ONLY_DEV,
      FeatureFlagLevel.ONLY_ADMIN,
    ],
    importExportTranslations: [FeatureFlagLevel.NONE],
    expenses: [
      FeatureFlagLevel.ONLY_BETA,
      FeatureFlagLevel.ONLY_DEV,
      FeatureFlagLevel.ONLY_ADMIN,
    ],
    eventLogged: [FeatureFlagLevel.ONLY_ADMIN],
    photoshoots: [FeatureFlagLevel.NONE],
    propertyGridView: [FeatureFlagLevel.NONE],
  },
} as const

/**
 * Dictionary of feature flags for each country and environment
 */
export const ORGANIZATION_FEATURE_FLAGS: Record<
  CountryNameEnum,
  Record<Environment, FeatureFlagDictionary>
> = {
  [CountryNameEnum.SPAIN]: {
    // It's recommended to use the default values for Spain
    local: {
      ...DEFAULT_FEATURE_FLAGS.local,
    },
    dev: {
      ...DEFAULT_FEATURE_FLAGS.dev,
      matchMaking: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
      groupUser: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
    },
    staging: {
      ...DEFAULT_FEATURE_FLAGS.staging,
    },
    production: {
      ...DEFAULT_FEATURE_FLAGS.production,
      matchMaking: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
      groupUser: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
    },
  },
  [CountryNameEnum.FINLAND]: {
    // Note: Remember to sort the feature flags alphabetically!
    local: {
      ...DEFAULT_FEATURE_FLAGS.local,
      createBrochureFI: [FeatureFlagLevel.EVERYONE],
      createDoS: [FeatureFlagLevel.ONLY_DEV],
      createOffer: [FeatureFlagLevel.ONLY_DEV],
      createProperty: [FeatureFlagLevel.EVERYONE],
      createSalesAgreement: [FeatureFlagLevel.ONLY_DEV],
      createSellingOffer: [FeatureFlagLevel.EVERYONE], // TODO: Check this value
      updatePropertyStatus: [FeatureFlagLevel.ONLY_DEV],
      createShareTrade: [FeatureFlagLevel.EVERYONE],
      fiPurchaseOffer: [FeatureFlagLevel.EVERYONE],
      fiSalesAgreement: [FeatureFlagLevel.EVERYONE],
      fiDetailsOfSale: [FeatureFlagLevel.EVERYONE],
      listProperties: [FeatureFlagLevel.EVERYONE],
      report: [FeatureFlagLevel.NONE],
    },
    dev: {
      ...DEFAULT_FEATURE_FLAGS.dev,
      createBrochureFI: [FeatureFlagLevel.EVERYONE],
      createBrokerageOffer: [FeatureFlagLevel.EVERYONE],
      createDoS: [FeatureFlagLevel.ONLY_DEV],
      createOffer: [FeatureFlagLevel.ONLY_DEV],
      createProperty: [FeatureFlagLevel.ONLY_ADMIN],
      createSalesAgreement: [FeatureFlagLevel.ONLY_DEV],
      createSellingOffer: [FeatureFlagLevel.EVERYONE], // TODO: Check this value
      updatePropertyStatus: [FeatureFlagLevel.ONLY_DEV],
      createShareTrade: [FeatureFlagLevel.ONLY_DEV],
      fiPurchaseOffer: [FeatureFlagLevel.EVERYONE],
      fiSalesAgreement: [FeatureFlagLevel.EVERYONE],
      fiDetailsOfSale: [FeatureFlagLevel.EVERYONE],
      listProperties: [FeatureFlagLevel.EVERYONE],
      report: [FeatureFlagLevel.NONE],
      matchMaking: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
      groupUser: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
    },
    staging: {
      ...DEFAULT_FEATURE_FLAGS.staging,
      createBrochureFI: [FeatureFlagLevel.EVERYONE],
      createDoS: [FeatureFlagLevel.ONLY_DEV],
      createOffer: [FeatureFlagLevel.ONLY_DEV],
      createProperty: [FeatureFlagLevel.NONE],
      createSalesAgreement: [FeatureFlagLevel.ONLY_DEV],
      updatePropertyStatus: [FeatureFlagLevel.ONLY_DEV],
      createShareTrade: [FeatureFlagLevel.NONE],
      listProperties: [FeatureFlagLevel.NONE],
      report: [FeatureFlagLevel.NONE],
    },
    production: {
      ...DEFAULT_FEATURE_FLAGS.production,
      createBrochureFI: [FeatureFlagLevel.EVERYONE],
      createDoS: [FeatureFlagLevel.ONLY_DEV],
      createOffer: [FeatureFlagLevel.ONLY_DEV],
      createProperty: [FeatureFlagLevel.NONE],
      createSalesAgreement: [FeatureFlagLevel.ONLY_DEV],
      updatePropertyStatus: [FeatureFlagLevel.ONLY_DEV],
      createShareTrade: [FeatureFlagLevel.NONE],
      report: [FeatureFlagLevel.ONLY_DEV],
      listProperties: [FeatureFlagLevel.NONE],
      groupUser: [
        FeatureFlagLevel.ONLY_ADMIN,
        FeatureFlagLevel.ONLY_DEV,
        FeatureFlagLevel.ONLY_BETA,
      ],
    },
  },
} as const
