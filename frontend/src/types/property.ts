import { FIPropertyRead } from '@/modules/fi-properties'
import { GetPropertiesParams } from './getPropertiesParams'
import { UserBasicInfo, UserRead } from './users'
import { SchemaContactListRead, SchemaContactRead } from '@/generated-types/api'

export enum PROPERTY_TYPE {
  EXIST_PROPERTY = 'exist_property',
  CUSTOM_PROPERTY = 'custom_property',
}

export enum CommissionTypeEnum {
  PERCENT = 'Percent',
  FIXED = 'Fixed',
}
export enum IVATaxEnum {
  TAX_INCLUDED = 'Tax included',
  TAX_ADDED = 'Tax added',
  NO_TAX = 'No tax',
}

export enum ListingTypeEnum {
  SALE = 'Sale',
  RENT_LONG = 'Rent Long Term',
  RENT_SHORT = 'Rent Short Term',
}

// finnish, english, spanish, german, swedish
export enum LanguageCodeEnum {
  DE = 'de',
  EN = 'en',
  ES = 'es',
  FI = 'fi',
  SV = 'sv',
}

export enum CurrencyEnum {
  EUR = 'EUR',
}

export enum DescriptionType {
  EXTRA = 'Extra',
  FULL = 'Full',
}

type LegacyData = {
  extPropertyId: string | null
  agencyref: string | null
  province: string | null
  area: string | null
  subareaId: number | null
  cityId: number | null
  cityName: string | null
  propertySubtype: number | null
  propertySubtypeName: string | null
  commercialType: number | null
  plotType: number | null
  saleStatus: number | null
  priceRentLong: number | null
  communityfee: number | null
  garbagetax: number | null
  ibi: number | null
  newDevelopment: number | null
  listedbyName: string | null
  listedbyEmail: string | null
  listedby2Name: string | null
  listedby2Email: string | null
  priority: number | null
  mainImgResized: string | null
  development: string | null
  newRow: number | null
  viewed: number | null
  saved: number | null
  isPublic: boolean
  isHidden: boolean
  etuoviPublic: number | null
  etuoviTitle: string | null
  etuoviDescriptionFi: string | null
  etuoviFlatstructure: string | null
}

export type PrivateInfo = {
  seller?: {
    name: string
  }
  location?: {
    address?: string
    city?: string
    postCode?: string
  }
}

export type Portals = {
  isStrandpropertiesEnabled: boolean
  isRightmoveEnabled: boolean
  isKyeroEnabled: boolean
  isThinkspainEnabled: boolean
  isResalesonlineEnabled: boolean
  isIdealistaEnabled: boolean
  isSpainforsaleEnabled: boolean
  isLeadingreEnabled: boolean
  isEtuoviEnabled: boolean
  isAplaceinthesunEnabled: boolean
  isLuxuryestateEnabled: boolean
  isPisosEnabled: boolean
  isJameseditionEnabled: boolean
  isInmobilienscout24Enabled: boolean
  isFotocasaEnabled: boolean
  isOikotieEnabled: boolean
}

// TODO: Change here the portals that are going to be shown in the Checkboxes
// TODO: And uncomment them from the respective states
export type StatusPortals = Omit<
  Portals,
  'isEtuoviEnabled' | 'isSpainforsaleEnabled' | 'isLuxuryestateEnabled'
>

export type PortalNames = {
  [key in keyof StatusPortals]: string
}

export const convertPortalsTypeToPortalNames: PortalNames = {
  isStrandpropertiesEnabled: 'Strandproperties.com',
  isRightmoveEnabled: 'Rightmove',
  isKyeroEnabled: 'Kyero',
  isThinkspainEnabled: 'Thinkspain',
  isResalesonlineEnabled: 'Resalesonline',
  isLeadingreEnabled: 'LeadingRE',
  isAplaceinthesunEnabled: 'A Place in the Sun',
  isPisosEnabled: 'Pisos',
  isJameseditionEnabled: 'JamesEdition',
  isIdealistaEnabled: 'Idealista',
  isInmobilienscout24Enabled: 'InmobilienScout24',
  isFotocasaEnabled: 'Fotocasa',
  isOikotieEnabled: 'Oikotie',
  // isLuxuryestateEnabled: 'Luxury Estate', // Temporarily disabled
  // isSpainforsaleEnabled: 'Spain for Sale',
  // isEtuoviEnabled: 'Etuovi',
}

export const convertPortalsTypeToPortalFilter: PortalNames = {
  isStrandpropertiesEnabled: 'strandproperties',
  isRightmoveEnabled: 'rightmove',
  isKyeroEnabled: 'kyero',
  isThinkspainEnabled: 'thinkspain',
  isResalesonlineEnabled: 'resalesonline',
  isLeadingreEnabled: 'leadingre',
  isAplaceinthesunEnabled: 'aplaceinthesun',
  isPisosEnabled: 'pisos',
  isJameseditionEnabled: 'jamesedition',
  isIdealistaEnabled: 'idealista',
  isInmobilienscout24Enabled: 'inmobilienscout24',
  isFotocasaEnabled: 'fotocasa',
  isOikotieEnabled: 'oikotie',
  // isLuxuryestateEnabled: 'luxuryestate', // Temporarily disabled
  // isSpainforsaleEnabled: 'Spain for Sale',
  // isEtuoviEnabled: 'Etuovi',
}

export const ALL_PORTALS: (keyof StatusPortals)[] = [
  'isStrandpropertiesEnabled',
  'isRightmoveEnabled',
  'isKyeroEnabled',
  'isThinkspainEnabled',
  'isResalesonlineEnabled',
  'isIdealistaEnabled',
  // 'isLuxuryestateEnabled', // Temporarily disabled
  'isLeadingreEnabled',
  'isAplaceinthesunEnabled',
  'isPisosEnabled',
  'isJameseditionEnabled',
  'isInmobilienscout24Enabled',
  'isFotocasaEnabled',
]

export const API_PORTALS: (keyof StatusPortals)[] = [
  'isRightmoveEnabled',
  'isInmobilienscout24Enabled',
  'isFotocasaEnabled',
  'isOikotieEnabled',
]

export const FI_API_PORTALS: (keyof StatusPortals)[] = ['isOikotieEnabled']

export const FI_PORTALS: (keyof StatusPortals)[] = ['isOikotieEnabled']

type TelecommunicationSystems = {
  telephoneNetwork: boolean
  generalCabling: boolean
  fiberCable: boolean
  unknown: boolean
}

type KeysAndHandoff = {
  protectedKeysTotal: number | null
  protectedKeysDelivered: number | null
  protectedKeysExisting: number | null
  unprotectedKeysTotal: number | null
  unprotectedKeysDelivered: number | null
  unprotectedKeysExisting: number | null
  otherKeysInfo: boolean
  otherKeysInfoPhoneNumber: string | null
  otherKeysInfoDescription: string | null
  strandPropertiesKeysInfo: boolean
  strandPropertiesKeysInfoOffice: string | null
  strandPropertiesKeysInfoNotes: string | null
  hasElectricity: boolean
  hasLights: boolean
}

type Renovations = {
  majorRenovationsPerformed: boolean
  describePerformedRenovations: string | null
  plannedRenovations: boolean
  describePlannedRenovations: string | null
  renovationsPerformedBeforeSellerOwnership: string | null
  describePreviousPerformedRenovations: string | null
}

type DamagesAndDefects = {
  defectsDamagesRepairObserved: boolean
  describeDefectsDamagesRepairs: string | null
  officialPermitsAcquired: boolean
  finalInspectionOfChanges: boolean
  describeRepairWork: string | null
}

type OtherDamages = {
  waterDamage: boolean
  moistureDamage: boolean
  timeOfDamage: string | null
  scopeOfDamage: string | null
  moldOrFungalProblems: boolean
  otherSpecialDamages: boolean
  causeOfDamage: string | null
  repairMethod: string | null
}

type KeysAndHandoffFormInput = Omit<
  KeysAndHandoff,
  'hasElectricity' | 'hasLights'
> & {
  hasElectricity: string
  hasLights: string
}

type RenovationsFormInput = Omit<
  Renovations,
  'majorRenovationsPerformed' | 'plannedRenovations'
> & {
  majorRenovationsPerformed: string
  plannedRenovations: string
}

type DamagesAndDefectsFormInput = Omit<
  DamagesAndDefects,
  | 'defectsDamagesRepairObserved'
  | 'officialPermitsAcquired'
  | 'finalInspectionOfChanges'
> & {
  defectsDamagesRepairObserved: string
  officialPermitsAcquired: string
  finalInspectionOfChanges: string
}

type OtherDamagesFormInput = Omit<
  OtherDamages,
  | 'waterDamage'
  | 'moistureDamage'
  | 'moldOrFungalProblems'
  | 'otherSpecialDamages'
> & {
  waterDamage: string
  moistureDamage: string
  moldOrFungalProblems: string
  otherSpecialDamages: string
}

export type PropertyModel = {
  id: number
  reference: string

  title: string | null
  slug: string | null

  dataSource: string | null

  areaLevel1Id: number | null
  areaLevel2Id: number | null
  areaLevel3Id: number | null
  areaLevel4Id: number | null
  areaLevel5Id: number | null

  location: string | null
  country: string | null
  city: string | null

  propertyTypeId: number
  propertyType: string | null

  bedrooms: number | null
  bathrooms: number | null

  pax: number | null

  priceSaleOld: number | null
  priceRentShortTermOld: number | null
  priceRentLongTermOld: number | null
  priceSale: number | null
  priceRentShortTerm: number | null
  priceRentLongTerm: number | null

  priceSquareMeter: number | null
  currency: string | null // Use ISO 4217 in uppercase

  builtYear: number | null
  builtArea: number | null

  interiorArea: number | null
  plotArea: number | null
  terraceArea: number | null

  levels: number | null
  floor: number | null

  organizationId: number

  status: StatusType

  commission: number | null
  commissionNotes: string | null
  commissionType: CommissionTypeEnum | null
  internalNotes: string | null
  ivaTax: IVATaxEnum | null

  latitude: number | null
  longitude: number | null
  isPublicCoordinates: boolean

  mainImg: string | null

  privateInfo: PrivateInfo

  isExclusive: boolean
  isStrandified: boolean

  portals: Portals

  condition: string | null

  legacyData: LegacyData

  legalRepresentative: {
    name: string
    mobile: string
    email: string
  } | null

  communalFees: number | null
  ibi: number | null
  garbageTax: number | null
  waterFee: number | null
  electricity: number | null

  cadastralReference: string | null

  buildingConstructor: string | null
  totalFloors: number | null
  buildingHasElevator: boolean

  foundationAndStructure: string | null
  roof: string | null
  exteriorWalls: string | null

  propertyHasCertificate: boolean
  certificateConsumptionRating: string | null
  certificateConsumptionValue: number | null
  certificateEmissionRating: string | null
  certificateEmissionValue: number | null

  roomsTotal: number | null
  toilets: number | null
  suiteBaths: number | null

  garageType: string | null
  parkingSpaces: number | null

  poolType: string | null

  gardenType: string | null

  telecommunicationSystems: TelecommunicationSystems

  keysAndHandoff: KeysAndHandoff

  renovations: Renovations

  damagesAndDefects: DamagesAndDefects

  otherDamages: OtherDamages
  hostawayPropertyId: null | string

  createdAt: string | null
  updatedAt: string | null
}

type PropertyBase = Pick<
  PropertyModel,
  | 'reference'
  | 'areaLevel1Id'
  | 'areaLevel2Id'
  | 'areaLevel3Id'
  | 'areaLevel4Id'
  | 'areaLevel5Id'
  | 'isExclusive'
  | 'isStrandified'
>

type PropertyDetail = PropertyBase &
  Pick<
    PropertyModel,
    | 'title'
    | 'dataSource'
    | 'bathrooms'
    | 'bedrooms'
    | 'location'
    | 'country'
    | 'city'
    | 'pax'
    | 'priceSaleOld'
    | 'priceRentShortTermOld'
    | 'priceRentLongTermOld'
    | 'priceSale'
    | 'priceRentShortTerm'
    | 'priceRentLongTerm'
    | 'priceSquareMeter'
    | 'currency'
    | 'builtArea'
    | 'builtYear'
    | 'interiorArea'
    | 'plotArea'
    | 'terraceArea'
    | 'levels'
    | 'floor'
    | 'organizationId'
    | 'commission'
    | 'commissionNotes'
    | 'commissionType'
    | 'internalNotes'
    | 'ivaTax'
    | 'latitude'
    | 'longitude'
    | 'isPublicCoordinates'
    | 'mainImg'
    | 'legalRepresentative'
  >

export type ManyToManyRead = {
  id: number
  name: string
}

export type FeaturesRead = ManyToManyRead & {
  featureGroupName: string
}

export type DescriptionsRead = {
  tagline: string
  description: string
  type: DescriptionType
  language: LanguageCodeEnum
}

export type FilesRead = {
  id: number
  key: string
}

export enum DocumentStatus {
  DRAFT = 'Draft',
  PENDING = 'Pending',
  SIGNED = 'Signed',
  ARCHIVED = 'Archived',
  GENERIC = 'Generic',
}

export type DocumentsRead = {
  id: number
  name: string
  type: string
  visibility: string
  sowiseId: string
  status: string
  language: string
  contacts: SchemaContactListRead[]
  expiresAt: string
  createdAt: string
  updatedAt: string
}

export type ReadProperty = PropertyDetail &
  Pick<
    PropertyModel,
    | 'id'
    | 'slug'
    | 'createdAt'
    | 'updatedAt'
    | 'status'
    | 'propertyType'
    | 'condition'
    | 'legacyData'
    | 'privateInfo'
    | 'portals'
    | 'propertyTypeId'
    | 'cadastralReference'
    | 'communalFees'
    | 'ibi'
    | 'garbageTax'
    | 'waterFee'
    | 'electricity'
    | 'buildingConstructor'
    | 'totalFloors'
    | 'buildingHasElevator'
    | 'foundationAndStructure'
    | 'roof'
    | 'exteriorWalls'
    | 'propertyHasCertificate'
    | 'certificateConsumptionRating'
    | 'certificateConsumptionValue'
    | 'certificateEmissionRating'
    | 'certificateEmissionValue'
    | 'roomsTotal'
    | 'toilets'
    | 'suiteBaths'
    | 'garageType'
    | 'parkingSpaces'
    | 'poolType'
    | 'gardenType'
    | 'telecommunicationSystems'
    | 'keysAndHandoff'
    | 'renovations'
    | 'damagesAndDefects'
    | 'otherDamages'
    | 'hostawayPropertyId'
  > & {
    orientations: ManyToManyRead[]
    listingTypes: ManyToManyRead[]
    features: FeaturesRead[]
    settings: ManyToManyRead[]
    views: ManyToManyRead[]
    descriptions: DescriptionsRead[]
    realtorUsers: UserRead[] | null
    documents: DocumentsRead[]
    files: FilesRead[]
    contacts: SchemaContactRead[] | null
    garageTypes: ManyToManyRead[]
    poolTypes: ManyToManyRead[]
    gardenTypes: ManyToManyRead[]
    videoStreams: { id: number; url: string; isHidden: boolean }[]
    videoTours: { id: number; url: string; isHidden: boolean }[]
    soldBy: SoldByEnum | null
    developmentName: null | string
    detailsOfSales: null | { id: number }[]
  }

export type ReadPropertyWithMetadata = ReadProperty & {
  metadata?: {
    isFavorite: boolean | undefined
  }
}

export type EditPropertyMutationProps = PropertyDetail &
  Pick<
    PropertyModel,
    | 'privateInfo'
    | 'legacyData'
    | 'propertyTypeId'
    | 'condition'
    | 'cadastralReference'
    | 'communalFees'
    | 'ibi'
    | 'garbageTax'
    | 'waterFee'
    | 'electricity'
    | 'buildingConstructor'
    | 'totalFloors'
    | 'buildingHasElevator'
    | 'foundationAndStructure'
    | 'roof'
    | 'exteriorWalls'
    | 'propertyHasCertificate'
    | 'certificateConsumptionRating'
    | 'certificateConsumptionValue'
    | 'certificateEmissionRating'
    | 'certificateEmissionValue'
    | 'roomsTotal'
    | 'toilets'
    | 'suiteBaths'
    | 'garageType'
    | 'parkingSpaces'
    | 'poolType'
    | 'gardenType'
    | 'telecommunicationSystems'
    | 'keysAndHandoff'
    | 'renovations'
    | 'damagesAndDefects'
    | 'otherDamages'
  > & {
    featureIds: number[] | null
    orientationIds: number[] | null
    listingTypeIds: number[] | null
    settingIds: number[] | null
    viewIds: number[] | null
    realtorUserIds: number[] | null
    descriptions: DescriptionsRead[] | null
    contactIds: number[] | null
    developmentName: null | string
  }

export type EditPropertyFormTypes = PropertyDetail &
  Pick<
    PropertyModel,
    | 'hostawayPropertyId'
    | 'legacyData'
    | 'propertyTypeId'
    | 'condition'
    | 'cadastralReference'
    | 'communalFees'
    | 'ibi'
    | 'garbageTax'
    | 'waterFee'
    | 'electricity'
    | 'buildingConstructor'
    | 'totalFloors'
    | 'buildingHasElevator'
    | 'foundationAndStructure'
    | 'roof'
    | 'exteriorWalls'
    | 'certificateConsumptionRating'
    | 'certificateConsumptionValue'
    | 'certificateEmissionRating'
    | 'certificateEmissionValue'
    | 'roomsTotal'
    | 'toilets'
    | 'suiteBaths'
    | 'garageType'
    | 'parkingSpaces'
    | 'poolType'
    | 'gardenType'
    | 'telecommunicationSystems'
  > & {
    featureIds: number[] | null
    orientationIds: number[] | null
    listingTypeIds: number[] | null
    garageTypeIds: number[] | null
    poolTypeIds: number[] | null
    gardenTypeIds: number[] | null
    settingIds: number[] | null
    viewIds: number[] | null
    realtorUserIds: number[] | null
    descriptions: DescriptionsRead[] | null
    contactIds: number[] | null
    address: string | null
    city: string | null
    postCode: string | null

    propertyHasCertificate: string | null
    keysAndHandoff: KeysAndHandoffFormInput
    renovations: RenovationsFormInput
    damagesAndDefects: DamagesAndDefectsFormInput
    otherDamages: OtherDamagesFormInput

    videoTours: string[] | null
    videoStreams: string[] | null
    developmentName: null | string
  }

export type ListProperties = PropertyBase &
  Pick<
    PropertyModel,
    | 'id'
    | 'reference'
    | 'location'
    | 'propertyType'
    | 'status'
    | 'priceSaleOld'
    | 'priceRentShortTermOld'
    | 'priceRentLongTermOld'
    | 'priceSale'
    | 'priceRentShortTerm'
    | 'priceRentLongTerm'
    | 'currency'
    | 'commission'
    | 'commissionType'
    | 'plotArea'
    | 'builtArea'
    | 'bathrooms'
    | 'bedrooms'
    | 'mainImg'
    | 'dataSource'
    | 'createdAt'
    | 'updatedAt'
  > & {
    realtorUsers: UserBasicInfo[] | null
    listingTypes: ManyToManyRead[]
    soldBy: SoldByEnum | null
  }

export type ListPropertiesExtend = ListProperties & {
  isFavorite: boolean | undefined | null
  clickCount: number | undefined
}

export type CreatePropertyMutationProps = Pick<
  PropertyModel,
  | 'privateInfo'
  | 'commission'
  | 'commissionType'
  | 'priceSale'
  | 'priceRentShortTerm'
  | 'priceRentLongTerm'
> & {
  organizationId: number
  realtorUserIds: number[]
  listingTypeIds: number[]
  propertyTypeId: number
  isExclusive: boolean
  isStrandified: boolean
  areaLevel1Id: number | undefined
  areaLevel2Id: number | undefined
  areaLevel3Id: number | undefined
  areaLevel4Id: number | undefined
  areaLevel5Id: number | undefined
  contactIds?: number[] | null
  developmentName: null | string
}

export type CreatePropertyFormTypes = {
  organizationId: number
  realtorUserIds: number[]
  listingTypeIds: number[]
  propertyTypeId: number
  isExclusive: boolean
  isStrandified: boolean
  areaLevel1Id: number | undefined
  areaLevel2Id: number | undefined
  areaLevel3Id: number | undefined
  areaLevel4Id: number | undefined
  areaLevel5Id: number | undefined
  commission: number
  commissionType: CommissionTypeEnum
  priceSale: number | undefined
  priceRentShortTerm: number | undefined
  priceRentLongTerm: number | undefined
  address: string | undefined
  city: string | undefined
  postCode: string | undefined
  cadastralReference: string | undefined
  contactIds?: number[] | null
  developmentName: null | string
}

export const Status = [
  'Archived',
  'Cancelled',
  'Draft',
  'Published',
  'Sold',
] as const
export enum StatusEnum {
  ARCHIVED = 'Archived',
  CANCELLED = 'Cancelled',
  DRAFT = 'Draft',
  PUBLISHED = 'Published',
  SOLD = 'Sold',
}
export type StatusType = (typeof Status)[number]

export const FilterDataSources = ['strand', 'network'] as const
export type FilterDataSourceType = (typeof FilterDataSources)[number]

export enum DataSource {
  STRAND = 'strand',
  RESALES_ONLINE = 'resales',
  INMOBALIA = 'inmoba',
  KIVI = 'kivi',
}

export const DataSourceNames = {
  strand: 'Strand',
  resales: 'Resales Online',
  inmoba: 'Strand',
  kivi: 'Kivi',
} as const

export type PropertyFilterProps = GetPropertiesParams & {
  areasFullHierarchy?: string[]
}

export type PropertyType = {
  id: number
  name: string
  category: string
  origin: string
}

export const Conditions = [
  'Excellent',
  'Good',
  'Fair',
  'Renovation required',
  'Restoration required',
] as const

export type PropertyListingType = {
  id: number
  name: string
}

export type NestedArea = {
  id: number
  name: string
  level: number
  subareas: NestedArea[]
}

export type PropertyApiResponse = {
  records: ListProperties[]
  metadata: {
    pageCount: number
    page: number
    pageSize: number
    totalCount: number
  }
}

export type PropertyImage = {
  url: string
  isHidden: boolean
  id: number
  // TO DO: Those two properties are missing in the API response but required in the UI
  uploadedDate?: Date
  author?: string
}

export type PropertyImagesResponse = PropertyImage[]

export type PropertyDocument = {
  id: string
  name: string
  type: string
  visibility: string
  sowiseId: string
  status: string
  language: string
  contacts: {
    id: string
    name: string
  }[]
  expiresAt: string
  createdAt: string
  updatedAt: string
}

export type PropertyDocumentsResponse = {
  records: PropertyDocument[]
  metadata: {
    pageCount: number
    page: number
    pageSize: number
    totalCount: number
  }
}

export type PropertyLocationsFilterParams = {
  page?: number
  pageSize?: number
  keyword?: string
  hideAreasWithoutProperties?: boolean
}

export type PropertyLocationsApiResponse = {
  records: NestedArea[]
  metadata: {
    pageCount: number
    page: number
    pageSize: number
    totalCount: number
  }
}

export type PropertyValidation = {
  salesAgreementValid: boolean
  imagesQuality: ImagesQuality
  missingFields: string[]
}

export type PropertyVideo = {
  url: string
  isHidden: boolean
  id: number
}

export enum OrderByEnum {
  LATEST = 'latest',
  OLDEST = 'oldest',
  LATEST_STRAND = 'latest_strand',
  OLDEST_STRAND = 'oldest_strand',
  LATEST_UPDATED = 'latest_updated',
  OLDEST_UPDATED = 'oldest_updated',
  LOWEST_SALE_PRICE = 'lowest_sale_price',
  HIGHEST_SALE_PRICE = 'highest_sale_price',
  LOWEST_PRICE = 'lowest_price',
  HIGHEST_PRICE = 'highest_price',
  LOWEST_PRICE_M2 = 'lowest_price_m2',
  HIGHEST_PRICE_M2 = 'highest_price_m2',
  ALPHABETICAL_ASC = 'alphabetical_asc',
  ALPHABETICAL_DESC = 'alphabetical_desc',
}

export enum ImagesQuality {
  GOOD = 'good',
  POOR = 'poor',
  INSUFFICIENT = 'insufficient',
}

export enum SoldByEnum {
  STRAND = 'Strand',
  OTHER = 'Other',
}

export type ESPropertyRead = ReadProperty

export type PropertyRead = ESPropertyRead | FIPropertyRead
