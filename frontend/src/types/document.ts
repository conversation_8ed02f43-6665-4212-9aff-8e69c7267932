import { SIGN_METHODS } from './common'
import { LanguageCodeEnum } from './property'

export type SalesAgreementDocs = {
  identityDocument: boolean | null
  publicDeedOrNotaSimple: boolean | null
  energyPerformanceCertificate: boolean | null
  firstOccupancyLicense: boolean | null
  urbanContributionTaxBill: boolean | null
  rubbishCollectionFeesReceipt: boolean | null
  communityFeesReceipt: boolean | null
  certificateOfCommunityPropertyOwners: boolean | null
  certificateOfTaxResidence: boolean | null
  legalRepresentativeAndPowerOfAttorney: boolean | null
  thePowersInCaseOfSociety: boolean | null
  separationRuling: boolean | null
  prenuptialAgreement: boolean | null
  certificateOfInheritance: boolean | null
  certificateOfOutstandingDebt: boolean | null
  utilityBill: boolean | null
}

export type SalesAgreementProps = {
  propertyId: number
  realtorId: number
  sellerIds: number[]
  validityInMonths: number
  documents: SalesAgreementDocs | null
  documentLanguage: LanguageCodeEnum
  levelOfSignature: SIGN_METHODS
  companyReference?: string
  liabilityInsurance?: string
  roaiib?: string
}

export type SalesAgreementRead = {
  propertyId: number
  realtorId: number
  sellerIds: number
  validityInMonths: number
  documents: SalesAgreementDocs | null
  documentLanguage: LanguageCodeEnum
  levelOfSignature: SIGN_METHODS
  documentId?: string
}
