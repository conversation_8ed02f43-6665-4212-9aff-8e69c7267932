import { ApiResponseWithMetadata, SortProps } from './common'
import { UserBasicInfo } from './users'

export enum EXPENSE_TRANSACTION_TYPE {
  EXPENSE = 'expense',
  DEPOSIT = 'deposit',
  PAYOUT = 'payout',
  GIFT = 'gift',
}

export enum EXPENSE_TRANSACTION_STATUS {
  SUCCEEDED = 'succeeded',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  PROCESSING = 'processing',
  REQUIRES_ACTION = 'requires_action',
  ON_HOLD = 'on_hold',
}

export interface ExpenseTransactionTargetCreate {
  targetType: string
  targetId: number
}

export interface ExpenseTransactionTargetRead
  extends ExpenseTransactionTargetCreate {
  id: number
  reference?: string
}

export interface ExpenseTransactionBase {
  userId: number
  amount: number
  type: EXPENSE_TRANSACTION_TYPE
  description?: string
  meta?: Record<string, unknown>
  stripeTransactionId?: string
  status: EXPENSE_TRANSACTION_STATUS
}

export interface ExpenseTransactionCreate extends ExpenseTransactionBase {
  targets?: ExpenseTransactionTargetCreate[]
}

export interface ExpenseTransactionRead extends ExpenseTransactionBase {
  id: number
  user: UserBasicInfo
  createdAt: string
  updatedAt: string
  targets?: ExpenseTransactionTargetRead[]
}

export interface CreateExpenseTransactionArgs {
  userId?: number
  amount: number
  type: EXPENSE_TRANSACTION_TYPE
  description?: string
  metadata?: Record<string, unknown>
  stripeTransactionId?: string
  targets?: ExpenseTransactionTargetCreate[]
}

export type ExpenseTransactionListApiResponse =
  ApiResponseWithMetadata<ExpenseTransactionRead>

export interface ConnectExpenseAccountResponse {
  status: string
  stripeCustomerId: string
  clientSecret: string
}

export interface ExpenseTransactionFilterParams
  extends Record<string, string | string[] | undefined>,
    SortProps {
  types?: string[]
  statuses?: string[]
  languages?: string[]
  page?: string | string[]
  pageSize?: string | string[]
  keyword?: string
  filterByMe?: string
  users?: string[]
}

export enum ExpenseTransactionTargetEntity {
  ADVERTISEMENT = 'advertisement',
  PROPERTY = 'property',
  FI_PROPERTY = 'fi_property',
}

export const ExpenseTransactionTargetsProps = {
  [ExpenseTransactionTargetEntity.ADVERTISEMENT]: {
    translationKey: 'adsManager.ad',
    route: null,
  },
  [ExpenseTransactionTargetEntity.PROPERTY]: {
    translationKey: 'property',
    route: null,
  },
  [ExpenseTransactionTargetEntity.FI_PROPERTY]: {
    translationKey: 'property',
    route: null,
  },
} as const
