import { <PERSON>rror<PERSON>abel } from '@/generated-types/api'
import { isAxiosError } from 'axios'
import { TFunction } from 'i18next'

export function isSchemaErrorLabel(label: unknown): label is ErrorLabel {
  return (
    typeof label === 'string' &&
    Object.values(ErrorLabel).includes(label as <PERSON>rror<PERSON>abel)
  )
}

export function getErrorLabel(
  t: TFunction,
  error: unknown
): string | undefined {
  if (!isAxiosError(error)) return undefined

  const label = error.response?.data?.label
  return isSchemaErrorLabel(label)
    ? translateErrorLabel(t, label)
    : t('somethingWentWrong') ?? ''
}

export const translateErrorLabel = (t: TFunction, label: ErrorLabel) => {
  switch (label) {
    case ErrorLabel.create_document_signing_user_missing_name:
      return t('errors.createDocumentSigningUserMissingName')
    case ErrorLabel.create_document_signing_contact_missing_name:
      return t('errors.createDocumentSigningContactMissingName')
    case ErrorLabel.create_document_signing_at_least_one_signer_required:
      return t('errors.createDocumentSigningAtLeastOneSignerRequired')
    case ErrorLabel.create_document_signing_ssn_invalid_or_missing:
      return t('errors.createDocumentSigningSsnInvalidOrMissing')
    case ErrorLabel.add_signer_document_signing_not_found:
      return t('errors.addSignerDocumentSigningNotFound')
    case ErrorLabel.add_signer_document_signing_not_allowed_status:
      return t('errors.addSignerDocumentSigningNotAllowedStatus')
    case ErrorLabel.add_signer_signer_already_added:
      return t('errors.addSignerSignerAlreadyAdded')
    case ErrorLabel.delete_document_signer_not_allowed_status:
      return t('errors.deleteDocumentSignerNotAllowedStatus')
    case ErrorLabel.delete_document_signer_not_found:
      return t('errors.deleteDocumentSignerNotFound')
    case ErrorLabel.create_document_signing_only_pdf_files_supported:
      return t('errors.createDocumentSigningOnlyPdfFilesSupported')
    default:
      return t('somethingWentWrong')
  }
}
