{"enums": {"contactType": {"Person": "Person", "Organization": "Company", "Estate": "Estate", "notSpecified": "Not specified"}}, "organizationSettings": {"organizationSettings": "Organization settings", "title": "Settings", "adTemplates": {"errors": {"imageUrl": "Invalid image URL", "smartyIdRequired": "Smarty ID is required", "create": "Failed to create template", "update": "Failed to update template", "delete": "Failed to delete template", "imageRequired": "Image is required"}, "create": "Create template", "update": "Update template", "delete": "Delete template", "imageUrl": "Image URL", "adTemplates": "Ad templates", "addTemplate": "Add template"}}, "expenses": {"listPlaceholder": {"button": "Connect card", "hasCard": {"title": "No expenses yet", "description": "You haven't recorded any expenses. Once you start using your card, your transactions will appear here."}, "noCard": {"title": "Connect your card", "description": "Add your card details to manage your expenses", "button": "Connect card"}, "default": {"title": "No expenses yet", "description": ""}}, "modal": {"removeCard": {"title": "Remove card", "message": "Are you sure you want to remove your card? You'll be able to still view your past expenses, but you'll need to connect a card again if you want to continue managing new ones."}}, "messages": {"cardRemoved": {"title": "Card removed", "description": "", "error": "Something went wrong while removing the card"}}, "connected_card": "Connected card", "remove_card": "Remove card", "tab": "Expenses", "expensed": "Expensed", "deposited": "Deposited", "on_hold": "On Hold", "transactions": "Transactions", "wallet_balance": "Wallet balance", "connect_card": {"title": "Connect your card to manage your expenses", "button_text": "Connect card"}, "stripe": {"setupInstructions": "Fill in your credit card"}, "type": {"expense": "Expense", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "payout": "Payout", "gift": "Gift"}, "status": {"cancelled": "Cancelled", "failed": "Failed", "processing": "Processing", "requires_action": "Requires action", "on_hold": "On Hold", "succeeded": "Succeeded"}}, "adsManager": {"copyLink": "Copy link", "ad": "Ad", "createTitle": "Create Ad", "updateTitle": "Update Ad", "end_ad": "End ad", "ad_type": "Ad type", "types": {"listing_property": "Listing property", "property_sold": "Property sold", "agent": "Agent", "event": "Event", "custom": "Custom"}, "languages": {"en": "English", "fi": "Finnish", "es": "Spanish"}, "errors": {"eventRequired": "Please select both a property and an event for this ad type", "duplicate": {"title": "Something went wrong while trying to duplicate ad"}, "cancel": {"title": "Something went wrong while trying to cancel ad"}, "publish": {"title": "Something went wrong while trying to publish ad"}, "budget": {"is_nan": "The budget has to be a number", "range": "Budget must be between €4 and €9999"}, "start_date_end_date": "The start date must occur before the end date.", "required": "This field is required.", "propertyRequired": "Please select a property for this ad type", "budgetTotal": {"is_nan": "The budget has to be a number", "range": "Budget must be between €4 and €9999"}, "end": {"title": "Something went wrong while trying to end ad"}}, "table": {"client_preview": "Client preview", "copy_url": "Copy url", "messages": {"duplicate": {"title": "Ad duplicated", "description": "{{entity}} duplicated"}, "cancel": {"title": "Draft deleted", "description": "{{entity}} deleted"}, "publish": {"title": "Ad completed", "description": "{{entity}} completed"}, "url_copied": {"title": "", "description": "Url copied"}}, "headers": {"status": {"title": "Status"}, "date": {"title": "Date"}, "impressions": {"title": "Impressions", "tooltip": "Number of times the ad was displayed"}, "link_clicks": {"title": "Link clicks", "tooltip": "Number of clicks on the ad link"}, "cpm": {"title": "Cost per mille (CPM)", "tooltip": "Average cost per 1,000 impressions"}, "ctr": {"title": "Click-through rate (CTR)", "tooltip": "Percentage of impressions that resulted in clicks"}, "cpc": {"title": "Cost per link click (CPC)", "tooltip": "Average cost per click on the ad link"}, "ad_preview": {"title": "Ad preview"}}, "errors": {"duplicate": {"title": "Failed to duplicate ad"}, "end": {"title": "Failed to end ad"}}}, "status": {"draft": "Draft", "in_review": "In review", "active": "Active", "completed": "Completed"}, "advertisements": "Advertisements", "selectPictures": "Select pictures", "listPlaceholder": {"title": "Create your first ad", "description": "You will find them here with all the relevant information about them.", "createAd": "Create ad"}, "modal": {"imagesSelected": "Selected {{number}} photos", "review": {"adFocus": "AD FOCUS", "adType": "Ad type", "property": "Property", "event": "Event", "dateRange": "Date Range", "adDetails": "AD DETAILS", "date": "Date", "budget": "Budget", "perDay": "per day", "targetArea": "Target area", "content": "CONTENT", "language": "Language"}, "fillContent": {"warning": "Actual ad preview may vary depending on the platform, device, and display settings.", "title": "Ad content", "headline": "Headline", "description": "Description", "primaryText": "Primary text"}, "runData": {"targetArea": "Target area", "municipality": "City", "endDate": "End date", "startDate": "Start date", "targetAreaTitle": "Target area", "country": "Country", "metaReviewNotice": "It might take up to 24 hours for Meta to review and publish the ad.", "dailyCost": "Daily cost", "language": "Language", "title": "Ad run", "budgetTitle": "Budget", "totalCampaignBudget": "Total campaign budget", "locationRadiusDisclaimer": "The location adds a +50km radius always, as specified by Meta's targeting settings.", "targetRadiusKm": "Target radius (in km)"}, "adIsReady": {"title": "Your ad is ready!", "description": "It might take up to two days for Meta to review and display the ad to users."}, "selectProperty": "Select property", "selectEvent": "Select event", "adType": "Ad type", "type": {"listingProperty": {"label": "Listing property", "subText": "Promote a property currently on the market to attract potential buyers."}, "agent": {"label": "Agent", "subText": "Highlight yourself as an agent to attract new clients."}, "event": {"label": "Events", "subText": "Promote events like open houses, property showcases, or community gatherings."}, "propertySold": {"label": "Sold property", "subText": "Celebrate a successful sale and showcase your expertise to future clients."}, "custom": {"label": "Custom", "subText": "Create an ad from scratch with full control over its content and purpose."}}, "errors": {"noEventsAvailable": "Please select a property with available events"}}, "messages": {"duplicate": {"title": "Ad duplicated", "description": "{{entity}} duplicated."}, "cancel": {"title": "Ad canceled", "description": "{{entity}} canceled."}, "end": {"description": "Ad ended"}, "publish": {"title": "Ad published", "description": "{{entity}} published."}}}, "signing": {"sendForSigning": "Send for signing", "lastSigningDate": "Last signing date", "comment": "Comment", "signers": "Signers", "otherSigners": "Other signers", "addAssignees": "Add assignees", "signingEvents": {"signer_signed": "signed", "signer_declined": "declined signing", "signing_completed": "Signing completed"}, "selectedDocuments": "Selected documents", "addSigner": "Add signer", "signersAdded": "Signers added", "failedAddingSigners": "Failed to add signer", "signer": "Signer", "reminderSent": "<PERSON><PERSON><PERSON> sent", "failedSendingReminder": "Failed to send reminder", "signerRemoved": "Signer removed", "failedRemovingSigner": "Failed to remove signer", "signerAdded": "Signer added", "signingDeleted": "Signing canceled", "failedDeletingSigning": "Failed to cancel signing", "deleteSigning": "Cancel signing", "deleteSigningDescription": "Are you sure you want to cancel this signing?"}, "fiSalesAgreement": {"tabTitle": "Sales Agreement", "editStep": "Fill", "validateStep": "Validate", "signatureStep": "Signatures", "completedStep": "Complete contract", "salesAgreement": "Sales Agreement", "salesAgreements": "Sales agreements", "noSalesAgreements": "The property does not have a sales agreement yet.", "noSalesAgreementsDescription": "The property must have a signed sales agreement and an appendix in order for you to publish it.", "create": "Create new sales agreement", "validateHeading": "Step 2: Validate", "validateDescription": "Check every field. Once complete next step is to add signees.", "saveAsDraft": "Save as draft", "submit": "Submit", "addAssignees": "Add assignees", "showAssignees": "Show assignees", "nextStep": "Next step", "created": "Sales agreement created", "updated": "Sales agreement updated", "copyAsNew": "<PERSON><PERSON> as new", "fetchSigningStatus": "Fetch signing status", "sendReminder": "Send reminder", "addSigneesHeading": "Step 3: Add signees", "addSigneesDescription": "Add signees to the sales agreement. You can add multiple signees.", "signersHeading": "Step 3: Signers", "digitalSigners": "Digital signers", "signed": "Signed", "signingDeadline": "Signing deadline", "pendingSignature": "Pending signature", "missingSignatures": "Missing signatures: ", "failedSendingReminder": "Failed sending reminder", "instructions": {"validity": "The validity of the assignment always ends four months after the start of its validity unless it is extended in writing in accordance with the requirements of the Brokerage Act. The four-month maximum validity period does not apply to agreements where the subject of the assignment is part of the client's business operations. A contract valid until further notice may be terminated in writing no later than ten days before the start of the next contract period.", "usedResidence": "If the continuous residence time for each partial owner during their ownership period is not at least two years, a capital gains tax will be imposed on the sale if a profit is made.", "tax": "If it is not possible to determine the capital gains tax, the client should be directed to contact the tax administration to clarify the tax consequences."}, "form": {"basicInformationHeading": "Basic information", "realtor": "Realtor", "contact": "Contact", "contentHeading": "Content", "priceHeading": "Price, selling-time and other sales-related considerations", "priceHeaderRow": "Price", "availabilityHeading": "Vacation, tenancy, rights of use", "availabilityHeaderRow": "Availability", "tenantHeaderRow": "Tenant", "rightsOfUseHeaderRow": "Right of use", "validityHeading": "Terms of the assignment", "commissionHeading": "Commission and other expenses", "commissionHeaderRow": "Commissions", "otherCostsHeaderRow": "Other expenses", "termsHeading": "Declarations by the parties", "announcementsHeaderRow": "Consentor parties", "consentHeaderRow": "Consenters", "consentersLabel": "Consenters", "otherHeaderRow": "Other", "otherTermsHeaderRow": "Other terms and conditions", "digitalSellingHeading": "Digital trading", "agreedMarketingMethodsLabel": "Agreed marketing methods", "separateMarketingAppendixLabel": "Separate appendix on marketing", "unencumberedPriceRequestLabel": "Debt-free asking price", "unencumberedPriceRequestEstimateLabel": "Estimate of the real debt-free price", "sharesIncludeLoanLabel": "Shares include loan", "loanAmountLabel": "Loan amount", "loanDetailsLabel": "Loan details", "actualPriceLabel": "Selling price", "priceIncludingLoanLabel": "Selling price", "paymentTermsLabel": "Payment terms", "paymentTermOtherLabel": "Other payment terms", "averageSellingTimeEstimateLabel": "Estimate of average selling time", "factorsAffectingSalesLabel": "Factors affecting sales", "availabilityLabel": "Availability", "dateWhenAvailableLabel": "Date when available", "availabilityDetailsLabel": "More information about availability", "tenantNameLabel": "Tenant's name", "tenantContactDetailsLabel": "Tenant's contact details", "leaseAgreementLabel": "Lease agreement", "leaseAgreementTermLabel": "Lease terms", "leaseStartDateLabel": "Lease start date", "leaseEndDateLabel": "Lease end date", "leaseTerminatedLabel": "Lease is terminated", "tenantHasPaidRentOnTimeLabel": "Tenant has paid rent on time", "leaseAmountLabel": "Lease amount", "leaseDepositLabel": "Lease deposit", "tenantPayingRentDetailsLabel": "More information about paying the rent", "leaseAgreementDetailsLabel": "Additional information about the lease agreement", "restrictiveRightOfUserLabel": "Restrictive rights of use", "restrictiveRightOfUserDetailsLabel": "More information about the restrictive rights of use", "writtenConsentToTransferLabel": "Written consent of transfer", "belongsToBusinessActivitiesLabel": "Realty belongs to customer business activities", "assignmentValidityLabel": "Validity", "assignmentValidityRenewalPeriodLabel": "Validity renewal period", "startDateLabel": "Start date", "endDateLabel": "End date", "commissionBasisCodeLabel": "Commission basis code", "commissionTypeLabel": "Commission type", "vatLabel": "Includes VAT", "commissionPercentageLabel": "Commission percentage", "commissionFixedLabel": "Fixed commission", "commissionDetailsLabel": "Commission details", "marketingExpensesMaxLabel": "Maximum marketing expences incl. VAT", "documentAcquisitionExpensesMaxLabel": "Maximum document acquisition expenses incl. VAT", "otherExpensesDetailsLabel": "Other expenses", "expenseIfNoCommissionLabel": "Expenses if seller is not obliged to pay commission", "acquisitionLabel": "Acquisition", "sellerIsMarriedOrInRegisteredRelationshipLabel": "The seller is been married or living in a registered partnership", "sellerHasBeenMarriedOrInRegisteredRelationshipLabel": "The seller has been married or living in a registered partnership", "sellerHasSpousesConsentLabel": "<PERSON><PERSON> has spouses consent", "divorceLegallyBindingLabel": "Divorce is legally binding", "legalPartitioningIsCompleteLabel": "Legal partitioning of the assets is completed", "acquisitionDateLabel": "Acquisition date", "acquisitionCostLabel": "Acquisition cost", "clientHasUsedResidenceAsResidenceLabel": "The client has used at least half of the housing stock continuously as permanent residence during the ownership period?", "residencyStartDateLabel": "Residency start date", "residencyEndDateLabel": "Residency end date", "shareRegisterFormatLabel": "Share register format", "shareRegisterStorageLabel": "Share register storage", "taxConsequencesLabel": "Tax consequence", "unpaidMaintenanceChargeLabel": "Unpaid maintenance charge", "unpaidMaintenanceChargeAmountLabel": "Amount unpaid", "digitalTradingAllowedLabel": "Digital trading allowed", "isDomesticSaleLabel": "Is domestic sale", "startAssignmentImmediatelyLabel": "Start assignment immediately", "startMarketingAfterCancelPeriodLabel": "Start marketing after cancellation period", "customerAskedToReadPrivacyPolicyLabel": "The customer was asked to read the privacy policy before signing", "previousExternalSalesAgreementLabel": "Has had external sales agreement within last 6 months", "previousExternalSalesAgreementDetailsLabel": "External sales agreement details", "additionalDetailsLabel": "Additional details and conditions", "lastSigningDate": "Last signing date", "signers": "Signers", "instructions": {"validity": "The validity of the assignment always ends four months after the start of its validity unless it is extended in writing in accordance with the requirements of the Brokerage Act. The four-month maximum validity period does not apply to agreements where the assignment relates to the client's business operations. An open-ended agreement can be terminated in writing no later than ten days before the start of the next contract period.", "usedResidence": "If the continuous residence period for each partial owner during their ownership is less than two years, capital gains tax will be imposed on the sale if a profit is made.", "tax": "If it is not possible to determine the capital gains tax, the client should be advised to contact the tax administration for clarification of tax consequences."}, "enums": {"status": {"draft": "Draft", "validated": "Validated", "pending_signatures": "Pending signatures", "completed": "Completed"}, "paymentTerms": {"cash": "Cash", "exchange": "Exchange", "other": "Other"}, "availability": {"immediately": "Immediately", "negotiable": "Negotiable", "rented": "Rented", "date": "Date", "other": "Other"}, "leaseAgreement": {"written_agreement": "Written", "oral_agreement": "Oral"}, "period": {"indefinite": "Indefinite", "fixed": "Fixed"}, "commissionBasisCode": {"debt_free_purchase_price": "Debt-free purchase price", "purchase_price": "Purchase price"}, "commissionType": {"percentage": "Percentage", "fixed": "Fixed", "other": "Other"}, "vat": {"vat_included": "VAT is included", "vat_not_included": "VAT is not included"}, "acquisition": {"purchase": "Purchase", "exchange": "Exchange", "gift": "Gift", "inheritage": "Inheritage", "testament": "Testament", "partitioning": "Partitioning", "other": "Other"}, "shareRegisterFormat": {"digital": "Digital", "paper": "Paper"}, "taxConsequences": {"tax_on_capital_gain_payable": "Tax on capital gain payable", "tax_on_capital_gain_not_payable": "Tax on capital gain not payable", "no_chance_to_find_out": "No chance to find out"}}, "validation": {"valueMaxSizeError": "Value cannot exceed 2,147,483,647.", "errorToast": "Please fill in all required fields", "propertyId": "Property is required", "contactIds": "Contact is required", "realtorUserIds": "Realtor is required", "unencumberedPriceRequest": "Debt-free asking price is required", "unencumberedPriceRequestEstimate": "Price request is required", "sharesIncludeLoan": "Shares include loan is required", "shareRegisterFormat": "Share register format is required", "loanAmount": "Loan amount is required", "priceIncludingLoan": "Price including loan is required", "paymentTerms": "Payment terms is required", "paymentTermOther": "Other payment terms is required", "availability": "Availability is required", "dateWhenAvailable": "Date when available is required", "tenantName": "Tenant's name is required", "belongsToBusinessActivities": "Business activity is required", "vat": "VAT is required", "assignmentValidity": "Validity is required", "assignmentValidityRenewalPeriod": "Renewal period is required", "startDate": "Start date is required", "endDate": "End date is required", "commissionBasisCode": "Commission basis code is required", "commissionType": "Commission type is required", "commissionFixed": "Fixed commission is required", "commissionPercentage": "Commission percentage is required", "isDomesticSale": "Domestic sale is required", "startAssignmentImmediately": "Assignment start is required", "startMarketingAfterCancelPeriod": "Marketing start is required", "restrictiveRightOfUserDetails": "Restrictive rights of use details is required"}, "otherTermsHeading": "Other terms and conditions"}, "reminderSent": "<PERSON><PERSON><PERSON> sent", "sentForSigning": "Sent for signing", "addSigners": "Add signers", "selectContactsToAdd": "Select contacts", "previewContract": "Preview contract", "defaultComment": "Hey, here is the sales agreement to be signed.", "missingFieldsTitle": "Missing information", "missingFields": "Please fill in all required values."}, "salesAgreement": "Sales Agreement", "fiPurchaseOffer": {"updated": "Purchase offer updated", "created": "Purchase offer created", "preview": "Preview", "copy": "Copy purchase offer", "previewOffer": "Preview offer", "create": "Create new purchase offer", "steps": {"fill": "Fill", "validate": "Validate", "sign": "Sign"}, "form": {"instructions": {"delayFeePerStartedWeek": "Compensation for each commenced week of delay until the property's release date.", "availabilityDelayFee": "If the transfer of possession to the buyer is delayed due to the seller, the seller shall compensate the buyer."}, "headers": {"parties": "Parties", "buyers": "Buyers", "sellers": "Sellers", "terms": "Terms", "priceAndAdministrative": "Selling price and administrative rights", "releaseAndTransfer": "Release and transfer of administrative rights", "termsOfTradeAndOther": "Terms of trade and other terms", "digitalTrading": "Digital trading", "standardCompensation": "Standard compensation and deposit", "termsOfTrade": "Terms of trade", "transferTax": "Transfer tax", "attachments": "Attachments", "signingAndValidity": "Signing of the deed and validity of the offer", "validity": "Validity", "informingApproval": "Informing about offer approval", "personalData": "Personal data privacy", "privacyStatement": "Privacy statement"}, "fields": {"buyers": "Buyers", "sellers": "Sellers", "unencumberedPrice": "Debt-free price", "loanAmount": "Debt amount", "priceIncludingLoan": "Selling price", "loanDate": "Housing company debt date", "buildingManagerCertificateDate": "Housing manager certification date", "paymentMethod": "Payment", "transferRightOfUse": "Possessory right transfer", "transferRightOfUseLatest": "Possessory rights transferred at the latest", "availabilityDelayFee": "Compensation amount for delay", "delayFeePerStartedWeek": "Weekly compensation amount", "isRented": "Realty sold as leased", "buyerReceivesRentStarting": "Rent payable to buyer since", "buyerReceivesRentalDepositLatest": "Deposit transfer date", "buyerResponsibleForCostsStarting": "Buyer is responsible for payments since", "buyerResponsibleForCapitalExpenditureChargeStarting": "Buyer is responsible for financial charges since", "digitalPurchase": "Digital trading allowed", "digitalPurchaseExpenses": "Digital trading expenses are paid by", "digitalPurchaseExpensesDetails": "Party responsible for the expenses", "standardCompensation": "Standard compensation", "downPayment": "Downpayment", "downPaymentTerm": "Term of downpayment", "downPaymentDate": "To be paid to the customer account before date", "validUntil": "Offer valid until", "signedLatest": "Sale deed signing deadline", "acceptOfferEmail": "Email", "acceptOfferPhone": "Text message to number", "hasReadPrivacyPolicy": "The customer was advised to get familiar with the privacy statement"}, "initialAttachments": {"energyCertificate": "Energy certificate", "propertyManagerCertificate": "Property manager certificate", "moistureMeasurement": "Moisture measurement", "maintenanceReport": "Maintenance report", "maintenancePlan": "Maintenance plan", "floorPlan": "Floor plan", "longTermMaintenancePlan": "Long-term maintenance plan", "financialStatement": "Financial statement", "articlesOfAssociation": "Articles of association"}, "initialTermsOfSale": {"moistureMeasurement": "Moisture measurement", "loan": "Loan approval", "ownPropertySold": "Sale of own apartment"}, "enums": {"paymentMethod": {"cash": "Cash", "installment": "Installment"}, "transferRightOfUse": {"purchase": "At time of purchase", "later": "At other time"}, "digitalPurchaseExpenses": {"buyer": "Buyer", "seller": "<PERSON><PERSON>", "both": "Buyer and seller", "other": "Other"}, "downPaymentTerm": {"mark_as_paid": "Mark as paid", "to_be_paid": "Paid to customer reserved account", "other": "Other"}, "status": {"draft": "Draft", "validated": "Validated", "pending_offeror_signatures": "Pending offeror signatures", "offeror_signed": "Offeror signed", "pending_buyer_signatures": "Pending buyer's signatures", "buyer_signed": "Buyer signed", "pending_offeree_signatures": "Pending offeree signatures", "offeree_signed": "Offeree signed", "pending_seller_signatures": "Pending seller's signatures", "seller_signed": "<PERSON><PERSON> signed", "accepted": "Accepted", "rejected": "Rejected", "expired": "Expired"}}, "validation": {"errorToast": "Please fill in all required fields", "propertyId": "Property is required", "buyerIds": "Buyers are required", "sellerIds": "Sellers are required", "unencumberedPrice": "Debt-free price is required", "paymentMethod": "Payment method is required", "transferRightOfUse": "Possessory right transfer is required", "transferRightOfUseLatest": "Possessory rights transfer is required", "buyerReceivesRentStarting": "Rent payable to buyer is required", "buyerReceivesRentalDepositLatest": "Deposit transfer date is required", "digitalPurchase": "Digital trading is required", "digitalPurchaseExpenses": "Digital trading expenses are required", "validUntil": "Offer valid until is required", "signedLatest": "Sale deed signing deadline is required", "acceptOfferEmail": "Email is required", "acceptOfferPhone": "Phone number is required", "counterOffer": {"purchaseOfferId": "Original offer is required", "unencumberedPrice": "New debt-free price is required", "validUntil": "Counter offer validity date and time is required"}}}, "addAssignees": "Add assignees", "sentForSigning": "Sent for signing", "sendForBuyerSigning": "Send for buyer to sign", "sendForSellerSigning": "Send for seller to sign", "signed": "Signed", "sendReminder": "Send reminder", "failedToCreate": "Failed to create purchase offer", "failedToCopy": "Failed to copy purchase offer", "reject": "Reject", "accept": "Accept", "statusUpdated": "Offers status updated", "failedToUpdateStatus": "Status update failed", "copied": "Purchase offer copied"}, "purchaseOffers": "Purchase Offers", "counterOffer": {"additionalDetails": "Counter offer additional details and conditions", "buyers": "Buyer(s)", "counterOffer": "Counter offer", "fill": "Fill counter offer", "fillSubtitle": "Fill each field carefully. You can preview when all required fields are filled.", "originalOffer": "Original offer", "otherTerms": "Other terms and conditions", "parties": "Parties", "sellers": "Seller(s)", "terms": "Counter offer terms", "unencumberedPrice": "New debt-free price", "validUntil": "Counter offer validity date and time", "validUntilHeader": "Validity", "counterOfferCreated": "Counter offer created", "validationFailed": "Please fill in all required fields", "creationFailed": "Counter offer creation failed", "offerors": "Offerors", "offerees": "Offerees", "counterOfferSentForSigning": "Counter offer sent for signing", "sendForOfferorSigning": "Send for offeror to sign", "sendForOffereeSigning": "Send for offeree to sign", "counterOfferUpdated": "Counter offer updated", "updateFailed": "Counter offer update failed", "counterOfferRejected": "Counter offer rejected", "failedToReject": "Failed to reject counter offer"}, "createCounterOffer": "Create counter offer", "signOut": "Sign Out", "create": "Create", "update": "Update", "date": "Date", "documentName": "Document name", "term": "Other term", "termDeadline": "Latest", "plusAddDocument": "+ Add document", "plusAddTerm": "+ Add term", "quickSearchPlaceholder": "Search for properties…", "roles": {"Admin": "Admin", "Realtor": "Realtor", "Photographer": "Photographer", "None": "None"}, "organizationSwitched": {"title": "Organization Changed", "description": "Successfully changed organization", "error": "Organization wasn't changed. Try again later"}, "editProperty": {"id": "Property ID", "name": "Property name", "title": "Title", "updatedTitle": "Property Updated", "updatedDescription": "Successfully updated Property", "generalInformation": "General Information", "financialInformation": "Financial Information", "description": "Description", "additionalDetails": "Additional Details", "amenities": "Amenities", "conditionAndHistory": "Condition & History", "locationAndAddress": "Location & Address", "coordinates": "Coordinates", "latitudeAndLongitude": "Latitude, Longitude", "publicLatLng": "Public Coordinates", "commission": "Commission", "commissionType": "Commission type", "percentOfSalePrice": "% of sale price", "fixedAmount": "Fixed amount", "ivaTax": "IVA Tax", "taxIncluded": "Tax included", "taxAdded": "Tax added", "noTax": "No tax", "propertyFees": "Property Fees", "communalFees": "Communal Fees", "ibi": "IBI", "garbageTax": "Garbage Tax", "waterFee": "Water Fee", "electricity": "Electricity", "cadastralReference": "Cadastral Reference", "buildingType": "Building Type", "constructionYear": "Construction Year", "buildingConstructor": "Building Constructor", "floor": "Floor", "totalFloors": "Total Floors", "buildingHasElevator": "Building has an elevator", "buildingSpecifications": "Building specifications", "buildingMaterials": "Building materials", "foundationAndStructure": "Foundation and structure", "roof": "<PERSON><PERSON>", "exteriorWalls": "Exterior walls", "additionalInformation": "Additional Information", "energyCertificate": "Energy certificate", "notApplicable": "Not applicable", "propertyHasCertificate": "Property has certificate", "spaceAndSize": "Space & size", "builtSize": "Built size", "plotSize": "Plot size", "terraceSize": "Terrace size", "interiorSize": "Interior size", "totalAreaSize": "Total area size", "roomsTotal": "Rooms in total", "pax": "Pax", "toilets": "<PERSON><PERSON><PERSON>", "suiteBaths": "Suite baths", "bathrooms": "Bathrooms", "bedrooms": "Bedrooms", "garage": "Garage", "parkingSpaces": "Parking spaces", "none": "None", "private": "Private", "communal": "Communal", "carport": "Carport", "other": "Other", "reference": "Reference ID", "typeOfGarage": "Type of garage", "pool": "Pool", "jacuzzi": "<PERSON><PERSON><PERSON><PERSON>", "dropDesignSpa": "Drop Design Spa", "views": "Views", "orientation": "Orientation", "keysAndHandoff": "Keys & Handoff", "keysInExistince": "Keys in existence", "protectedKeysTotal": "Protected", "protectedKeysDelivered": "Delivered", "protectedKeysExisting": "Existing", "unprotectedKeysTotal": "Unprotected", "unprotectedKeysDelivered": "Delivered", "unprotectedKeysExisting": "Existing", "whereKeysCanBeFound": "Where keys can be found?", "otherKeysInfo": "Other", "otherKeysInfoPhoneNumber": "Phone number", "otherKeysInfoDescription": "Describe where and who to contact...", "strandPropertiesKeysInfo": "Strand Properties", "strandPropertiesKeysInfoOffice": "Office name", "strandPropertiesKeysInfoNotes": "Other notes or comments...", "propertyHas": "Property has:", "lights": "Lights", "location": "Location", "climateControl": "Climate Control", "features": "Features", "furniture": "Furniture", "rooms": "Rooms", "security": "Security", "telecommunicationSystem": "Telecommunication system", "renovations": "Renovations", "majorRenovationsPerformed": "Major renovations performed", "renovationsHaveBeenPerformed": "Renovations have been performed", "plannedRenovations": "Planned renovations", "thereArePlannedRenovations": "There are planned renovations", "describePerformedRenovations": "Describe performed renovations", "renovationsPerformedBeforeSellerOwnership": "Renovations performed before the seller’s ownership.", "notPerformed": "Not performed", "unknown": "Unknown", "yes": "Yes", "no": "No", "defectsDamagesRepairObserved": "Defects / damages / repair needs to be observed in the property", "defectsFound": "Defects found", "describeDefectsDamagesRepairs": "Describe repair work:", "officialPermitsAcquired": "Official permits acquired", "finalInspectionOfChanges": "Final inspection of changes", "detailedAccountOfDamagesOrFault": "If the work was performed due to damages or faults, a more detailed account on the damages or fault, its manifestation, scope, and the contents of the repair work:", "damagesAndDefects": "Damages & Defects", "otherDamages": "Other Damages", "suspectedDamagesOrProblems": "The owner/seller knows or suspects that in the property there is:", "waterDamage": "Water Damage", "moistureDamage": "Moisture damage", "timeOfDamage": "Time of damage", "scopeOfDamage": "Scope of damage", "moldOrFungalProblems": "Mold or fungal problems", "otherSpecialDamages": "Other special damages, faults, or defects", "causeOfDamage": "Cause of damage", "repairMethod": "Repair method", "garden": "Garden", "telephoneNetwork": "Telephone network", "generalCabling": "General cabling", "fiberCable": "Fiber cable, house network", "certificateConsumptionRating": "Certificate Consumption Rating", "certificateConsumptionValue": "Certificate Consumption Value", "certificateEmissionRating": "Certificate Emission Rating", "certificateEmissionValue": "Certificate Emission Value", "hostawayPropertyId": "Hostaway property ID"}, "propertyType": {"resales": "Resales", "newdevelopment": "New Development", "plot": "Plot", "commercial": "Commercial", "others": "Others"}, "propertyCondition": {"excellent": "Excellent", "good": "Good", "fair": "Fair", "renovationRequired": "Renovation Required", "restorationRequired": "Restoration Required"}, "profileDetails": {"status": {"active": "Active"}, "viewProfile": "View Profile", "logout": "Log Out"}, "createContact": {"title": "Create Contact", "realtor": "Realtor", "accountDetails": "Account Details", "contactInfo": "Contact Info", "name": "Name", "createdTitle": "Contact Created", "createdDescription": "Successfully Created", "tags": "Tags", "emailIsAvailable": "Email is available", "emailAlreadyExists": "Email already in use", "contactToAssignedPersons": "Please ask from", "admin": "Administrator", "existNameContact": "This name already exists and has been assigned to someone else, please check:", "success": "Contact created successfully", "error": "Error creating contact", "createCompany": "Create company", "updateCompany": "Update company", "updateEstate": "Update estate", "updateContact": "Update contact", "createEstate": "Create estate", "beneficiaries": "Beneficiaries", "validation": {"errorToast": "Error creating contact", "typeRequired": "Type is required", "sourceRequired": "Source is required", "addressRequired": "Address is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "estateNameRequired": "Estate name is required", "companyNameRequired": "Company name is required", "businessIdRequired": "Business ID is required", "phoneNumberRequired": "Phone number is required", "emailRequired": "Email is required", "nameRequired": "Name is required", "businessId": "Business ID is required", "businessIdInvalid": "Enter valid business ID (1234567-8)", "emailInvalid": "Enter valid email"}, "newsLetterMarketingConsent": "Newsletter marketing consent", "emailMarketingConsent": "Email marketing consent", "estateName": "Estate name", "addPerson": "Add person", "signer": "Signer", "partners": "Partners", "addPartner": "Add partner", "authorizedpartner": "Authorized partner", "firstName": "First name", "lastName": "Last name", "email": "Email", "systemInfo": "System info", "signingRights": "Signing rights", "marketingSettings": "Marketing settings", "fiNewsletterConsent": "Subscribe to Finland newsletter", "esNewsletterConsent": "Subscribe to Spain newsletter", "esMarbellaNewsletterConsent": "Subscribe to Marbella newsletter"}, "assignContact": {"title": "Assign Contact", "assignInfo": "Assign {{number}} contact(s) to realtor(s)", "assign": "Assign", "createdTitle": "Contact Assigned", "createdDescription": "Successfully Assigned", "contactNotAssigned": "Contact not assigned. Try again later"}, "editContact": {"title": "Update Contact", "updatedTitle": "Contact Updated", "updatedDescription": "Successfully Updated"}, "createGroup": {"title": "Create Group", "groupInfo": "Group Info", "groupName": "Group Name", "newGroup": "Create New Group", "groupNotCreated": "Group not created. Try again later"}, "editGroup": {"title": "Update Group", "updatedTitle": "Group Updated", "updatedDescription": "Successfully Updated", "groupNotUpdated": "Group not updated. Try again later"}, "createEmail": {"title": "Create Message", "recipients": "Recipients", "content": "Content", "subject": "Subject", "message": "Message", "send": "Send", "emailSent": "<PERSON><PERSON> has been sent"}, "createUser": {"title": "Create User", "contactInfo": "Contact Info", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone", "email": "Email", "chooseRole": "Choose <PERSON>", "accountRole": "Account Role", "roleNote": "After creating a new account, the user will receive an email with reset password instructions.", "createdTitle": "New user created successfully", "createdDescription": "You can view and edit it", "marketingSettings": "Marketing settings", "emailMarketingConsent": "Customer offer emails allowed", "newsLetterMarketingConsent": "Newsletter delivery allowed", "systemInfo": "System information", "estateName": "Name of the deceased", "addPerson": "Add person", "signer": "Signer", "partners": "Partners", "addPartner": "Add partner", "authorizedpartner": "Authorized partner"}, "editUser": {"title": "Update User", "updatedTitle": "User Updated", "updatedDescription": "Successfully Updated", "realtor": "Realtor", "accountDetails": "Account Details", "contactInfo": "User Info", "firstName": "First Name", "createdTitle": "User Created", "createdDescription": "Successfully Created", "lastName": "Last Name", "realtorPublicInformation": "Public information", "profileImage": "Profile image", "replace": "Replace", "remove": "Remove", "addImage": "Add image", "jobTitle": "Job title", "teamName": "Team name", "introductionText": "Introduction text", "facebookLink": "Facebook link", "instagramLink": "Instagram link", "linkedinLink": "LinkedIn link", "tiktokLink": "Tiktok link", "provinces": "Provinces", "languages": "Languages", "offices": "Offices", "tags": "Tags", "preferredLanguage": "Preferred Language", "companyInvoicingDetails": "Company/Invoicing details", "companyType": "Company type", "companyName": "Company name", "companyId": "Company ID", "companyPhone": "Company phone", "companyEmail": "Company email", "serviceFormBookingCalendarId": "Service Form booking calendar ID", "videobotId": "Videobot ID", "hasDiasApiKey": "Has Dias API key", "setDiasApiKey": "Set new Dias API key", "newDiasApiKey": "New Dias API key", "newDiasApiKeyPlaceholder": "Leave empty to remove or enter new key"}, "editUserRole": {"title": "Update User Role", "updatedTitle": "User Role Updated", "updatedDescription": "User role successfully updated. Role changes will take effect at the next user login.", "chooseRole": "Choose <PERSON>", "changeUserRole": "Change Role"}, "listContact": {"assignedTo": "Assigned To", "assignmentStatus": "Assignment Status"}, "general": {"mandatory": "The fields marked with (*) are mandatory", "nameOrEmail": "Name or email...", "nameOrEmailOrPhone": "Name, email or phone...", "name": "Name..."}, "createProperty": {"title": "Create Property", "realtor": "Realtor", "type": "Type", "location": "Location", "contractType": "Contract Type", "rental": "Rental", "forSale": "For Sale", "exclusive": "Exclusive", "nonExclusive": "Non-Exclusive", "validityMonths": "Validity in months", "listingType": "Listing Type", "condition": "Condition", "realtors": "Realtor(s)"}, "createSalesAgreement": {"title": "Create Sales Agreement", "signingMethod": "Signing Method", "property": "Property", "createdSalesAgreement": "Sale agreement created", "createdDescription": "Successfully created", "agreementDetails": "Agreement Details", "sendForSign": "Send For Signing", "sentForSign": "Sent For Signing", "documentIsReadyForSigning": "Document is ready for signing", "previewContractByDownloadItBelow": "You can preview the contract by downloading it below.", "creatingDocument": "Creating Document...", "documentCreated": "Document Created", "sendForSignDescription": "Document successfully sent for signing", "contractOptions": "Contract Options", "reviewProperty": "Review Property Details", "reviewSeller": "Review Seller Details", "propertyCommission": "Property Commission", "seller": {"name": "Name", "phone": "Phone", "email": "Email", "streetAddress": "Street Address", "city": "City", "postalCode": "Postal Code", "country": "Country", "nationality": "Nationality", "contactLanguages": "Contact Languages", "passportNumber": "Passport Number"}}, "createDoS": {"sentForReview": "Document Created", "sendForReviewDescription": "Document successfully sent for review"}, "editDoS": {"sentForReview": "Document Edited"}, "shareTradeModal": {"createTitle": "Create DIAS Share Trade", "editTitle": "Edit DIAS Share Trade", "fiPropertySection": {"title": "Property", "propertyDropdown": "Property", "lockPropertyButton": "Next", "addressLabel": "Apartment Street Address", "postalCodeLabel": "Postal Code", "cityLabel": "City"}, "housingCompanySection": {"title": "Housing Company", "nameLabel": "Housing Company Name", "businessIdLabel": "Housing Company Business ID", "housingCompanyDropdown": "Housing Company"}, "apartmentSharesSection": {"title": "Apartment Shares", "description": "Shares must match the ones included in the agreement.", "shareTypeLabel": "Share Type", "DIGITAL": "Digital", "PAPER": "Paper", "shareLabel": "Shares", "shareGroupLabel": "Share Group Codes", "shareStatus": "Share certificate status", "sellerHasCertificate": "<PERSON><PERSON> has the certificate", "bankHasCertificate": "Sellers Bank has the certificate", "unknown": "Unknown"}, "participantsSection": {"sellerTitle": "Sellers", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "sellerTypeLabel": "Seller type", "INDIVIDUAL": "Individual", "ORGANIZATION": "Organization", "hasSellerRealtorPayment": "<PERSON><PERSON> pays part of the commission", "yes": "Yes", "no": "No", "sellerRealtorPaymentAmount": "Amount to be paid incl. VAT", "removeSeller": "Remove seller", "addSeller": "Add seller", "buyerTitle": "Buyers", "addBuyer": "Add buyer", "removeBuyer": "Remove buyer", "buyerTypeLabel": "Buyer type", "transferTaxLabel": "Transfer tax amount", "sellerBank": "Seller Bank", "buyerBank": "Buyer Bank"}, "participant": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "bank": "Bank", "phone": "Phone", "socialSecurityNumber": "Social Security Number", "companyName": "Organization name", "businessVat": "Organization Business VAT ID", "businessIdsLabel": "Shareholder Business VAT IDs", "roles": "Roles"}, "realtorSection": {"initiatorTitle": "Initiator", "initiatorDropdown": "Initiator", "realtorTitle": "Realtor", "realtorCompanyLabel": "Realtor Bank Account Name", "IBAN": "IBAN", "BIC": "BIC"}, "commissionSection": {"title": "Commission", "commissionLabel": "Commission", "includeCommission": "Includes commission", "realtorCommission": "Realtor commission incl. VAT", "invoiceReferenceNumber": "Invoice reference number", "invoiceMessage": "Invoice message", "invoiceReference": "Invoice reference"}, "otherSection": {"title": "Other", "deadlineLabel": "Deadline for signing <PERSON> of Sale", "mortgageLabel": "Property fully or partially financed by mortgage"}, "documentsSection": {"title": "Documents", "documents": "documents", "existingAttachmentsLabel": "Select uploaded files", "documentSectionNote": "All files except the house managers certificate will be shared with all parties", "documentSectionNoteBold": "Note: File size limit is 20MB", "billOfSale": "Bill of Sale", "uploading": "Uploading", "failedToUpload": "Failed to upload", "houseManagersCertificate": "House managers certificate", "shareCertificate": "Share certificate", "ownerApartment": "Owner apartment printout", "ownerApartmentNote": "This attachment must be fetched from HTJ on the same day as the trade is sent to DIAS. Otherwise one of the banks will cancel the trade. You will need one printout per osakeryhmä­tunnus", "companyStatement": "Company statement", "spousesConsent": "Spouses consent", "otherDocuments": "Other documents"}, "createdSuccess": "Successfully Created", "updatedSuccess": "Successfully Updated", "createFailed": "Failed to create", "updateFailed": "Failed to update", "initiateSuccess": "Successfully Initiated", "initiateFailed": "Failed to initiate"}, "shareTrade": {"title": "DIAS Share Trade", "status": {"DRAFT": "Draft", "INITIATED": "Initiated", "SELLER_BANK_APPROVED": "Seller bank approved", "BUYER_BANK_APPROVED": "Buyer bank approved", "INITIATOR_SIGNING_CONFIRMATION_RECEIVED": "Initiator signing confirmation received", "SIGNING_STARTED": "Signing started", "SIGNING_COMPLETED": "Signing completed", "BUYER_BANK_PAYMENTS_COMPLETED": "Processing", "SELLER_BANK_PAYMENTS_COMPLETED": "Processing", "SELLER_BANK_TRANSFERRED_SHARE": "Processing", "BUYER_BANK_RECEIVED_SHARE": "Processing", "COMPLETED": "Completed", "MOVED_TO_MANUAL_PROCESSING": "Moved to manual processing", "CANCELLED": "Cancelled"}, "overview": {"title": "Overview", "description": "DIAS Share Trade is a process where the seller and buyer of an apartment share agree to transfer their shares to each other. This process is used when the seller and buyer are not the same person or organization. The trade is initiated by the realtor who is responsible for the property. The trade is completed when the seller and buyer have signed the Bill of Sale and the trade has been initiated by DIAS.", "preparation": "Preparation", "inReview": "In Review", "signing": "Signing & Payment", "finalSteps": "Final Steps", "lastUpdated": "Last Updated", "created": "Created", "initiator": "Initiator", "initiatorTradeReferenceId": "Initiator Trade Reference ID", "needHelp": "Need help?", "diasSupportPage": "DIAS support page", "attachments": "Attachments"}, "preparation": {"title": "Step 1: Prepare DIAS Share Trade", "description": "Fill in all the required fields carefully. Once everything has been added you can initiate this trade.", "editButton": "Edit", "missingRequiredData": "Missing required data", "missingRequiredDataDescription": "All required data needs to be filled in before you can proceed to the next step."}, "inReview": {"title": "Step 2: In Review", "description": "The trade is now in review. The seller and buyer will receive an email with the trade details. The trade will be completed when the seller and buyer have signed the Bill of Sale and the trade has been initiated by DIAS.", "cancel": "Cancel Trade", "clone": "<PERSON><PERSON>", "cloneThisTrade": "Clone this trade", "cloneThisTradeDescription": "This trade has been canceled. If you'd like, you can clone it to restart the process."}, "signing": {"title": "Step 3: Signing & Payment", "signed": "Signed", "pending": "Pending", "participantSigning": "participantSigning", "viewSigningDetails": "View signing details", "movedToManualProcessing": "Trade was moved to Manual Processing", "movedToManualProcessingDescription": "You will no longer receive automated updates for this trade. For further updates, please contact DIAS support at:", "wrongEmailAddress": "Wrong e-mail address? Provide participants the following link to log in.", "description": "Description", "signingDetails": "Signing details"}, "finalSteps": {"title": "Step 4: Final Steps", "description": "The final transfer may take up to 6 months. We’ll notify you once the trade is complete, and the documents will be automatically archived for easy access. No further action is needed on your part.", "download": "Download", "downloadDocuments": "Download documents", "downloadDocumentsDescription": "You can now download the trade documents."}, "modal": {"cancelTrade": "Cancel DIAS share trade", "cancelTradeDescription": "You are about to cancel this trade. This will stop any progress on this trade and you will need to create a new trade in order to continue with trading this property.", "cancelButton": "Cancel trade", "goBackButton": "Go back", "cancellationReason": "Cancellation reason", "cancellationReasonError": "Text must not contain special characters and max 5000 characters.", "signingDetails": "Signing Details"}, "footer": {"nextStepInitiate": "Next step: Initiate", "nextStepInitiateDescription": "You need to fill in the required data before you can proceed to the next step.", "nextStepSign": "Next step: Signing & Payment", "nextStepSignDescription": "Once the trade has been approved, you can send it for signing. The signees will have 24h to sign the contract", "nextStepFinalSteps": "Next step: Final Steps", "nextStepFinalStepsDescription": "Once everyone has signed the final step will activate automatically.", "initiate": "Initiate", "sendForSigning": "Send for signing"}, "results": {"success": "Success", "initiateSuccess": "Initiated successfully", "sendForSigningSuccess": "Sending for signing successful", "initiateFailed": "Initiate failed!", "sendForSigningFailed": "Sending for signing failed!"}, "sections": {"property": "Property", "housingCompany": "Housing company", "apartmentShares": "Apartment shares", "sellers": "Sellers", "commissions": "Commissions", "buyers": "Buyers", "initiator": "Initiator", "realtor": "Realtor", "other": "Other", "documents": "Documents"}, "fields": {"streetAddress": "Street address", "postalCode": "Postal code", "city": "City", "housingCompanyName": "Housing company name", "businessVatId": "Business ID", "shareType": "Share type", "shares": "Shares", "osakeryhmatunnukset": "Osakeryhmätunnukset", "sellerShareCertificateStatus": "Seller Share Certificate Status", "sellerType": "Seller type", "firstName": "First name", "lastName": "Last name", "socialSecurityNumber": "Social security number", "email": "Email", "bank": "Bank", "phone": "Phone", "name": "Name", "amountToBePaid": "Amount to be paid incl. VAT", "commissionWillBePaid": "Commission will be paid", "bankAccountName": "Bank account name", "iban": "IBAN", "bic": "BIC", "commissionInclVat": "Commission incl. VAT", "invoiceMessage": "Invoice message", "invoiceReferenceNumber": "Invoice reference number", "buyerType": "Buyer type", "initiatorPersonId": "Initiator Person ID", "realestateCompanyName": "Real estate company name", "realestateCompanyVatId": "Real estate company VAT ID", "deadlineForSigning": "Deadline for Signing Bill of Sale", "mortgage": "Property fully or partially financed by mortgage"}, "validation": {"billOfSale": "Bill of Sale is required", "shareCertificates": "Share Certificates is required if one of the sellers has share certificate", "ownerApartmentPrintouts": "If share type is DIGITAL, Owner apartment printouts should match the number of apartment osakeryhmatunnukset", "houseManagersCertificates": "House managers certificates cannot be more than one", "spousesConsent": "Spouses consent cannot be more than one", "deadlineForSigningBillOfSale": "Deadline for signing bill of sale must be at least 7 weekdays from today", "sellerShareCertificateStatus": "Seller share certificate status is required", "apartmentAddressStreetAddress": "Street address is required", "apartmentAddressPostalCode": "Postal code is required", "apartmentAddressCity": "City is required", "housingCompanyName": "Housing company name is required", "housingCompanyBusinessId": "Housing company business ID is required", "invalidFIVAT": "Invalid housing company business ID", "shares": "Shares are required", "atLeastOneSeller": "At least one seller is required", "sellerType": "Seller type is required", "firstName": "First name is required", "lastName": "Last name is required", "email": "Email is required", "socialSecurityNumber": "Social security number is required", "invalidSocialSecurityNumber": "Invalid social security number", "atLeastOneBuyer": "At least one buyer is required", "buyerType": "Buyer type is required", "IBAN": "IBAN is required", "BIC": "BIC is required", "bankBusinessId": "Bank is required", "bankAccountName": "Bank account name is required when seller has realtor payment", "invalidBIC": "Invalid BIC", "id": "ID is required", "createdById": "Created by ID is required", "updatedAt": "Updated at is required", "initiatorPersonId": "Selecting initiator from the dropdown is required", "initiatorId": "Initiator ID is required", "initiatorEmail": "Initiator email is required", "initiatorPhoneNumber": "Initiator phone number is required", "invalidEmail": "Invalid email address"}}, "actionDropdown": {"create": {"header": "Create New", "property": "Create Property", "group": "Create Group", "email": "Create Email", "contact": "Create Contact", "salesAgreement": "Create Sales Agreement", "salesActivity": "Create Sales Activity", "user": "Create User", "offer": "Create Reservation & Deposit Proposal", "event": "Create Event", "sellingOffer": "Create offer", "brokerageOffer": "Create brokerage offer", "DoS": "Create Details of Sale", "brochureFI": "Create brochure", "diasShareTrade": "Create Share Trade", "matchMaking": "Create Match Making", "advertisement": "Create ad"}, "documents": {"header": "Documents", "agreement": "New Sale Agreement", "newOffer": "<PERSON> Offer", "ready": "Document is ready for signing"}, "event": {"header": "Events", "schedule": "Schedule Event", "logActivity": "Log Activity"}}, "propertiesInput": {"search": "Reference, seller, or title...", "location": "Location:", "beds": "Beds", "baths": "Baths", "filters": "Filters"}, "success": {"documentDownloadTitle": "Document Download", "documentDownloaded": "Document downloaded successfully", "documentDeleteTitle": "Document Deletion", "documentDeleted": "Document deleted successfully", "documentUploadTitle": "Document uploaded", "documentUploaded": "Document uploaded successfully"}, "errors": {"title": "Something went wrong", "propertyNotCreated": "Property not created. Try again later", "propertyNotUpdated": "Property not updated. Try again later", "contactNotCreated": "Contact not created. Try again later", "contactNotUpdated": "Contact not updated. Try again later", "groupNotCreated": "Group not created. Try again later", "userNotUpdated": "User not updated. Try again later", "salesAgreementNotCreated": "Sale agreement not created", "eventNotCreated": "Event not created. Try again later", "eventNotUpdated": "Event not updated. Try again later", "sendForSignFail": "Unable to send document for signing", "priceNotInRange": "Price not in range", "sizeNotInRange": "Size not in range", "dateNotInRange": "Date not in range", "minPriceHasToBeLowerThanMaxPrice": "Min price has to be lower than max price", "minSizeHasToBeLowerThanMaxSize": "Min size has to be lower than max size", "fromDateHasToBeBeforeUntilDate": "From date has to be before until date", "documentNotDownloaded": "Document could not be download. Please try again later", "documentNotDeleted": "Document could not be deleted. Please try again later", "salesActivityNotUpdated": "Sales Activity not edited. Please try again later", "salesActivityNotCreated": "Sales Activity not created. Please try again later", "detailOfSaleNotCreated": "Details of Sale not created. Please try again later", "detailOfSaleNotEdited": "Details of Sale not updated. Please try again later", "changingRequestedFailed": "Changing requested failed. Please try again later", "userNotCreated": "User not created. Try again later", "userNotActivated": "User not activated. Try again later", "userNotDeactivated": "User not deactivated. Try again later", "propertyMustBeSetForSaleToCreateASalesAgreement": "Property must be set for sale to create a Sales Agreement", "missingRequiredInformation": "Missing required information", "fieldRequired": "${path} is required", "AtLeastOneFieldMustBeSelected": "At least one ${path} must be selected", "fieldDoesntMatchTheFormat": "${path} does not match the format", "userRoleNotUpdated": "User role not updated. Try again later", "mustBeGreaterThanField2": "${path} must be greater than {{field2}}", "mustBeLaterThanField2": "${path} must be later than {{field2}}", "mustBePositive": "${path} must be positive", "mustBeValidPercentage": "${path} must be a valid percentage", "documentUploadedFail": "Document uploaded unsuccessfully", "maxCharacters": "Max characters allowed is ${max}", "fileSameNameExists": "A file with the same name has already been uploaded", "mustBeGreaterOrEqualThan": "${path} must be greater or equal than ${min}", "matchMakingNotCreated": "Matchmaking not created. Try again later", "uploadFailed": "Failed to upload", "emailNotSent": "Em<PERSON> is failed to sent", "pleaseFillRequiredFields": "Please fill the required fields to save changes.", "matchMakingPropertyNotUpdated": "Matchmaking property not updated. Try again later", "propertyAlreadyHasDoS": "Property already has Details of Sale", "createDocumentSigningUserMissingName": "<PERSON>reate failed, user missing name", "createDocumentSigningContactMissingName": "<PERSON><PERSON> failed, contact missing name", "createDocumentSigningAtLeastOneSignerRequired": "Create failed, at least one signer is required", "createDocumentSigningSsnInvalidOrMissing": "Create failed, SSN is invalid or missing", "addSignerDocumentSigningNotFound": "Add signer failed, document signing not found", "addSignerDocumentSigningNotAllowedStatus": "Add signer failed, document signing not allowed status", "addSignerSignerAlreadyAdded": "Add signer failed, signer already added", "deleteDocumentSignerNotAllowedStatus": "Delete signer failed, not allowed status", "deleteDocumentSignerNotFound": "Delete signer failed, not found", "createDocumentSigningOnlyPdfFilesSupported": "C<PERSON> failed, only pdf files are supported"}, "mediaPage": {"upload": "Upload Media", "addItems": "Add Items", "dragndropMediaHere": "Drag n' Drop media here", "or": "or", "chooseOwnDevice": "Choose from your own device", "supportedFiles": "Supported files", "remove": "Remove", "uploadedBy": "By {{author}}", "unknownAuthor": "Unknown author", "coverPhoto": "Cover photo"}, "documentsUpload": {"upload": "Upload Documents", "addItems": "Add Items", "dragndropFilesHere": "Drag n' Drop documents here", "or": "or", "chooseOwnDevice": "Choose from your own device", "supportedFiles": "Supported files", "remove": "Remove", "uploadSignedCopy": "Upload signed copy", "rejectOffer": "Reject offer"}, "modalConfirmation": {"title": "Are you sure you want to proceed?"}, "stats": {"totalNumberPublishedProperties": "Total number of properties published", "totalNumberSoldProperties": "Total number of properties sold", "totalNumberSalesAgreementsCreated": "Total number of sales agreements created", "published": "Published", "sold": "Sold", "salesAgreements": "Sales Agreements"}, "filters": {"listingType": "Listing type", "any": "Any", "resale": "For sale", "rentals": "Rentals", "price": "Price", "minimum": "Minimum", "maximum": "Maximum", "area": "Area", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "conditions": "Conditions", "size": "Size", "source": "Source", "garagePoolGardenViews": "Garage, Pool, Garden, Views", "garage": "Garage", "pool": "Pool", "garden": "Garden", "views": "Views", "orientations": "Orientations", "amenities": "Amenities", "location": "Location", "climatecontrol": "Climate control", "features": "Features", "furniture": "Furniture", "rooms": "Rooms", "security": "Security", "showMore": "Show more", "showLess": "Show less", "title": "Filters", "strandified": "Strandified", "createdAt": "Created at", "updatedAt": "Updated at", "from": "From", "to": "to", "until": "Until", "exclusive": "Exclusive"}, "yes": "Yes", "no": "No", "youWillLoseYourChanges": "By proceeding you will lose the changes.", "duplicate": "Duplicate", "youWillDuplicateProperty": "By proceeding you will duplicate the selected property.", "theFileWillBeDeleted": "The file will be deleted.", "successCreatedProperty": "Successfully created property", "propertyDuplicated": "Property Duplicated", "bed": "Bed", "bath": "Bath", "beds": "Beds", "baths": "Baths", "interior": "Interior", "plot": "Plot", "media": "Media", "details": "Details", "social": "Social", "tiktok": "Tiktok", "facebook": "Facebook", "instagram": "Instagram", "linkedin": "LinkedIn", "name": "Name", "mobile": "Mobile", "phone": "Phone", "phones": "Phones", "email": "Email", "address": "Address", "nationality": "Nationality", "source": "Source", "type": "Type", "bank": "Bank", "passportNumber": "Passport Number", "socialSecurityNumber": "Social Security Number", "businessId": "Business ID", "vatNumber": "VAT Number", "iban": "IBAN", "bic": "BIC", "nieNumber": "NIE number", "position": "Position", "introductionText": "Introduction text", "languages": "Languages", "offices": "Offices", "residentOfSpain": "Resident Of Spain", "assignedTo": "Assigned to", "listedFor": "Listed for", "listedBy": "Listed by", "location": "Location", "specs": "Specs", "reference": "Reference", "realtors": "Realtor(s)", "mainLanguage": "Main language", "mainDescription": "Main description", "description": "Description", "category": "Category", "EUR": "€", "M2": "m²", "HA": "ha", "Rent": "Rent", "month": "month", "privateInfo": "Private info", "legalRepresentative": "Legal representative", "commission": "Commission", "ivaTax": "IVA Tax", "commissionNotes": "Commission notes", "commissionType": "Commission type", "internalNotes": "Internal notes", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "fullAddress": "Full Address", "coordinates": "Coordinates", "coordinatesVisibility": "Coordinates visibility", "es": "Spanish", "fi": "Finnish", "en": "English", "de": "German", "sv": "Swedish", "translations": {"es": "Spanish translation", "fi": "Finnish translation", "en": "English translation", "de": "German translation", "sv": "Swedish translation"}, "edit": "Edit", "editDetails": "Edit Details", "syncNow": "Sync now", "portalsynchronised": "Portals synchronised", "country": "Country", "status": "Status", "statusUpdated": "Status Updated!", "successUpdateOfProperty": "Successfully updated status of property", "changeStatus": "Change Status", "updateStatus": "Update Status", "statusError": "Error when publishing/unpublishing to", "statusErrorDescription": "This property may be missing required fields or has reached the limit of properties that can be published to this portal", "createdBy": "Created By", "createdAt": "Created at", "publish": "Publish", "unpublish": "Unpublish", "cancel": "Cancel", "next": "Next", "prev": "Previous", "accept": "Accept", "save": "Save", "apply": "Apply", "reset": "Reset", "clearAll": "Clear filters", "price": "Price", "property": "Property", "people": "People", "all": "All", "own": "Own", "network": "Network", "close": "Close", "properties": "Properties", "resaleProperty": "Resale Property", "newDevelopment": "New Development", "plotM": "Plot M", "builtM": "Built M", "terraceM": "Terrace M", "interiorM": "Interior M", "translation": "Translation", "saleInfo": "Sale info", "listingPrice": "Listing price", "streetAddress": "Street address", "expiryDate": "Contract Expiry Date", "city": "City", "postalCode": "Postal code", "latitude": "Latitude", "longitude": "Longitude", "loading": "Loading", "sellers": "Seller(s)", "authenticator": "Authenticator", "authentication": "Require strong authentication", "documentation": "Necessary documentation", "publicDeedOrNotaSimple": "Pubic deed and/or land register note", "firstOccupancyLicense": "First occupancy license", "identityDocument": "Identity documents", "urbanContributionTaxBill": "Most recent urban contribution tax bill", "rubbishCollectionFeesReceipt": "Most Recent Rubbish collection fees", "communityFeesReceipt": "Last community fees receipt", "energyPerformanceCertificate": "Energy performance certificate (CEE)", "certificateOfCommunityPropertyOwners": "Certificate of community property owners", "certificateOfTaxResidence": "Certificate of tax residence", "legalRepresentativeAndPowerOfAttorney": "Legal representative and power of attorney", "thePowersInCaseOfSociety": "Powers in case of Society", "separationRuling": "Separation ruling of divorce judgement", "prenuptialAgreement": "Pre-nuptial agreement", "certificateOfInheritance": "Certificate of inheritance", "certificateOfOutstandingDebt": "Certificate of outstanding debt issued by the bank", "utilityBill": "Most Recent utilities bills", "documents": "Documents", "contracts": "Contracts", "resources": "Resources", "public": "Public", "private": "Private", "updated": "Updated", "updatedAt": "Updated at", "expiresAt": "Expires at", "downloadDocument": "Download", "deleteDocument": "Delete", "visibility": "Visibility", "portals": "Portals", "features": "Features", "files": "Media", "tags": "Tags", "welcomeBack": "Welcome back", "dataSource": "Data Source", "userCreated": "User Created", "locationAndAddress": "Location And address", "latitudeAndLongitude": "Latitude, longitude", "title": "Title", "fullDescription": "Full description", "financialInformation": "Financial information", "createBrochure": "Create brochure", "createWindowBrochure": "Create window brochure", "propertyFees": "Property fees", "communalFees": "Communal fees", "ibi": "IBI", "garbageTax": "Garbage tax", "water": "Water", "electricity": "Electricity", "exclusive": "Exclusive", "nonExclusive": "Non-Exclusive", "contractType": "Contract type", "listingTypes": "Listing types", "cadastralReference": "Cadastral reference", "additionalDetails": "Additional details", "renovationsHaveBeenPerformed": "Renovations have been performed", "thereArePlannedRenovations": "There are planned renovations", "renovationsHaveNotBeenPerformed": "Renovations have not been performed", "thereAreNoPlannedRenovations": "There are no planned renovations", "noDefectsFound": "No defects found", "amenities": "Amenities", "conditionAndHistory": "Condition & history", "waterFee": "Water fee", "buildingType": "Building type", "constructionYear": "Construction year", "buildingConstructor": "Building constructor", "floor": "Floor", "floors": "Floors", "totalFloors": "Total floors", "buildingHasElevator": "Building has an elevator", "buildingSpecifications": "Building specifications", "buildingMaterials": "Building materials", "foundationAndStructure": "Foundation and structure", "roof": "<PERSON><PERSON>", "exteriorWalls": "Exterior walls", "additionalInformation": "Additional Information", "energyCertificate": "Energy certificate", "notApplicable": "Not applicable", "propertyHasCertificate": "Property has certificate", "spaceAndSize": "Space & size", "builtSize": "Built size", "plotSize": "Plot size", "terraceSize": "Terrace size", "interiorSize": "Interior size", "totalAreaSize": "Total area size", "otherSpaces": "Other spaces", "totalSize": "Total size", "sizeVerification": "Size verification", "roomsTotal": "Rooms in total", "pax": "Pax", "toilets": "<PERSON><PERSON><PERSON>", "suiteBaths": "Suite baths", "bathrooms": "Bathrooms", "bedrooms": "Bedrooms", "garage": "Garage", "parkingSpaces": "Parking spaces", "none": "None", "communal": "Communal", "carport": "Carport", "other": "Other", "typeOfGarage": "Type of garage", "pool": "Pool", "jacuzzi": "<PERSON><PERSON><PERSON><PERSON>", "dropDesignSpa": "Drop design spa", "views": "Views", "orientation": "Orientation", "keysAndHandoff": "Keys & handoff", "keysInExistince": "Keys in existence", "protectedKeysTotal": "Protected", "protectedKeysDelivered": "Delivered", "protectedKeysExisting": "Existing", "unprotectedKeysTotal": "Unprotected", "unprotectedKeysDelivered": "Delivered", "unprotectedKeysExisting": "Existing", "whereKeysCanBeFound": "Where keys can be found?", "otherKeysInfo": "Other", "otherKeysInfoPhoneNumber": "Phone number", "otherKeysInfoDescription": "Describe where and who to contact...", "strandPropertiesKeysInfo": "Strand Properties", "strandPropertiesKeysInfoOffice": "Office name", "strandPropertiesKeysInfoNotes": "Other notes or comments...", "propertyHas": "Property has:", "lights": "Lights", "climateControl": "Climate control", "furniture": "Furniture", "rooms": "Rooms", "security": "Security", "telecommunicationSystem": "Telecommunication system", "renovations": "Renovations", "majorRenovationsPerformed": "Major renovations performed", "plannedRenovations": "Planned renovations", "describePerformedRenovations": "Describe performed renovations", "renovationsPerformedBeforeSellerOwnership": "Renovations performed before the seller’s ownership.", "notPerformed": "Not performed", "unknown": "Unknown", "defectsDamagesRepairObserved": "Defects / damages / repair needs to be observed in the property", "defectsFound": "Defects found", "describeDefectsDamagesRepairs": "Describe repair work:", "officialPermitsAcquired": "Official permits acquired", "finalInspectionOfChanges": "Final inspection of changes", "detailedAccountOfDamagesOrFault": "If the work was performed due to damages or faults, a more detailed account on the damages or fault, its manifestion, scope, and the contents of the repair work:", "damagesAndDefects": "Damages & defects", "otherDamages": "Other damages", "suspectedDamagesOrProblems": "The owner/seller knows or suspects that in the property there is:", "waterDamage": "Water damage", "moistureDamage": "Moisture damage", "timeOfDamage": "Time of damage", "scopeOfDamage": "Scope of damage", "moldOrFungalProblems": "Mold or fungal problems", "otherSpecialDamages": "Other special damages, faults, or defects", "causeOfDamage": "Cause of damage", "repairMethod": "Repair method", "garden": "Garden", "certificateConsumptionRating": "Cert. Consumption Rating", "certificateConsumptionValue": "Cert. Consumption Value", "certificateEmissionRating": "Cert. Emission Rating", "certificateEmissionValue": "Cert. Emission Value", "telecommunicationSystems": "Telecommunication systems", "propertyHasElectricity": "Property has electricity", "propertyHasLights": "Property has lights", "garageTypes": "Garage types", "poolTypes": "Pool types", "gardenTypes": "Garden types", "empty": "Empty", "lift": "Lift", "keysInExistence": "Keys in existence", "unauthorised": "Unauthorised", "credentialsExpiredLogInAgain": "Your credentials have expired. You will be redirected to the login page.", "condition": "Condition", "realtor": "Realtor", "photographer": "Photographer", "admin": "Admin", "company": "Company", "website": "Website", "postCode": "Postal Code", "preferredLanguage": "Preferred Language", "notes": "Notes", "moreInfo": "More Info", "salesActivityInfo": "Sales Activity Info", "associatedProperties": "Associated properties", "conversations": "Conversations", "leadInfo": "Lead info", "here": "here", "created": "created", "activity": "Activity", "aiSearch": "AI Search", "referenceNumber": "ROAIIB Nº", "salesActivity": {"activityID": "Activity ID", "salesActivity": "Sales Activity", "salesActivityStatus": "Status", "salesActivityRelevance": "Relevance", "salesActivitySource": "Source", "salesActivityType": "Type", "contacts": "Contacts", "createSalesActivity": "Create Sales Activity", "editSalesActivity": "Edit Sales Activity", "editSalesActivityDescription": "Your changes have been saved.", "createSalesActivityWithId": "You can view and edit the sales activity", "editSalesActivityWithId": "Edit Sales Activity with ID", "salesActivityNotEdited": "Sales Activity not edited. Try again later", "ticketInfo": "Ticket info", "linkedContacts": "Linked contacts", "none": "None", "status": {"new": "New", "contacted": "Contacted", "qualification": "Qualification", "proposal_offering": "Proposal/Offering", "closed_won": "Won", "closed_lost": "Lost"}, "relevance": {"hot": "Hot", "warm": "Warm", "cold": "Cold", "neutral": "Neutral"}, "source": {"personal_contact": "Personal contact", "website": "Website", "email": "Email", "phone_sms": "Phone/SMS", "portal": "Portal", "open_house": "Open house", "other": "Other"}, "type": {"buying": "Buying", "selling": "Selling", "other": "Other"}, "errors": {"realtorRequired": "At least one realtor is required"}, "matchMaking": "Matchmaking"}, "addressForm": {"fields": {"apartmentNumber": "Apartment number", "apartmentNumber_short": "Apartment no.", "coordinateSource": "Coordinate source", "district": "District", "latitude": "Latitude", "longitude": "Longitude", "municipality": "City", "stairwell": "Stairwell", "postalCode": "Postal code", "streetAddress": "Street address", "coordinateAccuracy": "Coordinate accuracy"}, "enums": {"coordinateSource": {"GEO_CODING": "Autofill", "MANUAL": "Manual"}}}, "fiProperty": {"sections": {"price": "Price", "basic_information": "Basic information", "prices_and_costs": "Price and costs", "condition_and_renovations": "Condition and renovations", "descriptions": "Descriptions", "defects_damages": "Defects and damages", "living_comfort": "Living comfort", "spaces_materials": "Spaces & materials", "kitchen_dining_area": "Kitchen and dining room", "kitchen": "Kitchen", "dining_room": "Dining room", "bathroom": "Bathroom", "sauna": "Sauna", "toilet": "WC", "utility_room": "Utility room", "living_room": "Livingroom", "bedroom_walkin_closet": "Bedroom and walk-in closet", "bedroom": "Bedroom", "walk_in_closet": "Walk-in closet", "loft": "Loft", "study_library": "Study and library", "study": "Study", "library": "Library", "hallway": "Hallway", "hall": "Hall", "balcony_terrace_yard": "Balcony, terrace and yard", "storage_cellar": "Storage and cellar", "more_information": "More information", "ground_floor": "Ground floor", "draught_lobby": "Draught lobby", "encumbrances_restrictions_dues": "Encumbrances, control restrictions and unpaid dues", "services_connections": "Services and connections", "other_information": "Other information", "housing_company": "Housing company", "finances": "Finances", "construction": "Construction", "buildings": "Buildings", "plot": "Plot", "services_transportation": "Services and transportation", "additional_details": "Defects and damages", "yard": "Yard", "parkingSpaces": "Parking spaces", "housingCompanyPremiseStatistics": "Housing company premise statistics", "renovations": "Renovations", "inspections": "Inspections", "internetConnections": "Internet connections", "meetingAndLiabilities": "Meetings and liabilities", "energyCertificate": "Energy certificate", "asbestosMapping": "Asbestos mapping", "additionalInformation": "Additional Information", "parkingSpace": "Parking spaces", "lease": "Lease", "zoning": "Zonings", "beach": "Beach", "constructionRight": "Construction right", "housingComfort": "Housing comfort", "heart": "Heart", "redemption": "Redemption", "additionalRealtyDetailsLink": "Additional realty details link", "condition": "Condition", "propertyDetail": "Property detail", "administration": "Administration", "moreAboutStorage": "More about storage", "transaction": "Transaction", "parking": "Parking", "boilerRoom": "Boiler Room", "patio": "<PERSON><PERSON>", "closet": "Closet", "workRoomAndStudy": "Workroom and library", "hallHallwayAndDraughtLobby": "Area halls", "storage": "Storage", "other": "Other", "terrace": "Terrace", "balcony": "Balcony", "television": "Television", "damage": "Damage", "damages": "Damages", "cost": "Cost", "featureDescription": "Feature description", "shares": "Shares", "rentDetails": "Rent details", "renovationsAndInstallations": "Renovations and installations", "defectsAndDamages": "Defects and damages"}, "fields": {"periods": "Period", "amount": "Amount", "costDescription": "Costs description", "waterCharge": "Water charge", "cost": "Cost", "costs": "Costs", "depthShareAmountDescription": "Debt share amount description", "auctionListing": "Auction listing", "ownershipType": "Ownership type", "sizeDescription": "Size description", "commission": "Commission", "commissionNotes": "Commission notes", "commissionType": "Commission type", "contractType": "Contract type", "listingType": "Listing type", "Code": "Ownership type", "property": "Property", "propertyAddress": "Property address", "propertyType": "Subtype", "propertyTypeGroup": "Property type", "theSalePrice": "Sale price", "startingPrice": "Starting price", "debtFreePrice": "Debt free price", "debtShareAmount": "Debt share amount", "residentialShareOverviewDebtFreePrice": "Debt free price", "residentialShareOverviewDebtShareAmount": "Debt share amount", "residentialShareDebtShareAmountDescription": "Residential Share - Debt Share Amount Description", "commercialOverviewDebtFreePrice": "Commercial overview - debt free price", "commercialOverviewDebtShareAmount": "Commercial overview - debt share amount", "otherShareOverviewDebtFreePrice": "Other Share overview debt free price", "otherShareOverviewDebtShareAmount": "Other share overview debt share amount", "wallMaterial": "Wall materials", "floorMaterial": "Floor materials", "ceilingMaterial": "Ceiling materials", "roomStructure": "Room structure", "residentialType": "Residential type", "livingQuarters": "Living quarters", "buildingFloorCode": "Floor level code", "livingAreaFloorCode": "Living floor level code", "floorLevel": "Floor level", "livingFloorLevel": "Living floor level", "totalFloors": "Total floors", "leaseStartDate": "Lease start date", "leaseEndDate": "Lease end date", "leaseType": "Lease contract type", "depositPaymentDate": "Deposit payment date", "depositAmount": "Deposit amount", "housingCompany": "Housing company", "businessId": "Business ID", "manager": "Manager", "managerContactDetails": "Manager contact details", "houseManagerCertificate": "House manager certificate", "maintenanceCompany": "Maintenance company", "propertyId": "Property ID", "builder": "Builder", "developmentPhase": "Development phase", "usageStartYear": "Usage start year", "constructionAndUsageYearDescription": "Construction and usage year description", "hasElevator": "Has elevator", "elevatorTakesToApartment": "Elevator takes to apartment", "hasSauna": "Has sauna", "yard": "Yard", "yardDescription": "Yard description", "plot": {"name": "Plot", "type": "Plot type", "ownerShip": "Plot ownership", "ownershipDescription": "More about construction right", "description": "Plot description", "number": "Plot number"}, "landlord": "Landlord", "annualRent": "Annual rent", "redeemable": "Redeemable", "redeemableDescriptions": "Redeemable description", "optionalRentalPlot": "Optional rental plot", "optionalRentalPlotDescription": "Optional rental plot description", "lease": "Lease", "leaseEnd": "Lease ends", "leasePeriodDescription": "Lease period description", "landChargeId": "Land charge ID", "leaseHolder": "Lease holder", "leaseTransferLimitations": "Lease transfer limitations", "leaseTransferLimitationsDescription": "Lease transfer limitations description", "confirmedSizeOfThePlot": "Confirmed size of the plot", "sizeUnit": "Size unit", "areaSize": "Area size", "areaUnit": "Area unit", "zoning": "Zoning", "zoningDescription": "Zoning description", "beach": {"name": "Beach", "type": "Beach type", "typeDescription": "Beach type description", "waterBody": "Water body"}, "constructionRight": "Construction right", "densityRate": "Density rate", "floorArea": "Floor area", "constructionRightDescription": "Construction right description", "unbuiltPlot": "Unbuilt plot", "internetConnections": "Internet connections", "broadbandAvailable": "Broadband available", "fiberOpticInternet": "Fiber optic internet", "housingCompanyBroadband": "Housing company broadband", "broadbandOperator": "Broadband operator", "internalNetworkCabling": "Internal network cabling", "networkDescription": "Network description", "television": {"name": "Television", "type": "Television type", "typeDescription": "Television type description"}, "meetingAndLiabilities": "Meetings and liabilities", "lastAnnualGeneralMeetingDate": "Last annual general meeting date", "nextAnnualGeneralMeetingDate": "Next annual general meeting date", "identifiedDeficiencies": "Identified deficiencies", "costIncurringLiabilities": "Cost incurring liabilities", "repairsAndMaintenanceAgreements": "Repairs and maintenance agreements", "loanAmount": "Loan amount", "mortgageAmount": "Mortgage amount", "rentRevenue": "Rent revenue", "bankAccountCreditLimit": "Bank account credit limit", "currencyCode": "Currency code", "financingFeeInterestOnlyPeriod": "Financing fee interest only period", "financingFeeInterestOnlyStartDate": "Financing fee interest only start date", "financingFeeInterestOnlyEndDate": "Financing fee interest only end date", "managementChargesInfoLink": "Management charges info link", "maintenance": "Maintenance", "energyCertificates": {"name": "Energy certificates", "type": "Energy certificate type", "description": "Energy certificate description"}, "asbestosMapping": "Asbestos mapping", "asbestosMappingDone": "Is asbestos mapping done?", "asbestosMappingReportAvailable": "Is asbestos mapping report available?", "asbestosPossibleInConstructionMaterials": "Construction materials might have asbestos", "asbestosMappingDescription": "Asbestos mapping description", "parkingSpaces": "Parking spaces", "parkingSpace": {"name": "Parking space", "type": "Parking space type", "count": "Count", "description": "Parking space description", "add": "Add parking space", "transferCode": "Transfer code", "basisForPossessionCodes": "Basis for possession codes"}, "premiseType": "Premise type", "managedByHousingCompany": "Managed by housing company", "areaManagedByHousingCompany": "Area managed by housing company", "housingCompanyPremiseSpecificationDescription": "Housing company premise specification description", "renovations": "Renovations", "renovation": "Renovation", "decidedByGeneralMeeting": "Decided by general meeting", "renovationsDescription": "Renovations description", "isHousingCompanyNotified": "Is housing company notified?", "renovationStatus": "Renovation status", "renovationType": "Renovation type", "typeOtherDescription": "Type other description", "addRenovation": "Add renovation", "inspections": "Inspections", "inspection": {"name": "Inspection", "type": "Inspection type", "description": "Inspection description", "add": "Add inspection"}, "additionalInformation": "Additional information", "additionalDescription": "Additional description", "listOfSharesTransferred": "List of shares transferred", "digitalShares": "Digital shares", "postalArea": "Postal Area", "constructionMaterial": "Construction material", "heating": "Heating", "ventilation": "Ventilation", "outerRoof": "Outer roof", "outerRoofMaterial": "Outer roof material", "premises": "Premises", "housingCompanyPremiseSpecifications": "Housing company premise specifications", "nearbyAmenities": "Nearby amenities", "schoolChildcare": "School and childcare", "services": "Services", "hobbiesAndActivities": "Hobbies and activities", "activitiesAndRecreationDescription": "Activities and recreation description", "transportConnections": "Transport connections", "transportationConnectionsDescription": "Transportation connections description", "drivingDirections": "Driving directions", "drivingDirectionsDescription": "Driving directions description", "administration": "Administration", "propertyDetails": "Property details", "sauna": "Sauna", "terrace": "Terrace", "balcony": "Balcony", "numberOfToilets": "Number of toilets", "numberOfBedrooms": "Numbers of bedrooms", "viewDescription": "View description", "shareholderInstallations": "Shareholder installations", "renovationDescription": "Renovation description", "condition": "Condition", "conditionDescription": "Condition description", "damages": "Damages", "propertyHasOrHadDamages": "Property has or had damages", "inspectionsDescription": "Inspections description", "asbestosMappingDoneDescription": "Asbestos mapping done", "asbestosMappingReportAvailableDescription": "Asbestos mapping report available", "asbestosPossibleInConstructionMaterialsDescription": "Construction materials might have asbestos", "asbestosDescription": "Asbestos description", "housingComfort": "Housing comfort", "thingsAffectingHousingComfort": "Things affecting housing comfort", "fitForWinterHabitation": "Fit for winter habitation", "smokingAllowed": "Smoking allowed", "petsAllowed": "Pets allowed", "accessible": "Accessible", "furnished": "Furnished", "heatingDescription": "Heating description", "underfloorHeatingDescription": "Underfloor heating description", "hearth": "Hearth", "hearthDescription": "Hearth description", "addHearth": "Add hearth", "shareCertification": "Share certification", "shareCertificateAvailable": "Share certificate available", "redemption": "Redemption", "redeemableByHousingCompany": "Redeemable by housing company", "redeemableByExistingShareholders": "Redeemable by existing shareholders", "redemptionRightAppliesToAllShares": "Redemption right applies to all shares", "otherRestrictions": "Other restrictions", "moreInformation": "More information", "additionalRealtyDetailLinks": "Additional realty detail links", "additionalRealtyDetailLink": "Additional realty detail link", "linkTitle": "Link title", "moreInformationLink": "More information link", "url": "URL", "modifications": "Modifications", "livingType": "Living type", "addPremise": "Add Housing company premise specification", "additionalRealtyDetailsLink": "Additional realty details link", "glazedTerrace": "Glazed terrace", "terraceDescription": "Terrace description", "compassPoint": "Compass point", "glassMaintenanceResponsibility": "Glass maintenance responsibility", "balconyType": "Balcony type", "otherTypeDescription": "Other type description", "balconyDescription": "Balcony description", "hasPrivateYard": "Has private yard", "basisForPossession": "Basis for possession", "basisForPossessionDescription": "Basis for possession description", "patioDescription": "Patio description", "storageType": "Storage type", "transfer": "Transfer", "storageUsageLimitations": "Storage usage limitations", "storageDescription": "Storage description", "refrigeratedCellar": "Refrigerated cellar", "boilerRoomDescription": "Boiler room description", "parkingDescription": "Parking description", "moreInformationAboutThePremises": "More information about the premises", "moreInformationAboutTheMaterials": "More information about the materials", "transactionDescription": "Transaction description", "transactionIncludes": "Transaction includes", "transactionDoesNotInclude": "Transaction does not include", "roomDescription": "Room description", "equipmentDescription": "Equipment description", "monthlyRentPrice": "Monthly rent price", "damageType": "Damage type", "damageDate": "Damage date", "causeDescription": "Cause description", "extentDescription": "Extent description", "repairDescription": "Repair description", "damageDescriptionLong": "A separate document can be attached which describes the damages in more detail.", "featureName": "Feature name", "description": "Description", "squareMeters": "Square meters", "isRented": "Is rented", "managementCost": "Management cost", "financingCost": "Financing cost", "plotCost": "Plot cost", "specialCost": "Special cost", "maintenanceCost": "Maintenance cost", "costsDescription": "Costs description", "repaymentHolidayPeriod": "Repayment holiday period", "startDateOfTheRepaymentHolidayPeriod": "Start date of the repayment holiday period", "endDateOfTheRepaymentHolidayPeriod": "End date of the repayment holiday period", "financialChargeAfterTheRepaymentHoliday": "Financial charge after the repayment holiday", "maintenanceChargeAfterTheRepaymentHoliday": "Maintenance charge after the repayment holiday", "moreInformationAboutTheChargesLink": "More information about the charges link", "hasBalcony": "Has balcony", "hasTerrace": "Has terrace", "redemptionDescription": "Redemption description", "livingFloorCount": "Number of living floors", "startingDebtFreePrice": "Starting debt free price", "startingDebtShareAmount": "Starting debt share amount", "availabilityDate": "Availability date"}, "enums": {"areaKind": {"MAJOR_REGION": "Major region", "REGION": "Region", "SUBREGION": "Subregion", "MUNICIPALITY": "Municipality", "DISTRICT": "District"}, "common": {"YES": "Yes", "NO": "No", "UNKNOWN": "Unknown"}, "televisionType": {"ANTENNA": "<PERSON><PERSON><PERSON>", "CABLE": "Cable", "IPTV": "IPTV", "SATELLITE": "Satellite"}, "unbuiltPlotValue": {"hasBuildings": "Plot has buildings", "hasNoBuildings": "Plot has no buildings", "true": "Plot has buildings", "false": "Plot has no buildings", "TRUE": "Plot has buildings", "FALSE": "Plot has no buildings"}, "leaseType": {"FIXED_TERM": "Fixed term", "UNTIL_FURTHER_NOTICE": "Until further notice"}, "chargePeriod": {"NOT_KNOWN": "Not known", "MONTH": "Month", "MONTH_PER_PERSON": "Month per person", "SINGLE_PAYMENT": "Single payment", "YEAR": "Year", "OTHER": "Other"}, "costType": {"ALERT_CONTROL_SYSTEM": "Alert control system", "CLEANING": "Cleaning", "ELECTRIC_CAR_CHARGING_POINT": "Electric car charging point", "ELECTRIC_HEATING_COSTS": "Electric heating costs", "GARAGE": "Garage", "OTHER_HEATING_COSTS": "Other heating costs", "OUTDOOR_PARKING_SPACE": "Outdoor parking space", "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG": "Outdoor parking space with electrical plug", "PARKING_SPACE_FEE": "Parking space fee", "PROPERTY_TAX": "Property tax", "ROAD": "Road", "SAUNA": "Sauna", "SPACE_IN_CARPORT": "Space in carport", "SPACE_IN_CARPORT_WITH_ELECTRICAL_PLUG": "Space in carport with electrical plug", "SPACE_IN_PARKING_HALL": "Space in parking hall", "TELECOM_CONNECTION": "Telecom connection", "TV": "TV", "USE_OF_HOUSE_DRYING_ROOM": "Use of house drying room", "USE_OF_HOUSE_LAUNDRY_ROOM": "Use of house laundry room", "WASTE": "Waste", "WATER": "Water", "WATER_AND_WASTE": "Water and waste"}, "waterChargeType": {"BASIC_FEE": "Basic fee", "BY_USE": "By use", "BY_USE_WITH_ADVANCE": "By use with advance", "INCLUDED_IN_MAINTENACE_CHARGE": "Included in maintenance charge", "INCLUDED_IN_RENT": "Included in rent"}, "areaBasis": {"ARTICLES_OF_ASSOCIATION": "Articles of association", "MANAGERS_CERTIFICATE": "Managers certificate", "VERIFYING_MEASUREMENT": "Verifying measurement"}, "ownershipTypeCode": {"OWN": "Own", "PARTIAL_OWNERSHIP": "Partial ownership", "LIVING_RIGHT": "Living right"}, "commissionType": {"FIXED": "Fixed", "PERCENT": "Percent"}, "contractType": {"EXCLUSIVE": "Exclusive", "NON_EXCLUSIVE": "Non-Exclusive"}, "listingType": {"SALE": "For Sale", "RENTAL": "For Rent"}, "ownershipType": {"SHARE": "Share", "PROPERTY": "Property"}, "propertyTypeGroup": {"COMMERCIAL_PROPERTY": "Commercial property", "ESTATE": "Estate", "LEISURE": "Leisure", "OTHER": "Garages and other", "PLOT": "Plot", "RESIDENTIAL": "Residential", "SHARED_APARTMENT": "Shared apartment", "SUBLEASE": "Sublease"}, "hearthTypeCode": {"CONVECTION_FIREPLACE": "Convection Fireplace", "FIREPLACE": "Fireplace", "FLUE_IN_PLACE": "Flue in Place", "HEAT_RETAINING_FIREPLACE": "Heat Retaining Fireplace", "IRON_STOVE": "Iron Stove", "OPEN_FIREPLACE": "Open Fireplace", "PLACE_ALLOCATED_FOR_FIREPLACE": "Place Allocated for Fireplace", "BAKING_OVEN": "Baking Oven", "OTHER": "Other"}, "propertyType": {"APARTMENT_HOUSE": "Apartment house", "APARTMENT_PLOT": "Apartment plot", "ARABLE_FARM": "<PERSON><PERSON> farm", "BALCONY_ACCESS_BLOCK": "Balcony access block", "BOAT": "Boat", "BUSINESS_OR_INDUSTRIAL_PLOT": "Business or industrial plot", "CAR_SHED": "Car shed", "CAR_SHELTER": "Car shelter", "CARE_FACILITY": "Care facility", "COMMERCIAL_PLOT": "Commercial plot", "COTTAGE_OR_VILLA": "Cottage or villa", "COWORKING": "Coworking", "DETACHED_HOUSE": "Detached house", "FARM": "Farm", "FOREST": "Forest", "GARAGE": "Garage", "HOLIDAY_PLOT": "Holiday plot", "HOUSE_PLOT": "House plot", "INDUSTRIAL_PLOT": "Industrial plot", "LEISURE_APARTMENT": "Leisure apartment", "OFFICE_SPACE": "Office space", "OTHER": "Other", "PARCEL_OF_LAND": "Parcel of land", "PARKING_SLOT": "Parking slot", "PRODUCTION_FACILITY": "Production facility", "RETAIL_SPACE": "Retail space", "ROW_HOUSE": "Row house", "ROW_HOUSE_PLOT": "Row house plot", "SEMI_DETACHED_HOUSE": "Semi detached house", "SEPARATE_HOUSE": "Separate house", "SOLAR_FARM": "Solar farm", "STORAGE": "Storage", "STORAGE_PLOT": "Storage plot", "TIME_SHARE_APARTMENT": "Time share apartment", "WAREHOUSE": "Warehouse", "WILDERNESS": "Wilderness", "WOODEN_HOUSE_APARTMENT": "Wooden house apartment"}, "floorSurfaceMaterial": {"TILED": "Tiled", "LAMINATE": "Laminate", "PARQUET": "Pa<PERSON><PERSON>", "PLASTIC": "Plastic", "BOARD": "Board", "STONE": "Stone", "CONCRETE": "Concrete", "MICROCEMENT": "Microcement", "VINYL": "Vinyl", "VINYL_CORK": "Vinyl cork", "CORK": "Cork", "ACRYLIC_MASS": "Acrylic mass", "WALL_TO_WALL_CARPET": "Wall-to-wall carpet", "OTHER": "Other"}, "wallSurfaceMaterial": {"CERAMIC_TILE": "Ceramic tile", "WOOD": "<PERSON>", "LOG": "Log", "PANEL": "Panel", "WAINSCOT": "Wainscot", "WALLPAPER": "Wallpaper", "GLASS_FIBRE_TEXTILE_COVERED": "Glass fibre textile covered", "GLASS": "Glass", "PARTIALLY_TILED": "Partially tiled", "PLASTIC": "Plastic", "STONE": "Stone", "CONCRETE": "Concrete", "MICROCEMENT": "Microcement", "PAINT": "Paint", "OTHER": "Other"}, "ceilingSurfaceMaterial": {"PLASTER": "<PERSON><PERSON><PERSON>", "PANEL": "Panel", "STONE": "Stone", "PAINTED": "Painted", "WOOD": "<PERSON>", "OTHER": "Other"}, "residentialType": {"APARTMENT_HOUSE": "Apartment house", "DETACHED_HOUSE": "Detached house", "ROW_HOUSE": "Row house", "SEMI_DETACHED_HOUSE": "Semi-detached house", "SEPARATE_HOUSE": "Separate house", "WOODEN_HOUSE_APARTMENT": "Wooden house apartment", "BALCONY_ACCESS_BLOCK": "Balcony access block", "COTTAGE": "Cottage", "TIME_SHARE_APARTMENT": "Time-share apartment", "LEISURE_APARTMENT": "Leisure apartment", "OTHER": "Other"}, "floorLevel": {"ON_BOTTOM_FLOOR": "On bottom floor", "ON_MIDDLE_FLOOR": "On middle floor", "ON_TOP_FLOOR": "On top floor", "NOT_KNOWN": "Not known"}, "livingFloorCount": {"SINGLE_FLOOR": "Single floor", "TWO_FLOORS": "Two floors", "FLOOR_AND_A_HALF": "Floor and a half", "MORE_THAN_TWO_FLOORS": "More than two floors", "NOT_KNOWN": "Not known"}, "housingCompanyMaintenanceType": {"JANITOR": "<PERSON><PERSON>", "PROPERTY_MAINTENANCE_COMPANY": "Property maintenance company", "BY_RESIDENTS": "By residents"}, "developmentPhase": {"PRE_MARKETING": "Pre-marketing", "IN_CONSTRUCTION": "In construction", "MOVE_IN_READY": "Move-in ready"}, "constructionMaterial": {"BRICK": "Brick", "CONCRETE": "Concrete", "ELEMENT": "Element", "STEEL": "Steel", "STONE": "Stone", "TIMBER": "<PERSON><PERSON>", "WOOD": "<PERSON>", "OTHER": "Other"}, "outerRoofType": {"GABLED": "Gabled", "HIPPED": "Hipped", "PENT": "Pen<PERSON>", "FLAT": "Flat", "GAMBREL": "G<PERSON><PERSON><PERSON>", "MANSARD": "<PERSON><PERSON>", "SATERI": "<PERSON><PERSON><PERSON>", "OTHER": "Other"}, "outerRoofMaterial": {"BRICK": "Brick", "SHEET_METAL": "Sheet metal", "FELT": "Felt", "BITUMEN_FELT": "Bitumen felt", "REINFORCED_CONCRETE": "Reinforced concrete", "PVC": "PVC", "STONE_COATED_METAL": "Stone-coated metal", "COPPER": "Copper", "GREEN_ROOF": "Green roof", "OTHER": "Other"}, "heatingSystem": {"DISTRICT_HEATING": "District heating", "ELECTRIC": "Electric", "GAS": "Gas", "GEOTHERMAL_HEATING": "Geothermal heating", "EXHAUST_AIR_HEAT_PUMP": "Exhaust air heat pump", "OIL": "Oil", "SUN": "Sun", "WATER_HEAT_PUMP": "Water heat pump", "WOOD": "<PERSON>", "OTHER": "Other"}, "heatDistributionSystem": {"AIR_HEAT_PUMP": "Air heat pump", "ELECTRIC_CEILING_HEATING": "Electric ceiling heating", "ELECTRIC_RADIATOR": "Electric radiator", "ELECTRIC_UNDERFLOOR_HEATING": "Electric underfloor heating", "WATER_RADIATOR": "Water radiator", "WATER_UNDERFLOOR_HEATING": "Water underfloor heating", "OTHER": "Other"}, "ventilationSystem": {"FORCED_EXHAUST": "Forced exhaust", "FORCED_INTAKE": "Forced intake", "GRAVITY_BASED": "Gravity-based", "HEAT_RECOVERY": "Heat recovery", "HEATING_AND_COOLING": "Heating and cooling"}, "premiseType": {"APARTMENT_SPECIFIC_STORAGE": "Apartment specific storage", "ATTIC_STORAGE": "Attic storage", "BALCONY": "Balcony", "CELLAR_STORAGE": "Cellar storage", "CRAFT_ROOM": "Craft room", "DRYING_ROOM": "Drying room", "EMERGENCY_SHELTER": "Emergency shelter", "LAUNDRY_ROOM": "Laundry room", "MANGLE_ROOM": "Mangle room", "MOVABLE_PROPERTY_STORAGE": "Movable property storage", "REFRIGERATED_CELLAR": "Refrigerated cellar", "SPORTS_EQUIPMENT_STORAGE": "Sports equipment storage", "SWIMMING_POOL": "Swimming pool", "OTHER": "Other"}, "plotPropertyType": {"APARTMENT_HOUSE_PLOT": "Apartment house plot", "HOLIDAY_PLOT": "Holiday plot", "HOUSE_PLOT": "House plot", "ROW_HOUSE_PLOT": "Row house plot", "COMMERCIAL_OR_INDUSTRIAL_PLOT": "Commercial or industrial plot", "OTHER": "Other"}, "holdingType": {"OWN": "Own", "LEASEHOLD": "Leasehold"}, "parkingSpaceType": {"CARPORT_SPACE": "Carport space", "CARPORT_SPACE_WITH_ELECTRICAL_PLUG": "Carport space with electrical plug", "CHARGING_POINT_FOR_ELECTRICAL_CARS": "Charging point for electrical cars", "GARAGE": "Garage", "OUTDOOR_PARKING_SPACE": "Outdoor parking space", "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG": "Outdoor parking space with electrical plug", "PARKING_HALL_SPACE": "Parking hall space", "PARKING_SPACE_SHARE": "Parking space share"}, "housingCompanyPremiseType": {"APARTMENT": "Apartment", "BUSINESS_PREMISE": "Business premise", "OTHER_PREMISE": "Other premise"}, "renovationStatus": {"PLANNED": "Planned", "IN_PROGRESS": "In progress", "FINISHED": "Finished"}, "renovationType": {"KITCHEN": "Kitchen", "BATHROOM": "Bathroom", "FLOOR": "Floor", "PLUMBING": "Plumbing", "FACADE": "Facade", "ROOF": "<PERSON><PERSON>", "BALCONY": "Balcony", "WINDOW": "Window", "LIFT": "Lift", "ELECTRICAL": "Electrical", "SUBDRAINAGE": "Subdrainage", "OTHER": "Other"}, "housingCompanyInspectionType": {"CONDITION_ASSESSMENT": "Condition assessment", "CONDITION_INSPECTION": "Condition inspection", "CONDITION_SURVEY": "Condition survey", "HUMIDITY_MEASUREMENT": "Humidity measurement", "MAINTENANCE_NEED_STATEMENT": "Maintenance need statement", "LONG_TERM_PLAN": "Long-term plan", "HUMIDITY_INSPECTION": "Humidity inspection"}, "zoningType": {"CITY_PLAN": "City plan", "RURAL_AREA": "Rural area", "UNZONED": "Unzoned", "COMMISSIONING_INSPECTION": "Commissioning inspection", "FINAL_INSPECTION": "Final inspection", "COMPONENT_MASTER_PLAN": "Component master plan", "DEVIATION_DECISION": "Deviation decision", "DETAILED_PLAN": "Detailed plan", "BUILDING_FORBID": "Building forbid", "BUILDING_PERMIT": "Building permit", "DETAILED_SHORE_PLAN": "Detailed shore plan", "SHORE_PLAN_AREA": "Shore plan area", "AREA_REQUIRING_PLANNING": "Area requiring planning", "ACTION_BAN": "Action ban", "MASTER_PLAN": "Master plan"}, "beachCode": {"NO_BEACH": "No beach", "SHARED_BEACH": "Shared beach", "OWN_BEACH": "Own beach", "BEACH_RIGHT": "Beach right", "RIGHT_TO_WATER_AREA": "Right to water area", "NEXT_TO_RELICTION_AREA": "Next to reliction area"}, "beachTypeCode": {"RIVER": "River", "LAKE": "Lake", "POND": "Pond", "SEA": "Sea", "OTHER": "Other"}, "energyCertificateTypeCode": {"A_2007": "A (2007)", "B_2007": "B (2007)", "C_2007": "C (2007)", "D_2007": "D (2007)", "E_2007": "E (2007)", "F_2007": "F (2007)", "G_2007": "G (2007)", "A_2013": "A (2013)", "B_2013": "B (2013)", "C_2013": "C (2013)", "D_2013": "D (2013)", "E_2013": "E (2013)", "F_2013": "F (2013)", "G_2013": "G (2013)", "A_2018": "A (2018)", "B_2018": "B (2018)", "C_2018": "C (2018)", "D_2018": "D (2018)", "E_2018": "E (2018)", "F_2018": "F (2018)", "G_2018": "G (2018)", "H": "H", "NOT_AVAILABLE": "Not Available", "NOT_REQUIRED": "Not Required"}, "additionalRealtyDetailLinkTypeCode": {"VIRTUAL_PRESENTATION": "Virtual Presentation", "VIDEO_PRESENTATION": "Video Presentation", "OTHER": "Other"}, "administrationTypeCode": {"APARTMENT_HOUSING_COMPANY": "Apartment housing company", "REAL_ESTATE_COMPANY": "Real estate company", "PART_OWNERSHIP": "Part ownership", "OTHER": "Other"}, "livingType": {"title": "Living type", "NONSUBSIDISED": "Non-Subsidised", "INTEREST_SUBSIDIED": "Interest Subsidised", "SHARED_APARTMENT": "Shared Apartment", "SUBTENANCY": "Subtenancy", "SENIOR_HOUSE": "Senior House", "SERVICE_HOUSE": "Service House", "EMPLOYMENT_BENEFIT_APARTMENT": "Employment Benefit Apartment", "STUDENT_APARTMENT": "Student Apartment"}, "newBuilding": {"title": "New building", "NO": "Not new building", "UNKNOWN": "Unknown", "YES": "New building"}, "livingFloorCountCode": {"title": "Living floor count", "SINGLE_FLOOR": "Single Floor", "TWO_FLOORS": "Two Floors", "FLOOR_AND_A_HALF": "Floor and a Half", "MORE_THAN_TWO_FLOORS": "More than Two Floors", "NOT_KNOWN": "Not Known"}, "available": {"title": "Available", "AVAILABLE": "Available", "NEGOTIABLE": "Negotiable", "DATE": "Date", "RENTED": "Rented", "OTHER": "Other", "description": "Availability description"}, "conditionCode": {"NEW": "New", "GOOD": "Good", "SATISFACTORY": "Satisfactory", "TOLERABLE": "Tolerable", "UNCLASSIFIED": "Unclassified"}, "unbuiltPlot": {"true": "Yes", "false": "No", "null": "Unknown", "TRUE": "Yes", "FALSE": "No"}, "space": {"BATH_ROOM": "Bathroom", "BEDROOM": "Bedroom", "DINING_ROOM": "Dining room", "DRAUGHT_LOBBY": "Draught lobby", "HALL": "Hall", "HALLWAY": "Hallway", "KITCHEN": "Kitchen", "LIBRARY": "Library", "LIVING_ROOM": "Living room", "LOFT": "Loft", "SAUNA": "Sauna", "STUDY": "Study", "TOILET": "<PERSON><PERSON><PERSON>", "UTILITY_ROOM": "Utility room", "WALK_IN_CLOSET": "Walk-in closet", "OTHER": "Other", "TERRACE": "Terrace", "YARD": "Yard", "BALCONY": "Balcony", "STORAGE": "Storage"}, "compassPoint": {"NORTH": "North", "EAST": "East", "SOUTH": "South", "WEST": "West", "NORTHEAST": "Northeast", "SOUTHEAST": "Southeast", "SOUTHWEST": "Southwest", "NORTHWEST": "Northwest"}, "balconyGlassMaintenanceResponsibility": {"HOUSING_COMPANY": "Housing Company", "MAINTENANCE_COMPANY": "Maintenance Company", "RESIDENTS": "Residents"}, "basisForPossessionCodes": {"BELONGS_TO_APARTMENT_ACCORDING_TO_THE_ARTICLES_OF_ASSOCIATION": "Belongs to Apartment According to the Articles of Association", "RENTED_FROM_THE_COMPANY": "Rented from the Company", "WITH_A_DIFFERENT_GROUP_OF_SHARES": "With a Different Group of Shares"}, "basisForPossessionCodeYard": {"ARTICLES_OF_ASSOCIATION": "Articles of Association", "NOT_KNOWN": "Not Known", "OTHER": "Other"}, "propertyStorageType": {"ATTIC": "Attic", "CELLAR": "Cellar", "OUTDOOR": "Outdoor", "REFRIGERATED_CELLAR": "Refrigerated Cellar", "OTHER": "Other"}, "transferCode": {"TRANSFERABLE": "Transferable", "NON_TRANSFERABLE": "Non-transferable", "NO_SELECTION": "No selection"}, "balconyType": {"FRENCH_WINDOW": "French window", "GLAZED": "Glazed balcony", "PROTRUDING": "Protruding balcony", "RETRACTED": "Retracted balcony", "OTHER": "Other type"}, "damageType": {"WATER_DAMAGE": "Water damage", "MOISTURE_DAMAGE": "Moisture damage", "OTHER": "Other"}, "featureCode": {"WC": "WC", "SHOWER": "Shower", "TWO_SHOWERS": "Two showers", "SHOWER_WALL": "Shower wall", "WALK_IN_SHOWER": "Walk-in shower", "WASHING_MACHINE_CONNECTION": "Washing machine connection", "FIXED_LAMPS": "Fixed lamps", "WASHING_MACHINE": "Washing machine", "TUMBLE_DRYER": "Tumble dryer", "DRYING_CABINET": "Drying cabinet", "MIRROR": "Mirror", "MIRROR_CABINET": "Mirror cabinet", "BASIN_CABINET": "Basin cabinet", "JACUZZI": "<PERSON><PERSON><PERSON><PERSON>", "BATHTUB": "Bathtub", "BATHROOM_CABINETS": "Bathroom cabinets", "REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT": "Refrigerator with small freezer compartment", "REFRIGERATOR_FREEZER": "Refrigerator-freezer", "REFRIGERATOR": "Refrigerator", "REFRIGERATED_CABINET": "Refrigerated cabinet", "COLD_ROOM": "Cold room", "FREEZER": "<PERSON><PERSON>", "REFRIGERATOR_CHILLER": "Refrigerator-chiller", "DISHWASHER": "Dishwasher", "DISHWASHER_CONNECTION": "Dishwasher connection", "RESERVED_LOCATION_FOR_DISHWASHER": "Reserved location for dishwasher", "INTEGRATED_DISHWASHER": "Integrated dishwasher", "FREE_STANDING_ISLANDS": "Free standing islands", "FREE_STANDING_CABINETS": "Free standing cabinets", "WINE_CABINET": "Wine cabinet", "INTEGRATED_HOUSEHOLD_APPLIANCES": "Integrated household appliances", "CERAMIC_STOVE": "Ceramic stove", "HOB": "<PERSON><PERSON>", "BAKING_OVEN": "Baking oven", "COOKTOP": "<PERSON><PERSON>", "INTEGRATED_STOVE": "Integrated stove", "EXTRACTOR_HOOD": "Extractor hood", "WOOD_BURNING_STOVE": "Wood-burning stove", "ELECTRIC_STOVE": "Electric stove", "INDUCTION_STOVE": "Induction stove", "GAS_STOVE": "Gas stove", "MICROWAVE_OVEN": "Microwave oven", "SEPARATE_OVEN": "Separate oven", "EXTRACTOR_HOOD_WITH_FLUE": "Extractor hood with flue", "COOKER_HOOD": "Cooker hood", "CONCRETE": "Concrete", "STONE": "Stone", "WOOD": "<PERSON>", "LAMINATE": "Laminate", "COMPOSITE": "Composite", "METAL": "Metal", "ROOM_WITH_FIREPLACE": "Room with fireplace", "ELECTRIC_HEATER": "Electric heater", "WOOD_HEATED_SAUNA_STOVE": "Wood-heated sauna stove", "ALWAYS_READY_HEATER": "Always ready heater", "READY_FOR_ELECTRIC_HEATER": "Ready for electric heater", "INTEGRATED_BUCKET": "Built-in water bucket", "WATER_FAUCET": "Water faucet", "OPTICAL_FIBRE_LIGHTING": "Optical fibre lighting", "LED_LIGHTING": "LED lighting", "WINDOW_OUT": "Window out", "FLOOR_MOUNTED_WC": "Floor-mounted WC", "WALL_HUNG_WC": "Wall-hung WC", "BIDET": "<PERSON><PERSON><PERSON>", "UNDERFLOOR_HEATING": "Underfloor heating", "FLOOR_DRAIN": "Floor drain", "LAUNDRY_CABINETS": "Laundry cabinets", "TABLE_TOP": "Table top", "IRONING_TABLE_BOARD": "Ironing table board", "BABY_CHANGING_TABLE": "Baby changing table", "SINK": "Sink", "SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR": "Shower and drain by exterior door", "CENTRAL_VACUUM_UNIT": "Central vacuum unit", "EXIT": "Exit"}}, "messages": {"propertyTypeGroupEmpty": "Select a listing type and a ownership type first", "propertyTypeEmpty": "Select a property type group first", "propertyCreatedWithId": "Property #{{id}} created", "canViewProperty": "You can view and edit the property"}, "add": {"BATH_ROOM": "Add bathroom", "BEDROOM": "Add bedroom", "DINING_ROOM": "Add dining room", "DRAUGHT_LOBBY": "Add draught lobby", "HALL": "Add hall", "HALLWAY": "Add hallway", "KITCHEN": "Add kitchen", "LIBRARY": "Add library", "LIVING_ROOM": "Add living room", "LOFT": "Add loft", "SAUNA": "Add sauna", "STUDY": "Add study", "TOILET": "Add toilet", "UTILITY_ROOM": "Add utility room", "WALK_IN_CLOSET": "Add walk-in closet", "OTHER": "Add other", "TERRACE": "Add terrace", "YARD": "Add yard", "BALCONY": "Add balcony", "STORAGE": "Add storage", "cost": "Add cost", "link": "Add link", "plot": "Add plot", "damage": "Add damage", "featureDescription": "Add feature description"}, "instructions": {"housingCompanyCost": "Housing company cost", "cost": "To manage housing company costs, please go to the “Housing company” section in the menu.", "commission": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.", "floor": "Identifies the number of floor found from the building in which the apartment is located. This value can be set only if the building in which the apartment is located has multiple floors.", "housingCompany": "Important: Changes made to this housing company will also impact any properties associated with it."}}, "event": {"upcoming": "Upcoming Events", "past": "Past Events", "actions": {"createEvent": "Create Event"}, "delete": {"title": "Delete event", "description": "Are you sure you want to delete this event?", "button": "Delete this event", "toast": {"title": "Event deleted", "description": "Event has been deleted successfully."}}, "enums": {"eventType": {"OPEN_HOUSE_VIEWING": "Open-House Viewing", "PRIVATE_VIEWING": "Private Viewing", "CLIENT_MEETING": "Client Meeting", "NOTARY_MEETING": "Notary Meeting", "OTHER": "Other"}, "eventFormat": {"PHYSICAL": "Physical", "VIRTUAL": "Virtual", "HYBRID": "Hybrid"}}, "fields": {"eventType": "Event Type", "eventFormat": "Event Format", "startTime": "Start time", "endTime": "End time", "location": "Location", "url": "URL", "isAllDay": "All day", "contacts": "Contacts"}, "messages": {"eventCreatedWithId": "Event #{{id}} created", "eventUpdatedWithId": "Event #{{id}} updated", "canViewEvent": "You can view and edit the event"}, "createEvent": "Create Event", "updateEvent": "Update Event"}, "changesDetectedToast": {"title": "Publish Changes", "description": "Changes detected! To update this property on the\n portals, please sync now."}, "addDocuments": "Add Documents", "addMedia": "Add Media", "propertyStrandified": "This property has been Strandified", "activateUser": {"button": "Activate User", "activatedUserTitle": "User has been activated", "activatedUserDescription": "Successfully activated user"}, "deactivateUser": {"button": "Deactivate User", "deactivatedUserTitle": "User has been deactivated", "deactivatedUserDescription": "Successfully deactivated user"}, "sortDropdown": {"sortBy": "Sort By", "recentlyCreated": "Recently Created", "recentlyUpdated": "Recently Updated", "lowestSalePrice": "Lowest Sale Price", "highestSalePrice": "Highest Sale Price", "alphabeticallyAscending": "Alphabetically Ascending", "alphabeticallyDescending": "Alphabetically Descending"}, "viewing": "Viewing", "contacts": "Contacts", "consenters": "Consenters", "groups": "Groups", "isRequired": "is required", "propertyValidation": {"showMore": "Show More", "showLess": "Show Less", "missingInformationIssueTitle": "Missing Information", "missingInformationIssueMessage": "Your property won't be published properly until these requirements are met. Please review the listing details and update accordingly.", "missingInformationOkayTitle": "Great, you’re all set! 🎉", "missingInformationOkayMessage": "This listing meets the content requirements.", "showDetails": "Show Details", "hideDetails": "Hide Details", "salesAgreementTitle": "Create Sales Agreement", "salesAgreementIssueMessage": "Please create a sales agreement for the property and make sure it is signed by all parties. This step is considered finished only when the signed sales agreement document is uploaded again (via Property / Resources / Contracts)", "salesAgreementOkayMessage": "The property has a signed sales agreement attached. No issues detected.", "imageQualityTitle": "Upload media to meet Strand guidelines", "imageQualityIssueMessage": "Please upload at least 10 images of the property, and make sure the images are high quality and to include the floor plan of the property.", "imageQualityOkayMessage": "The property has images that meet Strand guidelines. No issues detected.", "missingFieldsTitle": "Fill in all required listing info", "missingFieldsIssueMessage": "Some of the required fields to publish to portals are missing. Please fill as much details about the property as possible. Click \"Show Details\" to show the missing fields", "missingFieldsOkayMessage": "The property has all required fields filled in. No issues detected."}, "agreementDetails": "Agreement details", "back": "Back", "nextStep": "Next step", "saveAndDownload": "Save & Download", "offer": {"title": "Offer", "create": "Create Reservation & Deposit Proposal", "contactDetail": "Contact details", "editContact": "Edit contact", "sellers": "Sellers", "buyers": "Buyers", "realtors": "Realtors", "reviewPropertyDetails": "Review property details", "editProperty": "Edit property", "agreementReady": "Your agreement is now ready!", "saveAndDownloadContractBelow": "Save and download the contract below.", "createdOffer": "Offer created", "createSuccessful": "Successfully created offer {{offerId}}", "deleteOffer": "Offer deleted", "deleteSuccessful": "Successfully deleted offer", "deletedFail": "Offer could not be deleted. Please try again later", "rejected": "Offed rejected", "rejectedSuccessful": "The buyer’s agent will receive a rejection email.", "rejectedFail": "Offer could not be rejected. Please try again later", "status": {"created": "Created", "pending": "Pending", "declined": "Declined", "signed": "Signed"}, "offered": "Offered"}, "sellingOffer": {"title": "Create offer", "address": "Address", "price": "Price", "commission": "Commission in %", "note": "Other note", "realtor": "Realtor", "noteTooLong": "Note must be at most {{characterLimit}} characters long", "pdfError": "PDF error", "errorLoadingTemplate": "Cannot load pdf template", "errorFileType": "File type is not supported", "errorCreatePDF": "Cannot create pdf file", "errorLoadingPhoto": "Cannot load photo", "pdfSuccess": "PDF created", "pdfSuccessMessage": "Selling offer PDF created successfully for"}, "brokerageOffer": {"title": "Create brokerage offer", "address": "Property address", "priceInquiry": "Price inquiry", "priceInquiryError": "Price must be positive", "commission": "Commission in %", "commissionError": "Commission must be between 0 and 100", "realtor": "Realtor", "realtorDescription": "Optional realtor description", "realtorDescriptionDefault": "I offer high-quality real estate brokerage, with your property being the main focus. My work is supported by the Strand Properties sales concept, which allows for a secure and faster sale, as well as a higher sale price. My passion is the successful sale of properties; so you can focus on other things.", "realtorDescriptionTooLong": "Realtor description must be at most {{characterLimit}} characters long", "notes": "Optional additional notes", "notesTooLong": "Notes must be at most {{characterLimit}} characters long", "pdfError": "PDF error", "errorCreatePDF": "Cannot create PDF file", "pdfSuccess": "PDF created", "pdfSuccessMessage": "Brokerage offer PDF created successfully", "emailSuccess": "Email sent", "emailSuccessMessage": "Brokerage offer email sent successfully", "generatePDF": "Generate PDF", "generating": "Generating...", "sharingType": "Way of sharing", "shareWith": "Choose recipients", "recipientRequired": "At least one recipient is required", "contact": "Contact", "missingFieldsPrefix": "Fill the missing fields in your user profile:", "missingPhone": "Phone number", "missingEmail": "Email", "missingJobTitle": "Job title", "missingCompanyName": "Company name", "missingCompanyId": "Company ID", "enums": {"sharingType": {"print": "Print", "email": "Email"}}, "realtorNotAvailable": "Realtor information not available"}, "terms": "Terms", "depositDetail": "Deposit details", "buyers": "Buyer(s)", "agreeSalePrice": "Agreed Sale Price", "specialCondition": "Special condition", "reservationDepositAmount": "Reservation Deposit Amount", "depositPayee": "Deposit payee", "depositPaymentWillBePaidTo": "Deposit payment will be paid to", "standPropertiesSL": "Strand Properties SL", "IBANNumber": "IBAN number", "purchasePriceShallBeSettledBy": "Offer Agreed Date", "privatePurchaseContractDueDate": "Private purchase contract due date", "typeHere": "Type here...", "review": "Review", "shortTermRent": "Short term rent", "plusSomeOther_one": "+ {{number}} other", "plusSomeOther_other": "+ {{number}} others", "offers": "Offers", "documentId": "Document ID", "soldBy": "Sold by", "soldByStrand": "Strand Properties", "soldByOther": "Someone else", "signingMethods": {"title": "Signing method", "penAndPaper": "Pen & Paper", "eSignature": "E-Signature", "eSignatureComingSoon": "E-Signature (Coming soon)"}, "matchMaking": {"title": "Matchmaking", "start": "Start matchmaking", "success": "Matchmaking started", "linked": "Linked", "leadTicket": "Lead ticket", "shortList": "Shortlisted", "topMatch": "Top matching properties", "matchMakingPropertyNotUpdated": "Matchmaking property not updated. Try again later", "showDisliked": "Show disliked", "shareShortlist": "Share shortlist", "searchCriteria": "Search criteria", "criteria": "Criteria", "priceRange": "Price range", "listingTypes": "Listing types", "sizeRange": "Size range", "canViewEdit": "You can view and edit it", "emailTemplates": "Email templates", "autoSent": "Auto send email", "editSuccess": "Matchmaking edited", "updatedDescription": "Successfully updated match making"}, "missionInformation": "Missing information", "pleaseReviewAndAdjustIfNeed": "Please review the empty details below and adjust if needed.", "videoAndTour": "Video & Tour", "images": "Images", "uploadImages": "Upload Images", "addVideo": "Add Video", "addTour": "Add Tour", "videoUrl": "Video URL", "tourUrl": "Tour URL", "videoAddedTitle": "Video added!", "tourAddedTitle": "Tour added!", "videoAddedDescription": "Successfully added video", "tourAddedDescription": "Successfully added tour", "companyTypes": {"NOT_APPLICABLE": "Not applicable", "LIGHT_ENTREPRENEUR": "Light entrepreneur (Autónomo)", "COMPANY_SL": "Company (SL)"}, "downloading": "Downloading ...", "downloadPDF": "Download PDF", "documentTemplate": {"READY_SIGN": {"title": "Your document\nis ready to be signed", "content": "Raesent congue venenatis lectus, et euismod sapien mollis in. Aliquam quis leo faucibus, eleifend nunc sit amet, malesuada nunc.", "buttonContent": "Sign document"}, "CANCEL": {"title": "Signing cancelled", "content": "Raesent congue venenatis lectus, et euismod sapien mollis in. Aliquam quis leo faucibus, eleifend nunc sit amet, malesuada nunc.", "buttonContent": "Try again"}, "LOCKED": {"title": "Your document is locked", "content": "Raesent congue venenatis lectus, et euismod sapien mollis in. Aliquam quis leo faucibus, eleifend nunc sit amet, malesuada nunc.", "buttonContent": "Locked document"}, "SIGNED": {"title": "Document signed\nsuccessfully!", "content": "Raesent congue venenatis lectus, et euismod sapien mollis in. Aliquam quis leo faucibus, eleifend nunc sit amet, malesuada nunc.", "buttonContent": "Document signed"}, "SUCCESSFUL": {"title": "Document signed\nsuccessfully!", "content": "Raesent congue venenatis lectus, et euismod sapien mollis in. Aliquam quis leo faucibus, eleifend nunc sit amet, malesuada nunc.", "buttonContent": "Download document"}}, "strandPropertiesOfferSigned": "Strand Properties offer signed", "propertyAddress": "Property address", "offerAgreedDate": "Offer agreed date", "salePrice": "Sale price", "depositPaid": "Deposit paid", "account": {"STRAND_CLIENTS_ACCOUNT": "Strand clients account", "STRAND_COMMISSION": "Deposit is part of Strand commission", "LAWYERS_ACCOUNT_BUYER": "Lawyers account (buyer)", "LAWYERS_ACCOUNT_VENDOR": "Lawyers account (vendor)", "DEVELOPERS_ACCOUNT": "Developers account", "VENDORS_ACCOUNT": "Vendor account", "NO_DEPOSIT_PAID": "No deposit paid", "OTHER": "Other"}, "commissionUnit": {"PERCENT_PLUS_VAT": "% + VAT", "AMOUNT_PLUS_VAT": "{{currency}} + VAT", "PERCENT_VAT_INCLUDED": "% VAT included", "AMOUNT_VAT_INCLUDED": "{{currency}} VAT included"}, "dateTheDepositPaidToSeller": "Date the deposit paid to the seller", "describe": "Describe", "dateOfPPC": "Date of Private Purchase Contract", "deadlineOfTheCompletionNotary": "Deadline of the completion notary", "notaryDayBooked": "Notary day booked", "totalCommission": "Total commission", "strandCommission": "Strand commission", "otherAgencyName": "Name of the other agency", "otherAgencyCommission": "Other agency commission", "externalLead": "External lead", "nieNumberOrID": "NIE number / ID", "addSeller": "Add seller", "addBuyer": "Add buyer", "saveDraft": "Save draft", "commissionSplitInHouse": "Commission split in-house", "detailOfSaleReadyToVerify": "Details of sale is now ready to verify", "sendForVerify": "Send for verify", "agent": "Agent", "invoice": "Invoice", "uploadFile": "Upload file", "existProperty": "Existing property", "customReferenceNumber": "Custom reference number", "createOneInvoice": "Create one invoice", "createInvoiceForEachSeller": "Create invoice for each seller", "contractReadyForReview": "Contract ready for review", "theContractWillSendForSellerForReview": "The contract will be sent to Sales support for review, if there is anything you want to mention to the reviewer, please write it below.", "reviewer": "Reviewer", "noteToReviewer": "Note to reviewer", "writeYourNote": "Write your note here....", "noAdminAtThatOffice": "No admin at that office", "saveAsDraft": "Save as Draft", "attachmentType": {"AGENT_INVOICE": "Agent invoice"}, "remove": "Remove", "addAgent": "Add agent", "addAttachment": "Add attachment", "sellerSplit": "Seller {{seller}} split", "attachments": "Attachments", "attachmentFile": "Attach File", "sendForReview": "Send for review", "propertyReference": "Property reference", "createdDateAndTime": "Created date & time", "leadSource": "Lead source", "allocate": "Allocate", "reallocate": "Reallocate", "content": "Content", "processed": "Processed", "unprocessed": "Unprocessed", "SOURCE_TYPE": {"CAMPAIGN_NAME": "Campaign name", "CONTACT_FORM": "Contact form", "UNIQUE_NAME": "Unique name"}, "selections": "Selections", "leadLocated": {"leads": "Leads", "title": "Allocate lead", "successTitle": "Lead allocated", "successContent": "User has been notified and sale activity created."}, "leadDiscarded": {"confirmDiscard": "Discard this manual lead", "confirmUndoDiscard": "<PERSON><PERSON> discard this manual lead", "discardSuccessTitle": "Manual lead has changed status", "discardSuccess": "Manual lead has been discarded", "undoSuccess": "Manual lead has been undo discarded"}, "developmentName": "Development name", "Agent": "Agent", "Ticket": "Ticket", "Contact": "Contact", "IBI": "IBI", "orientations": "Orientations", "role": "Role", "users": "Users", "select": "Select", "detailsOfSale": {"title": "Details of sale", "edit": "Edit details of sale", "files": "Files", "status": {"CANCELLED": "Cancelled", "IN_REVIEW": "In review", "DRAFT": "Draft", "APPROVED": "Approved", "SIGNED": "Signed", "REQUESTING_CHANGES": "Requesting changes"}}, "notaryDate": "Notary date", "download": "Download", "proforma": "<PERSON><PERSON><PERSON>", "view": "View", "associatedRecords": "Associated Records", "contractDetails": "Contract details", "depositPaidTo": "Deposit paid to", "agentInvoices": "Agent invoices", "sellerInvoices": "Seller invoices", "CONTRACT_TYPE": {"OFFER": "Offer", "SALE_AGREEMENT": "Sale agreement", "DETAILS_OF_SALE": "Details of sale"}, "downloadAll": "Download all", "downloadDocumentFailed": "Download failed. Please try again later", "requestChanges": "Request changes", "approve": "Approve", "uploaded": "Uploaded", "moreUser": "+ {{users}} more", "file": "Files", "approveDoS": "Approve Details of Sale", "dates": "Dates", "invoiceIssuedBy": "Invoice issued by", "invoices": "Invoices", "agentNameOnTheInvoice": "Agent name on the invoice", "invoiceDate": "Invoice date", "invoiceDueDate": "Invoice due date", "split": "Split", "agentInvoice": "Agent invoice", "note_simple_pdf": "Nota simple PDF", "passport_pdf": "Passport PDF", "reservation_agreement_signed_pdf": "Reservation agreement signed PDF", "nie_dni": "NIE/DNI", "copySellerPassport": "Copy of the sellers passport", "sale_agreement_signed": "Sale agreement signed", "copyIdentificationDocument": "Copy of the identification documents.", "proof_of_transfer_deposit": "Proof of transfer deposit", "saleAgreementSigned": "Sale agreement signed", "signedSaleAgreementIsAutomaticallyAddedHere": "The signed sale agreement is automatically added here, if not please add it below.", "ibi_receipt": "IBI receipt", "basura_receipt": "Basura receipt", "copy_of_the_title_deed": "Copy of the title deed", "seller_invoices": "Seller Invoice", "agent_invoices": "Agent Invoice", "addAllAgentBelow": "Add all agent invoices below.", "chooseFile": "Choose file", "reviewInvoiceDetails": "Review invoice details", "invoicer": "Invoicer", "readyForApproveDoS": {"yourDoSAreReadyForApproval": "Your Details of Sale are ready for approval!", "approveAndCreateInvoiceBelow": "Approve and create invoices bellow", "approveAndCreateInvoices": "Approve and create invoices"}, "invoiceNumber": "Invoice number", "invoiceAmount": "Invoice amount", "agentName": "Agent name", "approveDos": {"approved": "Invoice created", "sendForReviewDescription": "Approved and create invoice successfully"}, "requestChangesDos": {"changingRequested": "Request changes successfully", "requestChangeDescription": "Realtors will be notified for the changes by email"}, "invoiceOption": "Invoice option", "shareShortlist": {"title": "Share shortlist", "description": "Here's a list of links featuring the properties you've selected for your shortlist. Feel free to copy the text and share it.", "placeholder": "Shortlist", "copyToClipboard": "Copy to clipboard", "copyAsPlainText": "Copy as plain text"}, "duplicateInvoiceNumber": "This invoice number has already exists", "contactNotAssignedToRealtor": "The seller contact is not assigned to realtor. Please check with the office Admins.", "accessWasDenied": "Access was denied", "documentLanguage": "Document language", "add": "Add", "translateTo": "Translate to", "propertyIsRented": "Property is rented", "createNew": "Create new", "addExisting": "Add existing", "addHousingCompany": "Add housing company", "confirm": "Confirm", "organization": "Organization", "extra_dos": "Additions attachments", "uploadFiles": "Upload files", "discard": "Discard", "discarded": "Discarded", "undoDiscard": "Undo discard", "companyReference": "Company reference", "insuranceNoAndCompany": "Insurance No & Company", "material": "Material", "map": "Map", "instructions": "Instructions", "kiviImport": {"button": "Import from Kivi", "title": "Import from Kivi", "refNumber": "Reference number", "inputRefNumber": "Input reference number", "success": "Import successful", "error": "Import failed", "refNumberRequired": "Reference number is required"}, "selectDistrict": "Select district", "selectCity": "Select city", "doSReport": {"totalNumberOfTransactions": "Total number of transactions", "totalSalesPrice": "Total sales price", "totalCommission": "Total commission", "totalCommissionPercentage": "Total commission as % of total sales price", "totalStrandFullCommission": "Total Strand full commission", "strandFullCommissionPercentage": "Strand full commission as % of total sales price", "totalStrandCommissionEarned": "Total Strand commission earned", "totalAgentCommissionEarned": "Total agent commission earned", "strandCommissionPercentage": "Strand commission as % of Strand full commission", "strandOfFullCommissionPercentage": "Strand commission as % of full commission", "subTotalNumberOfTransactions": "count of transactions", "subTotalSalesPrice": "sum of sales price", "subTotalCommission": "net of VAT", "subTotalStrandFullCommission": "net of VAT", "subStrandFullCommissionPercentage": "net of VAT", "subTotalStrandCommissionEarned": "net of VAT", "transactionReference": "Transaction reference", "propertyDescription": "Property description", "salesPrice": "Sales price", "strandCommissionEarned": "Strand commission earned \n(net of VAT)", "agentCommissionEarned": "Agent commission earned \n(net of VAT)"}, "fromTime": "From time", "toTime": "To time", "transactions": "Transactions", "attachment": "Attachment", "previousPeriod": "Previous period", "samePeriodLastYear": "Same period\nlast year", "currentValue": "Current value", "exportSheetFile": {"translations": "Translations", "importFile": "Import file", "exportFile": "Export file", "selectLanguageToImportExport": "Select languages", "exportFileNewKeys": "Export new keys"}, "archiveProperty": {"label": "Archive", "confirmTitle": "Archive property confirmation", "confirmMessage": "Are you sure you want to archive this property? This action will update the property status and trigger to the website hook.", "confirmSuccess": "Property archived successfully", "confirmSuccessMessage": "The property has been archived successfully. The website hook has been triggered.", "confirmError": "Failed to archive property"}, "roaiib": "ROAIIB", "liabilityInsurance": "Liability Insurance", "optional": "Optional", "daily": "Daily", "start": "Start", "end": "End", "advertisement": {"contact": "Contact", "copied": "Preview link is copied to your clipboard", "sidebar": {"active": "Active ads", "drafts": "Drafts", "completed": "Completed ads"}, "details": {"focus": "Ad Focus", "type": "Ad type", "property": "Property", "agent": "Agent", "event": "Event", "managedBy": "Managed by", "details": "Ad details", "date": "Date", "targetArea": "Target area", "budget": "Budget", "perday": "per day", "language": "Language", "metrics": "Metrics", "impressions": "Impressions", "cpm": "Cost per mille (CPM)", "linkClicks": "Link clicks", "ctr": "Click-through rate (CTR)", "cpc": "Cost per link click (CPC)", "placeholderAlert": "Actual ad preview may vary depending on the platform, device, and display settings.", "helpText": "Here you can see a summary of the advertising your agent has done for your property. You can follow the campaign's performance on this page. Please note that the data is updated with a slight delay.", "metricsDescription": "Here you can view advertising campaign performance metrics"}, "preview": {"alert": {"title": "Safe to share", "text": "This page contains information that is safe to share with your client. You can configure which sections to display on the public link you can share."}, "overview": "Ad overview", "focus": "FOCUS", "property": "Property", "adType": "Ad type", "event": "Event", "details": "DETAILS", "display": "Display", "date": "Date", "start": "Start", "end": "End", "targetArea": "Target area", "metrics": "METRICS", "impressions": "Impressions", "linkClicks": "Link clicks", "ctr": "Click-through rate (CTR)", "preview": "Preview"}}, "language": "Language", "person": "Person", "error": "Error", "history": "History", "by": "By", "loadMore": "Load more", "searchByNameEmailOrNumber": "Search by name, email or number", "noSearchResults": "No search results...", "clientType": "Type of client", "noActivityLogged": "No activity logged", "downloadAllMedia": "Download all media", "companyInfo": "Company info", "otherAgencyCommissionAmount": "Other agency commission amount", "Copy to clipboard": "Copy to clipboard", "Copy": "Copy", "estateInfo": "Estate info", "NIE / ID": "NIE / ID", "subtotal": "Subtotal", "IVA / VAT": "IVA / VAT", "total": "Total", "nieAttachments": "NIE attachments", "formik": {"errors": {"fieldRequired": "Field is required", "mustBeLaterThanField2": "Must be later than field2", "maxCharacters": "Max characters", "AtLeastOneFieldMustBeSelected": "At least one field must be selected", "title": "Title", "eventNotCreated": "Event not created", "eventNotUpdated": "Event not updated"}}, "Map": "Map", "formErrors": "Form errors", "id": "ID", "copySignatureLink": "Copy signature link", "Price": "Price", "moreUsers": "More users", "Select areas": "Select areas", "isExclusive": "Is exclusive", "lastSigningDate": "Last signing date", "comment": "Comment", "realtorUserIds": "Realtor user IDs", "contactIds": "Contact IDs", "Send Email": "Send Email", "sendAllContactsEmail": "Send all contacts email", "Assign": "Assign", "validation": {"typeRequired": "Type is required", "sourceRequired": "Source is required", "addressRequired": "Address is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "estateNameRequired": "Estate name is required", "companyNameRequired": "Company name is required", "businessIdRequired": "Business ID is required"}, "contactPersoninfo": "Contact person info", "missingName": "Missing name", "user": "User", "userSelector": {"main": "Main", "searchPlaceholder": "Search by name, email or phone number...", "realtor": "Realtor"}, "previous": "Previous", "expense": {"setupFailed": "Setup Failed", "setupFailedDesc": "Setup failed", "setupSuccess": "Setup Successful", "setupSuccessDesc": "Your payment method has been added successfully.", "setupPaymentTitle": "Add Payment Method", "transactionCreated": "Transaction Created", "transactionCreatedDesc": "Your expense transaction was recorded successfully.", "transactionFailedDesc": "An error occurred.", "transactionFailed": "Transaction Failed"}, "search": "Search", "reason": "Reason", "amount": "Amount", "connection": "Connection", "fiDetailsOfSale": {"title": "Details of sale", "updated": "Details of sale updated", "previewDetailsOfSale": "Check and lock the details of sale", "form": {"fields": {"propertyId": "Property ID", "ownershipType": "Ownership type", "transactionMethod": "Transaction method", "propertyPublishedAt": "Property published at", "estimatedTransactionDate": "Estimated transaction date", "saleDurationDays": "Sale duration days", "offerCount": "Offer count", "highestRejectedOffer": "Highest rejected offer", "salePrice": "Sale price", "debtFreePrice": "Debt free price", "mortgageBank": "Mortgage bank", "notes": "Notes", "commissionAmountTotal": "Commission amount total", "commissionPercent": "Commission %", "commissionVatPercent": "Commission VAT percent", "commissionVatIncluded": "Commission VAT included", "commissionAmountWithoutVat": "Commission amount without VAT", "commissionVatAmount": "Commission VAT amount", "commissionAmountWithVat": "Commission amount with VAT", "ownershipSharePercent": "Ownership share percent", "recipient": "Realtor", "removeRecipient": "Remove", "role": "Role", "sum": "Sum", "contact": "Contact", "ownershipSharePercentMax": "Ownership share percent must be less than or equal to 100", "ownershipSharePercentRequired": "Ownership share percent is required", "sumOfPercentages": "Sum of percentages must be 100", "propertyPublishedAtGreaterThan": "Estimated transaction date must be greater than property published at", "commissionPercentMax": "Commission percent must be less than or equal to 100", "commissionPercentRequired": "Commission percent is required", "estimatedTransactionDateRequired": "Estimated transaction date is required", "ownershipTypeRequired": "Ownership type is required", "transactionMethodRequired": "Transaction method is required", "debt": "Debt", "saleFinalDate": "Sale final date", "strandCommissionPercent": "Strand commission (% of total commission and expenses)", "strandCommissionAmount": "Strand commission", "leadBasis": "Lead basis calculated from", "leadCommission": "Lead commission", "totalCommissionAndExpenses": "Total commission and expenses", "endDateGreaterThanStartDate": "End date must be greater than start date"}, "enums": {"ownershipType": {"percentage": "Percentage", "fraction": "Fraction"}, "transactionMethod": {"traditional": "Traditional", "dias": "DIAS"}, "recipientRole": {"selling": "Selling", "brokerage_firm": "Brokerage Firm"}, "recipientType": {"contact": "Contact", "user": "User"}, "leadBasis": {"total_commission": "Total commission", "realtor_commission": "Realtor commission"}}, "headers": {"saleParticipants": "Sale participants", "reward": "<PERSON><PERSON>", "sellers": "Sellers", "buyers": "Buyers", "saleDetails": "Sale details", "rewardShare": "Realtor share of total commission", "strandCommissionHeader": "Strand commission", "leads": "Lead commission", "rewardShareDescription": "Realtor share of total commission:", "rewardDescription": "According to sales agreement commission is based on", "rewardBasedOnDebtFreePurchasePrice": "debt free purchase price", "rewardBasedOnPurchasePrice": "purchase price"}, "sellers": "Sellers", "validation": {"errorToast": "Error", "createErrorToast": "Validation error while creating details of sale", "editErrorToast": "Validation error while editing details of sale"}}, "noDetailsOfSale": "No details of sale", "noDetailsOfSaleDescription": "No details of sale found", "create": "Create new details of sale", "addSeller": "Add seller", "addBuyer": "Add buyer", "addRecipient": "Add realtor", "created": "Details of sale created", "preview": "Preview details of sale", "errors": {"totalPercentageExceeded": "Sum of percentages must be 100", "userIdRequired": "User is required", "roleRequired": "Role is required"}, "previewDetailsOfSaleDescription": "Fill in required fields so that you can proceed to next step and lock the details of sale", "steps": {"fill": "Fill", "check": "Check"}, "previewHeading": "Details of sale", "previewDescription": "Check and lock the details of sale", "checkAndLog": "Check and log the details of sale", "checkAndLock": "Check and lock the details of sale", "checkAndLockButton": "Lock details of sale", "unlockButton": "Unlock details of sale", "commission": "Commission", "commissionDistribution": "Commission distribution", "addExpense": "Add expense", "expenses": "Expenses", "vat": "VAT", "net": "Net", "tax": "Tax", "total": "Total", "otherExpensesTotal": "Other expenses total", "addLead": "Add lead commission", "summary": {"total": "Total commission and expenses (including VAT)", "net": "Net total", "tax": "Tax total", "otherExpenses": "+ other expenses {{expensesTotal}} € (including VAT)", "hideExpenses": "Hide other expenses", "showExpenses": "Show other expenses", "expenseName": "Name", "expenseAmount": "Amount", "expenseVat": "VAT", "commissionSummary": "Commission share"}, "failedToLoadPrefillData": "To create a details of sale we first need a completed sales agreement and purchase offer"}, "delete": "Delete", "days": "days", "pieces": "pcs", "commissionSummary": {"title": "Commission summary", "commission": "Commission {{percent}} % (incl. VAT {{vatPercent}} %)", "commissionWithoutVat": "Commission {{percent}} %", "role": "Role", "roundingCorrection": "Rounding correction", "includingVat": "incl. VAT {{vatPercentage}} %", "totalIncludingVat": "Sum (incl. VAT {{vatPercent}}%)", "net": "Net", "tax": "Tax", "strandCommission": "Strand share {{strandCommission}}%", "leadCommission": "Lead ({{name}}) share {{leadCommission}}%", "realtorCommission": "Realtor share {{realtorCommissionPercent}}%"}, "documentLibrary": {"documentTypes": {"FI_ASBESTOS_SURVEY": "Asbestos survey", "FI_SHARE_CERTIFICATE": "Share certificate (for housing cooperative)", "FI_ENERGY_CERTIFICATE": "Energy certificate", "FI_COMPREHENSIVE_BROCHURE": "Comprehensive brochure", "FI_BRIEF_BROCHURE": "Brief brochure", "FI_WINDOW_CARD": "Window card", "FI_PROPERTY_MANAGERS_CERTIFICATE": "Property manager's certificate", "FI_PLANNING_DOCUMENTS": "Planning documents", "FI_PURCHASE_AGREEMENT": "Purchase agreement", "FI_PROPERTY_REGISTER_EXTRACT": "Property register extract", "FI_PROPERTY_REGISTER_MAP": "Property register map extract", "FI_PROPERTY_TAX_STATEMENT": "Property tax statement", "FI_MOISTURE_MEASUREMENT": "Moisture measurement", "FI_MAINTENANCE_PLAN": "Maintenance plan", "FI_MAINTENANCE_NEED_ASSESSMENT": "Maintenance need assessment", "FI_CONDITION_INSPECTION_REPORT": "Condition inspection report", "FI_USAGE_RIGHTS_EXTRACT": "Usage rights unit extract", "FI_CERTIFICATE_OF_TITLE": "Certificate of title", "FI_OTHER": "Other document", "FI_OWNER_APARTMENT_PRINTOUT": "Owner apartment printout", "FI_SHARE_REGISTER_PRINT": "Share register print", "FI_LONG_TERM_MAINTENANCE_PLAN": "Long-term maintenance plan", "FI_SPOUSES_CONSENT": "Spouse's consent", "FI_FLOOR_PLAN": "Floor plan", "FI_BUILDING_PERMIT": "Building permit", "FI_BUILDING_DRAWINGS": "Building drawings", "FI_ENCUMBRANCE_CERTIFICATE": "Encumbrance certificate", "FI_LISTING_FORM": "Listing form", "FI_FINANCIAL_STATEMENT": "Financial statement", "FI_BROKERAGE_AGREEMENT": "Brokerage agreement", "FI_LEASE_RIGHTS_CERTIFICATE": "Lease rights certificate", "FI_LEASE_AGREEMENT": "Lease agreement", "FI_BROKERAGE_OFFER": "Brokerage offer", "FI_ARTICLES_OF_ASSOCIATION": "Articles of association", "FI_GENERAL_MEETING_MINUTES": "General meeting minutes", "FI_HOUSING_COMPANY_CONDITION_REPORTS": "Housing company condition reports"}, "addANoteOrDescription": "Add a note or description...", "chooseOwnDevice": "choose from your device.", "createdAt": "Created at", "createdBy": "Created by", "documentDescription": "Document description", "documentType": "Document type", "dragndropMediaHere": "Drag n Drop documents here", "editModalTitle": "Edit document", "fillAllRequiredFields": "Fill all required fields", "loadingItems": "Loading...", "name": "Name", "openUploadModal": "Upload documents", "or": "Or", "originalFile": "Original file", "remove": "Remove", "searchPlaceholder": "Search...", "supportedFiles": "Supported files: {{fileTypes}}", "tabTitle": "Documents", "upload_one": "Upload ({{count}})", "upload_other": "Upload ({{count}})", "upload_plural": "not in use, here just to prevent i18n:scan from adding it again", "upload_zero": "Upload", "upload": "not in use, here just to prevent i18n:scan from adding it again", "uploadModalTitle": "Upload documents", "deleteSuccessTitle": "Document deleted successfully", "updateSuccessTitle": "Document updated successfully", "confirmDeleteMessage": "Are you sure you want to delete the document: {{documentName}}?", "itemsSelected_one": "{{count}} document selected", "itemsSelected_other": "{{count}} documents selected", "sendForSigning": "Send for signing", "sendForSigningSuccessTitle": "Sent for signing", "uploadSuccessTitle_one": "Document uploaded successfully", "uploadSuccessTitle_other": "Documents uploaded successfully", "linkedTo": "Linked to", "itemsSelected": "place holder for i18n:scan", "itemsSelected_plural": "place holder for i18n:scan", "uploadSuccessTitle": "place holder for i18n:scan", "uploadSuccessTitle_plural": "place holder for i18n:scan", "itemNotFound": "Document not found", "invalidFileType": "Invalid file type", "pdfPreview": "PDF preview", "documentActions": "Document actions", "sharedDocuments": {"title": "Shared documents", "preview": "Preview"}, "share": "Share", "shareSelectedDocuments": "Share selected documents", "shareModalTitle": "Share documents", "shareModalHelpText": "The recipients will receive a download link to the selected documents. The link will expire within 24 hours.", "shareModalMessage": "Message", "shareModalMessagePlaceholder": "Add a personalised message for the receipients... (recommended)", "shareModalReceivers": "Add person", "shareSuccessTitle": "Documents shared successfully"}, "trade": "Trading", "purchaseOffer": "Purchase offer", "actions": "Actions", "loggedActionsTypes": {"CREATED": "Created", "UPDATED": "Updated", "DELETED": "Deleted", "PRICE_CHANGED": "Price changed", "STATUS_CHANGED": "Status changed", "SYNCED": "Synced", "VIEWED": "Viewed", "ASSIGNED": "Assigned", "ATTACH": "Attach", "DISCARDED": "Discarded", "MEMBER_ADDED": "Member added", "SENT": "<PERSON><PERSON>", "DEACTIVATED": "Deactivated", "UPDATED_DOCUMENT": "Document updated"}, "dataBefore": "Data before", "dataAfter": "Data after", "fromDate": "From date", "toDate": "To date", "companyId": "Company ID", "contactPerson": "Contact person", "photoshoots": "Photoshoots", "send": "Send", "oneContactRequired": "One contact is required", "oneRealtorRequired": "One realtor is required", "tagAlreadyExists": "One or more tags already exist", "somethingWentWrong": "Something went wrong", "vatPercent": "Tax %", "listedSince": "Listed since", "openInFullPage": "Open in full page", "gridView": "Grid view", "listView": "List view", "openInFullView": "Open in full view", "openInPeekView": "Open in peek view", "includeVat": "(incl. VAT)"}