{"enums": {"contactType": {"Person": "Henkilö", "Organization": "<PERSON><PERSON>s", "Estate": "Kuolinpesä", "notSpecified": "<PERSON><PERSON> m<PERSON>y"}}, "organizationSettings": {"organizationSettings": "Organisaation asetukset", "adTemplates": {"errors": {"imageRequired": "<PERSON><PERSON> vaa<PERSON>", "imageUrl": "Virheellinen kuvan URL-osoite", "smartyIdRequired": "Smarty ID vaaditaan", "create": "<PERSON><PERSON> luominen ep<PERSON>", "update": "<PERSON><PERSON> p<PERSON>ivittäminen epäonnistui", "delete": "<PERSON><PERSON> poistaminen ep<PERSON><PERSON>ui"}, "create": "<PERSON>o malli", "update": "Päivitä malli", "delete": "Poista malli", "imageUrl": "<PERSON>van <PERSON>-osoite", "adTemplates": "Mainosmallit", "addTemplate": "Lisää malli"}, "title": "Asetukset"}, "expenses": {"connected_card": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "remove_card": "<PERSON><PERSON> k<PERSON>", "tab": "<PERSON><PERSON>", "expensed": "<PERSON><PERSON>", "deposited": "Tall<PERSON>", "on_hold": "Odottaa", "transactions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet_balance": "Lompakon saldo", "connect_card": {"title": "Yhdistä korttisi hallitaksesi kuluja", "button_text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "stripe": {"setupInstructions": "Täytä l<PERSON>"}, "type": {"expense": "<PERSON><PERSON>", "deposit": "Tall<PERSON>", "Payout": "<PERSON><PERSON><PERSON>", "Gift": "<PERSON><PERSON><PERSON>", "payout": "<PERSON><PERSON><PERSON>", "gift": "<PERSON><PERSON><PERSON>"}, "status": {"cancelled": "Peruttu", "failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Käsitellään", "requires_action": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>pit<PERSON>", "on_hold": "Odottaa", "succeeded": "<PERSON><PERSON><PERSON>"}, "listPlaceholder": {"button": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "hasCard": {"title": "<PERSON><PERSON> kuluja", "description": "<PERSON>ulla ei ole yhtään kulua. Kun yhdistät kortin, kulut näkyvät täällä."}, "noCard": {"title": "Yhdistä korttisi hallitaksesi kuluja", "description": "Lisää korttitietosi hallitaksesi kuluja", "button": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "default": {"title": "Ei kuluja vielä", "description": "unknown"}}, "messages": {"cardRemoved": {"title": "<PERSON><PERSON><PERSON> pois<PERSON>", "description": "", "error": "<PERSON><PERSON><PERSON> poistam<PERSON> tapahtui virhe"}}, "modal": {"removeCard": {"title": "<PERSON><PERSON> k<PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa korttis<PERSON>? Voit edelleen tarkastella aiempia kuluja, mutta sinun on yhdistettävä kortti uudelle<PERSON>, jos haluat jatkaa uusien kulujen hallintaa."}}, "expenses": "<PERSON><PERSON>"}, "adsManager": {"ad": "<PERSON><PERSON>", "createTitle": "<PERSON><PERSON>", "updateTitle": "Päivitä <PERSON>os", "end_ad": "Päätä <PERSON>", "ad_type": "Mainostyyppi", "types": {"listing_property": "Myytävä kohde", "property_sold": "<PERSON><PERSON><PERSON> kohde", "agent": "Välittäjä", "event": "Tapahtuma", "custom": "Muka<PERSON>ttu"}, "languages": {"en": "Eng<PERSON><PERSON>", "fi": "<PERSON><PERSON>", "es": "Espanja"}, "errors": {"eventRequired": "Valitse sekä kohde että tapahtuma tälle mainostyypille", "duplicate": {"title": "<PERSON><PERSON><PERSON> kop<PERSON>innissa tapahtui virhe"}, "cancel": {"title": "<PERSON><PERSON><PERSON> perumisessa tapahtui virhe"}, "publish": {"title": "<PERSON><PERSON><PERSON> julkaisemisessa tapahtui virhe"}, "budget": {"is_nan": "Budjetin on oltava numero", "range": "Budjetin on oltava välillä 4–9999 €"}, "start_date_end_date": "Aloituspäivän tulee olla ennen lopetuspäivää.", "required": "Tämä kenttä on pakollinen.", "propertyRequired": "Please select a property for this ad type", "budgetTotal": {"is_nan": "unknown", "range": "unknown"}, "end": {"title": "Mainoksen päättämisessä tapahtui virhe"}}, "table": {"client_preview": "<PERSON><PERSON><PERSON> es<PERSON>", "copy_url": "Kopioi URL", "messages": {"duplicate": {"title": "<PERSON><PERSON> k<PERSON>", "description": "{{entity}} kopioitu"}, "cancel": {"title": "<PERSON><PERSON><PERSON> poistettu", "description": "{{entity}} poistettu"}, "publish": {"title": "<PERSON><PERSON> valmis", "description": "{{entity}} valmis"}, "url_copied": {"title": "", "description": "URL kopioitu"}}, "headers": {"status": {"title": "Tila"}, "date": {"title": "Päivämäärä"}, "impressions": {"title": "Näyttökerrat", "tooltip": "<PERSON><PERSON><PERSON> monta kertaa mainos näytetti<PERSON>"}, "link_clicks": {"title": "<PERSON><PERSON>", "tooltip": "Kuinka monta kertaa mainoslinkkiä klikattiin"}, "cpm": {"title": "<PERSON><PERSON><PERSON>ö<PERSON><PERSON> (CPM)", "tooltip": "Keskimääräinen hinta 1000 näyttökertaa kohti"}, "ctr": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CTR)", "tooltip": "<PERSON><PERSON><PERSON> monta prosent<PERSON> n<PERSON>ker<PERSON><PERSON> johti k<PERSON>"}, "cpc": {"title": "Klikkauskohtainen hinta (CPC)", "tooltip": "Keskimääräinen hinta yhtä mainoslinkin klikkausta kohti"}, "ad_preview": {"title": "<PERSON><PERSON><PERSON> es<PERSON>"}}, "errors": {"duplicate": {"title": "unknown"}, "end": {"title": "Mainoksen päättämisessä tapahtui virhe"}}}, "status": {"draft": "Luonnos", "in_review": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktiivinen", "completed": "Val<PERSON>"}, "advertisements": "<PERSON><PERSON><PERSON>", "selectPictures": "Valitse kuvat", "listPlaceholder": {"title": "<PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON>ok<PERSON>i", "description": "<PERSON><PERSON>et ne täällä kaikkine tärkeine tietoineen.", "createAd": "<PERSON><PERSON>"}, "modal": {"imagesSelected": "Valittu {{number}} kuvaa", "review": {"adFocus": "MAINOKSEN KOHDE", "adType": "Mainostyyppi", "property": "<PERSON><PERSON><PERSON>", "event": "Tapahtuma", "dateRange": "<PERSON><PERSON><PERSON><PERSON>", "adDetails": "MAINOKSEN TIEDOT", "date": "Päivämäärä", "budget": "<PERSON><PERSON><PERSON>", "perDay": "per päivä", "targetArea": "<PERSON><PERSON><PERSON><PERSON>", "content": "SISÄLTÖ", "language": "<PERSON><PERSON>"}, "fillContent": {"warning": "<PERSON><PERSON><PERSON> esikatselu voi vaih<PERSON> al<PERSON>, la<PERSON>en ja näyttöasetus<PERSON> mukaan.", "title": "Mainossisältö", "headline": "Headline", "description": "Description", "primaryText": "Primary text"}, "runData": {"targetArea": "<PERSON><PERSON><PERSON><PERSON>", "municipality": "Kaupunki", "endDate": "Lopetuspäivä", "startDate": "Aloituspäivä", "targetAreaTitle": "<PERSON><PERSON><PERSON><PERSON>", "country": "Maa", "metaReviewNotice": "Meta voi tarkistaa ja julkaista mainoksen 24 tunnin sisällä.", "dailyCost": "Päivittäinen hinta", "language": "<PERSON><PERSON>", "title": "Ad run", "budgetTitle": "Budget", "totalCampaignBudget": "Total campaign budget", "locationRadiusDisclaimer": "The location adds a +50km radius always, as specified by Meta's targeting settings.", "targetRadiusKm": "Target radius (in km)"}, "adIsReady": {"title": "Mainoksesi on valmis!", "description": "Meta voi tarkistaa ja näyttää mainoksen käyttäjille jopa kahden päivän kuluessa."}, "selectProperty": "<PERSON><PERSON><PERSON> kohde", "selectEvent": "<PERSON><PERSON><PERSON>", "adType": "Mainostyyppi", "type": {"listingProperty": {"label": "Myytävä kohde", "subText": "<PERSON><PERSON><PERSON>, j<PERSON> on mark<PERSON><PERSON><PERSON>, ho<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>."}, "agent": {"label": "Välittäjä", "subText": "Korosta itseäsi välittäjänä houkutellaksesi uusia asiakkaita."}, "event": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subText": "<PERSON><PERSON><PERSON>, kuten esittely<PERSON> tai yhteisötil<PERSON>uuk<PERSON>."}, "propertySold": {"label": "<PERSON><PERSON><PERSON> kohde", "subText": "<PERSON><PERSON><PERSON> onnist<PERSON>utta my<PERSON>tiä ja esittele asiantuntemustasi tuleville asiakka<PERSON>."}, "custom": {"label": "Muka<PERSON>ttu", "subText": "Luo mainos alusta alkaen hallitaksesi sisältöä ja tarkoitusta."}}, "errors": {"noEventsAvailable": "<PERSON><PERSON><PERSON> koh<PERSON>, jolla on tapahtumia"}}, "messages": {"duplicate": {"title": "<PERSON><PERSON> k<PERSON>", "description": "{{entity}} kopioitu."}, "cancel": {"title": "Mainos per<PERSON>u", "description": "{{entity}} peruutettu."}, "end": {"description": "<PERSON><PERSON>"}, "publish": {"title": "<PERSON><PERSON> j<PERSON>", "description": "{{entity}} julkaistu."}}, "copyLink": "<PERSON><PERSON><PERSON>"}, "signing": {"sendForSigning": "Lähetä allekirjoitettavaksi", "lastSigningDate": "Viimeinen allekirjoituspäivä", "message": "<PERSON><PERSON><PERSON><PERSON>", "signers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otherSigners": "<PERSON><PERSON>", "addAssignees": "Lisää allekirjoittajat", "send": "Lähetä", "siningEvents": {"signer_signed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signer_declined": "<PERSON><PERSON><PERSON><PERSON> allek<PERSON><PERSON><PERSON><PERSON><PERSON>", "signing_completed": "Allekir<PERSON><PERSON><PERSON> valmis"}, "comment": "<PERSON><PERSON><PERSON><PERSON>", "signingEvents": {"signing_completed": "Välityspalkkioprose<PERSON><PERSON> on pakollinen"}, "selectedDocuments": "Valitut <PERSON>", "addSigner": "Lisää allekirjoittaja", "signersAdded": "Allekirjoittaja l<PERSON>ät<PERSON>", "failedAddingSigners": "Allekirjoittajan lisääminen epäonnistui", "signer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reminderSent": "<PERSON><PERSON><PERSON><PERSON>", "failedSendingReminder": "Muistutuksen lähettäminen epäonnistui", "signerRemoved": "Allekirjoittaja pois<PERSON>ttu", "failedRemovingSigner": "Allekirjoittajan poistaminen epäonnistui", "signerAdded": "Allekirjoittaja l<PERSON>ät<PERSON>", "signingDeleted": "Allekirjoittaminen per<PERSON>utettu", "failedDeletingSigning": "Allekirjoittamisen per<PERSON>uttaminen epäonnistui", "deleteSigning": "<PERSON><PERSON>", "deleteSigningDescription": "<PERSON><PERSON><PERSON><PERSON> varmasti peruuttaa tämän allekirjoittami<PERSON>?"}, "fiSalesAgreement": {"tabTitle": "Toimeksianto", "editStep": "Täytä", "validateStep": "Tarkista", "signatureStep": "Allekirjoitukset", "completedStep": "<PERSON><PERSON> sopimus", "salesAgreement": "Toimeksiantoso<PERSON><PERSON>", "salesAgreements": "Toimeksiantosopimukset", "noSalesAgreements": "Kohteella ei ole vielä toimeksiantosopimusta.", "noSalesAgreementsDescription": "<PERSON><PERSON><PERSON><PERSON> tulee olla toimeksiantosopimus ja selostusliite allekirjoitettuna, jotta voit julkai<PERSON> koh<PERSON>.", "validateHeading": "Vaihe 2: Tark<PERSON>", "validateDescription": "Tarkista huolellisesti jokainen kohta. <PERSON><PERSON> o<PERSON> val<PERSON>, voit siirtyä allekirjoitukseen.", "saveAsDraft": "<PERSON><PERSON><PERSON> l<PERSON>", "submit": "Lähetä", "addAssignees": "Lisää allekirjoittajat", "showAssignees": "Näytä allekirjoittajat", "nextStep": "<PERSON><PERSON><PERSON> askel", "created": "<PERSON><PERSON>ksiantoso<PERSON><PERSON> luotu", "updated": "Toimeksiantosopi<PERSON>", "copyAsNew": "Kopioi uudeksi sopimukseksi", "fetchSigningStatus": "<PERSON>e allekirjoitusten tila", "sendReminder": "Lähetä muistutus", "addSigneesHeading": "Vaihe 3: <PERSON><PERSON><PERSON><PERSON> allekirjoittajat", "addSigneesDescription": "<PERSON>s<PERSON><PERSON> allek<PERSON>, jotka allekir<PERSON>itt<PERSON><PERSON> toimeksiantosopimuks<PERSON>.", "signersHeading": "Vaihe 3: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digitalSigners": "Sähköisesti allekirjoittavat", "signed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signingDeadline": "Allekirjoittaminen umpeutuu", "pendingSignature": "<PERSON>dottaa allekirjo<PERSON>usta", "missingSignatures": "Allekirjoituk<PERSON> puuttuu:", "failedSendingReminder": "Muistutuksen lähettäminen epäonnistui", "form": {"basicInformationHeading": "<PERSON><PERSON><PERSON><PERSON>", "realtor": "Välittäjä", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentHeading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceHeading": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ja my<PERSON>da<PERSON>", "priceHeaderRow": "<PERSON><PERSON>", "availabilityHeading": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vuo<PERSON><PERSON><PERSON> ja k<PERSON><PERSON>", "availabilityHeaderRow": "V<PERSON>ut<PERSON><PERSON>", "tenantHeaderRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rightsOfUseHeaderRow": "Käyttöoikeus", "validityHeading": "Voimassaoloaika", "commissionHeading": "Välityspalkkio ja muut kustannukset", "commissionHeaderRow": "Välityspalkkio", "otherCostsHeaderRow": "<PERSON><PERSON> to<PERSON>n maks<PERSON>ksi tulevat kustannukset", "termsHeading": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja ehdot", "announcementsHeaderRow": "Osapuolten ilmoitukset", "consentHeaderRow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consentersLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otherHeaderRow": "<PERSON><PERSON>", "otherTermsHeaderRow": "<PERSON><PERSON>", "digitalSellingHeading": "Digitaalinen Kaup<PERSON> ja <PERSON>", "agreedMarketingMethodsLabel": "<PERSON><PERSON><PERSON> mark<PERSON> ja mark<PERSON><PERSON><PERSON><PERSON><PERSON> on sovittu seuraavaa", "separateMarketingAppendixLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> on erill<PERSON> liite", "unencumberedPriceRequestLabel": "<PERSON><PERSON><PERSON>", "unencumberedPriceRequestEstimateLabel": "<PERSON><PERSON><PERSON> velatt<PERSON> hinnasta", "sharesIncludeLoanLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koh<PERSON>u la<PERSON>", "loanAmountLabel": "Lainaosuuden määrä", "loanDetailsLabel": "Lisätietoja la<PERSON>ta", "priceIncludingLoanLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentTermsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentTermOtherLabel": "<PERSON><PERSON> ma<PERSON>", "averageSellingTimeEstimateLabel": "<PERSON><PERSON><PERSON>räisestä myyntiajasta", "factorsAffectingSalesLabel": "<PERSON><PERSON><PERSON><PERSON> vaiku<PERSON>vat tekij<PERSON>", "availabilityLabel": "V<PERSON>ut<PERSON><PERSON>", "dateWhenAvailableLabel": "Vapautumispäivämäärä", "availabilityDetailsLabel": "Lisätietoja vapautumisesta", "tenantNameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "tenantContactDetailsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leaseAgreementLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leaseAgreementTermLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leaseStartDateLabel": "Vuokrasopimuksen alkamispäivä", "leaseEndDateLabel": "Vuokrasopimuksen päättymispäivä", "leaseTerminatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on irtisanottu", "tenantHasPaidRentOnTimeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> on maks<PERSON>t vuokrat a<PERSON>an", "leaseAmountLabel": "Vuokran määrä", "leaseDepositLabel": "Vuokravakuuden määrä", "tenantPayingRentDetailsLabel": "Lisätietoja vuokran maksusta", "leaseAgreementDetailsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ot", "restrictiveRightOfUserLabel": "<PERSON><PERSON><PERSON><PERSON>", "restrictiveRightOfUserDetailsLabel": "Lisätietoja käyttöä rajoittavasta o<PERSON>udesta", "writtenConsentToTransferLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> suostumus luo<PERSON>", "belongsToBusinessActivitiesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> kohde kuuluu to<PERSON><PERSON><PERSON><PERSON> elinkein<PERSON>", "assignmentValidityLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> voi<PERSON><PERSON>", "assignmentValidityRenewalPeriodLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDateLabel": "To<PERSON>ks<PERSON><PERSON>ä<PERSON>", "endDateLabel": "Toimeks<PERSON><PERSON> päättymispäivä", "commissionBasisCodeLabel": "Välityspalkkion peruste", "commissionTypeLabel": "Välityspalkkion tyyppi", "vatLabel": "Sisältää arvonlisäveron", "commissionPercentageLabel": "Välityspalkkioprosentti", "commissionFixedLabel": "Kiinteä palkkio", "commissionDetailsLabel": "Lisätietoja välityspalkkiosta", "marketingExpensesMaxLabel": "Markkinointikulut enintää<PERSON> (sis. alv)", "documentAcquisitionExpensesMaxLabel": "Dokumentien hallintakulut enintään (sis. alv)", "otherExpensesDetailsLabel": "<PERSON><PERSON> k<PERSON>", "expenseIfNoCommissionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mi<PERSON><PERSON><PERSON> kohde päättyy myymättömänä (sis.alv).", "acquisitionLabel": "Saanto toime<PERSON>aja", "sellerIsMarriedOrInRegisteredRelationshipLabel": "Toimeksiantaja on avioliitossa tai rekisteröidyssä parisuhteessa?", "sellerHasSpousesConsentLabel": "<PERSON><PERSON> puolison suostumus my<PERSON>?", "sellerHasBeenMarriedOrInRegisteredRelationshipLabel": "<PERSON><PERSON><PERSON><PERSON> on aiemmin ollut avioliitossa tai rekisteröidyssä parisuhteessa?", "divorceLegallyBindingLabel": "Onko ero lainvoimainen?", "legalPartitioningIsCompleteLabel": "<PERSON><PERSON> ositus tai omaisuuden erittely lainvoimaisesti suoritettu?", "acquisitionDateLabel": "Saantopäivä", "acquisitionCostLabel": "Lisätietoja saannosta", "clientHasUsedResidenceAsResidenceLabel": "<PERSON><PERSON>ksiantaja on omistusaikanaan käyttänyt vähintään puolta asunto-osakkeesta yhtäjaksoisesti vakituisena asuntona<PERSON>?", "shareRegisterFormatLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muoto", "shareRegisterStorageLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "taxConsequencesLabel": "Lu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residencyStartDateLabel": "Alkaen", "residencyEndDateLabel": "Pä<PERSON><PERSON><PERSON>", "unpaidMaintenanceChargeLabel": "Maksamattomia yhtiövastikkeita", "unpaidMaintenanceChargeAmountLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "digitalTradingAllowedLabel": "Toimeksiantaja hyväksyy, että kauppa voidaan tehdä sähköisesti", "isDomesticSaleLabel": "Kyseessä on kotimyynti", "startAssignmentImmediatelyLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> su<PERSON>n aloit<PERSON>an heti", "startMarketingAfterCancelPeriodLabel": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> per<PERSON><PERSON>", "customerAskedToReadPrivacyPolicyLabel": "Pyydetty asiakasta tutustumaan tietosuojaselosteeseen ennen toimeks<PERSON>non allekirjoitusta?", "previousExternalSalesAgreementLabel": "Onko ollut välitettävänä toisella välittäjällä viimeisen 6kk:n aikana", "previousExternalSalesAgreementDetailsLabel": "Lisätietoja aiemmasta toimeksiannosta", "additionalDetailsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastSigningDate": "Allekirjoitus annett<PERSON> viimeistään", "signers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instructions": {"validity": "Toimeksiannon voimass<PERSON><PERSON> päättyy aina neljän kuukauden kuluttua voimassaolon alkamisesta, ellei sitä kirjallisesti jatketa välityslain edellyttämällä tavalla. Neljän kuukauden enimmäisvoimassaoloaika ei koske sopimuksia, joissa to<PERSON><PERSON>non kohde kuuluu toimeksiantajan elinkeinotoimintaan. Toistaiseksi voimassaoleva sopimus voidaan kirjallisesti irtisanoa viimeistään kymmenen päivää ennen seuraavan sopimuskauden alkamista.", "usedResidence": "Jo<PERSON>i y<PERSON>äjaksoista asumisaikaa jokaisen osaomistajan osalta hänen omistusaikanaan ole vähintään kahta vuotta, my<PERSON><PERSON><PERSON> määrätään luo<PERSON>voittovero, jos voit<PERSON><PERSON> synty<PERSON>.", "tax": "<PERSON><PERSON> ei ole ma<PERSON>a selvittää, tulee toimeksiant<PERSON> ohjata kääntymään verohallinnon puoleen veroseuraamusten selvittämiseksi."}, "enums": {"status": {"draft": "Draft", "validated": "Validated", "pending_signatures": "Pending signatures", "completed": "Completed"}, "paymentTerms": {"cash": "Käteinen", "exchange": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "availability": {"immediately": "<PERSON><PERSON>", "negotiable": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>an", "rented": "Vuokrattu", "date": "Päivämäärä", "other": "<PERSON><PERSON>"}, "leaseAgreement": {"written_agreement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oral_agreement": "<PERSON><PERSON><PERSON>"}, "period": {"indefinite": "<PERSON><PERSON><PERSON><PERSON><PERSON> voimassa", "fixed": "Määräaikainen"}, "commissionBasisCode": {"debt_free_purchase_price": "<PERSON><PERSON><PERSON>", "purchase_price": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "commissionType": {"percentage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fixed": "Kiinteä", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vat": {"vat_included": "Sisältää alv:n", "vat_not_included": "Ei sisällä alv:a"}, "acquisition": {"purchase": "<PERSON><PERSON><PERSON>", "exchange": "<PERSON><PERSON><PERSON><PERSON>", "gift": "<PERSON><PERSON><PERSON>", "inheritage": "<PERSON><PERSON><PERSON>", "testament": "<PERSON><PERSON>", "partitioning": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "shareRegisterFormat": {"digital": "Sähköinen osakekirja", "paper": "<PERSON><PERSON> osakekirja"}, "taxConsequences": {"tax_on_capital_gain_payable": "<PERSON><PERSON> l<PERSON>", "tax_on_capital_gain_not_payable": "Ei mene luovutus<PERSON>oa", "no_chance_to_find_out": "<PERSON>i ma<PERSON><PERSON>a selvi<PERSON>"}}, "validation": {"valueMaxSizeError": "<PERSON>rvo ei voi ylittää lukua 2 ***********.", "errorToast": "Täytä kaikki pakolliset kentät", "propertyId": "<PERSON><PERSON><PERSON> on pakollinen", "contactIds": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "realtorUserIds": "Välittäjä on pakollinen", "unencumberedPriceRequest": "<PERSON><PERSON><PERSON> on pakollinen", "unencumberedPriceRequestEstimate": "<PERSON><PERSON><PERSON> on pakollinen", "shareRegisterFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muoto on pakollinen", "sharesIncludeLoan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "loanAmount": "<PERSON>elan määrä on pakollinen", "priceIncludingLoan": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pak<PERSON>inen", "paymentTerms": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "paymentTermOther": "<PERSON><PERSON> ma<PERSON> on pakollinen", "availability": "Vapautuminen on pakollinen", "dateWhenAvailable": "Vapautumispäivämäärä on pakollinen", "tenantName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi on pak<PERSON><PERSON>", "belongsToBusinessActivities": "Elinkeinotoiminta on pakollinen", "vat": "ALV on pakollinen", "assignmentValidity": "<PERSON><PERSON><PERSON><PERSON><PERSON> voi<PERSON><PERSON><PERSON> on pakollinen", "assignmentValidityRenewalPeriod": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "startDate": "Toimeksiannon alkamispäivä on pakollinen", "endDate": "Toimeksiannon päättymispäivä on pakollinen", "commissionBasisCode": "Välityspalkkion peruste on pakollinen", "commissionType": "Välityspalkkion tyyppi on pakollinen", "commissionFixed": "Kiinteä palkkio on pakollinen", "commissionPercentage": "Välityspalkkioprose<PERSON><PERSON> on pakollinen", "isDomesticSale": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pak<PERSON>inen", "startAssignmentImmediately": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "startMarketingAfterCancelPeriod": "<PERSON><PERSON><PERSON><PERSON> on pak<PERSON>inen", "restrictiveRightOfUserDetails": "Lisätiedot käyttöä rajoittavasta o<PERSON>udes<PERSON> on pakollinen"}, "otherTermsHeading": "<PERSON><PERSON> ehdot ja lis<PERSON>"}, "reminderSent": "<PERSON><PERSON><PERSON><PERSON>", "sentForSigning": "Lähetetty allekirjoitettavaksi", "create": "<PERSON><PERSON> uusi to<PERSON>", "addSigners": "Lisää allekirjoittajia", "selectContactsToAdd": "Valitse kontaktit", "previewContract": "Esikatsele sopimus<PERSON>", "defaultComment": "<PERSON><PERSON>, t<PERSON><PERSON><PERSON> toimeksiantosopimus allekirjoitettavaksi.", "missingFieldsTitle": "Puuttuvia tie<PERSON>", "missingFields": "Ole hyvä ja täytä kaikki pakolliset kentät."}, "salesAgreement": "Toimeksiantosopimukset", "fiPurchaseOffer": {"updated": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "preview": "Esikatsele", "copy": "<PERSON><PERSON><PERSON>", "previewOffer": "Esikatsele ostotarjousta", "create": "<PERSON><PERSON>", "steps": {"fill": "Täyttäminen", "validate": "Tarkista", "sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "form": {"instructions": {"delayFeePerStartedWeek": "Korvaus kultakin alkavalta viivästysviikolta kaupan kohteen vapautumispäivään asti.", "availabilityDelayFee": "<PERSON><PERSON><PERSON><PERSON> hallintaoikeuden luovutus ostajalle viivästyy myyjästä johtuvasta syystä, maksaa myyjä ostajalle."}, "headers": {"parties": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buyers": "<PERSON><PERSON><PERSON><PERSON>", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON><PERSON>", "priceAndAdministrative": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja omistus<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "releaseAndTransfer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> ja vapautuminen", "termsOfTradeAndOther": "<PERSON><PERSON> ehdot ja kaupan ed<PERSON>", "digitalTrading": "Sähk<PERSON><PERSON> ka<PERSON>ank<PERSON>", "standardCompensation": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja käsiraha", "termsOfTrade": "<PERSON><PERSON><PERSON>", "transferTax": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attachments": "<PERSON><PERSON><PERSON> lii<PERSON>", "signingAndValidity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> allek<PERSON>us ja tar<PERSON><PERSON><PERSON> voi<PERSON><PERSON><PERSON>", "validity": "Voimassaoloaika", "informingApproval": "Tarjouksen hyväksymisestä ilmoittaminen", "personalData": "Henkilötietojen k<PERSON>ly", "privacyStatement": "Tietosuojaseloste"}, "fields": {"buyers": "<PERSON><PERSON><PERSON><PERSON>", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "unencumberedPrice": "<PERSON><PERSON><PERSON>", "loanAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceIncludingLoan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loanDate": "Lainaosuuden päiväys", "buildingManagerCertificateDate": "Isännöitsijäntodistuksen päiväys", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transferRightOfUse": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> o<PERSON>", "transferRightOfUseLatest": "Hallintaoikeus siirtyy viimeistään", "availabilityDelayFee": "Vapautumisen viivästymisestä maksettava korvaus", "delayFeePerStartedWeek": "Korvaus kultakin alkaneelta viivästysviikolta", "isRented": "<PERSON><PERSON><PERSON>o myydään vuokrattuna", "buyerReceivesRentStarting": "Ostaja saa vuokratuoton, alkaen", "buyerReceivesRentalDepositLatest": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>, viimeistään", "buyerResponsibleForCostsStarting": "Ostaja vastaa huoneistoon kohdistuvista maksuista al<PERSON>en", "buyerResponsibleForCapitalExpenditureChargeStarting": "Ostaja vastaa pääomavastikkeesta alkaen", "digitalPurchase": "<PERSON><PERSON><PERSON> voidaan tehdä sähköisessä kaupankäyntijärjestelmässä", "digitalPurchaseExpenses": "Sähköisen kaupankäynnin kustannuksista vastaa", "digitalPurchaseExpensesDetails": "<PERSON><PERSON><PERSON> vastaava taho", "standardCompensation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downPayment": "Käsiraha", "downPaymentTerm": "Käsirahan maksaminen", "validUntil": "Tarjouksen voimassaoloaika", "signedLatest": "Kauppakirja allekirjoitetaan viimeistään", "acceptOfferEmail": "Sähköpostiosoitteeseen", "acceptOfferPhone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasReadPrivacyPolicy": "Pydetty asiakasta tutustumaan tietosuojaselosteeseen ennen ostotarjouksen allekirjoitusta"}, "initialAttachments": {"energyCertificate": "Ener<PERSON><PERSON><PERSON><PERSON>", "propertyManagerCertificate": "Isännöitsijäntodistus", "moistureMeasurement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maintenanceReport": "Kunnossapitosel<PERSON>s", "maintenancePlan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "floorPlan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longTermMaintenancePlan": "PTS", "financialStatement": "Tilinpäätös", "articlesOfAssociation": "Yhtiöjärjestys"}, "initialTermsOfSale": {"moistureMeasurement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loan": "<PERSON>nan sa<PERSON>", "ownPropertySold": "Oman as<PERSON>non <PERSON>"}, "enums": {"paymentMethod": {"cash": "Käteiskauppa", "installment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transferRightOfUse": {"purchase": "Kaupanteossa", "later": "<PERSON><PERSON><PERSON><PERSON>"}, "digitalPurchaseExpenses": {"buyer": "Ostaja", "seller": "<PERSON><PERSON><PERSON><PERSON>", "both": "Ostaja ja My<PERSON>jä", "other": "<PERSON><PERSON>"}, "downPaymentTerm": {"mark_as_paid": "<PERSON><PERSON><PERSON><PERSON>", "to_be_paid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "status": {"draft": "Luonnos", "validated": "Validoitu", "pending_offeror_signatures": "Odottaa tarjo<PERSON>n allekirjoitusta", "offeror_signed": "<PERSON><PERSON><PERSON><PERSON>", "pending_buyer_signatures": "<PERSON>do<PERSON>a o<PERSON> allek<PERSON>", "buyer_signed": "<PERSON><PERSON><PERSON>ek<PERSON>", "pending_offeree_signatures": "Odottaa tar<PERSON><PERSON>en saajan allekirjoitusta", "offeree_signed": "Tar<PERSON>ksen saaja allekirjo<PERSON>t", "pending_seller_signatures": "<PERSON><PERSON><PERSON><PERSON> my<PERSON>n allekirjoitusta", "seller_signed": "<PERSON><PERSON><PERSON><PERSON>", "accepted": "Hyväksytty", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>"}}, "validation": {"errorToast": "Täytä kaikki pakolliset kentät", "buyerIds": "Ostaja on pakollinen", "sellerIds": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "propertyId": "<PERSON><PERSON><PERSON> on pakollinen", "unencumberedPrice": "<PERSON><PERSON><PERSON> on pakollinen", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON> maks<PERSON> on pakollinen", "transferRightOfUse": "Hallintooikeuden siiirtäminen on pakollinen", "transferRightOfUseLatest": "Hallintaoikeuden siirtäminen on pakollinen", "buyerReceivesRentStarting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "buyerReceivesRentalDepositLatest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "digitalPurchase": "Sähköinen kaupankäynti on pakollinen", "validUntil": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "signedLatest": "Allekirjoituspvm on pakollinen", "acceptOfferEmail": "Sähköpostiosoite on pakollinen", "acceptOfferPhone": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "counterOffer": {"purchaseOfferId": "Alkuperäinen tarjous on pakollinen", "unencumberedPrice": "<PERSON><PERSON><PERSON> velaton ka<PERSON><PERSON> on pakollinen", "validUntil": "Vastatarjouksen voimassaoloaika on pakollinen"}}}, "addAssignees": "Lisää allekirjoittajia", "sentForSigning": "Lähetetty allekirjoitettavaksi", "sendForBuyerSigning": "Lähetä ostajan allekir<PERSON>ami<PERSON>n", "sendForSellerSigning": "Lähetä my<PERSON>jän allekirjoittamiseen", "sendReminder": "Lähetä muistutus", "signed": "Allekirjoitettu", "failedToCreate": "Ostotarjouksen luominen epäonnistui", "failedToCopy": "Ostotar<PERSON><PERSON><PERSON> kop<PERSON>iminen epäonnistui", "reject": "Hylkää", "accept": "Hyväksy", "statusUpdated": "<PERSON><PERSON><PERSON><PERSON><PERSON> tila p<PERSON>tty", "failedToUpdateStatus": "<PERSON><PERSON> päivittäminen epäonnist<PERSON>", "copied": "Osto<PERSON><PERSON><PERSON> kop<PERSON>u"}, "purchaseOffers": "Ostotarjoukset", "counterOffer": {"additionalDetails": "Vastatarjouksen lisätiedot ja ehdot", "buyers": "<PERSON><PERSON><PERSON>(t)", "counterOffer": "Vastatarjous", "fill": "Vastatarjouksen täyttäminen", "fillSubtitle": "Täytä huolellisesti jokainen kohta. Pää<PERSON> es<PERSON>, kun kaikki pakoll<PERSON>t tiedot on t<PERSON><PERSON><PERSON>.", "originalOffer": "<PERSON><PERSON><PERSON><PERSON> johon vii<PERSON>an", "otherTerms": "<PERSON><PERSON> ehdot ja lis<PERSON>", "parties": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sellers": "<PERSON><PERSON><PERSON><PERSON>(t)", "terms": "Vastatar<PERSON><PERSON><PERSON>", "unencumberedPrice": "<PERSON><PERSON><PERSON> velaton ka<PERSON><PERSON>", "validUntil": "Vastatarjouksen voimassaoloaika pvm ja kellonaika", "validUntilHeader": "Voimassaoloaika", "counterOfferCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>", "validationFailed": "Täytä kaikki pakolliset kentät", "creationFailed": "Vastatarjouksen luominen epäonnistui", "offerors": "Vastatar<PERSON><PERSON><PERSON> ta<PERSON>", "offerees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sendForOfferorSigning": "Lähetä tarjoajan allekirjoittamiseen", "sendForOffereeSigning": "Lähetä tarjouksen saajan allekirjoittamiseen", "counterOfferUpdated": "Vastata<PERSON><PERSON><PERSON>", "updateFailed": "Vastatarjouksen päivittäminen epäonnistui", "counterOfferSentForSigning": "Vastatarjous l<PERSON>tty allekirjoitettavaksi", "counterOfferRejected": "Vastata<PERSON><PERSON><PERSON>", "failedToReject": "Vastatarjouksen hylkä<PERSON> epäonnistui"}, "createCounterOffer": "<PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "update": "Päivitä", "date": "Päiväys", "plusAddDocument": "+ Lisää asiakirja", "plusAddTerm": "+ <PERSON><PERSON><PERSON><PERSON> ehto", "documentName": "<PERSON><PERSON><PERSON><PERSON>", "term": "<PERSON><PERSON> ka<PERSON>an ehto", "termDeadline": "<PERSON><PERSON><PERSON>", "quickSearchPlaceholder": "<PERSON><PERSON>i koh<PERSON>...", "roles": {"Admin": "Ylläpitäjä", "Realtor": "Välittäjä", "Photographer": "Val<PERSON>va<PERSON>", "None": "<PERSON><PERSON> mit<PERSON>n"}, "organizationSwitched": {"title": "Organisaatio V<PERSON>", "description": "Organisaation vaihto onnistui", "error": "Organisaatiota ei vaihdettu. Yritä uudelleen myö<PERSON>min"}, "editProperty": {"id": "Ko<PERSON>denumero", "title": "<PERSON><PERSON><PERSON><PERSON>", "updatedTitle": "<PERSON><PERSON><PERSON>", "updatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti", "generalInformation": "<PERSON><PERSON><PERSON><PERSON>", "financialInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "description": "<PERSON><PERSON><PERSON>", "additionalDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amenities": "Mu<PERSON><PERSON><PERSON><PERSON>", "conditionAndHistory": "Kunto ja historia", "locationAndAddress": "<PERSON><PERSON><PERSON><PERSON> ja osoite", "coordinates": "Koordinaatit", "latitudeAndLongitude": "Leveysaste, Pituusaste", "publicLatLng": "<PERSON><PERSON><PERSON> k<PERSON>", "commission": "<PERSON><PERSON><PERSON>", "commissionType": "Komission tyyppi", "percentOfSalePrice": "% myyntihinnasta", "fixedAmount": "Kiinteä summa", "ivaTax": "ALV", "taxIncluded": "Verot sisältyvät", "taxAdded": "<PERSON><PERSON><PERSON> l<PERSON>", "noTax": "<PERSON><PERSON> veroja", "propertyFees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "communalFees": "Yhteisömaksut", "ibi": "IBI", "garbageTax": "<PERSON><PERSON><PERSON><PERSON>", "waterFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "electricity": "Sähkö", "cadastralReference": "Cadastral-numero", "buildingType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "buildingConstructor": "Rakennuttaja", "floor": "<PERSON><PERSON>", "floors": "<PERSON><PERSON><PERSON>", "totalFloors": "Kerroksia yhteensä", "buildingHasElevator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on hissi", "buildingSpecifications": "Rakennuksen tekniset tiedot", "buildingMaterials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foundationAndStructure": "<PERSON><PERSON><PERSON> ja rakenne", "roof": "<PERSON><PERSON>", "exteriorWalls": "<PERSON><PERSON><PERSON>inät", "additionalInformation": "Lisätietoja", "energyCertificate": "Energiasertifikaatti", "notApplicable": "<PERSON><PERSON>", "propertyHasCertificate": "<PERSON><PERSON><PERSON><PERSON> on sertifikaatti", "spaceAndSize": "<PERSON>ila ja koko", "builtSize": "<PERSON><PERSON><PERSON><PERSON> koko", "plotSize": "<PERSON><PERSON> koko", "terraceSize": "<PERSON><PERSON><PERSON> koko", "interiorSize": "Sisätilan koko", "totalAreaSize": "Kokonaispinta-ala", "roomsTotal": "<PERSON><PERSON><PERSON>", "pax": "Henkilömäärä", "toilets": "WC:t", "suiteBaths": "Kylpyhuoneet", "bathrooms": "Kylpyhuoneet", "bedrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garage": "Autotalli", "parkingSpaces": "Pysäköintipaikat", "none": "<PERSON><PERSON> mit<PERSON>n", "private": "<PERSON><PERSON><PERSON><PERSON>", "communal": "Yhteisöllinen", "carport": "Katettu autopaikka", "other": "<PERSON><PERSON>", "reference": "Ko<PERSON>denumero", "typeOfGarage": "Autotallin t<PERSON>ppi", "pool": "Uima-allas", "jacuzzi": "<PERSON><PERSON><PERSON>", "dropDesignSpa": "Drop Design Spa", "views": "Näkymät", "orientation": "<PERSON><PERSON>", "keysAndHandoff": "Ava<PERSON>t ja luovutus", "keysInExistince": "<PERSON><PERSON><PERSON> o<PERSON>", "protectedKeysTotal": "<PERSON><PERSON><PERSON><PERSON>", "protectedKeysDelivered": "Toimitetut", "protectedKeysExisting": "<PERSON><PERSON><PERSON> o<PERSON>", "unprotectedKeysTotal": "<PERSON><PERSON><PERSON><PERSON>", "unprotectedKeysDelivered": "Toimitetut", "unprotectedKeysExisting": "<PERSON><PERSON><PERSON> o<PERSON>", "whereKeysCanBeFound": "Mistä avaimet löytyvät?", "otherKeysInfo": "<PERSON><PERSON>", "otherKeysInfoPhoneNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otherKeysInfoDescription": "Kuvaile missä ja keneen ottaa yhteyttä...", "strandPropertiesKeysInfo": "Strand Properties", "strandPropertiesKeysInfoOffice": "<PERSON><PERSON><PERSON><PERSON> nimi", "strandPropertiesKeysInfoNotes": "<PERSON><PERSON> muistiinpanot tai kommentit...", "propertyHas": "Ko<PERSON>eella on:", "lights": "<PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "climateControl": "Il<PERSON><PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "furniture": "<PERSON><PERSON><PERSON><PERSON>", "rooms": "Huoneet", "security": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telecommunicationSystem": "Telekommunikaatiojärjestelmä", "renovations": "Remontit", "majorRenovationsPerformed": "Suuret remontit suoritettu", "renovationsHaveBeenPerformed": "Remontteja on suoritettu", "plannedRenovations": "Suunnitellut remontit", "thereArePlannedRenovations": "Suunnitteilla olevia remontteja", "describePerformedRenovations": "Kuvaile suoritetut remontit", "renovationsPerformedBeforeSellerOwnership": "Remontit suoritettu ennen my<PERSON>än omistusta.", "notPerformed": "Ei suoritettu", "unknown": "Tuntematon", "yes": "K<PERSON><PERSON>ä", "no": "<PERSON>i", "defectsDamagesRepairObserved": "Hava<PERSON><PERSON><PERSON> vikoja / vau<PERSON><PERSON> / korja<PERSON><PERSON><PERSON><PERSON> kohteessa", "defectsFound": "Havaitut viat", "describeDefectsDamagesRepairs": "<PERSON><PERSON><PERSON> k<PERSON>:", "officialPermitsAcquired": "Viralliset luvat hankittu", "finalInspectionOfChanges": "<PERSON><PERSON><PERSON>", "detailedAccountOfDamagesOrFault": "<PERSON><PERSON> t<PERSON>t tehtiin vaurioiden tai vikojen vuoksi, tark<PERSON><PERSON> se<PERSON><PERSON> vahi<PERSON> tai viasta, sen ilmenemisestä, la<PERSON><PERSON>udesta ja korjaustöiden sisällöstä:", "damagesAndDefects": "Vauriot ja viat", "otherDamages": "<PERSON><PERSON> vauriot", "suspectedDamagesOrProblems": "Omistaja/myyjä tietää tai epäilee, että kohteessa on:", "waterDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moistureDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeOfDamage": "<PERSON><PERSON><PERSON><PERSON>", "scopeOfDamage": "<PERSON><PERSON><PERSON><PERSON> laaju<PERSON>", "moldOrFungalProblems": "Home- tai si<PERSON><PERSON><PERSON><PERSON>", "otherSpecialDamages": "<PERSON><PERSON> er<PERSON>, viat tai puutteet", "causeOfDamage": "Vahingon syy", "repairMethod": "Korjausmenetelmä", "garden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telephoneNetwork": "Puhelinverkko", "generalCabling": "Yleiskaapelointi", "fiberCable": "Valokaapeli, kotiverkko", "certificateConsumptionRating": "Ser<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateConsumptionValue": "Sertifikaatti Kulutusarvo", "certificateEmissionRating": "Sertifikaatti Päästöluokitus", "certificateEmissionValue": "Sertifikaatti Päästöarvo", "name": "title", "hostawayPropertyId": "Hostaway-kohteen numero"}, "propertyType": {"resales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newdevelopment": "<PERSON><PERSON><PERSON> keh<PERSON>s", "plot": "<PERSON><PERSON><PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON>", "others": "<PERSON><PERSON>"}, "propertyCondition": {"excellent": "<PERSON><PERSON><PERSON>", "good": "Hyvä", "fair": "<PERSON><PERSON><PERSON><PERSON>", "renovationRequired": "Vatii remonttia", "restorationRequired": "Vatii entisöintiä"}, "profileDetails": {"status": {"active": "Aktiivinen"}, "viewProfile": "Näytä profiili", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "createContact": {"title": "Lisää kontakti", "realtor": "Välittäjä", "accountDetails": "<PERSON><PERSON><PERSON>", "contactInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "createdTitle": "Kontakti luotu", "createdDescription": "Onnistuneesti luotu", "tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emailIsAvailable": "Sähköposti on saatavilla", "emailAlreadyExists": "Sähköposti on jo käytössä", "contactToAssignedPersons": "K<PERSON><PERSON>", "admin": "Ylläpitäjä", "existNameContact": "<PERSON><PERSON><PERSON><PERSON> nimi on jo olemassa ja se on mä<PERSON><PERSON><PERSON> jollekin muulle, tark<PERSON>:", "success": "Onnistuneesti luotu", "error": "<PERSON><PERSON><PERSON>", "newsLetterMarketingConsent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> salli<PERSON>u", "emailMarketingConsent": "Asiakastarjousten lähettäminen sallittu", "systemInfo": "Järjestelmätiedot", "estateName": "<PERSON><PERSON><PERSON><PERSON> nimi", "createCompany": "Lisää yritys", "createEstate": "Lisää kuolinpesä", "updateCompany": "Päivitä yritys", "updateEstate": "Päivitä kuolinpesä", "updateContact": "Päivitä yhteystieto", "beneficiaries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addPerson": "Lisää henkilö", "signer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partners": "Osakkaat", "addPartner": "<PERSON>sää osa<PERSON>", "authorizedpartner": "Valtuutettu osakas", "validation": {"errorToast": "<PERSON><PERSON><PERSON>", "typeRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "sourceRequired": "Lä<PERSON>de on pakollinen", "addressRequired": "Osoite on pakollinen", "firstNameRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "lastNameRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "estateNameRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "companyNameRequired": "<PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "businessIdRequired": "Y-tunnus on pakollinen", "phoneNumberRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "emailRequired": "Sähköposti on pakollinen", "nameRequired": "<PERSON><PERSON> on pak<PERSON><PERSON>", "businessId": "Y-tunnus on pakollinen", "businessIdInvalid": "Syötä kelvollinen Y-tunnus (1234567-8)", "emailInvalid": "Syötä kelvollinen sähköposti"}, "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Sähköposti", "signingRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marketingSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fiNewsletterConsent": "<PERSON><PERSON><PERSON>", "esNewsletterConsent": "<PERSON><PERSON><PERSON>", "esMarbellaNewsletterConsent": "<PERSON><PERSON><PERSON>"}, "assignContact": {"title": "Määritä yhteystieto", "assignInfo": "Määritä {{number}} yhteystietoa kiinteistönvälittäjälle", "assign": "Määritä", "createdTitle": "Yhteyshenkilö määrätty", "createdDescription": "Määritetty onnistuneesti", "contactNotAssigned": "Yhteystietoa ei ole mää<PERSON>tty. <PERSON><PERSON><PERSON> my<PERSON> uude<PERSON>en"}, "editContact": {"title": "Päivitä kontakti", "updatedTitle": "Kontakti päivitetty", "updatedDescription": "Onnistuneesti päivitetty"}, "createGroup": {"title": "<PERSON><PERSON>", "groupInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>ot", "groupName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "newGroup": "<PERSON><PERSON><PERSON>", "groupNotCreated": "Ryhmää ei luotu. Yritä uudelleen myö<PERSON>."}, "editGroup": {"title": "Päivitä ryhmä", "updatedTitle": "<PERSON><PERSON><PERSON><PERSON>", "updatedDescription": "Onnistuneesti päivitetty", "groupNotUpdated": "Ryhmää ei päivitetty. Yritä uudelleen myö<PERSON>."}, "createEmail": {"title": "<PERSON><PERSON> viesti", "recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "Sisältö", "subject": "<PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON>", "send": "Lähetä", "emailSent": "Sähköposti on lähetetty"}, "createUser": {"title": "Lisää käyttäjä", "contactInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON>", "email": "Sähköposti", "chooseRole": "Valitse rooli", "accountRole": "<PERSON><PERSON><PERSON> rooli", "roleNote": "Kun luot uuden tilin, k<PERSON>yttäjä saa sähköpostin salasanan nollaamisohjeilla.", "createdTitle": "<PERSON>us<PERSON> kä<PERSON>äj<PERSON> luotu onnistuneesti", "createdDescription": "<PERSON><PERSON> katsella ja muokata sitä", "marketingSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newsLetterMarketingConsent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> salli<PERSON>u", "emailMarketingConsent": "Asiakastarjousten lähettäminen sallittu", "systemInfo": "Järjestelmätiedot", "estateName": "<PERSON><PERSON><PERSON><PERSON> nimi", "createCompany": "Lisää yritys", "createEstate": "Lisää kuolinpesä", "updateCompany": "Päivitä yritys", "updateEstate": "Päivitä kuolinpesä", "updateContact": "Päivitä yhteystieto", "beneficiaries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addPerson": "Lisää henkilö", "signer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partners": "Osakkaat", "addPartner": "<PERSON>sää osa<PERSON>", "authorizedpartner": "Valtuutettu osakas"}, "editUser": {"title": "Päivitä käyttäjä", "updatedTitle": "Käyttäj<PERSON> päiv<PERSON>tty", "updatedDescription": "Onnistuneesti päivitetty", "realtor": "Välittäjä", "accountDetails": "<PERSON><PERSON><PERSON>", "contactInfo": "Käyttäjätiedot", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "createdTitle": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "createdDescription": "Onnistuneesti luotu", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "realtorPublicInformation": "<PERSON><PERSON><PERSON> tiedot", "profileImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replace": "<PERSON><PERSON><PERSON>", "remove": "Poista", "addImage": "Lisää kuva", "jobTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamName": "<PERSON><PERSON><PERSON> nimi", "introductionText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "facebookLink": "Facebook-linkki", "instagramLink": "Instagram-linkki", "linkedinLink": "LinkedIn-linkki", "tiktokLink": "Tiktok-linkki", "provinces": "<PERSON><PERSON><PERSON><PERSON>", "languages": "<PERSON><PERSON>", "offices": "<PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preferredLanguage": "Käyttöliittymän kieli", "companyInvoicingDetails": "<PERSON><PERSON>ksen/laskutus<PERSON>t", "companyType": "Yritysty<PERSON>ppi", "companyName": "<PERSON><PERSON><PERSON><PERSON> nimi", "companyId": "Yrityksen ID", "companyPhone": "<PERSON><PERSON><PERSON><PERSON>", "companyEmail": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>köposti", "serviceFormBookingCalendarId": "Serviceform kalenterin ID", "videobotId": "Videobot ID", "hasDiasApiKey": "Onko Dias API avain", "setDiasApiKey": "Aseta uusi Dias API avain", "newDiasApiKey": "Uusi Dias API avain", "newDiasApiKeyPlaceholder": "Tyhjenn<PERSON> tai kirjoita uusi key"}, "editUserRole": {"title": "Päivitä käyttäjän rooli", "updatedTitle": "K<PERSON>yttäjän rooli päivitetty", "updatedDescription": "Käyttäjän rooli päivitetty onnistuneesti. Roolimuutokset tulevat voimaan se<PERSON>lla kirjautumisella.", "chooseRole": "Valitse rooli", "changeUserRole": "<PERSON><PERSON><PERSON><PERSON> rooli"}, "listContact": {"assignedTo": "Määritetty", "assignmentStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> tila"}, "general": {"mandatory": "Kentät, jotka on merkitty (*) ovat pakollisia", "nameOrEmail": "Nimi tai s<PERSON>h<PERSON>öposti...", "nameOrEmailOrPhone": "<PERSON><PERSON>, sähköposti tai puhelin...", "name": "Nimi..."}, "createProperty": {"title": "Lis<PERSON><PERSON> kohde", "realtor": "Välittäjä", "type": "Tyyppi", "location": "<PERSON><PERSON><PERSON><PERSON>", "contractType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rental": "<PERSON><PERSON><PERSON><PERSON>", "forSale": "Myytävänä", "exclusive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonExclusive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validityMonths": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listingType": "Ilmoitustyyppi", "condition": "<PERSON><PERSON>", "realtors": "Välittäjät"}, "createSalesAgreement": {"title": "<PERSON><PERSON>", "signingMethod": "Allekirjoitusmenetelmä", "property": "<PERSON><PERSON><PERSON>", "createdSalesAgreement": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "createdDescription": "Onnistuneesti luotu", "agreementDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>ot", "sendForSign": "Lähetä allekirjoitettavaksi", "sentForSign": "Lähetetty allekirjoitettavaksi", "documentIsReadyForSigning": "<PERSON><PERSON><PERSON> on valmis allekirjoitetta<PERSON>i", "previewContractByDownloadItBelow": "<PERSON><PERSON> es<PERSON><PERSON>ella sopimusta lataamalla sen alla.", "creatingDocument": "<PERSON><PERSON><PERSON> sopi<PERSON>...", "documentCreated": "<PERSON><PERSON><PERSON> luo<PERSON>", "sendForSignDescription": "<PERSON><PERSON><PERSON> onnistuneesti allekirjoitettavaksi", "contractOptions": "Sopimusvaihtoehdot", "reviewProperty": "Tarkista koh<PERSON> tiedot", "reviewSeller": "Tark<PERSON><PERSON> tiedot", "propertyCommission": "<PERSON><PERSON><PERSON>", "seller": {"name": "<PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON>", "email": "Sähköposti", "streetAddress": "Katuosoite", "city": "Kaupunki", "postalCode": "Postinumero", "country": "Maa", "nationality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contactLanguages": "<PERSON><PERSON>", "passportNumber": "Passinumero"}}, "shareTradeModal": {"createTitle": "Luo DIAS-osakehuoneistokaup<PERSON>", "editTitle": "Muokkaa DIAS-osakehuoneistokauppaa", "fiPropertySection": {"title": "<PERSON><PERSON><PERSON>", "propertyDropdown": "<PERSON><PERSON><PERSON>", "lockPropertyButton": "<PERSON><PERSON><PERSON>", "addressLabel": "<PERSON><PERSON>non katuosoite", "postalCodeLabel": "Postinumero", "cityLabel": "Kaupunki"}, "housingCompanySection": {"title": "Asunto-osakeyhtiö", "nameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n nimi", "businessIdLabel": "Asunto-osakeyhtiön Y-tunnus", "housingCompanyDropdown": "Asunto-osakeyhtiö"}, "apartmentSharesSection": {"title": "Asunto-osakkeet", "description": "Osak<PERSON>iden on vastattava sopimuksessa mukana oleviin.", "shareTypeLabel": "Osakkeen ty<PERSON>i", "DIGITAL": "Digitaalinen", "PAPER": "<PERSON>inen", "shareLabel": "Osakkeet", "shareGroupLabel": "Osakeryhmätunnukset", "shareStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tilanne", "sellerHasCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on osakeryhmän osoitteen", "bankHasCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON> on osakeryhmän osoitteen", "unknown": "Tuntematon"}, "participantsSection": {"sellerTitle": "<PERSON><PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON><PERSON>", "buyer": "Ostaja", "sellerTypeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "INDIVIDUAL": "<PERSON><PERSON><PERSON><PERSON>", "ORGANIZATION": "Organisaatio", "hasSellerRealtorPayment": "My<PERSON>jä maksaa osan komissiosta", "yes": "K<PERSON><PERSON>ä", "no": "<PERSON>i", "sellerRealtorPaymentAmount": "<PERSON><PERSON><PERSON><PERSON>ma, mukaan lukien ALV", "removeSeller": "<PERSON><PERSON>", "addSeller": "Lisää my<PERSON>jä", "buyerTitle": "<PERSON><PERSON><PERSON><PERSON>", "addBuyer": "Lisää ostaja", "removeBuyer": "Poista ostaja", "buyerTypeLabel": "<PERSON><PERSON>jan <PERSON>", "transferTaxLabel": "Siirtotaksan m<PERSON>", "sellerBank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buyerBank": "<PERSON><PERSON><PERSON>"}, "participant": {"firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Sähköposti", "bank": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON>", "socialSecurityNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "companyName": "Organisaation nimi", "businessVat": "Organisaation ALV-numero", "businessIdsLabel": "Osakkeenomist<PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>"}, "realtorSection": {"initiatorTitle": "Alkuperäinen", "initiatorDropdown": "Alkuperäinen", "realtorTitle": "Välittäjä", "realtorDropdown": "Välittäjä", "realtorCompanyLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>s", "IBAN": "IBAN", "BIC": "BIC"}, "commissionSection": {"title": "<PERSON><PERSON><PERSON>", "commissionLabel": "<PERSON><PERSON><PERSON>", "includeCommission": "<PERSON><PERSON><PERSON> k<PERSON>", "realtorCommission": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, mukaan lukien ALV", "invoiceReferenceNumber": "Laskun viitenumero", "invoiceMessage": "<PERSON><PERSON><PERSON> viesti", "invoiceReference": "Laskun viite"}, "otherSection": {"title": "<PERSON><PERSON>", "deadlineLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> allekirjoitusdeadl<PERSON>", "mortgageLabel": "<PERSON><PERSON>o kokonaan tai osittain lainoitettu as<PERSON><PERSON><PERSON>"}, "documentsSection": {"title": "Asiakirjat", "documents": "asiaki<PERSON><PERSON><PERSON>", "existingAttachmentsLabel": "Valitse ladatut tiedostot", "documentSectionNote": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>si as<PERSON><PERSON><PERSON><PERSON><PERSON> sert<PERSON>i jaeta<PERSON> kaiki<PERSON> o<PERSON>", "documentSectionNoteBold": "Huom: <PERSON><PERSON><PERSON><PERSON> koko raj<PERSON> on 20MB", "billOfSale": "<PERSON><PERSON><PERSON>op<PERSON>", "uploading": "<PERSON><PERSON><PERSON>", "failedToUpload": "<PERSON><PERSON><PERSON> e<PERSON>", "houseManagersCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shareCertificate": "Osa<PERSON><PERSON><PERSON><PERSON><PERSON> sert<PERSON>", "ownerApartment": "Omist<PERSON><PERSON> asun<PERSON> t<PERSON>", "ownerApartmentNote": "Tämä liite on saatava HTJ:ltä samana päivänä, kun kauppa lähetetään DIASiin. Muuten yksi pankki per osakeryhmätunnus peruuttaa kaupan. Tarvitset yhden tulosteen osakeryhmätunnuskohtaisesti", "companyStatement": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>o", "spousesConsent": "Spousen käyttöönotto", "otherDocuments": "Muuasiakirja"}, "createdSuccess": "<PERSON><PERSON><PERSON>", "updatedSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti", "createFailed": "<PERSON><PERSON><PERSON> e<PERSON>", "updateFailed": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "initiateSuccess": "Alkuperäinen lähetetty onnistuneesti", "initiateFailed": "Alkuperäinen lähetys epäonnistui"}, "shareTrade": {"title": "DIAS-osakekauppa", "status": {"DRAFT": "Luonnos", "INITIATED": "Alkuperäinen", "SELLER_BANK_APPROVED": "<PERSON><PERSON><PERSON><PERSON>n pan<PERSON> hyväksyi", "BUYER_BANK_APPROVED": "Ostajan pan<PERSON> hyväksyi", "INITIATOR_SIGNING_CONFIRMATION_RECEIVED": "Alkuperäinen allekirjoitettu", "SIGNING_STARTED": "Allekirjoitustapahtuma al<PERSON>u", "SIGNING_COMPLETED": "Allekirjoitustapahtuma suoritettu", "BUYER_BANK_PAYMENTS_COMPLETED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SELLER_BANK_PAYMENTS_COMPLETED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SELLER_BANK_TRANSFERRED_SHARE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BUYER_BANK_RECEIVED_SHARE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COMPLETED": "Val<PERSON>", "MOVED_TO_MANUAL_PROCESSING": "<PERSON><PERSON><PERSON><PERSON> man<PERSON><PERSON> k<PERSON>", "CANCELLED": "Peruutettu"}, "overview": {"title": "Yleiskatsaus", "description": "DIAS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on prosessi, jossa asunnon osakkeiden myyjä ja ostaja sopivat osakkeidensa siirrosta toisilleen. Tätä prosessia käytetään, kun myyjä ja ostaja eivät ole sama henkilö tai organisaatio. <PERSON><PERSON><PERSON> aloittaa kiinteistönvälittäjä, joka on vastuussa kohteesta. <PERSON><PERSON><PERSON> saadaan päätökseen, kun myyjä ja ostaja ovat allekirjoittaneet kauppakirjan ja DIAS on aloittanut kaupan.", "preparation": "Valmistelu", "inReview": "Tarkastelussa", "signing": "Allekirjoitus & Maksu", "finalSteps": "Lopulliset vaiheet", "lastUpdated": "Viimeksi päiv<PERSON>tty", "created": "<PERSON><PERSON><PERSON>", "initiator": "Aloittaja", "initiatorTradeReferenceId": "Aloit<PERSON><PERSON> viitenumero", "needHelp": "Tarvitsetko apua?", "diasSupportPage": "DIAS-tukisivu", "attachments": "Li<PERSON><PERSON>"}, "preparation": {"title": "Vaihe 1: Valmistele DIAS-osakekauppa", "description": "Täytä kaikki vaaditut kentät huolellisesti. <PERSON><PERSON> kaikki on lisät<PERSON>, voit aloittaa tämän kaupan.", "editButton": "<PERSON><PERSON><PERSON><PERSON>", "missingRequiredData": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaa<PERSON><PERSON> tiedot", "missingRequiredDataDescription": "<PERSON><PERSON><PERSON> vaa<PERSON><PERSON> tiedot on täytettävä ennen kuin voit siirtyä se<PERSON>avaan vaiheeseen."}, "inReview": {"title": "Vaihe 2: <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> on nyt tarkastelussa. Myyjä ja ostaja saavat sähköpostin kaupan tiedoista. <PERSON><PERSON><PERSON> saadaan pä<PERSON><PERSON><PERSON><PERSON>, kun myyjä ja ostaja ovat allekirjoittaneet kauppakirjan ja DIAS on aloittanut kaupan.", "cancel": "<PERSON><PERSON> ka<PERSON>", "clone": "Kloonaa", "cloneThisTrade": "Kloonaa tämä kauppa", "cloneThisTradeDescription": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> on peruutettu. <PERSON><PERSON>, voit kloonata sen aloit<PERSON><PERSON><PERSON> prosessin u<PERSON>."}, "signing": {"title": "Vaihe 3: <PERSON>ek<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>", "signed": "Allekirjoitettu", "pending": "Odottaa", "participantSigning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewSigningDetails": "Näytä allekirjoitustiedot", "movedToManualProcessing": "<PERSON><PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON> man<PERSON> k<PERSON><PERSON>", "movedToManualProcessingDescription": "Et saa enää automaattisia päivityksiä tästä kaupasta. Lisätietoja varten ota yhteyttä DIAS-tukeen:", "wrongEmailAddress": "Väärä sähköpostiosoite? Anna o<PERSON>list<PERSON> se<PERSON> linkki kirjautumista varten.", "description": "<PERSON><PERSON><PERSON>", "signingDetails": "Allekir<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "finalSteps": {"title": "Vaihe 4: <PERSON><PERSON><PERSON><PERSON> vaiheet", "description": "Lopullinen siirto voi kestää jopa 6 kuukautta. <PERSON><PERSON><PERSON><PERSON> sinulle, kun kauppa on valmis, ja asiakirjat arkistoidaan automaattisesti helppoa pääsyä varten. Sinun ei tarvitse tehdä mitään muuta.", "download": "Lataa", "downloadDocuments": "La<PERSON>a <PERSON>", "downloadDocumentsDescription": "Voit nyt ladata kaupan <PERSON>."}, "modal": {"cancelTrade": "Peruuta DIAS-osakekauppa", "cancelTradeDescription": "<PERSON><PERSON> tämän kaupan. Tämä pysäyttää kaiken edistymisen tässä kaup<PERSON>a, ja sinun on luotava uusi kauppa jatkaaksesi tämän kohteen kauppaa.", "cancelButton": "<PERSON><PERSON> ka<PERSON>", "goBackButton": "<PERSON><PERSON><PERSON>", "cancellationReason": "<PERSON><PERSON><PERSON>sen syy", "cancellationReasonError": "Teksti ei saa sisältää erikoismerkkejä ja enintään 5000 merkkiä.", "signingDetails": "Allekir<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "footer": {"nextStepInitiate": "<PERSON><PERSON><PERSON> vaihe: <PERSON><PERSON><PERSON>", "nextStepInitiateDescription": "<PERSON>un täytyy täyttää vaaditut tiedot ennen kuin voit siirtyä seuraavaan vaiheeseen.", "nextStepSign": "Se<PERSON>ava vaihe: Allekirjoitus & Ma<PERSON>u", "nextStepSignDescription": "<PERSON><PERSON> ka<PERSON> on hy<PERSON><PERSON><PERSON><PERSON><PERSON>, voit lähettää sen allekirjoitettavaksi. Allekirjoittajilla on 24 tuntia aikaa allekirjoittaa sopimus", "nextStepFinalSteps": "<PERSON><PERSON><PERSON> vaihe: <PERSON><PERSON><PERSON><PERSON> vaiheet", "nextStepFinalStepsDescription": "<PERSON>n kaikki ovat allek<PERSON>, lopullinen vaihe aktivoituu automaattisesti.", "initiate": "Aloita", "sendForSigning": "Lähetä allekirjoitettavaksi"}, "results": {"success": "<PERSON><PERSON><PERSON>", "initiateSuccess": "Aloitettu onnist<PERSON>esti", "sendForSigningSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> allekir<PERSON>ttavaks<PERSON>", "initiateFailed": "Aloitus epäonnistui!", "sendForSigningFailed": "Lähetys allekirjoitettavaksi epäonnistui!"}, "sections": {"property": "<PERSON><PERSON><PERSON>", "housingCompany": "Asunto-osakeyhtiö", "apartmentShares": "Asunto-osakkeet", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "commissions": "Komissiot", "buyers": "<PERSON><PERSON><PERSON><PERSON>", "initiator": "Aloittaja", "realtor": "Välittäjä", "other": "<PERSON><PERSON>", "documents": "Asiakirjat"}, "fields": {"streetAddress": "Katuosoite", "postalCode": "Postinumero", "city": "Kaupunki", "housingCompanyName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n nimi", "businessVatId": "Y-tunnus", "shareType": "Osaketyyppi", "shares": "Osakkeet", "osakeryhmatunnukset": "Osakeryhmätunnukset", "sellerShareCertificateStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> osa<PERSON>ertifikaatin tila", "sellerType": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "socialSecurityNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "Sähköposti", "bank": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "amountToBePaid": "Maksettava summa sis. ALV", "commissionWillBePaid": "<PERSON><PERSON><PERSON>", "bankAccountName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "iban": "IBAN", "bic": "BIC", "commissionInclVat": "Komissio sis. ALV", "invoiceMessage": "<PERSON><PERSON><PERSON><PERSON>", "invoiceReferenceNumber": "Laskun viitenumero", "buyerType": "<PERSON><PERSON>jan <PERSON>", "initiatorPersonId": "<PERSON><PERSON><PERSON><PERSON>", "realestateCompanyName": "Kiinteistöyhtiön nimi", "realestateCompanyVatId": "Kiinteistöyhtiön ALV-tunnus", "deadlineForSigning": "Allekirjoituksen määräaika", "mortgage": "<PERSON><PERSON><PERSON> on kokonaan tai osittain asuntolainoitettu"}, "validation": {"billOfSale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen", "shareCertificates": "Osakesertifikaatit on pakollinen, jos yksi <PERSON> on osakesertifikaatti", "ownerApartmentPrintouts": "<PERSON><PERSON> on DIGITAL, omist<PERSON>n asunnon tulosteet pitää vastata asunnon osakeryhmatunnuksien määrää", "houseManagersCertificates": "Isännöitsijäntodistuksia ei voi olla enemmän kuin yksi", "spousesConsent": "<PERSON><PERSON>lison suostumus ei voi olla enemmän kuin yksi", "deadlineForSigningBillOfSale": "Kauppakirjan allekirjoitettava viimeistään 7 arkipäivää tästä päivästä", "sellerShareCertificateStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> osakesertifikaatin tila on pakollinen", "apartmentAddressStreetAddress": "Katuosoite on pakollinen", "apartmentAddressPostalCode": "Postinumero on pakollinen", "apartmentAddressCity": "Ka<PERSON><PERSON><PERSON> on pakollinen", "housingCompanyName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "housingCompanyBusinessId": "Asunt<PERSON><PERSON><PERSON><PERSON><PERSON>htiön y-tunnus on pakollinen", "invalidFIVAT": "<PERSON>unt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n y-tunnus on virheellinen", "shares": "Osakkeet on pakollinen", "atLeastOneSeller": "Vähintään yksi my<PERSON> on pakollinen", "sellerType": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyyppi on pakollinen", "firstName": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "lastName": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "email": "Sähköposti on pakollinen", "socialSecurityNumber": "Henkilötunnus on pakollinen", "invalidSocialSecurityNumber": "Henkilötunnus on virheellinen", "atLeastOneBuyer": "Vähintään yksi ostaja on pakollinen", "buyerType": "<PERSON><PERSON><PERSON> t<PERSON> on pakollinen", "IBAN": "IBAN on pakollinen", "BIC": "BIC on pakollinen", "bankAccountName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> on pak<PERSON><PERSON>, kun my<PERSON> on välittäjä", "bankBusinessId": "<PERSON><PERSON><PERSON> on pakollinen", "invalidBIC": "BIC on virheellinen", "id": "ID on pakollinen", "createdById": "Luoja ID on pakollinen", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pak<PERSON>inen", "initiatorPersonId": "<PERSON><PERSON><PERSON><PERSON> vali<PERSON> on pakollinen", "initiatorId": "Aloittajan ID on pakollinen", "initiatorEmail": "Aloittajan sähköposti on pakollinen", "initiatorPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "invalidEmail": "Sähköposti on virheellinen"}}, "actionDropdown": {"create": {"header": "<PERSON><PERSON>i", "property": "Lis<PERSON><PERSON> kohde", "group": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "contact": "Lisää kontakti", "salesAgreement": "<PERSON><PERSON> <PERSON>", "salesActivity": "<PERSON><PERSON>", "user": "Lisää käyttäjä", "offer": "<PERSON><PERSON> vä<PERSON>starjous", "sellingOffer": "<PERSON><PERSON>", "brokerageOffer": "<PERSON><PERSON> vä<PERSON>starjous", "event": "<PERSON><PERSON>", "DoS": "DoS", "brochureFI": "<PERSON><PERSON> esite", "diasShareTrade": "<PERSON><PERSON><PERSON>", "matchMaking": "<PERSON><PERSON> j<PERSON>r<PERSON>stä<PERSON>n", "advertisement": "<PERSON><PERSON> tie<PERSON>"}, "documents": {"header": "Sopimukset", "agreement": "<PERSON><PERSON><PERSON>", "newOffer": "<PERSON><PERSON><PERSON>", "ready": "<PERSON><PERSON><PERSON> on valmis allekirjoitetta<PERSON>i"}, "event": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "logActivity": "<PERSON><PERSON><PERSON><PERSON>"}}, "propertiesInput": {"search": "<PERSON><PERSON><PERSON><PERSON><PERSON>, my<PERSON><PERSON><PERSON> tai ots<PERSON>...", "location": "Sijainti:", "beds": "<PERSON><PERSON><PERSON><PERSON>", "baths": "Kylpyhuoneet", "filters": "<PERSON><PERSON><PERSON>"}, "success": {"documentDownloadTitle": "Lataa <PERSON>", "documentDownloaded": "Tiedosto ladattu onnistuneesti", "documentDeleteTitle": "Tiedoston poistaminen", "documentDeleted": "<PERSON>ied<PERSON><PERSON> poistettu onnistuneesti", "documentUploadTitle": "<PERSON>ied<PERSON><PERSON>", "documentUploaded": "Tiedosto ladattu onnistuneesti"}, "errors": {"title": "<PERSON><PERSON> meni pieleen", "propertyNotCreated": "<PERSON><PERSON><PERSON><PERSON> ei luotu. <PERSON>rit<PERSON> my<PERSON><PERSON> uudelleen", "propertyNotUpdated": "Kohdetta ei päivitetty. Yrit<PERSON> uude<PERSON>en", "contactNotCreated": "Kontaktia ei luotu. Yritä myöhem<PERSON> uudelleen", "contactNotUpdated": "Kontaktia ei päivitetty. Yritä <PERSON> uude<PERSON>en", "userNotUpdated": "Käyttäjää ei päivitetty. Yrit<PERSON> my<PERSON> uude<PERSON>en", "salesAgreementNotCreated": "Myyntisopimus<PERSON> ei luotu", "eventNotCreated": "Tapahtumaa ei luotu", "eventNotUpdated": "Ta<PERSON>htumaa ei ole p<PERSON>iv<PERSON>tty", "sendForSignFail": "Sopimusta ei voitu lähettää allekirjoitettavaksi", "priceNotInRange": "Hinta ei ole al<PERSON>", "sizeNotInRange": "<PERSON><PERSON> ei ole al<PERSON>", "dateNotInRange": "Päivämäärä ei ole al<PERSON>ella", "minPriceHasToBeLowerThanMaxPrice": "<PERSON><PERSON><PERSON><PERSON> on oltava pienempi kuin maksimihinta", "minSizeHasToBeLowerThanMaxSize": "Minimikoon on oltava pienempi kuin maksim<PERSON>ko", "fromDateHasToBeBeforeUntilDate": "Alkupäivän on oltava ennen loppupäivää", "documentNotDownloaded": "Sopimusta ei voitu ladata. <PERSON><PERSON><PERSON> my<PERSON> uude<PERSON>en", "documentNotDeleted": "Sopimusta ei voitu poistaa. Yrit<PERSON> my<PERSON> u<PERSON>en", "salesActivityNotUpdated": "Myyntiaktiviteettia ei muokattu. <PERSON><PERSON><PERSON> my<PERSON><PERSON> u<PERSON>en", "salesActivityNotCreated": "Myyntiaktiviteettia ei luotu. Yritä my<PERSON><PERSON> uudelleen", "userNotCreated": "Käyttäjää ei luotu. Yritä myö<PERSON> uudelleen", "userNotActivated": "Käyttäjää ei aktivoitu. Yritä my<PERSON> uude<PERSON>en", "userNotDeactivated": "Käyttäjää ei deaktivoitu. Yrit<PERSON> my<PERSON> uude<PERSON>en", "propertyMustBeSetForSaleToCreateASalesAgreement": "<PERSON><PERSON><PERSON> on oltava til<PERSON><PERSON>, jotta voit luoda my<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequiredInformation": "<PERSON><PERSON><PERSON>uu vaa<PERSON><PERSON><PERSON> tie<PERSON>", "fieldRequired": "${path} on pakollinen", "AtLeastOneFieldMustBeSelected": "Vähintään yksi ${path} on valittava", "fieldDoesntMatchTheFormat": "${path} ei vastaa formaattia", "userRoleNotUpdated": "Käyttäjän roolia ei päivitetty. <PERSON><PERSON><PERSON> u<PERSON>en", "mustBeGreaterThanField2": "${path} on oltava suurempi kuin {{field2}}", "mustBeLaterThanField2": "${path} on oltava myöhemmin kuin {{field2}}", "documentUploadedFail": "Tiedoston lisääminen epäonnistui", "maxCharacters": "Maksimimerkkimäärä on ${max} merkkiä", "detailOfSaleNotCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "changingRequestedFailed": "Muutospyyntö epäonnistui", "fileSameNameExists": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON> on jo ladattu", "mustBeGreaterOrEqualThan": "${path} on oltava suurempi tai yhtä suuri kuin ${min}", "matchMakingNotCreated": "Matchmaking-ominaisuutta ei luotu. Yrit<PERSON> uudelleen my<PERSON>min", "uploadFailed": "<PERSON><PERSON><PERSON> e<PERSON>", "emailNotSent": "Sähköpostin lähettäminen epäonnistui", "pleaseFillRequiredFields": "Please fill the required fields to save changes.", "groupNotCreated": "Ryhmää ei luotu", "detailOfSaleNotEdited": "<PERSON><PERSON><PERSON>op<PERSON> ei muutettu", "matchMakingPropertyNotUpdated": "Ottelun järjestäminen ei päivitetty", "mustBePositive": "Arvo on oltava positiivinen", "mustBeValidPercentage": "<PERSON><PERSON><PERSON> on oltava välillä 0-100", "propertyAlreadyHasDoS": "<PERSON><PERSON><PERSON><PERSON> on j<PERSON>", "createDocumentSigningUserMissingName": "<PERSON><PERSON><PERSON> e<PERSON>, k<PERSON><PERSON>täjän nimi puuttuu", "createDocumentSigningContactMissingName": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>, kont<PERSON><PERSON> nimi puuttuu", "createDocumentSigningAtLeastOneSignerRequired": "<PERSON><PERSON><PERSON>, vähintään yksi allek<PERSON> on vaadittu", "createDocumentSigningSsnInvalidOrMissing": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on virheellinen tai puuttuu", "addSignerDocumentSigningNotFound": "Allekirjoitusta ei lö<PERSON>ynyt", "addSignerDocumentSigningNotAllowedStatus": "Allekirjoittajan lisäys epä<PERSON>ui, väärä allekirjoituksen status.", "addSignerSignerAlreadyAdded": "Allekirjoittaja on jo l<PERSON><PERSON>", "deleteDocumentSignerNotAllowedStatus": "Allekirjoittajan poisto ep<PERSON>, väärä allekirjoituksen status.", "deleteDocumentSignerNotFound": "Allekirjoittajaa ei lö<PERSON>yt", "createDocumentSigningOnlyPdfFilesSupported": "<PERSON><PERSON><PERSON>, vain pdf-<PERSON><PERSON><PERSON> tue<PERSON>an"}, "mediaPage": {"upload": "Lisää kuvia", "addItems": "Lisää", "dragndropMediaHere": "<PERSON>edä ja pudota kuvat tähän", "or": "tai", "chooseOwnDevice": "Valitse omal<PERSON>i", "supportedFiles": "<PERSON><PERSON><PERSON>", "remove": "Poista", "uploadedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{author}}", "unknownAuthor": "-", "coverPhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "documentsUpload": {"upload": "Lataa <PERSON>", "addItems": "Lisää", "dragndropFilesHere": "Ved<PERSON> ja pudota tied<PERSON>ot tähän", "or": "tai", "chooseOwnDevice": "Valitse omal<PERSON>i", "supportedFiles": "<PERSON><PERSON><PERSON>", "remove": "Poista", "uploadSignedCopy": "Lataa allekirjoitettu kopio", "rejectOffer": "Hylkää tarjous"}, "modalConfirmation": {"title": "Jatka ilman tallennusta?"}, "stats": {"totalNumberPublishedProperties": "Julkaistujen kohteiden kokonaismäärä", "totalNumberSoldProperties": "My<PERSON>yjen kohteiden kokonaismäärä", "totalNumberSalesAgreementsCreated": "Luotujen toimeksiantosopimusten kokonaismäärä", "published": "Julkaistu", "sold": "<PERSON><PERSON><PERSON>", "salesAgreements": "Toimeksiantosopimukset"}, "filters": {"listingType": "Ilmoitustyyppi", "any": "<PERSON><PERSON><PERSON>", "resale": "<PERSON><PERSON><PERSON><PERSON>", "rentals": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "minimum": "<PERSON><PERSON>", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "area": "<PERSON><PERSON>", "bedrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bathrooms": "Kylpyhuoneet", "conditions": "<PERSON><PERSON><PERSON><PERSON>", "size": "Pinta-ala", "source": "Lä<PERSON><PERSON>", "garagePoolGardenViews": "Autotalli, Uima-allas, Puutarha, Näkymät", "garage": "Autotalli", "pool": "Uima-allas", "garden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "views": "Näkymät", "orientations": "<PERSON><PERSON>", "amenities": "Mu<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "climatecontrol": "Il<PERSON><PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "furniture": "<PERSON><PERSON><PERSON><PERSON>", "rooms": "Huoneet", "security": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showMore": "Näytä lisää", "showLess": "Näytä vähemmän", "title": "<PERSON><PERSON><PERSON><PERSON>", "strandified": "Strandified", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "Alkaen", "to": "<PERSON><PERSON>", "until": "<PERSON>ak<PERSON>", "exclusive": "Ekslusiivinen"}, "yes": "K<PERSON><PERSON>ä", "no": "<PERSON>i", "youWillLoseYourChanges": "<PERSON><PERSON> p<PERSON>t nyt, tekemäsi muutokset ka<PERSON>avat.", "duplicate": "Ko<PERSON>i", "youWillDuplicateProperty": "Jatkamalla kopioit valitun koh<PERSON>.", "theFileWillBeDeleted": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>.", "successCreatedProperty": "<PERSON><PERSON><PERSON> luo<PERSON> on<PERSON>", "propertyDuplicated": "<PERSON><PERSON><PERSON>", "bed": "<PERSON><PERSON><PERSON>", "bath": "Kylpyhuone", "beds": "<PERSON><PERSON><PERSON><PERSON>", "baths": "Kylpyhuoneet", "interior": "Sisätila", "plot": "<PERSON><PERSON><PERSON>", "media": "Media", "details": "<PERSON><PERSON><PERSON>", "social": "Some", "tiktok": "Tiktok", "facebook": "Facebook", "instagram": "Instagram", "linkedin": "LinkedIn", "name": "<PERSON><PERSON>", "mobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON>", "phones": "<PERSON><PERSON><PERSON><PERSON>", "email": "Sähköposti", "address": "Osoite", "nationality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "Lä<PERSON><PERSON>", "passportNumber": "Passinumero", "nieNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (NIE)", "position": "<PERSON><PERSON>", "introductionText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "languages": "<PERSON><PERSON>", "offices": "<PERSON><PERSON><PERSON><PERSON>", "residentOfSpain": "<PERSON><PERSON><PERSON><PERSON>", "assignedTo": "Omistaja", "listedFor": "Listattu", "listedBy": "Listannut", "location": "<PERSON><PERSON><PERSON><PERSON>", "specs": "Tekniset tiedot", "reference": "Ko<PERSON>denumero", "realtors": "Välittäjät", "mainLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mainDescription": "Pääkuvaus", "description": "<PERSON><PERSON><PERSON>", "category": "Kategoria", "EUR": "€", "M2": "m²", "HA": "ha", "Rent": "Vuokra", "month": "kk", "privateInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legalRepresentative": "<PERSON><PERSON><PERSON> edustaja", "commission": "<PERSON><PERSON><PERSON>", "ivaTax": "ALV (IVA)", "commissionNotes": "Komission mui<PERSON>iinpanot", "commissionType": "Komission tyyppi", "internalNotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> mui<PERSON>", "seller": "<PERSON><PERSON><PERSON><PERSON>", "buyer": "Ostaja", "fullAddress": "<PERSON><PERSON> osoite", "coordinates": "Koordinaatit", "coordinatesVisibility": "Koordinaattien näkyvyys", "es": "Espanja", "fi": "<PERSON><PERSON>", "en": "Eng<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>", "sv": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "editDetails": "Muokkaa tie<PERSON>", "syncNow": "Synkronoi", "portalsynchronised": "Portaalit synkronoitu", "country": "Maa", "status": "Tila", "statusUpdated": "<PERSON><PERSON> p<PERSON>!", "successUpdateOfProperty": "Ko<PERSON><PERSON> tila päivitetty onnistuneesti", "changeStatus": "<PERSON><PERSON><PERSON><PERSON> tila", "updateStatus": "Päivitä tila", "statusError": "<PERSON><PERSON><PERSON> j<PERSON>/poiste<PERSON><PERSON>a", "statusErrorDescription": "<PERSON><PERSON><PERSON>ä<PERSON> kohteen tiedoista voi puuttua vaadittuja kenttiä tai se on saavuttanut julkaistavien kohteiden tässä portaalissa", "createdBy": "Luonut", "createdAt": "<PERSON><PERSON><PERSON>", "publish": "Julkai<PERSON>", "unpublish": "Poista julkaisu", "cancel": "Peruuta", "next": "<PERSON><PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON>", "accept": "Hyväksy", "save": "<PERSON><PERSON><PERSON>", "apply": "Käytä", "reset": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "people": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "own": "<PERSON><PERSON>", "network": "Verkko", "close": "Sulje", "properties": "<PERSON><PERSON><PERSON><PERSON>", "resaleProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newDevelopment": "<PERSON><PERSON><PERSON> keh<PERSON>s", "plotM": "Tontti m²", "builtM": "Rakennettu m²", "terraceM": "Terassi m²", "interiorM": "Sisätila m²", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saleInfo": "Myyntitiedot", "listingPrice": "<PERSON><PERSON><PERSON>", "streetAddress": "Katuosoite", "expiryDate": "Sopimuksen päättymispäivä", "city": "Kaupunki", "postalCode": "Postinumero", "latitude": "<PERSON><PERSON><PERSON><PERSON>", "longitude": "Pituusaste", "loading": "Lataa", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "authenticator": "Tunnistautumismenetelmä", "authentication": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tun<PERSON><PERSON><PERSON><PERSON>", "documentation": "Tarvittavat dokumentit", "publicDeedOrNotaSimple": "Julkaistu todistus ja/tai kohder<PERSON>i", "firstOccupancyLicense": "Ensimmäinen käyttöoikeuslupa", "identityDocument": "Henkilöllisyystodistukset", "urbanContributionTaxBill": "<PERSON><PERSON><PERSON><PERSON>", "rubbishCollectionFeesReceipt": "Viimeisin jätekeräysmaksu", "communityFeesReceipt": "<PERSON><PERSON><PERSON><PERSON>", "energyPerformanceCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CEE)", "certificateOfCommunityPropertyOwners": "Yhteisökohteiden omistajien todistus", "certificateOfTaxResidence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legalRepresentativeAndPowerOfAttorney": "<PERSON><PERSON><PERSON> edustaja ja valtakirja", "thePowersInCaseOfSociety": "Valtuudet yht<PERSON>ön <PERSON>", "separationRuling": "Erotuomio tai avioerotuomio", "prenuptialAgreement": "Ennen avioliittoa tehty sopimus", "certificateOfInheritance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateOfOutstandingDebt": "<PERSON><PERSON> myöntämä velkatodistus", "utilityBill": "Viimeisimmät laskut", "documents": "<PERSON><PERSON><PERSON><PERSON>", "contracts": "Sopimukset", "resources": "Resurssit", "public": "<PERSON><PERSON><PERSON>", "private": "<PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiresAt": "Vanhenee", "downloadDocument": "Lataa", "deleteDocument": "Poista", "visibility": "Näkyvyys", "portals": "Portaalit", "features": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "files": "Media", "tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcomeBack": "<PERSON><PERSON>", "dataSource": "Tietolähde", "userCreated": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "locationAndAddress": "<PERSON><PERSON><PERSON><PERSON> ja osoite", "latitudeAndLongitude": "Leveysaste, pituusaste", "title": "<PERSON><PERSON><PERSON><PERSON>", "fullDescription": "<PERSON><PERSON>", "financialInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "createBrochure": "<PERSON><PERSON> esite", "createWindowBrochure": "Luo i<PERSON>kunan esite", "propertyFees": "Kiinteistömaksut", "communalFees": "Yhteisömaksut", "ibi": "IBI", "garbageTax": "Jätemaksu", "water": "<PERSON><PERSON>", "electricity": "Sähkö", "exclusive": "Eksklusiivinen", "nonExclusive": "Ei-eksklusiivinen", "contractType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listingTypes": "Ilmoitustyypit", "cadastralReference": "Cadastral-numero", "additionalDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renovationsHaveBeenPerformed": "Remontteja on suoritettu", "thereArePlannedRenovations": "Suunniteltuja remontteja", "renovationsHaveNotBeenPerformed": "Remontteja ei ole suoritettu", "thereAreNoPlannedRenovations": "Suunniteltuja remontteja ei ole", "noDefectsFound": "Vikoja ei löytynyt", "amenities": "Mu<PERSON><PERSON><PERSON><PERSON>", "conditionAndHistory": "Kunto ja historia", "waterFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buildingType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "buildingConstructor": "Rakennuttaja", "floor": "<PERSON><PERSON>", "totalFloors": "Kerroksia yhteensä", "buildingHasElevator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on hissi", "buildingSpecifications": "Rakennuksen tekniset tiedot", "buildingMaterials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foundationAndStructure": "<PERSON><PERSON><PERSON> ja rakenne", "roof": "<PERSON><PERSON>", "exteriorWalls": "<PERSON><PERSON><PERSON>inät", "additionalInformation": "Lisätietoja", "energyCertificate": "Energiasertifikaatti", "notApplicable": "<PERSON><PERSON>", "propertyHasCertificate": "<PERSON><PERSON><PERSON><PERSON> on sertifikaatti", "spaceAndSize": "<PERSON>ila ja koko", "builtSize": "<PERSON><PERSON><PERSON><PERSON> koko", "plotSize": "<PERSON><PERSON> koko", "terraceSize": "<PERSON><PERSON><PERSON> koko", "interiorSize": "Sisätilan koko", "totalAreaSize": "Kokonaispinta-ala", "otherSpaces": "<PERSON>ut tilat", "totalSize": "Kokonaispinta-ala", "sizeVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roomsTotal": "<PERSON><PERSON><PERSON>", "pax": "Henkilömäärä", "toilets": "WC:t", "suiteBaths": "Kylpyhuoneet", "bathrooms": "Kylpyhuoneet", "bedrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garage": "Autotalli", "parkingSpaces": "Pysäköintipaikat", "none": "<PERSON><PERSON> mit<PERSON>n", "communal": "Yhteisöllinen", "carport": "Katettu autopaikka", "other": "<PERSON><PERSON>", "typeOfGarage": "Autotallin t<PERSON>ppi", "pool": "Uima-allas", "jacuzzi": "<PERSON><PERSON><PERSON>", "dropDesignSpa": "Drop design spa", "views": "Näkymät", "orientation": "<PERSON><PERSON>", "keysAndHandoff": "Ava<PERSON>t ja luovutus", "keysInExistince": "<PERSON><PERSON><PERSON> o<PERSON>", "protectedKeysTotal": "<PERSON><PERSON><PERSON><PERSON>", "protectedKeysDelivered": "Toimitetut", "protectedKeysExisting": "<PERSON><PERSON><PERSON> o<PERSON>", "unprotectedKeysTotal": "<PERSON><PERSON><PERSON><PERSON>", "unprotectedKeysDelivered": "Toimitetut", "unprotectedKeysExisting": "<PERSON><PERSON><PERSON> o<PERSON>", "whereKeysCanBeFound": "Mistä avaimet löytyvät?", "otherKeysInfo": "<PERSON><PERSON>", "otherKeysInfoPhoneNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otherKeysInfoDescription": "Kuvaile missä ja keneen ottaa yhteyttä...", "strandPropertiesKeysInfo": "Strand Properties", "strandPropertiesKeysInfoOffice": "<PERSON><PERSON><PERSON><PERSON> nimi", "strandPropertiesKeysInfoNotes": "<PERSON><PERSON> muistiinpanot tai kommentit...", "propertyHas": "Ko<PERSON>eella on:", "lights": "<PERSON><PERSON>", "climateControl": "Il<PERSON><PERSON><PERSON><PERSON>", "furniture": "<PERSON><PERSON><PERSON><PERSON>", "rooms": "Huoneet", "security": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telecommunicationSystem": "Telekommunikaatiojärjestelmä", "renovations": "Remontit", "majorRenovationsPerformed": "Suuret remontit suoritettu", "plannedRenovations": "Suunnitellut remontit", "describePerformedRenovations": "Kuvaile suoritetut remontit", "renovationsPerformedBeforeSellerOwnership": "Remontit suoritettu ennen my<PERSON>än omistusta.", "notPerformed": "Ei suoritettu", "unknown": "Tuntematon", "defectsDamagesRepairObserved": "<PERSON>va<PERSON><PERSON><PERSON> vikoja / vau<PERSON><PERSON> / korja<PERSON><PERSON><PERSON><PERSON> kohteella", "defectsFound": "Havaitut viat", "describeDefectsDamagesRepairs": "<PERSON><PERSON><PERSON> k<PERSON>:", "officialPermitsAcquired": "Viralliset luvat hankittu", "finalInspectionOfChanges": "<PERSON><PERSON><PERSON>", "detailedAccountOfDamagesOrFault": "<PERSON><PERSON> t<PERSON>t tehtiin vaurioiden tai vikojen vuoksi, tark<PERSON><PERSON> se<PERSON><PERSON> vahi<PERSON> tai viasta, sen ilmenemisestä, la<PERSON><PERSON>udesta ja korjaustöiden sisällöstä:", "damagesAndDefects": "Vauriot ja viat", "otherDamages": "<PERSON><PERSON> vauriot", "suspectedDamagesOrProblems": "Omistaja/myyjä tietää tai epäilee, että kohteessa on:", "waterDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moistureDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeOfDamage": "<PERSON><PERSON><PERSON><PERSON>", "scopeOfDamage": "<PERSON><PERSON><PERSON><PERSON> laaju<PERSON>", "moldOrFungalProblems": "Home- tai si<PERSON><PERSON><PERSON><PERSON>", "otherSpecialDamages": "<PERSON><PERSON> er<PERSON>, viat tai puutteet", "causeOfDamage": "Vahingon syy", "repairMethod": "Korjausmenetelmä", "garden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateConsumptionRating": "<PERSON><PERSON><PERSON>", "certificateConsumptionValue": "Sert. <PERSON>", "certificateEmissionRating": "Sert. <PERSON>", "certificateEmissionValue": "Sert. Päästöarvo", "telecommunicationSystems": "Telekommunikaatiojärjestelmät", "propertyHasElectricity": "Ko<PERSON>eessa on sähköt", "propertyHasLights": "<PERSON><PERSON><PERSON><PERSON> on valaistus", "garageTypes": "Autotallin tyypit", "poolTypes": "Uima-altaan tyypit", "gardenTypes": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyypit", "empty": "Tyhjä", "lift": "<PERSON><PERSON>", "keysInExistence": "<PERSON><PERSON><PERSON> o<PERSON>", "unauthorised": "Luvat<PERSON>", "credentialsExpiredLogInAgain": "Tunn<PERSON><PERSON><PERSON><PERSON> ovat <PERSON>. <PERSON><PERSON> oh<PERSON> kirja<PERSON>umissivu<PERSON>.", "condition": "<PERSON><PERSON>", "realtor": "Välittäjä", "photographer": "Val<PERSON>va<PERSON>", "admin": "Ylläpitäjä", "company": "<PERSON><PERSON>s", "website": "Verkkosivusto", "postCode": "Postinumero", "preferredLanguage": "Käyttöliittymän kieli", "notes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moreInfo": "Lisätietoja", "salesActivityInfo": "Myyntiaktiv<PERSON><PERSON><PERSON>ot", "associatedProperties": "Liitetyt koh<PERSON>t", "conversations": "Keskustelut", "leadInfo": "<PERSON><PERSON><PERSON>", "here": "t<PERSON>ällä", "created": "luotu", "activity": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "ROAIIB Nº", "salesActivity": {"activityID": "Toiminnan ID", "salesActivity": "Myyntiaktiv<PERSON>etti", "salesActivityStatus": "Tila", "salesActivityRelevance": "Merkitys", "salesActivitySource": "Lä<PERSON><PERSON>", "salesActivityType": "Tyyppi", "contacts": "Kontaktit", "createSalesActivity": "<PERSON><PERSON> aktiv<PERSON>", "editSalesActivity": "Muokkaa aktiviteettia", "editSalesActivityDescription": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "createSalesActivityWithId": "<PERSON><PERSON> katsella ja muokata aktiviteettia", "editSalesActivityWithId": "Muokkaa aktiviteettia", "salesActivityNotEdited": "Myyntiaktiviteettia ei muokattu. <PERSON><PERSON><PERSON> my<PERSON><PERSON> u<PERSON>en", "ticketInfo": "Aktiv<PERSON><PERSON><PERSON>ot", "linkedContacts": "Linkitetyt kontaktit", "none": "<PERSON><PERSON> mit<PERSON>n", "status": {"new": "<PERSON>us<PERSON>", "contacted": "Kontaktoitu", "qualification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proposal_offering": "E<PERSON><PERSON><PERSON>/Tarjous", "closed_won": "<PERSON><PERSON><PERSON><PERSON>", "closed_lost": "<PERSON><PERSON><PERSON><PERSON>"}, "relevance": {"hot": "<PERSON><PERSON><PERSON>", "warm": "<PERSON><PERSON><PERSON><PERSON>", "cold": "Kylmä", "neutral": "Neutraali"}, "source": {"personal_contact": "Henkilökohtainen kontakti", "website": "Verkkosivusto", "email": "Sähköposti", "phone_sms": "<PERSON><PERSON><PERSON>/SMS", "portal": "Portaali", "open_house": "Avoimet ovet", "other": "<PERSON><PERSON>"}, "type": {"buying": "<PERSON><PERSON><PERSON><PERSON>", "selling": "<PERSON><PERSON><PERSON><PERSON>ä", "other": "<PERSON><PERSON>"}, "errors": {"realtorRequired": "Välittäjä vaaditaan"}, "matchMaking": "Myyntiaktiv<PERSON>etti"}, "addressForm": {"fields": {"street": "<PERSON><PERSON>", "stairwell": "Po<PERSON><PERSON>", "apartment": "<PERSON><PERSON><PERSON><PERSON>", "apartmentNumber": "Huoneiston numero", "postalCode": "Postinumero", "district": "Kaupunginosa", "city": "Kaupunki", "streetAddress": "Katuosoite", "apartmentNumber_short": "Huoneiston numero", "municipality": "Kaupunki", "latitude": "<PERSON><PERSON><PERSON><PERSON>", "longitude": "Pituusaste", "coordinateSource": "Koordinaattien lähde", "coordinateAccuracy": "Koordinaattien tarkkuus"}, "enums": {"coordinateSource": {"GEO_CODING": "Hae osoitetiedot", "MANUAL": "Syötä käsin"}}}, "newBuilding": {"title": "New building", "NO": "Not new building", "UNKNOWN": "Unknown", "YES": "New building"}, "livingFloorCountCode": {"title": "Living floor count", "SINGLE_FLOOR": "Single Floor", "TWO_FLOORS": "Two Floors", "FLOOR_AND_A_HALF": "Floor and a Half", "MORE_THAN_TWO_FLOORS": "More than Two Floors", "NOT_KNOWN": "Not Known"}, "available": {"title": "Available", "AVAILABLE": "Available", "NEGOTIABLE": "Negotiable", "DATE": "Date", "RENTED": "Rented", "OTHER": "Other", "description": "Availability description"}, "fiProperty": {"sections": {"price": "<PERSON><PERSON>", "basic_information": "<PERSON><PERSON><PERSON><PERSON>", "prices_and_costs": "<PERSON><PERSON> ja kust<PERSON>", "condition_and_renovations": "<PERSON>nto ja remontit", "descriptions": "<PERSON><PERSON><PERSON>", "defects_damages": "Viat ja vahingot", "living_comfort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaces_materials": "<PERSON>ilat ja materiaalit", "kitchen_dining_area": "Keittiö ja ruokailut<PERSON>", "kitchen": "Keittiö", "dining_room": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bathroom": "Kylpyhuone", "sauna": "Sauna", "toilet": "WC", "utility_room": "<PERSON><PERSON><PERSON>itohu<PERSON>", "living_room": "<PERSON><PERSON><PERSON><PERSON>", "bedroom_walkin_closet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yht<PERSON><PERSON> vaa<PERSON><PERSON><PERSON> (walk-in closet)", "bedroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walk_in_closet": "Walk-in closet", "loft": "<PERSON><PERSON><PERSON>", "study_library": "Työhuone ja kirjasto", "study": "<PERSON><PERSON><PERSON><PERSON>", "library": "<PERSON><PERSON><PERSON><PERSON>", "hallway": "<PERSON><PERSON><PERSON>", "hall": "<PERSON><PERSON>", "balcony_terrace_yard": "<PERSON><PERSON><PERSON>, terassi ja piha", "storage_cellar": "Varasto ja kellarik<PERSON>o?", "more_information": "Lisätietoja", "ground_floor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draught_lobby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "encumbrances_restrictions_dues": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ma<PERSON><PERSON><PERSON><PERSON>", "services_connections": "<PERSON><PERSON><PERSON> ja yhteydet", "other_information": "<PERSON><PERSON> tie<PERSON>", "housing_company": "Taloyhtiö", "finances": "<PERSON><PERSON>", "construction": "Perustukset", "buildings": "Rakennukset", "plot": "<PERSON><PERSON><PERSON>", "services_transportation": "Palvelut ja liikenneyhteydet", "additional_details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yard": "Pi<PERSON>", "parkingSpaces": "Parkkipaikka", "housingCompanyPremiseStatistics": "Asunto-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tilastot", "renovations": "Remontit", "inspections": "Tu<PERSON><PERSON>ukset", "internetConnections": "Internetyhteys", "meetingAndLiabilities": "<PERSON><PERSON><PERSON><PERSON> ja vastuut?", "energyCertificate": "Ener<PERSON><PERSON><PERSON><PERSON>", "asbestosMapping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parkingSpace": "Parkkipaikka", "lease": "Vuokrata", "zoning": "<PERSON><PERSON><PERSON><PERSON>", "beach": "<PERSON><PERSON>", "constructionRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housingComfort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heart": "<PERSON><PERSON><PERSON>", "redemption": "Lunastus", "additionalRealtyDetailsLink": "Link<PERSON> lisätietoihin kiinteistön o<PERSON>", "condition": "<PERSON><PERSON>", "propertyDetail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "administration": "Hall<PERSON><PERSON>", "moreAboutStorage": "Lisätietoa säilytystiloista", "transaction": "<PERSON><PERSON><PERSON>", "parking": "Pysäköinti", "boilerRoom": "<PERSON><PERSON><PERSON><PERSON>", "patio": "<PERSON><PERSON>", "closet": "<PERSON><PERSON><PERSON>", "workRoomAndStudy": "Työhuone ja kirjasto", "hallHallwayAndDraughtLobby": "<PERSON><PERSON><PERSON><PERSON>", "storage": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "terrace": "<PERSON><PERSON><PERSON>", "balcony": "Parveke", "television": "Televisio", "damage": "<PERSON><PERSON><PERSON>/vika", "damages": "Vahingot/viat", "cost": "<PERSON><PERSON><PERSON>", "featureDescription": "Ominaisu<PERSON>n kuva<PERSON>", "shares": "Osakkeet", "rentDetails": "Vuokra", "renovationsAndInstallations": "Remontit ja asennukset", "defectsAndDamages": "Vauriot ja viat"}, "fields": {"contractType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listingType": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyyppi", "ownershipType": "Omistustyyppi", "property": "<PERSON><PERSON><PERSON>", "propertyAddress": "Kohteen osoite", "propertyType": "Alatyyppi", "propertyTypeGroup": "Ko<PERSON><PERSON> ty<PERSON>ppi", "periods": "<PERSON><PERSON><PERSON>", "amount": "Määrä", "costDescription": "Lisätietoja kustannuksista", "waterCharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": "<PERSON><PERSON><PERSON>", "costs": "Kustannukset", "depthShareAmountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auctionListing": "Hu<PERSON>kaup<PERSON><PERSON><PERSON>", "sizeDescription": "Lisätietoja pinta-alasta", "commission": "<PERSON><PERSON><PERSON>", "commissionNotes": "Komission lisätiedot", "commissionType": "Komission tyyppi", "theSalePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startingPrice": "Hinta alkaen", "debtFreePrice": "<PERSON><PERSON><PERSON>", "debtShareAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> velk<PERSON>", "residentialShareOverviewDebtFreePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentialShareOverviewDebtShareAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentialShareDebtShareAmountDescription": "Lisätietoja velk<PERSON>", "commercialOverviewDebtFreePrice": "Commercial overview - debt free price", "commercialOverviewDebtShareAmount": "Commercial overview - debt share amount", "otherShareOverviewDebtFreePrice": "Other Share overview debt free price", "otherShareOverviewDebtShareAmount": "Other share overview debt share amount", "wallMaterial": "Seinä<PERSON><PERSON><PERSON><PERSON>", "floorMaterial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ceilingMaterial": "Kat<PERSON><PERSON><PERSON><PERSON>", "roomStructure": "Huonejärjestys", "residentialType": "Tyyppi", "livingQuarters": "<PERSON><PERSON><PERSON><PERSON>", "buildingFloorCode": "<PERSON><PERSON><PERSON><PERSON> tunnus", "livingAreaFloorCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> tunnus", "floorLevel": "<PERSON><PERSON><PERSON><PERSON>", "livingFloorLevel": "<PERSON><PERSON><PERSON><PERSON>", "totalFloors": "Kerroksia yhteensä", "leaseStartDate": "Vuokrasuhteen alkupvm.", "leaseEndDate": "Vuokrasuhteen loppupvm.", "leaseType": "<PERSON>uokra<PERSON><PERSON><PERSON><PERSON><PERSON>", "depositPaymentDate": "Vakuuden maksupäivä", "depositAmount": "Vakuuden määrä", "housingCompany": "Taloyhtiö", "businessId": "<PERSON>loyhtiön y-tunnus", "manager": "Isännöitsijä", "managerContactDetails": "Isännöitsijän <PERSON>", "houseManagerCertificate": "Isännöitsijäntodistus", "maintenanceCompany": "Huoltoyhtiö", "propertyId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builder": "Rakennuttaja", "developmentPhase": "Rakentamisen vaihe", "usageStartYear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructionAndUsageYearDescription": "Rakentamis- ja k<PERSON><PERSON>nottov<PERSON>den lisätiedot", "hasElevator": "<PERSON><PERSON> on", "elevatorTakesToApartment": "<PERSON><PERSON> as<PERSON>", "hasSauna": "Sauna on", "yard": "Pi<PERSON>", "yardDescription": "<PERSON><PERSON> k<PERSON>", "plot": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "ownerShip": "<PERSON><PERSON>", "ownershipDescription": "Lisätietoja ton<PERSON> o<PERSON>", "description": "<PERSON><PERSON>", "number": "Tontin numero"}, "landlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "annualRent": "<PERSON><PERSON><PERSON>inen vuokra", "redeemable": "Lunastettavissa", "redeemableDescriptions": "Lisätietoja lunastuksesta", "optionalRentalPlot": "Valinnainen vuo<PERSON>tti", "optionalRentalPlotDescription": "Valinnaisen vuokratontin kuvaus", "lease": "Vuokra", "leaseEnd": "Vuokra päättyy", "leasePeriodDescription": "Lisätietoja vuora-ajasta", "landChargeId": "<PERSON><PERSON><PERSON><PERSON><PERSON> tunnus?", "leaseHolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leaseTransferLimitations": "Vuokraoikeuden si<PERSON>ron r<PERSON>", "leaseTransferLimitationsDescription": "Vuokraoikeuden siirron raj<PERSON>en kuvaus", "confirmedSizeOfThePlot": "<PERSON><PERSON><PERSON><PERSON><PERSON> tontin koko", "sizeUnit": "Koon yks<PERSON>kö", "areaSize": "<PERSON><PERSON><PERSON> koko", "areaUnit": "<PERSON>ueen y<PERSON>", "zoning": "<PERSON><PERSON><PERSON><PERSON>", "zoningDescription": "Lis<PERSON><PERSON><PERSON>", "beach": {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON> t<PERSON>", "typeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ran<PERSON>", "waterBody": "Vesistö"}, "constructionRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "densityRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "floorArea": "Pinta-ala", "constructionRightDescription": "Lisätietoja rakenn<PERSON>", "unbuiltPlot": "<PERSON><PERSON><PERSON><PERSON> ton<PERSON>", "internetConnections": "Internet-yhteydet", "broadbandAvailable": "Laajakaista saatavilla", "fiberOpticInternet": "Valokuitu", "housingCompanyBroadband": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "broadbandOperator": "Laajakaistaoperaa<PERSON>ri", "internalNetworkCabling": "Sisäverkon kaapelointi", "networkDescription": "Verkon kuvaus", "television": {"name": "Televisio", "type": "Television tyyppi", "typeDescription": "Television tyypin kuvaus"}, "meetingAndLiabilities": "<PERSON><PERSON><PERSON>set ja vastuut", "lastAnnualGeneralMeetingDate": "Viimeinen yhtiökokouspäivä", "nextAnnualGeneralMeetingDate": "Seuraava yhtiökokouspäivä", "identifiedDeficiencies": "<PERSON><PERSON><PERSON><PERSON> puutteet", "costIncurringLiabilities": "Kustannuksia aiheuttavat velvoitteet", "repairsAndMaintenanceAgreements": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja h<PERSON><PERSON><PERSON><PERSON>", "loanAmount": "<PERSON><PERSON> m<PERSON>", "mortgageAmount": "Kiinnityksen määrä?", "rentRevenue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bankAccountCreditLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "currencyCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "financingFeeInterestOnlyPeriod": "Rahoitusvastikkeen k<PERSON> kausi", "financingFeeInterestOnlyStartDate": "Rahoitusvastikkeen korkovapaan kauden alkupäivä", "financingFeeInterestOnlyEndDate": "Rahoitusvastikkeen korkovapaan kauden päättymispäivä", "managementChargesInfoLink": "<PERSON><PERSON>", "maintenance": "<PERSON><PERSON><PERSON>", "energyCertificates": {"name": "Energiasertifikaatit", "type": "Energiasertifikaatin tyyppi", "description": "Lisätietoja energiasertifikaatista"}, "asbestosMapping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asbestosMappingDone": "<PERSON><PERSON> teht<PERSON>?", "asbestosMappingReportAvailable": "<PERSON><PERSON>?", "asbestosPossibleInConstructionMaterials": "Rakennusmateriaalit voivat sisältää asbestia", "asbestosMappingDescription": "Lisätietoja asbestikartoituksesta", "parkingSpaces": "Pysäköintipaikat", "parkingSpace": {"name": "Pysäköintipaikka", "type": "Pysäköintipaikan tyyppi", "count": "Määrä", "description": "Lisätietoja pysäköintipaikasta", "add": "Lisää pysäköintipaikka", "transferCode": "Pysäköintipaikan siirto", "basisForPossessionCodes": "Pysäköintipaikan omistuksen perustelut"}, "premiseType": "T<PERSON><PERSON><PERSON>ppi", "managedByHousingCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "areaManagedByHousingCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "housingCompanyPremiseSpecificationDescription": "Taloyhtiön tilamäärittelyn kuvaus", "renovations": "Remontit", "renovation": "<PERSON><PERSON><PERSON>", "decidedByGeneralMeeting": "Päätetty yhtiökokouksessa", "renovationsDescription": "Lisätietoja remonteista", "isHousingCompanyNotified": "Onko ta<PERSON> tiedotettu?", "renovationStatus": "<PERSON><PERSON><PERSON> tila", "renovationType": "Re<PERSON>in tyyppi", "typeOtherDescription": "<PERSON>u, mik<PERSON>?", "addRenovation": "Lisää remontti", "inspections": "Tarkastukset", "inspection": {"name": "<PERSON>rka<PERSON><PERSON>", "type": "Tarkastuksen tyyppi", "description": "Tarkastuksen kuvaus", "add": "Lisää tarkastus"}, "additionalInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalDescription": "Lisäkuvaus", "listOfSharesTransferred": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lue<PERSON>", "digitalShares": "Digitaaliset osakkeet", "postalArea": "<PERSON><PERSON><PERSON>", "constructionMaterial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ventilation": "Il<PERSON><PERSON><PERSON><PERSON>", "outerRoof": "<PERSON><PERSON><PERSON><PERSON>", "outerRoofMaterial": "Ulkokaton materiaali", "premises": "Tilat", "housingCompanyPremiseSpecifications": "Taloyhtiön tilamääritykset", "nearbyAmenities": "Lähellä olevat palvelut", "schoolChildcare": "Koulut ja päiväkodit", "services": "Palvelut", "hobbiesAndActivities": "Harrastukset ja aktiviteetit", "activitiesAndRecreationDescription": "Aktiviteettien ja virkistyksen kuvaus", "transportConnections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transportationConnectionsDescription": "<PERSON><PERSON>nney<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "drivingDirections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drivingDirectionsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "administration": "Hall<PERSON><PERSON>", "propertyDetails": "<PERSON><PERSON><PERSON>", "sauna": "Sauna", "terrace": "<PERSON><PERSON><PERSON>", "balcony": "Parveke", "numberOfToilets": "WC-<PERSON><PERSON><PERSON><PERSON> luku<PERSON>", "numberOfBedrooms": "Ma<PERSON>uhuoneiden lukumäärä", "viewDescription": "Näkymän kuva<PERSON>", "shareholderInstallations": "Osakkaiden teetättämät muutostyöt", "renovationDescription": "<PERSON><PERSON><PERSON> k<PERSON>", "condition": "<PERSON><PERSON>", "conditionDescription": "Lisätietoa kunnosta", "damages": "Vauriot", "propertyHasOrHadDamages": "Ki<PERSON>eist<PERSON><PERSON><PERSON> on tai on ollut vaurioita", "inspectionsDescription": "Lisätietoja tarkastuksesta", "asbestosMappingDoneDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>y", "asbestosMappingReportAvailableDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asbestosPossibleInConstructionMaterialsDescription": "Rakennusmateriaalit saattavat sisältää asbestia", "asbestosDescription": "<PERSON><PERSON><PERSON> k<PERSON>", "housingComfort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thingsAffectingHousingComfort": "Asumisviihtyvyyteen vaikuttavat tekijät", "fitForWinterHabitation": "Soveltuu talvikäyttöön", "smokingAllowed": "Tu<PERSON><PERSON><PERSON>i sallittu", "petsAllowed": "<PERSON><PERSON><PERSON><PERSON> sallittu", "accessible": "Esteetön", "furnished": "Kalustettu", "heatingDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "underfloorHeatingDescription": "Lattialämm<PERSON>ks<PERSON> kuva<PERSON>", "hearth": "Takka", "hearthDescription": "Lisätietoja tulisi<PERSON>ta", "addHearth": "Lisää takka", "shareCertification": "Osakekirja", "shareCertificateAvailable": "Osakekirja <PERSON>", "redemption": "Lunastus", "redeemableByHousingCompany": "Lunastettavissa taloyht<PERSON>ön to<PERSON>", "redeemableByExistingShareholders": "Lunastettavissa olemassa olevien osakkaiden toimesta", "redemptionRightAppliesToAllShares": "Lunastusoikeus koskee kaik<PERSON>a o<PERSON>", "otherRestrictions": "<PERSON><PERSON>", "moreInformation": "Lisätietoja", "additionalRealtyDetailLinks": "Linkit kiinteistön lisätiedoille", "additionalRealtyDetailLink": "Linkki kiinteistön l<PERSON>ätiedoille", "linkTitle": "<PERSON><PERSON>", "moreInformationLink": "Lisätietolinkki", "url": "<PERSON><PERSON> (URL)", "modifications": "Muutokset", "livingType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addPremise": "Lisää taloyht<PERSON>ön tilan tarkenne", "additionalRealtyDetailsLink": "Linkki kiinteistön l<PERSON>ätiedoille", "glazedTerrace": "<PERSON><PERSON><PERSON><PERSON> terassi", "terraceDescription": "<PERSON><PERSON><PERSON>", "compassPoint": "Ilmansuunta", "glassMaintenanceResponsibility": "<PERSON><PERSON>", "balconyType": "Parveketyyppi", "otherTypeDescription": "<PERSON>u, mik<PERSON>?", "balconyDescription": "Parvekkeen kuvaus", "hasPrivateYard": "<PERSON><PERSON> piha", "basisForPossession": "Hallitsemisperuste", "basisForPossessionDescription": "Hallitsemisperusteen k<PERSON>", "patioDescription": "Pation kuvaus", "storageType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "transfer": "Varastotyyppi", "storageUsageLimitations": "<PERSON><PERSON>ston k<PERSON> r<PERSON>", "storageDescription": "<PERSON><PERSON><PERSON> k<PERSON>", "refrigeratedCellar": "Viileäkellari", "boilerRoomDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "parkingDescription": "Lisätietoa p<PERSON>äköinnistä", "moreInformationAboutThePremises": "Lisätietoa tiloista", "moreInformationAboutTheMaterials": "Lisätietoa materiaaleista", "transactionDescription": "<PERSON><PERSON><PERSON>", "transactionIncludes": "<PERSON><PERSON><PERSON><PERSON> ku<PERSON>u", "transactionDoesNotInclude": "<PERSON><PERSON><PERSON><PERSON> ei kuulu", "roomDescription": "<PERSON><PERSON><PERSON> k<PERSON>", "equipmentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuvaus", "monthlyRentPrice": "Kuukausivuokra", "damageType": "Vahingon tyyppi", "damageDate": "Vahingon päivämäärä", "causeDescription": "Vahingon syy", "extentDescription": "<PERSON><PERSON><PERSON><PERSON> laaju<PERSON>", "repairDescription": "<PERSON><PERSON><PERSON><PERSON>", "damageDescriptionLong": "Voidaan liittää erillinen dokumentti, jossa tarkempi sel<PERSON>s vahingo<PERSON>.", "featureName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "code": "Omistustyyppi", "squareMeters": "Asuinpinta-ala", "isRented": "Vuokrattu", "Code": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "hasBalcony": "On parveke", "hasTerrace": "On terassi", "redemptionDescription": "<PERSON>st<PERSON><PERSON> kuvaus", "livingFloorCount": "Asuinker<PERSON>ten lukumäärä", "startingDebtFreePrice": "Alkuperäinen velaton hinta", "startingDebtShareAmount": "Alkuperäinen velkaosuus", "availabilityDate": "Vapautumispäivämäärä"}, "enums": {"areaKind": {"MAJOR_REGION": "<PERSON><PERSON><PERSON>", "REGION": "<PERSON><PERSON>", "SUBREGION": "<PERSON><PERSON><PERSON>", "MUNICIPALITY": "<PERSON><PERSON>", "DISTRICT": "Kaupunginosa"}, "contractType": {"EXCLUSIVE": "Eksklusiivinen", "NON_EXCLUSIVE": "Ei-eksklusiivinen"}, "listingType": {"SALE": "Myytävänä", "RENTAL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ownershipType": {"SHARE": "Osake", "PROPERTY": "Kiinteistö"}, "propertyTypeGroup": {"COMMERCIAL_PROPERTY": "<PERSON><PERSON><PERSON><PERSON>", "ESTATE": "<PERSON><PERSON><PERSON> <PERSON>a <PERSON><PERSON><PERSON><PERSON><PERSON>", "LEISURE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "Autotallit ja muut", "PLOT": "<PERSON><PERSON><PERSON>", "RESIDENTIAL": "<PERSON><PERSON><PERSON>", "SHARED_APARTMENT": "Solu", "SUBLEASE": "<PERSON><PERSON><PERSON><PERSON>"}, "propertyType": {"APARTMENT_HOUSE": "<PERSON><PERSON><PERSON>", "APARTMENT_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ARABLE_FARM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BALCONY_ACCESS_BLOCK": "Luhtitalo", "BOAT": "Venepaik<PERSON>", "BUSINESS_OR_INDUSTRIAL_PLOT": "<PERSON><PERSON>- tai te<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CAR_SHED": "Autohallipaikka", "CAR_SHELTER": "Autokatos", "CARE_FACILITY": "<PERSON><PERSON><PERSON><PERSON>", "COMMERCIAL_PLOT": "<PERSON><PERSON><PERSON><PERSON>", "COTTAGE_OR_VILLA": "<PERSON><PERSON>k<PERSON> tai huvila", "COWORKING": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DETACHED_HOUSE": "Omakotitalo", "FARM": "Ma<PERSON>la", "FOREST": "Metsätila", "GARAGE": "Autotalli", "HOLIDAY_PLOT": "Vapaa-<PERSON><PERSON>", "HOUSE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "INDUSTRIAL_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LEISURE_APARTMENT": "Lomahuoneisto", "OFFICE_SPACE": "<PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>", "PARCEL_OF_LAND": "Määräala", "PARKING_SLOT": "Autopaikka", "PRODUCTION_FACILITY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RETAIL_SPACE": "<PERSON><PERSON><PERSON><PERSON>", "ROW_HOUSE": "Rivitalo", "ROW_HOUSE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SEMI_DETACHED_HOUSE": "<PERSON><PERSON><PERSON>", "SEPARATE_HOUSE": "<PERSON><PERSON><PERSON><PERSON>", "SOLAR_FARM": "Aurinkovoimala", "STORAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STORAGE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TIME_SHARE_APARTMENT": "Lo<PERSON>osa<PERSON>", "WAREHOUSE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WILDERNESS": "Erämaa-alue", "WOODEN_HOUSE_APARTMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON>osa<PERSON>"}, "floorSurfaceMaterial": {"TILED": "<PERSON>att<PERSON>tti<PERSON>", "LAMINATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PARQUET": "Parkettilattia", "PLASTIC": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BOARD": "Lautalattia", "STONE": "<PERSON><PERSON><PERSON><PERSON>", "CONCRETE": "Betonilattia", "MICROCEMENT": "Mikrosementtilattia", "VINYL": "Vinyylilattia", "VINYL_CORK": "Vinyylikorkkilattia", "CORK": "Korkkilattia", "ACRYLIC_MASS": "Akryylimassalattia", "WALL_TO_WALL_CARPET": "Kokolattiamatto", "OTHER": "<PERSON><PERSON>"}, "wallSurfaceMaterial": {"CERAMIC_TILE": "Laatta", "WOOD": "Puuseinä", "LOG": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PANEL": "Paneeliseinä", "WAINSCOT": "Puolipaneeliseinä", "WALLPAPER": "Tapet<PERSON>inä", "GLASS_FIBRE_TEXTILE_COVERED": "Lasikuitutapettiseinä", "GLASS": "Lasiseinä", "PARTIALLY_TILED": "Osalaatoitusseinä", "PLASTIC": "Muoviseinä", "STONE": "Kiviseinä", "CONCRETE": "Betoniseinä", "MICROCEMENT": "Mikrosementtiseinä", "PAINT": "<PERSON><PERSON><PERSON><PERSON> seinä", "OTHER": "<PERSON><PERSON>"}, "ceilingSurfaceMaterial": {"PLASTER": "<PERSON><PERSON><PERSON>", "PANEL": "<PERSON><PERSON><PERSON>", "STONE": "<PERSON><PERSON>", "PAINTED": "Maalattu", "WOOD": "<PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "leaseType": {"FIXED_TERM": "Määräaikainen", "UNTIL_FURTHER_NOTICE": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chargePeriod": {"NOT_KNOWN": "<PERSON><PERSON> tied<PERSON>", "MONTH": "<PERSON><PERSON><PERSON><PERSON>", "MONTH_PER_PERSON": "Kustannus per hlö / kk", "SINGLE_PAYMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YEAR": "<PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "costType": {"ALERT_CONTROL_SYSTEM": "Hälytysjärjestelmä", "CLEANING": "Puhtaanapito", "ELECTRIC_CAR_CHARGING_POINT": "Sähköauton latauspiste", "ELECTRIC_HEATING_COSTS": "Sähkölämmityksen kustannukset", "GARAGE": "Autotalli", "OTHER_HEATING_COSTS": "<PERSON><PERSON> l<PERSON>ustannukset", "OUTDOOR_PARKING_SPACE": "Ulkoautopaikka", "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG": "Sähköpistokepaikka", "PARKING_SPACE_FEE": "Parkkipaikka<PERSON><PERSON><PERSON>", "PROPERTY_TAX": "Kiinteistöver<PERSON>", "ROAD": "Tie", "SAUNA": "Sauna", "SPACE_IN_CARPORT": "Autokatospaikka", "SPACE_IN_CARPORT_WITH_ELECTRICAL_PLUG": "Autokatospaikka la<PERSON>pisteellä", "SPACE_IN_PARKING_HALL": "Autohallipaikka", "TELECOM_CONNECTION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TV": "TV", "USE_OF_HOUSE_DRYING_ROOM": "Ku<PERSON>ushuo<PERSON>n k<PERSON>ö", "USE_OF_HOUSE_LAUNDRY_ROOM": "Pesutuvan k<PERSON>ö", "WASTE": "Jätevesi", "WATER": "Käyttövesi", "WATER_AND_WASTE": "Käyttövesi- ja j<PERSON>"}, "waterChargeType": {"BASIC_FEE": "Perusma<PERSON><PERSON>", "BY_USE": "<PERSON><PERSON><PERSON><PERSON><PERSON> mukaan", "BY_USE_WITH_ADVANCE": "Ennakkomaks<PERSON>", "INCLUDED_IN_MAINTENACE_CHARGE": "Sisältyy hoitovastikkeeseen", "INCLUDED_IN_RENT": "Sisältyy vuokraan"}, "areaBasis": {"ARTICLES_OF_ASSOCIATION": "Yhtiöjärjestys", "MANAGERS_CERTIFICATE": "Isännöitsijäntodistus", "VERIFYING_MEASUREMENT": "Tarkistusmitattu"}, "ownershipTypeCode": {"OWN": "<PERSON><PERSON>", "PARTIAL_OWNERSHIP": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LIVING_RIGHT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "commissionType": {"FIXED": "Kiinteä", "PERCENT": "<PERSON><PERSON><PERSON>"}, "residentialType": {"APARTMENT_HOUSE": "<PERSON><PERSON><PERSON>", "DETACHED_HOUSE": "Omakotitalo", "ROW_HOUSE": "Rivitalo", "SEMI_DETACHED_HOUSE": "<PERSON><PERSON><PERSON>", "SEPARATE_HOUSE": "<PERSON><PERSON><PERSON><PERSON>", "WOODEN_HOUSE_APARTMENT": "Puutaloasunto", "BALCONY_ACCESS_BLOCK": "Luhtitalo", "COTTAGE": "<PERSON><PERSON><PERSON><PERSON>", "TIME_SHARE_APARTMENT": "Lo<PERSON>osa<PERSON>", "LEISURE_APARTMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o", "OTHER": "<PERSON><PERSON>"}, "floorLevel": {"ON_BOTTOM_FLOOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ON_MIDDLE_FLOOR": "Keskiker<PERSON>", "ON_TOP_FLOOR": "<PERSON><PERSON> ker<PERSON>", "NOT_KNOWN": "<PERSON><PERSON> tied<PERSON>"}, "livingFloorCount": {"SINGLE_FLOOR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TWO_FLOORS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FLOOR_AND_A_HALF": "<PERSON><PERSON><PERSON><PERSON>", "MORE_THAN_TWO_FLOORS": "<PERSON><PERSON> kaksi k<PERSON>ta", "NOT_KNOWN": "<PERSON><PERSON> tied<PERSON>"}, "common": {"YES": "K<PERSON><PERSON>ä", "NO": "<PERSON>i", "UNKNOWN": "<PERSON><PERSON> tied<PERSON>"}, "televisionType": {"ANTENNA": "<PERSON><PERSON><PERSON>", "CABLE": "<PERSON><PERSON><PERSON>", "IPTV": "IPTV", "SATELLITE": "<PERSON><PERSON><PERSON><PERSON>"}, "unbuiltPlotValue": {"hasBuildings": "To<PERSON><PERSON> on rakennuksia", "hasNoBuildings": "<PERSON><PERSON>lla ei ole rakenn<PERSON>", "TRUE": "To<PERSON><PERSON> on rakennuksia", "FALSE": "<PERSON><PERSON>lla ei ole rakenn<PERSON>"}, "hearthTypeCode": {"CONVECTION_FIREPLACE": "Kiertoilmatakka", "FIREPLACE": "Tulisija/takka", "FLUE_IN_PLACE": "<PERSON><PERSON><PERSON>", "HEAT_RETAINING_FIREPLACE": "<PERSON><PERSON><PERSON> takka", "IRON_STOVE": "<PERSON><PERSON><PERSON>", "OPEN_FIREPLACE": "Avotakka", "PLACE_ALLOCATED_FOR_FIREPLACE": "<PERSON><PERSON><PERSON><PERSON>", "BAKING_OVEN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "housingCompanyMaintenanceType": {"JANITOR": "<PERSON><PERSON><PERSON>", "PROPERTY_MAINTENANCE_COMPANY": "Kiinteistönhuoltoyhtiö", "BY_RESIDENTS": "Asukkaiden toimesta"}, "developmentPhase": {"PRE_MARKETING": "Ennakkomarkkinoinnissa", "IN_CONSTRUCTION": "Rakenteilla", "MOVE_IN_READY": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "constructionMaterial": {"BRICK": "<PERSON><PERSON><PERSON>", "CONCRETE": "<PERSON><PERSON>", "ELEMENT": "<PERSON><PERSON><PERSON>", "STEEL": "<PERSON><PERSON><PERSON><PERSON>", "STONE": "<PERSON><PERSON>", "TIMBER": "<PERSON><PERSON><PERSON> (Timber)", "WOOD": "<PERSON><PERSON><PERSON> (Wood)", "OTHER": "<PERSON><PERSON>"}, "outerRoofType": {"GABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HIPPED": "Au<PERSON><PERSON><PERSON>", "PENT": "Pulpettikatto", "FLAT": "Tasa<PERSON><PERSON>", "GAMBREL": "<PERSON><PERSON><PERSON><PERSON>", "MANSARD": "<PERSON><PERSON><PERSON><PERSON>", "SATERI": "Säterikatto", "OTHER": "<PERSON><PERSON>"}, "outerRoofMaterial": {"BRICK": "<PERSON><PERSON><PERSON>", "SHEET_METAL": "<PERSON><PERSON><PERSON>", "FELT": "<PERSON><PERSON><PERSON>", "BITUMEN_FELT": "Bitumihuopa", "REINFORCED_CONCRETE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PVC": "PVC", "STONE_COATED_METAL": "Kivipinnoitettu pelti", "COPPER": "<PERSON><PERSON><PERSON>", "GREEN_ROOF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "heatingSystem": {"DISTRICT_HEATING": "Kaukolämpö", "ELECTRIC": "Sähkölämmitys", "GAS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GEOTHERMAL_HEATING": "Maalämpö", "EXHAUST_AIR_HEAT_PUMP": "Poistoilmalämpöpumppu", "OIL": "Ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SUN": "Aurinkolämpö", "WATER_HEAT_PUMP": "Ilmavesilämpöpumppu?", "WOOD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "heatDistributionSystem": {"AIR_HEAT_PUMP": "Ilmalämpöpumppu", "ELECTRIC_CEILING_HEATING": "Sähkökatto­lämmi­tys", "ELECTRIC_RADIATOR": "Sähköpatteri", "ELECTRIC_UNDERFLOOR_HEATING": "Sähköinen lattialämmitys", "WATER_RADIATOR": "<PERSON>esi<PERSON><PERSON><PERSON><PERSON> patteri", "WATER_UNDERFLOOR_HEATING": "Vesikiertoinen latti<PERSON>ämm<PERSON>s", "OTHER": "<PERSON><PERSON>"}, "ventilationSystem": {"FORCED_EXHAUST": "Ilmanvaihtojärjestelmä", "FORCED_INTAKE": "<PERSON><PERSON><PERSON><PERSON> tulo", "GRAVITY_BASED": "<PERSON><PERSON><PERSON><PERSON>", "HEAT_RECOVERY": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "HEATING_AND_COOLING": "Lämmitys/viilennys"}, "premiseType": {"APARTMENT_SPECIFIC_STORAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> varasto", "ATTIC_STORAGE": "Vinttikomero", "BALCONY": "Parveke", "CELLAR_STORAGE": "Kellarikomero", "CRAFT_ROOM": "Ask<PERSON>lu<PERSON><PERSON>", "DRYING_ROOM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EMERGENCY_SHELTER": "Väestönsuoja", "LAUNDRY_ROOM": "Pyykkitupa", "MANGLE_ROOM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MOVABLE_PROPERTY_STORAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REFRIGERATED_CELLAR": "Viileäkellari", "SPORTS_EQUIPMENT_STORAGE": "Urheiluvälinevarasto", "SWIMMING_POOL": "Uima-allas", "OTHER": "<PERSON><PERSON>"}, "plotPropertyType": {"APARTMENT_HOUSE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HOLIDAY_PLOT": "Vapaa-<PERSON><PERSON>", "HOUSE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ROW_HOUSE_PLOT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "COMMERCIAL_OR_INDUSTRIAL_PLOT": "<PERSON><PERSON>- tai te<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "holdingType": {"OWN": "<PERSON><PERSON>", "LEASEHOLD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "parkingSpaceType": {"CARPORT_SPACE": "Autokatospaikka", "CARPORT_SPACE_WITH_ELECTRICAL_PLUG": "Autokatospaikka sähköpistokkeella", "CHARGING_POINT_FOR_ELECTRICAL_CARS": "Sähköauton la<PERSON>uspaikka", "GARAGE": "Autotalli", "OUTDOOR_PARKING_SPACE": "Ulkoautopaikka", "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG": "Ulkoautopaikka sähköpistokkeella", "PARKING_HALL_SPACE": "Autohallipaikka", "PARKING_SPACE_SHARE": "Autopaikkaosake"}, "housingCompanyPremiseType": {"APARTMENT": "<PERSON><PERSON><PERSON><PERSON>", "BUSINESS_PREMISE": "<PERSON><PERSON><PERSON><PERSON>", "OTHER_PREMISE": "<PERSON><PERSON> tila"}, "renovationStatus": {"PLANNED": "Suunnitteilla", "IN_PROGRESS": "K<PERSON>ynnissä", "FINISHED": "Val<PERSON>"}, "renovationType": {"KITCHEN": "Keittiö", "BATHROOM": "Kylpyhuone", "FLOOR": "La<PERSON>a", "PLUMBING": "Putkisto", "FACADE": "Julkisivu", "ROOF": "<PERSON><PERSON>", "BALCONY": "Parveke", "WINDOW": "Ikkuna", "LIFT": "<PERSON><PERSON>", "ELECTRICAL": "Sähkö", "SUBDRAINAGE": "Viemäröinti", "OTHER": "<PERSON><PERSON>"}, "housingCompanyInspectionType": {"CONDITION_ASSESSMENT": "<PERSON><PERSON><PERSON><PERSON>", "CONDITION_INSPECTION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONDITION_SURVEY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HUMIDITY_MEASUREMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MAINTENANCE_NEED_STATEMENT": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "LONG_TERM_PLAN": "Pitkän aikavälin suunnitelma", "HUMIDITY_INSPECTION": "Kosteustarkastus"}, "zoningType": {"CITY_PLAN": "<PERSON><PERSON><PERSON><PERSON>", "RURAL_AREA": "Maaseutualue", "UNZONED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COMMISSIONING_INSPECTION": "Käyttöönottotarkastuts", "FINAL_INSPECTION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "COMPONENT_MASTER_PLAN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEVIATION_DECISION": "Poikkeamispäätös", "DETAILED_PLAN": "Yksityiskoh<PERSON><PERSON> ka<PERSON>", "BUILDING_FORBID": "Rakennus<PERSON>lt<PERSON>", "BUILDING_PERMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DETAILED_SHORE_PLAN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SHORE_PLAN_AREA": "Rantakaava-alue", "AREA_REQUIRING_PLANNING": "Sunnittelutarvealue", "ACTION_BAN": "To<PERSON>npid<PERSON><PERSON><PERSON>", "MASTER_PLAN": "<PERSON><PERSON>isk<PERSON><PERSON>"}, "beachCode": {"NO_BEACH": "<PERSON>i rantaa", "SHARED_BEACH": "Y<PERSON><PERSON><PERSON><PERSON><PERSON>", "OWN_BEACH": "<PERSON><PERSON> ranta", "BEACH_RIGHT": "Ra<PERSON><PERSON><PERSON><PERSON>", "RIGHT_TO_WATER_AREA": "<PERSON><PERSON><PERSON>", "NEXT_TO_RELICTION_AREA": "<PERSON><PERSON><PERSON><PERSON>"}, "beachTypeCode": {"RIVER": "<PERSON><PERSON>", "LAKE": "<PERSON><PERSON><PERSON><PERSON>", "POND": "<PERSON><PERSON>", "SEA": "<PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "energyCertificateTypeCode": {"A_2007": "A (2007)", "B_2007": "B (2007)", "C_2007": "C (2007)", "D_2007": "D (2007)", "E_2007": "E (2007)", "F_2007": "F (2007)", "G_2007": "G (2007)", "A_2013": "A (2013)", "B_2013": "B (2013)", "C_2013": "C (2013)", "D_2013": "D (2013)", "E_2013": "E (2013)", "F_2013": "F (2013)", "G_2013": "G (2013)", "A_2018": "A (2018)", "B_2018": "B (2018)", "C_2018": "C (2018)", "D_2018": "D (2018)", "E_2018": "E (2018)", "F_2018": "F (2018)", "G_2018": "G (2018)", "H": "H", "NOT_AVAILABLE": "Ei sa<PERSON>villa", "NOT_REQUIRED": "<PERSON><PERSON>"}, "additionalRealtyDetailLinkTypeCode": {"VIRTUAL_PRESENTATION": "Virtua<PERSON><PERSON><PERSON><PERSON>", "VIDEO_PRESENTATION": "Videoesittely", "OTHER": "<PERSON><PERSON>"}, "administrationTypeCode": {"APARTMENT_HOUSING_COMPANY": "Kerrostaloyhtiö", "REAL_ESTATE_COMPANY": "Kiinteistönvälitysyritys", "PART_OWNERSHIP": "Osaomisteinen", "OTHER": "<PERSON><PERSON>"}, "livingType": {"title": "Asumistyyppi", "NONSUBSIDISED": "Ei tuettu", "INTEREST_SUBSIDIED": "Korkotuettu", "SHARED_APARTMENT": "Solu", "SUBTENANCY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SENIOR_HOUSE": "Seniorital<PERSON>", "SERVICE_HOUSE": "<PERSON><PERSON><PERSON><PERSON>", "EMPLOYMENT_BENEFIT_APARTMENT": "Työsuhdeasunto", "STUDENT_APARTMENT": "Opiskelija-asunto"}, "newBuilding": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NO": "<PERSON><PERSON>", "UNKNOWN": "Tuntematon", "YES": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "livingFloorCountCode": {"title": "Asuinker<PERSON>ten määrä", "SINGLE_FLOOR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TWO_FLOORS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FLOOR_AND_A_HALF": "<PERSON><PERSON><PERSON><PERSON>", "MORE_THAN_TWO_FLOORS": "<PERSON><PERSON> kaksi k<PERSON>ta", "NOT_KNOWN": "<PERSON><PERSON> tied<PERSON>"}, "available": {"title": "V<PERSON>ut<PERSON><PERSON>", "AVAILABLE": "Vapaa", "NEGOTIABLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>an", "DATE": "Päivämäärä", "RENTED": "Vuokrattu", "OTHER": "<PERSON><PERSON>", "description": "Lisätietoja vapautumisesta"}, "conditionCode": {"NEW": "<PERSON>us<PERSON>", "GOOD": "Hyvä", "SATISFACTORY": "Tyydyttävä", "TOLERABLE": "Välttävä", "UNCLASSIFIED": "<PERSON><PERSON> luo<PERSON>"}, "unbuiltPlot": {"true": "Yes", "false": "No", "null": "<PERSON><PERSON> tied<PERSON>", "TRUE": "K<PERSON><PERSON>ä", "FALSE": "<PERSON>i"}, "space": {"BATH_ROOM": "Kylpyhuone", "BEDROOM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DINING_ROOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DRAUGHT_LOBBY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HALL": "<PERSON><PERSON><PERSON>", "HALLWAY": "Käytävä", "KITCHEN": "Keittiö", "LIBRARY": "<PERSON><PERSON><PERSON><PERSON>", "LIVING_ROOM": "<PERSON><PERSON><PERSON><PERSON>", "LOFT": "<PERSON><PERSON><PERSON>", "SAUNA": "Sauna", "STUDY": "<PERSON><PERSON><PERSON><PERSON>", "TOILET": "WC", "UTILITY_ROOM": "<PERSON><PERSON><PERSON>itohu<PERSON>", "WALK_IN_CLOSET": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>", "TERRACE": "<PERSON><PERSON><PERSON>", "YARD": "Pi<PERSON>", "BALCONY": "Parveke", "STORAGE": "<PERSON><PERSON><PERSON>"}, "compassPoint": {"NORTH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EAST": "Itään", "SOUTH": "Etelään", "WEST": "Länteen", "NORTHEAST": "<PERSON><PERSON><PERSON><PERSON>", "SOUTHEAST": "Kaakkoon", "SOUTHWEST": "<PERSON><PERSON><PERSON><PERSON>", "NORTHWEST": "<PERSON><PERSON><PERSON><PERSON>"}, "balconyGlassMaintenanceResponsibility": {"HOUSING_COMPANY": "Taloyhtiö", "MAINTENANCE_COMPANY": "Huoltoyhtiö", "RESIDENTS": "Asukkaat"}, "basisForPossessionCodes": {"BELONGS_TO_APARTMENT_ACCORDING_TO_THE_ARTICLES_OF_ASSOCIATION": "<PERSON><PERSON><PERSON> huoneistoon yhtiöjärjestyksen mukaan", "RENTED_FROM_THE_COMPANY": "Yhtiöltä vuokrattu", "WITH_A_DIFFERENT_GROUP_OF_SHARES": "<PERSON><PERSON>"}, "basisForPossessionCodeYard": {"ARTICLES_OF_ASSOCIATION": "Yhtiöjärjestys", "NOT_KNOWN": "<PERSON><PERSON> tied<PERSON>", "OTHER": "<PERSON><PERSON>"}, "propertyStorageType": {"ATTIC": "<PERSON><PERSON><PERSON><PERSON>", "CELLAR": "<PERSON><PERSON><PERSON>", "OUTDOOR": "Ulkona", "REFRIGERATED_CELLAR": "Viileäkellari", "OTHER": "<PERSON><PERSON>"}, "transferCode": {"TRANSFERABLE": "Siirrettävissä", "NON_TRANSFERABLE": "<PERSON><PERSON> si<PERSON>", "NO_SELECTION": "<PERSON>i valintaa"}, "balconyType": {"FRENCH_WINDOW": "Ranskalainen parveke", "GLAZED": "Lasitettu parveke", "PROTRUDING": "Ulokeparveke", "RETRACTED": "Sisäänvedetty parveke", "OTHER": "<PERSON><PERSON> tyyppi"}, "damageType": {"WATER_DAMAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MOISTURE_DAMAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OTHER": "<PERSON><PERSON>"}, "featureCode": {"WC": "WC", "SHOWER": "<PERSON><PERSON><PERSON>", "TWO_SHOWERS": "<PERSON><PERSON><PERSON>", "SHOWER_WALL": "Suihkuseinä", "WALK_IN_SHOWER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WASHING_MACHINE_CONNECTION": "Pyykinpesukoneliitäntä", "FIXED_LAMPS": "Kiinteät valaisimet", "WASHING_MACHINE": "Pyykinpesukone", "TUMBLE_DRYER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DRYING_CABINET": "Kuivauskaappi", "MIRROR": "<PERSON><PERSON><PERSON>", "MIRROR_CABINET": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BASIN_CABINET": "Allaskaappi", "JACUZZI": "<PERSON><PERSON><PERSON>", "BATHTUB": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BATHROOM_CABINETS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT": "Jääkaap<PERSON> p<PERSON>", "REFRIGERATOR_FREEZER": "Jääkaappipakastin", "REFRIGERATOR": "Jääkaappi", "REFRIGERATED_CABINET": "Kylmiö", "COLD_ROOM": "Viileähuone", "FREEZER": "<PERSON><PERSON><PERSON>", "REFRIGERATOR_CHILLER": "Jääviileäkaappi", "DISHWASHER": "Astianpesukone", "DISHWASHER_CONNECTION": "Astianpesukoneliitäntä", "RESERVED_LOCATION_FOR_DISHWASHER": "<PERSON><PERSON><PERSON>", "INTEGRATED_DISHWASHER": "Integroitu astianpesukone", "FREE_STANDING_ISLANDS": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ova sa<PERSON>ke", "FREE_STANDING_CABINETS": "Vapaasti seisovat kaapit", "WINE_CABINET": "Viinikaappi", "INTEGRATED_HOUSEHOLD_APPLIANCES": "Integroidut kodinkoneet", "CERAMIC_STOVE": "<PERSON><PERSON><PERSON><PERSON> liesi", "HOB": "<PERSON><PERSON>", "BAKING_OVEN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COOKTOP": "<PERSON><PERSON><PERSON><PERSON>", "INTEGRATED_STOVE": "Integroitu liesi", "EXTRACTOR_HOOD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WOOD_BURNING_STOVE": "<PERSON><PERSON><PERSON><PERSON>", "ELECTRIC_STOVE": "Sähköliesi", "INDUCTION_STOVE": "Induktioliesi", "GAS_STOVE": "<PERSON><PERSON><PERSON><PERSON>", "MICROWAVE_OVEN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SEPARATE_OVEN": "<PERSON><PERSON><PERSON>", "EXTRACTOR_HOOD_WITH_FLUE": "<PERSON><PERSON><PERSON><PERSON><PERSON> hormilla", "COOKER_HOOD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONCRETE": "<PERSON><PERSON>", "STONE": "<PERSON><PERSON>", "WOOD": "<PERSON><PERSON><PERSON>", "LAMINATE": "<PERSON><PERSON><PERSON><PERSON>", "COMPOSITE": "Komposiitti", "METAL": "Metalli", "ROOM_WITH_FIREPLACE": "<PERSON><PERSON>", "ELECTRIC_HEATER": "Sähkölämmitteinen kiuas", "WOOD_HEATED_SAUNA_STOVE": "Puulämmitteinen kiuas", "ALWAYS_READY_HEATER": "<PERSON><PERSON> v<PERSON> -k<PERSON>as", "READY_FOR_ELECTRIC_HEATER": "<PERSON><PERSON><PERSON>", "INTEGRATED_BUCKET": "Integroitu löylyvesiastia", "WATER_FAUCET": "<PERSON><PERSON><PERSON>", "OPTICAL_FIBRE_LIGHTING": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LED_LIGHTING": "LED-valaistus", "WINDOW_OUT": "<PERSON><PERSON><PERSON><PERSON> ul<PERSON>", "FLOOR_MOUNTED_WC": "WC-is<PERSON>in", "WALL_HUNG_WC": "<PERSON><PERSON><PERSON><PERSON><PERSON> kiinn<PERSON> WC-istuin", "BIDET": "Bidee", "UNDERFLOOR_HEATING": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FLOOR_DRAIN": "Lattiakaivo", "LAUNDRY_CABINETS": "Pyykkikaapit", "TABLE_TOP": "<PERSON><PERSON><PERSON><PERSON>", "IRONING_TABLE_BOARD": "Silityslautalevy", "BABY_CHANGING_TABLE": "Hoitopöytä", "SINK": "<PERSON><PERSON><PERSON><PERSON>", "SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR": "<PERSON><PERSON><PERSON> ja viemäri ulko-oven lähellä", "CENTRAL_VACUUM_UNIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EXIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "messages": {"propertyTypeGroupEmpty": "Valitse ensin ilmoitustyyppi ja omistustyyppi", "propertyTypeEmpty": "Valitse ensin kiinteistötyyppiryhmä", "propertyCreatedWithId": "<PERSON><PERSON><PERSON> #{{id}} luotu", "canViewProperty": "<PERSON><PERSON> katsoa ja muokata kohdetta"}, "add": {"BATH_ROOM": "Lisää k<PERSON>py<PERSON>one", "BEDROOM": "Lisää makuuhuone", "DINING_ROOM": "Lisää ruo<PERSON>", "DRAUGHT_LOBBY": "Lisää tuulikaappi", "HALL": "Lisää eteinen", "HALLWAY": "Lisää käytävä", "KITCHEN": "Lisää keittiö", "LIBRARY": "Lisää kir<PERSON>to", "LIVING_ROOM": "Lisää <PERSON>", "LOFT": "Lisä<PERSON> parvi", "SAUNA": "Lisää sauna", "STUDY": "Lisää työhuone", "TOILET": "Lisää wc", "UTILITY_ROOM": "Lisää kodinhoitohuone", "WALK_IN_CLOSET": "Lisää vaatehuone", "OTHER": "Lisää muu", "TERRACE": "Lisää terassi", "YARD": "Lisää piha", "BALCONY": "Lisää parveke", "STORAGE": "Lisää varasto", "cost": "Lisää kustannus", "link": "<PERSON><PERSON><PERSON><PERSON>", "plot": "<PERSON><PERSON><PERSON><PERSON>", "damage": "Lisää v<PERSON>nko", "featureDescription": "Lisää ominaisuus"}, "instructions": {"commission": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.", "floor": "Rakennuks<PERSON> kerrosmäärä. Tämä arvo <PERSON> as<PERSON><PERSON> vain, jos rake<PERSON><PERSON> on use<PERSON> kerroksia.", "housingCompany": "Huomio: <PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>ön muutokset vaikuttavat myös kaikkiin siihen liittyviin kiinteist<PERSON>.", "housingCompanyCost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kustann<PERSON>", "cost": "Hallitse taloyhtiön kustannuksia siirtymällä “Taloyhtiö”-osiosta vali<PERSON>."}}, "event": {"upcoming": "Tulevat tapahtumat", "past": "<PERSON><PERSON><PERSON> tap<PERSON>", "actions": {"createEvent": "<PERSON><PERSON>"}, "enums": {"eventType": {"open_house_viewing": "Avoimien ovien esittely", "private_viewing": "<PERSON><PERSON><PERSON><PERSON> esittely", "client_meeting": "Asiakastapaaminen", "notary_meeting": "Notaaritapaaminen", "other": "<PERSON><PERSON>", "OPEN_HOUSE_VIEWING": "Yleisnäyttö", "PRIVATE_VIEWING": "Yksityisnäyttö", "CLIENT_MEETING": "Asiakastapaaminen", "NOTARY_MEETING": "Notaaritapaaminen", "OTHER": "<PERSON><PERSON>"}, "eventFormat": {"physical": "<PERSON><PERSON><PERSON><PERSON>", "virtual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hybrid": "<PERSON><PERSON>", "PHYSICAL": "<PERSON><PERSON><PERSON><PERSON>", "VIRTUAL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HYBRID": "<PERSON><PERSON>"}}, "fields": {"eventType": "Ta<PERSON>htumatyypp<PERSON>", "eventFormat": "<PERSON><PERSON><PERSON><PERSON> muoto", "startTime": "Al<PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "isAllDay": "<PERSON><PERSON>", "contacts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "messages": {"eventCreatedWithId": "Tapahtuma #{{id}} luotu", "eventUpdatedWithId": "Tapahtuma #{{id}} päivitetty", "canViewEvent": "<PERSON><PERSON> tarkastella ja muokata tapah<PERSON>aa"}, "createEvent": "<PERSON><PERSON>", "updateEvent": "Päivitä <PERSON>", "delete": {"title": "Poista tapah<PERSON>a", "description": "<PERSON><PERSON><PERSON>, että haluat poistaa tämän tapah<PERSON>?", "button": "Poista tämä tapahtuma", "toast": {"title": "Tapahtuma poistettu", "description": "<PERSON><PERSON><PERSON><PERSON> on poistettu onnistuneesti."}}}, "changesDetectedToast": {"title": "Julkaise muutokset", "description": "“Muutoksia havaittu! Päivittääksesi tämän kiinteistön portaaleissa, synkronoi nyt.”"}, "addDocuments": "Lisää <PERSON>", "addMedia": "Lisää media", "propertyStrandified": "Tämä kohde on on Strandified", "activateUser": {"button": "Aktivoi käyttäjä", "activatedUserTitle": "Käyttäjä on aktivoitu", "activatedUserDescription": "Käyttäjä aktivoitu onnistuneesti"}, "deactivateUser": {"button": "Deaktivoi käyttäjä", "deactivatedUserTitle": "Käyttäjä on deaktivoitu", "deactivatedUserDescription": "Käyttäjä deaktivoitu onnistuneesti"}, "sortDropdown": {"sortBy": "Järjestä", "recentlyCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "recentlyUpdated": "Viimeksi päiv<PERSON>tty", "lowestSalePrice": "<PERSON><PERSON><PERSON>", "highestSalePrice": "<PERSON><PERSON><PERSON>", "alphabeticallyAscending": "Aakkosjärjestyksessä <PERSON>", "alphabeticallyDescending": "Aakkosjärjestyksess<PERSON>"}, "viewing": "Näyttö", "contacts": "Kontaktit", "consenters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "groups": "<PERSON><PERSON><PERSON><PERSON>", "isRequired": "on pakollinen", "propertyValidation": {"showMore": "Näytä lisää", "showLess": "Näytä vähemmän", "missingInformationIssueTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tieto", "missingInformationIssueMessage": "Kohdetta ei voi julkaista ennen kuin nämä vaatimukset täyttyvät. Tarkista ilmoituksen tiedot ja päivitä tarvittaessa.", "missingInformationOkayTitle": "<PERSON><PERSON><PERSON>, kaikki valmista! 🎉", "missingInformationOkayMessage": "Tämä ilmoitus täyttää sisältövaatimukset.", "showDetails": "<PERSON><PERSON><PERSON><PERSON>", "hideDetails": "<PERSON><PERSON><PERSON>", "salesAgreementTitle": "<PERSON><PERSON> <PERSON>", "salesAgreementIssueMessage": "<PERSON><PERSON> <PERSON>oso<PERSON><PERSON> kohteelle ja varmista, että kaikki osapu<PERSON>t allekirjoittavat sen.", "salesAgreementOkayMessage": "<PERSON><PERSON><PERSON><PERSON> on allekirjoitettu toimeksiantosopimus. Ei havaittuja ongelmia.", "imageQualityTitle": "Lataa mediaa Strand-<PERSON><PERSON><PERSON> m<PERSON>", "imageQualityIssueMessage": "Lataa vähintään 10 kuvaa koh<PERSON><PERSON>, var<PERSON><PERSON>, että kuvat ovat korkealaatuisia ja sisällytä kohteen poh<PERSON>piir<PERSON>.", "imageQualityOkayMessage": "<PERSON><PERSON><PERSON><PERSON> on kuvia ja ne tä<PERSON>tävät Strand-ohjeet. Ei havaittuja ongelmia.", "missingFieldsTitle": "Täytä kaikki vaaditut il<PERSON>ustiedot", "missingFieldsIssueMessage": "Jotkut portaalien julkaisemiseksi vaaditut kentät puuttuvat. Täytä mahdollisimman paljon tietoja kohteelle. Napsauta \"Näytä tiedot\" näyttääksesi puuttuvat kentät", "missingFieldsOkayMessage": "<PERSON><PERSON><PERSON><PERSON> on kaikki vaaditut kentät täytetty. Ei havaittuja ongelmia."}, "agreementDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>ot", "back": "<PERSON><PERSON><PERSON>", "nextStep": "<PERSON><PERSON><PERSON> askel", "saveAndDownload": "<PERSON><PERSON>na ja lataa", "offer": {"title": "Tar<PERSON><PERSON>", "create": "<PERSON><PERSON> vä<PERSON>starjous", "contactDetail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editContact": "Muokkaa kontaktia", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "buyers": "<PERSON><PERSON><PERSON><PERSON>", "realtors": "Välittäjät", "reviewPropertyDetails": "Tarkista koh<PERSON> tiedot", "editProperty": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "agreementReady": "<PERSON><PERSON><PERSON><PERSON><PERSON> on nyt valmis!", "saveAndDownloadContractBelow": "Tallenna ja lataa sopimus alla.", "createdOffer": "<PERSON><PERSON><PERSON><PERSON> luotu", "createSuccessful": "Tarjous {{offerId}} luotu onnist<PERSON><PERSON>i", "deleteOffer": "Poista tarjous", "deleteSuccessful": "<PERSON><PERSON><PERSON><PERSON> poistettu onnistuneesti", "deletedFail": "Tarjousta ei voitu poistaa. <PERSON>rit<PERSON> my<PERSON> u<PERSON>en", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "rejectedSuccessful": "<PERSON><PERSON><PERSON> lähetetään hylkäysviesti.", "rejectedFail": "Tarjousta ei voitu hylätä. Yritä myöhemmin uudelleen", "status": {"created": "<PERSON><PERSON><PERSON>", "pending": "Odottaa", "declined": "<PERSON><PERSON><PERSON><PERSON>", "signed": "Allekirjoitettu"}, "offered": "Tarjottu"}, "sellingOffer": {"title": "<PERSON><PERSON>", "address": "Osoite", "price": "<PERSON><PERSON>", "commission": "Komissio %", "note": "<PERSON><PERSON>", "realtor": "Kohteen välittäjä", "noteTooLong": "Huomio saa olla enintään {{characterLimit}} merkkiä pitkä", "pdfError": "PDF-virhe", "errorLoadingTemplate": "Ei voida ladata pdf-mallia", "errorFileType": "Tiedostotyyppi ei tueta", "errorCreatePDF": "Ei voida luoda pdf-tied<PERSON><PERSON>", "errorLoadingPhoto": "Ei voida ladata kuvaa", "pdfSuccess": "PDF luotu", "pdfSuccessMessage": "Myyntitarjous PDF luotu onnistuneesti"}, "brokerageOffer": {"title": "<PERSON><PERSON> vä<PERSON>starjous", "address": "<PERSON><PERSON>eist<PERSON><PERSON> o<PERSON>ite", "priceInquiry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceInquiryError": "Hinta ei voi olla negatiivinen", "commission": "Komissio %", "commissionError": "Komission tulee olla vähintään 0 ja enintään 100", "realtor": "Välittäjä", "realtorDescription": "Välittäjän kuvaus", "realtorDescriptionDefault": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> kii<PERSON><PERSON><PERSON><PERSON><PERSON>st<PERSON>, j<PERSON> p<PERSON> on sinun asuntosi. <PERSON><PERSON><PERSON> tuke<PERSON> on Strand Properties -myyntikonsepti, joka ma<PERSON>do<PERSON><PERSON><PERSON> asunnon turvallisen ja nopeamman myynnin sekä korkeamman myynti<PERSON>nan. <PERSON><PERSON> into<PERSON><PERSON><PERSON> on asunto<PERSON>n tulo<PERSON> myynti; jolloin sinä voit keskittyä muuhun.", "realtorDescriptionTooLong": "Välittäjän kuvaus saa olla enintään {{characterLimit}} merkkiä pitkä", "notes": "Lisätiedot kiinteistöstä", "notesTooLong": "Lisätiedot saa olla enintään {{characterLimit}} merkkiä pitkä", "pdfError": "PDF-virhe", "errorCreatePDF": "Ei voida luoda PDF-tiedostoa", "pdfSuccess": "PDF luotu", "pdfSuccessMessage": "Välitystarjous PDF luotu onnistuneesti", "emailSuccess": "PDF lähetetty", "emailSuccessMessage": "Välitystarjous lähetetty sähköpostitse onnistuneesti", "generatePDF": "Luo PDF", "generating": "<PERSON><PERSON><PERSON>...", "sharingType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shareWith": "<PERSON><PERSON><PERSON>", "recipientRequired": "Vähintään yksi vastaan<PERSON>aja vaaditaan", "contact": "Yhteystieto", "missingFieldsPrefix": "Täytä puuttuvat tiedot profiiliisi:", "missingPhone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missingEmail": "Sähköposti", "missingJobTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missingCompanyName": "<PERSON><PERSON><PERSON><PERSON> nimi", "missingCompanyId": "Yrityksen ID", "enums": {"sharingType": {"print": "<PERSON><PERSON><PERSON>", "email": "Sähköposti"}}, "realtorNotAvailable": "Välittäjän tietoja ei saatavilla"}, "terms": "<PERSON><PERSON><PERSON><PERSON>", "depositDetail": "<PERSON><PERSON><PERSON><PERSON> tiedot", "buyers": "<PERSON><PERSON><PERSON><PERSON>", "agreeSalePrice": "<PERSON><PERSON><PERSON><PERSON>", "specialCondition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reservationDepositAmount": "Varausmaksun mä<PERSON>ä", "depositPayee": "<PERSON><PERSON><PERSON><PERSON> saaja", "depositPaymentWillBePaidTo": "<PERSON><PERSON>", "standPropertiesSL": "Strand Properties SL", "IBANNumber": "IBAN-numero", "purchasePriceShallBeSettledBy": "<PERSON><PERSON><PERSON><PERSON>", "privatePurchaseContractDueDate": "Yksityisen ostosopimuksen eräpäivä", "typeHere": "<PERSON><PERSON><PERSON><PERSON>...", "review": "<PERSON><PERSON><PERSON><PERSON>", "shortTermRent": "Lyhytaikainen vuokra", "plusSomeOther_one": "+ {{number}} muu", "plusSomeOther_other": "+ {{number}} muuta", "offers": "Tarjoukset", "documentId": "Tiedoston ID", "soldBy": "<PERSON><PERSON><PERSON>", "soldByStrand": "Strand Properties", "soldByOther": "Joku muu", "signingMethods": {"title": "Allekirjoitusmenetelmä", "penAndPaper": "<PERSON><PERSON><PERSON> ja paperi", "eSignature": "Sähköinen allekirjoitus", "eSignatureComingSoon": "Sähk<PERSON>inen allekirjoitus (tulossa pian)"}, "matchMaking": {"title": "Allekirjoitusmenetelmä", "start": "Aloita matchmaking", "success": "O<PERSON>lun etsintä aloitettiin.", "linked": "<PERSON><PERSON><PERSON>", "leadTicket": "<PERSON><PERSON><PERSON> lippu", "shortList": "Lyhyt lista", "topMatch": "<PERSON><PERSON><PERSON><PERSON> vast<PERSON>vat omina<PERSON>u<PERSON>t", "matchMakingPropertyNotUpdated": "Matchmaking-ominaisuutta ei päivitetty. <PERSON><PERSON><PERSON> uudelleen my<PERSON>", "showDisliked": "Näytä hylät<PERSON>", "shareShortlist": "Jaa lyhyt lista", "searchCriteria": "<PERSON><PERSON>", "criteria": "kriteerit", "priceRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listingTypes": "Luettelotyypit", "sizeRange": "Koko-alue", "canViewEdit": "voit katsella ja muokata sitä.", "emailTemplates": "Sähköpostimallit", "autoSent": "Automaattinen sähköpostin lähetys", "editSuccess": "Matchmaking muokattu", "updatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti"}, "missionInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON> tieto", "pleaseReviewAndAdjustIfNeed": "Tarkista alla olevat tyhj<PERSON> tiedot ja muuta niitä tarvitta<PERSON>a.", "videoAndTour": "Video ja 3D-esittely", "images": "<PERSON><PERSON>", "uploadImages": "Lataa kuvia", "addVideo": "Lisää video", "addTour": "Lisää 3D-esittely", "videoUrl": "Videon URL", "tourUrl": "3D-esittelyn URL", "videoAddedTitle": "Video lisätty!", "tourAddedTitle": "3D-esittely l<PERSON>!", "videoAddedDescription": "Video lisätty onnistuneesti", "tourAddedDescription": "3D-es<PERSON><PERSON> l<PERSON><PERSON>nistuneesti", "companyTypes": {"NOT_APPLICABLE": "<PERSON><PERSON>", "LIGHT_ENTREPRENEUR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (TMI/Autónomo)", "COMPANY_SL": "Yritys (OY/SL)"}, "downloading": "Ladataan...", "downloadPDF": "Lataa PDF", "leadSource": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allocate": "Kohdista", "reallocate": "<PERSON><PERSON><PERSON> uudelleen", "content": "Sisältö", "contact": "Yhteystieto", "contactPerson": "Yhteyshenkilö", "processed": "Käsitelty", "unprocessed": "Käsittelemätön", "leadLocated": {"leads": "Liidit", "title": "Kohdista liidi", "successTitle": "<PERSON><PERSON><PERSON> k<PERSON>", "successContent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on ilmoitettu ja myyntitoiminto luotu."}, "leadDiscarded": {"confirmDiscard": "Discard this manual lead", "confirmUndoDiscard": "<PERSON><PERSON> discard this manual lead", "discardSuccessTitle": "Manual lead has changed status", "discardSuccess": "Manual lead has been discarded", "undoSuccess": "Manual lead has been undo discarded"}, "select": "Valitse", "type": "Tyyppi", "socialSecurityNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessId": "Y-tunnus", "vatNumber": "ALV-numero", "bank": "<PERSON><PERSON><PERSON>", "iban": "IBAN", "bic": "BIC", "invoice": "<PERSON><PERSON>", "offerAgreedDate": "Tarjouksen hyväksynnän päivämäärä", "salePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer": "Tarkastaja", "totalCommission": "<PERSON><PERSON> k<PERSON>", "strandCommission": "Strand-komissio", "agent": "Välittäjä", "attachmentFile": "Lisää <PERSON>", "createDoS": {"sentForReview": "Lähetä hyväksyttäväksi", "sendForReviewDescription": "<PERSON><PERSON><PERSON><PERSON>"}, "editDoS": {"sentForReview": "Lähetä hyväksyttäväksi"}, "sendForReview": "Lähetä hyväksyttäväksi", "saveAsDraft": "<PERSON><PERSON><PERSON> l<PERSON>", "developmentName": "<PERSON><PERSON><PERSON> nimi", "Agent": "Välittäjä", "remove": "Poista", "commissionSplitInHouse": "Komission jako <PERSON>", "addAgent": "Lisää välittäjä", "existProperty": "Valitse kohde listalta", "customReferenceNumber": "<PERSON><PERSON>", "strandPropertiesOfferSigned": "<PERSON><PERSON><PERSON><PERSON>", "depositPaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>u", "dateTheDepositPaidToSeller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maksettu <PERSON>", "dateOfPPC": "Yksityisen ostosopimuksen päivämäärä", "deadlineOfTheCompletionNotary": "<PERSON><PERSON><PERSON>", "notaryDayBooked": "<PERSON><PERSON><PERSON> aika varattu", "otherAgencyName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toimiston nimi", "otherAgencyCommission": "Ulk<PERSON><PERSON><PERSON><PERSON> toim<PERSON>on komissio", "externalLead": "Ulkopuolinen liidi", "attachmentType": {"AGENT_INVOICE": "<PERSON>ä<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadFile": "Lataa", "addAttachment": "Lisää", "createOneInvoice": "<PERSON>o yksi lasku", "createInvoiceForEachSeller": "<PERSON><PERSON> j<PERSON> oma lasku", "sellerSplit": "<PERSON><PERSON><PERSON> o<PERSON>", "contractReadyForReview": "Odottaa tarkistusta", "theContractWillSendForSellerForReview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noteToReviewer": "<PERSON><PERSON><PERSON><PERSON>", "writeYourNote": "Lisää huomiot", "noAdminAtThatOffice": "No admin at that office", "Ticket": "Aktiviteetti", "Contact": "Kontakti", "IBI": "IBI", "orientations": "Ilmansuunta", "role": "<PERSON><PERSON><PERSON>", "users": "Käyttäjä", "aiSearch": "AI haku", "notaryDate": "Notary date", "download": "Download", "proforma": "<PERSON><PERSON><PERSON>", "view": "View", "associatedRecords": "Associated Records", "contractDetails": "Contract details", "depositPaidTo": "Deposit paid to", "agentInvoices": "Agent invoices", "sellerInvoices": "Seller invoices", "detailsOfSale": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "files": "Files", "status": {"CANCELLED": "Cancelled", "IN_REVIEW": "In review", "DRAFT": "Draft", "APPROVED": "Approved", "SIGNED": "Signed", "REQUESTING_CHANGES": "Requesting changes"}, "edit": "<PERSON><PERSON><PERSON><PERSON>"}, "CONTRACT_TYPE": {"OFFER": "Offer", "SALE_AGREEMENT": "Sale agreement", "DETAILS_OF_SALE": "Details of sale"}, "downloadAll": "Download all", "downloadDocumentFailed": "Download failed. Please try again later", "requestChanges": "Request changes", "approve": "Approve", "uploaded": "Uploaded", "moreUser": "+ {{users}} more", "file": "Files", "agentInvoice": "Agent invoice", "approveDoS": "Approve Details of Sale", "dates": "Dates", "invoiceIssuedBy": "Invoice issued by", "invoices": "Invoices", "agentNameOnTheInvoice": "Agent name on the invoice", "invoiceDate": "Invoice date", "invoiceDueDate": "Invoice due date", "split": "Split", "reviewInvoiceDetails": "Review invoice details", "invoicer": "Invoicer", "readyForApproveDoS": {"yourDoSAreReadyForApproval": "Your Details of Sale are ready for approval!", "approveAndCreateInvoiceBelow": "Approve and create invoices bellow", "approveAndCreateInvoices": "Approve and create invoices"}, "invoiceNumber": "Invoice number", "invoiceAmount": "Invoice amount", "agentName": "Agent name", "approveDos": {"approved": "Invoice created", "sendForReviewDescription": "Approved and create invoice successfully"}, "requestChangesDos": {"changingRequested": "Request changes successfully", "requestChangeDescription": "Realtors will be notified for the changes by email"}, "note_simple_pdf": "Nota simple PDF", "passport_pdf": "Passport PDF", "reservation_agreement_signed_pdf": "Reservation agreement signed PDF", "nie_dni": "NIE/DNI", "copySellerPassport": "Copy of the sellers passport", "sale_agreement_signed": "Reservation agreement signed PDF", "copyIdentificationDocument": "Copy of the identification documents.", "proof_of_transfer_deposit": "Proof of transfer deposit", "saleAgreementSigned": "Sale agreement signed", "signedSaleAgreementIsAutomaticallyAddedHere": "The signed sale agreement is automatically added here, if not please add it below.", "ibi_receipt": "IBI receipt", "basura_receipt": "Basura receipt", "copy_of_the_title_deed": "Copy of the title deed", "seller_invoices": "Seller Invoice", "agent_invoices": "Agent Invoice", "invoiceOption": "Invoice option", "detailOfSaleNotEdited": "Details of Sale not updated. Please try again later", "shareShortlist": {"title": "Jaa lyhyt lista", "description": "T<PERSON>ssä on link<PERSON>j<PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> olet valinnut ly<PERSON>en listan. Voit vapaasti kopioida tekstin ja jakaa sen.", "placeholder": "Lyhyt lista", "copyToClipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "copyAsPlainText": "Kopioi tekstiksi"}, "duplicateInvoiceNumber": "This invoice number has already exists", "contactNotAssignedToRealtor": "The seller contact is not assigned to realtor. Please check with the office Admins.", "accessWasDenied": "Access was denied", "documentLanguage": "Document language", "organization": "Organization", "extra_dos": "Additions attachments", "uploadFiles": "Lisää <PERSON>", "discard": "Discard", "discarded": "Discarded", "undoDiscard": "Undo discard", "companyReference": "Company reference", "insuranceNoAndCompany": "Insurance No & Company", "material": "Material", "confirm": "Confirm", "instructions": "<PERSON><PERSON><PERSON>", "translations": {"es": "Espanjan<PERSON><PERSON>n k<PERSON>", "fi": "Suomenkielinen kuvaus", "en": "Englan<PERSON><PERSON><PERSON><PERSON> ku<PERSON>", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "sv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kuva<PERSON>"}, "map": "Map", "kiviImport": {"button": "Import from Kivi", "title": "Import from Kivi", "refNumber": "Reference number", "inputRefNumber": "Input reference number", "success": "Import successful", "error": "Import failed", "refNumberRequired": "Reference number is required"}, "selectDistrict": "Valitse <PERSON>", "selectCity": "Valitse ka<PERSON>unki", "doSReport": {"totalNumberOfTransactions": "Total number of transactions", "totalSalesPrice": "Total sales price", "totalCommission": "Total commission", "totalCommissionPercentage": "Total commission as % of total sales price", "totalStrandFullCommission": "Total strand full commission", "strandFullCommissionPercentage": "Strand full commission as % of total sales price", "totalStrandCommissionEarned": "Total strand commission earned", "totalAgentCommissionEarned": "Total agent commission earned", "strandCommissionPercentage": "Strand commission as % of strand full commission", "strandOfFullCommissionPercentage": "Strand commission as % of full commission", "subTotalNumberOfTransactions": "count of transactions", "subTotalSalesPrice": "sum of sales price", "subTotalCommission": "net of VAT", "subTotalStrandFullCommission": "net of VAT", "subStrandFullCommissionPercentage": "net of VAT", "subTotalStrandCommissionEarned": "net of VAT", "transactionReference": "Transaction reference", "propertyDescription": "Property description", "salesPrice": "Sales price", "strandCommissionEarned": "Strand commission earned \n(net of VAT)", "agentCommissionEarned": "Agent commission earned \n(net of VAT)"}, "fromTime": "From time", "toTime": "To time", "transactions": "Transactions", "attachment": "Liitetiedostot", "previousPeriod": "Previous period", "samePeriodLastYear": "Same period\nlast year", "currentValue": "Current value", "exportSheetFile": {"translations": "Translations", "importFile": "Import file", "exportFile": "Export file", "selectLanguageToImportExport": "Select languages", "exportFileNewKeys": "Export new keys"}, "archiveProperty": {"label": "Archive", "confirmTitle": "Archive property confirmation", "confirmMessage": "Are you sure you want to archive this property? This action will update the property status and trigger to the website hook.", "confirmSuccess": "Property archived successfully", "confirmSuccessMessage": "The property has been archived successfully. The website hook has been triggered.", "confirmError": "Failed to archive property"}, "roaiib": "ROAIIB", "liabilityInsurance": "Liability Insurance", "language": "<PERSON><PERSON>", "person": "Henkilö", "error": "<PERSON><PERSON><PERSON>", "history": "Historia", "by": "By", "loadMore": "Lataa lisää", "searchByNameEmailOrNumber": "<PERSON><PERSON>, sähköpostilla tai puhelinnumerolla", "noSearchResults": "<PERSON>i haku<PERSON>...", "createNew": "<PERSON><PERSON>i", "advertisement": {"sidebar": {"active": "Aktiiviset mainokset", "drafts": "Luonnokset", "completed": "Valmiit <PERSON>"}, "details": {"focus": "<PERSON><PERSON><PERSON>", "type": "Mainostyyppi", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agent": "<PERSON><PERSON>", "event": "Tapahtuma", "managedBy": "Ylläpitäjä", "details": "<PERSON><PERSON><PERSON> tiedot", "date": "Päivämäärä", "targetArea": "<PERSON><PERSON><PERSON><PERSON>", "budget": "<PERSON><PERSON><PERSON>", "perday": "päivässä", "language": "<PERSON><PERSON>", "metrics": "<PERSON><PERSON><PERSON><PERSON>", "impressions": "Vaikutelmia", "cpm": "Kustannukset promillea (CPM)", "linkClicks": "<PERSON><PERSON>", "ctr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CTR)", "cpc": "Hinta per linkin napsautus (CPC)", "placeholderAlert": "Todellinen mainoksen esikatselu voi vaih<PERSON>a al<PERSON>, la<PERSON>en ja näyttöasetusten mukaan.", "helpText": "Täältä näet yhteenvedon mainonnasta, jon<PERSON> v<PERSON> on tehnyt kohteellesi. Voit seurata mainonnan tuloksia tältä sivulta. <PERSON><PERSON><PERSON><PERSON>, että tiedot päivittyvät pienellä viiveellä.", "metricsDescription": "unknown"}, "copied": "<PERSON>pied", "contact": "Yhteyshenkilö", "preview": {"alert": {"title": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>a", "text": "Tämä sivu sisältää tietoa, jonka voi turvallisesti jakaa asiakkaallesi. V<PERSON> mä<PERSON>ä<PERSON>, mitk<PERSON> osiot näytetään jaettavassa julkisessa linkissä."}, "overview": "Mainonnan yleiskatsaus", "focus": "PAINOPISTE", "property": "<PERSON><PERSON><PERSON>", "adType": "Mainostyyppi", "event": "Tapahtuma", "details": "TIEDOT", "display": "Näyttö", "date": "Päivämäärä", "start": "Alku", "end": "Loppu", "targetArea": "<PERSON><PERSON><PERSON><PERSON>", "metrics": "MITTARIT", "impressions": "Näyttökerrat", "linkClicks": "<PERSON><PERSON>", "ctr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CTR)", "preview": "Esikatselu"}}, "clientType": "Asiakastyyppi", "noActivityLogged": "<PERSON>i toim<PERSON>aa", "downloadAllMedia": "Lataa kaikki media", "companyInfo": "<PERSON><PERSON><PERSON><PERSON>", "otherAgencyCommissionAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> muun toimiston komissio", "Copy to clipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "Copy": "Ko<PERSON>i", "estateInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "add": "Lisää", "NIE / ID": "NIE/DNI", "subtotal": "Alennettu", "IVA / VAT": "ALV", "total": "Yhteensä", "nieAttachments": "NIE liitetiedostot", "chooseFile": "Valitse tiedosto", "formik": {"errors": {"fieldRequired": "Kent<PERSON><PERSON> on pakollinen", "mustBeLaterThanField2": "Kenttä 2 on pakollinen", "maxCharacters": "<PERSON><PERSON><PERSON><PERSON>", "AtLeastOneFieldMustBeSelected": "Vähintään yksi kenttä on pakollinen", "title": "<PERSON><PERSON><PERSON><PERSON>", "eventNotCreated": "Tapahtuma ei luotu", "eventNotUpdated": "Tapahtuma ei päivitetty"}}, "addExisting": "Lisää olemassa oleva", "propertyIsRented": "<PERSON><PERSON><PERSON> on vuokrattu", "Map": "<PERSON><PERSON><PERSON>", "floors": "<PERSON><PERSON><PERSON>", "formErrors": "Form errors", "id": "ID", "copySignatureLink": "<PERSON><PERSON><PERSON>", "Price": "<PERSON><PERSON>", "translateTo": "Käännä kielelle", "moreUsers": "Lisää käyttäjä", "Select areas": "Val<PERSON><PERSON> al<PERSON>et", "isExclusive": "<PERSON><PERSON>", "lastSigningDate": "Viimeinen allekirjoituspäivä", "comment": "<PERSON><PERSON><PERSON><PERSON>", "realtorUserIds": "Välittäjän ID:t", "contactIds": "Kontaktit<PERSON><PERSON>", "Send Email": "Lähetä sähköposti", "sendAllContactsEmail": "Lähetä kaikille kontakteille", "Assign": "Määritä", "documentTemplate": {"LOCKED": {"buttonContent": "Yritys (OY/SL)", "title": "<PERSON><PERSON>s", "content": "<PERSON><PERSON>s"}, "SIGNED": {"buttonContent": "Allekirjoitettu", "title": "Yritysty<PERSON>ppi"}, "SUCCESSFUL": {"title": "Onnistunut", "content": "Onnistunut"}, "READY_SIGN": {"title": "<PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "content": "<PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "buttonContent": "<PERSON><PERSON><PERSON><PERSON>"}, "CANCEL": {"title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>köposti", "content": "<PERSON><PERSON><PERSON><PERSON>", "buttonContent": "<PERSON><PERSON>ksen/laskutus<PERSON>t"}}, "katu osoite tässä hei": "katu osoite tässä hei", "validation": {"typeRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "sourceRequired": "Lä<PERSON>de on pakollinen", "addressRequired": "Osoite on pakollinen", "firstNameRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "lastNameRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "estateNameRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "companyNameRequired": "<PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "businessIdRequired": "Y-tunnus on pakollinen"}, "contactPersoninfo": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "missingName": "<PERSON><PERSON> pu<PERSON>uu", "user": "Käyttäjä", "userSelector": {"main": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON>, sähköpostilla tai puhelinnumerolla...", "realtor": "Välittäjä"}, "previous": "<PERSON><PERSON><PERSON>", "start": "Alku", "end": "Loppu", "End": "Loppu", "Start": "Alku", "optional": "<PERSON><PERSON><PERSON><PERSON>", "expense": {"setupFailed": "<PERSON><PERSON><PERSON> e<PERSON>", "setupFailedDesc": "<PERSON><PERSON><PERSON> e<PERSON>", "setupSuccess": "<PERSON><PERSON><PERSON>", "setupSuccessDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> on lisätty onnistuneesti.", "setupPaymentTitle": "Lisää maksutapa", "transactionCreated": "Tapahtuma luotu", "transactionCreatedDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on tallennettu onnistuneesti.", "transactionFailedDesc": "Tapaht<PERSON> virhe.", "transactionFailed": "Tapahtuma epä<PERSON>nist<PERSON>"}, "search": "Hae", "reason": "Syy", "amount": "Määrä", "connection": "<PERSON><PERSON><PERSON><PERSON>", "fiDetailsOfSale": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previewDetailsOfSale": "Tarkista ja lukitse ka<PERSON>vitys", "form": {"fields": {"propertyId": "Omaisuuden ID", "ownershipType": "Määritä omistus", "transactionMethod": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>nettely", "propertyPublishedAt": "<PERSON><PERSON><PERSON>", "estimatedTransactionDate": "Ostotar<PERSON>s h<PERSON>", "saleDurationDays": "<PERSON><PERSON><PERSON><PERSON>", "offerCount": "Tarjouks<PERSON> m<PERSON>", "highestRejectedOffer": "<PERSON><PERSON><PERSON> h<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debtFreePrice": "<PERSON><PERSON><PERSON>", "mortgageBank": "Lainapank<PERSON>", "notes": "Muistiinpano", "commissionAmountTotal": "Kokonaisprovisiomäärä", "commissionPercent": "Palkkio %", "commissionVatPercent": "ALV-prosent<PERSON>", "commissionVatIncluded": "<PERSON><PERSON><PERSON><PERSON>", "commissionAmountWithoutVat": "Veroton", "commissionVatAmount": "Vero", "commissionAmountWithVat": "Verollinen palk<PERSON>o", "ownershipSharePercent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recipient": "Välittäjä", "removeRecipient": "Poista", "role": "<PERSON><PERSON><PERSON>", "sum": "Summa", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownershipSharePercentMax": "Omist<PERSON><PERSON><PERSON><PERSON> prosentti ei voi olla suurempi kuin 100", "ownershipSharePercentRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pak<PERSON>inen", "sumOfPercentages": "Prosenttiosuuksien summa ei voi olla suurempi kuin 100", "propertyPublishedAtGreaterThan": "Ostotarjous hyväksytty ei voi olla ennen kohteen julkai<PERSON>a", "commissionPercentMax": "Provisiomäärä ei voi olla suurempi kuin 100", "commissionPercentRequired": "Provisiomäärä on pakollinen", "estimatedTransactionDateRequired": "Ostotarjous hyväksytty on pakollinen", "ownershipTypeRequired": "Oma<PERSON><PERSON><PERSON>n tyyppi on pakollinen", "transactionMethodRequired": "Kaupankäyntimenettely on pakollinen", "debt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saleFinalDate": "Kaupanteko viimeistään", "strandCommissionPercent": "<PERSON><PERSON> o<PERSON> p<PERSON>a ja kuluista (%)", "strandCommissionAmount": "<PERSON><PERSON> o<PERSON> p<PERSON>a ja kuluista", "leadBasis": "Lead-<PERSON><PERSON><PERSON><PERSON>", "leadCommission": "Lead-<PERSON><PERSON><PERSON><PERSON>", "totalCommissionAndExpenses": "Palkkio + muut kulut (summa)", "endDateGreaterThanStartDate": "Päättymispäivä ei voi olla ennen alkamispäivää"}, "enums": {"ownershipType": {"percentage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fraction": "Murtoluku"}, "transactionMethod": {"traditional": "<PERSON><PERSON><PERSON><PERSON>", "dias": "DIAS"}, "recipientRole": {"selling": "<PERSON><PERSON><PERSON>", "brokerage_firm": "Välitystoimisto"}, "recipientType": {"contact": "Yhteyshenkilö", "user": "Käyttäjä"}, "leadBasis": {"total_commission": "Kokonaispalkkiosta", "realtor_commission": "Välittäjän palkkiosta"}}, "headers": {"saleParticipants": "<PERSON><PERSON><PERSON>", "reward": "<PERSON><PERSON><PERSON><PERSON>", "sellers": "<PERSON><PERSON><PERSON><PERSON>", "buyers": "<PERSON><PERSON><PERSON><PERSON>", "saleDetails": "<PERSON><PERSON><PERSON>", "rewardShare": "Välittäjien o<PERSON>us kokonaispalkkiosta", "strandCommissionHeader": "<PERSON><PERSON> o<PERSON> k<PERSON>", "leads": "Lead-<PERSON><PERSON><PERSON><PERSON>", "rewardShareDescription": "Välittäjien osuus kokonaispalkkiosta:", "rewardDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan palk<PERSON>o perustuu", "rewardBasedOnDebtFreePurchasePrice": "<PERSON><PERSON><PERSON><PERSON>", "rewardBasedOnPurchasePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "validation": {"errorToast": "<PERSON><PERSON><PERSON>", "createErrorToast": "<PERSON><PERSON><PERSON> luo<PERSON> ka<PERSON>", "editErrorToast": "<PERSON><PERSON><PERSON> muokat<PERSON>a <PERSON>"}, "sellers": "<PERSON><PERSON><PERSON><PERSON>"}, "noDetailsOfSale": "Kaupanselvitystä ei l<PERSON>", "noDetailsOfSaleDescription": "Kaupanselvitystä ei l<PERSON>", "create": "<PERSON><PERSON> uusi ka<PERSON>", "addSeller": "Lisää my<PERSON>jä", "addBuyer": "Lisää ostaja", "addRecipient": "Lisää välittäjä", "created": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": {"totalPercentageExceeded": "Prosenttiosuuksien summan pitää olla 100", "userIdRequired": "Käyttäjä on pakollinen", "roleRequired": "<PERSON><PERSON><PERSON> on pakollinen"}, "previewDetailsOfSaleDescription": "<PERSON><PERSON><PERSON><PERSON> pakoll<PERSON>t tiedot, niin voit siirtyä se<PERSON><PERSON>an vai<PERSON>seen ja lukita ka<PERSON><PERSON>.", "steps": {"fill": "Täyttäminen", "check": "Tarkistaminen"}, "previewHeading": "Kaupanselvityksen tarkastaminen", "previewDescription": "Tark<PERSON> ka<PERSON><PERSON><PERSON><PERSON> tiedot ja lukitse se<PERSON><PERSON>an vai<PERSON>n", "checkAndLock": "Tarkista ja lukitse ka<PERSON>vitys", "checkAndLockButton": "Lukitse ka<PERSON>elvitys", "unlockButton": "<PERSON><PERSON> lukitus", "commissionDistribution": "<PERSON><PERSON><PERSON><PERSON>jak<PERSON>", "commission": "<PERSON><PERSON><PERSON><PERSON>", "addExpense": "Lisää kulu", "expenses": "<PERSON><PERSON> my<PERSON>n vastuulla olevat kulut", "vat": "Alv", "net": "Veroton", "tax": "Vero", "total": "Yhteensä", "otherExpensesTotal": "<PERSON><PERSON> kulut yhteen<PERSON>ä", "addLead": "Lisää lead-p<PERSON><PERSON><PERSON>", "summary": {"total": "Välityspalkkio + muut kulut yhteensä veroineen", "net": "Veroton y<PERSON>ä", "tax": "Verot yhteensä", "otherExpenses": "+ muut myyjän vastuulla olevat kulut {{expensesTotal}} € veroineen", "hideExpenses": "<PERSON><PERSON><PERSON> muut kulut", "showExpenses": "Näytä muut kulut", "expenseName": "<PERSON><PERSON>", "expenseAmount": "Määrä", "expenseVat": "Alv", "commissionSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "failedToLoadPrefillData": "Kaupansel<PERSON><PERSON>en alustamiseksi tarvitaan ensin hyväksytty toimeksiantosopimus sekä osto<PERSON>."}, "delete": "Poista", "days": "päivää", "pieces": "kpl", "commissionSummary": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commission": "Palkkio {{percent}} % (sis. ALV {{vatPercent}} %)", "commissionWithoutVat": "Palkkio {{percent}} %", "role": "<PERSON><PERSON><PERSON>", "roundingCorrection": "Pyöristyksestä johtuva oikaisu", "includingVat": "sis. ALV {{vatPercentage}} %", "totalIncludingVat": "Summa (sis. alv {{vatPercent}}%)", "net": "Veroton", "tax": "Vero", "strandCommission": "Strandin osuus {{strandCommission}}%", "leadCommission": "Lead ({{name}}) {{leadCommission}}%", "realtorCommission": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> {{realtorCommissionPercent}}%"}, "documentLibrary": {"documentTypes": {"FI_ASBESTOS_SURVEY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_SHARE_CERTIFICATE": "Asunto-osakekirja", "FI_ENERGY_CERTIFICATE": "Ener<PERSON><PERSON><PERSON><PERSON>", "FI_COMPREHENSIVE_BROCHURE": "Esite (Laaja)", "FI_BRIEF_BROCHURE": "Esite (suppea)", "FI_WINDOW_CARD": "Ikkunakortti", "FI_PROPERTY_MANAGERS_CERTIFICATE": "Isännöitsijäntodistus", "FI_PLANNING_DOCUMENTS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_PURCHASE_AGREEMENT": "Kauppakir<PERSON>", "FI_PROPERTY_REGISTER_EXTRACT": "Kiinteistörekisteriote", "FI_PROPERTY_REGISTER_MAP": "Kiinteistörekisterin karttaote", "FI_PROPERTY_TAX_STATEMENT": "Kiinteistöveroil<PERSON><PERSON><PERSON>", "FI_MOISTURE_MEASUREMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_MAINTENANCE_PLAN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_MAINTENANCE_NEED_ASSESSMENT": "Kunnossapitotarveselvitys", "FI_CONDITION_INSPECTION_REPORT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_USAGE_RIGHTS_EXTRACT": "Käyttöoikeusyksikköote", "FI_CERTIFICATE_OF_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_OTHER": "<PERSON><PERSON>", "FI_OWNER_APARTMENT_PRINTOUT": "Osakehuoneistotuloste", "FI_SHARE_REGISTER_PRINT": "Osakeluettelotuloste", "FI_LONG_TERM_MAINTENANCE_PLAN": "Pitkän tähtäimen suunnitelma (PTS)", "FI_SPOUSES_CONSENT": "Puolison hyväksyntä", "FI_FLOOR_PLAN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_BUILDING_PERMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_BUILDING_DRAWINGS": "Rakennus<PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_ENCUMBRANCE_CERTIFICATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_LISTING_FORM": "Selostusliite", "FI_FINANCIAL_STATEMENT": "Tilinpäätös", "FI_BROKERAGE_AGREEMENT": "Toimeksiantoso<PERSON><PERSON>", "FI_LEASE_RIGHTS_CERTIFICATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_LEASE_AGREEMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FI_BROKERAGE_OFFER": "Välitystarjous", "FI_ARTICLES_OF_ASSOCIATION": "Yhtiöjärjestys", "FI_GENERAL_MEETING_MINUTES": "Yhtiökokouksen pöytäkirjat", "FI_HOUSING_COMPANY_CONDITION_REPORTS": "Taloyhtiön kuntoon liittyvä selvitys / selvitykset"}, "addANoteOrDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "chooseOwnDevice": "Valitse omalta la<PERSON>el<PERSON>i.", "createdAt": "Ladattu", "createdBy": "<PERSON><PERSON><PERSON>", "documentDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "documentType": "Do<PERSON><PERSON>in t<PERSON>i", "dragndropMediaHere": "Vedä ja pudota dokumentit tähän", "editModalTitle": "Muokkaa dokumenttia", "fillAllRequiredFields": "Täytä kaikki pakolliset kentät", "loadingItems": "Ladataan...", "name": "<PERSON><PERSON>", "openUploadModal": "Lisää dokumentteja", "or": "Tai", "originalFile": "Alkuperäinen tiedosto", "remove": "Poista", "searchPlaceholder": "Hae...", "supportedFiles": "Tuetut tiedostot: {{fileTypes}}", "tabTitle": "Dokumentit", "upload_one": "Lisää ({{count}})", "upload_other": "Lisää ({{count}})", "upload_plural": "not in use, here just to prevent i18n:scan from adding it again", "upload_zero": "Lisää", "upload": "not in use, here just to prevent i18n:scan from adding it again", "uploadModalTitle": "Lisää dokumentteja", "deleteSuccessTitle": "Dokumentti poistettu onnistuneesti", "uploadSuccessTitle_one": "Dokumentti ladattu onnistuneesti", "uploadSuccessTitle_other": "Dokumentit ladattu onnistuneesti", "confirmDeleteMessage": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa dokumentin: {{documentName}}?", "itemsSelected_one": "{{count}} doku<PERSON><PERSON> valittu", "itemsSelected_other": "{{count}} dokument<PERSON> valittu", "sendForSigning": "Lähetä allekirjoitukseen", "sendForSigningSuccessTitle": "Dokumentit lähetetty allekirjoitukseen", "linkedTo": "<PERSON><PERSON><PERSON>", "updateSuccessTitle": "Dokumentti päivitetty onnistuneesti", "itemsSelected": "place holder for i18n:scan", "itemsSelected_plural": "place holder for i18n:scan", "uploadSuccessTitle": "place holder for i18n:scan", "uploadSuccessTitle_plural": "place holder for i18n:scan", "itemNotFound": "Documenttia ei lö<PERSON>yt", "invalidFileType": "Tiedostomuoto ei ole tuettu", "pdfPreview": "PDF esikatselu", "documentActions": "<PERSON><PERSON><PERSON><PERSON>", "sharedDocuments": {"title": "Jaetut dokumentit", "preview": "Esikatselu"}, "share": "Jaa", "shareSelectedDocuments": "Jaa valitut dokumentit", "shareModalTitle": "Jaa dokumentit", "shareModalHelpText": "<PERSON><PERSON><PERSON> saavat lata<PERSON>linkin valituille dokumenteille. <PERSON><PERSON> 24 tunnin kuluttua.", "shareModalMessage": "<PERSON><PERSON><PERSON>", "shareModalMessagePlaceholder": "Lisää henkilökohtainen viesti saajille... (suositeltu)", "shareModalReceivers": "Lisää henkilö", "shareSuccessTitle": "Dokumentit jaettu onnistuneesti"}, "trade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purchaseOffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "daily": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "actions": "<PERSON><PERSON><PERSON><PERSON>", "loggedActionsTypes": {"CREATED": "<PERSON><PERSON><PERSON>", "UPDATED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DELETED": "<PERSON><PERSON><PERSON><PERSON>", "PRICE_CHANGED": "<PERSON>nta muutettu", "STATUS_CHANGED": "<PERSON><PERSON> mu<PERSON>u", "SYNCED": "Synkronoitu", "VIEWED": "Katsottu", "ASSIGNED": "Määrät<PERSON>", "ATTACH": "Liit<PERSON>", "DISCARDED": "<PERSON><PERSON><PERSON><PERSON>", "MEMBER_ADDED": "<PERSON><PERSON><PERSON> l<PERSON>", "SENT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DEACTIVATED": "Deaktivoitu", "UPDATED_DOCUMENT": "Asiaki<PERSON>ja <PERSON>"}, "dataBefore": "Aiempi data", "dataAfter": "Myöhempi data", "fromDate": "Alkupäivämäärä", "toDate": "Loppupäivämäärä", "companyId": "Y-tunnus", "photoshoots": "Valokuvauskalenteri", "send": "Lähetä", "oneContactRequired": "<PERSON><PERSON><PERSON> k<PERSON> on pakollinen", "oneRealtorRequired": "Yksi välittäjä on pakollinen", "tagAlreadyExists": "<PERSON>ksi tai useampi tunniste on jo olemassa", "somethingWentWrong": "<PERSON><PERSON> meni pieleen", "vatPercent": "Alv %", "listedSince": "Listattu", "openInFullPage": "<PERSON><PERSON> t<PERSON><PERSON> sivu", "gridView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listView": "Luettelon näkymä", "openInFullView": "Avaa täysikokoinen näkymä", "openInPeekView": "Avaa näky<PERSON>ä", "includeVat": "(sis. ALV)"}