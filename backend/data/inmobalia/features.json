[{"id": 1, "name": "FrontLineGolf", "description": "Front line golf"}, {"id": 2, "name": "FrontLineBeach", "description": "Front line beach"}, {"id": 3, "name": "MountaInSide", "description": "Mountainside"}, {"id": 4, "name": "AmenitiesNear", "description": "Amenities near"}, {"id": 5, "name": "TransportNear", "description": "Transport near"}, {"id": 6, "name": "Airconditioning", "description": "Air conditioning"}, {"id": 7, "name": "CentralHeating", "description": "Central heating"}, {"id": 8, "name": "PartlyFurnished", "description": "Partly furnished"}, {"id": 9, "name": "FullyFurnished", "description": "Fully furnished"}, {"id": 10, "name": "<PERSON>y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Fully fitted kitchen"}, {"id": 11, "name": "UtilityRoom", "description": "Utility room"}, {"id": 12, "name": "FirePlace", "description": "Fireplace"}, {"id": 13, "name": "MarbleFloors", "description": "Marble floors"}, {"id": 14, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 15, "name": "Sauna", "description": "Sauna"}, {"id": 16, "name": "SatelliteTV", "description": "Satellite TV"}, {"id": 17, "name": "Basement", "description": "Basement"}, {"id": 18, "name": "SolarPanels", "description": "Solar panels"}, {"id": 19, "name": "GuestRoom", "description": "Guest room"}, {"id": 20, "name": "StorageRoom", "description": "Storage room"}, {"id": 21, "name": "Gym", "description": "Gym"}, {"id": 22, "name": "Alarm", "description": "Alarm"}, {"id": 23, "name": "Solarium", "description": "Solarium"}, {"id": 24, "name": "SecurityEntrance", "description": "Security entrance"}, {"id": 25, "name": "DoubleGlazing", "description": "Double glazing"}, {"id": 26, "name": "VideoEntrance", "description": "Video entrance"}, {"id": 27, "name": "BrandNew", "description": "Brand new"}, {"id": 28, "name": "DiningRoom", "description": "Dining room"}, {"id": 29, "name": "Barbeque", "description": "Barbeque"}, {"id": 30, "name": "SecurityService24h", "description": "Security service 24h"}, {"id": 31, "name": "Telephone", "description": "Telephone"}, {"id": 32, "name": "GuestToilet", "description": "Guest toilet"}, {"id": 33, "name": "PrivateTerrace", "description": "Private terrace"}, {"id": 34, "name": "KitchenEquipped", "description": "Kitchen equipped"}, {"id": 35, "name": "LivingRoom", "description": "Living room"}, {"id": 36, "name": "StudyRoom", "description": "Study room"}, {"id": 37, "name": "WaterTank", "description": "Water tank"}, {"id": 38, "name": "ParquetFloors", "description": "Parquet floors"}, {"id": 39, "name": "WallToWallCarpet", "description": "Wall-to-wall carpet"}, {"id": 40, "name": "SeparateApartment", "description": "Separate apartment"}, {"id": 41, "name": "SeaView", "description": "Sea view"}, {"id": 42, "name": "CountryView", "description": "Country view"}, {"id": 43, "name": "MountainView", "description": "Mountain view"}, {"id": 44, "name": "Golfview", "description": "Golf view"}, {"id": 45, "name": "InDoorPool", "description": "Indoor pool"}, {"id": 46, "name": "HeatedPool", "description": "Heated pool"}, {"id": 47, "name": "UnderFloorHeating", "description": "Underfloor heating (throughout)"}, {"id": 48, "name": "AutomaticIrrigationSystem", "description": "Automatic irrigation system"}, {"id": 49, "name": "SecurityShutters", "description": "Security shutters"}, {"id": 50, "name": "HomeAutomationSystem", "description": "Home automation system"}, {"id": 51, "name": "DolbyStereoSurroundSystem", "description": "Dolby Stereo Surround system"}, {"id": 52, "name": "Bars", "description": "Bars"}, {"id": 53, "name": "Laundry<PERSON><PERSON>", "description": "Laundry room"}, {"id": 54, "name": "InternetWifi", "description": "Internet - Wifi"}, {"id": 55, "name": "CoveredTerrace", "description": "Covered terrace"}, {"id": 56, "name": "24hService", "description": "24h Service"}, {"id": 57, "name": "ElectricBlinds", "description": "Electric blinds"}, {"id": 58, "name": "FittedWardrobes", "description": "Fitted wardrobes"}, {"id": 59, "name": "GatedCommunity", "description": "Gated community"}, {"id": 60, "name": "Lift", "description": "Lift"}, {"id": 62, "name": "GardenView", "description": "Garden view"}, {"id": 63, "name": "PoolView", "description": "Pool view"}, {"id": 64, "name": "PanoramicView", "description": "Panoramic view"}, {"id": 65, "name": "TennisPaddleCourt", "description": "Tennis / paddle court"}, {"id": 66, "name": "Beachside", "description": "Beachside"}, {"id": 67, "name": "CinemaRoom", "description": "Cinema room"}, {"id": 68, "name": "StreetView", "description": "Street view"}, {"id": 69, "name": "<PERSON>man", "description": "<PERSON>man"}, {"id": 70, "name": "UnderfloorHheatingBathrooms", "description": "Underfloor heating (bathrooms)"}, {"id": 71, "name": "UnderfloorHeatingPartial", "description": "Underfloor heating (partial)"}, {"id": 72, "name": "WineCellar", "description": "Wine Cellar"}, {"id": 73, "name": "SteamRoom", "description": "Steam Room"}, {"id": 74, "name": "Unfurnished", "description": "Unfurnished"}, {"id": 75, "name": "CloseToChildrenPlayground", "description": "Close to children playground"}, {"id": 78, "name": "CloseToSeaBeach", "description": "Close to Sea/Beach"}, {"id": 79, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Close to Golf"}, {"id": 80, "name": "UncoveredTerrace", "description": "Uncovered terrace"}, {"id": 81, "name": "GameRoom", "description": "Game Room"}, {"id": 82, "name": "GlassDoors", "description": "Glass Doors"}, {"id": 83, "name": "SeparateDiningRoom", "description": "Separate dining room"}, {"id": 84, "name": "WoodenFloors", "description": "Wooden floors"}, {"id": 85, "name": "OpenPlan<PERSON><PERSON>en", "description": "Open plan kitchen"}, {"id": 86, "name": "Balcony", "description": "Balcony"}, {"id": 87, "name": "OptionalFurniture", "description": "Optional furniture"}, {"id": 88, "name": "SPA", "description": "SPA"}, {"id": 89, "name": "TurkishBath", "description": "Turkish bath"}, {"id": 90, "name": "HandicapAccessible", "description": "Handicap Accessible"}, {"id": 91, "name": "ExcellentCondition", "description": "Excellent condition"}, {"id": 92, "name": "GoodCondition", "description": "Good condition"}, {"id": 93, "name": "RecentlyRenovatedRefurbished", "description": "Recently Renovated/Refurbished"}, {"id": 94, "name": "RenovationNeeded", "description": "Renovation Needed"}, {"id": 95, "name": "LakeView", "description": "Lake view"}, {"id": 96, "name": "UrbanView", "description": "Urban view"}, {"id": 97, "name": "CeilingCoolingSystem", "description": "Ceiling cooling system"}, {"id": 98, "name": "CeilingHeatingSystem", "description": "Ceiling heating system"}, {"id": 99, "name": "UnderfloorCoolingSystem", "description": "Underfloor cooling system"}, {"id": 100, "name": "SaltwaterSwimmingPool", "description": "Saltwater swimming pool"}, {"id": 101, "name": "CloseToShops", "description": "Close to shops"}, {"id": 102, "name": "CloseToTown", "description": "Close to town"}, {"id": 103, "name": "CloseToPort", "description": "Close to port"}, {"id": 104, "name": "CloseToSchools", "description": "Close to schools"}, {"id": 105, "name": "SurveillanceCameras", "description": "Surveillance cameras"}, {"id": 106, "name": "GuestApartment", "description": "Guest apartment"}, {"id": 107, "name": "InsideGolfResort", "description": "Inside Golf Resort"}, {"id": 108, "name": "Marina<PERSON>iew", "description": "Marina view"}, {"id": 109, "name": "OfficeRoom", "description": "Office room"}, {"id": 110, "name": "PetsAllowed", "description": "Pets allowed"}, {"id": 111, "name": "IndividualUnitsAC", "description": "Individual A/C units"}, {"id": 112, "name": "CentralHeatingByRadiators", "description": "Central heating by radiators"}, {"id": 113, "name": "ElectricRadiators", "description": "Electric radiators"}, {"id": 114, "name": "GasHeating", "description": "Gas heating"}, {"id": 115, "name": "GresFloors", "description": "Gres floors"}, {"id": 116, "name": "ArmoredDoor", "description": "Armored door"}, {"id": 117, "name": "kitchenette", "description": "kitchenette"}, {"id": 118, "name": "GroundFloorPatio", "description": "Ground floor patio"}, {"id": 119, "name": "PartialSeaViews", "description": "Partial sea views"}, {"id": 120, "name": "Well", "description": "Well"}, {"id": 121, "name": "PorcelainFloors", "description": "Porcelain floors"}, {"id": 122, "name": "Aerothermics", "description": "Aerothermics"}, {"id": 123, "name": "InternetFibre", "description": "Internet - Fibre optic"}, {"id": 124, "name": "Walk in closet", "description": "Walk in closet"}, {"id": 125, "name": "Walk in closet", "description": "Walk in closet"}, {"id": 126, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Walk-in closet"}, {"id": 127, "name": "GasoilHeating", "description": "Gasoil heating"}, {"id": 128, "name": "SepticTank", "description": "Septic Tank"}, {"id": 129, "name": "OutdoorKitchen", "description": "Outdoor kitchen"}, {"id": 130, "name": "R<PERSON><PERSON>errace", "description": "Roof terrace"}, {"id": 131, "name": "SwimJet", "description": "Swim jet"}, {"id": 132, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Mature gardens"}, {"id": 133, "name": "Heliport", "description": "Heliport"}, {"id": 134, "name": "StoneFloors", "description": "Stone floors"}, {"id": 135, "name": "DirectSeaAccess", "description": "Direct sea access"}, {"id": 136, "name": "MainsElectricitySupply", "description": "Mains electricity supply"}, {"id": 137, "name": "MainsWaterSupply", "description": "Mains water supply"}, {"id": 138, "name": "Stables", "description": "Stables"}, {"id": 139, "name": "ReverseOsmosisWaterSystem", "description": "Reverse Osmosis water system"}, {"id": 140, "name": "StaffAccommodation", "description": "Staff accommodation"}, {"id": 141, "name": "EVChargingStation", "description": "EV charging station"}, {"id": 142, "name": "PrivateMooring", "description": "Private mooring"}, {"id": 143, "name": "CloseToRestaurants", "description": "Close to restaurants"}, {"id": 144, "name": "CityViews", "description": "City views"}, {"id": 145, "name": "CoWorkingSpace", "description": "Co-working space"}, {"id": 146, "name": "RotatingParking", "description": "Rotating parking"}, {"id": 147, "name": "MassageRoom", "description": "Massage Room"}]