INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Share','Residential','Apartment house'),
	 (NOW(),NOW(),'Sale','Share','Residential','Detached house'),
	 (NOW(),NOW(),'Sale','Share','Residential','Row house'),
	 (NOW(),NOW(),'Sale','Share','Residential','Semi detached house'),
	 (NOW(),NOW(),'Sale','Share','Residential','Separate house'),
	 (NOW(),NOW(),'Sale','Share','Residential','Wooden house apartment'),
	 (NOW(),NOW(),'Sale','Share','Residential','Balcony access block'),
	 (NOW(),NOW(),'Sale','Share','Leisure','Cottage or villa'),
	 (NOW(),NOW(),'Sale','Share','Leisure','Time share apartment'),
	 (NOW(),NOW(),'Sale','Share','Leisure','Leisure apartment');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Share','Leisure','Detached house'),
	 (NOW(),NOW(),'Sale','Share','Leisure','Other'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Retail space'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Production facility'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Warehouse'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Office space'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Coworking'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Care facility'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Commercial plot'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Industrial plot');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Share','Commercial property','Storage plot'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Solar farm'),
	 (NOW(),NOW(),'Sale','Share','Commercial property','Other'),
	 (NOW(),NOW(),'Sale','Share','Other','Other'),
	 (NOW(),NOW(),'Sale','Share','Other','Boat'),
	 (NOW(),NOW(),'Sale','Share','Other','Car shed'),
	 (NOW(),NOW(),'Sale','Share','Other','Car shelter'),
	 (NOW(),NOW(),'Sale','Share','Other','Garage'),
	 (NOW(),NOW(),'Sale','Share','Other','Parking slot'),
	 (NOW(),NOW(),'Sale','Share','Other','Storage');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Property','Residential','Apartment house'),
	 (NOW(),NOW(),'Sale','Property','Residential','Detached house'),
	 (NOW(),NOW(),'Sale','Property','Residential','Row house'),
	 (NOW(),NOW(),'Sale','Property','Residential','Semi detached house'),
	 (NOW(),NOW(),'Sale','Property','Residential','Separate house'),
	 (NOW(),NOW(),'Sale','Property','Residential','Wooden house apartment'),
	 (NOW(),NOW(),'Sale','Property','Residential','Balcony access block'),
	 (NOW(),NOW(),'Sale','Property','Leisure','Cottage or villa'),
	 (NOW(),NOW(),'Sale','Property','Leisure','Leisure apartment');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Property','Leisure','Detached house'),
	 (NOW(),NOW(),'Sale','Property','Leisure','Other'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Retail space'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Production facility'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Warehouse'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Office space'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Coworking'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Care facility'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Commercial plot'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Industrial plot');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Property','Commercial property','Storage plot'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Solar farm'),
	 (NOW(),NOW(),'Sale','Property','Commercial property','Other'),
	 (NOW(),NOW(),'Sale','Property','Estate','Other'),
	 (NOW(),NOW(),'Sale','Property','Estate','Arable farm'),
	 (NOW(),NOW(),'Sale','Property','Estate','Farm'),
	 (NOW(),NOW(),'Sale','Property','Estate','Forest'),
	 (NOW(),NOW(),'Sale','Property','Estate','Parcel of land'),
	 (NOW(),NOW(),'Sale','Property','Estate','Wilderness'),
	 (NOW(),NOW(),'Sale','Property','Plot','Other');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Sale','Property','Plot','Apartment plot'),
	 (NOW(),NOW(),'Sale','Property','Plot','Holiday plot'),
	 (NOW(),NOW(),'Sale','Property','Plot','House plot'),
	 (NOW(),NOW(),'Sale','Property','Plot','Row house plot'),
	 (NOW(),NOW(),'Sale','Property','Plot','Business or industrial plot'),
	 (NOW(),NOW(),'Rental','Share','Residential','Apartment house'),
	 (NOW(),NOW(),'Rental','Share','Residential','Detached house'),
	 (NOW(),NOW(),'Rental','Share','Residential','Row house'),
	 (NOW(),NOW(),'Rental','Share','Residential','Semi detached house'),
	 (NOW(),NOW(),'Rental','Share','Residential','Separate house');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Share','Residential','Wooden house apartment'),
	 (NOW(),NOW(),'Rental','Share','Residential','Balcony access block'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Retail space'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Production facility'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Warehouse'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Office space'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Coworking'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Care facility'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Commercial plot'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Industrial plot');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Share','Commercial property','Storage plot'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Solar farm'),
	 (NOW(),NOW(),'Rental','Share','Commercial property','Other'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Apartment house'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Detached house'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Row house'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Semi detached house'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Separate house'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Wooden house apartment'),
	 (NOW(),NOW(),'Rental','Share','Shared apartment','Balcony access block');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Share','Sublease','Apartment house'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Detached house'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Row house'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Semi detached house'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Separate house'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Wooden house apartment'),
	 (NOW(),NOW(),'Rental','Share','Sublease','Balcony access block'),
	 (NOW(),NOW(),'Rental','Share','Other','Other'),
	 (NOW(),NOW(),'Rental','Share','Other','Boat'),
	 (NOW(),NOW(),'Rental','Share','Other','Car shed');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Share','Other','Car shelter'),
	 (NOW(),NOW(),'Rental','Share','Other','Garage'),
	 (NOW(),NOW(),'Rental','Share','Other','Parking slot'),
	 (NOW(),NOW(),'Rental','Share','Other','Storage'),
	 (NOW(),NOW(),'Rental','Property','Residential','Apartment house'),
	 (NOW(),NOW(),'Rental','Property','Residential','Detached house'),
	 (NOW(),NOW(),'Rental','Property','Residential','Row house'),
	 (NOW(),NOW(),'Rental','Property','Residential','Semi detached house'),
	 (NOW(),NOW(),'Rental','Property','Residential','Separate house'),
	 (NOW(),NOW(),'Rental','Property','Residential','Wooden house apartment');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Property','Residential','Balcony access block'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Retail space'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Production facility'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Warehouse'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Office space'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Coworking'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Care facility'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Commercial plot'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Industrial plot'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Storage plot');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Property','Commercial property','Solar farm'),
	 (NOW(),NOW(),'Rental','Property','Commercial property','Other'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Apartment house'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Detached house'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Row house'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Semi detached house'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Separate house'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Wooden house apartment'),
	 (NOW(),NOW(),'Rental','Property','Shared apartment','Balcony access block'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Apartment house');
INSERT INTO fi_property_type (created_at,updated_at,category,subcategory,`type`,subtype) VALUES
	 (NOW(),NOW(),'Rental','Property','Sublease','Detached house'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Row house'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Semi detached house'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Separate house'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Wooden house apartment'),
	 (NOW(),NOW(),'Rental','Property','Sublease','Balcony access block');

