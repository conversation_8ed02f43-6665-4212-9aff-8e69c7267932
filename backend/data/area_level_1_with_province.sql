-- This is the data to update the area_level_1 with provinces.
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Al<PERSON><PERSON><PERSON> Torre', 'Spain', 1, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Alhaurín el Grande', 'Spain', 2, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Benahavis', 'Spain', 4, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Benalmadena', 'Spain', 5, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Casares', 'Spain', 9, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Coín', 'Spain', 10, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Estepona', 'Spain', 11, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Fuengirola', 'Spain', 12, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Gaucín', 'Spain', 13, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Istán', 'Spain', 16, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Jubrique', 'Spain', 17, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Malaga', 'Spain', 21, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Manilva', 'Spain', 22, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Marbella', 'Spain', 24, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Mijas', 'Spain', 25, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Mijas Costa', 'Spain', 26, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Monda', 'Spain', 27, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Ojen', 'Spain', 28, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Rincón de la Victoria', 'Spain', 31, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Ronda', 'Spain', 32, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Pedro de Alcantara', 'Spain', 35, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Sotogrande', 'Spain', 36, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Torremolinos', 'Spain', 39, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Alora', 'Spain', 42, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Guaro', 'Spain', 43, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Cártama', 'Spain', 44, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Pizarra', 'Spain', 45, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Roque', 'Spain', 72, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Torrox', 'Spain', 117, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Montejaque', 'Spain', 121, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Motril', 'Spain', 157, NOW(), NOW(), 'Granada') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Valle de Abdalajis', 'Spain', 167, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Cortes de la Frontera', 'Spain', 273, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Torre del Mar', 'Spain', 284, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Tolox', 'Spain', 328, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Alameda', 'Spain', 329, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Almogía', 'Spain', 336, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Alozaina', 'Spain', 337, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Antequera', 'Spain', 338, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Archidona', 'Spain', 339, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Ardales', 'Spain', 340, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Arriate', 'Spain', 342, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Benaoján', 'Spain', 346, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Campillos', 'Spain', 349, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Cañete la Real', 'Spain', 350, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Casabermeja', 'Spain', 352, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Casarabonela', 'Spain', 353, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Cuevas De San Marcos', 'Spain', 359, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('El Burgo', 'Spain', 362, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Frigiliana', 'Spain', 364, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Mollina', 'Spain', 370, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Nerja', 'Spain', 372, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Teba', 'Spain', 377, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Villanueva de Algaidas', 'Spain', 380, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Villanueva del Rosario', 'Spain', 382, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Villanueva del Trabuco', 'Spain', 383, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Yunquera', 'Spain', 385, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Alpandeire', 'Spain', 68729, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Benadalid', 'Spain', 68731, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Carratraca', 'Spain', 68735, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Villanueva De La Concepcion', 'Spain', 73775, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Torremar', 'Spain', 99100, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Diego', 'Spain', 99200, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Enrique', 'Spain', 99300, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Luis de Sabinillas', 'Spain', 99400, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('San Martín de Tesorillo', 'Spain', 99500, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Torreguadiaro', 'Spain', 99700, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Algeciras', 'Spain', 99800, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Calypso', 'Spain', 99900, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('La Alcaidesa', 'Spain', 99901, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('La Atalaya', 'Spain', 99902, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('La Línea', 'Spain', 99903, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Los Barrios', 'Spain', 99904, NOW(), NOW(), 'Cádiz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Pueblo Nuevo de Guadiaro', 'Spain', 99905, NOW(), NOW(), 'Badajoz') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Punta Chullera', 'Spain', 99906, NOW(), NOW(), 'Málaga') ON DUPLICATE KEY update province = VALUES(province);
INSERT INTO area_level_1 (name, country, id, created_at, updated_at, province) VALUES('Mallorca', 'Spain', 999999, NOW(), NOW(), 'Islas Baleares') ON DUPLICATE KEY update province = VALUES(province);