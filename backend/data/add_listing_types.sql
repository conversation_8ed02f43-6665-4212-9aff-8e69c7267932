-- Insert into listing_type
INSERT IGNORE INTO listing_type (id, name, created_at, updated_at) VALUES(1, 'Sale', NOW(), NOW());
INSERT IGNORE INTO listing_type (id, name, created_at, updated_at) VALUES(2, 'Rent Short Term', NOW(), NOW());
INSERT IGNORE INTO listing_type (id, name, created_at, updated_at) VALUES(3, 'Rent Long Term', NOW(), NOW());

-- Insert into property_listing_type (considering all properties there as 'Sale' because this is the current situation in production DB)
INSERT IGNORE INTO property_listing_type (listing_type_id, property_id, created_at, updated_at)
SELECT (SELECT id as listing_type_id from listing_type where name = 'Sale'), id as property_id, NOW(), NOW() FROM property_spain; 
