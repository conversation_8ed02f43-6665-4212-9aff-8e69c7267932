START TRANSACTION;
DELETE FROM fi_area;
ALTER TABLE fi_area AUTO_INCREMENT = 1;

INSERT INTO fi_area (
		kind,
		area_code,
		area_name_en,
		area_name_fi,
		area_name_sv,
		parent_id,
		created_at,
		updated_at
	)
VALUES (
		'MAJOR_REGION',
		'3',
		'Western Finland',
		'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
		'Västra Finland',
		NULL,
		NOW(),
		NOW()
	),
	(
		'MAJOR_REGION',
		'4',
		'Northern and Eastern Finland',
		'<PERSON><PERSON><PERSON><PERSON>- ja <PERSON><PERSON><PERSON><PERSON><PERSON>',
		'Norra och Östra Finland',
		NULL,
		NOW(),
		NOW()
	),
	(
		'MAJOR_REGION',
		'2',
		'Southern Finland',
		'Etel<PERSON>-<PERSON><PERSON>',
		'Södra Finland',
		NULL,
		NOW(),
		NOW()
	),
	(
		'MAJOR_REGION',
		'1',
		'Helsinki-Uusimaa',
		'Helsinki-Uusimaa',
		'<PERSON>lsingfors-Nyland',
		NULL,
		NOW(),
		NOW()
	),
	(
		'MAJOR_REGION',
		'5',
		'Åland',
		'Ahvenanmaa',
		'Åland',
		NULL,
		NOW(),
		NOW()
	);
CREATE TEMPORARY TABLE temp_major_regions (area_code VARCHAR(50), parent_id BIGINT);
INSERT INTO temp_major_regions (area_code, parent_id)
SELECT area_code,
	id AS parent_id
FROM fi_area
WHERE kind = 'MAJOR_REGION';
INSERT INTO fi_area (
		kind,
		area_code,
		area_name_en,
		area_name_fi,
		area_name_sv,
		parent_id,
		created_at,
		updated_at
	)
VALUES (
		'REGION',
		'14',
		'South Ostrobothnia',
		'Etelä-Pohjanmaa',
		'Södra Österbotten',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '3'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'17',
		'North Ostrobothnia',
		'Pohjois-Pohjanmaa',
		'Norra Österbotten',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'7',
		'Päijät-Häme',
		'Päijät-Häme',
		'Päijänne-Tavastland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'1',
		'Uusimaa',
		'Uusimaa',
		'Nyland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'2',
		'Southwest Finland',
		'Varsinais-Suomi',
		'Egentliga Finland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'6',
		'Pirkanmaa',
		'Pirkanmaa',
		'Birkaland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '3'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'21',
		'Åland',
		'Ahvenanmaa',
		'Åland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'10',
		'South Savo',
		'Etelä-Savo',
		'Södra Savolax',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'19',
		'Lapland',
		'Lappi',
		'Lappland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'4',
		'Satakunta',
		'Satakunta',
		'Satakunta',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '3'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'5',
		'Kanta-Häme',
		'Kanta-Häme',
		'Egentliga Tavastland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'16',
		'Central Ostrobothnia',
		'Keski-Pohjanmaa',
		'Mellersta Österbotten',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'8',
		'Kymenlaakso',
		'Kymenlaakso',
		'Kymmenedalen',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'13',
		'Central Finland',
		'Keski-Suomi',
		'Mellersta Finland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '3'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'12',
		'North Karelia',
		'Pohjois-Karjala',
		'Norra Karelen',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'18',
		'Kainuu',
		'Kainuu',
		'Kajanaland',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'11',
		'North Savo',
		'Pohjois-Savo',
		'Norra Savolax',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'9',
		'South Karelia',
		'Etelä-Karjala',
		'Södra Karelen',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'REGION',
		'15',
		'Ostrobothnia',
		'Pohjanmaa',
		'Österbotten',
		(
			SELECT parent_id
			FROM temp_major_regions
			WHERE area_code = '3'
		),
		NOW(),
		NOW()
	);
CREATE TEMPORARY TABLE temp_regions (area_code VARCHAR(255), parent_id BIGINT);
INSERT INTO temp_regions (area_code, parent_id)
SELECT area_code,
	id AS parent_id
FROM fi_area
WHERE kind = 'REGION';
INSERT INTO fi_area (
		kind,
		area_code,
		area_name_en,
		area_name_fi,
		area_name_sv,
		parent_id,
		created_at,
		updated_at
	)
VALUES (
		'MUNICIPALITY',
		'5',
		'Alajärvi',
		'Alajärvi',
		'Alajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'9',
		'Alavieska',
		'Alavieska',
		'Alavieska',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'10',
		'Alavus',
		'Alavus',
		'Alavus',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'16',
		'Asikkala',
		'Asikkala',
		'Asikkala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'18',
		'Askola',
		'Askola',
		'Askola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'19',
		'Aura',
		'Aura',
		'Aura',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'20',
		'Akaa',
		'Akaa',
		'Akaa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'35',
		'Brändö',
		'Brändö',
		'Brändö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'43',
		'Eckerö',
		'Eckerö',
		'Eckerö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'46',
		'Enonkoski',
		'Enonkoski',
		'Enonkoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'47',
		'Enontekiö',
		'Enontekiö',
		'Enontekiö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'49',
		'Espoo',
		'Espoo',
		'Esbo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'50',
		'Eura',
		'Eura',
		'Eura',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'51',
		'Eurajoki',
		'Eurajoki',
		'Eurajoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'52',
		'Evijärvi',
		'Evijärvi',
		'Evijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'60',
		'Finström',
		'Finström',
		'Finström',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'61',
		'Forssa',
		'Forssa',
		'Forssa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'62',
		'Föglö',
		'Föglö',
		'Föglö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'65',
		'Geta',
		'Geta',
		'Geta',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'69',
		'Haapajärvi',
		'Haapajärvi',
		'Haapajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'71',
		'Haapavesi',
		'Haapavesi',
		'Haapavesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'72',
		'Hailuoto',
		'Hailuoto',
		'Hailuoto',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'74',
		'Halsua',
		'Halsua',
		'Halsua',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'75',
		'Hamina',
		'Hamina',
		'Hamina',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'76',
		'Hammarland',
		'Hammarland',
		'Hammarland',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'77',
		'Hankasalmi',
		'Hankasalmi',
		'Hankasalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'78',
		'Hanko',
		'Hanko',
		'Hangö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'79',
		'Harjavalta',
		'Harjavalta',
		'Harjavalta',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'81',
		'Hartola',
		'Hartola',
		'Hartola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'82',
		'Hattula',
		'Hattula',
		'Hattula',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'86',
		'Hausjärvi',
		'Hausjärvi',
		'Hausjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'90',
		'Heinävesi',
		'Heinävesi',
		'Heinävesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'91',
		'Helsinki',
		'Helsinki',
		'Helsingfors',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'92',
		'Vantaa',
		'Vantaa',
		'Vanda',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'97',
		'Hirvensalmi',
		'Hirvensalmi',
		'Hirvensalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'98',
		'Hollola',
		'Hollola',
		'Hollola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'102',
		'Huittinen',
		'Huittinen',
		'Huittinen',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'103',
		'Humppila',
		'Humppila',
		'Humppila',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'105',
		'Hyrynsalmi',
		'Hyrynsalmi',
		'Hyrynsalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'106',
		'Hyvinkää',
		'Hyvinkää',
		'Hyvinge',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'108',
		'Hämeenkyrö',
		'Hämeenkyrö',
		'Tavastkyro',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'109',
		'Hämeenlinna',
		'Hämeenlinna',
		'Tavastehus',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'111',
		'Heinola',
		'Heinola',
		'Heinola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'139',
		'Ii',
		'Ii',
		'Ii',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'140',
		'Iisalmi',
		'Iisalmi',
		'Idensalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'142',
		'Iitti',
		'Iitti',
		'Iitti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'143',
		'Ikaalinen',
		'Ikaalinen',
		'Ikalis',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'145',
		'Ilmajoki',
		'Ilmajoki',
		'Ilmola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'146',
		'Ilomantsi',
		'Ilomantsi',
		'Ilomants',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'148',
		'Inari',
		'Inari',
		'Enare',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'149',
		'Inkoo',
		'Inkoo',
		'Ingå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'151',
		'Isojoki',
		'Isojoki',
		'Storå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'152',
		'Isokyrö',
		'Isokyrö',
		'Storkyro',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'153',
		'Imatra',
		'Imatra',
		'Imatra',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'165',
		'Janakkala',
		'Janakkala',
		'Janakkala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'167',
		'Joensuu',
		'Joensuu',
		'Joensuu',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'169',
		'Jokioinen',
		'Jokioinen',
		'Jockis',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'170',
		'Jomala',
		'Jomala',
		'Jomala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'171',
		'Joroinen',
		'Joroinen',
		'Jorois',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'172',
		'Joutsa',
		'Joutsa',
		'Joutsa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'176',
		'Juuka',
		'Juuka',
		'Juuka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'177',
		'Juupajoki',
		'Juupajoki',
		'Juupajoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'178',
		'Juva',
		'Juva',
		'Juva',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'179',
		'Jyväskylä',
		'Jyväskylä',
		'Jyväskylä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'181',
		'Jämijärvi',
		'Jämijärvi',
		'Jämijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'182',
		'Jämsä',
		'Jämsä',
		'Jämsä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'186',
		'Järvenpää',
		'Järvenpää',
		'Träskända',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'202',
		'Kaarina',
		'Kaarina',
		'S:t Karins',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'204',
		'Kaavi',
		'Kaavi',
		'Kaavi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'205',
		'Kajaani',
		'Kajaani',
		'Kajana',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'208',
		'Kalajoki',
		'Kalajoki',
		'Kalajoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'211',
		'Kangasala',
		'Kangasala',
		'Kangasala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'213',
		'Kangasniemi',
		'Kangasniemi',
		'Kangasniemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'214',
		'Kankaanpää',
		'Kankaanpää',
		'Kankaanpää',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'216',
		'Kannonkoski',
		'Kannonkoski',
		'Kannonkoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'217',
		'Kannus',
		'Kannus',
		'Kannus',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'218',
		'Karijoki',
		'Karijoki',
		'Karijoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'224',
		'Karkkila',
		'Karkkila',
		'Högfors',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'226',
		'Karstula',
		'Karstula',
		'Karstula',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'230',
		'Karvia',
		'Karvia',
		'Karvia',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'231',
		'Kaskinen',
		'Kaskinen',
		'Kaskö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'232',
		'Kauhajoki',
		'Kauhajoki',
		'Kauhajoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'233',
		'Kauhava',
		'Kauhava',
		'Kauhava',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'235',
		'Kauniainen',
		'Kauniainen',
		'Grankulla',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'236',
		'Kaustinen',
		'Kaustinen',
		'Kaustby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'239',
		'Keitele',
		'Keitele',
		'Keitele',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'240',
		'Kemi',
		'Kemi',
		'Kemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'241',
		'Keminmaa',
		'Keminmaa',
		'Keminmaa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'244',
		'Kempele',
		'Kempele',
		'Kempele',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'245',
		'Kerava',
		'Kerava',
		'Kervo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'249',
		'Keuruu',
		'Keuruu',
		'Keuruu',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'250',
		'Kihniö',
		'Kihniö',
		'Kihniö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'256',
		'Kinnula',
		'Kinnula',
		'Kinnula',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'257',
		'Kirkkonummi',
		'Kirkkonummi',
		'Kyrkslätt',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'260',
		'Kitee',
		'Kitee',
		'Kitee',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'261',
		'Kittilä',
		'Kittilä',
		'Kittilä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'263',
		'Kiuruvesi',
		'Kiuruvesi',
		'Kiuruvesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'265',
		'Kivijärvi',
		'Kivijärvi',
		'Kivijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'271',
		'Kokemäki',
		'Kokemäki',
		'Kumo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'272',
		'Kokkola',
		'Kokkola',
		'Karleby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'273',
		'Kolari',
		'Kolari',
		'Kolari',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'275',
		'Konnevesi',
		'Konnevesi',
		'Konnevesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'276',
		'Kontiolahti',
		'Kontiolahti',
		'Kontiolax',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'280',
		'Korsnäs',
		'Korsnäs',
		'Korsnäs',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'284',
		'Koski Tl',
		'Koski Tl',
		'Koski Tl',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'285',
		'Kotka',
		'Kotka',
		'Kotka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'286',
		'Kouvola',
		'Kouvola',
		'Kouvola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'287',
		'Kristinestad',
		'Kristiinankaupunki',
		'Kristinestad',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'288',
		'Kronoby',
		'Kruunupyy',
		'Kronoby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'290',
		'Kuhmo',
		'Kuhmo',
		'Kuhmo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'291',
		'Kuhmoinen',
		'Kuhmoinen',
		'Kuhmoinen',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'295',
		'Kumlinge',
		'Kumlinge',
		'Kumlinge',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'297',
		'Kuopio',
		'Kuopio',
		'Kuopio',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'300',
		'Kuortane',
		'Kuortane',
		'Kuortane',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'301',
		'Kurikka',
		'Kurikka',
		'Kurikka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'304',
		'Kustavi',
		'Kustavi',
		'Gustavs',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'305',
		'Kuusamo',
		'Kuusamo',
		'Kuusamo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'309',
		'Outokumpu',
		'Outokumpu',
		'Outokumpu',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'312',
		'Kyyjärvi',
		'Kyyjärvi',
		'Kyyjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'316',
		'Kärkölä',
		'Kärkölä',
		'Kärkölä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'317',
		'Kärsämäki',
		'Kärsämäki',
		'Kärsämäki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'318',
		'Kökar',
		'Kökar',
		'Kökar',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'320',
		'Kemijärvi',
		'Kemijärvi',
		'Kemijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'322',
		'Kemiönsaari',
		'Kemiönsaari',
		'Kimitoön',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'398',
		'Lahti',
		'Lahti',
		'Lahti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'399',
		'Laihia',
		'Laihia',
		'Laihia',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'400',
		'Laitila',
		'Laitila',
		'Laitila',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'402',
		'Lapinlahti',
		'Lapinlahti',
		'Lapinlahti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'403',
		'Lappajärvi',
		'Lappajärvi',
		'Lappajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'405',
		'Lappeenranta',
		'Lappeenranta',
		'Villmanstrand',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'407',
		'Lapinjärvi',
		'Lapinjärvi',
		'Lappträsk',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'408',
		'Lapua',
		'Lapua',
		'Lappo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'410',
		'Laukaa',
		'Laukaa',
		'Laukaa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'416',
		'Lemi',
		'Lemi',
		'Lemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'417',
		'Lemland',
		'Lemland',
		'Lemland',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'418',
		'Lempäälä',
		'Lempäälä',
		'Lempäälä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'420',
		'Leppävirta',
		'Leppävirta',
		'Leppävirta',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'421',
		'Lestijärvi',
		'Lestijärvi',
		'Lestijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'422',
		'Lieksa',
		'Lieksa',
		'Lieksa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'423',
		'Lieto',
		'Lieto',
		'Lundo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'425',
		'Liminka',
		'Liminka',
		'Limingo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'426',
		'Liperi',
		'Liperi',
		'Libelits',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'430',
		'Loimaa',
		'Loimaa',
		'Loimaa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'433',
		'Loppi',
		'Loppi',
		'Loppi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'434',
		'Loviisa',
		'Loviisa',
		'Lovisa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'435',
		'Luhanka',
		'Luhanka',
		'Luhanka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'436',
		'Lumijoki',
		'Lumijoki',
		'Lumijoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'438',
		'Lumparland',
		'Lumparland',
		'Lumparland',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'440',
		'Luoto',
		'Luoto',
		'Larsmo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'441',
		'Luumäki',
		'Luumäki',
		'Luumäki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'444',
		'Lohja',
		'Lohja',
		'Lojo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'445',
		'Parainen',
		'Parainen',
		'Pargas',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'475',
		'Malax',
		'Malax',
		'Malax',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'478',
		'Mariehamn',
		'Maarianhamina',
		'Mariehamn',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'480',
		'Marttila',
		'Marttila',
		'S:t Mårtens',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'481',
		'Masku',
		'Masku',
		'Masku',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'483',
		'Merijärvi',
		'Merijärvi',
		'Merijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'484',
		'Merikarvia',
		'Merikarvia',
		'Merikarvia',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'489',
		'Miehikkälä',
		'Miehikkälä',
		'Miehikkälä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'491',
		'Mikkeli',
		'Mikkeli',
		'S:t Michel',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'494',
		'Muhos',
		'Muhos',
		'Muhos',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'495',
		'Multia',
		'Multia',
		'Multia',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'498',
		'Muonio',
		'Muonio',
		'Muonio',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'499',
		'Mustasaari',
		'Mustasaari',
		'Korsholm',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'500',
		'Muurame',
		'Muurame',
		'Muurame',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'503',
		'Mynämäki',
		'Mynämäki',
		'Mynämäki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'504',
		'Myrskylä',
		'Myrskylä',
		'Mörskom',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'505',
		'Mäntsälä',
		'Mäntsälä',
		'Mäntsälä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'507',
		'Mäntyharju',
		'Mäntyharju',
		'Mäntyharju',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'508',
		'Mänttä-Vilppula',
		'Mänttä-Vilppula',
		'Mänttä-Vilppula',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'529',
		'Naantali',
		'Naantali',
		'Nådendal',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'531',
		'Nakkila',
		'Nakkila',
		'Nakkila',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'535',
		'Nivala',
		'Nivala',
		'Nivala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'536',
		'Nokia',
		'Nokia',
		'Nokia',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'538',
		'Nousiainen',
		'Nousiainen',
		'Nousis',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'541',
		'Nurmes',
		'Nurmes',
		'Nurmes',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'543',
		'Nurmijärvi',
		'Nurmijärvi',
		'Nurmijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'545',
		'Närpiö',
		'Närpiö',
		'Närpes',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'560',
		'Orimattila',
		'Orimattila',
		'Orimattila',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'561',
		'Oripää',
		'Oripää',
		'Oripää',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'562',
		'Orivesi',
		'Orivesi',
		'Orivesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'563',
		'Oulainen',
		'Oulainen',
		'Oulainen',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'564',
		'Oulu',
		'Oulu',
		'Uleåborg',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'576',
		'Padasjoki',
		'Padasjoki',
		'Padasjoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'577',
		'Paimio',
		'Paimio',
		'Pemar',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'578',
		'Paltamo',
		'Paltamo',
		'Paltamo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'580',
		'Parikkala',
		'Parikkala',
		'Parikkala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'581',
		'Parkano',
		'Parkano',
		'Parkano',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'583',
		'Pelkosenniemi',
		'Pelkosenniemi',
		'Pelkosenniemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'584',
		'Perho',
		'Perho',
		'Perho',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'588',
		'Pertunmaa',
		'Pertunmaa',
		'Pertunmaa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'592',
		'Petäjävesi',
		'Petäjävesi',
		'Petäjävesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'593',
		'Pieksämäki',
		'Pieksämäki',
		'Pieksämäki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'595',
		'Pielavesi',
		'Pielavesi',
		'Pielavesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'598',
		'Pietarsaari',
		'Pietarsaari',
		'Jakobstad',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'599',
		'Pedersöre',
		'Pedersöre',
		'Pedersöre',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'601',
		'Pihtipudas',
		'Pihtipudas',
		'Pihtipudas',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'604',
		'Pirkkala',
		'Pirkkala',
		'Birkala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'607',
		'Polvijärvi',
		'Polvijärvi',
		'Polvijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'608',
		'Pomarkku',
		'Pomarkku',
		'Påmark',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'609',
		'Pori',
		'Pori',
		'Björneborg',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'611',
		'Pornainen',
		'Pornainen',
		'Borgnäs',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'614',
		'Posio',
		'Posio',
		'Posio',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'615',
		'Pudasjärvi',
		'Pudasjärvi',
		'Pudasjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'616',
		'Pukkila',
		'Pukkila',
		'Pukkila',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'619',
		'Punkalaidun',
		'Punkalaidun',
		'Punkalaidun',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'620',
		'Puolanka',
		'Puolanka',
		'Puolanka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'623',
		'Puumala',
		'Puumala',
		'Puumala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'624',
		'Pyhtää',
		'Pyhtää',
		'Pyttis',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'625',
		'Pyhäjoki',
		'Pyhäjoki',
		'Pyhäjoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'626',
		'Pyhäjärvi',
		'Pyhäjärvi',
		'Pyhäjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'630',
		'Pyhäntä',
		'Pyhäntä',
		'Pyhäntä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'631',
		'Pyhäranta',
		'Pyhäranta',
		'Pyhäranta',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'635',
		'Pälkäne',
		'Pälkäne',
		'Pälkäne',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'636',
		'Pöytyä',
		'Pöytyä',
		'Pöytyä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'638',
		'Porvoo',
		'Porvoo',
		'Borgå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'678',
		'Raahe',
		'Raahe',
		'Brahestad',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'680',
		'Raisio',
		'Raisio',
		'Reso',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'681',
		'Rantasalmi',
		'Rantasalmi',
		'Rantasalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'683',
		'Ranua',
		'Ranua',
		'Ranua',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'684',
		'Rauma',
		'Rauma',
		'Raumo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'686',
		'Rautalampi',
		'Rautalampi',
		'Rautalampi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'687',
		'Rautavaara',
		'Rautavaara',
		'Rautavaara',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'689',
		'Rautjärvi',
		'Rautjärvi',
		'Rautjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'691',
		'Reisjärvi',
		'Reisjärvi',
		'Reisjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'694',
		'Riihimäki',
		'Riihimäki',
		'Riihimäki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'697',
		'Ristijärvi',
		'Ristijärvi',
		'Ristijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'698',
		'Rovaniemi',
		'Rovaniemi',
		'Rovaniemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'700',
		'Ruokolahti',
		'Ruokolahti',
		'Ruokolahti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'702',
		'Ruovesi',
		'Ruovesi',
		'Ruovesi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'704',
		'Rusko',
		'Rusko',
		'Rusko',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'707',
		'Rääkkylä',
		'Rääkkylä',
		'Bräkylä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'710',
		'Raasepori',
		'Raasepori',
		'Raseborg',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'729',
		'Saarijärvi',
		'Saarijärvi',
		'Saarijärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'732',
		'Salla',
		'Salla',
		'Salla',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'734',
		'Salo',
		'Salo',
		'Salo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'736',
		'Saltvik',
		'Saltvik',
		'Saltvik',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'738',
		'Sauvo',
		'Sauvo',
		'Sagu',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'739',
		'Savitaipale',
		'Savitaipale',
		'Savitaipale',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'740',
		'Savonlinna',
		'Savonlinna',
		'Nyslott',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'742',
		'Savukoski',
		'Savukoski',
		'Savukoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'743',
		'Seinäjoki',
		'Seinäjoki',
		'Seinäjoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'746',
		'Sievi',
		'Sievi',
		'Sievi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'747',
		'Siikainen',
		'Siikainen',
		'Siikainen',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'748',
		'Siikajoki',
		'Siikajoki',
		'Siikajoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'749',
		'Siilinjärvi',
		'Siilinjärvi',
		'Siilinjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'751',
		'Simo',
		'Simo',
		'Simo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'753',
		'Sipoo',
		'Sipoo',
		'Sibbo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'755',
		'Siuntio',
		'Siuntio',
		'Sjundeå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'758',
		'Sodankylä',
		'Sodankylä',
		'Sodankylä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'759',
		'Soini',
		'Soini',
		'Soini',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'761',
		'Somero',
		'Somero',
		'Somero',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'762',
		'Sonkajärvi',
		'Sonkajärvi',
		'Sonkajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'765',
		'Sotkamo',
		'Sotkamo',
		'Sotkamo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'766',
		'Sottunga',
		'Sottunga',
		'Sottunga',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'768',
		'Sulkava',
		'Sulkava',
		'Sulkava',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '10'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'771',
		'Sund',
		'Sund',
		'Sund',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'777',
		'Suomussalmi',
		'Suomussalmi',
		'Suomussalmi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '18'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'778',
		'Suonenjoki',
		'Suonenjoki',
		'Suonenjoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'781',
		'Sysmä',
		'Sysmä',
		'Sysmä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '7'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'783',
		'Säkylä',
		'Säkylä',
		'Säkylä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'785',
		'Vaala',
		'Vaala',
		'Vaala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'790',
		'Sastamala',
		'Sastamala',
		'Sastamala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'791',
		'Siikalatva',
		'Siikalatva',
		'Siikalatva',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'831',
		'Taipalsaari',
		'Taipalsaari',
		'Taipalsaari',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '9'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'832',
		'Taivalkoski',
		'Taivalkoski',
		'Taivalkoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'833',
		'Taivassalo',
		'Taivassalo',
		'Tövsala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'834',
		'Tammela',
		'Tammela',
		'Tammela',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'837',
		'Tampere',
		'Tampere',
		'Tammerfors',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'844',
		'Tervo',
		'Tervo',
		'Tervo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'845',
		'Tervola',
		'Tervola',
		'Tervola',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'846',
		'Teuva',
		'Teuva',
		'Östermark',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'848',
		'Tohmajärvi',
		'Tohmajärvi',
		'Tohmajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '12'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'849',
		'Toholampi',
		'Toholampi',
		'Toholampi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'850',
		'Toivakka',
		'Toivakka',
		'Toivakka',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'851',
		'Tornio',
		'Tornio',
		'Torneå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'853',
		'Turku',
		'Turku',
		'Åbo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'854',
		'Pello',
		'Pello',
		'Pello',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'857',
		'Tuusniemi',
		'Tuusniemi',
		'Tuusniemi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'858',
		'Tuusula',
		'Tuusula',
		'Tusby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'859',
		'Tyrnävä',
		'Tyrnävä',
		'Tyrnävä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'886',
		'Ulvila',
		'Ulvila',
		'Ulvsby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '4'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'887',
		'Urjala',
		'Urjala',
		'Urjala',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'889',
		'Utajärvi',
		'Utajärvi',
		'Utajärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'890',
		'Utsjoki',
		'Utsjoki',
		'Utsjoki',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'892',
		'Uurainen',
		'Uurainen',
		'Uurainen',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'893',
		'Uusikaarlepyy',
		'Uusikaarlepyy',
		'Nykarleby',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'895',
		'Uusikaupunki',
		'Uusikaupunki',
		'Nystad',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'905',
		'Vaasa',
		'Vaasa',
		'Vasa',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'908',
		'Valkeakoski',
		'Valkeakoski',
		'Valkeakoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'915',
		'Varkaus',
		'Varkaus',
		'Varkaus',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'918',
		'Vehmaa',
		'Vehmaa',
		'Vemo',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '2'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'921',
		'Vesanto',
		'Vesanto',
		'Vesanto',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'922',
		'Vesilahti',
		'Vesilahti',
		'Vesilahti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'924',
		'Veteli',
		'Veteli',
		'Vetil',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '16'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'925',
		'Vieremä',
		'Vieremä',
		'Vieremä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '11'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'927',
		'Vihti',
		'Vihti',
		'Vichtis',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '1'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'931',
		'Viitasaari',
		'Viitasaari',
		'Viitasaari',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'934',
		'Vimpeli',
		'Vimpeli',
		'Vimpeli',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'935',
		'Virolahti',
		'Virolahti',
		'Virolahti',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '8'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'936',
		'Virrat',
		'Virrat',
		'Virdois',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'941',
		'Vårdö',
		'Vårdö',
		'Vårdö',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '21'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'946',
		'Vöyri',
		'Vöyri',
		'Vörå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '15'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'976',
		'Ylitornio',
		'Ylitornio',
		'Övertorneå',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '19'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'977',
		'Ylivieska',
		'Ylivieska',
		'Ylivieska',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '17'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'980',
		'Ylöjärvi',
		'Ylöjärvi',
		'Ylöjärvi',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '6'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'981',
		'Ypäjä',
		'Ypäjä',
		'Ypäjä',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '5'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'989',
		'Ähtäri',
		'Ähtäri',
		'Etseri',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '14'
		),
		NOW(),
		NOW()
	),
	(
		'MUNICIPALITY',
		'992',
		'Äänekoski',
		'Äänekoski',
		'Äänekoski',
		(
			SELECT parent_id
			FROM temp_regions
			WHERE area_code = '13'
		),
		NOW(),
		NOW()
	);
CREATE TEMPORARY TABLE temp_municipalities (area_code VARCHAR(255), parent_id BIGINT);
INSERT INTO temp_municipalities (area_code, parent_id)
SELECT area_code,
	id
FROM fi_area
WHERE kind = 'MUNICIPALITY';
INSERT INTO fi_area (
		kind,
		area_code,
		area_name_en,
		area_name_fi,
		area_name_sv,
		parent_id,
		created_at,
		updated_at
	)
VALUES (
		'DISTRICT',
		'101',
		'Vironniemi',
		'Vironniemi',
		'Estnäs',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'102',
		'Ullanlinna',
		'Ullanlinna',
		'Ulrikasborg',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'103',
		'Kamppi',
		'Kamppi',
		'Kampen',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'104',
		'Taka-Töölö',
		'Taka-Töölö',
		'Bortre Tölö',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'105',
		'Lauttasaari',
		'Lauttasaari',
		'Drumso',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'201',
		'Reijola',
		'Reijola',
		'Grejus',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'202',
		'Munkkiniemi',
		'Munkkiniemi',
		'Munksnäs',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'203',
		'Haaga',
		'Haaga',
		'Haga',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'204',
		'Pitäjänmäki',
		'Pitäjänmäki',
		'Sockenbacka',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'205',
		'Kaarela',
		'Kaarela',
		'Kårböle',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'301',
		'Kallio',
		'Kallio',
		'Berghäll',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'302',
		'Alppiharju',
		'Alppiharju',
		'Åshöjden',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'303',
		'Vallila',
		'Vallila',
		'Vallgård',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'304',
		'Pasila',
		'Pasila',
		'Böle',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'305',
		'Vanhakaupunki',
		'Vanhakaupunki',
		'Gammelstaden',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'401',
		'Maunula',
		'Maunula',
		'Månsas',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'402',
		'Länsi-Pakila',
		'Länsi-Pakila',
		'Västra Baggböle',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'403',
		'Tuomarinkylä',
		'Tuomarinkylä',
		'Domarby',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'404',
		'Oulunkylä',
		'Oulunkylä',
		'Åggelby',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'405',
		'Itä-Pakila',
		'Itä-Pakila',
		'Östra Baggböle',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'501',
		'Latokartano',
		'Latokartano',
		'Ladugården',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'502',
		'Pukinmäki',
		'Pukinmäki',
		'Bocksbacka',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'503',
		'Malmi',
		'Malmi',
		'Malm',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'504',
		'Suutarila',
		'Suutarila',
		'Skomakarböle',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'505',
		'Puistola',
		'Puistola',
		'Parkstad',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'506',
		'Jakomäki',
		'Jakomäki',
		'Jakobacka',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'601',
		'Kulosaari',
		'Kulosaari',
		'Brändö',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'602',
		'Herttoniemi',
		'Herttoniemi',
		'Hertonäs',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'603',
		'Laajasalo',
		'Laajasalo',
		'Degerö',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'701',
		'Vartiokylä',
		'Vartiokylä',
		'Botby',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'702',
		'Myllypuro',
		'Myllypuro',
		'Kvarnbäcken',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'703',
		'Mellunkylä',
		'Mellunkylä',
		'Mellungsby',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'704',
		'Vuosaari',
		'Vuosaari',
		'Nordsjö',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	),
	(
		'DISTRICT',
		'801',
		'Östersundom',
		'Östersundom',
		'Östersundom',
		(
			SELECT parent_id
			FROM temp_municipalities
			WHERE area_code = '91'
		),
		NOW(),
		NOW()
	);
COMMIT;