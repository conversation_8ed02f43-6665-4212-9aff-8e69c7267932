START TRANSACTION;
DELETE FROM fi_location;
ALTER TABLE fi_location AUTO_INCREMENT = 1;

INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-<PERSON><PERSON>','Western Finland','Vastra Finland','14','Etela-Poh<PERSON>maa','South Ostrobothnia','Sodra <PERSON>','146','Jarviseu<PERSON>','Jarviseutu','Jarviseutu','5','Alajarvi','South Ostrobothnia','<PERSON><PERSON>',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','<PERSON><PERSON><PERSON>is- ja Ita-<PERSON><PERSON>','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','9','Alavieska','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','144','Kuusiokunnat','Kuusiokunnat','Kuusiokunnat','10','Alavus','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','16','Asikkala','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','15','Porvoo','Porvoo','Borga','18','Askola','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','19','Aura','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','63','Etela-Pirkanmaa','Etela-Pirkanmaa','Sodra Birkaland','20','Akaa','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','35','Brando','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','43','Eckero','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','103','Savonlinna','Savonlinna','Nyslott','46','Enonkoski','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','196','Tunturi-Lappi','Tunturi-Lappi','Tunturi-Lappi','47','Enontekio','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','49','Espoo','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','41','Rauma','Rauma','Raumo','50','Eura','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','41','Rauma','Rauma','Raumo','51','Eurajoki','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','146','Jarviseutu','Jarviseutu','Jarviseutu','52','Evijarvi','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','60','Finstrom','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','53','Forssa','Forssa','Forssa','61','Forssa','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','62','Foglo','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','65','Geta','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','176','Nivala-Haapajarvi','Nivala-Haapajarvi','Nivala-Haapajarvi','69','Haapajarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','175','Haapavesi-Siikalatva','Haapavesi-Siikalatva','Haapavesi-Siikalatva','71','Haapavesi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','72','Hailuoto','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','74','Halsua','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','82','Kotka-Hamina','Kotka-Hamina','Kotka-Fredrikshamn','75','Hamina','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','76','Hammarland','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','77','Hankasalmi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','14','Raasepori','Raasepori','Raseborg','78','Hanko','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','79','Harjavalta','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','81','Hartola','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','51','Hameenlinna','Hameenlinna','Tavastehus','82','Hattula','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','52','Riihimaki','Riihimaki','Riihimaki','86','Hausjarvi','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','90','Heinavesi','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','92','Vantaa','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','97','Hirvensalmi','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','98','Hollola','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','102','Huittinen','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','53','Forssa','Forssa','Forssa','103','Humppila','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','181','Kehys-Kainuu','Kehys-Kainuu','Kehys-Kainuu','105','Hyrynsalmi','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','106','Hyvinkaa','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','108','Hameenkyro','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','51','Hameenlinna','Hameenlinna','Tavastehus','109','Hameenlinna','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','111','Heinola','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','173','Oulunkaari','Oulunkaari','Oulunkaari','139','Ii','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','140','Iisalmi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','142','Iitti','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','61','Luoteis-Pirkanmaa','Luoteis-Pirkanmaa','Nordvastra Birkaland','143','Ikaalinen','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','145','Ilmajoki','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','146','Ilomantsi','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','197','Pohjois-Lappi','Pohjois-Lappi','Norra Lappland','148','Inari','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','14','Raasepori','Raasepori','Raseborg','149','Inkoo','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','141','Suupohja','Suupohja','Suupohja','151','Isojoki','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','152','Isokyro','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','93','Imatra','Imatra','Imatra','153','Imatra','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','51','Hameenlinna','Hameenlinna','Tavastehus','165','Janakkala','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','167','Joensuu','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','53','Forssa','Forssa','Forssa','169','Jokioinen','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','170','Jomala','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','114','Varkaus','Varkaus','Varkaus','171','Joroinen','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','132','Joutsa','Joutsa','Joutsa','172','Joutsa','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','176','Juuka','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','69','Yla-Pirkanmaa','Yla-Pirkanmaa','Ovre Birkaland','177','Juupajoki','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','105','Pieksamaki','Pieksamaki','Pieksamaki','178','Juva','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','179','Jyvaskyla','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','44','Pohjois-Satakunta','Pohjois-Satakunta','Norra Satakunta','181','Jamijarvi','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','134','Jamsa','Jamsa','Jamsa','182','Jamsa','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','186','Jarvenpaa','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','202','Kaarina','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','113','Koillis-Savo','Koillis-Savo','Nordostra Savolax','204','Kaavi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','182','Kajaani','Kajaani','Kajana','205','Kajaani','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','208','Kalajoki','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','211','Kangasala','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','213','Kangasniemi','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','44','Pohjois-Satakunta','Pohjois-Satakunta','Norra Satakunta','214','Kankaanpaa','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','216','Kannonkoski','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','162','Kokkola','Kokkola','Karleby','217','Kannus','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','141','Suupohja','Suupohja','Suupohja','218','Karijoki','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','224','Karkkila','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','226','Karstula','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','44','Pohjois-Satakunta','Pohjois-Satakunta','Norra Satakunta','230','Karvia','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','153','Sydosterbotten','Sydosterbotten','Sydosterbotten','231','Kaskinen','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','141','Suupohja','Suupohja','Suupohja','232','Kauhajoki','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','233','Kauhava','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','235','Kauniainen','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','236','Kaustinen','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','239','Keitele','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','192','Kemi-Tornio','Kemi-Tornio','Kemi-Tornea','240','Kemi','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','192','Kemi-Tornio','Kemi-Tornio','Kemi-Tornea','241','Keminmaa','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','244','Kempele','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','245','Kerava','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','133','Keuruu','Keuruu','Keuru','249','Keuruu','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','61','Luoteis-Pirkanmaa','Luoteis-Pirkanmaa','Nordvastra Birkaland','250','Kihnio','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','256','Kinnula','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','257','Kirkkonummi','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','124','Keski-Karjala','Keski-Karjala','Mellersta Karelen','260','Kitee','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','196','Tunturi-Lappi','Tunturi-Lappi','Tunturi-Lappi','261','Kittila','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','263','Kiuruvesi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','265','Kivijarvi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','271','Kokemaki','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','162','Kokkola','Kokkola','Karleby','272','Kokkola','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','196','Tunturi-Lappi','Tunturi-Lappi','Tunturi-Lappi','273','Kolari','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','135','Aanekoski','Aanekoski','Aanekoski','275','Konnevesi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','276','Kontiolahti','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','280','Korsnas','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','284','Koski Tl','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','82','Kotka-Hamina','Kotka-Hamina','Kotka-Fredrikshamn','285','Kotka','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','81','Kouvola','Kouvola','Kouvola','286','Kouvola','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','153','Sydosterbotten','Sydosterbotten','Sydosterbotten','287','Kristiinankaupunki','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','154','Jakobstadsregionen','Jakobstadsregionen','Jakobstadsregionen','288','Kruunupyy','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','181','Kehys-Kainuu','Kehys-Kainuu','Kehys-Kainuu','290','Kuhmo','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','291','Kuhmoinen','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','295','Kumlinge','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','112','Kuopio','Kuopio','Kuopio','297','Kuopio','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','144','Kuusiokunnat','Kuusiokunnat','Kuusiokunnat','300','Kuortane','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','301','Kurikka','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','304','Kustavi','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','178','Koillismaa','Koillismaa','Koillismaa','305','Kuusamo','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','309','Outokumpu','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','312','Kyyjarvi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','316','Karkola','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','176','Nivala-Haapajarvi','Nivala-Haapajarvi','Nivala-Haapajarvi','317','Karsamaki','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','318','Kokar','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','194','Ita-Lappi','Ita-Lappi','Ostra Lappland','320','Kemijarvi','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','21','Aboland-Turunmaa','Aboland-Turunmaa','Aboland-Turunmaa','322','Kemionsaari','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','398','Lahti','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','399','Laihia','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','400','Laitila','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','402','Lapinlahti','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','146','Jarviseutu','Jarviseutu','Jarviseutu','403','Lappajarvi','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','91','Lappeenranta','Lappeenranta','Villmanstrand','405','Lappeenranta','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','16','Loviisa','Loviisa','Lovisa','407','Lapinjarvi','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','408','Lapua','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','410','Laukaa','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','91','Lappeenranta','Lappeenranta','Villmanstrand','416','Lemi','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','417','Lemland','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','418','Lempaala','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','114','Varkaus','Varkaus','Varkaus','420','Leppavirta','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','421','Lestijarvi','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','125','Pielisen Karjala','Pielisen Karjala','Pielisen Karjala','422','Lieksa','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','423','Lieto','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','425','Liminka','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','426','Liperi','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','430','Loimaa','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','52','Riihimaki','Riihimaki','Riihimaki','433','Loppi','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','16','Loviisa','Loviisa','Lovisa','434','Loviisa','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','132','Joutsa','Joutsa','Joutsa','435','Luhanka','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','436','Lumijoki','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','438','Lumparland','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','154','Jakobstadsregionen','Jakobstadsregionen','Jakobstadsregionen','440','Luoto','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','91','Lappeenranta','Lappeenranta','Villmanstrand','441','Luumaki','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','444','Lohja','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','21','Aboland-Turunmaa','Aboland-Turunmaa','Aboland-Turunmaa','445','Parainen','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','475','Maalahti','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','211','Mariehamns stad','Mariehamns stad','Mariehamns stad','478','Maarianhamina - Mariehamn','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','480','Marttila','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','481','Masku','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','483','Merijarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','484','Merikarvia','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','82','Kotka-Hamina','Kotka-Hamina','Kotka-Fredrikshamn','489','Miehikkala','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','491','Mikkeli','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','494','Muhos','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','133','Keuruu','Keuruu','Keuru','495','Multia','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','196','Tunturi-Lappi','Tunturi-Lappi','Tunturi-Lappi','498','Muonio','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','499','Mustasaari','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','500','Muurame','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','503','Mynamaki','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','15','Porvoo','Porvoo','Borga','504','Myrskyla','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','505','Mantsala','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','507','Mantyharju','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','69','Yla-Pirkanmaa','Yla-Pirkanmaa','Ovre Birkaland','508','Mantta-Vilppula','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','529','Naantali','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','531','Nakkila','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','176','Nivala-Haapajarvi','Nivala-Haapajarvi','Nivala-Haapajarvi','535','Nivala','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','536','Nokia','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','538','Nousiainen','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','125','Pielisen Karjala','Pielisen Karjala','Pielisen Karjala','541','Nurmes','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','543','Nurmijarvi','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','153','Sydosterbotten','Sydosterbotten','Sydosterbotten','545','Narpio','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','560','Orimattila','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','561','Oripaa','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','562','Orivesi','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','563','Oulainen','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','564','Oulu','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','576','Padasjoki','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','577','Paimio','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','182','Kajaani','Kajaani','Kajana','578','Paltamo','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','93','Imatra','Imatra','Imatra','580','Parikkala','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','61','Luoteis-Pirkanmaa','Luoteis-Pirkanmaa','Nordvastra Birkaland','581','Parkano','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','194','Ita-Lappi','Ita-Lappi','Ostra Lappland','583','Pelkosenniemi','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','584','Perho','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','588','Pertunmaa','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','592','Petajavesi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','105','Pieksamaki','Pieksamaki','Pieksamaki','593','Pieksamaki','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','595','Pielavesi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','154','Jakobstadsregionen','Jakobstadsregionen','Jakobstadsregionen','598','Pietarsaari','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','154','Jakobstadsregionen','Jakobstadsregionen','Jakobstadsregionen','599','Pedersoren kunta','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','601','Pihtipudas','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','604','Pirkkala','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','122','Joensuu','Joensuu','Joensuu','607','Polvijarvi','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','608','Pomarkku','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','609','Pori','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','611','Pornainen','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','194','Ita-Lappi','Ita-Lappi','Ostra Lappland','614','Posio','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','173','Oulunkaari','Oulunkaari','Oulunkaari','615','Pudasjarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','15','Porvoo','Porvoo','Borga','616','Pukkila','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','68','Lounais-Pirkanmaa','Lounais-Pirkanmaa','Sydvastra Birkaland','619','Punkalaidun','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','181','Kehys-Kainuu','Kehys-Kainuu','Kehys-Kainuu','620','Puolanka','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','101','Mikkeli','Mikkeli','S:t Michel','623','Puumala','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','82','Kotka-Hamina','Kotka-Hamina','Kotka-Fredrikshamn','624','Pyhtaa','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','174','Raahe','Raahe','Brahestad','625','Pyhajoki','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','176','Nivala-Haapajarvi','Nivala-Haapajarvi','Nivala-Haapajarvi','626','Pyhajarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','175','Haapavesi-Siikalatva','Haapavesi-Siikalatva','Haapavesi-Siikalatva','630','Pyhanta','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','631','Pyharanta','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','635','Palkane','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','25','Loimaa','Loimaa','Loimaa','636','Poytya','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','15','Porvoo','Porvoo','Borga','638','Porvoo','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','174','Raahe','Raahe','Brahestad','678','Raahe','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','680','Raisio','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','103','Savonlinna','Savonlinna','Nyslott','681','Rantasalmi','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','191','Rovaniemi','Rovaniemi','Rovaniemi','683','Ranua','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','41','Rauma','Rauma','Raumo','684','Rauma','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','115','Sisa-Savo','Sisa-Savo','Inre Savolax','686','Rautalampi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','113','Koillis-Savo','Koillis-Savo','Nordostra Savolax','687','Rautavaara','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','93','Imatra','Imatra','Imatra','689','Rautjarvi','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','176','Nivala-Haapajarvi','Nivala-Haapajarvi','Nivala-Haapajarvi','691','Reisjarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','52','Riihimaki','Riihimaki','Riihimaki','694','Riihimaki','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','182','Kajaani','Kajaani','Kajana','697','Ristijarvi','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','191','Rovaniemi','Rovaniemi','Rovaniemi','698','Rovaniemi','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','93','Imatra','Imatra','Imatra','700','Ruokolahti','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','69','Yla-Pirkanmaa','Yla-Pirkanmaa','Ovre Birkaland','702','Ruovesi','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','704','Rusko','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','124','Keski-Karjala','Keski-Karjala','Mellersta Karelen','707','Raakkyla','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','14','Raasepori','Raasepori','Raseborg','710','Raasepori','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','729','Saarijarvi','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','194','Ita-Lappi','Ita-Lappi','Ostra Lappland','732','Salla','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','22','Salo','Salo','Salo','734','Salo','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','736','Saltvik','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','738','Sauvo','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','91','Lappeenranta','Lappeenranta','Villmanstrand','739','Savitaipale','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','103','Savonlinna','Savonlinna','Nyslott','740','Savonlinna','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','194','Ita-Lappi','Ita-Lappi','Ostra Lappland','742','Savukoski','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','142','Seinajoki','Seinajoki','Seinajoki','743','Seinajoki','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','746','Sievi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','44','Pohjois-Satakunta','Pohjois-Satakunta','Norra Satakunta','747','Siikainen','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','174','Raahe','Raahe','Brahestad','748','Siikajoki','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','112','Kuopio','Kuopio','Kuopio','749','Siilinjarvi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','192','Kemi-Tornio','Kemi-Tornio','Kemi-Tornea','751','Simo','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','753','Sipoo','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','755','Siuntio','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','197','Pohjois-Lappi','Pohjois-Lappi','Norra Lappland','758','Sodankyla','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','146','Jarviseutu','Jarviseutu','Jarviseutu','759','Soini','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','22','Salo','Salo','Salo','761','Somero','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','762','Sonkajarvi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','182','Kajaani','Kajaani','Kajana','765','Sotkamo','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','766','Sottunga','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','10','Etela-Savo','South Savo','Sodra Savolax','103','Savonlinna','Savonlinna','Nyslott','768','Sulkava','South Savo','Sodra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','212','Alands landsbygd','Alands landsbygd','Alands landsbygd','771','Sund','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','18','Kainuu','Kainuu','Kajanaland','181','Kehys-Kainuu','Kehys-Kainuu','Kehys-Kainuu','777','Suomussalmi','Kainuu','Kajanaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','115','Sisa-Savo','Sisa-Savo','Inre Savolax','778','Suonenjoki','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','7','Paijat-Hame','Paijat-Hame','Paijanne-Tavastland','71','Lahti','Lahti','Lahtis','781','Sysma','Paijat-Hame','Paijanne-Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','41','Rauma','Rauma','Raumo','783','Sakyla','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','173','Oulunkaari','Oulunkaari','Oulunkaari','785','Vaala','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','68','Lounais-Pirkanmaa','Lounais-Pirkanmaa','Sydvastra Birkaland','790','Sastamala','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','175','Haapavesi-Siikalatva','Haapavesi-Siikalatva','Haapavesi-Siikalatva','791','Siikalatva','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','9','Etela-Karjala','South Karelia','Sodra Karelen','91','Lappeenranta','Lappeenranta','Villmanstrand','831','Taipalsaari','South Karelia','Sodra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','178','Koillismaa','Koillismaa','Koillismaa','832','Taivalkoski','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','833','Taivassalo','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','53','Forssa','Forssa','Forssa','834','Tammela','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','837','Tampere','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','115','Sisa-Savo','Sisa-Savo','Inre Savolax','844','Tervo','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','192','Kemi-Tornio','Kemi-Tornio','Kemi-Tornea','845','Tervola','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','141','Suupohja','Suupohja','Suupohja','846','Teuva','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','12','Pohjois-Karjala','North Karelia','Norra Karelen','124','Keski-Karjala','Keski-Karjala','Mellersta Karelen','848','Tohmajarvi','North Karelia','Norra Karelen',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','849','Toholampi','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','850','Toivakka','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','192','Kemi-Tornio','Kemi-Tornio','Kemi-Tornea','851','Tornio','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','23','Turku','Turku','Abo','853','Turku','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','193','Torniolaakso','Torniolaakso','Tornedalen','854','Pello','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','113','Koillis-Savo','Koillis-Savo','Nordostra Savolax','857','Tuusniemi','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','858','Tuusula','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','171','Oulu','Oulu','Uleaborg','859','Tyrnava','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','4','Satakunta','Satakunta','Satakunta','43','Pori','Pori','Bjorneborg','886','Ulvila','Satakunta','Satakunta',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','63','Etela-Pirkanmaa','Etela-Pirkanmaa','Sodra Birkaland','887','Urjala','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','173','Oulunkaari','Oulunkaari','Oulunkaari','889','Utajarvi','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','197','Pohjois-Lappi','Pohjois-Lappi','Norra Lappland','890','Utsjoki','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','131','Jyvaskyla','Jyvaskyla','Jyvaskyla','892','Uurainen','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','154','Jakobstadsregionen','Jakobstadsregionen','Jakobstadsregionen','893','Uusikaarlepyy','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','895','Uusikaupunki','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','905','Vaasa','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','63','Etela-Pirkanmaa','Etela-Pirkanmaa','Sodra Birkaland','908','Valkeakoski','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','114','Varkaus','Varkaus','Varkaus','915','Varkaus','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','2','Varsinais-Suomi','Southwest Finland','Egentliga Finland','24','Vakka-Suomi','Vakka-Suomi','Nystadsregionen','918','Vehmaa','Southwest Finland','Egentliga Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','115','Sisa-Savo','Sisa-Savo','Inre Savolax','921','Vesanto','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','922','Vesilahti','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','16','Keski-Pohjanmaa','Central Ostrobothnia','Mellersta Osterbotten','161','Kaustinen','Kaustinen','Kaustby','924','Veteli','Central Ostrobothnia','Mellersta Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','11','Pohjois-Savo','North Savo','Norra Savolax','111','Yla-Savo','Yla-Savo','Norra Savolax','925','Vierema','North Savo','Norra Savolax',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','927','Vihti','Uusimaa','Nyland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','138','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','Saarijarvi-Viitasaari','931','Viitasaari','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','146','Jarviseutu','Jarviseutu','Jarviseutu','934','Vimpeli','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','8','Kymenlaakso','Kymenlaakso','Kymmenedalen','82','Kotka-Hamina','Kotka-Hamina','Kotka-Fredrikshamn','935','Virolahti','Kymenlaakso','Kymmenedalen',NULL,NULL,NULL,NULL, NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','69','Yla-Pirkanmaa','Yla-Pirkanmaa','Ovre Birkaland','936','Virrat','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','5','Ahvenanmaa - Aland','Aland','Aland','21','Ahvenanmaa','Aland','Aland','213','Alands skargard','Alands skargard','Alands skargard','941','Vardo','Aland','Aland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','15','Pohjanmaa','Ostrobothnia','Osterbotten','152','Vaasa','Vaasa','Vasa','946','Voyri','Ostrobothnia','Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','19','Lappi','Lapland','Lappland','193','Torniolaakso','Torniolaakso','Tornedalen','976','Ylitornio','Lapland','Lappland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','4','Pohjois- ja Ita-Suomi','Northern and Eastern Finland','Norra och Ostra Finland','17','Pohjois-Pohjanmaa','North Ostrobothnia','Norra Osterbotten','177','Ylivieska','Ylivieska','Ylivieska','977','Ylivieska','North Ostrobothnia','Norra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','6','Pirkanmaa','Pirkanmaa','Birkaland','64','Tampere','Tampere','Tammerfors','980','Ylojarvi','Pirkanmaa','Birkaland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','2','Etela-Suomi','Southern Finland','Sodra Finland','5','Kanta-Hame','Kanta-Hame','Egentliga Tavastland','53','Forssa','Forssa','Forssa','981','Ypaja','Kanta-Hame','Egentliga Tavastland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','14','Etela-Pohjanmaa','South Ostrobothnia','Sodra Osterbotten','144','Kuusiokunnat','Kuusiokunnat','Kuusiokunnat','989','Ahtari','South Ostrobothnia','Sodra Osterbotten',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('MUNICIPALITY','3','Lansi-Suomi','Western Finland','Vastra Finland','13','Keski-Suomi','Central Finland','Mellersta Finland','135','Aanekoski','Aanekoski','Aanekoski','992','Aanekoski','Central Finland','Mellersta Finland',NULL,NULL,NULL,NULL, NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','101','Vironniemi','Vironniemi','Estnas', NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','102','Ullanlinna','Ullanlinna','Ulrikasborg', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','103','Kampinmalmi','Kamppi','Kampmalmen', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','104','Taka-Toolo','Taka-Toolo','Bortre Tolo', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','105','Lauttasaari','Lauttasaari','Drumso', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','201','Reijola','Reijola','Grejus', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','202','Munkkiniemi','Munkkiniemi','Munksnas', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','203','Haaga','Haaga','Haga', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','204','Pitajanmaki','Pitajanmaki','Sockenbacka', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','205','Kaarela','Kaarela','Karbole', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','301','Kallio','Kallio','Berghall', NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','302','Alppiharju','Alppiharju','Ashojden', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','303','Vallila','Vallila','Vallgard', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','304','Pasila','Pasila','Bole', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','305','Vanhakaupunki','Vanhakaupunki','Gammelstaden', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','401','Maunula','Maunula','Monsas', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','402','Lansi-Pakila','Lansi-Pakila','Vastra Baggbole', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','403','Tuomarinkyla','Tuomarinkyla','Domarby', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','404','Oulunkyla','Oulunkyla','Aggelby', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','405','Ita-Pakila','Ita-Pakila','Ostra Baggbole', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','501','Latokartano','Latokartano','Ladugarden', NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','502','Pukinmaki','Pukinmaki','Bocksbacka', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','503','Malmi','Malmi','Malm', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','504','Suutarila','Suutarila','Skomakarbole', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','505','Puistola','Puistola','Parkstad', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','506','Jakomaki','Jakomaki','Jakobacka', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','601','Kulosaari','Kulosaari','Brando', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','602','Herttoniemi','Herttoniemi','Hertonas', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','603','Laajasalo','Laajasalo','Degero', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','701','Vartiokyla','Vartiokyla','Botby', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','702','Myllypuro','Myllypuro','Kvarnbacken', NOW(), NOW());
INSERT INTO fi_location (kind,major_region_code,major_region_name_fi,major_region_name_en,major_region_name_sv,region_code,region_name_fi,region_name_en,region_name_sv,subregion_code,subregion_name_fi,subregion_name_en,subregion_name_sv,municipality_code,municipality_name_fi,municipality_name_en,municipality_name_sv,district_code,district_name_fi,district_name_en,district_name_sv,created_at,updated_at) VALUES
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','703','Mellunkyla','Mellunkyla','Mellungsby', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','704','Vuosaari','Vuosaari','Nordsjo', NOW(), NOW()),
	 ('DISTRICT','1','Helsinki-Uusimaa','Helsinki-Uusimaa','Helsingfors - Nyland','1','Uusimaa','Uusimaa','Nyland','11','Helsinki','Helsinki','Helsingfors','91','Helsinki','Uusimaa','Nyland','801','Ostersundom','Ostersundom','Ostersundom', NOW(), NOW());


COMMIT;