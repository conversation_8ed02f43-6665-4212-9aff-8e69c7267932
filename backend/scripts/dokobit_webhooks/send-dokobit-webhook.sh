#!/bin/bash

# Set your webhook endpoint here
WEBHOOK_URL="http://localhost:6543/dokobit/webhooks/signing-event"

# Call example:  ./send-dokobit-webhook.sh signing_completed_zip 4641df1aeed9f5d3206c0287c5726eb9ffc2d8ca c36cafa9623540fb519190eb7d11e973f8846096

# You can get the token and signer token from the database
# SELECT
#     CONCAT(signing.document_external_id, ' ', signer.signing_external_id)  AS token_and_signer_token,
#     signing.status,
#     signing.created_at
# FROM
#     document_signing signing
#         LEFT JOIN
#     document_signer signer ON signer.document_signing_id = signing.id;

# Required arguments
ACTION="$1"
TOKEN="$2"
SIGNER_TOKEN="$3"

# Path to the JSON template
TEMPLATE_FILE="payloads/${ACTION}.json"

# Validation
if [ -z "$ACTION" ] || [ -z "$TOKEN" ]; then
  echo "❌ Usage: $0 {action} {TOKEN} [SIGNER_TOKEN]"
  echo "   e.g.: $0 signer_signed 9086abc1234 def456"
  exit 1
fi

if [ ! -f "$TEMPLATE_FILE" ]; then
  echo "❌ Template file not found: $TEMPLATE_FILE"
  exit 1
fi

# Prepare payload by substituting template variables
PAYLOAD=$(<"$TEMPLATE_FILE")
PAYLOAD="${PAYLOAD//\{\{TOKEN\}\}/$TOKEN}"
PAYLOAD="${PAYLOAD//\{\{SIGNER_TOKEN\}\}/$SIGNER_TOKEN}"

# Send the request
echo "📤 Sending $ACTION webhook to $WEBHOOK_URL..."
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD"

echo -e "\n✅ Done."



