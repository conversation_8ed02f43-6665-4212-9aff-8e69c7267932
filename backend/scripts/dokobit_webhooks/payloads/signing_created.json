{"action": "signing_created", "token": "{{TOKEN}}", "template": null, "name": "Sales Agreement 37", "type": "pdf", "status": "pending", "date_created": "2025-06-04T09:02:05+03:00", "deadline": "2025-06-04T23:59:59+03:00", "files": [{"name": "sales_agreement_37.pdf", "url": "https://beta.dokobit.com/api/signing-external/46c3bdd064a6eb612484206cbe404a2aa82c069caa5997020255affe2a41497e/file-download", "type": null, "mime_type": "application/pdf"}], "signers": [{"token": "{{SIGNER_TOKEN}}", "type": "signer", "status": "pending", "signing_method": null, "signature": [], "meta_information": {"company": null, "position": null, "signing_purpose": null, "signing_location": null, "country": null, "city": null, "postal_code": null, "subdivision": null}, "is_qualified_electronic_signature": false, "first_name": "<PERSON>", "last_name": "Peltonen", "code": null, "country": "fi", "email": "<EMAIL>", "is_in_trash_bin": false}], "is_qualified_signature_required": false}