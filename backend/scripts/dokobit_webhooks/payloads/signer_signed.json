{"action": "signer_signed", "token": "{{TOKEN}}", "template": null, "signer_token": "{{SIGNER_TOKEN}}", "file": "https://beta.dokobit.com/api/signing-external/a0a2320588b82bf70551f3c59e06f35fb12cc250/download", "name": "Sales Agreement 37", "type": "pdf", "status": "completed", "date_created": "2025-06-04T09:02:05+03:00", "deadline": "2025-06-04T23:59:59+03:00", "files": [{"name": "sales_agreement_37.pdf", "url": "https://beta.dokobit.com/api/signing-external/46c3bdd064a6eb612484206cbe404a2aa82c069caa5997020255affe2a41497e/file-download", "type": null, "mime_type": "application/pdf"}], "signers": [{"token": "{{SIGNER_TOKEN}}", "type": "signer", "status": "signed", "signing_method": null, "signature": {"signing_time": "2025-06-04T09:03:38+03:00", "certificate": {"owner": "<PERSON><PERSON>", "issuer": "TEST of SK ID Solutions ORG 2021E, SK ID Solutions AS, EE", "valid_from": "2024-11-20 14:42:46", "valid_to": "2027-12-20 14:42:45", "qualified": true, "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "level": "PAdES-LT", "timestamp": {"qualified": true}, "timemark": [], "seal": false, "errors": [], "warnings": []}, "meta_information": {"company": null, "position": null, "signing_purpose": null, "signing_location": null, "country": null, "city": null, "postal_code": null, "subdivision": null}, "is_qualified_electronic_signature": true, "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "code": "030883-925M", "country": "fi", "email": "<EMAIL>", "is_in_trash_bin": false}], "is_qualified_signature_required": false, "signed_file": [{"url": "https://beta.dokobit.com/api/signing-external/a0a2320588b82bf70551f3c59e06f35fb12cc250/download"}]}