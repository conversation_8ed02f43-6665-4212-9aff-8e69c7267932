#!/bin/bash
set -e

echo "⏳ Waiting for LocalStack to be ready..."
until curl -s http://localhost:4566/_localstack/health | grep '"s3": "available"'; do
  sleep 2
done

echo "✅ LocalStack is ready. Creating buckets..."

awslocal s3api create-bucket --bucket strand-files --create-bucket-configuration LocationConstraint=eu-west-1
awslocal s3api put-bucket-cors --bucket strand-files --cors-configuration '{
  "CORSRules": [
    {
      "AllowedOrigins": ["http://localhost:3000"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": [],
      "MaxAgeSeconds": 3000
    }
  ]
}'

awslocal s3api create-bucket --bucket strand-media --create-bucket-configuration LocationConstraint=eu-west-1
awslocal s3api put-bucket-cors --bucket strand-media --cors-configuration '{
  "CORSRules": [
    {
      "AllowedOrigins": ["http://localhost:3000"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": [],
      "MaxAgeSeconds": 3000
    }
  ]
}'

echo "✅ Buckets and CORS config created."