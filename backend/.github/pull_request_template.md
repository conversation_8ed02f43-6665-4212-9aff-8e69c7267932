#
<!-- 
  These are guidelines for a better understanding and documentation of the PRs of the project.

  If you PR doesn't require a section, just remove it.

  Sections marked with an asterisk (*) are required.
-->

## What*
<!--
  (REQUIRED) Describe in a simple way what your PR is about. 

  Example:
  This PR handles the status change of a property in the details page.
-->

## Current behavior*

<!--
  (R<PERSON><PERSON><PERSON>RED) Please describe the current behavior of the code before the changes in this pull request is applied.

  Example:
  User has no option to change the status of a property in the details page.
-->

## Proposed changes*

<!--
  (REQUIRED) Please describe the changes proposed in this pull request.

  If this pull request resolves an already recorded bug or a feature request, please add a link to that issue.

  Example:
  New component `Modal` is added to the details page that lets users change the status of the property.
-->

### Workflow

<!--
  (OPTIONAL) If you wanna describe how the user will interact with these changes.

  Example:
  1) User selects a property from the list of properties;
  2) User clicks on Change status in the Property Details page;
  3) Modal pops up and then user can select between 'Archived' or 'Published' status.
-->

## Requirements

<!-- 
  (OPTIONAL) If this PR depends on something (let's say another PR to be merged first in this or another repository), here is where you can inform it.
-->

## Checklist*

<!-- (REQUIRED) To help us review and merge this pull request quickly, please confirm the following (when applicable):  -->

- [ ] I have included tests
- [ ] I have updated the documentation (README, OpenAPI, function comments)
- [ ] I have removed hanging `logs` (for example `console.log()` for debug purposes)
- [ ] I have included meaningful `TODO` comments when the flow is not yet completely defined so cannot be totally completed
