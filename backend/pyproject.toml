[tool.poetry]
name = "strandproperties"
version = "0.1.0"
description = "Strand Properties backend monolith"
authors = [
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON><PERSON> <guilherme.per<PERSON>@reaktor.com>",
    "<PERSON><PERSON><PERSON><PERSON> <ab<PERSON><PERSON><PERSON>.el<PERSON><PERSON>@reaktor.com>",
    "<PERSON> <<EMAIL>>",
]
readme = "README.md"
requires-poetry = ">=2.1"

[tool.poetry.dependencies]
python = "^3.11"
tet = "0.4.1"
gunicorn = "^22.0.0"
alembic = "^1.12.0"
zope-sqlalchemy = "^3.1"
pyramid-tm = "^2.5"
pymysql = "^1.1.1"
pydantic = "^2.4.2"
pyramid-apispec = "^0.5.0"
apispec-pydantic-plugin = "^0.4.2"
pyramid-jwt = "^1.6.1"
typer = "^0.9.0"
email-validator = "^2.0.0.post2"
boto3 = "^1.28.66"
python-slugify = "^8.0.1"
requests-toolbelt = "^1.0.0"
num2words = "^0.5.13"
python-json-logger = "^2.0.7"
pydantic-settings = "^2.0.3"
httpx = "^0.25.1"
unidecode = "^1.3.7"
requests-oauthlib = "^2.0.0"
jsonschema = "^4.22.0"
pytz = "^2024.1"
jinja2 = "^3.1.4"
sentry-sdk = "^2.13.0"
cryptography = "^44.0.1"
weasyprint = "^63.1"
stripe = "^11.6.0"
sqlalchemy-utils = "^0.41.2"
huey = "^2.5.3"
redis = "^6.2.0"
cronitor = "^4.7.1"
pypdf = "^6.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-mock = "^3.14.1"
webtest = "^3.0.0"
pytest-sugar = "^1.0.0"
pyramid-debugtoolbar = "^4.10"
requests-mock = "^1.11.0"
hubspot-api-client = "^9.0.0"
pre-commit = "^3.8.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
target-version = ["py311"]

[tool.isort]
profile = "black"

[tool.pytest.ini_options]
addopts = "--capture=no --log-level=INFO --log-cli-level=INFO --tb=native"
testpaths = ["strandproperties/tests"]
filterwarnings = ["ignore::DeprecationWarning"]

[tool.poetry.scripts]
worker = "strandproperties.scripts.worker:WorkerScript.cli"
user = "strandproperties.scripts.user:UserScript.cli"
contact = "strandproperties.scripts.contact:ContactScript.cli"
organization = "strandproperties.scripts.organization:OrganizationScript.cli"
seed = "strandproperties.scripts.seed:SeedScript.cli"
seed_fi = "strandproperties.scripts.seed_fi_property_data:SeedScript.cli"
migrate = "strandproperties.scripts.migrate:MigrateScript.cli"
inmobalia = "strandproperties.scripts.inmobalia:InmobaliaScript.cli"
leadimport = "strandproperties.scripts.lead_hubspot_import:LeadImportScript.cli"
idealista = "strandproperties.libs.idealista.json_builder:IdealistaJSONBuilder.cli"
rawleaddata = "strandproperties.scripts.raw_lead_data:RawLeadDataScript.cli"
template = "strandproperties.scripts.template:TemplateScript.cli"
leadprocess = "strandproperties.scripts.lead_process:LeadProcessScript.cli"
properties_newsletter = "strandproperties.scripts.properties_newsletter:PropertiesNewsletterScript.cli"
webhook_integration = "strandproperties.scripts.webhook_integration:WebhookIntegrationScript.cli"
properties_match_making_update = "strandproperties.scripts.properties_match_making_update:PropertiesMatchMakingUpdateScript.cli"
update_contact_and_sales_activity = "strandproperties.scripts.source_contact_and_sale_activity:SourceContactAndSaleActivityScript.cli"
import_fi_property_from_kivi = "strandproperties.scripts.kivi_fi_property_import:ImportFIPropertyFromKiviScript.cli"
migrate_roaiib_user = "strandproperties.scripts.migrate_roaiib_user:MigrateRoaiibUserScript.cli"
resales_s3_sync = "strandproperties.scripts.resales_s3_sync:ResalesS3Sync.cli"
take_off_unpublished_properties = "strandproperties.scripts.take_off_unpublished_properties:TakeOffUnpublishedPropertiesScript.cli"
move_properties_to_draft = "strandproperties.scripts.move_list_properties_to_archive_draf_state:MovePropertiesToDraftScript.cli"
es_idealista_feed_based_office = "strandproperties.scripts.es_idealista_feed_based_office:EsIdealistaFeedBasedOffice.cli"
