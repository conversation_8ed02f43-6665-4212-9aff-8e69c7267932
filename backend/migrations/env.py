from logging.config import fileConfig

from alembic import context
from sqlalchemy import create_engine, pool

from strandproperties.config import app_cfg
from strandproperties.models.base import Base, _scan_models

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)


def _run_migrations_online(context, target_metadata) -> None:
    engine = create_engine(app_cfg.db_url(), poolclass=pool.NullPool)

    with engine.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


def _run_migrations_offline(context, target_metadata) -> None:
    """Run migrations in 'offline' mode."""
    context.configure(
        url=app_cfg.db_url(),
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    context.run_migrations()


def run_migrations():
    _scan_models(app_cfg.db_models_dotted_path)
    if context.is_offline_mode():
        _run_migrations_offline(context, Base.metadata)
    else:
        _run_migrations_online(context, Base.metadata)


run_migrations()
