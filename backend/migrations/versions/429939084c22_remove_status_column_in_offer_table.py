"""Remove status column in Offer table

Revision ID: 429939084c22
Revises: 8005efe52b6f
Create Date: 2024-07-09 08:03:33.081018

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "429939084c22"
down_revision = "8005efe52b6f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("offer", "status")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "offer",
        sa.Column(
            "status",
            mysql.VARCHAR(length=10),
            nullable=False,
            server_default=("pending"),
        ),
    )

    # ### end Alembic commands ###
