"""add new ownertype

Revision ID: 3326cf57e7bd
Revises: fddec2590f18
Create Date: 2025-06-12 07:30:26.600069

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "3326cf57e7bd"
down_revision = "fddec2590f18"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add FI_SALES_AGREEMENT to the existing owner_type_enum
    op.execute(
        """ALTER TABLE document_library_item_owner 
        MODIFY COLUMN owner_type ENUM('FI_PROPERTY', 'FI_SALES_AGREEMENT') NOT NULL
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Note: PostgreSQL doesn't support removing enum values directly
    # If you need to rollback, you would need to recreate the enum
    # For now, we'll leave this empty as it's typically not needed
    pass
    # ### end Alembic commands ###
