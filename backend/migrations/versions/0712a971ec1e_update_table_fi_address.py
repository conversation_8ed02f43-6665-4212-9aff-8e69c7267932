"""Update tables fi_address and fi_property

Revision ID: 0712a971ec1e
Revises: 9ce2b943b859
Create Date: 2024-09-02 01:10:06.419945

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "0712a971ec1e"
down_revision = "9ce2b943b859"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Update table fi_address
    op.alter_column(
        "fi_address",
        "post_code",
        new_column_name="postal_code",
        type_=sa.String(length=10),
        existing_type=mysql.VARCHAR(length=200),
        nullable=False,
    )
    op.alter_column(
        "fi_address",
        "locality",
        new_column_name="municipality",
        type_=sa.String(length=100),
        existing_type=mysql.VARCHAR(length=100),
        nullable=False,
    )
    op.alter_column(
        "fi_address",
        "postal_area",
        new_column_name="district",
        type_=sa.String(length=100),
        existing_type=mysql.VARCHAR(length=200),
        nullable=True,
    )

    op.drop_column("fi_address", "locality_code")

    # Update table fi_realty
    op.add_column("fi_realty", sa.Column("commission", sa.Float(), nullable=True))
    op.add_column(
        "fi_realty", sa.Column("commission_notes", sa.Text(length=200), nullable=True)
    )
    op.add_column(
        "fi_realty", sa.Column("commission_type", sa.String(length=20), nullable=True)
    )
    op.alter_column(
        "fi_realty",
        "agency_office_id",
        existing_type=mysql.VARCHAR(length=50),
        nullable=True,
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Update table fi_realty
    op.alter_column(
        "fi_realty",
        "agency_office_id",
        existing_type=mysql.VARCHAR(length=50),
        nullable=False,
    )

    op.drop_column("fi_realty", "commission_type")
    op.drop_column("fi_realty", "commission_notes")
    op.drop_column("fi_realty", "commission")

    # Update table fi_address
    op.alter_column(
        "fi_address",
        "postal_code",
        new_column_name="post_code",
        type_=mysql.VARCHAR(length=200),
        existing_type=sa.String(length=10),
        nullable=False,
    )
    op.alter_column(
        "fi_address",
        "municipality",
        new_column_name="locality",
        type_=mysql.VARCHAR(length=100),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "fi_address",
        "district",
        new_column_name="postal_area",
        type_=mysql.VARCHAR(length=200),
        existing_type=sa.String(length=100),
        nullable=False,
    )

    op.add_column(
        "fi_address",
        sa.Column("locality_code", mysql.VARCHAR(length=100), nullable=True),
    )
    # ### end Alembic commands ###
