"""add group user tables

Revision ID: 5252c61b9b2a
Revises: dbc963b68e95
Create Date: 2024-08-14 12:27:31.339157

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "5252c61b9b2a"
down_revision = "dbc963b68e95"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "group",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("language", sa.String(length=30), nullable=False),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name=op.f("fk_group_organization_id_organization"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_group")),
    )
    op.create_index(
        op.f("ix_group_organization_id"), "group", ["organization_id"], unique=False
    )
    op.create_table(
        "group_contact",
        sa.Column("group_id", sa.BigInteger(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_group_contact_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["group_id"], ["group.id"], name=op.f("fk_group_contact_group_id_group")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_group_contact")),
        sa.UniqueConstraint("group_id", "contact_id", name="uq_group_contact"),
    )
    op.create_index(
        "ix_group_contact_contact", "group_contact", ["contact_id"], unique=False
    )
    op.create_index(
        "ix_group_contact_group", "group_contact", ["group_id"], unique=False
    )
    op.create_table(
        "group_user",
        sa.Column("group_id", sa.BigInteger(), nullable=False),
        sa.Column("user_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["group_id"], ["group.id"], name=op.f("fk_group_user_group_id_group")
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_group_user_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_group_user")),
        sa.UniqueConstraint("group_id", "user_id", name="uq_group_user"),
    )
    op.create_index("ix_group_user_group", "group_user", ["group_id"], unique=False)
    op.create_index("ix_group_user_user", "group_user", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_group_contact_contact_id_contact"), "group_contact", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_group_contact_group_id_group"), "group_contact", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_group_organization_id_organization"), "group", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_group_user_group_id_group"), "group_user", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_group_user_user_id_user"), "group_user", type_="foreignkey"
    )
    op.drop_index("ix_group_user_user", table_name="group_user")
    op.drop_index("ix_group_user_group", table_name="group_user")
    op.drop_table("group_user")
    op.drop_index("ix_group_contact_group", table_name="group_contact")
    op.drop_index("ix_group_contact_contact", table_name="group_contact")
    op.drop_table("group_contact")
    op.drop_index(op.f("ix_group_organization_id"), table_name="group")
    op.drop_table("group")
    # ### end Alembic commands ###
