"""Remove unused fields from fi sales agreement

Revision ID: eb1d1bc8d388
Revises: 1d5ced15d674
Create Date: 2025-03-12 13:39:54.414457

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "eb1d1bc8d388"
down_revision = "1d5ced15d674"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_sales_agreement", "marketing_expenses_max")
    op.drop_column("fi_sales_agreement", "document_acquisition_expenses_max")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement",
        sa.Column("document_acquisition_expenses_max", mysql.FLOAT(), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("marketing_expenses_max", mysql.FLOAT(), nullable=True),
    )
    # ### end Alembic commands ###
