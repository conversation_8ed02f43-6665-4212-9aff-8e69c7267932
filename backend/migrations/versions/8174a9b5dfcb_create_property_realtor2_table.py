"""create property realtor2 table

Revision ID: 8174a9b5dfcb
Revises: 53d176060bed
Create Date: 2023-08-02 15:09:39.541154

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8174a9b5dfcb"
down_revision = "53d176060bed"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "property_realtor2",
        sa.<PERSON>umn("user_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_realtor2_property_id_property"),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_property_realtor2_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_realtor2")),
        sa.UniqueConstraint(
            "property_id", "user_id", name=op.f("uq_property_realtor2_property_id")
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("property_realtor2")
    # ### end Alembic commands ###
