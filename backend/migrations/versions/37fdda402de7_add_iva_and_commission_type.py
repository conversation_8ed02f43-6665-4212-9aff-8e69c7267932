"""Add iva and commission type

Revision ID: 37fdda402de7
Revises: d30370ee4e22
Create Date: 2024-05-22 11:59:05.323234

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "37fdda402de7"
down_revision = "d30370ee4e22"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property", sa.Column("commission_type", sa.String(length=20), nullable=True)
    )
    op.add_column("property", sa.Column("iva_tax", sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "iva_tax")
    op.drop_column("property", "commission_type")
    # ### end Alembic commands ###
