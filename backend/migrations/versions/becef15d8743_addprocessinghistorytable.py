"""AddProcessingHistoryTable

Revision ID: becef15d8743
Revises: d54f5a575cf9
Create Date: 2024-08-14 17:25:31.569420

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'becef15d8743'
down_revision = 'd54f5a575cf9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('leads_processing_history',
    sa.Column('process_type', sa.String(length=20), nullable=False),
    sa.Column('last_time_run', sa.DateTime(), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_processing_history')),
    sa.UniqueConstraint('process_type', name=op.f('uq_leads_processing_history_process_type'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('leads_processing_history')
    # ### end Alembic commands ###
