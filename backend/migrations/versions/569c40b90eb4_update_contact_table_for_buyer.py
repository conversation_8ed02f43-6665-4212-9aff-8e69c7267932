"""update contact table for buyer

Revision ID: 569c40b90eb4
Revises: 6fba7ccd603b
Create Date: 2024-11-26 05:36:39.301738

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '569c40b90eb4'
down_revision = '6fba7ccd603b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('buyer_property_type',
    sa.Column('contact_id', sa.BigInteger(), nullable=False),
    sa.Column('property_type_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contact.id'], name=op.f('fk_buyer_property_type_contact_id_contact')),
    sa.ForeignKeyConstraint(['property_type_id'], ['property_type.id'], name=op.f('fk_buyer_property_type_property_type_id_property_type')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_buyer_property_type')),
    sa.UniqueConstraint('property_type_id', 'contact_id', name='uq_buyer_property_type')
    )
    op.create_index('ix_buyer_property_type_contact', 'buyer_property_type', ['contact_id'], unique=False)
    op.create_index('ix_buyer_property_type_property', 'buyer_property_type', ['property_type_id'], unique=False)
    op.add_column('contact', sa.Column('areas', sa.JSON(), nullable=True))
    op.add_column('contact', sa.Column('min_bedrooms', sa.Integer(), nullable=True))
    op.add_column('contact', sa.Column('min_price', sa.Integer(), nullable=True))
    op.add_column('contact', sa.Column('max_price', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_buyer_property_type_contact_id_contact'), 'buyer_property_type', type_='foreignkey')
    op.drop_constraint(op.f('fk_buyer_property_type_property_type_id_property_type'), 'buyer_property_type', type_='foreignkey')
    op.drop_index('ix_buyer_property_type_property', table_name='buyer_property_type')
    op.drop_index('ix_buyer_property_type_contact', table_name='buyer_property_type')
    op.drop_table('buyer_property_type')
    op.drop_column('contact', 'max_price')
    op.drop_column('contact', 'min_price')
    op.drop_column('contact', 'min_bedrooms')
    op.drop_column('contact', 'areas')
    # ### end Alembic commands ###
