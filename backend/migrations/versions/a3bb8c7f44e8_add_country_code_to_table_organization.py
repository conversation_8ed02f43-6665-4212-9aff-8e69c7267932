"""Add country_code to table organization

Revision ID: a3bb8c7f44e8
Revises: becef15d8743
Create Date: 2024-08-15 16:01:16.090089

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "a3bb8c7f44e8"
down_revision = "becef15d8743"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "organization", sa.Column("country_code", sa.String(length=2), nullable=True)
    )

    # ### custom commands to update country_code ###
    op.execute(
        """
        UPDATE organization
        SET country_code = 'ES'
        WHERE name = 'Strand Spain';
        """
    )

    op.execute(
        """
        UPDATE organization
        SET country_code = 'FI'
        WHERE name = 'Strand Finland';
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("organization", "country_code")
    # ### end Alembic commands ###
