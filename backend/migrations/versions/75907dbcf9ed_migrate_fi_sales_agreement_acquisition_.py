"""migrate fi sales agreement acquisition fields to be contact specific

Revision ID: 75907dbcf9ed
Revises: 95f0c3c7a5ec
Create Date: 2025-07-30 08:07:31.820441

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "75907dbcf9ed"
down_revision = "95f0c3c7a5ec"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fi_sales_agreement_contact_acquisition",
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("fi_sales_agreement_id", sa.BigInteger(), nullable=False),
        sa.Column("acquisition", sa.String(length=50), nullable=True),
        sa.Column(
            "seller_is_married_or_in_registered_relationship",
            sa.<PERSON>olean(),
            nullable=True,
        ),
        sa.Column(
            "seller_has_been_married_or_in_registered_relationship",
            sa.<PERSON>olean(),
            nullable=True,
        ),
        sa.Column("seller_has_spouses_consent", sa.<PERSON>(), nullable=True),
        sa.Column("legal_partitioning_is_complete", sa.Boolean(), nullable=True),
        sa.Column("divorce_legally_binding", sa.Boolean(), nullable=True),
        sa.Column("acquisition_date", sa.Date(), nullable=True),
        sa.Column("acquisition_cost", sa.String(length=250), nullable=True),
        sa.Column(
            "client_has_used_residence_as_residence", sa.Boolean(), nullable=True
        ),
        sa.Column("residency_start_date", sa.Date(), nullable=True),
        sa.Column("residency_end_date", sa.Date(), nullable=True),
        sa.Column("share_register_format", sa.String(length=50), nullable=True),
        sa.Column("share_register_storage", sa.String(length=250), nullable=True),
        sa.Column("tax_consequence", sa.String(length=50), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_fi_sales_agreement_contact_acquisition_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_sales_agreement_id"],
            ["fi_sales_agreement.id"],
            name=op.f(
                "fk_fi_sales_agreement_contact_acquisition_fi_sales_agreement_id_fi_sales_agreement"
            ),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "id", name=op.f("pk_fi_sales_agreement_contact_acquisition")
        ),
        sa.UniqueConstraint(
            "fi_sales_agreement_id",
            "contact_id",
            name="uq_fi_sales_agreement_contact_acquisition",
        ),
    )

    # Migrate existing acquisition data to the new contact-specific table
    op.execute(
        sa.text(
            """
        INSERT INTO fi_sales_agreement_contact_acquisition (
            fi_sales_agreement_id,
            contact_id,
            acquisition,
            seller_is_married_or_in_registered_relationship,
            seller_has_been_married_or_in_registered_relationship,
            seller_has_spouses_consent,
            legal_partitioning_is_complete,
            divorce_legally_binding,
            acquisition_date,
            acquisition_cost,
            client_has_used_residence_as_residence,
            residency_start_date,
            residency_end_date,
            share_register_format,
            share_register_storage,
            tax_consequence,
            created_at,
            updated_at
        )
        WITH first_contacts AS (
            SELECT 
                fi_sales_agreement_id, 
                contact_id,
                ROW_NUMBER() OVER (PARTITION BY fi_sales_agreement_id ORDER BY id) as rn
            FROM fi_sales_agreement_contact
        )
        SELECT 
            fsa.id,
            fc.contact_id,
            fsa.acquisition,
            fsa.seller_is_married_or_in_registered_relationship,
            fsa.seller_has_been_married_or_in_registered_relationship,
            fsa.seller_has_spouses_consent,
            fsa.legal_partitioning_is_complete,
            fsa.divorce_legally_binding,
            fsa.acquisition_date,
            fsa.acquisition_cost,
            fsa.client_has_used_residence_as_residence,
            fsa.residency_start_date,
            fsa.residency_end_date,
            fsa.share_register_format,
            fsa.share_register_storage,
            fsa.tax_consequence,
            NOW(),
            NOW()
        FROM fi_sales_agreement fsa
        INNER JOIN first_contacts fc ON fsa.id = fc.fi_sales_agreement_id AND fc.rn = 1
        """
        )
    )

    op.drop_column("fi_sales_agreement", "share_register_storage")
    op.drop_column(
        "fi_sales_agreement", "seller_is_married_or_in_registered_relationship"
    )
    op.drop_column("fi_sales_agreement", "legal_partitioning_is_complete")
    op.drop_column(
        "fi_sales_agreement", "seller_has_been_married_or_in_registered_relationship"
    )
    op.drop_column("fi_sales_agreement", "acquisition_date")
    op.drop_column("fi_sales_agreement", "share_register_format")
    op.drop_column("fi_sales_agreement", "divorce_legally_binding")
    op.drop_column("fi_sales_agreement", "acquisition_cost")
    op.drop_column("fi_sales_agreement", "tax_consequence")
    op.drop_column("fi_sales_agreement", "seller_has_spouses_consent")
    op.drop_column("fi_sales_agreement", "residency_end_date")
    op.drop_column("fi_sales_agreement", "client_has_used_residence_as_residence")
    op.drop_column("fi_sales_agreement", "acquisition")
    op.drop_column("fi_sales_agreement", "residency_start_date")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement",
        sa.Column("residency_start_date", sa.DATE(), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("acquisition", mysql.VARCHAR(length=50), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "client_has_used_residence_as_residence",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement", sa.Column("residency_end_date", sa.DATE(), nullable=True)
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "seller_has_spouses_consent",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("tax_consequence", mysql.VARCHAR(length=50), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("acquisition_cost", mysql.VARCHAR(length=250), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "divorce_legally_binding",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("share_register_format", mysql.VARCHAR(length=50), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("acquisition_date", mysql.VARCHAR(length=250), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "seller_has_been_married_or_in_registered_relationship",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "legal_partitioning_is_complete",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column(
            "seller_is_married_or_in_registered_relationship",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("share_register_storage", mysql.VARCHAR(length=250), nullable=True),
    )
    op.drop_table("fi_sales_agreement_contact_acquisition")
    # ### end Alembic commands ###
