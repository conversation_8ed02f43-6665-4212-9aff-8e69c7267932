"""Extend the length of the url

Revision ID: ca9d8064986e
Revises: b5b94db3010d
Create Date: 2025-02-20 01:00:59.429087

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'ca9d8064986e'
down_revision = 'b5b94db3010d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('image', 'url',
               existing_type=mysql.VARCHAR(length=1000),
               type_=sa.String(length=3000),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('image', 'url',
               existing_type=sa.String(length=3000),
               type_=mysql.VARCHAR(length=1000),
               existing_nullable=False)
    # ### end Alembic commands ###
