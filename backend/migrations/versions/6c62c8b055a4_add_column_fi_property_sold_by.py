"""Add column fi_property.sold_by

Revision ID: 6c62c8b055a4
Revises: a589de5cfc7c
Create Date: 2024-09-18 00:25:07.154020

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6c62c8b055a4'
down_revision = 'a589de5cfc7c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_property', sa.Column('sold_by', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_property', 'sold_by')
    # ### end Alembic commands ###
