"""Add table event

Revision ID: 467a6fa85a88
Revises: 1986c4a43b1f
Create Date: 2024-07-20 01:05:37.318360

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "467a6fa85a88"
down_revision = "1986c4a43b1f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "event",
        sa.Column("event_type", sa.Text(), nullable=False),
        sa.Column("event_format", sa.Text(), nullable=False),
        sa.Column("title", sa.Text(), nullable=True),
        sa.Column("descriptions", sa.JSON(), nullable=True),
        sa.Column("internal_notes", sa.Text(), nullable=True),
        sa.Column("start", sa.DateTime(), nullable=False),
        sa.Column("end", sa.DateTime(), nullable=True),
        sa.Column("location", sa.Text(), nullable=True),
        sa.Column("additional_details", sa.JSON(), nullable=True),
        sa.Column("is_cancelled", sa.Boolean(), nullable=False),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name=op.f("fk_event_organization_id_organization"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event")),
    )
    op.create_index(
        op.f("ix_event_organization_id"), "event", ["organization_id"], unique=False
    )
    op.create_table(
        "event_contact",
        sa.Column("event_id", sa.BigInteger(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_event_contact_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["event_id"], ["event.id"], name=op.f("fk_event_contact_event_id_event")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event_contact")),
        sa.UniqueConstraint("event_id", "contact_id", name="uq_event_contact"),
    )
    op.create_index(
        "ix_event_contact_contact", "event_contact", ["contact_id"], unique=False
    )
    op.create_index(
        "ix_event_contact_event", "event_contact", ["event_id"], unique=False
    )
    op.create_table(
        "event_property",
        sa.Column("event_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["event_id"], ["event.id"], name=op.f("fk_event_property_event_id_event")
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_event_property_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event_property")),
        sa.UniqueConstraint("event_id", "property_id", name="uq_event_property"),
    )
    op.create_index(
        "ix_event_property_event", "event_property", ["event_id"], unique=False
    )
    op.create_index(
        "ix_event_property_property", "event_property", ["property_id"], unique=False
    )
    op.create_table(
        "event_user",
        sa.Column("event_id", sa.BigInteger(), nullable=False),
        sa.Column("user_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["event_id"], ["event.id"], name=op.f("fk_event_user_event_id_event")
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_event_user_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event_user")),
        sa.UniqueConstraint("event_id", "user_id", name="uq_event_user"),
    )
    op.create_index("ix_event_user_event", "event_user", ["event_id"], unique=False)
    op.create_index("ix_event_user_user", "event_user", ["user_id"], unique=False)
    op.create_table(
        "event_lead",
        sa.Column("event_id", sa.BigInteger(), nullable=False),
        sa.Column("lead_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["event_id"], ["event.id"], name=op.f("fk_event_lead_event_id_event")
        ),
        sa.ForeignKeyConstraint(
            ["lead_id"], ["lead.id"], name=op.f("fk_event_lead_lead_id_lead")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event_lead")),
        sa.UniqueConstraint("event_id", "lead_id", name="uq_event_lead"),
    )
    op.create_index("ix_event_lead_event", "event_lead", ["event_id"], unique=False)
    op.create_index("ix_event_lead_lead", "event_lead", ["lead_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("event_lead") as batch_op:
        batch_op.drop_constraint("fk_event_lead_event_id_event", type_="foreignkey")
        batch_op.drop_constraint("fk_event_lead_lead_id_lead", type_="foreignkey")
    op.drop_index("ix_event_lead_lead", table_name="event_lead")
    op.drop_index("ix_event_lead_event", table_name="event_lead")
    op.drop_table("event_lead")

    with op.batch_alter_table("event_user") as batch_op:
        batch_op.drop_constraint("fk_event_user_event_id_event", type_="foreignkey")
        batch_op.drop_constraint("fk_event_user_user_id_user", type_="foreignkey")
    op.drop_index("ix_event_user_user", table_name="event_user")
    op.drop_index("ix_event_user_event", table_name="event_user")
    op.drop_table("event_user")

    with op.batch_alter_table("event_property") as batch_op:
        batch_op.drop_constraint("fk_event_property_event_id_event", type_="foreignkey")
        batch_op.drop_constraint(
            "fk_event_property_property_id_property", type_="foreignkey"
        )
    op.drop_index("ix_event_property_property", table_name="event_property")
    op.drop_index("ix_event_property_event", table_name="event_property")
    op.drop_table("event_property")

    with op.batch_alter_table("event_contact") as batch_op:
        batch_op.drop_constraint("fk_event_contact_event_id_event", type_="foreignkey")
        batch_op.drop_constraint(
            "fk_event_contact_contact_id_contact", type_="foreignkey"
        )
    op.drop_index("ix_event_contact_event", table_name="event_contact")
    op.drop_index("ix_event_contact_contact", table_name="event_contact")
    op.drop_table("event_contact")

    with op.batch_alter_table("event") as batch_op:
        batch_op.drop_constraint(
            "fk_event_organization_id_organization", type_="foreignkey"
        )
    op.drop_index(op.f("ix_event_organization_id"), table_name="event")
    op.drop_table("event")
    # ### end Alembic commands ###
