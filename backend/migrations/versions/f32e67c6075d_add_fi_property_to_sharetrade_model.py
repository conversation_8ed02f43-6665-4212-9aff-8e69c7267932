"""add fi_property to ShareTrade model

Revision ID: f32e67c6075d
Revises: c69166df5164
Create Date: 2024-09-27 16:08:14.088836

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f32e67c6075d'
down_revision = 'c69166df5164'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dias_shared_trade', sa.Column('fi_property_id', sa.BigInteger(), nullable=True))
    op.drop_constraint('fk_dias_shared_trade_draft_property_id_property', 'dias_shared_trade', type_='foreignkey')
    op.create_foreign_key(op.f('fk_dias_shared_trade_fi_property_id_fi_property'), 'dias_shared_trade', 'fi_property', ['fi_property_id'], ['id'], ondelete='SET NULL')
    op.drop_column('dias_shared_trade', 'property_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dias_shared_trade', sa.Column('property_id', mysql.BIGINT(display_width=20), autoincrement=False, nullable=True))
    op.drop_constraint(op.f('fk_dias_shared_trade_fi_property_id_fi_property'), 'dias_shared_trade', type_='foreignkey')
    op.create_foreign_key('fk_dias_shared_trade_draft_property_id_property', 'dias_shared_trade', 'property', ['property_id'], ['id'], ondelete='SET NULL')
    op.drop_column('dias_shared_trade', 'fi_property_id')
    # ### end Alembic commands ###
