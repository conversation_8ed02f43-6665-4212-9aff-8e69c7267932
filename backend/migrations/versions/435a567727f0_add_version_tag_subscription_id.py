"""Add version tag and subscription id column

Revision ID: 435a567727f0
Revises: 69de4a123b9a
Create Date: 2025-01-06 01:15:21.013340

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "435a567727f0"
down_revision = "69de4a123b9a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("contact", sa.Column("subscription_id", sa.Text(), nullable=True))
    op.add_column("email_template", sa.Column("version_tag", sa.Text(), nullable=True))
    op.drop_column("email_template", "template")


def downgrade():
    op.add_column(
        "email_template", sa.Column("template", mysql.LONGTEXT(), nullable=True)
    )
    op.drop_column("email_template", "version_tag")
    op.drop_column("contact", "subscription_id")
