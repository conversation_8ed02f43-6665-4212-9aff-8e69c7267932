"""Add brochure generation system

Revision ID: ce22b8b3236d
Revises: 390f201051c8
Create Date: 2025-08-21 10:07:46.099457

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "ce22b8b3236d"
down_revision = "390f201051c8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "brochure_images",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_brochure_images")),
    )
    op.create_table(
        "brochure_information",
        sa.Column("property_description", sa.Text(), nullable=True),
        sa.Column("id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_brochure_information")),
    )
    op.create_table(
        "brochure_image_page",
        sa.Column("brochure_images_id", sa.BigInteger(), nullable=False),
        sa.Column("page_number", sa.Integer(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["brochure_images_id"],
            ["brochure_images.id"],
            name=op.f("fk_brochure_image_page_brochure_images_id_brochure_images"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_brochure_image_page")),
    )
    op.create_table(
        "brochure",
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("realtor_id", sa.BigInteger(), nullable=False),
        sa.Column(
            "theme",
            sa.Enum(
                "WHITE",
                "FLOWER",
                "LIVINGROOM",
                "LIVINGROOM2",
                "OLIVE",
                name="cover_theme_enum",
            ),
            nullable=False,
        ),
        sa.Column(
            "language", sa.Enum("FI", "EN", name="language_enum"), nullable=False
        ),
        sa.Column("version_number", sa.Integer(), nullable=False),
        sa.Column("is_complete", sa.Boolean(), nullable=False),
        sa.Column("property_information_version_id", sa.BigInteger(), nullable=True),
        sa.Column("property_images_version_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_brochure_property_id_property"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["property_images_version_id"],
            ["brochure_images.id"],
            name=op.f("fk_brochure_property_images_version_id_brochure_images"),
        ),
        sa.ForeignKeyConstraint(
            ["property_information_version_id"],
            ["brochure_information.id"],
            name=op.f(
                "fk_brochure_property_information_version_id_brochure_information"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["realtor_id"], ["user.id"], name=op.f("fk_brochure_realtor_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_brochure")),
        sa.UniqueConstraint(
            "property_id", "version_number", name="uix_brochure_property_version"
        ),
    )
    op.create_table(
        "brochure_image_with_position",
        sa.Column("page_id", sa.BigInteger(), nullable=False),
        sa.Column("image_url", sa.String(length=500), nullable=False),
        sa.Column(
            "position",
            sa.Enum(
                "FULL_PAGE",
                "TOP",
                "BOTTOM",
                "LEFT",
                "RIGHT",
                "CENTER",
                "TOP_LEFT",
                "TOP_RIGHT",
                "BOTTOM_LEFT",
                "BOTTOM_RIGHT",
                name="image_position_enum",
            ),
            nullable=False,
        ),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["page_id"],
            ["brochure_image_page.id"],
            name=op.f("fk_brochure_image_with_position_page_id_brochure_image_page"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_brochure_image_with_position")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("brochure_image_with_position")
    op.drop_table("brochure")
    op.drop_table("brochure_image_page")
    op.drop_table("brochure_information")
    op.drop_table("brochure_images")
    # ### end Alembic commands ###
