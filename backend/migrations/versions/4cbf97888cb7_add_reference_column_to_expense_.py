"""add_reference_column_to_expense_transaction_targets

Revision ID: 4cbf97888cb7
Revises: 2e8c0ef72580
Create Date: 2025-08-20 14:15:32.227181

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4cbf97888cb7'
down_revision = '2e8c0ef72580'
branch_labels = None
depends_on = None


def upgrade():
    # Add reference column to expense_transaction_targets table
    op.add_column('expense_transaction_targets', sa.Column('reference', sa.String(length=50), nullable=True))


def downgrade():
    # Remove reference column from expense_transaction_targets table
    op.drop_column('expense_transaction_targets', 'reference')
