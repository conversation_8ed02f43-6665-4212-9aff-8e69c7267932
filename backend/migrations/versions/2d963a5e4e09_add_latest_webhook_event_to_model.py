"""add latest_webhook_event to model

Revision ID: 2d963a5e4e09
Revises: 041deeddb4ca
Create Date: 2024-10-07 16:06:18.274249

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2d963a5e4e09'
down_revision = '041deeddb4ca'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dias_shared_trade', sa.Column('latest_webhook_event', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dias_shared_trade', 'latest_webhook_event')
    # ### end Alembic commands ###
