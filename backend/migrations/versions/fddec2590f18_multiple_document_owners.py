"""Multiple document owners

Revision ID: fddec2590f18
Revises: 400d052c02f4
Create Date: 2025-06-10 15:38:43.943860

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'fddec2590f18'
down_revision = '400d052c02f4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_library_item_owner',
    sa.<PERSON>umn('item_id', sa.BigInteger(), nullable=False),
    sa.Column('owner_type', sa.Enum('FI_PROPERTY', name='owner_type_enum'), nullable=False),
    sa.Column('owner_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['item_id'], ['document_library_item.id'], name=op.f('fk_document_library_item_owner_item_id_document_library_item'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_document_library_item_owner')),
    sa.UniqueConstraint('item_id', 'owner_type', 'owner_id', name='uq_document_library_item_owner')
    )
    op.create_index('ix_document_library_item_owner_document', 'document_library_item_owner', ['item_id'], unique=False)
    op.create_index('ix_document_library_item_owner_owner', 'document_library_item_owner', ['owner_type', 'owner_id'], unique=False)
    op.execute(
        """
        INSERT INTO document_library_item_owner (
            item_id,
            owner_type,
            owner_id,
            created_at,
            updated_at
        )
        SELECT 
            id,
            owner_type,
            owner_id,
            created_at,
            updated_at
        FROM document_library_item
        """
    )
    op.drop_index('ix_document_library_item_owner', table_name='document_library_item')
    op.drop_column('document_library_item', 'owner_id')
    op.drop_column('document_library_item', 'owner_type')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('document_library_item', sa.Column('owner_type', mysql.ENUM('FI_PROPERTY'), nullable=False))
    op.add_column('document_library_item', sa.Column('owner_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False))
    op.create_index('ix_document_library_item_owner', 'document_library_item', ['owner_type', 'owner_id'], unique=False)
    op.execute(
      """
      UPDATE document_library_item dli
      JOIN (
          SELECT item_id, owner_type, owner_id
          FROM document_library_item_owner 
          WHERE (item_id, created_at) IN (
              SELECT item_id, MIN(created_at)
              FROM document_library_item_owner
              GROUP BY item_id
          )
      ) dlio ON dlio.item_id = dli.id
      SET 
          dli.owner_type = dlio.owner_type,
          dli.owner_id = dlio.owner_id
      """
    )
    op.drop_index('ix_document_library_item_owner_owner', table_name='document_library_item_owner')
    op.drop_index('ix_document_library_item_owner_document', table_name='document_library_item_owner')
    op.drop_table('document_library_item_owner')
    # ### end Alembic commands ###
