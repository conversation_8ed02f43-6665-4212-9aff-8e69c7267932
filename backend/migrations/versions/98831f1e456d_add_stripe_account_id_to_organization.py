"""Add stripe account id to organization

Revision ID: 98831f1e456d
Revises: b0bfaebcd617
Create Date: 2025-04-11 13:11:01.411683

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '98831f1e456d'
down_revision = 'b0bfaebcd617'
branch_labels = None
depends_on = None


def upgrade():
    # Drop the foreign key constraint from advertisement_image.
    op.drop_constraint(
        'fk_advertisement_image_advertisement_id_advertisement',
        'advertisement_image',
        type_='foreignkey'
    )

    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('advertisement', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('advertisement', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('advertisement', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('advertisement_image', 'advertisement_id',
               existing_type=mysql.INTEGER(display_width=11),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('advertisement_image', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.add_column('organization', sa.Column('stripe_account_id', sa.Text(), nullable=True))
    # ### end Alembic commands ###

    # Re-create the foreign key constraint with the updated column type.
    op.create_foreign_key(
        'fk_advertisement_image_advertisement_id_advertisement',
        source_table='advertisement_image',
        referent_table='advertisement',
        local_cols=['advertisement_id'],
        remote_cols=['id'],
        ondelete='CASCADE'  # Adjust if you need a different ondelete behavior.
    )


def downgrade():
    # Drop the re-created foreign key constraint.
    op.drop_constraint(
        'fk_advertisement_image_advertisement_id_advertisement',
        'advertisement_image',
        type_='foreignkey'
    )

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization', 'stripe_account_id')
    op.alter_column('advertisement_image', 'id',
               existing_type=sa.BigInteger(),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('advertisement_image', 'advertisement_id',
               existing_type=sa.BigInteger(),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=False)
    op.alter_column('advertisement', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('advertisement', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('advertisement', 'id',
               existing_type=sa.BigInteger(),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###

    # Re-create the original foreign key constraint.
    op.create_foreign_key(
        'fk_advertisement_image_advertisement_id_advertisement',
        source_table='advertisement_image',
        referent_table='advertisement',
        local_cols=['advertisement_id'],
        remote_cols=['id'],
        ondelete='CASCADE'  # Ensure this matches your original constraint.
    )
