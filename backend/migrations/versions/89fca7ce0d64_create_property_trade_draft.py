"""Create property_trade_draft

Revision ID: 89fca7ce0d64
Revises: bfe0b1855d29
Create Date: 2024-08-26 09:53:56.663453

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '89fca7ce0d64'
down_revision = 'bfe0b1855d29'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('property_trade_draft',
    sa.Column('transferred_shares', sa.JSON(), nullable=False),
    sa.Column('attachments', sa.JSON(), nullable=False),
    sa.Column('initiator_contact_info', sa.JSON(), nullable=False),
    sa.Column('consenters', sa.JSON(), nullable=False),
    sa.Column('deed_type', sa.Text(), nullable=False),
    sa.Column('terms_of_transfer', sa.JSO<PERSON>(), nullable=False),
    sa.Column('transfer_object', sa.Text(), nullable=False),
    sa.Column('sellers', sa.JSON(), nullable=False),
    sa.Column('buyers', sa.JSON(), nullable=False),
    sa.Column('purchase_price', sa.JSON(), nullable=False),
    sa.Column('initiator_person_id', sa.Text(), nullable=False),
    sa.Column('trade_initiated_timestamp', sa.Text(), nullable=False),
    sa.Column('initiator_trade_reference_id', sa.Text(), nullable=False),
    sa.Column('institution_code', sa.Text(), nullable=True),
    sa.Column('real_estate_code', sa.Text(), nullable=True),
    sa.Column('mandatory_deed_terms', sa.JSON(), nullable=True),
    sa.Column('transfer_notification', sa.JSON(), nullable=True),
    sa.Column('pledge_codes', sa.JSON(), nullable=True),
    sa.Column('realtor_bank_account', sa.JSON(), nullable=True),
    sa.Column('non_transferable_pledge_codes', sa.JSON(), nullable=True),
    sa.Column('penalty_for_non_payment', sa.JSON(), nullable=True),
    sa.Column('down_payment', sa.JSON(), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_property_trade_draft'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('property_trade_draft')
    # ### end Alembic commands ###
