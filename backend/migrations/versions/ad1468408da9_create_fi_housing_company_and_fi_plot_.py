"""Create fi_housing_company and fi_plot_overview

Revision ID: ad1468408da9
Revises: 8c09b071f42d
Create Date: 2024-07-23 05:54:20.994158

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ad1468408da9"
down_revision = "8c09b071f42d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_plot_overview",
        sa.Column("plot_property_type_code", sa.JSON(), nullable=True),
        sa.Column("holding_type_code", sa.JSON(), nullable=True),
        sa.Column("holding_type_description", sa.JSON(), nullable=True),
        sa.Column("landlord", sa.String(length=100), nullable=True),
        sa.Column("yearly_rent", sa.Integer(), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("is_redeemable", sa.String(length=50), nullable=True),
        sa.Column("redemption_portion", sa.Integer(), nullable=True),
        sa.Column("optional_rental_plot", sa.String(length=50), nullable=True),
        sa.Column("optional_rental_plot_description", sa.JSON(), nullable=True),
        sa.Column("lease_end_date", sa.String(length=100), nullable=True),
        sa.Column("lease_period_description", sa.JSON(), nullable=True),
        sa.Column("plot_number", sa.String(length=100), nullable=True),
        sa.Column(
            "identification_number_of_land_charge", sa.String(length=100), nullable=True
        ),
        sa.Column("lease_holder", sa.String(length=100), nullable=True),
        sa.Column("lease_hold_tranfer_limitation", sa.String(length=50), nullable=True),
        sa.Column(
            "lease_hold_tranfer_limitation_description", sa.JSON(), nullable=True
        ),
        sa.Column(
            "legal_confirmation_of_title_to_real_property", sa.JSON(), nullable=True
        ),
        sa.Column("area", sa.JSON(), nullable=True),
        sa.Column("zoning", sa.JSON(), nullable=True),
        sa.Column("beach", sa.JSON(), nullable=True),
        sa.Column("construction_right", sa.JSON(), nullable=True),
        sa.Column("unbuilt_plot", sa.Boolean(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_plot_overview")),
    )
    op.create_table(
        "fi_housing_company",
        sa.Column("business_id", sa.String(length=10), nullable=False),
        sa.Column("scheme_id", sa.String(length=10), nullable=False),
        sa.Column("name", sa.String(length=100), nullable=True),
        sa.Column("fi_address_id", sa.BigInteger(), nullable=True),
        sa.Column("manager_contact_details", sa.JSON(), nullable=True),
        sa.Column("maintenance", sa.JSON(), nullable=True),
        sa.Column("property_identifier", sa.String(length=50), nullable=False),
        sa.Column("construction", sa.JSON(), nullable=True),
        sa.Column("buildings", sa.JSON(), nullable=True),
        sa.Column("yard_description", sa.JSON(), nullable=True),
        sa.Column("fi_plot_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("internet_connections", sa.JSON(), nullable=True),
        sa.Column("television", sa.JSON(), nullable=True),
        sa.Column(
            "last_annual_general_meeting_date", sa.String(length=100), nullable=True
        ),
        sa.Column(
            "next_annual_general_meeting_date", sa.String(length=100), nullable=True
        ),
        sa.Column("identified_deficiencies", sa.JSON(), nullable=True),
        sa.Column("cost_incurring_liabilities", sa.JSON(), nullable=True),
        sa.Column("repairs_and_maintenance_agreements", sa.JSON(), nullable=True),
        sa.Column("house_manager_certificate", sa.String(length=100), nullable=True),
        sa.Column("finances", sa.JSON(), nullable=True),
        sa.Column("energy_certificate", sa.JSON(), nullable=True),
        sa.Column("asbestos_mapping", sa.JSON(), nullable=True),
        sa.Column("parking_spaces", sa.JSON(), nullable=True),
        sa.Column("parking_spaces_description", sa.JSON(), nullable=True),
        sa.Column("housing_company_premise_statistics", sa.JSON(), nullable=True),
        sa.Column(
            "housing_company_premise_statistics_description", sa.JSON(), nullable=True
        ),
        sa.Column("renovations", sa.JSON(), nullable=True),
        sa.Column("renovations_description", sa.JSON(), nullable=True),
        sa.Column("renovations_planned_description", sa.JSON(), nullable=True),
        sa.Column("inspections", sa.JSON(), nullable=True),
        sa.Column("inspections_description", sa.JSON(), nullable=True),
        sa.Column("additional_description", sa.JSON(), nullable=True),
        sa.Column("list_of_shares_transferred", sa.String(length=50), nullable=True),
        sa.Column("digital_share_group_identifiers", sa.JSON(), nullable=True),
        sa.Column("management_charge", sa.Integer(), nullable=True),
        sa.Column("financing_charge", sa.JSON(), nullable=True),
        sa.Column("plot_charges", sa.JSON(), nullable=True),
        sa.Column("special_charge", sa.Integer(), nullable=True),
        sa.Column("maintenance_charge", sa.Integer(), nullable=True),
        sa.Column("charges_description", sa.JSON(), nullable=True),
        sa.Column("charges_currency_code", sa.String(length=3), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_address_id"],
            ["fi_address.id"],
            name=op.f("fk_fi_housing_company_fi_address_id_fi_address"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_plot_overview_id"],
            ["fi_plot_overview.id"],
            name=op.f("fk_fi_housing_company_fi_plot_overview_id_fi_plot_overview"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_housing_company")),
    )
    op.create_index(
        op.f("ix_fi_housing_company_fi_address_id"),
        "fi_housing_company",
        ["fi_address_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_housing_company_fi_plot_overview_id"),
        "fi_housing_company",
        ["fi_plot_overview_id"],
        unique=False,
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_fi_housing_company_fi_address_id_fi_address"),
        "fi_housing_company",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_housing_company_fi_plot_overview_id_fi_plot_overview"),
        "fi_housing_company",
        type_="foreignkey",
    )
    op.drop_index(
        op.f("ix_fi_housing_company_fi_plot_overview_id"),
        table_name="fi_housing_company",
    )
    op.drop_index(
        op.f("ix_fi_housing_company_fi_address_id"), table_name="fi_housing_company"
    )
    op.drop_table("fi_housing_company")
    op.drop_table("fi_plot_overview")
