"""change null able for fi housing company

Revision ID: 6a6737152189
Revises: e238e03b0076
Create Date: 2024-11-05 16:10:20.123883

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6a6737152189'
down_revision = 'e238e03b0076'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'business_id',
               existing_type=mysql.VARCHAR(length=10),
               nullable=True)
    op.alter_column('fi_housing_company', 'property_identifier',
               existing_type=mysql.VARCHAR(length=50),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'property_identifier',
               existing_type=mysql.VARCHAR(length=50),
               nullable=False)
    op.alter_column('fi_housing_company', 'business_id',
               existing_type=mysql.VARCHAR(length=10),
               nullable=False)
    # ### end Alembic commands ###
