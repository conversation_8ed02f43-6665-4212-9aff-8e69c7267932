"""rename conditions to condition

Revision ID: 862a5936abd4
Revises: 545cb3e469c2
Create Date: 2024-01-30 08:17:05.729939

"""
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "862a5936abd4"
down_revision = "545cb3e469c2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "conditions",
        existing_type=mysql.VARCHAR(length=50),
        new_column_name="condition",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "condition",
        existing_type=mysql.VARCHAR(length=50),
        new_column_name="conditions",
    )
    # ### end Alembic commands ###
