"""Engine & collation

Revision ID: d594ad659e19
Revises: c048acc3346a
Create Date: 2023-06-18 13:08:41.144101

"""

import sqlalchemy as sa
from alembic import op, context

# revision identifiers, used by Alembic.
revision = "d594ad659e19"
down_revision = "54148c3d48e9"
branch_labels = None
depends_on = None


def upgrade():
    # Skip this migration in offline mode
    if context.is_offline_mode():
        return

    conn = op.get_bind()
    db_name = conn.engine.url.database
    target_engine = "InnoDB"
    target_collation = "utf8mb4_unicode_ci"
    target_charset = "utf8mb4"

    table_status = conn.execute(
        sa.text(
            f"""
            SELECT TABLE_NAME, ENGINE, TABLE_COLLATION
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = '{db_name}' 
                AND ENGINE IS NOT NULL
                AND (ENGINE != '{target_engine}' or TABLE_COLLATION != '{target_collation}');
            """
        )
    ).all()

    for table, engine, collation in table_status:
        if engine != target_engine:
            query = f"ALTER TABLE {table} ENGINE = {target_engine};"
            print(query)
            conn.execute(sa.text(query))
        if collation != target_collation:
            query = f"ALTER TABLE {table} CONVERT TO CHARACTER SET {target_charset} COLLATE {target_collation};"
            print(query)
            conn.execute(sa.text(query))
    else:
        print("Tables engine and charset already corrected")

    db_charset, db_collation = conn.execute(
        sa.text(
            f"""
            SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME
            FROM information_schema.SCHEMATA
            WHERE SCHEMA_NAME = '{db_name}';
            """
        )
    ).one()

    if db_charset != target_charset or db_collation != target_collation:
        query = f"ALTER DATABASE {db_name} CHARACTER SET {target_charset} COLLATE {target_collation};"
        print(query)
        conn.execute(sa.text(query))
    else:
        print("DB charset already corrected")


def downgrade():
    pass
