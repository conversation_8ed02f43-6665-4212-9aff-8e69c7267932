"""user photo and property imported data

Revision ID: 42cc88eb4c8b
Revises: 7cc0b2769b8e
Create Date: 2023-11-29 12:22:16.693977

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42cc88eb4c8b'
down_revision = '7cc0b2769b8e'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('property', sa.Column('imported_data', sa.JSON(), nullable=True))
    op.add_column('user', sa.Column('photo_url', sa.String(length=255), nullable=True))


def downgrade():
    op.drop_column('user', 'photo_url')
    op.drop_column('property', 'imported_data')
