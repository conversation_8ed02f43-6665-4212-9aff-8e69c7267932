"""add is auction listing

Revision ID: 8d24048bd342
Revises: 5caa155b97f5
Create Date: 2024-10-17 14:56:10.309470

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8d24048bd342'
down_revision = '5caa155b97f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_other_share_overview', sa.Column('is_auction_listing', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_other_share_overview', 'is_auction_listing')
    # ### end Alembic commands ###
