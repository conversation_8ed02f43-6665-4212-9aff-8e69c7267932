"""Add fi_property_overview table

Revision ID: aaf5e8a201c2
Revises: b4a6b856e40d
Create Date: 2024-07-23 05:07:38.836431

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "aaf5e8a201c2"
down_revision = "b4a6b856e40d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_property_overview",
        sa.<PERSON>umn("real_estate_type_code", sa.JSON(), nullable=True),
        sa.Column("property_name", sa.String(length=100), nullable=True),
        sa.Column("property_id", sa.String(length=100), nullable=True),
        sa.Column("has_possession_or_divide_agreement", sa.<PERSON>(), nullable=True),
        sa.Column("estate_has_residence", sa.<PERSON>(), nullable=True),
        sa.Column("electricity_consumption_description", sa.JSO<PERSON>(), nullable=True),
        sa.Column("internet_connections", sa.JSO<PERSON>(), nullable=True),
        sa.Column("television", sa.JSON(), nullable=True),
        sa.Column("restriction_for_use_and_assignment", sa.JSON(), nullable=True),
        sa.Column(
            "restriction_for_use_and_assignment_description", sa.JSON(), nullable=True
        ),
        sa.Column("water_supply", sa.JSON(), nullable=True),
        sa.Column("sewage_disposal", sa.JSON(), nullable=True),
        sa.Column("renovation_grant", sa.JSON(), nullable=True),
        sa.Column("road_available_till_property", sa.JSON(), nullable=True),
        sa.Column("road_access_rights_and_restrictions", sa.JSON(), nullable=True),
        sa.Column("property_on_island", sa.JSON(), nullable=True),
        sa.Column("island_property", sa.JSON(), nullable=True),
        sa.Column("property_connections", sa.JSON(), nullable=True),
        sa.Column("yard_description", sa.JSON(), nullable=True),
        sa.Column("view_description", sa.JSON(), nullable=True),
        sa.Column("ground_area", sa.JSON(), nullable=True),
        sa.Column("buildings", sa.JSON(), nullable=True),
        sa.Column("storages", sa.JSON(), nullable=True),
        sa.Column("encumbrance", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_property_overview")),
    )


def downgrade():
    op.drop_table("fi_property_overview")
