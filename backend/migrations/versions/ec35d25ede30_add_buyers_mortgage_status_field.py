"""Add buyers_mortgage_status field

Revision ID: ec35d25ede30
Revises: 6fee8c9e4e59
Create Date: 2024-10-31 09:42:11.244174

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "ec35d25ede30"
down_revision = "6fee8c9e4e59"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade",
        sa.Column("buyers_mortgage_status", sa.String(length=50), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("dias_shared_trade", "buyers_mortgage_status")
    # ### end Alembic commands ###
