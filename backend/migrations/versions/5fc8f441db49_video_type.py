"""video type

Revision ID: 5fc8f441db49
Revises: 7a7f4a2c5128
Create Date: 2024-05-13 15:04:57.943693

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "5fc8f441db49"
down_revision = "7a7f4a2c5128"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("video", sa.Column("type", sa.String(length=50), nullable=False))


def downgrade():
    op.drop_column("video", "type")
