"""Add differentiation between price sale and rental

Revision ID: 89f4b818ad57
Revises: 40ac38410ca6
Create Date: 2024-01-03 11:18:43.967709

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "89f4b818ad57"
down_revision = "40ac38410ca6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "price_old",
        new_column_name="price_sale_old",
        existing_type=mysql.INTEGER(display_width=11),
    )
    op.alter_column(
        "property",
        "price",
        new_column_name="price_sale",
        existing_type=mysql.INTEGER(display_width=11),
    )

    op.add_column(
        "property", sa.Column("price_rent_short_term_old", sa.Integer(), nullable=True)
    )
    op.add_column(
        "property", sa.Column("price_rent_long_term_old", sa.Integer(), nullable=True)
    )
    op.add_column(
        "property", sa.Column("price_rent_short_term", sa.Integer(), nullable=True)
    )
    op.add_column(
        "property", sa.Column("price_rent_long_term", sa.Integer(), nullable=True)
    )

    op.create_index(
        op.f("ix_property_price_rent_long_term"),
        "property",
        ["price_rent_long_term"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_price_rent_short_term"),
        "property",
        ["price_rent_short_term"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "price_sale_old",
        new_column_name="price_old",
        existing_type=mysql.INTEGER(display_width=11),
    )
    op.alter_column(
        "property",
        "price_sale",
        new_column_name="price",
        existing_type=mysql.INTEGER(display_width=11),
    )

    op.drop_column("property", "price_rent_long_term")
    op.drop_column("property", "price_rent_short_term")
    op.drop_column("property", "price_rent_long_term_old")
    op.drop_column("property", "price_rent_short_term_old")

    op.drop_index(op.f("ix_property_price_rent_short_term"), table_name="property")
    op.drop_index(op.f("ix_property_price_rent_long_term"), table_name="property")
    # ### end Alembic commands ###
