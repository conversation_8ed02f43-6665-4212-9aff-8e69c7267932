"""add AgentCommissionInfo table

Revision ID: 83a3896aef87
Revises: 1fbbd7ce00d5
Create Date: 2024-09-20 18:13:50.010832

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "83a3896aef87"
down_revision = "1fbbd7ce00d5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_attachments", sa.Column("realtor_id", sa.BigInteger(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_document_attachments_realtor_id_realtor_details_of_sale"),
        "document_attachments",
        "realtor_details_of_sale",
        ["realtor_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_constraint(
        op.f("fk_document_attachments_realtor_id_realtor_details_of_sale"),
        "document_attachments",
        type_="foreignkey",
    )
    op.drop_column("document_attachments", "realtor_id")
    # ### end Alembic commands ###
