"""Update fi sales agreement model

Revision ID: c5ad803da40b
Revises: b299c954e8a8
Create Date: 2025-02-27 13:31:56.579954

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "c5ad803da40b"
down_revision = "b299c954e8a8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement", sa.Column("lease_deposit", sa.Float(), nullable=True)
    )
    op.alter_column(
        "fi_sales_agreement",
        "average_selling_time_estimate",
        existing_type=mysql.FLOAT(),
        type_=sa.String(length=250),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_sales_agreement",
        "average_selling_time_estimate",
        existing_type=sa.String(length=250),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.drop_column("fi_sales_agreement", "lease_deposit")
    # ### end Alembic commands ###
