"""like to favorite

Revision ID: 0a2f334a784a
Revises: caaca4fce7db
Create Date: 2024-08-21 09:26:30.037784

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "0a2f334a784a"
down_revision = "caaca4fce7db"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "match_making_property",
        "like",
        new_column_name="is_favorite",
        existing_type=mysql.BOOLEAN,
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "match_making_property",
        "is_favorite",
        new_column_name="like",
        existing_type=mysql.BOOLEAN,
        existing_nullable=True,
    )
    # ### end Alembic commands ###
