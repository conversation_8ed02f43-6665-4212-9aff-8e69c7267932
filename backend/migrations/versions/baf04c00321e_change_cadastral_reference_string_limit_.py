"""Change cadastral reference string limit to 50

Revision ID: baf04c00321e
Revises: 62e36306a56f
Create Date: 2024-04-08 11:30:03.932200

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "baf04c00321e"
down_revision = "62e36306a56f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "cadastral_reference",
        existing_type=mysql.VARCHAR(length=20),
        type_=sa.String(length=50),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "property",
        "cadastral_reference",
        existing_type=sa.String(length=50),
        type_=mysql.VARCHAR(length=20),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
