"""Remove listing_type property and add listing_type database

Revision ID: 17cfdc6d3794
Revises: 89f4b818ad57
Create Date: 2024-01-04 11:12:37.828721

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '17cfdc6d3794'
down_revision = '89f4b818ad57'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('listing_type',
    sa.Column('name', sa.Text(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_listing_type'))
    )
    op.create_table('property_listing_type',
    sa.Column('listing_type_id', sa.BigInteger(), nullable=False),
    sa.Column('property_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['listing_type_id'], ['listing_type.id'], name=op.f('fk_property_listing_type_listing_type_id_listing_type')),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], name=op.f('fk_property_listing_type_property_id_property')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_property_listing_type')),
    sa.UniqueConstraint('listing_type_id', 'property_id', name='uq_listingtype_property')
    )
    op.create_index('ix_property_listingtype_listingtype', 'property_listing_type', ['listing_type_id'], unique=False)
    op.create_index('ix_property_listingtype_property', 'property_listing_type', ['property_id'], unique=False)
    op.drop_index('ix_property_price', table_name='property')
    op.create_index(op.f('ix_property_price_sale'), 'property', ['price_sale'], unique=False)
    op.drop_column('property', 'listing_type')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property', sa.Column('listing_type', mysql.VARCHAR(length=100), nullable=False))
    op.drop_index(op.f('ix_property_price_sale'), table_name='property')
    op.create_index('ix_property_price', 'property', ['price_sale'], unique=False)
    op.drop_index('ix_property_listingtype_property', table_name='property_listing_type')
    op.drop_index('ix_property_listingtype_listingtype', table_name='property_listing_type')
    op.drop_table('property_listing_type')
    op.drop_table('listing_type')
    # ### end Alembic commands ###
