"""add organization ad templates options

Revision ID: 6b2c39b1773f
Revises: d28227503d4d
Create Date: 2025-04-14 15:10:47.938229

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6b2c39b1773f'
down_revision = 'd28227503d4d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ad_template',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('organization_id', sa.BigInteger(), nullable=False),
    sa.<PERSON>umn('image_url', sa.String(length=255), nullable=False),
    sa.Column('smartly_id', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['organization.id'], name=op.f('fk_ad_template_organization_id_organization')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_ad_template'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ad_template')
    # ### end Alembic commands ###
