"""database restructure

Revision ID: fbe903764fce
Revises: 73c6eb8a2c47
Create Date: 2023-07-26 18:02:15.044843

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "fbe903764fce"
down_revision = "73c6eb8a2c47"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "area_level_1",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("country", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_area_level_1")),
    )
    op.create_table(
        "feature_group",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.<PERSON>nteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_feature_group")),
    )
    op.create_table(
        "orientation",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_orientation")),
    )
    op.create_table(
        "property_type",
        sa.Column("name", sa.String(length=100), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_type")),
    )
    op.create_table(
        "setting",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_setting")),
    )
    op.create_table(
        "view",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_view")),
    )
    op.create_table(
        "area_level_2",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("area_level_1_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["area_level_1_id"],
            ["area_level_1.id"],
            name=op.f("fk_area_level_2_area_level_1_id_area_level_1"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_area_level_2")),
    )
    op.create_table(
        "feature",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("feature_group_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["feature_group_id"],
            ["feature_group.id"],
            name=op.f("fk_feature_feature_group_id_feature_group"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_feature")),
    )
    op.create_table(
        "area_level_3",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("area_level_2_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["area_level_2_id"],
            ["area_level_2.id"],
            name=op.f("fk_area_level_3_area_level_2_id_area_level_2"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_area_level_3")),
    )
    op.create_table(
        "area_level_4",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("area_level_3_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["area_level_3_id"],
            ["area_level_3.id"],
            name=op.f("fk_area_level_4_area_level_3_id_area_level_3"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_area_level_4")),
    )
    op.create_table(
        "area_level_5",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("area_level_4_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["area_level_4_id"],
            ["area_level_4.id"],
            name=op.f("fk_area_level_5_area_level_4_id_area_level_4"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_area_level_5")),
    )
    op.create_table(
        "property",
        sa.Column("reference", sa.String(length=20), nullable=False),
        sa.Column("title", sa.Text(), nullable=True),
        sa.Column("slug", sa.Text(), nullable=True),
        sa.Column("data_source", sa.String(length=100), nullable=True),
        sa.Column("area_level_1_id", sa.BigInteger(), nullable=True),
        sa.Column("area_level_2_id", sa.BigInteger(), nullable=True),
        sa.Column("area_level_3_id", sa.BigInteger(), nullable=True),
        sa.Column("area_level_4_id", sa.BigInteger(), nullable=True),
        sa.Column("area_level_5_id", sa.BigInteger(), nullable=True),
        sa.Column("location", sa.String(length=100), nullable=True),
        sa.Column("listing_type", sa.String(length=100), nullable=False),
        sa.Column("property_type_id", sa.BigInteger(), nullable=False),
        sa.Column("bedrooms", sa.Integer(), nullable=True),
        sa.Column("bathrooms", sa.Integer(), nullable=True),
        sa.Column("pax", sa.Integer(), nullable=True),
        sa.Column("price_old", sa.Integer(), nullable=True),
        sa.Column("price", sa.Integer(), nullable=True),
        sa.Column("price_square_meter", sa.Integer(), nullable=True),
        sa.Column("price_change_percent", sa.Float(), nullable=True),
        sa.Column("currency", sa.String(length=20), nullable=True),
        sa.Column("built_year", sa.Integer(), nullable=True),
        sa.Column("built_area", sa.Integer(), nullable=True),
        sa.Column("interior_area", sa.Integer(), nullable=True),
        sa.Column("plot_area", sa.Integer(), nullable=True),
        sa.Column("terrace_area", sa.Integer(), nullable=True),
        sa.Column("levels", sa.Integer(), nullable=True),
        sa.Column("floor", sa.Integer(), nullable=True),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("status", sa.String(length=20), nullable=False),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.Column("latitude", sa.Float(), nullable=True),
        sa.Column("longitude", sa.Float(), nullable=True),
        sa.Column("is_public_coordinates", sa.Boolean(), nullable=False),
        sa.Column("main_img", sa.Text(), nullable=True),
        sa.Column("private_info", sa.JSON(), nullable=False),
        sa.Column("is_exclusive", sa.Boolean(), nullable=False),
        sa.Column("portals", sa.JSON(), nullable=False),
        sa.Column("conditions", sa.String(length=100), nullable=True),
        sa.Column("legacy_data", sa.JSON(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["area_level_1_id"],
            ["area_level_1.id"],
            name=op.f("fk_property_area_level_1_id_area_level_1"),
        ),
        sa.ForeignKeyConstraint(
            ["area_level_2_id"],
            ["area_level_2.id"],
            name=op.f("fk_property_area_level_2_id_area_level_2"),
        ),
        sa.ForeignKeyConstraint(
            ["area_level_3_id"],
            ["area_level_3.id"],
            name=op.f("fk_property_area_level_3_id_area_level_3"),
        ),
        sa.ForeignKeyConstraint(
            ["area_level_4_id"],
            ["area_level_4.id"],
            name=op.f("fk_property_area_level_4_id_area_level_4"),
        ),
        sa.ForeignKeyConstraint(
            ["area_level_5_id"],
            ["area_level_5.id"],
            name=op.f("fk_property_area_level_5_id_area_level_5"),
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name=op.f("fk_property_organization_id_organization"),
        ),
        sa.ForeignKeyConstraint(
            ["property_type_id"],
            ["property_type.id"],
            name=op.f("fk_property_property_type_id_property_type"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property")),
    )
    op.create_index(
        op.f("ix_property_bathrooms"), "property", ["bathrooms"], unique=False
    )
    op.create_index(
        op.f("ix_property_bedrooms"), "property", ["bedrooms"], unique=False
    )
    op.create_index(
        op.f("ix_property_organization_id"),
        "property",
        ["organization_id"],
        unique=False,
    )
    op.create_index(op.f("ix_property_price"), "property", ["price"], unique=False)
    op.create_index(
        op.f("ix_property_reference"), "property", ["reference"], unique=True
    )
    op.create_index(op.f("ix_property_status"), "property", ["status"], unique=False)
    op.create_table(
        "property_description",
        sa.Column("property_id", sa.BigInteger(), nullable=True),
        sa.Column("tagline", sa.Text(), nullable=False),
        sa.Column("description", mysql.LONGTEXT(), nullable=False),
        sa.Column("type", sa.String(length=100), nullable=False),
        sa.Column("language", sa.String(length=100), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_description_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_description")),
    )
    op.create_table(
        "property_feature",
        sa.Column("feature_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["feature_id"],
            ["feature.id"],
            name=op.f("fk_property_feature_feature_id_feature"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_feature_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_feature")),
    )
    op.create_table(
        "property_orientation",
        sa.Column("orientation_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["orientation_id"],
            ["orientation.id"],
            name=op.f("fk_property_orientation_orientation_id_orientation"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_orientation_property_id_property"),
        ),
        sa.PrimaryKeyConstraint(
            "orientation_id", "property_id", "id", name=op.f("pk_property_orientation")
        ),
    )
    op.create_table(
        "property_setting",
        sa.Column("setting_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_setting_property_id_property"),
        ),
        sa.ForeignKeyConstraint(
            ["setting_id"],
            ["setting.id"],
            name=op.f("fk_property_setting_setting_id_setting"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_setting")),
    )
    op.create_table(
        "property_view",
        sa.Column("view_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_view_property_id_property"),
        ),
        sa.ForeignKeyConstraint(
            ["view_id"], ["view.id"], name=op.f("fk_property_view_view_id_view")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_view")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("property_view")
    op.drop_table("property_setting")
    op.drop_table("property_orientation")
    op.drop_table("property_feature")
    op.drop_table("property_description")
    op.drop_index(op.f("ix_property_status"), table_name="property")
    op.drop_index(op.f("ix_property_reference"), table_name="property")
    op.drop_index(op.f("ix_property_price"), table_name="property")
    op.drop_index(op.f("ix_property_organization_id"), table_name="property")
    op.drop_index(op.f("ix_property_bedrooms"), table_name="property")
    op.drop_index(op.f("ix_property_bathrooms"), table_name="property")
    op.drop_table("property")
    op.drop_table("area_level_5")
    op.drop_table("area_level_4")
    op.drop_table("area_level_3")
    op.drop_table("feature")
    op.drop_table("area_level_2")
    op.drop_table("view")
    op.drop_table("setting")
    op.drop_table("property_type")
    op.drop_table("orientation")
    op.drop_table("feature_group")
    op.drop_table("area_level_1")
    # ### end Alembic commands ###
