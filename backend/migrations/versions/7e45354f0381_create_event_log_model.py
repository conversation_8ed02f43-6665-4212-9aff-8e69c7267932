"""Create event log model

Revision ID: 7e45354f0381
Revises: 597698cf7489
Create Date: 2025-03-17 14:34:43.177599

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "7e45354f0381"
down_revision = "597698cf7489"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "event_log",
        sa.Column("object_type", sa.String(length=50), nullable=False),
        sa.Column("object_id", sa.Integer(), nullable=False),
        sa.Column("actor_id", sa.<PERSON>ger(), nullable=False),
        sa.Column("action", sa.String(length=50), nullable=False),
        sa.Column("data_before", sa.JSON(), nullable=True),
        sa.Column("data_after", sa.<PERSON>(), nullable=True),
        sa.Column("details", sa.JSO<PERSON>(), nullable=True),
        sa.Column("relation_object_type", sa.String(length=50), nullable=True),
        sa.Column("relation_object_id", sa.Integer(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["actor_id"], ["user.id"], name=op.f("fk_event_log_actor_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_event_log")),
    )
    op.create_index(
        "idx_object_type_object_id",
        "event_log",
        ["object_type", "object_id"],
        unique=False,
    )
    op.create_index(
        "idx_relation_object_type_object_id",
        "event_log",
        ["relation_object_type", "object_id"],
        unique=False,
    )
    op.create_index(op.f("ix_event_log_action"), "event_log", ["action"], unique=False)
    op.create_index(
        op.f("ix_event_log_object_type"), "event_log", ["object_type"], unique=False
    )
    op.create_index(
        op.f("ix_event_log_relation_object_type"),
        "event_log",
        ["relation_object_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_event_log_actor_id"), "event_log", ["actor_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_event_log_actor_id"), table_name="event_log")
    op.drop_index(op.f("ix_event_log_action"), table_name="event_log")
    op.drop_index(op.f("ix_event_log_object_type"), table_name="event_log")
    op.drop_index(op.f("ix_event_log_relation_object_type"), table_name="event_log")
    op.drop_index("idx_relation_object_type_object_id", table_name="event_log")
    op.drop_index("idx_object_type_object_id", table_name="event_log")
    op.drop_table("event_log")
    # ### end Alembic commands ###
