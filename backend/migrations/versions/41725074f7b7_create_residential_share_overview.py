"""Create residential_share_overview

Revision ID: 41725074f7b7
Revises: b55c35c008b0
Create Date: 2024-07-24 05:13:54.503345

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "41725074f7b7"
down_revision = "b55c35c008b0"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_residential_share_overview",
        sa.Column("residential_type_code", sa.String(length=50), nullable=True),
        sa.Column("ownership_type_code", sa.String(length=50), nullable=True),
        sa.Column("administration", sa.JSON(), nullable=True),
        sa.Column("apartment", sa.JSON(), nullable=True),
        sa.Column("debt_free_price", sa.Integer(), nullable=True),
        sa.Column("debt_share_amount", sa.Integer(), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("starting_debt_free_price", sa.Integer(), nullable=True),
        sa.Column("storages", sa.JSO<PERSON>(), nullable=True),
        sa.Column("limits_of_storage_usage", sa.JSON(), nullable=True),
        sa.Column("parking_spaces", sa.JSON(), nullable=True),
        sa.Column("parking_space_description", sa.JSON(), nullable=True),
        sa.Column("share_certificate", sa.JSON(), nullable=True),
        sa.Column("redemption", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_residential_share_overview")),
    )


def downgrade():
    op.drop_table("fi_residential_share_overview")
