"""add more explicit contact related party types

Revision ID: 15f29bcd91c2
Revises: 0a6e2e0778de
Create Date: 2025-05-02 07:02:54.195289

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "15f29bcd91c2"
down_revision = "0a6e2e0778de"
branch_labels = None
depends_on = None


def upgrade():
    # Update related_party_type to company_beneficiary for organization contacts
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'company_beneficiary'
        WHERE related_party_type = 'member'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'organization'
        )
    """
    )

    # Update related_party_type to company_signing_rights for organization contacts
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'company_signing_rights'
        WHERE related_party_type = 'main'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'organization'
        )
    """
    )

    # Update related_party_type to estate_partner for estate contacts
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'estate_party'
        WHERE related_party_type = 'member'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'estate'
        )
    """
    )

    # Update related_party_type to estate_authorized_partner for estate contacts
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'estate_authorized_party'
        WHERE related_party_type = 'main'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'estate'
        )
    """
    )


def downgrade():
    # Revert the changes for company_beneficiary
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'member'
        WHERE related_party_type = 'company_beneficiary'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'organization'
        )
    """
    )

    # Revert the changes for company_signing_rights
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'main'
        WHERE related_party_type = 'company_signing_rights'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'organization'
        )
    """
    )

    # Revert the changes for estate_partner
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'member'
        WHERE related_party_type = 'estate_party'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'estate'
        )
    """
    )

    # Revert the changes for estate_authorized_partner
    op.execute(
        """
        UPDATE contact_related_party
        SET related_party_type = 'main'
        WHERE related_party_type = 'estate_authorized_party'
        AND contact_id IN (
            SELECT id FROM contact WHERE type = 'estate'
        )
    """
    )
