"""add_contract_info

Revision ID: 5b90b94e7713
Revises: 3f1a5f10f19d
Create Date: 2024-08-29 21:32:44.636228

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "5b90b94e7713"
down_revision = "3f1a5f10f19d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("offer", sa.Column("office_id", sa.BigInteger(), nullable=True))
    op.create_foreign_key(
        op.f("fk_offer_office_id_office"), "offer", "office", ["office_id"], ["id"]
    )
    op.add_column(
        "sales_agreement", sa.Column("office_id", sa.BigInteger(), nullable=True)
    )
    op.add_column(
        "sales_agreement", sa.Column("created_by", sa.<PERSON>Integer(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_sales_agreement_created_by_user"),
        "sales_agreement",
        "user",
        ["created_by"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_sales_agreement_office_id_office"),
        "sales_agreement",
        "office",
        ["office_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_sales_agreement_office_id_office"),
        "sales_agreement",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_sales_agreement_created_by_user"),
        "sales_agreement",
        type_="foreignkey",
    )
    op.drop_column("sales_agreement", "created_by")
    op.drop_column("sales_agreement", "office_id")
    op.drop_constraint(op.f("fk_offer_office_id_office"), "offer", type_="foreignkey")
    op.drop_column("offer", "office_id")
    # ### end Alembic commands ###
