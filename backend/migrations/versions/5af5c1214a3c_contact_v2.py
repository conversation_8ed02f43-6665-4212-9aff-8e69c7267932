"""contact_v2

Revision ID: 5af5c1214a3c
Revises: 5b1d8d0776bb
Create Date: 2025-04-29 13:22:11.882469

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "5af5c1214a3c"
down_revision = "5b1d8d0776bb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "contact_related_party",
        sa.<PERSON>umn("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("related_party_id", sa.BigInteger(), nullable=False),
        sa.Column("related_party_type", sa.String(length=50), nullable=False),
        sa.Column("id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_contact_related_party_contact_id_contact"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["related_party_id"],
            ["contact.id"],
            name=op.f("fk_contact_related_party_related_party_id_contact"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contact_related_party")),
    )
    op.add_column(
        "contact", sa.Column("first_name", sa.String(length=50), nullable=True)
    )
    op.add_column(
        "contact", sa.Column("last_name", sa.String(length=50), nullable=True)
    )
    op.add_column(
        "contact", sa.Column("date_of_birth", sa.String(length=50), nullable=True)
    )
    op.add_column(
        "contact", sa.Column("confidential_customer", sa.Boolean(), nullable=True)
    )
    op.add_column(
        "contact", sa.Column("consent_email_marketing", sa.Boolean(), nullable=True)
    )
    op.add_column(
        "contact",
        sa.Column("consent_newsletter_marketing", sa.Boolean(), nullable=True),
    )
    op.alter_column(
        "contact",
        "name",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        nullable=True,
    )
    op.alter_column(
        "contact",
        "email",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        existing_nullable=True,
    )
    op.drop_index("uq_contact_email", table_name="contact")
    op.create_index(
        "ix_contact_fulltext",
        "contact",
        ["name", "email", "first_name", "last_name"],
        unique=False,
        mysql_prefix="FULLTEXT",
    )
    op.alter_column(
        "contact_phone",
        "phone_number",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.create_index(
        "ix_contact_phone_fulltext",
        "contact_phone",
        ["phone_number"],
        unique=False,
        mysql_prefix="FULLTEXT",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_contact_phone_fulltext", table_name="contact_phone", mysql_prefix="FULLTEXT"
    )
    op.alter_column(
        "contact_phone",
        "phone_number",
        existing_type=sa.String(length=50),
        type_=mysql.TEXT(),
        existing_nullable=False,
    )
    op.drop_index("ix_contact_fulltext", table_name="contact", mysql_prefix="FULLTEXT")
    op.create_index("uq_contact_email", "contact", ["email"], unique=True)
    op.alter_column(
        "contact",
        "email",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    op.alter_column(
        "contact",
        "name",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        nullable=False,
    )
    op.drop_column("contact", "consent_newsletter_marketing")
    op.drop_column("contact", "consent_email_marketing")
    op.drop_column("contact", "confidential_customer")
    op.drop_column("contact", "date_of_birth")
    op.drop_column("contact", "last_name")
    op.drop_column("contact", "first_name")
    op.drop_table("contact_related_party")
    # ### end Alembic commands ###
