"""adding sold by field to property

Revision ID: d8fe1521ac05
Revises: fd92e79bbd3a
Create Date: 2024-05-29 11:17:31.447837

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d8fe1521ac05"
down_revision = "fd92e79bbd3a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("property", sa.Column("sold_by", sa.String(length=20), nullable=True))


def downgrade():
    op.drop_column("property", "sold_by")
