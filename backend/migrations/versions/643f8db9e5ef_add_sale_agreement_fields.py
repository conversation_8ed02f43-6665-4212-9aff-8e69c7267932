"""add sale agreement fields

Revision ID: 643f8db9e5ef
Revises: 8787bc353a37
Create Date: 2023-08-21 08:54:07.028196

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "643f8db9e5ef"
down_revision = "8787bc353a37"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact", sa.Column("address", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("passport_number", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("nationality", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("country_specific", sa.JSON(), nullable=True))
    op.add_column(
        "property", sa.Column("legal_representative", sa.J<PERSON>(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "legal_representative")
    op.drop_column("contact", "country_specific")
    op.drop_column("contact", "nationality")
    op.drop_column("contact", "passport_number")
    op.drop_column("contact", "address")
    # ### end Alembic commands ###
