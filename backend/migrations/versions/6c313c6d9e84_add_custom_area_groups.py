"""Add custom area groups

Revision ID: 6c313c6d9e84
Revises: 2d963a5e4e09
Create Date: 2024-10-14 00:51:22.612776

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6c313c6d9e84'
down_revision = '2d963a5e4e09'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('es_custom_area_group_1',
    sa.Column('name', sa.Text(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_es_custom_area_group_1'))
    )
    op.create_index(op.f('ix_es_custom_area_group_1_name'), 'es_custom_area_group_1', ['name'], unique=False)
    op.create_table('es_custom_area_group_2',
    sa.Column('name', sa.Text(), nullable=False),
    sa.Column('es_custom_area_group_1_id', sa.BigInteger(), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['es_custom_area_group_1_id'], ['es_custom_area_group_1.id'], name=op.f('fk_es_custom_area_group_2_es_custom_area_group_1_id_es_custom_area_group_1')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_es_custom_area_group_2'))
    )
    op.create_index(op.f('ix_es_custom_area_group_2_name'), 'es_custom_area_group_2', ['name'], unique=False)
    op.add_column('area_level_1', sa.Column('internal_name', sa.Text(), nullable=True))
    op.add_column('area_level_1', sa.Column('es_custom_area_group_2_id', sa.BigInteger(), nullable=True))
    op.create_foreign_key(op.f('fk_area_level_1_es_custom_area_group_2_id_es_custom_area_group_2'), 'area_level_1', 'es_custom_area_group_2', ['es_custom_area_group_2_id'], ['id'])
    op.add_column('area_level_2', sa.Column('internal_name', sa.Text(), nullable=True))
    op.add_column('area_level_3', sa.Column('internal_name', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('area_level_3', 'internal_name')
    op.drop_column('area_level_2', 'internal_name')
    op.drop_constraint(op.f('fk_area_level_1_es_custom_area_group_2_id_es_custom_area_group_2'), 'area_level_1', type_='foreignkey')
    op.drop_column('area_level_1', 'es_custom_area_group_2_id')
    op.drop_column('area_level_1', 'internal_name')
    op.drop_index(op.f('ix_es_custom_area_group_2_name'), table_name='es_custom_area_group_2')
    op.drop_table('es_custom_area_group_2')
    op.drop_index(op.f('ix_es_custom_area_group_1_name'), table_name='es_custom_area_group_1')
    op.drop_table('es_custom_area_group_1')
    # ### end Alembic commands ###
