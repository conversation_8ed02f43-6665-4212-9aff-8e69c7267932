"""update fi sales agreement model

Revision ID: eb124a1c8be7
Revises: d12c7edc034d
Create Date: 2025-08-20 14:17:33.012149

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "eb124a1c8be7"
down_revision = "d12c7edc034d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement",
        sa.Column("share_register_format", sa.String(length=50), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement",
        sa.Column("share_register_storage", sa.String(length=250), nullable=True),
    )
    op.drop_column("fi_sales_agreement_contact_acquisition", "share_register_storage")
    op.drop_column("fi_sales_agreement_contact_acquisition", "share_register_format")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement_contact_acquisition",
        sa.Column("share_register_format", mysql.VARCHAR(length=50), nullable=True),
    )
    op.add_column(
        "fi_sales_agreement_contact_acquisition",
        sa.Column("share_register_storage", mysql.VARCHAR(length=250), nullable=True),
    )
    op.drop_column("fi_sales_agreement", "share_register_storage")
    op.drop_column("fi_sales_agreement", "share_register_format")
    # ### end Alembic commands ###
