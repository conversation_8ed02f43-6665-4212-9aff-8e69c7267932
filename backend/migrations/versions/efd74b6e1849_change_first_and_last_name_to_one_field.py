"""change first and last name to one field

Revision ID: efd74b6e1849
Revises: 23030a2e330b
Create Date: 2024-02-21 08:50:04.501460

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "efd74b6e1849"
down_revision = "23030a2e330b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contact",
        "first_name",
        existing_type=sa.Text(),
        nullable=False,
        new_column_name="name",
    )
    op.drop_column("contact", "last_name")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contact",
        "name",
        existing_type=sa.Text(),
        nullable=False,
        new_column_name="first_name",
    )
    op.add_column("contact", sa.Column("last_name", mysql.TEXT(), nullable=False))
    # ### end Alembic commands ###
