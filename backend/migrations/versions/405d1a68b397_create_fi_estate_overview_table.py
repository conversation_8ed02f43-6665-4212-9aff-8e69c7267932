"""Create fi_estate_overview table

Revision ID: 405d1a68b397
Revises: 20293dacce8b
Create Date: 2024-07-23 11:18:54.557711

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "405d1a68b397"
down_revision = "20293dacce8b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_estate_overview",
        sa.Column("estate_property_type_code", sa.String(length=50), nullable=False),
        sa.Column("areas", sa.JSON(), nullable=True),
        sa.Column("partition_type_code", sa.String(length=50), nullable=True),
        sa.Column("partition_min_size", sa.JSON(), nullable=True),
        sa.Column("forest_land_description", sa.JSON(), nullable=True),
        sa.Column(
            "immediate_forest_logging_possibilities_in_cubic_meters",
            sa.Integer(),
            nullable=True,
        ),
        sa.Column("forest_logging_potential_description", sa.JSON(), nullable=True),
        sa.Column("arable_land_description", sa.JSON(), nullable=True),
        sa.Column("forest_stand", sa.JSON(), nullable=True),
        sa.Column("land_areas", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_estate_overview")),
    )


def downgrade():
    op.drop_table("fi_estate_overview")
