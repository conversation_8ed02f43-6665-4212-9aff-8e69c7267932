"""Add Offer table

Revision ID: 15ea733434d6
Revises: d8fe1521ac05
Create Date: 2024-05-25 21:57:37.844143

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "15ea733434d6"
down_revision = "d8fe1521ac05"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "offer",
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("sale_price", sa.Integer(), nullable=False),
        sa.Column("special_condition", sa.Text(), nullable=True),
        sa.Column("deposit_amount", sa.Numeric(), nullable=False),
        sa.Column("iban_number", sa.String(length=40), nullable=True),
        sa.Column("deposit_payment_paid_to", sa.String(length=25), nullable=False),
        sa.Column("price_settled_by", sa.DateTime(), nullable=False),
        sa.Column("status", sa.String(length=10), nullable=False),
        sa.Column("signing_method", sa.String(length=15), nullable=False),
        sa.Column("document_id", sa.BigInteger(), nullable=False),
        sa.Column("created_by", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by"], ["user.id"], name=op.f("fk_offer_created_by_user")
        ),
        sa.ForeignKeyConstraint(
            ["document_id"], ["document.id"], name=op.f("fk_offer_document_id_document")
        ),
        sa.ForeignKeyConstraint(
            ["property_id"], ["property.id"], name=op.f("fk_offer_property_id_property")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_offer")),
    )
    op.create_table(
        "buyer_offer",
        sa.Column("offer_id", sa.BigInteger(), nullable=True),
        sa.Column("buyer_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["buyer_id"], ["contact.id"], name=op.f("fk_buyer_offer_buyer_id_contact")
        ),
        sa.ForeignKeyConstraint(
            ["offer_id"], ["offer.id"], name=op.f("fk_buyer_offer_offer_id_offer")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_buyer_offer")),
        sa.UniqueConstraint(
            "offer_id", "buyer_id", name=op.f("uq_buyer_offer_offer_id")
        ),
    )
    op.create_index(
        op.f("ix_buyer_offer_buyer_id"), "buyer_offer", ["buyer_id"], unique=False
    )
    op.create_index(
        op.f("ix_buyer_offer_offer_id"), "buyer_offer", ["offer_id"], unique=False
    )
    op.create_table(
        "seller_offer",
        sa.Column("offer_id", sa.BigInteger(), nullable=True),
        sa.Column("seller_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["offer_id"], ["offer.id"], name=op.f("fk_seller_offer_offer_id_offer")
        ),
        sa.ForeignKeyConstraint(
            ["seller_id"],
            ["contact.id"],
            name=op.f("fk_seller_offer_seller_id_contact"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_seller_offer")),
        sa.UniqueConstraint(
            "offer_id", "seller_id", name=op.f("uq_seller_offer_offer_id")
        ),
    )
    op.create_index(
        op.f("ix_seller_offer_offer_id"), "seller_offer", ["offer_id"], unique=False
    )
    op.create_index(
        op.f("ix_seller_offer_seller_id"), "seller_offer", ["seller_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("seller_offer")
    op.drop_table("buyer_offer")
    op.drop_table("offer")
    # ### end Alembic commands ###
