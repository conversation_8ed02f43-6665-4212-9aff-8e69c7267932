"""Increase size for manager name

Revision ID: 8f4819abd975
Revises: e21821a610b0
Create Date: 2024-12-26 10:51:52.765276

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8f4819abd975'
down_revision = 'e21821a610b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'manager_name',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.String(length=250),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'manager_name',
               existing_type=sa.String(length=250),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=True)
    # ### end Alembic commands ###
