"""add field property_reference to DiasAttachment

Revision ID: 37d1cd852b68
Revises: 3666c014d17c
Create Date: 2024-10-30 13:44:15.493250

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "37d1cd852b68"
down_revision = "3666c014d17c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_attachment",
        sa.Column("property_reference", sa.String(length=200), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("dias_attachment", "property_reference")
    # ### end Alembic commands ###
