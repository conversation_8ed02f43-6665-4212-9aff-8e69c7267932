"""Property info

Revision ID: 73c6eb8a2c47
Revises: c048acc3346a
Create Date: 2023-06-18 16:38:19.249407

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "73c6eb8a2c47"
down_revision = "fd9b8e28bb43"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "property_info",
        sa.Column("reference", sa.String(length=100), nullable=False),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("status", sa.Text(), nullable=False),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.Column("private_info", sa.JSON(), nullable=False),
        sa.Column("is_exclusive", sa.<PERSON>(), nullable=False),
        sa.Column("is_leadingre_enabled", sa.<PERSON>(), nullable=False),
        sa.Column("is_idealista_enabled", sa.<PERSON>(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name=op.f("fk_property_info_organization_id_organization"),
        ),
        sa.ForeignKeyConstraint(
            ["reference"],
            ["dw_objects.reference"],
            name=op.f("fk_property_info_reference_dw_objects"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_info")),
    )
    op.create_index(
        op.f("ix_property_info_organization_id"),
        "property_info",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_info_reference"), "property_info", ["reference"], unique=False
    )
    op.create_table(
        "property_realtor",
        sa.Column("reference", sa.String(length=100), nullable=False),
        sa.Column("user_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["reference"],
            ["dw_objects.reference"],
            name=op.f("fk_property_realtor_reference_dw_objects"),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_property_realtor_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_realtor")),
        sa.UniqueConstraint(
            "reference", "user_id", name=op.f("uq_property_realtor_reference")
        ),
    )
    op.create_index(
        op.f("ix_property_realtor_reference"),
        "property_realtor",
        ["reference"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_realtor_user_id"),
        "property_realtor",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_property_realtor_user_id_user"), "property_realtor", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_property_realtor_reference_dw_objects"),
        "property_realtor",
        type_="foreignkey",
    )
    op.drop_table("property_realtor")
    op.drop_constraint(
        op.f("fk_property_info_reference_dw_objects"),
        "property_info",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_property_info_organization_id_organization"),
        "property_info",
        type_="foreignkey",
    )
    op.drop_table("property_info")
    # ### end Alembic commands ###
