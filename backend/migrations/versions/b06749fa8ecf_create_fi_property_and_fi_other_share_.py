"""Create fi_property and fi_other_share_overview

Revision ID: b06749fa8ecf
Revises: 6ffef3faccde
Create Date: 2024-07-25 05:25:31.353122

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b06749fa8ecf"
down_revision = "6ffef3faccde"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_other_share_overview",
        sa.Column("other_share_type_code", sa.String(length=50), nullable=True),
        sa.Column("administration", sa.JSON(), nullable=True),
        sa.Column("area_basis_code", sa.JSON(), nullable=True),
        sa.Column("floor_area", sa.JSON(), nullable=True),
        sa.Column("total_area", sa.JSON(), nullable=True),
        sa.Column("debt_free_price", sa.Integer(), nullable=True),
        sa.Column("debt_share_amount", sa.Integer(), nullable=True),
        sa.Column("starting_debt_free_price", sa.Integer(), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("share_certificate", sa.JSON(), nullable=True),
        sa.Column("redemption", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_other_share_overview")),
    )

    op.create_table(
        "fi_property",
        sa.Column("fi_property_type_id", sa.BigInteger(), nullable=False),
        sa.Column("fi_realty_id", sa.BigInteger(), nullable=False),
        sa.Column("fi_property_overview_id", sa.BigInteger(), nullable=True),
        sa.Column(
            "fi_residential_property_overview_id", sa.BigInteger(), nullable=True
        ),
        sa.Column("fi_plot_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_estate_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_commercial_property_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_housing_company_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_residential_share_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_other_share_overview_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_commercial_property_overview_id"],
            ["fi_commercial_overview.id"],
            name=op.f(
                "fk_fi_property_fi_commercial_property_overview_id_fi_commercial_overview"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["fi_estate_overview_id"],
            ["fi_estate_overview.id"],
            name=op.f("fk_fi_property_fi_estate_overview_id_fi_estate_overview"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_housing_company_id"],
            ["fi_housing_company.id"],
            name=op.f("fk_fi_property_fi_housing_company_id_fi_housing_company"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_other_share_overview_id"],
            ["fi_other_share_overview.id"],
            name=op.f(
                "fk_fi_property_fi_other_share_overview_id_fi_other_share_overview"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["fi_plot_overview_id"],
            ["fi_plot_overview.id"],
            name=op.f("fk_fi_property_fi_plot_overview_id_fi_plot_overview"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_property_overview_id"],
            ["fi_property_overview.id"],
            name=op.f("fk_fi_property_fi_property_overview_id_fi_property_overview"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_property_type_id"],
            ["fi_property_type.id"],
            name=op.f("fk_fi_property_fi_property_type_id_fi_property_type"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_realty_id"],
            ["fi_realty.id"],
            name=op.f("fk_fi_property_fi_realty_id_fi_realty"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_residential_property_overview_id"],
            ["fi_residential_property_overview.id"],
            name=op.f(
                "fk_fi_property_fi_residential_property_overview_id_fi_residential_property_overview"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["fi_residential_share_overview_id"],
            ["fi_residential_share_overview.id"],
            name=op.f(
                "fk_fi_property_fi_residential_share_overview_id_fi_residential_share_overview"
            ),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_property")),
    )

    op.create_index(
        op.f("ix_fi_property_fi_commercial_property_overview_id"),
        "fi_property",
        ["fi_commercial_property_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_estate_overview_id"),
        "fi_property",
        ["fi_estate_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_housing_company_id"),
        "fi_property",
        ["fi_housing_company_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_other_share_overview_id"),
        "fi_property",
        ["fi_other_share_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_plot_overview_id"),
        "fi_property",
        ["fi_plot_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_property_overview_id"),
        "fi_property",
        ["fi_property_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_property_type_id"),
        "fi_property",
        ["fi_property_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_realty_id"),
        "fi_property",
        ["fi_realty_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_residential_property_overview_id"),
        "fi_property",
        ["fi_residential_property_overview_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_property_fi_residential_share_overview_id"),
        "fi_property",
        ["fi_residential_share_overview_id"],
        unique=False,
    )


def downgrade():
    op.drop_constraint(
        op.f(
            "fk_fi_property_fi_commercial_property_overview_id_fi_commercial_overview"
        ),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_estate_overview_id_fi_estate_overview"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_housing_company_id_fi_housing_company"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_other_share_overview_id_fi_other_share_overview"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_plot_overview_id_fi_plot_overview"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_property_overview_id_fi_property_overview"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_property_type_id_fi_property_type"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_fi_property_fi_realty_id_fi_realty"),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f(
            "fk_fi_property_fi_residential_property_overview_id_fi_residential_property_overview"
        ),
        "fi_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f(
            "fk_fi_property_fi_residential_share_overview_id_fi_residential_share_overview"
        ),
        "fi_property",
        type_="foreignkey",
    )

    op.drop_index(
        op.f("ix_fi_property_fi_residential_share_overview_id"),
        table_name="fi_property",
    )
    op.drop_index(
        op.f("ix_fi_property_fi_residential_property_overview_id"),
        table_name="fi_property",
    )
    op.drop_index(op.f("ix_fi_property_fi_realty_id"), table_name="fi_property")
    op.drop_index(op.f("ix_fi_property_fi_property_type_id"), table_name="fi_property")
    op.drop_index(
        op.f("ix_fi_property_fi_property_overview_id"), table_name="fi_property"
    )
    op.drop_index(op.f("ix_fi_property_fi_plot_overview_id"), table_name="fi_property")
    op.drop_index(
        op.f("ix_fi_property_fi_other_share_overview_id"), table_name="fi_property"
    )
    op.drop_index(
        op.f("ix_fi_property_fi_housing_company_id"), table_name="fi_property"
    )
    op.drop_index(
        op.f("ix_fi_property_fi_estate_overview_id"), table_name="fi_property"
    )
    op.drop_index(
        op.f("ix_fi_property_fi_commercial_property_overview_id"),
        table_name="fi_property",
    )

    op.drop_table("fi_property")
    op.drop_table("fi_other_share_overview")
