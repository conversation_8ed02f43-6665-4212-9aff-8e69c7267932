"""Change type of cost for housing company

Revision ID: 1e429e1337f2
Revises: bf53317ef53d
Create Date: 2025-06-10 14:12:24.728074

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "1e429e1337f2"
down_revision = "bf53317ef53d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_housing_company",
        "management_charge",
        existing_type=mysql.INTEGER(display_width=11),
        type_=sa.Float(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_housing_company",
        "special_charge",
        existing_type=mysql.INTEGER(display_width=11),
        type_=sa.Float(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_housing_company",
        "maintenance_charge",
        existing_type=mysql.INTEGER(display_width=11),
        type_=sa.Float(),
        existing_nullable=True,
    )
    op.add_column('fi_residential_property_overview', sa.Column('ownership_type_code', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_residential_property_overview', 'ownership_type_code')
    op.alter_column(
        "fi_housing_company",
        "maintenance_charge",
        existing_type=sa.Float(),
        type_=mysql.INTEGER(display_width=11),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_housing_company",
        "special_charge",
        existing_type=sa.Float(),
        type_=mysql.INTEGER(display_width=11),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_housing_company",
        "management_charge",
        existing_type=sa.Float(),
        type_=mysql.INTEGER(display_width=11),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
