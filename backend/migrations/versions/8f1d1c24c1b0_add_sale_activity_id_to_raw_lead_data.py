"""add sale activity id to raw lead data

Revision ID: 8f1d1c24c1b0
Revises: de34fd9aa8f3
Create Date: 2024-08-09 09:14:33.073885

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "8f1d1c24c1b0"
down_revision = "de34fd9aa8f3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "raw_lead_data", sa.Column("sale_activity_id", sa.BigInteger(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_raw_lead_data_sale_activity_id_lead"),
        "raw_lead_data",
        "lead",
        ["sale_activity_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_raw_lead_data_sale_activity_id_lead"),
        "raw_lead_data",
        type_="foreignkey",
    )
    op.drop_column("raw_lead_data", "sale_activity_id")
    # ### end Alembic commands ###
