"""update missing models

Revision ID: b0b2fff610d1
Revises: 17941950ff54
Create Date: 2025-08-19 08:41:05.950539

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "b0b2fff610d1"
down_revision = "17941950ff54"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "document_library_item_owner",
        "owner_type",
        existing_type=mysql.ENUM(
            "FI_PROPERTY", "FI_SALES_AGREEMENT", "CONTACT", "FI_PURCHASE_OFFER"
        ),
        type_=sa.Enum(
            "FI_PROPERTY",
            "FI_SALES_AGREEMENT",
            "FI_PURCHASE_OFFER",
            "CONTACT",
            name="owner_type_enum",
        ),
        existing_nullable=False,
    )
    op.create_index(
        op.f("ix_fi_lead_details_of_sale_fi_details_of_sale_id"),
        "fi_lead_details_of_sale",
        ["fi_details_of_sale_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_lead_details_of_sale_user_id"),
        "fi_lead_details_of_sale",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_fi_lead_details_of_sale_user_id"), table_name="fi_lead_details_of_sale"
    )
    op.drop_index(
        op.f("ix_fi_lead_details_of_sale_fi_details_of_sale_id"),
        table_name="fi_lead_details_of_sale",
    )
    op.alter_column(
        "document_library_item_owner",
        "owner_type",
        existing_type=sa.Enum(
            "FI_PROPERTY",
            "FI_SALES_AGREEMENT",
            "FI_PURCHASE_OFFER",
            "CONTACT",
            name="owner_type_enum",
        ),
        type_=mysql.ENUM(
            "FI_PROPERTY", "FI_SALES_AGREEMENT", "CONTACT", "FI_PURCHASE_OFFER"
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
