"""empty message

Revision ID: f8606cb85323
Revises: 742a681e503a, add_cancelled_status_to_adstatus
Create Date: 2025-05-27 15:56:09.103309

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f8606cb85323'
down_revision = ('742a681e503a', 'add_cancelled_status_to_adstatus')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
