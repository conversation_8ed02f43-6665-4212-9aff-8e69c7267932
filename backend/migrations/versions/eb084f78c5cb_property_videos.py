"""Property videos

Revision ID: eb084f78c5cb
Revises: baf04c00321e
Create Date: 2024-04-11 15:17:22.700414

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "eb084f78c5cb"
down_revision = "baf04c00321e"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "video",
        sa.Column("url", sa.String(length=255), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("is_hidden", sa.<PERSON>(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"], ["property.id"], name=op.f("fk_video_property_id_property")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_video")),
    )


def downgrade():
    op.drop_table("video")
