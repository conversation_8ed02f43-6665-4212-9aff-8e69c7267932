"""Remove has damages field

Revision ID: e21821a610b0
Revises: 28013ffb2466
Create Date: 2024-12-24 22:05:30.479722

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e21821a610b0"
down_revision = "28013ffb2466"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_realty", "has_damages")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_realty", sa.Column("has_damages", mysql.VARCHAR(length=50), nullable=True)
    )
    # ### end Alembic commands ###
