"""Add advertisements table

Revision ID: 002977723127
Revises: c5ad803da40b
Create Date: 2025-02-27 14:36:42.930706

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '002977723127'
down_revision = 'c5ad803da40b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('advertisement',
    sa.Column('property_id', sa.BigInteger(), nullable=True),
    sa.Column('event_id', sa.BigInteger(), nullable=True),
    sa.Column('fi_property_id', sa.BigInteger(), nullable=True),
    sa.Column('fi_agent_id', sa.BigInteger(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('owner_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(length=512), nullable=True),
    sa.Column('type', sa.Enum('LISTING_PROPERTY', 'PROPERTY_SOLD', 'AGENT', 'EVENT', 'CUSTOM', name='adtype'), nullable=False),
    sa.Column('status', sa.Enum('DRAFT', 'IN_REVIEW', 'ACTIVE', 'COMPLETED', name='adstatus'), nullable=False),
    sa.Column('status_reason', sa.String(length=512), nullable=True),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('budget_total', sa.Float(), nullable=False),
    sa.Column('budget_daily', sa.Float(), nullable=True),
    sa.Column('target_area_radius_km', sa.Float(), nullable=False),
    sa.Column('conversion_location_url', sa.String(length=512), nullable=True),
    sa.Column('entry_point', sa.String(length=50), nullable=True),
    sa.Column('metrics', sa.JSON(), nullable=True),
    sa.Column('ad_content', sa.JSON(), nullable=True),
    sa.Column('preview_url', sa.String(length=512), nullable=True),
    sa.Column('is_smartly_ad', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], name=op.f('fk_advertisement_event_id_event')),
    sa.ForeignKeyConstraint(['fi_agent_id'], ['fi_agent.id'], name=op.f('fk_advertisement_fi_agent_id_fi_agent')),
    sa.ForeignKeyConstraint(['fi_property_id'], ['fi_property.id'], name=op.f('fk_advertisement_fi_property_id_fi_property')),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], name=op.f('fk_advertisement_owner_id_user')),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], name=op.f('fk_advertisement_property_id_property')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_advertisement'))
    )
    op.create_index(op.f('ix_advertisement_event_id'), 'advertisement', ['event_id'], unique=False)
    op.create_index(op.f('ix_advertisement_fi_agent_id'), 'advertisement', ['fi_agent_id'], unique=False)
    op.create_index(op.f('ix_advertisement_fi_property_id'), 'advertisement', ['fi_property_id'], unique=False)
    op.create_index(op.f('ix_advertisement_owner_id'), 'advertisement', ['owner_id'], unique=False)
    op.create_index(op.f('ix_advertisement_property_id'), 'advertisement', ['property_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_advertisement_property_id'), table_name='advertisement')
    op.drop_index(op.f('ix_advertisement_owner_id'), table_name='advertisement')
    op.drop_index(op.f('ix_advertisement_fi_property_id'), table_name='advertisement')
    op.drop_index(op.f('ix_advertisement_fi_agent_id'), table_name='advertisement')
    op.drop_index(op.f('ix_advertisement_event_id'), table_name='advertisement')
    op.drop_table('advertisement')
    # ### end Alembic commands ###
