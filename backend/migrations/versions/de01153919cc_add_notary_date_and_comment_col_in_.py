"""add notary_date and comment col in Document table

Revision ID: de01153919cc
Revises: a38252de2052
Create Date: 2024-08-21 13:57:12.416611

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "de01153919cc"
down_revision = "a38252de2052"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("document", sa.Column("notary_date", sa.Date(), nullable=True))
    op.add_column("document", sa.Column("comment", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("document", "comment")
    op.drop_column("document", "notary_date")
    # ### end Alembic commands ###
