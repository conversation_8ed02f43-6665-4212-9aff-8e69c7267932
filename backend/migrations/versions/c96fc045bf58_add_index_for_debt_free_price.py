"""Add index for debt free price

Revision ID: c96fc045bf58
Revises: 70acfc661028
Create Date: 2024-12-05 09:33:56.817578

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c96fc045bf58'
down_revision = '70acfc661028'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_fi_commercial_property_overview_debt_free_price'), 'fi_commercial_property_overview', ['debt_free_price'], unique=False)
    op.create_index(op.f('ix_fi_other_share_overview_debt_free_price'), 'fi_other_share_overview', ['debt_free_price'], unique=False)
    op.create_index(op.f('ix_fi_realty_selling_price'), 'fi_realty', ['selling_price'], unique=False)
    op.create_index(op.f('ix_fi_residential_share_overview_debt_free_price'), 'fi_residential_share_overview', ['debt_free_price'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_fi_residential_share_overview_debt_free_price'), table_name='fi_residential_share_overview')
    op.drop_index(op.f('ix_fi_realty_selling_price'), table_name='fi_realty')
    op.drop_index(op.f('ix_fi_other_share_overview_debt_free_price'), table_name='fi_other_share_overview')
    op.drop_index(op.f('ix_fi_commercial_property_overview_debt_free_price'), table_name='fi_commercial_property_overview')
    # ### end Alembic commands ###
