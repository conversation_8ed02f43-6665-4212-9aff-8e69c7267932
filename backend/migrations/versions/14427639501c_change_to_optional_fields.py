"""Change to optional fields

Revision ID: 14427639501c
Revises: a84857509156
Create Date: 2024-09-09 22:44:02.467908

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '14427639501c'
down_revision = 'a84857509156'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('dias_shared_trade', 'attachments',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'bill_of_sale',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'realtor_bank_account',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'sellers',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'buyers',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'initiator_contact_info',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'require_initiator_confirmation',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    op.alter_column('dias_shared_trade', 'initiator_person_id',
               existing_type=mysql.VARCHAR(length=100),
               nullable=True)
    op.alter_column('dias_shared_trade', 'apartment',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=True)
    op.alter_column('dias_shared_trade', 'trade_initiated_timestamp',
               existing_type=mysql.VARCHAR(length=100),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('dias_shared_trade', 'trade_initiated_timestamp',
               existing_type=mysql.VARCHAR(length=100),
               nullable=False)
    op.alter_column('dias_shared_trade', 'apartment',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'initiator_person_id',
               existing_type=mysql.VARCHAR(length=100),
               nullable=False)
    op.alter_column('dias_shared_trade', 'require_initiator_confirmation',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.alter_column('dias_shared_trade', 'initiator_contact_info',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'buyers',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'sellers',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'realtor_bank_account',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'bill_of_sale',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    op.alter_column('dias_shared_trade', 'attachments',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               nullable=False)
    # ### end Alembic commands ###
