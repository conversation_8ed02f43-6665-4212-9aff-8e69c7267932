"""Give the option to have multiple realtors per sales agreement

Revision ID: cf56cbf0dfdb
Revises: b44d6f76ca5e
Create Date: 2025-07-31 17:28:13.429287

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'cf56cbf0dfdb'
down_revision = 'b44d6f76ca5e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column('fi_sales_agreement_realtor', sa.Column('_lft', sa.Integer(), nullable=False))
    op.add_column('fi_sales_agreement_realtor', sa.Column('_rgt', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_sales_agreement_realtor', '_rgt')
    op.drop_column('fi_sales_agreement_realtor', '_lft')
        # ### end Alembic commands ###
