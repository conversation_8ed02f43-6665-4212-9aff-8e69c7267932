"""Images

Revision ID: 6146331d8580
Revises: 8787bc353a37
Create Date: 2023-08-18 10:11:03.899497

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "6146331d8580"
down_revision = "acb8621c8ddd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "image",
        sa.Column("url", sa.String(length=255), nullable=False),
        sa.Column("order", sa.Integer(), nullable=True),
        sa.Column("is_hidden", sa.<PERSON>(), nullable=False),
        sa.<PERSON>umn("property_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.<PERSON>nteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"], ["property.id"], name=op.f("fk_image_property_id_property")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_image")),
    )

    op.alter_column(
        "contact_realtor",
        "contact",
        existing_type=sa.BigInteger(),
        nullable=False,
        new_column_name="contact_id",
    )
    op.alter_column(
        "contact_realtor",
        "realtor",
        existing_type=sa.BigInteger(),
        nullable=False,
        new_column_name="realtor_id",
    )

    op.alter_column(
        "contact_realtor", "created_at", existing_type=mysql.DATETIME(), nullable=True
    )
    op.alter_column(
        "contact_realtor", "updated_at", existing_type=mysql.DATETIME(), nullable=True
    )

    op.alter_column(
        "contact", "created_at", existing_type=mysql.DATETIME(), nullable=True
    )
    op.alter_column(
        "contact", "updated_at", existing_type=mysql.DATETIME(), nullable=True
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contact_realtor",
        "contact_id",
        existing_type=sa.BigInteger(),
        nullable=False,
        new_column_name="contact",
    )
    op.alter_column(
        "contact_realtor",
        "realtor_id",
        existing_type=sa.BigInteger(),
        nullable=False,
        new_column_name="realtor",
    )

    op.alter_column(
        "contact_realtor", "updated_at", existing_type=mysql.DATETIME(), nullable=False
    )
    op.alter_column(
        "contact_realtor", "created_at", existing_type=mysql.DATETIME(), nullable=False
    )

    op.alter_column(
        "contact", "updated_at", existing_type=mysql.DATETIME(), nullable=False
    )
    op.alter_column(
        "contact", "created_at", existing_type=mysql.DATETIME(), nullable=False
    )

    op.drop_table("image")
    # ### end Alembic commands ###
