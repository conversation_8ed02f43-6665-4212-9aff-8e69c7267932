"""add new owner type

Revision ID: b44d6f76ca5e
Revises: 95f0c3c7a5ec
Create Date: 2025-07-24 14:20:16.359382

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "b44d6f76ca5e"
down_revision = "95f0c3c7a5ec"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.alter_column(
        "document_library_item_owner",
        "owner_type",
        existing_type=mysql.ENUM(
            "FI_PROPERTY", "FI_SALES_AGREEMENT", "CONTACT", "FI_PURCHASE_OFFER"
        ),
        nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
