"""add new contact fields

Revision ID: 23030a2e330b
Revises: c1698dff89d2
Create Date: 2024-02-21 07:45:38.209081

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "23030a2e330b"
down_revision = "c1698dff89d2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact", sa.<PERSON>umn("company", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("website", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("city", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("post_code", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("country", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("preferred_language", sa.Text(), nullable=True))
    op.add_column("contact", sa.Column("notes", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("contact", "notes")
    op.drop_column("contact", "preferred_language")
    op.drop_column("contact", "country")
    op.drop_column("contact", "post_code")
    op.drop_column("contact", "city")
    op.drop_column("contact", "website")
    op.drop_column("contact", "company")
    # ### end Alembic commands ###
