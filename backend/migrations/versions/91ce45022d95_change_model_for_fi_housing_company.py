"""change model for fi housing company

Revision ID: 91ce45022d95
Revises: 8d24048bd342
Create Date: 2024-10-25 09:33:12.544646

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '91ce45022d95'
down_revision = '8d24048bd342'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_housing_company', sa.Column('manager_name', sa.String(length=100), nullable=True))
    op.alter_column('fi_housing_company', 'manager_contact_details',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               type_=sa.Text(length=20),
               existing_nullable=True)
    op.alter_column('fi_housing_company', 'digital_share_group_identifiers',
               existing_type=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.add_column('fi_plot_overview', sa.Column('redeemable_description', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_plot_overview', 'redeemable_description')
    op.alter_column('fi_housing_company', 'digital_share_group_identifiers',
               existing_type=sa.String(length=50),
               type_=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               existing_nullable=True)
    op.alter_column('fi_housing_company', 'manager_contact_details',
               existing_type=sa.Text(length=20),
               type_=mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'),
               existing_nullable=True)
    op.drop_column('fi_housing_company', 'manager_name')
    # ### end Alembic commands ###
