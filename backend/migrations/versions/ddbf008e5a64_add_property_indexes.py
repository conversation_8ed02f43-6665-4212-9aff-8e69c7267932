"""Add property indexes

Revision ID: ddbf008e5a64
Revises: b90b9227977e
Create Date: 2025-07-08 23:55:42.018042

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ddbf008e5a64"
down_revision = "b90b9227977e"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        op.f("ix_property_created_at"), "property", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_property_data_source"), "property", ["data_source"], unique=False
    )
    op.create_index(
        op.f("ix_property_is_exclusive"), "property", ["is_exclusive"], unique=False
    )
    op.create_index(
        op.f("ix_property_is_strandified"), "property", ["is_strandified"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_property_is_strandified"), table_name="property")
    op.drop_index(op.f("ix_property_is_exclusive"), table_name="property")
    op.drop_index(op.f("ix_property_data_source"), table_name="property")
    op.drop_index(op.f("ix_property_created_at"), table_name="property")
