"""add custom_reference_property to offer

Revision ID: 107c1a65ee7c
Revises: 59e9658ad330
Create Date: 2024-09-23 08:32:41.983118

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "107c1a65ee7c"
down_revision = "59e9658ad330"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "offer",
        sa.Column("custom_reference_property", sa.String(length=50), nullable=True),
    )
    op.alter_column(
        "offer",
        "property_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=True,
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("offer", "custom_reference_property")
    # ### end Alembic commands ###
