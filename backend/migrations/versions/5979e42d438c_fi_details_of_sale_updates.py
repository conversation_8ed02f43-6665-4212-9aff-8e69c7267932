"""fi-details-of-sale-updates

Revision ID: 5979e42d438c
Revises: 0bcd12159684
Create Date: 2025-08-12 08:23:51.664958

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "5979e42d438c"
down_revision = "0bcd12159684"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_details_of_sale",
        sa.Column("property_published_at", sa.Date(), nullable=True),
    )
    op.add_column("fi_details_of_sale", sa.Column("debt", sa.Float(), nullable=True))
    op.add_column(
        "fi_details_of_sale",
        sa.Column("strand_commission_percent", sa.Float(), nullable=True),
    )
    op.drop_column("fi_details_of_sale", "assignment_started_at")
    op.add_column(
        "fi_recipient_details_of_sale",
        sa.Column("commission_vat_percent", sa.Float(), nullable=True),
    )
    op.add_column(
        "fi_details_of_sale", sa.Column("sale_final_date", sa.Date(), nullable=True)
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_recipient_details_of_sale", "commission_vat_percent")
    op.add_column(
        "fi_details_of_sale",
        sa.Column("assignment_started_at", sa.DATE(), nullable=True),
    )
    op.drop_column("fi_details_of_sale", "strand_commission_percent")
    op.drop_column("fi_details_of_sale", "debt")
    op.drop_column("fi_details_of_sale", "property_published_at")
    op.drop_column("fi_details_of_sale", "debt")
    op.drop_column("fi_details_of_sale", "strand_commission_percent")
    op.drop_column("fi_details_of_sale", "sale_final_date")
    # ### end Alembic commands ###
