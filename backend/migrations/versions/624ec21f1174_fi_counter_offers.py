"""fi counter offers

Revision ID: 624ec21f1174
Revises: 086deab883a0
Create Date: 2025-05-13 13:35:57.940725

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '624ec21f1174'
down_revision = '086deab883a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fi_counter_offer',
    sa.Column('created_by', sa.BigInteger(), nullable=True),
    sa.Column('unencumbered_price', sa.Float(), nullable=False),
    sa.Column('valid_until', sa.DateTime(), nullable=False),
    sa.Column('additional_details', sa.String(length=500), nullable=True),
    sa.Column('purchase_offer_id', sa.BigInteger(), nullable=False),
    sa.Column('previous_counter_offer_id', sa.<PERSON>nteger(), nullable=True),
    sa.Column('id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], name=op.f('fk_fi_counter_offer_created_by_user')),
    sa.ForeignKeyConstraint(['previous_counter_offer_id'], ['fi_counter_offer.id'], name=op.f('fk_fi_counter_offer_previous_counter_offer_id_fi_counter_offer'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['purchase_offer_id'], ['fi_purchase_offer.id'], name=op.f('fk_fi_counter_offer_purchase_offer_id_fi_purchase_offer'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_counter_offer'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('fi_counter_offer')
    # ### end Alembic commands ###
