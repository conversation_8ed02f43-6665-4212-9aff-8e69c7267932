"""Add more two field roaiib and liability insurance

Revision ID: d7322c3ebe61
Revises: 70b472729be8
Create Date: 2025-02-10 12:43:45.305082

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "d7322c3ebe61"
down_revision = "70b472729be8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("roaiib", sa.String(length=50), nullable=True))
    op.add_column(
        "user", sa.Column("liability_insurance", sa.String(length=50), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "liability_insurance")
    op.drop_column("user", "roaiib")
    # ### end Alembic commands ###
