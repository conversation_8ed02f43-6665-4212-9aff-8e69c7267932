"""Convert type of lease holder

Revision ID: a91a00821bb3
Revises: c96fc045bf58
Create Date: 2024-12-06 14:07:50.012428

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "a91a00821bb3"
down_revision = "c96fc045bf58"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_plot_overview",
        "lease_holder",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=100),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_plot_overview",
        "lease_holder",
        existing_type=sa.String(length=100),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
