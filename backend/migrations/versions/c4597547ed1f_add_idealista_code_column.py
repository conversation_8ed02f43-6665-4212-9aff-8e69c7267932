"""add idealista_code column

Revision ID: c4597547ed1f
Revises: 6dfd21b7cf33
Create Date: 2024-02-27 13:05:33.743499

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c4597547ed1f"
down_revision = "6dfd21b7cf33"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property", sa.Column("idealista_code", sa.String(length=20), nullable=True)
    )
    op.create_unique_constraint(
        op.f("uq_property_idealista_code"), "property", ["idealista_code"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("uq_property_idealista_code"), "property", type_="unique")
    op.drop_column("property", "idealista_code")
    # ### end Alembic commands ###
