"""file model

Revision ID: 686c3a8ad371
Revises: 288fa75f4a0e
Create Date: 2024-02-07 08:28:04.767076

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "686c3a8ad371"
down_revision = "288fa75f4a0e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "file",
        sa.Column("key", sa.String(length=255), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"], ["property.id"], name=op.f("fk_file_property_id_property")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_file")),
        sa.UniqueConstraint("key", name=op.f("uq_file_key")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("file")
    # ### end Alembic commands ###
