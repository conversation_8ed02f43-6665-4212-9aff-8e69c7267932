"""Change type of ppc date to nullable

Revision ID: 95f0c3c7a5ec
Revises: 932802998bda
Create Date: 2025-07-22 17:45:21.512136

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "95f0c3c7a5ec"
down_revision = "932802998bda"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "offer",
        "private_purchase_contract_due_date",
        existing_type=mysql.DATETIME(),
        nullable=True,
        existing_server_default=sa.text("'2024-07-05 07:15:00'"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "offer",
        "private_purchase_contract_due_date",
        existing_type=mysql.DATETIME(),
        nullable=False,
        existing_server_default=sa.text("'2024-07-05 07:15:00'"),
    )
    # ### end Alembic commands ###
