"""change model for fi plot overview

Revision ID: 218af0fd6fa0
Revises: 91ce45022d95
Create Date: 2024-10-25 10:21:41.063846

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '218af0fd6fa0'
down_revision = '91ce45022d95'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_plot_overview', 'lease_holder',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Text(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_plot_overview', 'lease_holder',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=True)
    # ### end Alembic commands ###
