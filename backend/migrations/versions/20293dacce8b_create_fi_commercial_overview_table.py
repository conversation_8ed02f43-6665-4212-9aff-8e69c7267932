"""Create fi_commercial_overview table

Revision ID: 20293dacce8b
Revises: aaf5e8a201c2
Create Date: 2024-07-23 06:42:27.793328

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "20293dacce8b"
down_revision = "aaf5e8a201c2"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_commercial_overview",
        sa.Column(
            "commercial_property_type_code", sa.String(length=100), nullable=False
        ),
        sa.Column("typical_uses", sa.JSON(), nullable=False),
        sa.Column("administration", sa.JSON(), nullable=True),
        sa.Column("floor_area", sa.JSON(), nullable=True),
        sa.Column("total_area", sa.JSON(), nullable=True),
        sa.Column("debt_free_price", sa.Integer(), nullable=True),
        sa.Column("debt_share_amount", sa.Integer(), nullable=True),
        sa.Column("starting_debt_free_price", sa.Integer(), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("share_certificate", sa.JSON(), nullable=True),
        sa.Column("redemption", sa.JSON(), nullable=True),
        sa.Column("sustainable_development", sa.JSON(), nullable=True),
        sa.Column("additional_services", sa.JSON(), nullable=False),
        sa.Column("spaces", sa.JSON(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_commercial_overview")),
    )


def downgrade():
    op.drop_table("fi_commercial_overview")
