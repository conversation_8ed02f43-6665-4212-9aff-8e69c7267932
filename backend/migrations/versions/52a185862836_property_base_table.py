"""property_base table

Revision ID: 52a185862836
Revises: 9eefa0e80b37
Create Date: 2024-06-11 15:37:40.025460

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "52a185862836"
down_revision = "9eefa0e80b37"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    op.create_table(
        "property",
        sa.Column("market", sa.String(length=50), nullable=False),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("reference", sa.String(length=20), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name=op.f("fk_property_base_organization_id_organization"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property")),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
    )
    conn.execute(
        sa.text(
            f"""
            INSERT INTO property (market, organization_id, reference, id, created_at, updated_at)
            select 'spain', organization_id, reference, id, created_at, updated_at
            from property_spain;
        """
        )
    )

    op.create_index(
        op.f("ix_property_organization_id"),
        "property",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_reference"), "property", ["reference"], unique=True
    )
    op.drop_constraint(
        "fk_lead_property_reference_property", "lead", type_="foreignkey"
    )
    op.create_foreign_key(
        op.f("fk_lead_property_reference_property"),
        "lead",
        "property",
        ["property_reference"],
        ["reference"],
    )
    op.drop_constraint(
        "fk_property_organization_id_organization", "property_spain", type_="foreignkey"
    )
    op.drop_index("ix_property_spain_organization_id", table_name="property_spain")
    op.drop_index("ix_property_spain_reference", table_name="property_spain")
    op.create_foreign_key(
        op.f("fk_property_spain_id_property"),
        "property_spain",
        "property",
        ["id"],
        ["id"],
    )
    op.drop_column("property_spain", "reference")
    op.drop_column("property_spain", "created_at")
    op.drop_column("property_spain", "organization_id")
    op.drop_column("property_spain", "updated_at")
    # ### end Alembic commands ###


def downgrade():
    conn = op.get_bind()
    op.add_column(
        "property_spain", sa.Column("updated_at", mysql.DATETIME(), nullable=True)
    )
    op.add_column(
        "property_spain",
        sa.Column(
            "organization_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "property_spain", sa.Column("created_at", mysql.DATETIME(), nullable=True)
    )
    op.add_column(
        "property_spain",
        sa.Column("reference", mysql.VARCHAR(length=20), nullable=False),
    )

    conn.execute(
        sa.text(
            f"""
                update property_spain
                set reference = (select reference from property where property.id = property_spain.id)
                , organization_id = (select organization_id from property where property.id = property_spain.id)
                , created_at = (select created_at from property where property.id = property_spain.id)
                , updated_at = (select updated_at from property where property.id = property_spain.id);
            """
        )
    )

    op.drop_constraint(
        op.f("fk_property_spain_id_property"), "property_spain", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_property_organization_id_organization",
        "property_spain",
        "organization",
        ["organization_id"],
        ["id"],
    )
    op.create_index(
        "ix_property_spain_reference", "property_spain", ["reference"], unique=True
    )
    op.create_index(
        "ix_property_spain_organization_id",
        "property_spain",
        ["organization_id"],
        unique=False,
    )
    op.drop_constraint(
        op.f("fk_lead_property_reference_property"), "lead", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_lead_property_reference_property",
        "lead",
        "property_spain",
        ["property_reference"],
        ["reference"],
    )
    # op.drop_index(op.f("ix_property_reference"), table_name="property")
    # op.drop_index(op.f("ix_property_organization_id"), table_name="property")
    op.drop_table("property")
    # ### end Alembic commands ###
