"""Update finnish property type

Revision ID: e2da6c93b5a6
Revises: 429939084c22
Create Date: 2024-07-12 06:32:41.099625

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e2da6c93b5a6"
down_revision = "429939084c22"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("fi_property_type", "category")
    op.drop_column("fi_property_type", "name")
    op.add_column(
        "fi_property_type", sa.Column("category", sa.String(length=50), nullable=False)
    )
    op.add_column(
        "fi_property_type",
        sa.Column("subcategory", sa.String(length=50), nullable=False),
    )
    op.add_column(
        "fi_property_type", sa.Column("type", sa.String(length=50), nullable=False)
    )
    op.add_column(
        "fi_property_type", sa.Column("subtype", sa.String(length=50), nullable=False)
    )


def downgrade():
    op.add_column(
        "fi_property_type",
        sa.Column("category", mysql.VARCHAR(length=50), nullable=False),
    )
    op.add_column(
        "fi_property_type", sa.Column("name", mysql.VARCHAR(length=50), nullable=False)
    )
    op.drop_column("fi_property_type", "subtype")
    op.drop_column("fi_property_type", "type")
    op.drop_column("fi_property_type", "subcategory")
