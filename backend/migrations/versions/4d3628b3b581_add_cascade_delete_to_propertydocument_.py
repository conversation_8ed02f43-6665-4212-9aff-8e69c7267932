"""Add cascade delete to PropertyDocument-SalesAgreement relationship

Revision ID: 4d3628b3b581
Revises: 7607e0bb65cd
Create Date: 2024-06-23 23:27:30.361212

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "4d3628b3b581"
down_revision = "7607e0bb65cd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "fk_sales_agreement_property_document_id_property_document",
        "sales_agreement",
        type_="foreignkey",
    )
    op.create_foreign_key(
        op.f("fk_sales_agreement_property_document_id_property_document"),
        "sales_agreement",
        "property_document",
        ["property_document_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_sales_agreement_property_document_id_property_document"),
        "sales_agreement",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "fk_sales_agreement_property_document_id_property_document",
        "sales_agreement",
        "property_document",
        ["property_document_id"],
        ["id"],
    )
    # ### end Alembic commands ###
