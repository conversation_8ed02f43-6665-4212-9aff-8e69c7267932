"""property to property_spain

Revision ID: 9eefa0e80b37
Revises: c23fdb71ca65
Create Date: 2024-06-05 21:49:57.954292

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9eefa0e80b37"
down_revision = "53e06dd32941"
branch_labels = None
depends_on = None


def upgrade():
    op.rename_table("property", "property_spain")
    op.create_index(
        op.f("ix_property_spain_bathrooms"),
        "property_spain",
        ["bathrooms"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_spain_bedrooms"), "property_spain", ["bedrooms"], unique=False
    )
    op.create_index(
        op.f("ix_property_spain_organization_id"),
        "property_spain",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_spain_price_rent_long_term"),
        "property_spain",
        ["price_rent_long_term"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_spain_price_rent_short_term"),
        "property_spain",
        ["price_rent_short_term"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_spain_price_sale"),
        "property_spain",
        ["price_sale"],
        unique=False,
    )
    op.create_index(
        op.f("ix_property_spain_reference"),
        "property_spain",
        ["reference"],
        unique=True,
    )
    op.create_index(
        op.f("ix_property_spain_status"), "property_spain", ["status"], unique=False
    )
    op.create_unique_constraint(
        op.f("uq_property_spain_idealista_code"), "property_spain", ["idealista_code"]
    )
    op.drop_index("ix_property_bathrooms", table_name="property_spain")
    op.drop_index("ix_property_bedrooms", table_name="property_spain")
    op.drop_index("ix_property_organization_id", table_name="property_spain")
    op.drop_index("ix_property_price_rent_long_term", table_name="property_spain")
    op.drop_index("ix_property_price_rent_short_term", table_name="property_spain")
    op.drop_index("ix_property_price_sale", table_name="property_spain")
    op.drop_index("ix_property_reference", table_name="property_spain")
    op.drop_index("ix_property_status", table_name="property_spain")
    op.drop_index("uq_property_idealista_code", table_name="property_spain")


def downgrade():
    op.create_index(
        "uq_property_idealista_code", "property_spain", ["idealista_code"], unique=True
    )
    op.create_index("ix_property_status", "property_spain", ["status"], unique=False)
    op.create_index(
        "ix_property_reference", "property_spain", ["reference"], unique=True
    )
    op.create_index(
        "ix_property_price_sale", "property_spain", ["price_sale"], unique=False
    )
    op.create_index(
        "ix_property_price_rent_short_term",
        "property_spain",
        ["price_rent_short_term"],
        unique=False,
    )
    op.create_index(
        "ix_property_price_rent_long_term",
        "property_spain",
        ["price_rent_long_term"],
        unique=False,
    )
    op.create_index(
        "ix_property_organization_id",
        "property_spain",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        "ix_property_bedrooms", "property_spain", ["bedrooms"], unique=False
    )
    op.create_index(
        "ix_property_bathrooms", "property_spain", ["bathrooms"], unique=False
    )
    op.drop_constraint(
        op.f("uq_property_spain_idealista_code"), "property_spain", type_="unique"
    )
    op.drop_index(op.f("ix_property_spain_status"), table_name="property_spain")
    op.drop_index(op.f("ix_property_spain_reference"), table_name="property_spain")
    op.drop_index(op.f("ix_property_spain_price_sale"), table_name="property_spain")
    op.drop_index(
        op.f("ix_property_spain_price_rent_short_term"), table_name="property_spain"
    )
    op.drop_index(
        op.f("ix_property_spain_price_rent_long_term"), table_name="property_spain"
    )
    op.drop_index(
        op.f("ix_property_spain_organization_id"), table_name="property_spain"
    )
    op.drop_index(op.f("ix_property_spain_bedrooms"), table_name="property_spain")
    op.drop_index(op.f("ix_property_spain_bathrooms"), table_name="property_spain")

    op.rename_table("property_spain", "property")
