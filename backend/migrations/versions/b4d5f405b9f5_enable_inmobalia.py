"""enable inmobalia

Revision ID: b4d5f405b9f5
Revises: e2da6c93b5a6
Create Date: 2024-07-16 09:39:57.841583

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b4d5f405b9f5"
down_revision = "e2da6c93b5a6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
    UPDATE property_spain 
    SET portals = JSON_SET(portals, '$.is_inmobalia_enabled', TRUE)
    WHERE 
        status = 'Published' AND 
        data_source != 'resales';
    """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
    UPDATE property_spain 
    SET portals = JSON_REMOVE(portals, '$.is_inmobalia_enabled');
    """
    )
    # ### end Alembic commands ###

