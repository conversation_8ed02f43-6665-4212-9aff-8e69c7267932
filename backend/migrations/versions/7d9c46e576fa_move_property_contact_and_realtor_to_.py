"""move property contact and realtor to base

Revision ID: 7d9c46e576fa
Revises: 0a3e462b9d70
Create Date: 2024-06-12 16:52:04.493112

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7d9c46e576fa'
down_revision = '0a3e462b9d70'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_property_contact_property_id_property', 'property_contact', type_='foreignkey')
    op.create_foreign_key(op.f('fk_property_contact_property_id_property'), 'property_contact', 'property', ['property_id'], ['id'])
    op.drop_constraint('fk_property_realtor2_property_id_property', 'property_realtor', type_='foreignkey')
    op.create_foreign_key(op.f('fk_property_realtor_property_id_property'), 'property_realtor', 'property', ['property_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_property_realtor_property_id_property'), 'property_realtor', type_='foreignkey')
    op.create_foreign_key('fk_property_realtor2_property_id_property', 'property_realtor', 'property_spain', ['property_id'], ['id'])
    op.drop_constraint(op.f('fk_property_contact_property_id_property'), 'property_contact', type_='foreignkey')
    op.create_foreign_key('fk_property_contact_property_id_property', 'property_contact', 'property_spain', ['property_id'], ['id'])
    # ### end Alembic commands ###
