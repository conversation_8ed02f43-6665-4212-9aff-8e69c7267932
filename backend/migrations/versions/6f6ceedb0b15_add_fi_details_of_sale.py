"""add fi details of sale

Revision ID: 6f6ceedb0b15
Revises: e1ef7f939fc4
Create Date: 2025-05-14 13:41:55.176023

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "6f6ceedb0b15"
down_revision = "e1ef7f939fc4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fi_details_of_sale",
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("DRAFT", "VALIDATED", "LOCKED", name="fidetailsofsalestatus"),
            nullable=False,
        ),
        sa.Column(
            "ownership_type",
            sa.Enum("PERCENTAGE", "FRACTION", name="fidetailsofsaleownershiptype"),
            nullable=True,
        ),
        sa.Column(
            "transaction_method",
            sa.Enum("TRADITIONAL", "DIAS", name="fidetailsofsaletransactionmethod"),
            nullable=True,
        ),
        sa.Column("assignment_started_at", sa.Date(), nullable=True),
        sa.Column("estimated_transaction_date", sa.Date(), nullable=True),
        sa.Column("sale_duration_days", sa.Integer(), nullable=True),
        sa.Column("offer_count", sa.Integer(), nullable=True),
        sa.Column("highest_rejected_offer", sa.Float(), nullable=True),
        sa.Column("sale_price", sa.Float(), nullable=True),
        sa.Column("debt_free_price", sa.Float(), nullable=True),
        sa.Column("mortgage_bank", sa.String(length=50), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("commission_amount_total", sa.Float(), nullable=True),
        sa.Column("commission_percent", sa.Float(), nullable=True),
        sa.Column("commission_vat_percent", sa.Float(), nullable=True),
        sa.Column("commission_vat_included", sa.Boolean(), nullable=True),
        sa.Column("commission_amount_without_vat", sa.Float(), nullable=True),
        sa.Column("commission_vat_amount", sa.Float(), nullable=True),
        sa.Column("commission_amount_with_vat", sa.Float(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["fi_property.id"],
            name=op.f("fk_fi_details_of_sale_property_id_fi_property"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_details_of_sale")),
    )
    op.create_table(
        "fi_buyer_details_of_sale",
        sa.Column("fi_details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("buyer_id", sa.BigInteger(), nullable=True),
        sa.Column("ownership_share_percent", sa.Float(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["buyer_id"],
            ["contact.id"],
            name=op.f("fk_fi_buyer_details_of_sale_buyer_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_details_of_sale_id"],
            ["fi_details_of_sale.id"],
            name=op.f(
                "fk_fi_buyer_details_of_sale_fi_details_of_sale_id_fi_details_of_sale"
            ),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_buyer_details_of_sale")),
        sa.UniqueConstraint(
            "fi_details_of_sale_id",
            "buyer_id",
            name=op.f("uq_fi_buyer_details_of_sale_fi_details_of_sale_id"),
        ),
    )
    op.create_index(
        op.f("ix_fi_buyer_details_of_sale_buyer_id"),
        "fi_buyer_details_of_sale",
        ["buyer_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_buyer_details_of_sale_fi_details_of_sale_id"),
        "fi_buyer_details_of_sale",
        ["fi_details_of_sale_id"],
        unique=False,
    )
    op.create_table(
        "fi_recipient_details_of_sale",
        sa.Column("user_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column(
            "role",
            sa.Enum("SELLING", "BROKERAGE_FIRM", name="fidetailsofsalerecipientrole"),
            nullable=False,
        ),
        sa.Column("commission_percent", sa.Float(), nullable=True),
        sa.Column("commission_amount", sa.Float(), nullable=True),
        sa.Column(
            "type",
            sa.Enum("CONTACT", "USER", name="fidetailsofsalerecipienttype"),
            nullable=False,
        ),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_details_of_sale_id"],
            ["fi_details_of_sale.id"],
            name=op.f(
                "fk_fi_recipient_details_of_sale_fi_details_of_sale_id_fi_details_of_sale"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
            name=op.f("fk_fi_recipient_details_of_sale_user_id_user"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_recipient_details_of_sale")),
    )
    op.create_index(
        op.f("ix_fi_recipient_details_of_sale_fi_details_of_sale_id"),
        "fi_recipient_details_of_sale",
        ["fi_details_of_sale_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_recipient_details_of_sale_user_id"),
        "fi_recipient_details_of_sale",
        ["user_id"],
        unique=False,
    )
    op.create_table(
        "fi_seller_details_of_sale",
        sa.Column("fi_details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("ownership_share_percent", sa.Float(), nullable=True),
        sa.Column("seller_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_details_of_sale_id"],
            ["fi_details_of_sale.id"],
            name=op.f(
                "fk_fi_seller_details_of_sale_fi_details_of_sale_id_fi_details_of_sale"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["seller_id"],
            ["contact.id"],
            name=op.f("fk_fi_seller_details_of_sale_seller_id_contact"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_seller_details_of_sale")),
        sa.UniqueConstraint(
            "fi_details_of_sale_id",
            "seller_id",
            name=op.f("uq_fi_seller_details_of_sale_fi_details_of_sale_id"),
        ),
    )
    op.create_index(
        op.f("ix_fi_seller_details_of_sale_fi_details_of_sale_id"),
        "fi_seller_details_of_sale",
        ["fi_details_of_sale_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_seller_details_of_sale_seller_id"),
        "fi_seller_details_of_sale",
        ["seller_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_fi_seller_details_of_sale_seller_id"),
        table_name="fi_seller_details_of_sale",
    )
    op.drop_index(
        op.f("ix_fi_seller_details_of_sale_fi_details_of_sale_id"),
        table_name="fi_seller_details_of_sale",
    )
    op.drop_table("fi_seller_details_of_sale")
    op.drop_index(
        op.f("ix_fi_recipient_details_of_sale_user_id"),
        table_name="fi_recipient_details_of_sale",
    )
    op.drop_index(
        op.f("ix_fi_recipient_details_of_sale_fi_details_of_sale_id"),
        table_name="fi_recipient_details_of_sale",
    )
    op.drop_table("fi_recipient_details_of_sale")
    op.drop_index(
        op.f("ix_fi_buyer_details_of_sale_fi_details_of_sale_id"),
        table_name="fi_buyer_details_of_sale",
    )
    op.drop_index(
        op.f("ix_fi_buyer_details_of_sale_buyer_id"),
        table_name="fi_buyer_details_of_sale",
    )
    op.drop_table("fi_buyer_details_of_sale")
    op.drop_table("fi_details_of_sale")
    # ### end Alembic commands ###
