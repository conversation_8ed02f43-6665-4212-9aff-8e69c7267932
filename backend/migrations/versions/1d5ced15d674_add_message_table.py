"""Add message table

Revision ID: 1d5ced15d674
Revises: c5ad803da40b
Create Date: 2025-03-10 05:11:00.457161

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import String

# revision identifiers, used by Alembic.
revision = "1d5ced15d674"
down_revision = "c5ad803da40b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "message",
        sa.Column("event_uuid", String(36), nullable=False, unique=True),
        sa.Column("realtor_id", sa.BigInteger(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("message_data", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("status", sa.Text(), nullable=False),
        sa.ForeignKeyConstraint(
            ["contact_id"], ["contact.id"], name=op.f("fk_message_contact_id_contact")
        ),
        sa.ForeignKeyConstraint(
            ["event_uuid"],
            ["external_event.event_uuid"],
            name=op.f("fk_message_event_uuid_external_event"),
        ),
        sa.ForeignKeyConstraint(
            ["realtor_id"], ["user.id"], name=op.f("fk_message_realtor_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_message")),
        sa.UniqueConstraint(
            "realtor_id", "contact_id", "id", name="uq_realtor_contact_message"
        ),
    )
    op.create_index(
        "ix_contact_realtor_contact", "message", ["contact_id"], unique=False
    )
    op.create_index(
        "ix_contact_realtor_realtor", "message", ["realtor_id"], unique=False
    )


def downgrade():
    op.drop_constraint("fk_message_realtor_id_user", "message", type_="foreignkey")
    op.drop_constraint(
        "fk_message_event_uuid_external_event", "message", type_="foreignkey"
    )
    op.drop_constraint("fk_message_contact_id_contact", "message", type_="foreignkey")
    op.drop_index("ix_contact_realtor_realtor", table_name="message")
    op.drop_index("ix_contact_realtor_contact", table_name="message")
    op.drop_table("message")
