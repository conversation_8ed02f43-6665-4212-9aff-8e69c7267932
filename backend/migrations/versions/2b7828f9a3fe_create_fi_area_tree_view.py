"""Create fi_area_ancestor_view

Revision ID: 2b7828f9a3fe
Revises: 8981f5991581
Create Date: 2024-09-25 04:00:57.196656

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "2b7828f9a3fe"
down_revision = "8981f5991581"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        CREATE VIEW fi_area_ancestor_view AS
        WITH RECURSIVE area_cte AS (
            -- Base case: select all areas
            SELECT id, parent_id, id AS ancestor_id, 0 AS level
            FROM fi_area
            UNION ALL
            -- Recursive case: join each area with its parent
            SELECT fa.id, fa.parent_id, area_cte.ancestor_id, area_cte.level + 1
            FROM fi_area fa
            JOIN area_cte ON fa.parent_id = area_cte.id
        )
        -- Final selection: id and topmost ancestor_id
        SELECT id, ancestor_id
        FROM area_cte
        WHERE ancestor_id IS NOT NULL;
        """
    )


def downgrade():
    op.execute("DROP VIEW IF EXISTS fi_area_ancestor_view")
