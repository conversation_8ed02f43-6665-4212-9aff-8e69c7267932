"""add Dias Sharedtrade Attachment relationship

Revision ID: c69166df5164
Revises: f069c36c17d7
Create Date: 2024-09-26 16:21:50.412366

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "c69166df5164"
down_revision = "f069c36c17d7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "dias_shared_trade_attachment",
        sa.<PERSON>umn("shared_trade_id", sa.BigInteger(), nullable=False),
        sa.Column("attachment_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["attachment_id"],
            ["dias_attachment.id"],
            name=op.f("fk_dias_shared_trade_attachment_attachment_id_dias_attachment"),
        ),
        sa.ForeignKeyConstraint(
            ["shared_trade_id"],
            ["dias_shared_trade.id"],
            name=op.f(
                "fk_dias_shared_trade_attachment_shared_trade_id_dias_shared_trade"
            ),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dias_shared_trade_attachment")),
        sa.UniqueConstraint(
            "shared_trade_id", "attachment_id", name="uq_dias_shared_trade_attachment"
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("dias_shared_trade_attachment")
    # ### end Alembic commands ###
