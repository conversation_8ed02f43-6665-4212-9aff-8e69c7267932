"""fix-typo

Revision ID: 1f6053ea3762
Revises: 742a681e503a
Create Date: 2025-05-27 08:48:58.108196

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1f6053ea3762'
down_revision = '742a681e503a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_purchase_offer', sa.Column('standard_compensation', sa.Float(), nullable=True))
    op.execute("UPDATE fi_purchase_offer SET standard_compensation = standard_compenstation")
    op.drop_column('fi_purchase_offer', 'standard_compenstation')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_purchase_offer', sa.Column('standard_compenstation', mysql.FLOAT(), nullable=True))
    op.execute("UPDATE fi_purchase_offer SET standard_compenstation = standard_compensation")
    op.drop_column('fi_purchase_offer', 'standard_compensation')
    # ### end Alembic commands ###
