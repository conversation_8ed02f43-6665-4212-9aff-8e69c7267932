"""Add document library item

Revision ID: bf8df696dd77
Revises: 65fdea36febf
Create Date: 2025-05-23 09:39:16.353810

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bf8df696dd77'
down_revision = '65fdea36febf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_library_item',
    sa.<PERSON>umn('owner_type', sa.Enum('FI_PROPERTY', name='owner_type_enum'), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('s3_key', sa.String(length=500), nullable=True),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('mime_type', sa.String(length=100), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('document_type', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('upload_state', sa.Enum('PENDING_UPLOAD', 'READY', name='upload_state_enum'), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['user.id'], name=op.f('fk_document_library_item_created_by_id_user'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_document_library_item')),
    sa.UniqueConstraint('s3_key', name=op.f('uq_document_library_item_s3_key'))
    )
    op.create_index('ix_document_library_item_owner', 'document_library_item', ['owner_type', 'owner_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_document_library_item_owner', table_name='document_library_item')
    op.drop_table('document_library_item')
    # ### end Alembic commands ###
