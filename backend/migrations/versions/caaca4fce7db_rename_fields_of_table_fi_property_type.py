"""Rename fields of table fi_property_type

Revision ID: caaca4fce7db
Revises: de01153919cc
Create Date: 2024-08-22 01:15:15.666769

"""

from alembic import op
import sqlalchemy as sa
from strandproperties.utils.alembic import alembic_mysql_util as op_util

# revision identifiers, used by Alembic.
revision = "caaca4fce7db"
down_revision = "de01153919cc"
branch_labels = None
depends_on = None


def upgrade():
    # Rename columns in the fi_property_type table
    op.alter_column(
        "fi_property_type",
        "category",
        new_column_name="listing_type",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "subcategory",
        new_column_name="ownership_type",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "type",
        new_column_name="property_type_group",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "subtype",
        new_column_name="property_type",
        type_=sa.String(length=50),
        existing_nullable=False,
    )

    # Rename indexes using op_util
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_category",
        "ix_fi_property_type_listing_type",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_subcategory",
        "ix_fi_property_type_ownership_type",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_type",
        "ix_fi_property_type_property_type_group",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_subtype",
        "ix_fi_property_type_property_type",
    )


def downgrade():
    # Revert column names back to their original names in the fi_property_type table
    op.alter_column(
        "fi_property_type",
        "listing_type",
        new_column_name="category",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "ownership_type",
        new_column_name="subcategory",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "property_type_group",
        new_column_name="type",
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_property_type",
        "property_type",
        new_column_name="subtype",
        type_=sa.String(length=50),
        existing_nullable=False,
    )

    # Revert index names using op_util
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_listing_type",
        "ix_fi_property_type_category",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_ownership_type",
        "ix_fi_property_type_subcategory",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_property_type_group",
        "ix_fi_property_type_type",
    )
    op_util.rename_index(
        "fi_property_type",
        "ix_fi_property_type_property_type",
        "ix_fi_property_type_subtype",
    )
