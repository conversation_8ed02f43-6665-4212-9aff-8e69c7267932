"""Add expenses transaction to user key


Revision ID: 1746bf380eab
Revises: 7fdcbc4bdb17
Create Date: 2025-03-28 14:39:32.462664

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1746bf380eab'
down_revision = '7fdcbc4bdb17'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_transactions_user_id'), 'transactions', ['user_id'], unique=False)
    op.create_foreign_key(op.f('fk_transactions_user_id_user'), 'transactions', 'user', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_transactions_user_id_user'), 'transactions', type_='foreignkey')
    op.drop_index(op.f('ix_transactions_user_id'), table_name='transactions')
    # ### end Alembic commands ###
