"""add_cognito_sub_to_user

Revision ID: 70acfc661028
Revises: 569c40b90eb4
Create Date: 2024-12-02 10:22:28.230195

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "70acfc661028"
down_revision = "569c40b90eb4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "user", sa.Column("cognito_sub", sa.String(length=255), nullable=True)
    )
    op.create_index(op.f("ix_user_cognito_sub"), "user", ["cognito_sub"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_user_cognito_sub"), table_name="user")
    op.drop_column("user", "cognito_sub")
