"""Add Company model

Revision ID: 1fbbd7ce00d5
Revises: 6c62c8b055a4
Create Date: 2024-09-18 16:12:42.813688

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "1fbbd7ce00d5"
down_revision = "6c62c8b055a4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "company",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("address", sa.String(length=200), nullable=True),
        sa.Column("website", sa.String(length=200), nullable=True),
        sa.Column("business_id", sa.String(length=100), nullable=True),
        sa.Column("_dias_api_key", sa.Text(), nullable=True),
        sa.Column("id", sa.<PERSON>nteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_company")),
    )
    op.add_column("user", sa.Column("company_id", sa.BigInteger(), nullable=True))
    op.create_foreign_key(
        op.f("fk_user_company_id_company"), "user", "company", ["company_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("fk_user_company_id_company"), "user", type_="foreignkey")
    op.drop_column("user", "company_id")
    op.drop_table("company")
    # ### end Alembic commands ###
