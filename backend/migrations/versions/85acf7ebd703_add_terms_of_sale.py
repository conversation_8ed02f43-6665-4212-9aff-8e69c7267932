"""add terms_of_sale

Revision ID: 85acf7ebd703
Revises: f00c2d112c0c
Create Date: 2025-04-02 13:35:32.299984

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "85acf7ebd703"
down_revision = "f00c2d112c0c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_purchase_offer", sa.Column("terms_of_sale", sa.JSON(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_purchase_offer", "terms_of_sale")
    # ### end Alembic commands ###
