"""Add ssn to a user

Revision ID: 932802998bda
Revises: 75d489e7fa7b
Create Date: 2025-07-16 13:35:39.981089

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '932802998bda'
down_revision = '75d489e7fa7b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('social_security_number', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'social_security_number')
    # ### end Alembic commands ###
