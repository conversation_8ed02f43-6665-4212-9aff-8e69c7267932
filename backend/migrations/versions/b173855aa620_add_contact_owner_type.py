"""add_contact_owner_type

Revision ID: b173855aa620
Revises: b90b9227977e
Create Date: 2025-07-09 13:31:21.759885

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b173855aa620"
down_revision = "b90b9227977e"
branch_labels = None
depends_on = None


def upgrade():
    # Add CONTACT to owner_type_enum
    op.execute(
        "ALTER TABLE document_library_item_owner MODIFY COLUMN owner_type ENUM('FI_PROPERTY', 'FI_SALES_AGREEMENT', 'CONTACT')"
    )


def downgrade():
    # Remove CONTACT from owner_type_enum (WARNING: This will fail if there are CONTACT records)
    op.execute(
        "ALTER TABLE document_library_item_owner MODIFY COLUMN owner_type ENUM('FI_PROPERTY', 'FI_SALES_AGREEMENT')"
    )
