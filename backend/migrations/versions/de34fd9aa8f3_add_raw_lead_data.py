"""add raw lead data

Revision ID: de34fd9aa8f3
Revises: bedf29dc69c8
Create Date: 2024-08-07 12:03:57.916093

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "de34fd9aa8f3"
down_revision = "bedf29dc69c8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "raw_lead_data",
        sa.Column("source_lead_id", sa.String(length=100), nullable=False),
        sa.Column("full_name", sa.String(length=100), nullable=False),
        sa.Column("email", sa.String(length=100), nullable=False),
        sa.Column("source", sa.String(length=100), nullable=False),
        sa.Column("status", sa.Text(), nullable=True),
        sa.Column("contact_id", sa.<PERSON>nteger(), nullable=False),
        sa.Column("property_reference", sa.String(length=100), nullable=True),
        sa.Column("content", sa.Text(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_raw_lead_data_contact_id_contact"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_raw_lead_data")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("raw_lead_data")
    # ### end Alembic commands ###
