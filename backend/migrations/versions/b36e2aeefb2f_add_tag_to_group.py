"""add tag to group

Revision ID: b36e2aeefb2f
Revises: 14427639501c
Create Date: 2024-09-10 23:19:08.280498

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b36e2aeefb2f"
down_revision = "14427639501c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "group_tag",
        sa.Column("group_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("tag_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=True),
        sa.<PERSON>umn("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["group_id"], ["group.id"], name=op.f("fk_group_tag_group_id_group")
        ),
        sa.ForeignKeyConstraint(
            ["tag_id"], ["tag.id"], name=op.f("fk_group_tag_tag_id_tag")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_group_tag")),
        sa.UniqueConstraint("group_id", "tag_id", name="uq_group_tag"),
    )
    op.create_index("ix_group_tag_group", "group_tag", ["group_id"], unique=False)
    op.create_index("ix_group_tag_tag", "group_tag", ["tag_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_group_tag_tag", table_name="group_tag")
    op.drop_index("ix_group_tag_group", table_name="group_tag")
    op.drop_table("group_tag")
    # ### end Alembic commands ###
