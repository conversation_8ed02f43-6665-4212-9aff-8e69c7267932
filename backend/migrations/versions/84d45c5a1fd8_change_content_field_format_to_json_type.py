"""change content field format to json type

Revision ID: 84d45c5a1fd8
Revises: 8f0328506401
Create Date: 2024-08-11 22:28:59.450200

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '84d45c5a1fd8'
down_revision = '8f0328506401'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('raw_lead_data', sa.Column('phone', sa.String(length=100), nullable=True))
    op.alter_column('raw_lead_data', 'content',
               existing_type=mysql.TEXT(),
               type_=sa.JSON(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('raw_lead_data', 'content',
               existing_type=sa.JSON(),
               type_=mysql.TEXT(),
               existing_nullable=True)
    op.drop_column('raw_lead_data', 'phone')
    # ### end Alembic commands ###
