"""add deposit payee to offer model

Revision ID: 18705936dec1
Revises: b4d5f405b9f5
Create Date: 2024-07-16 23:07:58.268236

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '18705936dec1'
down_revision = 'b4d5f405b9f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('offer', sa.Column('deposit_payee', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('offer', 'deposit_payee')
    # ### end Alembic commands ###
