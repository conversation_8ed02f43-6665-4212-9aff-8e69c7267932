"""add street address and postal area

Revision ID: 7e3db1771e38
Revises: a0f573c0becd
Create Date: 2024-11-04 15:26:29.384986

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7e3db1771e38'
down_revision = 'a0f573c0becd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_housing_company', sa.Column('street_address', sa.String(length=200), nullable=True))
    op.add_column('fi_housing_company', sa.Column('postal_area', sa.String(length=100), nullable=True))
    op.add_column('fi_plot_overview', sa.Column('size_of_plot', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_plot_overview', 'size_of_plot')
    op.drop_column('fi_housing_company', 'postal_area')
    op.drop_column('fi_housing_company', 'street_address')
    # ### end Alembic commands ###
