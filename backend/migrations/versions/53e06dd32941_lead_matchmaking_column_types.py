"""lead matchmaking column types

Revision ID: 53e06dd32941
Revises: 939658382c76
Create Date: 2024-06-06 11:01:46.490348

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '53e06dd32941'
down_revision = '939658382c76'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('email_template', 'subject',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('email_template', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('email_template', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('lead', 'status',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('lead', 'relevance',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('lead', 'title',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('lead', 'type',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('lead', 'source',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('lead', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('lead', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.drop_column('lead', 'linked_match_making')
    op.alter_column('lead_contact', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('lead_contact', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('lead_user', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('lead_user', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making', 'title',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('match_making', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_contact', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_contact', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_email_template', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_email_template', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_property', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_property', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_user', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_user', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_version', 'version',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('match_making_version', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('match_making_version', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('match_making_version', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_version', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_version', 'version',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('match_making_user', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_user', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_property', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_property', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_email_template', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_email_template', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_contact', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making_contact', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('match_making', 'title',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('lead_user', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('lead_user', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('lead_contact', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('lead_contact', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.add_column('lead', sa.Column('linked_match_making', mysql.BIGINT(display_width=20), autoincrement=False, nullable=True))
    op.alter_column('lead', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('lead', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('lead', 'source',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('lead', 'type',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('lead', 'title',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('lead', 'relevance',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('lead', 'status',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('email_template', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('email_template', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('email_template', 'subject',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    # ### end Alembic commands ###
