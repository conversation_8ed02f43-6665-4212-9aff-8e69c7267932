"""add field created_by

Revision ID: 6fee8c9e4e59
Revises: 37d1cd852b68
Create Date: 2024-10-30 15:57:30.288416

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "6fee8c9e4e59"
down_revision = "37d1cd852b68"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_attachment", sa.Column("created_by_id", sa.BigInteger(), nullable=False)
    )
    op.create_foreign_key(
        op.f("fk_dias_attachment_created_by_id_user"),
        "dias_attachment",
        "user",
        ["created_by_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_dias_attachment_created_by_id_user"),
        "dias_attachment",
        type_="foreignkey",
    )
    op.drop_column("dias_attachment", "created_by_id")
    # ### end Alembic commands ###
