"""make start_date nullablke

Revision ID: d1f26aa2bbd0
Revises: aa57da7766e3
Create Date: 2025-05-21 15:10:49.761215

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd1f26aa2bbd0'
down_revision = 'aa57da7766e3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('advertisement', 'start_date',
               existing_type=mysql.DATETIME(),
               nullable=True)
   
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('advertisement', 'start_date',
               existing_type=mysql.DATETIME(),
               nullable=False)
    # ### end Alembic commands ###
