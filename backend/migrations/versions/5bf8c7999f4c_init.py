"""Init

Revision ID: 5bf8c7999f4c
Revises: 
Create Date: 2023-06-07 00:34:56.098533

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "5bf8c7999f4c"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "dw_agents",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("firstname", sa.String(length=200), nullable=True),
        sa.Column("lastname", sa.String(length=200), nullable=True),
        sa.Column("email", sa.String(length=100), nullable=True),
        sa.Column("phone", sa.String(length=100), nullable=True),
        sa.Column("image_url", sa.String(length=500), nullable=True),
        sa.Column("person_parameter", sa.String(length=100), nullable=True),
        sa.<PERSON>eyConstraint("id", name=op.f("pk_dw_agents")),
    )
    op.create_table(
        "dw_areas",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("id_area1", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("name_area1", sa.String(length=300), nullable=True),
        sa.Column("id_area2", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("name_area2", sa.String(length=300), nullable=True),
        sa.Column("id_area3", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("name_area3", sa.String(length=300), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas")),
    )
    op.create_table(
        "dw_areas1",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("active", mysql.INTEGER(display_width=6), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas1")),
    )
    op.create_table(
        "dw_areas2",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.Column("area1_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("active", mysql.INTEGER(display_width=6), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas2")),
    )
    op.create_table(
        "dw_areas3",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.Column("area1_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area2_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("active", mysql.INTEGER(display_width=6), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas3")),
    )
    op.create_table(
        "dw_areas4",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.Column("area1_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area2_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area3_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("active", mysql.INTEGER(display_width=6), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas4")),
    )
    op.create_table(
        "dw_areas5",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.Column("area1_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area2_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area3_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("area4_id", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("active", mysql.INTEGER(display_width=6), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_areas5")),
    )
    op.create_table(
        "dw_feature_groups",
        sa.Column("id", mysql.INTEGER(display_width=20), nullable=False),
        sa.Column("groupname", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_feature_groups")),
    )
    op.create_table(
        "dw_mapping_city",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("city", sa.String(length=30), nullable=True),
        sa.Column("subarea_id", mysql.INTEGER(display_width=10), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_city")),
    )
    op.create_index("idx_city", "dw_mapping_city", ["id", "city"], unique=True)
    op.create_table(
        "dw_mapping_condition",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("condition_name", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_condition")),
    )
    op.create_table(
        "dw_mapping_features",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("features_name", sa.String(length=100), nullable=True),
        sa.Column("group_id", mysql.INTEGER(display_width=10), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_features")),
    )
    op.create_table(
        "dw_mapping_orientation",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("orientation_name", sa.String(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_orientation")),
    )
    op.create_table(
        "dw_mapping_property_type2",
        sa.Column("id", mysql.INTEGER(display_width=10), nullable=False),
        sa.Column("property_type", sa.String(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_property_type2")),
    )
    op.create_table(
        "dw_mapping_settings",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("settings_name", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_settings")),
    )
    op.create_table(
        "dw_mapping_subarea",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("city_id", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("subarea", sa.String(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_subarea")),
    )
    op.create_table(
        "dw_mapping_views",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("views_name", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_mapping_views")),
    )
    op.create_table(
        "dw_objects",
        sa.Column("reference", sa.String(length=100), nullable=False),
        sa.Column("property_id", sa.String(length=50), nullable=True),
        sa.Column("agencyref", sa.String(length=100), nullable=True),
        sa.Column("title", mysql.LONGTEXT(), nullable=True),
        sa.Column("area_level1", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("area_level2", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("area_level3", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("area_level4", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("area_level5", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("province", sa.String(length=400), nullable=True),
        sa.Column("area", sa.String(length=400), nullable=True),
        sa.Column("subarea_id", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("city_id", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("city_name", sa.String(length=400), nullable=True),
        sa.Column("slug", sa.String(length=600), nullable=True),
        sa.Column("property_type_id", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("property_subtype", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("property_subtype_name", sa.String(length=400), nullable=True),
        sa.Column("commercial_type", mysql.INTEGER(display_width=4), nullable=True),
        sa.Column("plot_type", mysql.INTEGER(display_width=4), nullable=True),
        sa.Column("sale_status", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("bedrooms", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("bathrooms", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("onsuitebathrooms", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("toilets", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("pool", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("pooltypename", sa.String(length=100), nullable=True),
        sa.Column("gardentypename", sa.String(length=100), nullable=True),
        sa.Column("garagetypename", sa.String(length=100), nullable=True),
        sa.Column("parkingspaces", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("pax", sa.String(length=50), nullable=True),
        sa.Column("climate_control", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("price_eur_old", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("price_eur", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("squareprice", mysql.INTEGER(display_width=11), nullable=False),
        sa.Column("price_change_percent", sa.Float(), nullable=True),
        sa.Column("price_rent_long", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("communityfee", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("garbagetax", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("ibi", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("built_year", mysql.INTEGER(display_width=20), nullable=True),
        sa.Column("built_area", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("interior_area", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("plot_area", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("terrace_area", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("levels", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("floor", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("condition_ids", sa.String(length=200), nullable=True),
        sa.Column("features", sa.String(length=400), nullable=True),
        sa.Column("orientation", sa.String(length=200), nullable=True),
        sa.Column("settings", sa.String(length=400), nullable=True),
        sa.Column("views", sa.String(length=200), nullable=True),
        sa.Column("description", mysql.LONGTEXT(), nullable=True),
        sa.Column("short_description", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column("extra_description", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column("description_es", mysql.LONGTEXT(), nullable=True),
        sa.Column("short_description_es", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column("new_development", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("data_source", sa.String(length=40), nullable=True),
        sa.Column("listedby_name", sa.String(length=300), nullable=True),
        sa.Column("listedby_email", sa.String(length=100), nullable=True),
        sa.Column("listedby2_name", sa.String(length=300), nullable=True),
        sa.Column("listedby2_email", sa.String(length=100), nullable=True),
        sa.Column("priority", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("main_img", mysql.LONGTEXT(), nullable=True),
        sa.Column("main_img_resized", mysql.LONGTEXT(), nullable=True),
        sa.Column("development", sa.String(length=40), nullable=True),
        sa.Column("new_row", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("viewed", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column("saved", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column(
            "is_public",
            mysql.TINYINT(display_width=1),
            server_default=sa.text("1"),
            nullable=True,
        ),
        sa.Column(
            "is_hidden",
            mysql.TINYINT(display_width=4),
            server_default=sa.text("0"),
            nullable=False,
        ),
        sa.Column("latitude", sa.Float(), nullable=True),
        sa.Column("longitude", sa.Float(), nullable=True),
        sa.Column("etuovi_public", mysql.INTEGER(display_width=6), nullable=True),
        sa.Column("etuovi_title", sa.Text(), nullable=True),
        sa.Column("etuovi_description_fi", mysql.LONGTEXT(), nullable=True),
        sa.Column("etuovi_flatstructure", sa.Text(), nullable=True),
        sa.Column(
            "time_created",
            sa.TIMESTAMP(),
            server_default=sa.text("current_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "time_updated",
            sa.TIMESTAMP(),
            server_default=sa.text("current_timestamp() ON UPDATE current_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("reference", name=op.f("pk_dw_objects")),
    )
    op.create_table(
        "dw_objects_translations",
        sa.Column("reference", sa.String(length=20), nullable=False),
        sa.Column("lang_code", sa.String(length=2), nullable=False),
        sa.Column("description", mysql.MEDIUMTEXT(), nullable=False),
        sa.Column("short_description", mysql.MEDIUMTEXT(), nullable=False),
        sa.Column("extra_description", mysql.MEDIUMTEXT(), nullable=False),
        sa.Column("price_description", mysql.MEDIUMTEXT(), nullable=False),
        sa.Column("auto_translation", mysql.INTEGER(display_width=11), nullable=False),
        sa.Column("modified", sa.String(length=30), nullable=False),
        sa.PrimaryKeyConstraint(
            "reference", "lang_code", name=op.f("pk_dw_objects_translations")
        ),
    )
    op.create_table(
        "dw_pictures",
        sa.Column("order_imgs", mysql.INTEGER(display_width=10), nullable=True),
        sa.Column("reference", sa.String(length=30), nullable=True),
        sa.Column("img", sa.String(length=255), nullable=False),
        sa.PrimaryKeyConstraint("img", name=op.f("pk_dw_pictures")),
    )
    op.create_table(
        "dw_price_history",
        sa.Column("id", mysql.INTEGER(display_width=20), nullable=False),
        sa.Column("reference", sa.String(length=10), nullable=True),
        sa.Column("price_eur", mysql.INTEGER(display_width=11), nullable=True),
        sa.Column(
            "time_created",
            sa.TIMESTAMP(),
            server_default=sa.text("current_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_price_history")),
    )
    op.create_table(
        "dw_resales_locations",
        sa.Column("id", mysql.INTEGER(display_width=6), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dw_resales_locations")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("dw_resales_locations")
    op.drop_table("dw_price_history")
    op.drop_table("dw_pictures")
    op.drop_table("dw_objects_translations")
    op.drop_table("dw_objects")
    op.drop_table("dw_mapping_views")
    op.drop_table("dw_mapping_subarea")
    op.drop_table("dw_mapping_settings")
    op.drop_table("dw_mapping_property_type2")
    op.drop_table("dw_mapping_orientation")
    op.drop_table("dw_mapping_features")
    op.drop_table("dw_mapping_condition")
    op.drop_index("idx_city", table_name="dw_mapping_city")
    op.drop_table("dw_mapping_city")
    op.drop_table("dw_feature_groups")
    op.drop_table("dw_areas5")
    op.drop_table("dw_areas4")
    op.drop_table("dw_areas3")
    op.drop_table("dw_areas2")
    op.drop_table("dw_areas1")
    op.drop_table("dw_areas")
    op.drop_table("dw_agents")
    # ### end Alembic commands ###
