"""change nullable for email and phone

Revision ID: dbc963b68e95
Revises: c6bd2ce1fe77
Create Date: 2024-08-20 09:14:08.107106

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "dbc963b68e95"
down_revision = "c6bd2ce1fe77"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "raw_lead_data", "email", existing_type=mysql.VARCHAR(length=100), nullable=True
    )
    op.alter_column(
        "raw_lead_data", "phone", existing_type=mysql.VARCHAR(length=100), nullable=True
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "raw_lead_data",
        "phone",
        existing_type=mysql.VARCHAR(length=100),
        nullable=False,
    )
    op.alter_column(
        "raw_lead_data",
        "email",
        existing_type=mysql.VARCHAR(length=100),
        nullable=False,
    )
    # ### end Alembic commands ###
