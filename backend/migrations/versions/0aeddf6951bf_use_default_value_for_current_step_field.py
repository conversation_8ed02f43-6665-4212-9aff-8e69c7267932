"""use default value for current_step field

Revision ID: 0aeddf6951bf
Revises: 9875996594c9
Create Date: 2024-11-19 09:37:08.294426

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "0aeddf6951bf"
down_revision = "9875996594c9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "UPDATE dias_shared_trade SET current_step = 1 WHERE current_step IS NULL"
    )

    op.alter_column(
        "dias_shared_trade",
        "current_step",
        existing_type=mysql.INTEGER(display_width=11),
        nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "dias_shared_trade",
        "current_step",
        existing_type=mysql.INTEGER(display_width=11),
        nullable=True,
    )
    # ### end Alembic commands ###
