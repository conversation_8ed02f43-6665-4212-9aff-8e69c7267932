"""Added primary_text to Advertisement

Revision ID: ff6d6d83f450
Revises: 701abd18393e
Create Date: 2025-04-04 15:37:27.660334

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'ff6d6d83f450'
down_revision = '701abd18393e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column('advertisement', sa.Column('primary_text', sa.Text(), nullable=True))


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column('advertisement', 'primary_text')
    # ### end Alembic commands ###
