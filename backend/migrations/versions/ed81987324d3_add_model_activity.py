"""Add model activity

Revision ID: ed81987324d3
Revises: 5979e42d438c
Create Date: 2025-08-12 09:56:32.716854

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ed81987324d3"
down_revision = "5979e42d438c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "activity",
        sa.Column("description", sa.JSON(), nullable=False),
        sa.Column("object_type", sa.String(length=50), nullable=False),
        sa.Column("object_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("actor_id", sa.BigInteger(), nullable=True),
        sa.<PERSON>umn("id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["actor_id"], ["user.id"], name=op.f("fk_activity_actor_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_activity")),
    )
    op.create_index(
        "idx_object_type_object_id",
        "activity",
        ["object_type", "object_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activity_actor_id"), "activity", ["actor_id"], unique=False
    )
    op.create_index(
        op.f("ix_activity_object_type"), "activity", ["object_type"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_activity_object_type"), table_name="activity")
    op.drop_index(op.f("ix_activity_actor_id"), table_name="activity")
    op.drop_index("idx_object_type_object_id", table_name="activity")
    op.drop_table("activity")
    # ### end Alembic commands ###
