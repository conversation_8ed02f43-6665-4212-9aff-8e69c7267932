"""add is_new_contact field

Revision ID: f42e9abe2a37
Revises: e8f238868208
Create Date: 2024-08-27 16:13:18.609377

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "f42e9abe2a37"
down_revision = "e8f238868208"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "raw_lead_data", sa.Column("is_new_contact", sa.<PERSON>(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("raw_lead_data", "is_new_contact")
    # ### end Alembic commands ###
