"""Move data_source and is_strandified to property_base

Revision ID: d75395eadb0d
Revises: 1df4587ce254
Create Date: 2024-08-18 23:30:37.025963

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "d75395eadb0d"
down_revision = "1df4587ce254"
branch_labels = None
depends_on = None


def upgrade():
    # Add columns to the property table for data_source, is_exclusive, and is_strandified
    op.add_column(
        "property", sa.Column("data_source", sa.String(length=100), nullable=True)
    )
    op.add_column("property", sa.Column("is_exclusive", sa.<PERSON>an(), nullable=False))
    op.add_column("property", sa.Column("is_strandified", sa.Bo<PERSON>(), nullable=False))

    # Move data from property_spain to property
    op.execute(
        """
        UPDATE property p
        JOIN property_spain ps ON p.id = ps.id
        SET p.data_source = ps.data_source,
            p.is_exclusive = ps.is_exclusive,
            p.is_strandified = ps.is_strandified;
        """
    )

    # Drop the columns from the property_spain table
    op.drop_column("property_spain", "data_source")
    op.drop_column("property_spain", "is_exclusive")
    op.drop_column("property_spain", "is_strandified")


def downgrade():
    # Re-add columns to the property_spain table for data_source, is_exclusive, and is_strandified
    op.add_column(
        "property_spain",
        sa.Column("is_strandified", sa.Boolean(), nullable=False),
    )
    op.add_column(
        "property_spain",
        sa.Column("is_exclusive", sa.Boolean(), nullable=False),
    )
    op.add_column(
        "property_spain",
        sa.Column("data_source", sa.String(length=100), nullable=True),
    )

    # Move data from property to property_spain
    op.execute(
        """
        UPDATE property_spain ps
        JOIN property p ON ps.id = p.id
        SET ps.data_source = p.data_source,
            ps.is_exclusive = p.is_exclusive,
            ps.is_strandified = p.is_strandified;
        """
    )

    # Drop the columns from the property table
    op.drop_column("property", "is_strandified")
    op.drop_column("property", "is_exclusive")
    op.drop_column("property", "data_source")
