"""counter offer signing

Revision ID: 6eabcb53945e
Revises: 3d89f4b9d1df
Create Date: 2025-08-05 14:34:27.861646

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "6eabcb53945e"
down_revision = "3d89f4b9d1df"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fi_counter_offer_offeree",
        sa.<PERSON>umn("counter_offer_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("offeree_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["counter_offer_id"],
            ["fi_counter_offer.id"],
            name=op.f("fk_fi_counter_offer_offeree_counter_offer_id_fi_counter_offer"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["offeree_id"],
            ["contact.id"],
            name=op.f("fk_fi_counter_offer_offeree_offeree_id_contact"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_counter_offer_offeree")),
    )
    op.create_table(
        "fi_counter_offer_offeror",
        sa.Column("counter_offer_id", sa.BigInteger(), nullable=False),
        sa.Column("offeror_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["counter_offer_id"],
            ["fi_counter_offer.id"],
            name=op.f("fk_fi_counter_offer_offeror_counter_offer_id_fi_counter_offer"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["offeror_id"],
            ["contact.id"],
            name=op.f("fk_fi_counter_offer_offeror_offeror_id_contact"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_counter_offer_offeror")),
    )

    op.add_column(
        "fi_counter_offer", sa.Column("status", sa.String(length=50), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column("fi_counter_offer", "status")

    op.drop_table("fi_counter_offer_offeror")
    op.drop_table("fi_counter_offer_offeree")
    # ### end Alembic commands ###
