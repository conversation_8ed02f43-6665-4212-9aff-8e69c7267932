"""fi details of sale leads

Revision ID: c80c8629139c
Revises: 5979e42d438c
Create Date: 2025-08-12 10:40:53.706188

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "c80c8629139c"
down_revision = "5979e42d438c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fi_lead_details_of_sale",
        sa.<PERSON>umn("user_id", sa.BigInteger(), nullable=True),
        sa.Column("fi_details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("commission_percent", sa.Float(), nullable=True),
        sa.Column("commission_vat_percent", sa.Float(), nullable=True),
        sa.Column("commission_amount", sa.Float(), nullable=True),
        sa.Column("id", sa.<PERSON>nteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column(
            "lead_basis",
            sa.Enum(
                "TOTAL_COMMISSION",
                "REALTOR_COMMISSION",
                name="fidetailsofsaleleadbasis",
            ),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["fi_details_of_sale_id"],
            ["fi_details_of_sale.id"],
            name=op.f(
                "fk_fi_lead_details_of_sale_fi_details_of_sale_id_fi_details_of_sale"
            ),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
            name=op.f("fk_fi_lead_details_of_sale_user_id_user"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_lead_details_of_sale")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_fi_lead_details_of_sale_user_id"), table_name="fi_lead_details_of_sale"
    )
    op.drop_index(
        op.f("ix_fi_lead_details_of_sale_fi_details_of_sale_id"),
        table_name="fi_lead_details_of_sale",
    )
    op.drop_table("fi_lead_details_of_sale")
    # ### end Alembic commands ###
