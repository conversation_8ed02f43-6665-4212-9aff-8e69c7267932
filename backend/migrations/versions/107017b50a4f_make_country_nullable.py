"""make country nullable

Revision ID: 107017b50a4f
Revises: d1f26aa2bbd0
Create Date: 2025-05-21 15:14:31.678487

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '107017b50a4f'
down_revision = 'd1f26aa2bbd0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('advertisement', 'country',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
    op.alter_column('advertisement', 'municipality',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('advertisement', 'municipality',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
    op.alter_column('advertisement', 'country',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
    # ### end Alembic commands ###
