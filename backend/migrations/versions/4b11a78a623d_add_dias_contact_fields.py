"""add dias contact fields

Revision ID: 4b11a78a623d
Revises: 18705936dec1
Create Date: 2024-07-18 15:42:29.951926

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '4b11a78a623d'
down_revision = '18705936dec1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bank',
    sa.Column('name', sa.Text(), nullable=False),
    sa.Column('business_id', sa.Text(), nullable=False),
    sa.Column('group_name', sa.Text(), nullable=False),
    sa.Column('group_business_id', sa.Text(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_bank')),
    sa.UniqueConstraint('name', name=op.f('uq_bank_name'))
    )
    op.add_column('contact', sa.Column('social_security_number', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('type', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('business_id', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('vat_number', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('iban', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('bic', sa.Text(), nullable=True))
    op.add_column('contact', sa.Column('bank_id', sa.BigInteger(), nullable=True))
    op.create_index(op.f('ix_contact_bank_id'), 'contact', ['bank_id'], unique=False)
    op.create_foreign_key(op.f('fk_contact_bank_id_bank'), 'contact', 'bank', ['bank_id'], ['id'])
    op.alter_column('offer', 'deposit_payee',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=40),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('offer', 'deposit_payee',
               existing_type=sa.String(length=40),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.drop_constraint(op.f('fk_contact_bank_id_bank'), 'contact', type_='foreignkey')
    op.drop_index(op.f('ix_contact_bank_id'), table_name='contact')
    op.drop_column('contact', 'bank_id')
    op.drop_column('contact', 'bic')
    op.drop_column('contact', 'iban')
    op.drop_column('contact', 'vat_number')
    op.drop_column('contact', 'business_id')
    op.drop_column('contact', 'type')
    op.drop_column('contact', 'social_security_number')
    op.drop_table('bank')
    # ### end Alembic commands ###
