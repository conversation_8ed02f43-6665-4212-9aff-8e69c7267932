"""Add last sent time for match making

Revision ID: 240c117e6f9d
Revises: aa57da7766e3
Create Date: 2025-05-22 07:20:13.822386

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "240c117e6f9d"
down_revision = "aa57da7766e3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "match_making", sa.Column("last_sent_at", sa.DateTime(), nullable=True)
    )


def downgrade():
    op.drop_column("match_making", "last_sent_at")
