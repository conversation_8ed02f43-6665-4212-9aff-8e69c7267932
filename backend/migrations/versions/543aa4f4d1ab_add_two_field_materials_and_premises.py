"""Add two field materials and premises

Revision ID: 543aa4f4d1ab
Revises: 4cbf97888cb7
Create Date: 2025-08-20 17:29:54.573699

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "543aa4f4d1ab"
down_revision = "4cbf97888cb7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_residential_share_overview",
        sa.Column("more_information_about_the_materials", sa.JSON(), nullable=True),
    )
    op.add_column(
        "fi_residential_share_overview",
        sa.Column("more_information_about_the_premises", sa.JSON(), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column(
        "fi_residential_share_overview", "more_information_about_the_premises"
    )
    op.drop_column(
        "fi_residential_share_overview", "more_information_about_the_materials"
    )
    # ### end Alembic commands ###
