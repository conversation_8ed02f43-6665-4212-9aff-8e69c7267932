"""update_dos_invoice

Revision ID: 9ce2b943b859
Revises: 5b90b94e7713
Create Date: 2024-09-05 14:13:54.058091

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "9ce2b943b859"
down_revision = "5b90b94e7713"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "fk_details_of_sale_invoice_seller_id_seller_details_of_sale",
        "details_of_sale_invoice",
        type_="foreignkey",
    )
    op.drop_column("details_of_sale_invoice", "seller_id")
    op.add_column(
        "realtor_details_of_sale",
        sa.Column("details_of_sale_invoice_id", sa.BigInteger(), nullable=True),
    )
    op.create_foreign_key(
        op.f(
            "fk_realtor_details_of_sale_details_of_sale_invoice_id_details_of_sale_invoice"
        ),
        "realtor_details_of_sale",
        "details_of_sale_invoice",
        ["details_of_sale_invoice_id"],
        ["id"],
    )
    op.add_column(
        "seller_details_of_sale",
        sa.Column("details_of_sale_invoice_id", sa.BigInteger(), nullable=True),
    )
    op.create_foreign_key(
        op.f(
            "fk_seller_details_of_sale_details_of_sale_invoice_id_details_of_sale_invoice"
        ),
        "seller_details_of_sale",
        "details_of_sale_invoice",
        ["details_of_sale_invoice_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f(
            "fk_seller_details_of_sale_details_of_sale_invoice_id_details_of_sale_invoice"
        ),
        "seller_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("seller_details_of_sale", "details_of_sale_invoice_id")
    op.drop_constraint(
        op.f(
            "fk_realtor_details_of_sale_details_of_sale_invoice_id_details_of_sale_invoice"
        ),
        "realtor_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("realtor_details_of_sale", "details_of_sale_invoice_id")
    op.add_column(
        "details_of_sale_invoice",
        sa.Column(
            "seller_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_foreign_key(
        "fk_details_of_sale_invoice_seller_id_seller_details_of_sale",
        "details_of_sale_invoice",
        "seller_details_of_sale",
        ["seller_id"],
        ["id"],
    )
    # ### end Alembic commands ###
