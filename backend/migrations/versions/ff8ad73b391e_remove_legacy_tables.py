"""Remove legacy tables

Revision ID: ff8ad73b391e
Revises: 6146331d8580
Create Date: 2023-09-20 17:03:26.997977

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "ff8ad73b391e"
down_revision = "6146331d8580"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        op.f("fk_dw_areas5_area4_id_dw_areas4"), "dw_areas5", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas4_area3_id_dw_areas3"), "dw_areas4", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas3_area2_id_dw_areas2"), "dw_areas3", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas2_area1_id_dw_areas1"), "dw_areas2", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_property_info_reference_dw_objects"),
        "property_info",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_property_info_organization_id_organization"),
        "property_info",
        type_="foreignkey",
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("dw_mapping_views")
    op.drop_table("dw_mapping_settings")
    op.drop_table("dw_areas")
    op.drop_table("dw_areas1")
    op.drop_table("dw_areas2")
    op.drop_table("dw_areas3")
    op.drop_table("dw_areas4")
    op.drop_table("dw_areas5")
    op.drop_table("dw_agents")
    op.drop_table("dw_mapping_subarea")
    op.drop_table("dw_mapping_condition")
    op.drop_index("idx_city", table_name="dw_mapping_city")
    op.drop_table("dw_mapping_city")
    op.drop_table("dw_feature_groups")
    op.drop_table("dw_objects_translations")
    op.drop_table("property_info")
    op.drop_table("dw_resales_locations")
    op.drop_table("dw_mapping_orientation")
    op.drop_table("dw_pictures")
    op.drop_table("dw_mapping_features")
    op.drop_table("dw_objects")
    op.drop_table("dw_price_history")
    op.drop_table("dw_mapping_property_type2")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "dw_areas",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "id_area1",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("name_area1", mysql.VARCHAR(length=300), nullable=True),
        sa.Column(
            "id_area2",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("name_area2", mysql.VARCHAR(length=300), nullable=True),
        sa.Column(
            "id_area3",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("name_area3", mysql.VARCHAR(length=300), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_areas5",
        sa.Column(
            "id", mysql.INTEGER(display_width=6), autoincrement=False, nullable=False
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column(
            "area1_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area2_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area3_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area4_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "active", mysql.INTEGER(display_width=6), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["area4_id"], ["dw_areas4.id"], name="fk_dw_areas5_area4_id_dw_areas4"
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_agents",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("firstname", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("lastname", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("email", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("phone", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("image_url", mysql.VARCHAR(length=500), nullable=True),
        sa.Column("person_parameter", mysql.VARCHAR(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_property_type2",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=10, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("property_type", mysql.VARCHAR(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_price_history",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=20, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("reference", mysql.VARCHAR(length=10), nullable=True),
        sa.Column(
            "price_eur",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "time_created",
            mysql.TIMESTAMP(),
            server_default=sa.text("current_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_objects",
        sa.Column("reference", mysql.VARCHAR(length=100), nullable=False),
        sa.Column("property_id", mysql.VARCHAR(length=50), nullable=True),
        sa.Column("agencyref", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("title", mysql.LONGTEXT(), nullable=True),
        sa.Column(
            "area_level1",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area_level2",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area_level3",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area_level4",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area_level5",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("province", mysql.VARCHAR(length=400), nullable=True),
        sa.Column("area", mysql.VARCHAR(length=400), nullable=True),
        sa.Column(
            "subarea_id",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "city_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("city_name", mysql.VARCHAR(length=400), nullable=True),
        sa.Column("slug", mysql.VARCHAR(length=600), nullable=True),
        sa.Column(
            "property_type_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "property_subtype",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("property_subtype_name", mysql.VARCHAR(length=400), nullable=True),
        sa.Column(
            "commercial_type",
            mysql.INTEGER(display_width=4),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "plot_type",
            mysql.INTEGER(display_width=4),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "sale_status",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "bedrooms",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "bathrooms",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "onsuitebathrooms",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "toilets",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "pool", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True
        ),
        sa.Column("pooltypename", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("gardentypename", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("garagetypename", mysql.VARCHAR(length=100), nullable=True),
        sa.Column(
            "parkingspaces",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("pax", mysql.VARCHAR(length=50), nullable=True),
        sa.Column(
            "climate_control",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "price_eur_old",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "price_eur",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "squareprice",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("price_change_percent", mysql.FLOAT(), nullable=True),
        sa.Column(
            "price_rent_long",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "communityfee",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "garbagetax",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "ibi", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True
        ),
        sa.Column(
            "built_year",
            mysql.INTEGER(display_width=20),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "built_area",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "interior_area",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "plot_area",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "terrace_area",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "levels",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "floor", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True
        ),
        sa.Column("condition_ids", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("features", mysql.VARCHAR(length=400), nullable=True),
        sa.Column("orientation", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("settings", mysql.VARCHAR(length=400), nullable=True),
        sa.Column("views", mysql.VARCHAR(length=200), nullable=True),
        sa.Column("description", mysql.LONGTEXT(), nullable=True),
        sa.Column("short_description", mysql.LONGTEXT(), nullable=True),
        sa.Column("extra_description", mysql.LONGTEXT(), nullable=True),
        sa.Column("description_es", mysql.LONGTEXT(), nullable=True),
        sa.Column("short_description_es", mysql.LONGTEXT(), nullable=True),
        sa.Column(
            "new_development",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("data_source", mysql.VARCHAR(length=40), nullable=True),
        sa.Column("listedby_name", mysql.VARCHAR(length=300), nullable=True),
        sa.Column("listedby_email", mysql.VARCHAR(length=100), nullable=True),
        sa.Column("listedby2_name", mysql.VARCHAR(length=300), nullable=True),
        sa.Column("listedby2_email", mysql.VARCHAR(length=100), nullable=True),
        sa.Column(
            "priority",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("main_img", mysql.LONGTEXT(), nullable=True),
        sa.Column("main_img_resized", mysql.LONGTEXT(), nullable=True),
        sa.Column("development", mysql.VARCHAR(length=40), nullable=True),
        sa.Column(
            "new_row",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "viewed",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "saved", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True
        ),
        sa.Column(
            "is_public",
            mysql.TINYINT(display_width=1),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "is_hidden",
            mysql.TINYINT(display_width=4),
            server_default=sa.text("0"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("latitude", mysql.FLOAT(), nullable=True),
        sa.Column("longitude", mysql.FLOAT(), nullable=True),
        sa.Column(
            "etuovi_public",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("etuovi_title", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column("etuovi_description_fi", mysql.LONGTEXT(), nullable=True),
        sa.Column("etuovi_flatstructure", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column(
            "time_created",
            mysql.TIMESTAMP(),
            server_default=sa.text("current_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "time_updated",
            mysql.TIMESTAMP(),
            server_default=sa.text("current_timestamp() ON UPDATE current_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("reference"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_features",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("features_name", mysql.VARCHAR(length=100), nullable=True),
        sa.Column(
            "group_id",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_pictures",
        sa.Column(
            "order_imgs",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("reference", mysql.VARCHAR(length=30), nullable=True),
        sa.Column("img", mysql.VARCHAR(length=255), nullable=False),
        sa.Column(
            "id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False
        ),
        sa.Column(
            "is_hidden",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("created_at", mysql.DATETIME(), nullable=True),
        sa.Column("updated_at", mysql.DATETIME(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_orientation",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("orientation_name", mysql.VARCHAR(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_resales_locations",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "property_info",
        sa.Column("reference", mysql.VARCHAR(length=100), nullable=False),
        sa.Column(
            "organization_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("status", mysql.TEXT(), nullable=False),
        sa.Column("commission", mysql.FLOAT(), nullable=True),
        sa.Column(
            "private_info",
            mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
            nullable=False,
        ),
        sa.Column(
            "is_exclusive",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "is_leadingre_enabled",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "is_idealista_enabled",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "id", mysql.BIGINT(display_width=20), autoincrement=True, nullable=False
        ),
        sa.Column("created_at", mysql.DATETIME(), nullable=True),
        sa.Column("updated_at", mysql.DATETIME(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name="fk_property_info_organization_id_organization",
        ),
        sa.ForeignKeyConstraint(
            ["reference"],
            ["dw_objects.reference"],
            name="fk_property_info_reference_dw_objects",
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_index(
        "ix_property_info_reference", "property_info", ["reference"], unique=False
    )
    op.create_index(
        "ix_property_info_organization_id",
        "property_info",
        ["organization_id"],
        unique=False,
    )
    op.create_table(
        "dw_objects_translations",
        sa.Column("reference", mysql.VARCHAR(length=20), nullable=False),
        sa.Column("lang_code", mysql.VARCHAR(length=2), nullable=False),
        sa.Column("description", mysql.LONGTEXT(), nullable=False),
        sa.Column("short_description", mysql.LONGTEXT(), nullable=False),
        sa.Column("extra_description", mysql.LONGTEXT(), nullable=False),
        sa.Column("price_description", mysql.LONGTEXT(), nullable=False),
        sa.Column(
            "auto_translation",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("modified", mysql.VARCHAR(length=30), nullable=False),
        sa.PrimaryKeyConstraint("reference", "lang_code"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_feature_groups",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=20, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("groupname", mysql.VARCHAR(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_city",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("city", mysql.VARCHAR(length=30), nullable=True),
        sa.Column(
            "subarea_id",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
        mariadb_row_format="COMPACT",
    )
    op.create_index("idx_city", "dw_mapping_city", ["id", "city"], unique=False)
    op.create_table(
        "dw_areas4",
        sa.Column(
            "id", mysql.INTEGER(display_width=6), autoincrement=False, nullable=False
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column(
            "area1_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area2_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area3_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "active", mysql.INTEGER(display_width=6), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["area3_id"], ["dw_areas3.id"], name="fk_dw_areas4_area3_id_dw_areas3"
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_condition",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("condition_name", mysql.VARCHAR(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_subarea",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "city_id",
            mysql.INTEGER(display_width=10),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("subarea", mysql.VARCHAR(length=30), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_areas1",
        sa.Column(
            "id", mysql.INTEGER(display_width=6), autoincrement=False, nullable=False
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column(
            "active", mysql.INTEGER(display_width=6), autoincrement=False, nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_areas2",
        sa.Column(
            "id", mysql.INTEGER(display_width=6), autoincrement=False, nullable=False
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column(
            "area1_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "active", mysql.INTEGER(display_width=6), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["area1_id"], ["dw_areas1.id"], name="fk_dw_areas2_area1_id_dw_areas1"
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_areas3",
        sa.Column(
            "id", mysql.INTEGER(display_width=6), autoincrement=False, nullable=False
        ),
        sa.Column("name", mysql.VARCHAR(length=200), nullable=True),
        sa.Column(
            "area1_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "area2_id",
            mysql.INTEGER(display_width=6),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "active", mysql.INTEGER(display_width=6), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["area2_id"], ["dw_areas2.id"], name="fk_dw_areas3_area2_id_dw_areas2"
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_settings",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("settings_name", mysql.VARCHAR(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_table(
        "dw_mapping_views",
        sa.Column(
            "id",
            mysql.INTEGER(display_width=6, unsigned=True),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("views_name", mysql.VARCHAR(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    # ### end Alembic commands ###
