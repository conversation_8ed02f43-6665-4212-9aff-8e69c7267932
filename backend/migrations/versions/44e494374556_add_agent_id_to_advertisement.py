"""add_agent_id_to_advertisement

Revision ID: 44e494374556
Revises: b0b2fff610d1
Create Date: 2025-08-19 10:35:12.221998

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '44e494374556'
down_revision = 'b0b2fff610d1'
branch_labels = None
depends_on = None


def upgrade():
    bind = op.get_bind()
    inspector = sa.inspect(bind)
    columns = [col['name'] for col in inspector.get_columns('advertisement')]
    
    if 'agent_id' not in columns:
        op.add_column('advertisement', sa.Column('agent_id', sa.BigInteger(), nullable=True))
        op.create_index(op.f('ix_advertisement_agent_id'), 'advertisement', ['agent_id'], unique=False)
        op.create_foreign_key(op.f('fk_advertisement_agent_id_user'), 'advertisement', 'user', ['agent_id'], ['id'])
    
    if 'fi_agent_id' in columns:
        op.drop_constraint(op.f('fk_advertisement_fi_agent_id_fi_agent'), 'advertisement', type_='foreignkey')
        op.drop_index(op.f('ix_advertisement_fi_agent_id'), table_name='advertisement')
        op.drop_column('advertisement', 'fi_agent_id')


def downgrade():
    
    bind = op.get_bind()
    inspector = sa.inspect(bind)
    columns = [col['name'] for col in inspector.get_columns('advertisement')]
    
    
    if 'fi_agent_id' not in columns:
        op.add_column('advertisement', sa.Column('fi_agent_id', sa.BigInteger(), nullable=True))
        op.create_index(op.f('ix_advertisement_fi_agent_id'), 'advertisement', ['fi_agent_id'], unique=False)
        op.create_foreign_key(op.f('fk_advertisement_fi_agent_id_fi_agent'), 'advertisement', 'fi_agent', ['fi_agent_id'], ['id'])
    
    if 'agent_id' in columns:
        op.drop_constraint(op.f('fk_advertisement_agent_id_user'), 'advertisement', type_='foreignkey')
        op.drop_index(op.f('ix_advertisement_agent_id'), table_name='advertisement')
        op.drop_column('advertisement', 'agent_id')
