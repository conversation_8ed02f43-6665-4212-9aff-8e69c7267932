"""Add organization to advertisement

Revision ID: b5d523ed1caf
Revises: 014a1bbcbbf2
Create Date: 2025-03-24 22:11:13.264929

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b5d523ed1caf'
down_revision = '014a1bbcbbf2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement', sa.Column('organization_id', sa.BigInteger(), nullable=True))
    op.create_index(op.f('ix_advertisement_organization_id'), 'advertisement', ['organization_id'], unique=False)
    op.create_foreign_key(op.f('fk_advertisement_organization_id_organization'), 'advertisement', 'organization', ['organization_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_advertisement_organization_id_organization'), 'advertisement', type_='foreignkey')
    op.drop_index(op.f('ix_advertisement_organization_id'), table_name='advertisement')
    op.drop_column('advertisement', 'organization_id')
    # ### end Alembic commands ###
