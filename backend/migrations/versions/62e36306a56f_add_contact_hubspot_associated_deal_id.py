"""add contact hubspot associated deal id

Revision ID: 62e36306a56f
Revises: c4597547ed1f
Create Date: 2024-03-20 10:08:14.128138

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "62e36306a56f"
down_revision = "c4597547ed1f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact", sa.Column("hs_object_id", sa.Text(), nullable=True))
    op.add_column("lead", sa.Column("hs_object_id", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("contact", "hs_object_id")
    op.drop_column("lead", "hs_object_id")
    # ### end Alembic commands ###
