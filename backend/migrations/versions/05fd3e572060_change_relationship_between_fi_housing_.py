"""change relationship between fi housing company with fi plot overview

Revision ID: 05fd3e572060
Revises: 6a6737152189
Create Date: 2024-11-07 10:01:41.544030

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '05fd3e572060'
down_revision = '6a6737152189'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_fi_housing_company_fi_plot_overview_id_fi_plot_overview', 'fi_housing_company', type_='foreignkey')
    op.drop_index('ix_fi_housing_company_fi_plot_overview_id', table_name='fi_housing_company')
    op.drop_column('fi_housing_company', 'fi_plot_overview_id')
    op.add_column('fi_plot_overview', sa.Column('fi_housing_company_id', sa.BigInteger(), nullable=True))
    op.create_index(op.f('ix_fi_plot_overview_fi_housing_company_id'), 'fi_plot_overview', ['fi_housing_company_id'], unique=False)
    op.create_foreign_key(op.f('fk_fi_plot_overview_fi_housing_company_id_fi_housing_company'), 'fi_plot_overview', 'fi_housing_company', ['fi_housing_company_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_fi_plot_overview_fi_housing_company_id_fi_housing_company'), 'fi_plot_overview', type_='foreignkey')
    op.drop_index(op.f('ix_fi_plot_overview_fi_housing_company_id'), table_name='fi_plot_overview')
    op.drop_column('fi_plot_overview', 'fi_housing_company_id')
    op.add_column('fi_housing_company', sa.Column('fi_plot_overview_id', mysql.BIGINT(display_width=20), autoincrement=False, nullable=True))
    op.create_foreign_key('fk_fi_housing_company_fi_plot_overview_id_fi_plot_overview', 'fi_housing_company', 'fi_plot_overview', ['fi_plot_overview_id'], ['id'])
    op.create_index('ix_fi_housing_company_fi_plot_overview_id', 'fi_housing_company', ['fi_plot_overview_id'], unique=False)
    # ### end Alembic commands ###
