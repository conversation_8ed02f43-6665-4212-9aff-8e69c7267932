"""Add document type

Revision ID: 400d052c02f4
Revises: 45388655e585
Create Date: 2025-06-05 14:33:11.647231

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '400d052c02f4'
down_revision = '45388655e585'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE document_library_item SET document_type = 'FI_OTHER'")
    op.alter_column('document_library_item', 'document_type',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Enum('FI_ASBESTOS_SURVEY', 'FI_SHARE_CERTIFICATE', 'FI_ENERGY_CERTIFICATE', 'FI_COMPREHENSIVE_BROCHURE', 'FI_BRIEF_BROCHURE', 'FI_WINDOW_CARD', 'FI_PROPERTY_MANAGERS_CERTIFICATE', 'FI_PLANNING_DOCUMENTS', 'FI_PURCHASE_AGREEMENT', 'FI_PROPERTY_REGISTER_EXTRACT', 'FI_PROPERTY_REGISTER_MAP', 'FI_PROPERTY_TAX_STATEMENT', 'FI_MOISTURE_MEASUREMENT', 'FI_MAINTENANCE_PLAN', 'FI_MAINTENANCE_NEED_ASSESSMENT', 'FI_CONDITION_INSPECTION_REPORT', 'FI_USAGE_RIGHTS_EXTRACT', 'FI_CERTIFICATE_OF_TITLE', 'FI_OTHER', 'FI_OWNER_APARTMENT_PRINTOUT', 'FI_SHARE_REGISTER_PRINT', 'FI_LONG_TERM_MAINTENANCE_PLAN', 'FI_SPOUSES_CONSENT', 'FI_FLOOR_PLAN', 'FI_BUILDING_PERMIT', 'FI_BUILDING_DRAWINGS', 'FI_ENCUMBRANCE_CERTIFICATE', 'FI_LISTING_FORM', 'FI_FINANCIAL_STATEMENT', 'FI_BROKERAGE_AGREEMENT', 'FI_LEASE_RIGHTS_CERTIFICATE', 'FI_LEASE_AGREEMENT', 'FI_BROKERAGE_OFFER', 'FI_ARTICLES_OF_ASSOCIATION', 'FI_GENERAL_MEETING_MINUTES', 'FI_HOUSING_COMPANY_CONDITION_REPORTS', name='document_type_enum'),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('document_library_item', 'document_type',
               existing_type=sa.Enum('FI_ASBESTOS_SURVEY', 'FI_SHARE_CERTIFICATE', 'FI_ENERGY_CERTIFICATE', 'FI_COMPREHENSIVE_BROCHURE', 'FI_BRIEF_BROCHURE', 'FI_WINDOW_CARD', 'FI_PROPERTY_MANAGERS_CERTIFICATE', 'FI_PLANNING_DOCUMENTS', 'FI_PURCHASE_AGREEMENT', 'FI_PROPERTY_REGISTER_EXTRACT', 'FI_PROPERTY_REGISTER_MAP', 'FI_PROPERTY_TAX_STATEMENT', 'FI_MOISTURE_MEASUREMENT', 'FI_MAINTENANCE_PLAN', 'FI_MAINTENANCE_NEED_ASSESSMENT', 'FI_CONDITION_INSPECTION_REPORT', 'FI_USAGE_RIGHTS_EXTRACT', 'FI_CERTIFICATE_OF_TITLE', 'FI_OTHER', 'FI_OWNER_APARTMENT_PRINTOUT', 'FI_SHARE_REGISTER_PRINT', 'FI_LONG_TERM_MAINTENANCE_PLAN', 'FI_SPOUSES_CONSENT', 'FI_FLOOR_PLAN', 'FI_BUILDING_PERMIT', 'FI_BUILDING_DRAWINGS', 'FI_ENCUMBRANCE_CERTIFICATE', 'FI_LISTING_FORM', 'FI_FINANCIAL_STATEMENT', 'FI_BROKERAGE_AGREEMENT', 'FI_LEASE_RIGHTS_CERTIFICATE', 'FI_LEASE_AGREEMENT', 'FI_BROKERAGE_OFFER', 'FI_ARTICLES_OF_ASSOCIATION', 'FI_GENERAL_MEETING_MINUTES', 'FI_HOUSING_COMPANY_CONDITION_REPORTS', name='document_type_enum'),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=False)
    # ### end Alembic commands ###
