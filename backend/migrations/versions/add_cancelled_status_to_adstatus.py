"""Add CANCELLED to AdStatus enum

Revision ID: add_cancelled_status_to_adstatus
Revises: 1cd19db972e5
Create Date: 2024-03-27 15:55:00.000000

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = 'add_cancelled_status_to_adstatus'
down_revision = '1cd19db972e5'
branch_labels = None
depends_on = None


def upgrade():
    # For MySQL, we need to modify the enum directly
    op.execute("ALTER TABLE advertisement MODIFY COLUMN status ENUM('draft', 'in_review', 'active', 'completed', 'cancelled') NOT NULL")


def downgrade():
    # First update any cancelled status to completed
    op.execute("UPDATE advertisement SET status = 'completed' WHERE status = 'cancelled'")
    # Then remove the cancelled enum value
    op.execute("ALTER TABLE advertisement MODIFY COLUMN status ENUM('draft', 'in_review', 'active', 'completed') NOT NULL") 