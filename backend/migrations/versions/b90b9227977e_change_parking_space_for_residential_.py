"""Change parking space for residential property

Revision ID: b90b9227977e
Revises: 1e429e1337f2
Create Date: 2025-06-18 17:39:34.653442

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "b90b9227977e"
down_revision = "1e429e1337f2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text

    conn = op.get_bind()
    conn.execute(
        text(
            """
        UPDATE fi_residential_property_overview
        SET parking_spaces = JSON_ARRAY(parking_spaces)
        WHERE JSON_TYPE(parking_spaces) = 'OBJECT'
    """
        )
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text

    conn = op.get_bind()
    conn.execute(
        text(
            """
        UPDATE fi_residential_property_overview
        SET parking_spaces = JSON_EXTRACT(parking_spaces, '$[0]')
        WHERE JSON_TYPE(parking_spaces) = 'ARRAY'
          AND JSON_LENGTH(parking_spaces) = 1
    """
        )
    )
    # ### end Alembic commands ###
