"""Increase size of url image

Revision ID: ff9bede2664e
Revises: 8f4819abd975
Create Date: 2024-12-31 10:35:47.444910

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "ff9bede2664e"
down_revision = "8f4819abd975"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "image",
        "url",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.String(length=1000),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "image",
        "url",
        existing_type=sa.String(length=1000),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
