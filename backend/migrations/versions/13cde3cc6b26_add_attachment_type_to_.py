"""add attachment_type to DocumentAttachments

Revision ID: 13cde3cc6b26
Revises: 81e57a0b9728
Create Date: 2024-09-06 13:52:34.812974

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "13cde3cc6b26"
down_revision = "81e57a0b9728"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_attachments",
        sa.Column(
            "attachment_type",
            sa.String(length=50),
            nullable=False,
            server_default="unknown",
        ),
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column("document_attachments", "attachment_type")
    # ### end Alembic commands ###
