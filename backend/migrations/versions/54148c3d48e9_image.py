"""Image

Revision ID: a0b727dbd42e
Revises: 54148c3d48e9
Create Date: 2023-06-11 20:44:59.817808

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "54148c3d48e9"
down_revision = "456396acede7"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        constraint_name="PRIMARY", table_name="dw_pictures", type_="primary"
    )
    op.execute("ALTER TABLE dw_pictures ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY;")
    op.add_column("dw_pictures", sa.Column("is_hidden", sa.<PERSON>(), nullable=False))
    op.add_column("dw_pictures", sa.Column("created_at", sa.DateTime(), nullable=True))
    op.add_column("dw_pictures", sa.Column("updated_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_constraint(
        constraint_name="PRIMARY", table_name="dw_pictures", type_="primary"
    )
    op.drop_column("dw_pictures", "id")
    op.create_primary_key(
        table_name="dw_pictures", constraint_name="pk_dw_pictures", columns=["img"]
    )
    op.drop_column("dw_pictures", "updated_at")
    op.drop_column("dw_pictures", "created_at")
    op.drop_column("dw_pictures", "is_hidden")
