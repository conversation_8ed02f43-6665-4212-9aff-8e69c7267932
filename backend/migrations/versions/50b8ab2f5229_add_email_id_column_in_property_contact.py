"""add email id column in property contact

Revision ID: 50b8ab2f5229
Revises: 435a567727f0
Create Date: 2025-01-22 09:41:49.394877

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "50b8ab2f5229"
down_revision = "435a567727f0"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "match_making_property_contact", sa.Column("email_id", sa.Text(), nullable=True)
    )
    op.add_column(
        "match_making", sa.Column("is_auto_sent", sa.<PERSON>(), nullable=True)
    )


def downgrade():
    op.drop_column("match_making_property_contact", "email_id")
    op.drop_column("match_making", "is_auto_sent")
