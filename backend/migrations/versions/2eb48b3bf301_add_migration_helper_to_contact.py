"""add migration helper to contact

Revision ID: 2eb48b3bf301
Revises: efd74b6e1849
Create Date: 2024-02-21 18:23:01.107167

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2eb48b3bf301"
down_revision = "efd74b6e1849"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("contact", sa.Column("legacy_id", sa.Integer(), nullable=True))
    op.add_column("contact", sa.Column("legacy_data", sa.Text(), nullable=True))
    op.add_column(
        "user", sa.Column("legacy_username", sa.String(length=20), nullable=True)
    )


def downgrade():
    op.drop_column("user", "legacy_username")
    op.drop_column("contact", "legacy_data")
    op.drop_column("contact", "legacy_id")
