"""Add fi sales agreement

Revision ID: 467c34d31b32
Revises: b5b94db3010d
Create Date: 2025-02-24 09:55:06.770509

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '467c34d31b32'
down_revision = 'b5b94db3010d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fi_sales_agreement',
    sa.Column('property_id', sa.BigInteger(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('agreed_marketing_methods', sa.String(length=250), nullable=True),
    sa.Column('separate_marketing_appendix', sa.Boolean(), nullable=True),
    sa.Column('unencumbered_price_request', sa.Float(), nullable=True),
    sa.Column('average_selling_time_estimate', sa.Float(), nullable=True),
    sa.Column('unencumbered_price_request_estimate', sa.Float(), nullable=True),
    sa.Column('shares_include_loan', sa.Boolean(), nullable=True),
    sa.Column('loan_amount', sa.Float(), nullable=True),
    sa.Column('price_including_loan', sa.Float(), nullable=True),
    sa.Column('payment_term_other', sa.String(length=250), nullable=True),
    sa.Column('factors_affecting_sales', sa.String(length=250), nullable=True),
    sa.Column('date_when_available', sa.Date(), nullable=True),
    sa.Column('availability_details', sa.String(length=250), nullable=True),
    sa.Column('tenant_name', sa.String(length=250), nullable=True),
    sa.Column('tenant_contact_details', sa.String(length=250), nullable=True),
    sa.Column('lease_amount', sa.Float(), nullable=True),
    sa.Column('lease_start_date', sa.Date(), nullable=True),
    sa.Column('lease_end_date', sa.Date(), nullable=True),
    sa.Column('lease_terminated', sa.Boolean(), nullable=True),
    sa.Column('tenant_has_paid_rent_on_time', sa.Boolean(), nullable=True),
    sa.Column('tenant_paying_rent_details', sa.String(length=250), nullable=True),
    sa.Column('lease_agreement_details', sa.String(length=250), nullable=True),
    sa.Column('restrictive_right_of_user', sa.Boolean(), nullable=True),
    sa.Column('restrictive_right_of_user_details', sa.String(length=250), nullable=True),
    sa.Column('written_consent_to_transfer', sa.Boolean(), nullable=True),
    sa.Column('belongs_to_business_activities', sa.Boolean(), nullable=True),
    sa.Column('assignment_validity', sa.String(length=50), nullable=True),
    sa.Column('assignment_validity_renewal_period', sa.Integer(), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('commission_fixed', sa.Float(), nullable=True),
    sa.Column('commission_other', sa.String(length=250), nullable=True),
    sa.Column('commission_percentage', sa.Float(), nullable=True),
    sa.Column('commission_details', sa.String(length=250), nullable=True),
    sa.Column('marketing_expenses_max', sa.Float(), nullable=True),
    sa.Column('document_acquisition_expenses_max', sa.Float(), nullable=True),
    sa.Column('other_expenses_details', sa.String(length=250), nullable=True),
    sa.Column('expense_if_no_commission', sa.String(length=250), nullable=True),
    sa.Column('seller_is_married_or_in_registered_relationship', sa.Boolean(), nullable=True),
    sa.Column('seller_has_been_married_or_in_registered_relationship', sa.Boolean(), nullable=True),
    sa.Column('seller_has_spouses_consent', sa.Boolean(), nullable=True),
    sa.Column('legal_partitioning_is_complete', sa.Boolean(), nullable=True),
    sa.Column('divorce_legally_binding', sa.Boolean(), nullable=True),
    sa.Column('acquisition_date', sa.Date(), nullable=True),
    sa.Column('acquisition_cost', sa.Float(), nullable=True),
    sa.Column('client_has_used_residence_as_residence', sa.Boolean(), nullable=True),
    sa.Column('residency_start_date', sa.Date(), nullable=True),
    sa.Column('residency_end_date', sa.Date(), nullable=True),
    sa.Column('share_register_format', sa.String(length=50), nullable=True),
    sa.Column('share_register_storage', sa.String(length=250), nullable=True),
    sa.Column('unpaid_maintenance_charge', sa.Boolean(), nullable=True),
    sa.Column('unpaid_maintenance_charge_amount', sa.Float(), nullable=True),
    sa.Column('digital_trading_allowed', sa.Boolean(), nullable=True),
    sa.Column('is_domestic_sale', sa.Boolean(), nullable=True),
    sa.Column('start_assignment_immediately', sa.Boolean(), nullable=True),
    sa.Column('start_marketing_after_cancel_period', sa.Boolean(), nullable=True),
    sa.Column('customer_asked_to_read_privacy_policy', sa.Boolean(), nullable=True),
    sa.Column('previous_external_sales_agreement', sa.Boolean(), nullable=True),
    sa.Column('previous_external_sales_agreement_details', sa.String(length=250), nullable=True),
    sa.Column('additional_details', sa.String(length=500), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=True),
    sa.Column('payment_terms', sa.String(length=50), nullable=True),
    sa.Column('availability', sa.String(length=50), nullable=True),
    sa.Column('lease_agreement', sa.String(length=50), nullable=True),
    sa.Column('lease_agreement_term', sa.String(length=50), nullable=True),
    sa.Column('commission_basis_code', sa.String(length=50), nullable=True),
    sa.Column('commission_type', sa.String(length=50), nullable=True),
    sa.Column('vat', sa.Boolean(), nullable=True),
    sa.Column('acquisition', sa.String(length=50), nullable=True),
    sa.Column('tax_consequence', sa.String(length=50), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], name=op.f('fk_fi_sales_agreement_created_by_user')),
    sa.ForeignKeyConstraint(['property_id'], ['fi_property.id'], name=op.f('fk_fi_sales_agreement_property_id_fi_property'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_sales_agreement'))
    )
    op.create_table('fi_sales_agreement_contact',
    sa.Column('contact_id', sa.BigInteger(), nullable=False),
    sa.Column('fi_sales_agreement_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contact.id'], name=op.f('fk_fi_sales_agreement_contact_contact_id_contact')),
    sa.ForeignKeyConstraint(['fi_sales_agreement_id'], ['fi_sales_agreement.id'], name=op.f('fk_fi_sales_agreement_contact_fi_sales_agreement_id_fi_sales_agreement')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_sales_agreement_contact')),
    sa.UniqueConstraint('fi_sales_agreement_id', 'contact_id', name='uq_fi_sales_agreement_contact')
    )
    op.create_table('fi_sales_agreement_realtor',
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('fi_sales_agreement_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['fi_sales_agreement_id'], ['fi_sales_agreement.id'], name=op.f('fk_fi_sales_agreement_realtor_fi_sales_agreement_id_fi_sales_agreement')),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name=op.f('fk_fi_sales_agreement_realtor_user_id_user')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_sales_agreement_realtor')),
    sa.UniqueConstraint('fi_sales_agreement_id', 'user_id', name='uq_fi_sales_agreement_realtor')
    )
    op.alter_column('fi_housing_company', 'manager_contact_details',
               existing_type=mysql.TINYTEXT(),
               type_=sa.Text(length=20),
               existing_nullable=True)
    op.alter_column('fi_residential_share_overview', 'debt_share_additional_info',
               existing_type=mysql.TEXT(),
               type_=sa.JSON(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_residential_share_overview', 'debt_share_additional_info',
               existing_type=sa.JSON(),
               type_=mysql.TEXT(),
               existing_nullable=True)
    op.alter_column('fi_housing_company', 'manager_contact_details',
               existing_type=sa.Text(length=20),
               type_=mysql.TINYTEXT(),
               existing_nullable=True)
    op.drop_table('fi_sales_agreement_realtor')
    op.drop_table('fi_sales_agreement_contact')
    op.drop_table('fi_sales_agreement')
    # ### end Alembic commands ###
