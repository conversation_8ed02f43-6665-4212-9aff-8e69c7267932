"""add polymorphic connections for expense transactions

Revision ID: d28227503d4d
Revises: 98831f1e456d
Create Date: 2025-04-13 12:43:05.527153

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import logging

# revision identifiers, used by Alembic.
revision = 'd28227503d4d'
down_revision = '98831f1e456d'
branch_labels = None
depends_on = None

logger = logging.getLogger('alembic.runtime.migration')

def upgrade():
    connection = op.get_bind()
    inspector = sa.engine.reflection.Inspector.from_engine(connection)
    existing_tables = inspector.get_table_names()

    # Create expense_transaction_targets table if it doesn't exist.
    if 'expense_transaction_targets' not in existing_tables:
        op.create_table(
            'expense_transaction_targets',
            sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
            sa.Column('expense_transaction_id', sa.BigInteger(), nullable=False),
            sa.Column('target_type', sa.String(length=20), nullable=False),
            sa.Column('target_id', sa.BigInteger(), nullable=False),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(
                ['expense_transaction_id'],
                ['transactions.id'],
                name=op.f('fk_expense_transaction_targets_expense_transaction_id_transactions')
            ),
            sa.PrimaryKeyConstraint('id', name=op.f('pk_expense_transaction_targets'))
        )
    else:
        logger.info("Table 'expense_transaction_targets' already exists; skipping its creation.")

    # Adjust the advertisement_image foreign key if necessary.
    op.drop_constraint(
        'fk_advertisement_image_advertisement_id_advertisement',
        'advertisement_image',
        type_='foreignkey'
    )
    op.create_foreign_key(
        op.f('fk_advertisement_image_advertisement_id_advertisement'),
        'advertisement_image',
        'advertisement',
        ['advertisement_id'], ['id']
    )

    # Before altering column types, drop the FK on expense_transaction_targets that references transactions.id.
    # Get all FK constraints on expense_transaction_targets.
    fk_constraints = inspector.get_foreign_keys('expense_transaction_targets')
    fk_to_drop = None
    for fk in fk_constraints:
        if 'expense_transaction_id' in fk.get('constrained_columns', []):
            fk_to_drop = fk["name"]
            break
    if fk_to_drop:
        op.drop_constraint(fk_to_drop, 'expense_transaction_targets', type_='foreignkey')
    else:
        logger.info("No foreign key found on expense_transaction_targets.expense_transaction_id; skipping drop.")

    # Alter transactions.id from BIGINT to INTEGER.
    op.alter_column(
        'transactions',
        'id',
        existing_type=mysql.BIGINT(display_width=20),
        type_=sa.Integer(),
        existing_nullable=False,
        autoincrement=True
    )

    # Alter expense_transaction_targets.expense_transaction_id to match the new type.
    op.alter_column(
        'expense_transaction_targets',
        'expense_transaction_id',
        existing_type=mysql.BIGINT(display_width=20),
        type_=sa.Integer(),
        existing_nullable=False
    )

    # Recreate the FK constraint on expense_transaction_targets linking to transactions.id.
    op.create_foreign_key(
        op.f('fk_expense_transaction_targets_expense_transaction_id_transactions'),
        'expense_transaction_targets',
        'transactions',
        ['expense_transaction_id'], ['id']
    )


def downgrade():
    connection = op.get_bind()
    inspector = sa.engine.reflection.Inspector.from_engine(connection)

    # Drop the FK on expense_transaction_targets first.
    fk_constraints = inspector.get_foreign_keys('expense_transaction_targets')
    fk_to_drop = None
    for fk in fk_constraints:
        if 'expense_transaction_id' in fk.get('constrained_columns', []):
            fk_to_drop = fk["name"]
            break
    if fk_to_drop:
        op.drop_constraint(fk_to_drop, 'expense_transaction_targets', type_='foreignkey')
    else:
        logger.info("No foreign key found on expense_transaction_targets.expense_transaction_id during downgrade; skipping drop.")

    # Revert the transactions.id column from INTEGER back to BIGINT.
    op.alter_column(
        'transactions',
        'id',
        existing_type=sa.Integer(),
        type_=mysql.BIGINT(display_width=20),
        existing_nullable=False,
        autoincrement=True
    )

    # Revert expense_transaction_targets.expense_transaction_id as well.
    op.alter_column(
        'expense_transaction_targets',
        'expense_transaction_id',
        existing_type=sa.Integer(),
        type_=mysql.BIGINT(display_width=20),
        existing_nullable=False
    )

    # Recreate the original FK constraint.
    op.create_foreign_key(
        op.f('fk_expense_transaction_targets_expense_transaction_id_transactions'),
        'expense_transaction_targets',
        'transactions',
        ['expense_transaction_id'], ['id']
    )

    # Adjust the advertisement_image foreign key back.
    op.drop_constraint(
        op.f('fk_advertisement_image_advertisement_id_advertisement'),
        'advertisement_image',
        type_='foreignkey'
    )
    op.create_foreign_key(
        'fk_advertisement_image_advertisement_id_advertisement',
        'advertisement_image',
        'advertisement',
        ['advertisement_id'], ['id'],
        ondelete='CASCADE'
    )

    op.drop_table('expense_transaction_targets')
