"""Update fi sales agreement properties

Revision ID: f00c2d112c0c
Revises: 7e45354f0381
Create Date: 2025-04-02 12:10:10.923166

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "f00c2d112c0c"
down_revision = "7e45354f0381"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_sales_agreement",
        sa.Column("loan_details", sa.String(length=250), nullable=True),
    )
    op.alter_column(
        "fi_sales_agreement",
        "acquisition_date",
        existing_type=sa.DATE(),
        type_=sa.String(length=250),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "acquisition_cost",
        existing_type=mysql.FLOAT(),
        type_=sa.String(length=250),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_sales_agreement",
        "acquisition_cost",
        existing_type=sa.String(length=250),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "acquisition_date",
        existing_type=sa.String(length=250),
        type_=sa.DATE(),
        existing_nullable=True,
    )
    op.drop_column("fi_sales_agreement", "loan_details")
    # ### end Alembic commands ###
