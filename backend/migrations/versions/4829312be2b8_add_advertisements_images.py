"""Add advertisements images

Revision ID: 4829312be2b8
Revises: 2da5bafc2ae8
Create Date: 2025-03-21 15:42:40.465527

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '4829312be2b8'
down_revision = '2da5bafc2ae8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('advertisement_image',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('advertisement_id', sa.Integer(), nullable=False),
    sa.Column('url', sa.String(length=3000), nullable=False),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('is_hidden', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['advertisement_id'], ['advertisement.id'], name=op.f('fk_advertisement_image_advertisement_id_advertisement')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_advertisement_image'))
    )
    op.create_index(op.f('ix_advertisement_image_advertisement_id'), 'advertisement_image', ['advertisement_id'], unique=False)
    op.add_column('advertisement', sa.Column('link_clicks', sa.Integer(), nullable=True))
    op.add_column('advertisement', sa.Column('impressions', sa.Integer(), nullable=True))
    op.add_column('advertisement', sa.Column('ctr', sa.Float(), nullable=True))
    op.drop_column('advertisement', 'type')
    op.drop_column('advertisement', 'image_url')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement', sa.Column('image_url', mysql.VARCHAR(length=512), nullable=True))
    op.add_column('advertisement', sa.Column('type', mysql.ENUM('LISTING_PROPERTY', 'PROPERTY_SOLD', 'AGENT', 'EVENT', 'CUSTOM'), nullable=False))
    op.drop_column('advertisement', 'ctr')
    op.drop_column('advertisement', 'impressions')
    op.drop_column('advertisement', 'link_clicks')
    op.drop_index(op.f('ix_advertisement_image_advertisement_id'), table_name='advertisement_image')
    op.drop_table('advertisement_image')
    # ### end Alembic commands ###
