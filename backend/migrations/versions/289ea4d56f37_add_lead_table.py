"""add lead table

Revision ID: 289ea4d56f37
Revises: 545cb3e469c2
Create Date: 2024-01-26 09:50:22.979575

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "289ea4d56f37"
down_revision = "2eb48b3bf301"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "lead",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("status", sa.String(length=255), nullable=False),
        sa.Column("relevance", sa.String(length=255), nullable=True),
        sa.Column("linked_match_making", sa.BigInteger(), nullable=True),
        sa.Column("property_reference", sa.String(length=20), nullable=True),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("type", sa.String(length=255), nullable=True),
        sa.Column("source", sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_reference"],
            ["property.reference"],
            name=op.f("fk_lead_property_reference_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_lead")),
    )

    op.create_table(
        "lead_user",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("lead_id", sa.BigInteger(), nullable=False),
        sa.Column("user_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["lead_id"], ["lead.id"], name=op.f("fk_lead_user_lead_lead")
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_lead_user_user_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_lead_user")),
        sa.UniqueConstraint("lead_id", "user_id", name=op.f("uq_lead_user")),
    )

    op.create_table(
        "lead_contact",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("lead_id", sa.BigInteger(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["lead_id"], ["lead.id"], name=op.f("fk_lead_contact_lead_lead")
        ),
        sa.ForeignKeyConstraint(
            ["contact_id"], ["contact.id"], name=op.f("fk_lead_contact_contact_contact")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_lead_contact")),
        sa.UniqueConstraint("lead_id", "contact_id", name=op.f("uq_lead_contact")),
    )

    op.create_index(
        op.f("ix_lead_user_lead"),
        "lead_user",
        ["lead_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_lead_user_user"),
        "lead_user",
        ["user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_lead_contact_lead"),
        "lead_contact",
        ["lead_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_lead_contact_contact"),
        "lead_contact",
        ["contact_id"],
        unique=False,
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_lead_property_reference_property"), "lead", type_="foreignkey"
    )
    op.drop_constraint(op.f("fk_lead_user_user_user"), "lead_user", type_="foreignkey")
    op.drop_constraint(op.f("fk_lead_user_lead_lead"), "lead_user", type_="foreignkey")
    op.drop_constraint(
        op.f("fk_lead_contact_lead_lead"), "lead_contact", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_lead_contact_contact_contact"), "lead_contact", type_="foreignkey"
    )
    op.drop_table("lead")
    op.drop_table("lead_user")
