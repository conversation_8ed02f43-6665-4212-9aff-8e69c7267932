"""Add unique constraint to many-to-many tables

Revision ID: d065be9af2a2
Revises: 7d9c46e576fa
Create Date: 2024-06-18 15:52:21.881957

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d065be9af2a2"
down_revision = "7d9c46e576fa"
branch_labels = None
depends_on = None


def upgrade():
    op.create_unique_constraint(
        "uq_property_feature", "property_feature", ["property_id", "feature_id"]
    )
    op.create_unique_constraint(
        "uq_property_orientation",
        "property_orientation",
        ["property_id", "orientation_id"],
    )
    op.create_unique_constraint(
        "uq_property_setting", "property_setting", ["property_id", "setting_id"]
    )
    op.create_unique_constraint(
        "uq_property_view", "property_view", ["property_id", "view_id"]
    )


def downgrade():
    op.drop_constraint("uq_property_view", "property_view", type_="unique")
    op.drop_constraint("uq_property_setting", "property_setting", type_="unique")
    op.drop_constraint(
        "uq_property_orientation", "property_orientation", type_="unique"
    )
    op.drop_constraint("uq_property_feature", "property_feature", type_="unique")
