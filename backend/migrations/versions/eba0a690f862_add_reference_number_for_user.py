"""Add reference number for user

Revision ID: eba0a690f862
Revises: b687d359c6c3
Create Date: 2024-11-14 13:53:18.911262

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'eba0a690f862'
down_revision = 'b687d359c6c3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('reference_number', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'reference_number')
    # ### end Alembic commands ###
