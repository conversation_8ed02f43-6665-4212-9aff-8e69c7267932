"""Add expenses and transactions

Revision ID: 7fdcbc4bdb17
Revises: b5d523ed1caf
Create Date: 2025-03-28 14:32:13.652490

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7fdcbc4bdb17'
down_revision = 'b5d523ed1caf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('transactions',
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=20), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('meta', sa.JSON(), nullable=True),
    sa.Column('stripe_transaction_id', sa.String(length=255), nullable=True),
    sa.Column('id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_transactions'))
    )
    op.add_column('user', sa.Column('stripe_customer_id', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'stripe_customer_id')
    op.create_index('uq_message_event_uuid', 'message', ['event_uuid'], unique=True)
    op.drop_table('transactions')
    # ### end Alembic commands ###
