"""Update property relationship for video

Revision ID: 5caa155b97f5
Revises: 6c313c6d9e84
Create Date: 2024-10-14 14:21:56.123572

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "5caa155b97f5"
down_revision = "6c313c6d9e84"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_video_property_id_property", "video", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_video_property_id_property"),
        "video",
        "property",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_video_property_id_property"), "video", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_video_property_id_property",
        "video",
        "property_spain",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###
