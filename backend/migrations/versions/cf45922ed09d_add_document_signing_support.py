"""Add document signing support

Revision ID: cf45922ed09d
Revises: f00c2d112c0c
Create Date: 2025-04-03 09:16:49.618484

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "cf45922ed09d"
down_revision = "f00c2d112c0c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_signing",
        sa.Column("entity_id", sa.Integer(), nullable=False),
        sa.Column("entity_type", sa.String(length=50), nullable=False),
        sa.Column("document_external_id", sa.String(length=200), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("deadline", sa.DateTime(), nullable=True),
        sa.Column("id", sa.<PERSON>ger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_document_signing")),
    )
    op.create_table(
        "document_event",
        sa.Column("document_signing_id", sa.BigInteger(), nullable=False),
        sa.Column("action", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("raw_payload", sa.JSON(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_signing_id"],
            ["document_signing.id"],
            name=op.f("fk_document_event_document_signing_id_document_signing"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_document_event")),
    )
    op.create_table(
        "document_signer",
        sa.Column("document_signing_id", sa.BigInteger(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("user_type", sa.String(length=50), nullable=False),
        sa.Column("signing_external_id", sa.String(length=200), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_signing_id"],
            ["document_signing.id"],
            name=op.f("fk_document_signer_document_signing_id_document_signing"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_document_signer")),
    )
    op.create_table(
        "fi_sales_agreement_consenter",
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("fi_sales_agreement_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_fi_sales_agreement_consenter_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["fi_sales_agreement_id"],
            ["fi_sales_agreement.id"],
            name=op.f(
                "fk_fi_sales_agreement_consenter_fi_sales_agreement_id_fi_sales_agreement"
            ),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_sales_agreement_consenter")),
        sa.UniqueConstraint(
            "fi_sales_agreement_id",
            "contact_id",
            name="uq_fi_sales_agreement_consenter",
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("fi_sales_agreement_consenter")
    op.drop_table("document_signer")
    op.drop_table("document_event")
    op.drop_table("document_signing")
    # ### end Alembic commands ###
