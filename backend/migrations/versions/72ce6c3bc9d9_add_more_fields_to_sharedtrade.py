"""add more fields to SharedTrade

Revision ID: 72ce6c3bc9d9
Revises: eba0a690f862
Create Date: 2024-11-14 09:49:11.504442

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "72ce6c3bc9d9"
down_revision = "eba0a690f862"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade", sa.<PERSON>umn("internal_sellers", sa.JSON(), nullable=True)
    )
    op.add_column(
        "dias_shared_trade", sa.Column("internal_buyers", sa.JSON(), nullable=True)
    )
    op.add_column(
        "dias_shared_trade", sa.Column("internal_realtor", sa.JSO<PERSON>(), nullable=True)
    )
    op.add_column(
        "dias_shared_trade",
        sa.<PERSON>umn("include_commission", sa.<PERSON>(), nullable=True),
    )
    op.add_column(
        "dias_shared_trade",
        sa.Column("realtor_sum_commission", sa.<PERSON>nteger(), nullable=True),
    )
    op.add_column(
        "dias_shared_trade",
        sa.Column("invoice_verification_type", sa.String(length=100), nullable=True),
    )
    op.add_column(
        "dias_shared_trade",
        sa.Column("invoice_verification_value", sa.String(length=250), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("dias_shared_trade", "invoice_verification_value")
    op.drop_column("dias_shared_trade", "invoice_verification_type")
    op.drop_column("dias_shared_trade", "realtor_sum_commission")
    op.drop_column("dias_shared_trade", "include_commission")
    op.drop_column("dias_shared_trade", "internal_realtor")
    op.drop_column("dias_shared_trade", "internal_buyers")
    op.drop_column("dias_shared_trade", "internal_sellers")
    # ### end Alembic commands ###
