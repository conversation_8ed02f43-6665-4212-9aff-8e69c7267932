"""add organization for raw lead data

Revision ID: a0f573c0becd
Revises: ec35d25ede30
Create Date: 2024-10-31 16:36:06.043514

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a0f573c0becd'
down_revision = 'ec35d25ede30'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('raw_lead_data', sa.Column('organization', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('raw_lead_data', 'organization')
    # ### end Alembic commands ###
