"""change int to float

Revision ID: b65b3793e5ea
Revises: d71d7391af04
Create Date: 2024-12-17 17:00:00.275088

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "b65b3793e5ea"
down_revision = "d71d7391af04"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "dias_shared_trade",
        "realtor_sum_commission",
        existing_type=mysql.BIGINT(display_width=20),
        type_=sa.Float(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "dias_shared_trade",
        "realtor_sum_commission",
        existing_type=sa.Float(),
        type_=mysql.BIGINT(display_width=20),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
