"""add campaign name for raw lead data

Revision ID: a38252de2052
Revises: ba0c15e85630
Create Date: 2024-08-21 09:52:54.680783

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a38252de2052'
down_revision = 'ba0c15e85630'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('raw_lead_data', sa.Column('campaign_name', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('raw_lead_data', 'campaign_name')
    # ### end Alembic commands ###
