"""fix_advertisement_preview_settings_primary_key

Revision ID: 0550fc6eb4f0
Revises: 086deab883a0
Create Date: 2025-05-13 16:37:13.917765

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '0550fc6eb4f0'
down_revision = '086deab883a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement_preview_settings', sa.Column('id', sa.BigInteger(), nullable=False))
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('advertisement_preview_settings', 'id')
    # ### end Alembic commands ###
