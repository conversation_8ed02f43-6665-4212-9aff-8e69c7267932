"""update_dos_invoice_seller

Revision ID: 87680345c315
Revises: b36e2aeefb2f
Create Date: 2024-09-14 21:30:01.385481

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "87680345c315"
down_revision = "b36e2aeefb2f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "details_of_sale_invoice",
        sa.Column("issued_by", sa.BigInteger(), nullable=True),
    )
    op.add_column(
        "details_of_sale_invoice", sa.Column("invoice_number", sa.Text(), nullable=True)
    )
    op.add_column(
        "details_of_sale_invoice",
        sa.Column("proforma_id", sa.BigInteger(), nullable=True),
    )
    op.add_column(
        "details_of_sale_invoice", sa.Column("agent_id", sa.<PERSON>Integer(), nullable=True)
    )
    op.create_unique_constraint(
        op.f("uq_details_of_sale_invoice_invoice_number"),
        "details_of_sale_invoice",
        ["invoice_number"],
    )
    op.create_foreign_key(
        op.f("fk_details_of_sale_invoice_proforma_id_document"),
        "details_of_sale_invoice",
        "document",
        ["proforma_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_details_of_sale_invoice_agent_id_user"),
        "details_of_sale_invoice",
        "user",
        ["agent_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_details_of_sale_invoice_issued_by_office"),
        "details_of_sale_invoice",
        "office",
        ["issued_by"],
        ["id"],
    )
    op.drop_constraint(
        "fk_seller_details_of_sale_issued_by_office",
        "seller_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("seller_details_of_sale", "issued_by")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "seller_details_of_sale",
        sa.Column(
            "issued_by",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_foreign_key(
        "fk_seller_details_of_sale_issued_by_office",
        "seller_details_of_sale",
        "office",
        ["issued_by"],
        ["id"],
    )
    op.drop_constraint(
        op.f("fk_details_of_sale_invoice_issued_by_office"),
        "details_of_sale_invoice",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_details_of_sale_invoice_agent_id_user"),
        "details_of_sale_invoice",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_details_of_sale_invoice_proforma_id_document"),
        "details_of_sale_invoice",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("uq_details_of_sale_invoice_invoice_number"),
        "details_of_sale_invoice",
        type_="unique",
    )
    op.drop_column("details_of_sale_invoice", "agent_id")
    op.drop_column("details_of_sale_invoice", "proforma_id")
    op.drop_column("details_of_sale_invoice", "invoice_number")
    op.drop_column("details_of_sale_invoice", "issued_by")
    # ### end Alembic commands ###
