"""Add long, lat for FIProperty

Revision ID: b6a3d083cca2
Revises: 0a2f334a784a
Create Date: 2024-08-23 17:02:27.378527

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b6a3d083cca2"
down_revision = "0a2f334a784a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("fi_property", sa.Column("latitude", sa.Float(), nullable=True))
    op.add_column("fi_property", sa.Column("longitude", sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_property", "longitude")
    op.drop_column("fi_property", "latitude")
    # ### end Alembic commands ###
