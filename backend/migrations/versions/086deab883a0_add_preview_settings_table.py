"""Add preview settings table

Revision ID: 086deab883a0
Revises: 2bdabb0e2fa9
Create Date: 2025-05-02 21:59:56.321463

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '086deab883a0'
down_revision = '2bdabb0e2fa9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('advertisement_preview_settings',
    sa.<PERSON>umn('advertisement_id', sa.BigInteger(), nullable=False),
    sa.Column('display_metrics', sa.<PERSON>(), nullable=False),
    sa.Column('display_details', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['advertisement_id'], ['advertisement.id'], name=op.f('fk_advertisement_preview_settings_advertisement_id_advertisement'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('advertisement_id', name=op.f('pk_advertisement_preview_settings'))
    )
    op.create_index(op.f('ix_advertisement_preview_settings_advertisement_id'), 'advertisement_preview_settings', ['advertisement_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_advertisement_preview_settings_advertisement_id'), table_name='advertisement_preview_settings')
    op.drop_table('advertisement_preview_settings')
    # ### end Alembic commands ###
