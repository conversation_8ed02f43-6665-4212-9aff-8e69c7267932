"""Change signing_method to string

Revision ID: 7fabf14f245f
Revises: 4d3628b3b581
Create Date: 2024-06-24 13:47:29.506729

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "7fabf14f245f"
down_revision = "4d3628b3b581"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "sales_agreement",
        "signing_method",
        existing_type=mysql.INTEGER(display_width=11),
        type_=sa.String(length=15),
        existing_nullable=False,
    )
    op.execute("UPDATE sales_agreement SET signing_method = 'pen_and_paper'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE sales_agreement SET signing_method = 0")
    op.alter_column(
        "sales_agreement",
        "signing_method",
        existing_type=sa.String(length=15),
        type_=mysql.INTEGER(display_width=11),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
