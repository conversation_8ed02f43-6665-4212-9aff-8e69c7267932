"""add new is_manual_assign field for raw lead data

Revision ID: c6bd2ce1fe77
Revises: d75395eadb0d
Create Date: 2024-08-19 14:11:05.151704

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision = "c6bd2ce1fe77"
down_revision = "d75395eadb0d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "raw_lead_data",
        sa.Column("is_manual_assign", sa.<PERSON>an(), nullable=True, default=False),
    )
    op.alter_column(
        "raw_lead_data",
        "full_name",
        existing_type=mysql.VARCHAR(length=100),
        nullable=True,
    )
    op.alter_column(
        "raw_lead_data",
        "phone",
        existing_type=mysql.VARCHAR(length=100),
        nullable=False,
    )
    op.alter_column(
        "raw_lead_data",
        "contact_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("raw_lead_data", "is_manual_assign")
    op.alter_column(
        "raw_lead_data",
        "contact_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=False,
    )
    op.alter_column(
        "raw_lead_data", "phone", existing_type=mysql.VARCHAR(length=100), nullable=True
    )
    op.alter_column(
        "raw_lead_data",
        "full_name",
        existing_type=mysql.VARCHAR(length=100),
        nullable=False,
    )
    # ### end Alembic commands ###
