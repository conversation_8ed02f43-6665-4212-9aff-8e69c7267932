"""Change auction listing to optional

Revision ID: 6fba7ccd603b
Revises: 62231a092825
Create Date: 2024-11-28 11:17:26.148668

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6fba7ccd603b'
down_revision = '62231a092825'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_other_share_overview', 'is_auction_listing',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_other_share_overview', 'is_auction_listing',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    # ### end Alembic commands ###
