"""Add match making track table

Revision ID: e238e03b0076
Revises: 7e3db1771e38
Create Date: 2024-11-01 09:54:10.870445

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e238e03b0076"
down_revision = "7e3db1771e38"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "match_making_track",
        sa.<PERSON>umn("match_making_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("property_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("realtor_id", sa.BigInteger(), nullable=False),
        sa.Column("click_count", sa.<PERSON>Integer(), nullable=False),
        sa.Column("id", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_track_match_making_id_match_making"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property_spain.id"],
            name=op.f("fk_match_making_track_property_id_property_spain"),
        ),
        sa.ForeignKeyConstraint(
            ["realtor_id"],
            ["user.id"],
            name=op.f("fk_match_making_track_realtor_id_user"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_track")),
        sa.UniqueConstraint(
            "match_making_id", "property_id", "realtor_id", name="uq_match_making_track"
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_match_making_track_match_making_id_match_making"),
        "match_making_track",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_track_property_id_property_spain"),
        "match_making_track",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_track_realtor_id_user"),
        "match_making_track",
        type_="foreignkey",
    )
    op.drop_table("match_making_track")
    # ### end Alembic commands ###
