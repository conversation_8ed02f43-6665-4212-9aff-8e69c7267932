"""energy certificate fields

Revision ID: 14b22f751151
Revises: 289ea4d56f37
Create Date: 2024-03-13 13:23:03.838450

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "14b22f751151"
down_revision = "289ea4d56f37"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property",
        sa.Column(
            "certificate_consumption_rating", sa.String(length=10), nullable=True
        ),
    )
    op.add_column(
        "property",
        sa.Column("certificate_consumption_value", sa.Float(), nullable=True),
    )
    op.add_column(
        "property",
        sa.Column("certificate_emission_rating", sa.String(length=10), nullable=True),
    )
    op.add_column(
        "property", sa.Column("certificate_emission_value", sa.Float(), nullable=True)
    )
    op.drop_column("property", "certificate_type")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property",
        sa.Column("certificate_type", mysql.VARCHAR(length=20), nullable=True),
    )
    op.drop_column("property", "certificate_emission_value")
    op.drop_column("property", "certificate_emission_rating")
    op.drop_column("property", "certificate_consumption_value")
    op.drop_column("property", "certificate_consumption_rating")
    # ### end Alembic commands ###
