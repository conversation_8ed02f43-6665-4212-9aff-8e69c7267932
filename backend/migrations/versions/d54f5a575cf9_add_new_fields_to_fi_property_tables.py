"""Add new fields to FI property tables

Revision ID: d54f5a575cf9
Revises: 0e441fac7d18
Create Date: 2024-08-13 23:03:07.214059

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "d54f5a575cf9"
down_revision = "0e441fac7d18"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_plot_overview", sa.Column("plot_redemption_info", sa.Text(), nullable=True)
    )
    op.add_column(
        "fi_plot_overview",
        sa.Column("plot_redemption_info_link", sa.Text(length=255), nullable=True),
    )
    op.add_column(
        "fi_plot_overview", sa.Column("plot_rental_agreement", sa.Text(), nullable=True)
    )
    op.add_column(
        "fi_plot_overview",
        sa.Column("plot_rental_agreement_link", sa.Text(length=255), nullable=True),
    )
    op.create_foreign_key(
        op.f("fk_fi_property_id_property"), "fi_property", "property", ["id"], ["id"]
    )
    op.drop_column("fi_property", "created_at")
    op.drop_column("fi_property", "updated_at")
    op.add_column(
        "fi_realty", sa.Column("status", sa.String(length=20), nullable=False)
    )
    op.create_index(op.f("ix_fi_realty_status"), "fi_realty", ["status"], unique=False)
    op.add_column(
        "fi_residential_share_overview",
        sa.Column("debt_share_additional_info", sa.String(length=255), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_residential_share_overview", "debt_share_additional_info")
    op.drop_index(op.f("ix_fi_realty_status"), table_name="fi_realty")
    op.drop_column("fi_realty", "status")
    op.add_column(
        "fi_property", sa.Column("updated_at", mysql.DATETIME(), nullable=True)
    )
    op.add_column(
        "fi_property", sa.Column("created_at", mysql.DATETIME(), nullable=True)
    )
    op.drop_constraint(
        op.f("fk_fi_property_id_property"), "fi_property", type_="foreignkey"
    )
    op.drop_column("fi_plot_overview", "plot_rental_agreement_link")
    op.drop_column("fi_plot_overview", "plot_rental_agreement")
    op.drop_column("fi_plot_overview", "plot_redemption_info_link")
    op.drop_column("fi_plot_overview", "plot_redemption_info")
    # ### end Alembic commands ###
