"""tweak fi purchase offer

Revision ID: 5b1d8d0776bb
Revises: 20134daad6b2
Create Date: 2025-04-07 12:14:53.288172

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "5b1d8d0776bb"
down_revision = "20134daad6b2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_purchase_offer",
        sa.Column("availability_delay_fee", sa.Float(), nullable=True),
    )
    op.add_column(
        "fi_purchase_offer",
        sa.Column(
            "digital_purchase_expenses_details", sa.String(length=250), nullable=True
        ),
    )
    op.alter_column(
        "fi_purchase_offer",
        "transfer_right_of_use_latest",
        existing_type=sa.DATE(),
        type_=sa.DateTime(),
        existing_nullable=True,
    )
    op.drop_column("fi_purchase_offer", "avalability_delay_fee")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_purchase_offer",
        sa.Column("avalability_delay_fee", mysql.FLOAT(), nullable=True),
    )
    op.alter_column(
        "fi_purchase_offer",
        "transfer_right_of_use_latest",
        existing_type=sa.DateTime(),
        type_=sa.DATE(),
        existing_nullable=True,
    )
    op.drop_column("fi_purchase_offer", "digital_purchase_expenses_details")
    op.drop_column("fi_purchase_offer", "availability_delay_fee")
    # ### end Alembic commands ###
