"""add table sale_agreement

Revision ID: 7607e0bb65cd
Revises: bb49feb13dd4
Create Date: 2024-06-18 17:52:39.586352

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "7607e0bb65cd"
down_revision = "bb49feb13dd4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sales_agreement",
        sa.Column(
            "property_document_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "first_seller_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "validity_in_months",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "signing_method",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "metadata_json",
            mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
            nullable=False,
        ),
        sa.Column(
            "id", mysql.BIGINT(display_width=20), autoincrement=True, nullable=False
        ),
        sa.Column("created_at", mysql.DATETIME(), nullable=True),
        sa.Column("updated_at", mysql.DATETIME(), nullable=True),
        sa.ForeignKeyConstraint(
            ["property_document_id"],
            ["property_document.id"],
            name="fk_sales_agreement_property_document_id_property_document",
        ),
        sa.ForeignKeyConstraint(
            ["first_seller_id"],
            ["contact.id"],
            name="fk_sale_agreement_first_seller_id_contact",
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.CheckConstraint(
            "validity_in_months > 0", name="positive_validity_in_months"
        ),
        mariadb_collate="utf8mb4_unicode_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("sales_agreement")
    # ### end Alembic commands ###
