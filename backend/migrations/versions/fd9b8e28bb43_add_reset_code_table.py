"""Add reset_code table

Revision ID: fd9b8e28bb43
Revises: c048acc3346a
Create Date: 2023-06-19 14:34:05.199271

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "fd9b8e28bb43"
down_revision = "c048acc3346a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "reset_code",
        sa.<PERSON>umn("code", sa.Text(), nullable=False),
        sa.Column("status", sa.Text(), nullable=True),
        sa.Column("expired_at", sa.DateTime(), nullable=True),
        sa.Column("user_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_reset_code_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_reset_code")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("reset_code")
    # ### end Alembic commands ###
