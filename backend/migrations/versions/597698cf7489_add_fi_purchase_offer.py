"""add fi_purchase_offer

Revision ID: 597698cf7489
Revises: eb1d1bc8d388
Create Date: 2025-03-13 08:41:35.327967

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "597698cf7489"
down_revision = "eb1d1bc8d388"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fi_purchase_offer",
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=True),
        sa.Column("created_by", sa.<PERSON>ger(), nullable=True),
        sa.Column("reviewed_documents", sa.JSON(), nullable=True),
        sa.Column("unencumbered_price", sa.Float(), nullable=True),
        sa.Column("loan_amount", sa.Float(), nullable=True),
        sa.Column("price_including_loan", sa.Float(), nullable=True),
        sa.Column("loan_date", sa.Date(), nullable=True),
        sa.Column("building_manager_certificate_date", sa.Date(), nullable=True),
        sa.Column("payment_method", sa.String(length=50), nullable=True),
        sa.Column("payment_schedule_and_terms", sa.String(length=500), nullable=True),
        sa.Column("transfer_right_of_use", sa.String(length=50), nullable=True),
        sa.Column("transfer_right_of_use_latest", sa.Date(), nullable=True),
        sa.Column("avalability_delay_fee", sa.Float(), nullable=True),
        sa.Column("delay_fee_per_started_week", sa.Float(), nullable=True),
        sa.Column("is_rented", sa.Boolean(), nullable=True),
        sa.Column("buyer_receives_rent_starting", sa.Date(), nullable=True),
        sa.Column("buyer_receives_rental_deposit_latest", sa.Date(), nullable=True),
        sa.Column("buyer_responsible_for_costs_starting", sa.Date(), nullable=True),
        sa.Column(
            "buyer_responsible_for_capital_expenditure_charge_starting",
            sa.Date(),
            nullable=True,
        ),
        sa.Column("digital_purchase", sa.Boolean(), nullable=True),
        sa.Column("digital_purchase_expenses", sa.String(length=50), nullable=True),
        sa.Column("standard_compenstation", sa.Float(), nullable=True),
        sa.Column("down_payment", sa.Float(), nullable=True),
        sa.Column("down_payment_term", sa.String(length=50), nullable=True),
        sa.Column("valid_until", sa.DateTime(), nullable=True),
        sa.Column("signed_latest", sa.Date(), nullable=True),
        sa.Column("accept_offer_email", sa.String(length=250), nullable=True),
        sa.Column("accept_offer_phone", sa.String(length=50), nullable=True),
        sa.Column("has_read_privacy_policy", sa.Boolean(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by"],
            ["user.id"],
            name=op.f("fk_fi_purchase_offer_created_by_user"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["fi_property.id"],
            name=op.f("fk_fi_purchase_offer_property_id_fi_property"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_purchase_offer")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("fi_purchase_offer")
    # ### end Alembic commands ###
