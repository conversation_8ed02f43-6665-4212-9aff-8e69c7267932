"""Add email template columns

Revision ID: 3de092a4d143
Revises: ff9bede2664e
Create Date: 2025-01-01 23:10:44.642271

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "3de092a4d143"
down_revision = "ff9bede2664e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table("match_making_email_template")
    op.add_column("email_template", sa.Column("uuid", sa.Text(), nullable=False))
    op.add_column("email_template", sa.Column("name", sa.Text(), nullable=True))
    op.alter_column(
        "email_template",
        "template",
        existing_type=mysql.TEXT(),
        type_=mysql.LONGTEXT(),
        existing_nullable=True,
    )
    op.create_unique_constraint("uq_email_template_uuid", "email_template", ["uuid"])
    op.add_column(
        "match_making", sa.Column("email_template_id", sa.<PERSON>Integer(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_match_making_email_template_id_email_template"),
        "match_making",
        "email_template",
        ["email_template_id"],
        ["id"],
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_match_making_email_template_id_email_template"),
        "match_making",
        type_="foreignkey",
    )
    op.drop_column("match_making", "email_template_id")
    op.drop_constraint("uq_email_template_uuid", "email_template", type_="unique")
    op.alter_column(
        "email_template",
        "template",
        existing_type=mysql.LONGTEXT(),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    op.drop_column("email_template", "uuid")
    op.drop_column("email_template", "name")
    op.create_table(
        "match_making_email_template",
        sa.Column(
            "id", mysql.BIGINT(display_width=20), autoincrement=True, nullable=False
        ),
        sa.Column("created_at", mysql.DATETIME(), nullable=True),
        sa.Column("updated_at", mysql.DATETIME(), nullable=True),
        sa.Column(
            "match_making_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "email_template_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["email_template_id"],
            ["email_template.id"],
            name="fk_match_making_email_template_email_template_id_email_template",
        ),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name="fk_match_making_email_template_match_making_id_match_making",
        ),
        sa.PrimaryKeyConstraint("id"),
        mariadb_collate="utf8mb4_general_ci",
        mariadb_default_charset="utf8mb4",
        mariadb_engine="InnoDB",
    )
    op.create_index(
        "uq_match_making_email_template",
        "match_making_email_template",
        ["match_making_id", "email_template_id"],
        unique=True,
    )
