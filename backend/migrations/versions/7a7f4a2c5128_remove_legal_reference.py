"""remove legal reference

Revision ID: 7a7f4a2c5128
Revises: 41c3c2451bf0
Create Date: 2024-05-09 17:11:43.815338

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "7a7f4a2c5128"
down_revision = "41c3c2451bf0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "legal_reference")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property",
        sa.Column("legal_reference", mysql.VARCHAR(length=20), nullable=True),
    )
    # ### end Alembic commands ###
