"""Add private_purchase_contract_due_date in Offer table

Revision ID: 8005efe52b6f
Revises: c88a0261bafc
Create Date: 2024-07-04 04:17:23.655858

"""

from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = "8005efe52b6f"
down_revision = "c88a0261bafc"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "offer",
        sa.Column(
            "private_purchase_contract_due_date",
            sa.DateTime(),
            nullable=False,
            server_default=sa.text(f"'{datetime.utcnow()}'"),
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("offer", "private_purchase_contract_due_date")
    # ### end Alembic commands ###
