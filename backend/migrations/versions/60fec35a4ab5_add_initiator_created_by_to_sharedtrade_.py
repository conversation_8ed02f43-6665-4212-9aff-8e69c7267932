"""add initiator, created_by to SharedTrade model

Revision ID: 60fec35a4ab5
Revises: 88e524c67b44
Create Date: 2024-09-02 13:41:19.570026

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '60fec35a4ab5'
down_revision = '88e524c67b44'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dias_shared_trade', sa.Column('initiator_id', sa.BigInteger(), nullable=True))
    op.add_column('dias_shared_trade', sa.Column('created_by_id', sa.BigInteger(), nullable=False))
    op.drop_constraint('fk_dias_shared_trade_draft_created_by_user', 'dias_shared_trade', type_='foreignkey')
    op.create_foreign_key(op.f('fk_dias_shared_trade_created_by_id_user'), 'dias_shared_trade', 'user', ['created_by_id'], ['id'])
    op.create_foreign_key(op.f('fk_dias_shared_trade_initiator_id_user'), 'dias_shared_trade', 'user', ['initiator_id'], ['id'])
    op.drop_column('dias_shared_trade', 'created_by')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dias_shared_trade', sa.Column('created_by', mysql.BIGINT(display_width=20), autoincrement=False, nullable=False))
    op.drop_constraint(op.f('fk_dias_shared_trade_initiator_id_user'), 'dias_shared_trade', type_='foreignkey')
    op.drop_constraint(op.f('fk_dias_shared_trade_created_by_id_user'), 'dias_shared_trade', type_='foreignkey')
    op.create_foreign_key('fk_dias_shared_trade_draft_created_by_user', 'dias_shared_trade', 'user', ['created_by'], ['id'])
    op.drop_column('dias_shared_trade', 'created_by_id')
    op.drop_column('dias_shared_trade', 'initiator_id')
    # ### end Alembic commands ###
