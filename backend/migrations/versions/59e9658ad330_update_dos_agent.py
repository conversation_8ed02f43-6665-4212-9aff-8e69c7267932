"""update_dos_agent

Revision ID: 59e9658ad330
Revises: 83a3896aef87
Create Date: 2024-09-23 10:56:40.615466

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "59e9658ad330"
down_revision = "83a3896aef87"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "fk_realtor_details_of_sale_details_of_sale_invoice_id_de_fda2",
        "realtor_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("realtor_details_of_sale", "details_of_sale_invoice_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "realtor_details_of_sale",
        sa.Column(
            "details_of_sale_invoice_id",
            mysql.BIGINT(display_width=20),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_foreign_key(
        "fk_realtor_details_of_sale_details_of_sale_invoice_id_de_fda2",
        "realtor_details_of_sale",
        "details_of_sale_invoice",
        ["details_of_sale_invoice_id"],
        ["id"],
    )
    # ### end Alembic commands ###
