"""Add has damages to fi realty

Revision ID: b687d359c6c3
Revises: 05fd3e572060
Create Date: 2024-11-11 10:16:51.302249

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b687d359c6c3'
down_revision = '05fd3e572060'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_realty', sa.Column('has_damages', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_realty', 'has_damages')
    # ### end Alembic commands ###
