"""Make organization_id on lead table non-nullable

Revision ID: 2c6dc5a7812a
Revises: 59357dc0446e
Create Date: 2024-06-25 04:45:49.609942

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "2c6dc5a7812a"
down_revision = "59357dc0446e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        op.f("fk_lead_organization_id_organization"), "lead", type_="foreignkey"
    )

    op.alter_column(
        "lead",
        "organization_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=False,
    )

    op.create_foreign_key(
        op.f("fk_lead_organization_id_organization"),
        "lead",
        "organization",
        ["organization_id"],
        ["id"],
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_lead_organization_id_organization"), "lead", type_="foreignkey"
    )

    op.alter_column(
        "lead",
        "organization_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=True,
    )

    op.create_foreign_key(
        op.f("fk_lead_organization_id_organization"),
        "lead",
        "organization",
        ["organization_id"],
        ["id"],
    )
