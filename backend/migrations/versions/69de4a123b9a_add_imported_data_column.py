"""Add imported data column

Revision ID: 69de4a123b9a
Revises: 3de092a4d143
Create Date: 2025-01-06 09:48:01.005675

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "69de4a123b9a"
down_revision = "3de092a4d143"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("fi_property", sa.Column("imported_data", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_property", "imported_data")
    # ### end Alembic commands ###
