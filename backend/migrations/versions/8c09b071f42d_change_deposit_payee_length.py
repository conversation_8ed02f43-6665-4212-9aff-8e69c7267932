"""Change deposit_payee length

Revision ID: 8c09b071f42d
Revises: 467a6fa85a88
Create Date: 2024-07-20 12:48:08.197450

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8c09b071f42d'
down_revision = '467a6fa85a88'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('offer', 'deposit_payee',
               existing_type=mysql.VARCHAR(length=40),
               type_=sa.String(length=255),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('offer', 'deposit_payee',
               existing_type=sa.String(length=255),
               type_=mysql.VARCHAR(length=40),
               existing_nullable=True)
    # ### end Alembic commands ###
