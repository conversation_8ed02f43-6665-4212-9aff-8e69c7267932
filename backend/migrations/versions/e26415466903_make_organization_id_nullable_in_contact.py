"""Make organization_id nullable in contact

Revision ID: e26415466903
Revises: d6dafe198615
Create Date: 2024-06-24 06:55:38.396655

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e26415466903"
down_revision = "d6dafe198615"
branch_labels = None
depends_on = None


def upgrade():
    # Drop the FK constraint so we can modify the field
    op.drop_constraint(
        op.f("fk_contact_organization_id_organization"), "contact", type_="foreignkey"
    )

    op.alter_column(
        "contact",
        "organization_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=False,
    )

    # Re-create the FK constraint
    op.create_foreign_key(
        op.f("fk_contact_organization_id_organization"),
        "contact",
        "organization",
        ["organization_id"],
        ["id"],
    )


def downgrade():
    # Drop the FK constraint so we can modify the field
    op.drop_constraint(
        op.f("fk_contact_organization_id_organization"), "contact", type_="foreignkey"
    )

    op.alter_column(
        "contact",
        "organization_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=True,
    )

    # Re-create the FK constraint
    op.create_foreign_key(
        op.f("fk_contact_organization_id_organization"),
        "contact",
        "organization",
        ["organization_id"],
        ["id"],
    )
