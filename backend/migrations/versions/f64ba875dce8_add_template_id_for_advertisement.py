"""add template id for advertisement

Revision ID: f64ba875dce8
Revises: 6f6ceedb0b15
Create Date: 2025-05-16 16:06:10.062793

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f64ba875dce8'
down_revision = '6f6ceedb0b15'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement', sa.Column('ad_template_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_advertisement_ad_template_id'), 'advertisement', ['ad_template_id'], unique=False)
    op.create_foreign_key(op.f('fk_advertisement_ad_template_id_ad_template'), 'advertisement', 'ad_template', ['ad_template_id'], ['id'])
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_constraint(op.f('fk_advertisement_ad_template_id_ad_template'), 'advertisement', type_='foreignkey')
    op.drop_index(op.f('ix_advertisement_ad_template_id'), table_name='advertisement')
    op.drop_column('advertisement', 'ad_template_id')
    # ### end Alembic commands ###
