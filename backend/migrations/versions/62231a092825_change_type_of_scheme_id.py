"""Change type of scheme id

Revision ID: 62231a092825
Revises: a1209c5dd2be
Create Date: 2024-11-27 22:44:55.577058

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '62231a092825'
down_revision = 'a1209c5dd2be'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'scheme_id',
               existing_type=mysql.VARCHAR(length=10),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_housing_company', 'scheme_id',
               existing_type=mysql.VARCHAR(length=10),
               nullable=False)
    # ### end Alembic commands ###
