"""make review_office_id to Optional for save as daft

Revision ID: 8981f5991581
Revises: 107c1a65ee7c
Create Date: 2024-09-24 11:52:19.744048

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "8981f5991581"
down_revision = "107c1a65ee7c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "details_of_sale",
        "reviewer_office_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=True,
    )
    op.add_column(
        "details_of_sale",
        sa.Column(
            "metadata_json", sa.JSON(), nullable=False, server_default=sa.text("'{}'")
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "details_of_sale",
        "reviewer_office_id",
        existing_type=mysql.BIGINT(display_width=20),
        nullable=False,
    )
    op.drop_column("details_of_sale", "metadata_json")
    # ### end Alembic commands ###
