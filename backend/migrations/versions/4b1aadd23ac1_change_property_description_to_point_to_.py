"""Change property description to point to base property

Revision ID: 4b1aadd23ac1
Revises: 8eb1ac28b03e
Create Date: 2024-07-02 10:46:48.607900

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "4b1aadd23ac1"
down_revision = "7ae7d37ef3aa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "fk_property_description_property_id_property",
        "property_description",
        type_="foreignkey",
    )
    op.create_foreign_key(
        op.f("fk_property_description_property_id_property"),
        "property_description",
        "property",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_property_description_property_id_property"),
        "property_description",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "fk_property_description_property_id_property",
        "property_description",
        "property_spain",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###
