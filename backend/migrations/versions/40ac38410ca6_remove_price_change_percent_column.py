"""Remove price_change_percent column

Revision ID: 40ac38410ca6
Revises: 00ea6c87790a
Create Date: 2024-01-03 10:24:12.375677

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "40ac38410ca6"
down_revision = "00ea6c87790a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "price_change_percent")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property", sa.Column("price_change_percent", mysql.FLOAT(), nullable=True)
    )
    # ### end Alembic commands ###
