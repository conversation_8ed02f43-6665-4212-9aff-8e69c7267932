"""Change foreign key for file table

Revision ID: 28013ffb2466
Revises: 25468920e77b
Create Date: 2024-12-18 11:11:39.496326

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "28013ffb2466"
down_revision = "25468920e77b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_file_property_id_property", "file", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_file_property_id_property"),
        "file",
        "property",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("fk_file_property_id_property"), "file", type_="foreignkey")
    op.create_foreign_key(
        "fk_file_property_id_property",
        "file",
        "property_spain",
        ["property_id"],
        ["id"],
    )
    # ### end Alembic commands ###
