"""change encode type for fullname field

Revision ID: ba0c15e85630
Revises: 5252c61b9b2a
Create Date: 2024-08-20 17:21:51.156081

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'ba0c15e85630'
down_revision = '5252c61b9b2a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('raw_lead_data', 'full_name',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_bin', length=100),
               type_=sa.String(length=200, collation='utf8mb4_bin'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('raw_lead_data', 'full_name',
               existing_type=sa.String(length=200, collation='utf8mb4_bin'),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_bin', length=100),
               existing_nullable=True)
    # ### end Alembic commands ###
