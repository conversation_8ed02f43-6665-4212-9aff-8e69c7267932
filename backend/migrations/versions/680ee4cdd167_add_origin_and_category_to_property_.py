"""Add origin and category to property_type db

Revision ID: 680ee4cdd167
Revises: 477ce79eb319
Create Date: 2023-12-22 11:38:23.519985

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '680ee4cdd167'
down_revision = '477ce79eb319'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_type', sa.Column('origin', sa.String(length=100), nullable=False))
    op.add_column('property_type', sa.Column('category', sa.String(length=100), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_type', 'category')
    op.drop_column('property_type', 'origin')
    # ### end Alembic commands ###
