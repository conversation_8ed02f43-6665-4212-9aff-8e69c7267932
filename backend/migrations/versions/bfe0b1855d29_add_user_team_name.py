"""add_user_team_name

Revision ID: bfe0b1855d29
Revises: b6a3d083cca2
Create Date: 2024-08-24 00:04:01.976826

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "bfe0b1855d29"
down_revision = "b6a3d083cca2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("team_name", sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "team_name")
    # ### end Alembic commands ###
