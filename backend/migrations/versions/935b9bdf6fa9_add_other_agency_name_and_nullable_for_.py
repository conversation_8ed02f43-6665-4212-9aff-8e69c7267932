"""Add other agency name and nullable for email and phone

Revision ID: 935b9bdf6fa9
Revises: 3326cf57e7bd
Create Date: 2025-06-11 15:41:33.130293

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "935b9bdf6fa9"
down_revision = "3326cf57e7bd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "details_of_sale",
        sa.Column("other_agency_name", sa.String(length=50), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("details_of_sale", "other_agency_name")
    # ### end Alembic commands ###
