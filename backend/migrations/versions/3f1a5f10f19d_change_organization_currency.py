"""Change organization currency to uppercase

Revision ID: 3f1a5f10f19d
Revises: 179ebb631750
Create Date: 2024-08-29 00:59:03.032336

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "3f1a5f10f19d"
down_revision = "179ebb631750"
branch_labels = None
depends_on = None


def upgrade():
    # Convert all 'currency' values to uppercase
    op.execute("UPDATE organization SET currency = UPPER(currency);")


def downgrade():
    # Convert all 'currency' values to lowercase as before
    op.execute("UPDATE organization SET currency = LOWER(currency);")
