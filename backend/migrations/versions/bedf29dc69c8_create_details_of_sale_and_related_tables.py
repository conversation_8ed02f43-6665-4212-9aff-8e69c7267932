"""create details_of_sale and related tables 

Revision ID: bedf29dc69c8
Revises: b06749fa8ecf
Create Date: 2024-07-10 08:19:42.290950

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bedf29dc69c8"
down_revision = "b06749fa8ecf"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "details_of_sale",
        sa.Column("property_id", sa.BigInteger(), nullable=True),
        sa.Column("custom_reference_property", sa.String(length=50), nullable=True),
        sa.Column("offer_agreed_date", sa.DateTime(), nullable=True),
        sa.Column("sale_price", sa.Numeric(precision=14, scale=2), nullable=True),
        sa.Column("deposit_percentage", sa.Float(), nullable=True),
        sa.Column("deposit_amount", sa.Numeric(precision=14, scale=2), nullable=True),
        sa.Column("deposit_account_type", sa.String(length=50), nullable=True),
        sa.Column("deposit_paid_date", sa.DateTime(), nullable=True),
        sa.Column("ppc_date", sa.DateTime(), nullable=True),
        sa.Column("completion_notary_deadline", sa.DateTime(), nullable=True),
        sa.Column("notary_day_booked", sa.DateTime(), nullable=True),
        sa.Column(
            "total_commission_amount", sa.Numeric(precision=14, scale=2), nullable=True
        ),
        sa.Column("total_commission_type", sa.String(length=50), nullable=True),
        sa.Column(
            "strand_commission_amount", sa.Numeric(precision=14, scale=2), nullable=True
        ),
        sa.Column("strand_commission_type", sa.String(length=50), nullable=True),
        sa.Column(
            "other_agency_commission_amount",
            sa.Numeric(precision=14, scale=2),
            nullable=True,
        ),
        sa.Column("other_agency_commission_type", sa.String(length=50), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("external_lead", sa.String(length=50), nullable=True),
        sa.Column("external_lead_percentage", sa.Float(), nullable=True),
        sa.Column(
            "separate_invoice_for_each_seller",
            sa.Boolean(),
            server_default=sa.text("true"),
            nullable=False,
        ),
        sa.Column("document_id", sa.BigInteger(), nullable=False),
        sa.Column("reviewer_office_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_by", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by"], ["user.id"], name=op.f("fk_details_of_sale_created_by_user")
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_details_of_sale_property_id_property"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["reviewer_office_id"],
            ["office.id"],
            name=op.f("fk_details_of_sale_reviewer_office_id_office"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["document.id"],
            name=op.f("fk_details_of_sale_document_id_document"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_details_of_sale")),
    )
    op.create_table(
        "realtor_details_of_sale",
        sa.Column("details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("user_id", sa.BigInteger(), nullable=True),
        sa.Column(
            "commission_amount", sa.Numeric(precision=14, scale=2), nullable=True
        ),
        sa.Column("commission_percentage", sa.Float(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["details_of_sale_id"],
            ["details_of_sale.id"],
            name=op.f("fk_realtor_details_of_sale_details_of_sale_id_details_of_sale"),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
            name=op.f("fk_realtor_details_of_sale_user_id_user"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_realtor_details_of_sale")),
    )
    op.create_index(
        op.f("ix_realtor_details_of_sale_details_of_sale_id"),
        "realtor_details_of_sale",
        ["details_of_sale_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_realtor_details_of_sale_user_id"),
        "realtor_details_of_sale",
        ["user_id"],
        unique=False,
    )
    op.create_table(
        "buyer_details_of_sale",
        sa.Column("details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("buyer_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["buyer_id"],
            ["contact.id"],
            name=op.f("fk_buyer_details_of_sale_buyer_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["details_of_sale_id"],
            ["details_of_sale.id"],
            name=op.f("fk_buyer_details_of_sale_details_of_sale_id_details_of_sale"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_buyer_details_of_sale")),
        sa.UniqueConstraint(
            "details_of_sale_id",
            "buyer_id",
            name=op.f("uq_buyer_details_of_sale_details_of_sale_id"),
        ),
    )
    op.create_index(
        op.f("ix_buyer_details_of_sale_buyer_id"),
        "buyer_details_of_sale",
        ["buyer_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_buyer_details_of_sale_details_of_sale_id"),
        "buyer_details_of_sale",
        ["details_of_sale_id"],
        unique=False,
    )
    op.create_table(
        "seller_details_of_sale",
        sa.Column("details_of_sale_id", sa.BigInteger(), nullable=True),
        sa.Column("seller_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("invoice_percentage", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["details_of_sale_id"],
            ["details_of_sale.id"],
            name=op.f("fk_seller_details_of_sale_details_of_sale_id_details_of_sale"),
        ),
        sa.ForeignKeyConstraint(
            ["seller_id"],
            ["contact.id"],
            name=op.f("fk_seller_details_of_sale_seller_id_contact"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_seller_details_of_sale")),
        sa.UniqueConstraint(
            "details_of_sale_id",
            "seller_id",
            name=op.f("uq_seller_details_of_sale_details_of_sale_id"),
        ),
    )
    op.create_index(
        op.f("ix_seller_details_of_sale_details_of_sale_id"),
        "seller_details_of_sale",
        ["details_of_sale_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_seller_details_of_sale_seller_id"),
        "seller_details_of_sale",
        ["seller_id"],
        unique=False,
    )
    op.create_table(
        "document_attachments",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("type", sa.Text(), nullable=False),
        sa.Column("sowise_id", sa.Text(), nullable=False),
        sa.Column("document_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["document.id"],
            name=op.f("fk_document_attachments_document_id_document"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_document_attachments")),
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_table("seller_details_of_sale")
    op.drop_table("buyer_details_of_sale")
    op.drop_table("realtor_details_of_sale")
    op.drop_table("details_of_sale")
    op.drop_table("document_attachments")
    # ### end Alembic commands ###
