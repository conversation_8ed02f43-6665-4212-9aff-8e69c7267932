"""Rename table fi_location to fi_area

Revision ID: e8f238868208
Revises: de961802a5ed
Create Date: 2024-08-28 09:24:43.369345

"""

from alembic import op
from strandproperties.utils.alembic import alembic_mysql_util as op_util
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e8f238868208"
down_revision = "de961802a5ed"
branch_labels = None
depends_on = None


def upgrade():
    # Rename table from fi_location to fi_area
    op.rename_table("fi_location", "fi_area")

    # Rename columns in the fi_area table and ensure NOT NULL constraints are maintained
    op.alter_column(
        "fi_area",
        "location_code",
        new_column_name="area_code",
        existing_type=sa.String(20),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_area",
        "location_name_en",
        new_column_name="area_name_en",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_area",
        "location_name_fi",
        new_column_name="area_name_fi",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_area",
        "location_name_sv",
        new_column_name="area_name_sv",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )

    # Drop the old unique constraint and create a new one with the new name
    op.drop_constraint("uq_location_code_parent_id", "fi_area", type_="unique")
    op.create_unique_constraint(
        "uq_area_code_parent_id", "fi_area", ["area_code", "parent_id"]
    )

    # Rename index for parent_id using rename_index utility
    op_util.rename_index("fi_area", "ix_fi_location_parent_id", "ix_fi_area_parent_id")

    # Rename column and foreign key in fi_address table, ensuring NOT NULL constraint is preserved
    op.alter_column(
        "fi_address",
        "fi_location_id",
        new_column_name="area_id",
        existing_type=sa.BigInteger(),
        nullable=False,  # Ensure NOT NULL is preserved
    )

    # Drop the old foreign key constraint and create a new one with the new name
    op.drop_constraint(
        "fk_fi_address_fi_location_id_fi_location", "fi_address", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_fi_address_area_id_fi_area", "fi_address", "fi_area", ["area_id"], ["id"]
    )

    # Rename index for area_id in fi_address table
    op_util.rename_index(
        "fi_address", "ix_fi_address_fi_location_id", "ix_fi_address_area_id"
    )


def downgrade():
    # Rename table from fi_area back to fi_location
    op.rename_table("fi_area", "fi_location")

    # Rename columns in the fi_location table back to original names, ensuring NOT NULL constraint is preserved
    op.alter_column(
        "fi_location",
        "area_code",
        new_column_name="location_code",
        existing_type=sa.String(20),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_location",
        "area_name_en",
        new_column_name="location_name_en",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_location",
        "area_name_fi",
        new_column_name="location_name_fi",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )
    op.alter_column(
        "fi_location",
        "area_name_sv",
        new_column_name="location_name_sv",
        existing_type=sa.String(50),
        nullable=False,  # Ensure NOT NULL is preserved
    )

    # Drop the new unique constraint and recreate the old one
    op.drop_constraint("uq_area_code_parent_id", "fi_location", type_="unique")
    op.create_unique_constraint(
        "uq_location_code_parent_id", "fi_location", ["location_code", "parent_id"]
    )

    # Rename index for parent_id back to original name using rename_index utility
    op_util.rename_index(
        "fi_location", "ix_fi_area_parent_id", "ix_fi_location_parent_id"
    )

    # Rename column and foreign key in fi_address table back to original names, ensuring NOT NULL constraint is preserved
    op.alter_column(
        "fi_address",
        "area_id",
        new_column_name="fi_location_id",
        existing_type=sa.BigInteger(),
        nullable=False,  # Ensure NOT NULL is preserved
    )

    # Drop the new foreign key constraint and recreate the old one
    op.drop_constraint(
        "fk_fi_address_area_id_fi_area", "fi_address", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_fi_address_fi_location_id_fi_location",
        "fi_address",
        "fi_location",
        ["fi_location_id"],
        ["id"],
    )

    # Rename index for fi_location_id in fi_address table back to original name
    op_util.rename_index(
        "fi_address", "ix_fi_address_area_id", "ix_fi_address_fi_location_id"
    )
