"""Move commission fields from fi_realty to fi_property

Revision ID: 81e57a0b9728
Revises: 0712a971ec1e
Create Date: 2024-09-06 00:56:09.037064

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "81e57a0b9728"
down_revision = "0712a971ec1e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("fi_property", sa.Column("commission", sa.Float(), nullable=True))
    op.add_column(
        "fi_property", sa.Column("commission_note", sa.Text(length=200), nullable=True)
    )
    op.add_column(
        "fi_property", sa.Column("commission_type", sa.String(length=20), nullable=True)
    )
    op.drop_column("fi_realty", "commission")
    op.drop_column("fi_realty", "commission_type")
    op.drop_column("fi_realty", "commission_notes")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_realty", sa.Column("commission_notes", mysql.TEXT(), nullable=True)
    )
    op.add_column(
        "fi_realty",
        sa.Column("commission_type", mysql.VARCHAR(length=20), nullable=True),
    )
    op.add_column("fi_realty", sa.Column("commission", mysql.FLOAT(), nullable=True))
    op.drop_column("fi_property", "commission_type")
    op.drop_column("fi_property", "commission_note")
    op.drop_column("fi_property", "commission")
    # ### end Alembic commands ###
