"""add details of sale expenses

Revision ID: 0bcd12159684
Revises: 6eabcb53945e
Create Date: 2025-08-11 08:27:15.916811

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "0bcd12159684"
down_revision = "6eabcb53945e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("fi_details_of_sale", sa.Column("expenses", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_details_of_sale", "expenses")
    # ### end Alembic commands ###
