"""new field hostaway_property_id to ES Property

Revision ID: d12c7edc034d
Revises: b0b2fff610d1
Create Date: 2025-08-19 14:15:16.462763

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd12c7edc034d'
down_revision = 'b0b2fff610d1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_spain', sa.Column('hostaway_property_id', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_spain', 'hostaway_property_id')
    # ### end Alembic commands ###
