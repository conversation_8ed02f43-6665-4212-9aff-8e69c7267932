"""add Dias Attachment

Revision ID: f069c36c17d7
Revises: 549f748875d0
Create Date: 2024-09-26 16:08:19.223965

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f069c36c17d7'
down_revision = '549f748875d0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dias_attachment',
    sa.<PERSON>umn('dias_field', sa.String(length=200), nullable=False),
    sa.Column('dias_id', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('business_id', sa.String(length=100), nullable=True),
    sa.Column('document_type', sa.String(length=200), nullable=True),
    sa.Column('id', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_dias_attachment'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('dias_attachment')
    # ### end Alembic commands ###
