"""group language to optional

Revision ID: 142b1df19012
Revises: 87680345c315
Create Date: 2024-09-13 10:18:53.868621

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "142b1df19012"
down_revision = "87680345c315"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "group", "language", existing_type=mysql.VARCHAR(length=30), nullable=True
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "group", "language", existing_type=mysql.VARCHAR(length=30), nullable=False
    )
    # ### end Alembic commands ###
