"""modify SharedTrade model

Revision ID: 53d75666f9f2
Revises: e3aa208447a8
Create Date: 2024-08-30 16:42:11.955071

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "53d75666f9f2"
down_revision = "e3aa208447a8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade_draft",
        sa.Column("trade_id", sa.String(length=50), nullable=True),
    )
    op.add_column(
        "dias_shared_trade_draft", sa.Column("trade_phase", sa.JSON(), nullable=True)
    )
    op.add_column(
        "dias_shared_trade_draft",
        sa.Column("property_id", sa.BigInteger(), nullable=True),
    )
    op.add_column(
        "dias_shared_trade_draft",
        sa.<PERSON>umn("created_by", sa.<PERSON>(), nullable=False),
    )
    op.create_foreign_key(
        op.f("fk_dias_shared_trade_draft_created_by_user"),
        "dias_shared_trade_draft",
        "user",
        ["created_by"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_dias_shared_trade_draft_property_id_property"),
        "dias_shared_trade_draft",
        "property",
        ["property_id"],
        ["id"],
        ondelete="SET NULL",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_dias_shared_trade_draft_property_id_property"),
        "dias_shared_trade_draft",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_dias_shared_trade_draft_created_by_user"),
        "dias_shared_trade_draft",
        type_="foreignkey",
    )
    op.drop_column("dias_shared_trade_draft", "created_by")
    op.drop_column("dias_shared_trade_draft", "property_id")
    op.drop_column("dias_shared_trade_draft", "trade_phase")
    op.drop_column("dias_shared_trade_draft", "trade_id")
    # ### end Alembic commands ###
