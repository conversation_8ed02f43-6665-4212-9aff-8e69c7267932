"""add offer_id to details_of_sale table

Revision ID: 3d796cd4a843
Revises: d028da01094e
Create Date: 2025-05-26 14:17:22.571600

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "3d796cd4a843"
down_revision = "d028da01094e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "details_of_sale", sa.Column("offer_id", sa.BigInteger(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_details_of_sale_offer_id_offer"),
        "details_of_sale",
        "offer",
        ["offer_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_details_of_sale_offer_id_offer"), "details_of_sale", type_="foreignkey"
    )
    op.drop_column("details_of_sale", "offer_id")
    # ### end Alembic commands ###
