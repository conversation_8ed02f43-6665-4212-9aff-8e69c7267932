"""Add status for transactions

Revision ID: c1c6eccdbe9c
Revises: 1746bf380eab
Create Date: 2025-04-01 21:24:09.413159

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c1c6eccdbe9c'
down_revision = '1746bf380eab'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column('transactions', sa.Column('status', sa.String(length=20), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('transactions', 'status')
    # ### end Alembic commands ###
