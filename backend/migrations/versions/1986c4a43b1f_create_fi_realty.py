"""Create fi_realty

Revision ID: 1986c4a43b1f
Revises: 4b11a78a623d
Create Date: 2024-07-18 04:10:03.462806

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "1986c4a43b1f"
down_revision = "4b11a78a623d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_address",
        sa.Column("street_address", sa.String(length=200), nullable=False),
        sa.Column("stairwell", sa.String(length=100), nullable=True),
        sa.Column("apartment_number", sa.String(length=100), nullable=True),
        sa.Column("post_code", sa.String(length=200), nullable=False),
        sa.Column("postal_area", sa.String(length=200), nullable=False),
        sa.Column("locality", sa.String(length=100), nullable=False),
        sa.Column("locality_code", sa.String(length=100), nullable=True),
        sa.Column("location", sa.JSON(), nullable=True),
        sa.Column("show_map_on_announcement", sa.String(length=50), nullable=True),
        sa.Column("fi_location_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_location_id"],
            ["fi_location.id"],
            name=op.f("fk_fi_address_fi_location_id_fi_location"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_address")),
    )
    op.create_index(
        op.f("ix_fi_address_fi_location_id"),
        "fi_address",
        ["fi_location_id"],
        unique=False,
    )
    op.create_table(
        "fi_agent",
        sa.Column("role_code", sa.String(length=50), nullable=False),
        sa.Column("status_code", sa.String(length=50), nullable=False),
        sa.Column("realtor_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["realtor_id"], ["user.id"], name=op.f("fk_fi_agent_realtor_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_agent")),
    )
    op.create_index(
        op.f("ix_fi_agent_realtor_id"), "fi_agent", ["realtor_id"], unique=False
    )
    op.create_table(
        "fi_realty",
        sa.Column("new_building", sa.String(length=50), nullable=True),
        sa.Column("consent_to_change", sa.JSON(), nullable=True),
        sa.Column("costs", sa.JSON(), nullable=True),
        sa.Column("costs_description", sa.JSON(), nullable=True),
        sa.Column("monthly_rent", sa.Integer(), nullable=True),
        sa.Column("auction_allowed", sa.Boolean(), nullable=True),
        sa.Column("starting_price_amount", sa.Integer(), nullable=True),
        sa.Column("selling_price", sa.Integer(), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("availability", sa.JSON(), nullable=True),
        sa.Column("agency_office_id", sa.String(length=50), nullable=False),
        sa.Column("is_rented", sa.String(length=50), nullable=True),
        sa.Column("fi_address_id", sa.BigInteger(), nullable=True),
        sa.Column("notify_if_price_changed", sa.String(length=50), nullable=True),
        sa.Column("title", sa.JSON(), nullable=True),
        sa.Column("transaction_description", sa.JSON(), nullable=True),
        sa.Column("transaction_does_not_include", sa.JSON(), nullable=True),
        sa.Column("transaction_includes", sa.JSON(), nullable=True),
        sa.Column("condition", sa.JSON(), nullable=True),
        sa.Column("additional_area_measurement_information", sa.JSON(), nullable=True),
        sa.Column("additional_information", sa.JSON(), nullable=True),
        sa.Column("supplier_assigned_identifiers", sa.JSON(), nullable=True),
        sa.Column("damages", sa.JSON(), nullable=True),
        sa.Column("living_comfort_factors", sa.JSON(), nullable=True),
        sa.Column("key_management", sa.JSON(), nullable=True),
        sa.Column("lease_details", sa.JSON(), nullable=True),
        sa.Column("living_form_type_code", sa.String(length=50), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["fi_address_id"],
            ["fi_address.id"],
            name=op.f("fk_fi_realty_fi_address_id_fi_address"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_realty")),
    )
    op.create_index(
        op.f("ix_fi_realty_fi_address_id"), "fi_realty", ["fi_address_id"], unique=False
    )
    op.create_table(
        "fi_realty_agent",
        sa.Column("realty_id", sa.BigInteger(), nullable=False),
        sa.Column("agent_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["agent_id"],
            ["fi_agent.id"],
            name=op.f("fk_fi_realty_agent_agent_id_fi_agent"),
        ),
        sa.ForeignKeyConstraint(
            ["realty_id"],
            ["fi_realty.id"],
            name=op.f("fk_fi_realty_agent_realty_id_fi_realty"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_realty_agent")),
        sa.UniqueConstraint("realty_id", "agent_id", name="uq_fi_realty_agent"),
    )
    op.create_index(
        op.f("ix_fi_realty_agent_agent_id"),
        "fi_realty_agent",
        ["agent_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_fi_realty_agent_realty_id"),
        "fi_realty_agent",
        ["realty_id"],
        unique=False,
    )


def downgrade():
    op.drop_table("fi_realty_agent")

    op.drop_constraint(
        op.f("fk_fi_realty_fi_address_id_fi_address"),
        "fi_realty",
        type_="foreignkey",
    )
    op.drop_index(op.f("ix_fi_realty_fi_address_id"), table_name="fi_realty")
    op.drop_table("fi_realty")

    op.drop_constraint(
        op.f("fk_fi_agent_realtor_id_user"),
        "fi_agent",
        type_="foreignkey",
    )
    op.drop_index(op.f("ix_fi_agent_realtor_id"), table_name="fi_agent")
    op.drop_table("fi_agent")

    op.drop_constraint(
        op.f("fk_fi_address_fi_location_id_fi_location"),
        "fi_address",
        type_="foreignkey",
    )
    op.drop_index(op.f("ix_fi_address_fi_location_id"), table_name="fi_address")
    op.drop_table("fi_address")
