"""Email marketing consent tables

Revision ID: 48e1f3f44788
Revises: 65fdea36febf
Create Date: 2025-05-22 21:35:41.277553

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '48e1f3f44788'
down_revision = '65fdea36febf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contact_marketing_consent_status',
    sa.<PERSON>umn('contact_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('newsletter_type', sa.String(length=64), nullable=False),
    sa.Column('is_subscribed', sa.<PERSON>(), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.<PERSON>umn('id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contact.id'], name=op.f('fk_contact_marketing_consent_status_contact_id_contact'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_contact_marketing_consent_status')),
    sa.UniqueConstraint('contact_id', 'newsletter_type', name='uq_contact_newsletter')
    )
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('contact_marketing_consent_status')
    # ### end Alembic commands ###
