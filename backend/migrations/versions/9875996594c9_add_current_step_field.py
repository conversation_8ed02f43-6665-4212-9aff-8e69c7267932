"""add current_step field

Revision ID: 9875996594c9
Revises: 72ce6c3bc9d9
Create Date: 2024-11-19 09:33:36.901320

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "9875996594c9"
down_revision = "72ce6c3bc9d9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade", sa.Column("current_step", sa.Integer(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("dias_shared_trade", "current_step")
    # ### end Alembic commands ###
