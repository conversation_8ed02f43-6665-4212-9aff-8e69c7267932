"""Add province to area_level

Revision ID: 7cc0b2769b8e
Revises: 471f7fbd163a
Create Date: 2023-11-06 13:59:41.334378

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7cc0b2769b8e'
down_revision = '471f7fbd163a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('area_level_1', sa.Column('province', sa.Text(), nullable=False))
    op.alter_column('contact', 'first_name',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('contact', 'last_name',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('contact', 'email',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('contact', 'phone_number',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Text(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contact', 'phone_number',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('contact', 'email',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=False)
    op.alter_column('contact', 'last_name',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=False)
    op.alter_column('contact', 'first_name',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=False)
    op.drop_column('area_level_1', 'province')
    # ### end Alembic commands ###
