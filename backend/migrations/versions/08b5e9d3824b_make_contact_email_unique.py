"""make contact email unique

Revision ID: 08b5e9d3824b
Revises: 453e13314caf
Create Date: 2023-10-03 09:52:21.362043

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "08b5e9d3824b"
down_revision = "453e13314caf"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(op.f("uq_contact_email"), "contact", ["email"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("uq_contact_email"), "contact", type_="unique")
    # ### end Alembic commands ###
