"""property edit renewal

Revision ID: 477ce79eb319
Revises: 42cc88eb4c8b
Create Date: 2023-12-20 11:13:39.114590

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '477ce79eb319'
down_revision = '42cc88eb4c8b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property', sa.Column('communal_fees', sa.Float(), nullable=True))
    op.add_column('property', sa.Column('ibi', sa.Float(), nullable=True))
    op.add_column('property', sa.Column('garbage_tax', sa.Float(), nullable=True))
    op.add_column('property', sa.Column('water_fee', sa.Float(), nullable=True))
    op.add_column('property', sa.Column('electricity', sa.Float(), nullable=True))
    op.add_column('property', sa.Column('cadastral_reference', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('legal_reference', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('building_constructor', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('total_floors', sa.Integer(), nullable=True))
    op.add_column('property', sa.Column('building_has_elevator', sa.Boolean(), nullable=True))
    op.add_column('property', sa.Column('foundation_and_structure', sa.String(length=50), nullable=True))
    op.add_column('property', sa.Column('roof', sa.String(length=50), nullable=True))
    op.add_column('property', sa.Column('exterior_walls', sa.String(length=50), nullable=True))
    op.add_column('property', sa.Column('property_has_certificate', sa.Boolean(), nullable=True))
    op.add_column('property', sa.Column('certificate_type', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('rooms_total', sa.Integer(), nullable=True))
    op.add_column('property', sa.Column('toilets', sa.Integer(), nullable=True))
    op.add_column('property', sa.Column('suite_baths', sa.Integer(), nullable=True))
    op.add_column('property', sa.Column('garage_type', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('parking_spaces', sa.Integer(), nullable=True))
    op.add_column('property', sa.Column('pool_type', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('garden_type', sa.String(length=20), nullable=True))
    op.add_column('property', sa.Column('telecommunication_systems', sa.JSON(), nullable=True))
    op.add_column('property', sa.Column('keys_and_handoff', sa.JSON(), nullable=True))
    op.add_column('property', sa.Column('renovations', sa.JSON(), nullable=True))
    op.add_column('property', sa.Column('damages_and_defects', sa.JSON(), nullable=True))
    op.add_column('property', sa.Column('other_damages', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property', 'other_damages')
    op.drop_column('property', 'damages_and_defects')
    op.drop_column('property', 'renovations')
    op.drop_column('property', 'keys_and_handoff')
    op.drop_column('property', 'telecommunication_systems')
    op.drop_column('property', 'garden_type')
    op.drop_column('property', 'pool_type')
    op.drop_column('property', 'parking_spaces')
    op.drop_column('property', 'garage_type')
    op.drop_column('property', 'suite_baths')
    op.drop_column('property', 'toilets')
    op.drop_column('property', 'rooms_total')
    op.drop_column('property', 'certificate_type')
    op.drop_column('property', 'property_has_certificate')
    op.drop_column('property', 'exterior_walls')
    op.drop_column('property', 'roof')
    op.drop_column('property', 'foundation_and_structure')
    op.drop_column('property', 'building_has_elevator')
    op.drop_column('property', 'total_floors')
    op.drop_column('property', 'building_constructor')
    op.drop_column('property', 'legal_reference')
    op.drop_column('property', 'cadastral_reference')
    op.drop_column('property', 'electricity')
    op.drop_column('property', 'water_fee')
    op.drop_column('property', 'garbage_tax')
    op.drop_column('property', 'ibi')
    op.drop_column('property', 'communal_fees')
    # ### end Alembic commands ###
