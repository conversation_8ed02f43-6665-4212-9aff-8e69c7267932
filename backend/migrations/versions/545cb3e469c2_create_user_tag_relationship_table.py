"""create user_tag relationship table

Revision ID: 545cb3e469c2
Revises: 17cfdc6d3794
Create Date: 2024-01-19 13:32:21.612376

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '545cb3e469c2'
down_revision = '17cfdc6d3794'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_tag',
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('tag_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], name=op.f('fk_user_tag_tag_id_tag')),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name=op.f('fk_user_tag_user_id_user')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_tag')),
    sa.UniqueConstraint('user_id', 'tag_id', name='uq_user_tag')
    )
    op.create_index('ix_user_tag_tag', 'user_tag', ['tag_id'], unique=False)
    op.create_index('ix_user_tag_user', 'user_tag', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_user_tag_user', table_name='user_tag')
    op.drop_index('ix_user_tag_tag', table_name='user_tag')
    op.drop_table('user_tag')
    # ### end Alembic commands ###
