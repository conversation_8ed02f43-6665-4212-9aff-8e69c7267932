"""document signing support multiple documents

Revision ID: 55c67c64f64e
Revises: d15e30c42795
Create Date: 2025-06-03 09:21:42.616620

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "55c67c64f64e"
down_revision = "d15e30c42795"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_signing_entity",
        sa.<PERSON>umn("document_signing_id", sa.BigInteger(), nullable=False),
        sa.Column("entity_id", sa.Integer(), nullable=False),
        sa.Column("entity_type", sa.String(length=50), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_signing_id"],
            ["document_signing.id"],
            name=op.f(
                "fk_document_signing_entity_document_signing_id_document_signing"
            ),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_document_signing_entity")),
    )
    op.execute(
        """
        INSERT INTO document_signing_entity (document_signing_id, entity_id, entity_type, created_at, updated_at)
        SELECT id, entity_id, entity_type, created_at, updated_at
        FROM document_signing
        """
    )
    op.drop_column("document_signing", "entity_id")
    op.drop_column("document_signing", "entity_type")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_signing",
        sa.Column("entity_type", mysql.VARCHAR(length=50), nullable=False),
    )
    op.add_column(
        "document_signing",
        sa.Column(
            "entity_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
    )
    # This is pretty optimistic since there could be multiple and we would just map one back...
    op.execute(
        """
        UPDATE document_signing
        SET entity_id = (
            SELECT entity_id
            FROM document_signing_entity
            WHERE document_signing_entity.document_signing_id = document_signing.id
            LIMIT 1
        ),
        entity_type = (
            SELECT entity_type
            FROM document_signing_entity
            WHERE document_signing_entity.document_signing_id = document_signing.id
            LIMIT 1
        )
        """
    )
    op.drop_table("document_signing_entity")
    # ### end Alembic commands ###
