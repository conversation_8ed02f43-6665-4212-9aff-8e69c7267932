"""Add source field to contact

Revision ID: 26e67b0c44e9
Revises: 08b5e9d3824b
Create Date: 2023-10-19 19:27:27.454734

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "26e67b0c44e9"
down_revision = "08b5e9d3824b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact", sa.Column("source", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("contact", "source")
    # ### end Alembic commands ###
