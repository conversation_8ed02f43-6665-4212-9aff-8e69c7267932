"""contact support multiple phones

Revision ID: 9ef6835ffed8
Revises: 680ee4cdd167
Create Date: 2024-01-08 11:55:43.108710

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "9ef6835ffed8"
down_revision = "680ee4cdd167"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "contact_phone",
        sa.Column("phone_number", sa.Text(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_contact_phone_contact_id_contact"),
        ),
        sa.<PERSON>KeyConstraint("id", name=op.f("pk_contact_phone")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("phone")
    # ### end Alembic commands ###
