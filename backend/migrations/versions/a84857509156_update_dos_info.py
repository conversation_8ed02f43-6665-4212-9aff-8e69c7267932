"""update_dos_info

Revision ID: a84857509156
Revises: 60fec35a4ab5
Create Date: 2024-09-09 17:59:21.769217

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "a84857509156"
down_revision = "60fec35a4ab5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "realtor_details_of_sale",
        sa.Column("document_id", sa.BigInteger(), nullable=True),
    )
    op.create_foreign_key(
        op.f("fk_realtor_details_of_sale_document_id_document"),
        "realtor_details_of_sale",
        "document",
        ["document_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_realtor_details_of_sale_document_id_document"),
        "realtor_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("realtor_details_of_sale", "document_id")
    # ### end Alembic commands ###
