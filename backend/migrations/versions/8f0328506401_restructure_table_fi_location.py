"""Restructure table fi_location

Revision ID: 8f0328506401
Revises: 8f1d1c24c1b0
Create Date: 2024-08-02 17:58:18.684550

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8f0328506401"
down_revision = "8f1d1c24c1b0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_location", sa.Column("location_code", sa.String(length=20), nullable=False)
    )
    op.add_column(
        "fi_location",
        sa.Column("location_name_en", sa.String(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("location_name_fi", sa.String(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("location_name_sv", sa.String(length=50), nullable=False),
    )
    op.add_column("fi_location", sa.Column("parent_id", sa.<PERSON>ger(), nullable=True))
    op.create_index(
        op.f("ix_fi_location_parent_id"), "fi_location", ["parent_id"], unique=False
    )
    op.create_unique_constraint(
        "uq_location_code_parent_id", "fi_location", ["location_code", "parent_id"]
    )
    op.create_foreign_key(
        op.f("fk_fi_location_parent_id_fi_location"),
        "fi_location",
        "fi_location",
        ["parent_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_column("fi_location", "district_code")
    op.drop_column("fi_location", "district_name_en")
    op.drop_column("fi_location", "district_name_fi")
    op.drop_column("fi_location", "district_name_sv")
    op.drop_column("fi_location", "major_region_code")
    op.drop_column("fi_location", "major_region_name_en")
    op.drop_column("fi_location", "major_region_name_fi")
    op.drop_column("fi_location", "major_region_name_sv")
    op.drop_column("fi_location", "municipality_code")
    op.drop_column("fi_location", "municipality_name_en")
    op.drop_column("fi_location", "municipality_name_fi")
    op.drop_column("fi_location", "municipality_name_sv")
    op.drop_column("fi_location", "region_code")
    op.drop_column("fi_location", "region_name_en")
    op.drop_column("fi_location", "region_name_fi")
    op.drop_column("fi_location", "region_name_sv")
    op.drop_column("fi_location", "subregion_code")
    op.drop_column("fi_location", "subregion_name_en")
    op.drop_column("fi_location", "subregion_name_fi")
    op.drop_column("fi_location", "subregion_name_sv")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_location",
        sa.Column("district_code", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("district_name_en", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("district_name_fi", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("district_name_sv", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("major_region_code", sa.Text(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("major_region_name_en", sa.Text(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("major_region_name_fi", sa.Text(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("major_region_name_sv", sa.Text(length=50), nullable=False),
    )
    op.add_column(
        "fi_location",
        sa.Column("municipality_code", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("municipality_name_en", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("municipality_name_fi", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("municipality_name_sv", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location", sa.Column("region_code", sa.Text(length=50), nullable=True)
    )
    op.add_column(
        "fi_location",
        sa.Column("region_name_en", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("region_name_fi", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("region_name_sv", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("subregion_code", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("subregion_name_en", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("subregion_name_fi", sa.Text(length=50), nullable=True),
    )
    op.add_column(
        "fi_location",
        sa.Column("subregion_name_sv", sa.Text(length=50), nullable=True),
    )
    op.drop_constraint(
        op.f("fk_fi_location_parent_id_fi_location"), "fi_location", type_="foreignkey"
    )
    op.drop_constraint("uq_location_code_parent_id", "fi_location", type_="unique")
    op.drop_index(op.f("ix_fi_location_parent_id"), table_name="fi_location")
    op.drop_column("fi_location", "parent_id")
    op.drop_column("fi_location", "location_name_sv")
    op.drop_column("fi_location", "location_name_fi")
    op.drop_column("fi_location", "location_name_en")
    op.drop_column("fi_location", "location_code")
    # ### end Alembic commands ###
