"""purchase_offer_buyers_sellers

Revision ID: 232b1014ad78
Revises: e1ef7f939fc4
Create Date: 2025-05-16 13:24:01.746046

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '232b1014ad78'
down_revision = 'e1ef7f939fc4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fi_purchase_offer_buyer',
    sa.Column('purchase_offer_id', sa.BigInteger(), nullable=False),
    sa.Column('buyer_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['buyer_id'], ['contact.id'], name=op.f('fk_fi_purchase_offer_buyer_buyer_id_contact'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['purchase_offer_id'], ['fi_purchase_offer.id'], name=op.f('fk_fi_purchase_offer_buyer_purchase_offer_id_fi_purchase_offer'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_purchase_offer_buyer'))
    )
    op.create_table('fi_purchase_offer_seller',
    sa.Column('purchase_offer_id', sa.BigInteger(), nullable=False),
    sa.Column('seller_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['purchase_offer_id'], ['fi_purchase_offer.id'], name=op.f('fk_fi_purchase_offer_seller_purchase_offer_id_fi_purchase_offer'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['seller_id'], ['contact.id'], name=op.f('fk_fi_purchase_offer_seller_seller_id_contact'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_fi_purchase_offer_seller'))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('fi_purchase_offer_seller')
    op.drop_table('fi_purchase_offer_buyer')
    # ### end Alembic commands ###
