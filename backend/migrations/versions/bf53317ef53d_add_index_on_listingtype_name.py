"""add-index-on-listingtype-name

Revision ID: bf53317ef53d
Revises: 935b9bdf6fa9
Create Date: 2025-06-13 09:29:35.552277

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "bf53317ef53d"
down_revision = "935b9bdf6fa9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_listingtype_name", "listing_type", ["name"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_listingtype_name", table_name="listing_type")
    # ### end Alembic commands ###
