"""Change type for plot redemption info

Revision ID: 70b472729be8
Revises: 50b8ab2f5229
Create Date: 2025-02-04 17:48:24.726054

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "70b472729be8"
down_revision = "50b8ab2f5229"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info",
        existing_type=mysql.VARCHAR(
            charset="utf8mb4", collation="utf8mb4_bin", length=255
        ),
        type_=sa.JSON(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement",
        existing_type=mysql.VARCHAR(
            charset="utf8mb4", collation="utf8mb4_bin", length=255
        ),
        type_=sa.JSON(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement",
        existing_type=sa.JSON(),
        type_=mysql.VARCHAR(charset="utf8mb4", collation="utf8mb4_bin", length=255),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info",
        existing_type=sa.JSON(),
        type_=mysql.VARCHAR(charset="utf8mb4", collation="utf8mb4_bin", length=255),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
