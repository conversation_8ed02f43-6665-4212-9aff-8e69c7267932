"""add internal_notes

Revision ID: d30370ee4e22
Revises: aa4b01376a0a
Create Date: 2024-05-20 10:31:38.729298

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d30370ee4e22"
down_revision = "aa4b01376a0a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("property", sa.Column("internal_notes", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "internal_notes")
    # ### end Alembic commands ###
