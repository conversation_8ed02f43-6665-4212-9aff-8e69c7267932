"""add commission_notes

Revision ID: aa4b01376a0a
Revises: 5fc8f441db49
Create Date: 2024-05-17 11:35:23.817470

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aa4b01376a0a"
down_revision = "5fc8f441db49"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("property", sa.Column("commission_notes", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "commission_notes")
    # ### end Alembic commands ###
