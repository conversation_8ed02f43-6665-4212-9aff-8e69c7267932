"""add realtor email for raw lead data

Revision ID: e3aa208447a8
Revises: 13cde3cc6b26
Create Date: 2024-09-06 17:25:12.284606

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e3aa208447a8"
down_revision = "13cde3cc6b26"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "raw_lead_data",
        sa.Column("realtor_email", sa.String(length=100), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("raw_lead_data", "realtor_email")
    # ### end Alembic commands ###
