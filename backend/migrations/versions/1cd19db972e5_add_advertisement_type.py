"""Add advertisement type

Revision ID: 1cd19db972e5
Revises: 4829312be2b8
Create Date: 2025-03-21 22:38:40.835962

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1cd19db972e5'
down_revision = '4829312be2b8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement', sa.Column('type', sa.Enum('LISTING_PROPERTY', 'PROPERTY_SOLD', 'AGENT', 'EVENT', 'CUSTOM', name='adtype'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('advertisement', 'type')
    # ### end Alembic commands ###
