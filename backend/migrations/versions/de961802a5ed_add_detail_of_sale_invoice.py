"""add_detail_of_sale_invoice

Revision ID: de961802a5ed
Revises: 89fca7ce0d64
Create Date: 2024-08-28 09:18:05.607995

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "de961802a5ed"
down_revision = "89fca7ce0d64"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "details_of_sale_invoice",
        sa.<PERSON>umn("details_of_sale_id", sa.BigInteger(), nullable=False),
        sa.Column("invoice_date", sa.DateTime(), nullable=True),
        sa.Column("invoice_due_date", sa.DateTime(), nullable=True),
        sa.Column("seller_id", sa.BigInteger(), nullable=True),
        sa.Column("document_id", sa.BigInteger(), nullable=True),
        sa.Column("id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["details_of_sale_id"],
            ["details_of_sale.id"],
            name=op.f("fk_details_of_sale_invoice_details_of_sale_id_details_of_sale"),
        ),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["document.id"],
            name=op.f("fk_details_of_sale_invoice_document_id_document"),
        ),
        sa.ForeignKeyConstraint(
            ["seller_id"],
            ["seller_details_of_sale.id"],
            name=op.f("fk_details_of_sale_invoice_seller_id_seller_details_of_sale"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_details_of_sale_invoice")),
    )
    op.create_index(
        op.f("ix_details_of_sale_invoice_details_of_sale_id"),
        "details_of_sale_invoice",
        ["details_of_sale_id"],
        unique=False,
    )
    op.add_column(
        "seller_details_of_sale", sa.Column("issued_by", sa.BigInteger(), nullable=True)
    )
    op.add_column(
        "seller_details_of_sale", sa.Column("agent_id", sa.BigInteger(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_seller_details_of_sale_issued_by_office"),
        "seller_details_of_sale",
        "office",
        ["issued_by"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_seller_details_of_sale_agent_id_user"),
        "seller_details_of_sale",
        "user",
        ["agent_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_seller_details_of_sale_agent_id_user"),
        "seller_details_of_sale",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_seller_details_of_sale_issued_by_office"),
        "seller_details_of_sale",
        type_="foreignkey",
    )
    op.drop_column("seller_details_of_sale", "agent_id")
    op.drop_column("seller_details_of_sale", "issued_by")
    op.drop_index(
        op.f("ix_details_of_sale_invoice_details_of_sale_id"),
        table_name="details_of_sale_invoice",
    )
    op.drop_table("details_of_sale_invoice")
    # ### end Alembic commands ###
