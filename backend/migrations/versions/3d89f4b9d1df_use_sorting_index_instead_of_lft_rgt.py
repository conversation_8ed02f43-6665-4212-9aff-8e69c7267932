"""use sorting index instead of lft rgt

Revision ID: 3d89f4b9d1df
Revises: f822822863d9
Create Date: 2025-08-04 15:01:28.530902

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '3d89f4b9d1df'
down_revision = 'f822822863d9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_sales_agreement_realtor', sa.Column('sorting_index', sa.Integer(), nullable=False))
    op.drop_column('fi_sales_agreement_realtor', '_lft')
    op.drop_column('fi_sales_agreement_realtor', '_rgt')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_sales_agreement_realtor', sa.Column('_rgt', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False))
    op.add_column('fi_sales_agreement_realtor', sa.Column('_lft', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False))
    op.drop_column('fi_sales_agreement_realtor', 'sorting_index')
       # ### end Alembic commands ###
