"""Create residential_property_overview

Revision ID: b55c35c008b0
Revises: 405d1a68b397
Create Date: 2024-07-24 04:33:08.742870

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b55c35c008b0"
down_revision = "405d1a68b397"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_residential_property_overview",
        sa.Column("residential_type_code", sa.String(length=50), nullable=True),
        sa.Column("construction_permit_grant_year", sa.Integer(), nullable=True),
        sa.Column("construction_start_year", sa.Integer(), nullable=True),
        sa.Column("construction_end_year", sa.Integer(), nullable=True),
        sa.Column("usage_start_year", sa.Integer(), nullable=True),
        sa.Column("development_phase_code", sa.String(length=50), nullable=True),
        sa.Column("property_inspections", sa.J<PERSON>(), nullable=True),
        sa.Column("construction_permit_usage_purpose", sa.JSON(), nullable=True),
        sa.Column("apartment", sa.JSON(), nullable=True),
        sa.Column("parking_spaces", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_residential_property_overview")),
    )


def downgrade():
    op.drop_table("fi_residential_property_overview")
