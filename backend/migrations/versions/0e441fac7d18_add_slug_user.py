"""add_slug_user

Revision ID: 0e441fac7d18
Revises: 84d45c5a1fd8
Create Date: 2024-08-12 14:49:04.879538

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0e441fac7d18'
down_revision = '84d45c5a1fd8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('slug', sa.TEXT(), nullable=True))
    op.create_unique_constraint(op.f('uq_user_slug'), 'user', ['slug'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('uq_user_slug'), 'user', type_='unique')
    op.drop_column('user', 'slug')
    # ### end Alembic commands ###
