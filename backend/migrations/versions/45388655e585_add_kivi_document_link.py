"""Add kivi document link

Revision ID: 45388655e585
Revises: 040822d66f0d
Create Date: 2025-06-05 14:20:48.891316

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '45388655e585'
down_revision = '040822d66f0d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('kivi_document_link',
    sa.<PERSON>umn('kivi_assignment_id', sa.Integer(), nullable=False),
    sa.Column('kivi_document_id', sa.Integer(), nullable=False),
    sa.Column('kivi_document_type', sa.String(length=100), nullable=True),
    sa.Column('document_library_item_id', sa.BigInteger(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['document_library_item_id'], ['document_library_item.id'], name=op.f('fk_kivi_document_link_document_library_item_id_document_library_item'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_kivi_document_link')),
    sa.UniqueConstraint('kivi_assignment_id', 'kivi_document_id', name='uq_kivi_document_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('kivi_document_link')
    # ### end Alembic commands ###
