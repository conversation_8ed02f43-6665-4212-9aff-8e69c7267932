"""Add share field for fi realty

Revision ID: 1c1a435553ed
Revises: 086deab883a0
Create Date: 2025-05-09 13:45:23.727095

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "1c1a435553ed"
down_revision = "086deab883a0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("fi_realty", sa.Column("share", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_realty", "share")
    # ### end Alembic commands ###
