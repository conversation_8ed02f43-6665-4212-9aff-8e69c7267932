"""Update table event

Revision ID: 21fc57dcca2f
Revises: ad1468408da9
Create Date: 2024-07-23 12:04:34.444171

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "21fc57dcca2f"
down_revision = "ad1468408da9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "event",
        "start",
        new_column_name="start_time",
        existing_type=mysql.DATETIME(),
        nullable=False,
    )
    op.alter_column(
        "event",
        "end",
        new_column_name="end_time",
        existing_type=mysql.DATETIME(),
        nullable=True,
    )
    op.alter_column(
        "event",
        "end_time",
        existing_type=mysql.DATETIME(),
        nullable=False,
    )
    op.add_column("event", sa.Column("url", sa.String(length=300), nullable=True))
    op.alter_column(
        "event",
        "event_type",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "event",
        "event_format",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "event",
        "title",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=120),
        existing_nullable=True,
    )
    op.alter_column(
        "event",
        "location",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=300),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "event",
        "start_time",
        new_column_name="start",
        existing_type=mysql.DATETIME(),
        nullable=False,
    )
    op.alter_column(
        "event",
        "end_time",
        new_column_name="end",
        existing_type=mysql.DATETIME(),
        nullable=True,
    )
    op.alter_column(
        "event",
        "end",
        existing_type=mysql.DATETIME(),
        nullable=True,
    )
    op.alter_column(
        "event",
        "location",
        existing_type=sa.String(length=300),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    op.alter_column(
        "event",
        "title",
        existing_type=sa.String(length=120),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    op.alter_column(
        "event",
        "event_format",
        existing_type=sa.String(length=50),
        type_=mysql.TEXT(),
        existing_nullable=False,
    )
    op.alter_column(
        "event",
        "event_type",
        existing_type=sa.String(length=50),
        type_=mysql.TEXT(),
        existing_nullable=False,
    )
    op.drop_column("event", "url")
    # ### end Alembic commands ###
