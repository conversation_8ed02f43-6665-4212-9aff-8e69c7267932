"""create contact table

Revision ID: 53d176060bed
Revises: fbe903764fce
Create Date: 2023-07-26 14:43:08.767525

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "53d176060bed"
down_revision = "fbe903764fce"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "contact",
        sa.Column("id", sa.Big<PERSON>ger(), nullable=False),
        sa.Column("first_name", sa.String(length=100), nullable=False),
        sa.Column("last_name", sa.String(length=100), nullable=False),
        sa.Column("phone_number", sa.String(length=100), nullable=True),
        sa.Column("email", sa.String(length=100), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contact")),
    )
    op.create_table(
        "contact_realtor",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("contact", sa.BigInteger(), nullable=False),
        sa.Column("realtor", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["contact"], ["contact.id"], name=op.f("fk_contact_realtor_contact_contact")
        ),
        sa.ForeignKeyConstraint(
            ["realtor"], ["user.id"], name=op.f("fk_contact_realtor_realtor_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contact_realtor")),
        sa.UniqueConstraint("contact", "realtor", name=op.f("uq_contact_realtor")),
    )
    op.create_index(
        op.f("ix_contact_realtor_contact"),
        "contact_realtor",
        ["contact"],
        unique=False,
    )
    op.create_index(
        op.f("ix_contact_realtor_realtor"),
        "contact_realtor",
        ["realtor"],
        unique=False,
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_contact_realtor_contact_contact"),
        "contact_realtor",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_contact_realtor_realtor_user"), "contact_realtor", type_="foreignkey"
    )
    op.drop_table("contact_realtor")
    op.drop_table("contact")
