"""Add external event table

Revision ID: b5b94db3010d
Revises: d7322c3ebe61
Create Date: 2025-02-17 13:49:25.706186

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "b5b94db3010d"
down_revision = "d7322c3ebe61"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "external_event",
        sa.Column("event_uuid", sa.String(length=36), nullable=False),
        sa.Column("event_source", sa.String(length=50), nullable=False),
        sa.Column("data", sa.JSON(), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_external_event")),
        sa.UniqueConstraint("event_uuid", name=op.f("uq_external_event_event_uuid")),
    )


def downgrade():
    op.drop_table("external_event")
