"""add floor plan for fi property

Revision ID: 73ef40d5a1b9
Revises: f32e67c6075d
Create Date: 2024-10-01 10:20:00.314312

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "73ef40d5a1b9"
down_revision = "f32e67c6075d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_property_overview",
        sa.Column("floor_plan", sa.String(length=100), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_property_overview", "floor_plan")
    # ### end Alembic commands ###
