"""Remove composite PK from PropertyOrientation

Revision ID: 8787bc353a37
Revises: 79c743d7c40a
Create Date: 2023-08-08 15:36:37.107550

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8787bc353a37"
down_revision = "79c743d7c40a"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table("property_orientation")

    op.create_table(
        "property_orientation",
        sa.Column("orientation_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["orientation_id"],
            ["orientation.id"],
            name=op.f("fk_property_orientation_orientation_id_orientation"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_orientation_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_orientation")),
    )


def downgrade():
    pass
