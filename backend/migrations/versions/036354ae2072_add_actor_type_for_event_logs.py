"""Add actor type for event logs

Revision ID: 036354ae2072
Revises: 18cbbc30dcf6
Create Date: 2025-05-29 10:44:07.954024

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '036354ae2072'
down_revision = '18cbbc30dcf6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('event_log', sa.Column('actor_type', sa.String(length=50), server_default='USER', nullable=False))
    op.alter_column('event_log', 'actor_id',
               existing_type=mysql.BIGINT(display_width=20),
               nullable=True)
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('event_log', 'actor_id',
               existing_type=mysql.BIGINT(display_width=20),
               nullable=False)
    op.drop_column('event_log', 'actor_type')
    # ### end Alembic commands ###
