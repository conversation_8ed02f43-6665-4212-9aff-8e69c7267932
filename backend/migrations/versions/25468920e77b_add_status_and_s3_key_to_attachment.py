"""add Status and s3_key to Attachment

Revision ID: 25468920e77b
Revises: b65b3793e5ea
Create Date: 2024-12-18 15:06:44.960345

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "25468920e77b"
down_revision = "b65b3793e5ea"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_attachment", sa.Column("status", sa.String(length=20), nullable=True)
    )
    op.add_column(
        "dias_attachment", sa.Column("s3_key", sa.String(length=250), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("dias_attachment", "s3_key")
    op.drop_column("dias_attachment", "status")
    # ### end Alembic commands ###
