"""Update FI-properties tables

Revision ID: 1df4587ce254
Revises: a3bb8c7f44e8
Create Date: 2024-08-18 02:17:50.393963

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "1df4587ce254"
down_revision = "a3bb8c7f44e8"
branch_labels = None
depends_on = None


def upgrade():
    # Rename the table from fi_commercial_overview to fi_commercial_property_overview
    op.rename_table("fi_commercial_overview", "fi_commercial_property_overview")

    # Create a new index on commercial_property_type_code in fi_commercial_property_overview
    op.create_index(
        "ix_fi_commercial_property_overview_commercial_property_type_code",
        "fi_commercial_property_overview",
        ["commercial_property_type_code"],
        unique=False,
    )

    # Create a new index on estate_property_type_code in fi_estate_overview
    op.create_index(
        op.f("ix_fi_estate_overview_estate_property_type_code"),
        "fi_estate_overview",
        ["estate_property_type_code"],
        unique=False,
    )

    # Create a new index on other_share_type_code in fi_other_share_overview
    op.create_index(
        op.f("ix_fi_other_share_overview_other_share_type_code"),
        "fi_other_share_overview",
        ["other_share_type_code"],
        unique=False,
    )

    # Alter plot_property_type_code in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_property_type_code",
        existing_type=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        type_=sa.String(length=50),
        existing_nullable=True,
    )

    # Alter holding_type_code in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "holding_type_code",
        existing_type=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        type_=sa.String(length=50),
        existing_nullable=True,
    )

    # Alter plot_redemption_info in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        existing_nullable=True,
    )

    # Alter plot_redemption_info_link in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info_link",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        existing_nullable=True,
    )

    # Alter plot_rental_agreement in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        existing_nullable=True,
    )

    # Alter plot_rental_agreement_link in fi_plot_overview to change its data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement_link",
        existing_type=mysql.TEXT(),
        type_=sa.String(length=255),
        existing_nullable=True,
    )

    # Alter real_estate_type_code in fi_property_overview to change its data type and length
    op.alter_column(
        "fi_property_overview",
        "real_estate_type_code",
        existing_type=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        type_=sa.String(length=50),
        existing_nullable=True,
    )

    # Create a new index on category in fi_property_type
    op.create_index(
        op.f("ix_fi_property_type_category"),
        "fi_property_type",
        ["category"],
        unique=False,
    )

    # Create a new index on subcategory in fi_property_type
    op.create_index(
        op.f("ix_fi_property_type_subcategory"),
        "fi_property_type",
        ["subcategory"],
        unique=False,
    )

    # Create a new index on subtype in fi_property_type
    op.create_index(
        op.f("ix_fi_property_type_subtype"),
        "fi_property_type",
        ["subtype"],
        unique=False,
    )

    # Create a new index on type in fi_property_type
    op.create_index(
        op.f("ix_fi_property_type_type"), "fi_property_type", ["type"], unique=False
    )

    # Create a new index on residential_type_code in fi_residential_property_overview
    op.create_index(
        op.f("ix_fi_residential_property_overview_residential_type_code"),
        "fi_residential_property_overview",
        ["residential_type_code"],
        unique=False,
    )

    # Create a new index on residential_type_code in fi_residential_share_overview
    op.create_index(
        op.f("ix_fi_residential_share_overview_residential_type_code"),
        "fi_residential_share_overview",
        ["residential_type_code"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # Drop the index on residential_type_code in fi_residential_share_overview
    op.drop_index(
        "ix_fi_residential_share_overview_residential_type_code",
        table_name="fi_residential_share_overview",
    )

    # Drop the index on residential_type_code in fi_residential_property_overview
    op.drop_index(
        "ix_fi_residential_property_overview_residential_type_code",
        table_name="fi_residential_property_overview",
    )

    # Drop the index on type in fi_property_type
    op.drop_index("ix_fi_property_type_type", table_name="fi_property_type")

    # Drop the index on subtype in fi_property_type
    op.drop_index("ix_fi_property_type_subtype", table_name="fi_property_type")

    # Drop the index on subcategory in fi_property_type
    op.drop_index("ix_fi_property_type_subcategory", table_name="fi_property_type")

    # Drop the index on category in fi_property_type
    op.drop_index("ix_fi_property_type_category", table_name="fi_property_type")

    # Revert real_estate_type_code in fi_property_overview to its original data type and length
    op.alter_column(
        "fi_property_overview",
        "real_estate_type_code",
        existing_type=sa.String(length=50),
        type_=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        existing_nullable=True,
    )

    # Revert plot_rental_agreement_link in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement_link",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )

    # Revert plot_rental_agreement in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_rental_agreement",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )

    # Revert plot_redemption_info_link in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info_link",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )

    # Revert plot_redemption_info in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_redemption_info",
        existing_type=sa.String(length=255),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )

    # Revert holding_type_code in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "holding_type_code",
        existing_type=sa.String(length=50),
        type_=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        existing_nullable=True,
    )

    # Revert plot_property_type_code in fi_plot_overview to its original data type and length
    op.alter_column(
        "fi_plot_overview",
        "plot_property_type_code",
        existing_type=sa.String(length=50),
        type_=mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
        existing_nullable=True,
    )

    # Drop the index on other_share_type_code in fi_other_share_overview
    op.drop_index(
        "ix_fi_other_share_overview_other_share_type_code",
        table_name="fi_other_share_overview",
    )

    # Drop the index on estate_property_type_code in fi_estate_overview
    op.drop_index(
        "ix_fi_estate_overview_estate_property_type_code",
        table_name="fi_estate_overview",
    )

    # Drop the index on commercial_property_type_code in fi_commercial_property_overview
    op.drop_index(
        "ix_fi_commercial_property_overview_commercial_property_type_code",
        table_name="fi_commercial_property_overview",
    )

    # Reverse the renaming of the table from fi_commercial_property_overview back to fi_commercial_overview
    op.rename_table("fi_commercial_property_overview", "fi_commercial_overview")
    # ### end Alembic commands ###
