"""add _dias_api_key field to User

Revision ID: d71d7391af04
Revises: a91a00821bb3
Create Date: 2024-12-04 11:52:59.704137

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "d71d7391af04"
down_revision = "a91a00821bb3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("_dias_api_key", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "_dias_api_key")
    # ### end Alembic commands ###
