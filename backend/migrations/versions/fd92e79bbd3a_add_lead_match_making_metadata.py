"""add lead match making metadata

Revision ID: fd92e79bbd3a
Revises: 37fdda402de7
Create Date: 2024-05-26 21:08:41.216241

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "fd92e79bbd3a"
down_revision = "37fdda402de7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "match_making_property",
        sa.<PERSON>umn("id", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("like", sa.<PERSON>(), nullable=True),
        sa.<PERSON>umn("match_making_id", sa.Big<PERSON>ger(), nullable=False),
        sa.Column("property_id", sa.<PERSON>Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_property_match_making_id_match_making"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_match_making_property_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_property")),
        sa.UniqueConstraint(
            "match_making_id", "property_id", name=op.f("uq_match_making_property")
        ),
    )

    op.create_table(
        "email_template",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("template", sa.Text(), nullable=True),
        sa.Column("subject", sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_email_template")),
    )

    op.create_table(
        "match_making_email_template",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("match_making_id", sa.BigInteger(), nullable=False),
        sa.Column("email_template_id", sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_email_template_match_making_id_match_making"),
        ),
        sa.ForeignKeyConstraint(
            ["email_template_id"],
            ["email_template.id"],
            name=op.f(
                "fk_match_making_email_template_email_template_id_email_template"
            ),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_email_template")),
        sa.UniqueConstraint(
            "match_making_id",
            "email_template_id",
            name=op.f("uq_match_making_email_template"),
        ),
    )

    op.create_table(
        "match_making_version",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("match_making_id", sa.BigInteger(), nullable=False),
        sa.Column("version", sa.String(255), nullable=False),
        sa.Column("click", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_version_match_making_id_match_making"),
        ),
    )


def downgrade():
    op.drop_constraint(
        "fk_match_making_version_match_making_id_match_making",
        "match_making_version",
        type_="foreignkey",
    )
    op.drop_table("match_making_version")
    op.drop_constraint(
        "fk_match_making_email_template_match_making_id_match_making",
        "match_making_email_template",
        type_="foreignkey",
    )
    op.drop_constraint(
        "fk_match_making_email_template_email_template_id_email_template",
        "match_making_email_template",
        type_="foreignkey",
    )
    op.drop_table("match_making_email_template")
    op.drop_table("email_template")
    op.drop_constraint(
        "fk_match_making_property_match_making_id_match_making",
        "match_making_property",
        type_="foreignkey",
    )
    op.drop_constraint(
        "fk_match_making_property_property_id_property",
        "match_making_property",
        type_="foreignkey",
    )
    op.drop_table("match_making_property")
