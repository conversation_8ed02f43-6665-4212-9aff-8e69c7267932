"""Add match making property contact table

Revision ID: 517fa6dd3f58
Revises: 0aeddf6951bf
Create Date: 2024-11-20 03:04:34.806986

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "517fa6dd3f58"
down_revision = "0aeddf6951bf"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "match_making_property_contact",
        sa.<PERSON>umn("property_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("contact_id", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("is_sent", sa.<PERSON>(), nullable=False),
        sa.<PERSON>umn("id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_match_making_property_contact_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property_spain.id"],
            name=op.f("fk_match_making_property_contact_property_id_property_spain"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_property_contact")),
        sa.UniqueConstraint(
            "property_id", "contact_id", name="uq_match_making_property_id_contact_id"
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_match_making_property_contact_contact_id_contact"),
        "match_making_property_contact",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_property_contact_property_id_property_spain"),
        "match_making_property_contact",
        type_="foreignkey",
    )
    op.drop_table("match_making_property_contact")
    # ### end Alembic commands ###
