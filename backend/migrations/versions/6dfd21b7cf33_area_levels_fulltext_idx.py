"""area levels fulltext idx

Revision ID: 6dfd21b7cf33
Revises: 14b22f751151
Create Date: 2024-03-12 12:36:43.059435

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6dfd21b7cf33"
down_revision = "14b22f751151"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_name_fulltext",
        "area_level_1",
        ["name"],
        unique=False,
        mariadb_prefix="FULLTEXT",
    )
    op.create_index(
        "ix_name_fulltext",
        "area_level_2",
        ["name"],
        unique=False,
        mariadb_prefix="FULLTEXT",
    )
    op.create_index(
        "ix_name_fulltext",
        "area_level_3",
        ["name"],
        unique=False,
        mariadb_prefix="FULLTEXT",
    )
    op.create_index(
        "ix_name_fulltext",
        "area_level_4",
        ["name"],
        unique=False,
        mariadb_prefix="FULLTEXT",
    )
    op.create_index(
        "ix_name_fulltext",
        "area_level_5",
        ["name"],
        unique=False,
        mariadb_prefix="FULLTEXT",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_name_fulltext", table_name="area_level_5", mariadb_prefix="FULLTEXT"
    )
    op.drop_index(
        "ix_name_fulltext", table_name="area_level_4", mariadb_prefix="FULLTEXT"
    )
    op.drop_index(
        "ix_name_fulltext", table_name="area_level_3", mariadb_prefix="FULLTEXT"
    )
    op.drop_index(
        "ix_name_fulltext", table_name="area_level_2", mariadb_prefix="FULLTEXT"
    )
    op.drop_index(
        "ix_name_fulltext", table_name="area_level_1", mariadb_prefix="FULLTEXT"
    )
    # ### end Alembic commands ###
