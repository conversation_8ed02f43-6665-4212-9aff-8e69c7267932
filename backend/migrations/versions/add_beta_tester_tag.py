"""add beta tester tag

Revision ID: 2f9a4e3b8d12
Revises: d15e30c42795
Create Date: 2024-03-26

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column


# revision identifiers, used by Alembic.
revision = '2f9a4e3b8d12'
down_revision = 'd15e30c42795'  # Points to the latest migration
branch_labels = None
depends_on = None


def upgrade():
    # Create a temp table construct for the insert
    tag_table = table('tag',
        column('id', sa.BigInteger),
        column('name', sa.Text),
        column('created_at', sa.DateTime),
        column('updated_at', sa.DateTime)
    )

    # Insert the Beta Tester tag
    op.bulk_insert(
        tag_table,
        [
            {'name': 'Beta Tester'}
        ]
    )


def downgrade():
    # Remove the Beta Tester tag
    op.execute("DELETE FROM tag WHERE name = 'Beta Tester'") 