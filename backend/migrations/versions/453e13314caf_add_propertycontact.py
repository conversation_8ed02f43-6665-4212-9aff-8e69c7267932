"""Add PropertyContact

Revision ID: 453e13314caf
Revises: ff8ad73b391e
Create Date: 2023-09-25 13:29:22.254337

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "453e13314caf"
down_revision = "ff8ad73b391e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "property_contact",
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_property_contact_contact_id_contact"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_contact_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_contact")),
        sa.UniqueConstraint("property_id", "contact_id", name="uq_property_contact"),
    )
    op.create_index(
        "ix_property_contact_contact", "property_contact", ["contact_id"], unique=False
    )
    op.create_index(
        "ix_property_contact_property",
        "property_contact",
        ["property_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_property_contact_property", table_name="property_contact")
    op.drop_index("ix_property_contact_contact", table_name="property_contact")
    op.drop_table("property_contact")
    # ### end Alembic commands ###
