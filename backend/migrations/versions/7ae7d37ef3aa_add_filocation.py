"""Add FILocation

Revision ID: 7ae7d37ef3aa
Revises: 2c6dc5a7812a
Create Date: 2024-06-28 16:20:42.205793

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7ae7d37ef3aa"
down_revision = "8eb1ac28b03e"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "fi_location",
        sa.Column("kind", sa.String(length=50), nullable=False),
        sa.Column("major_region_code", sa.String(length=50), nullable=False),
        sa.Column("major_region_name_fi", sa.String(length=50), nullable=False),
        sa.Column("major_region_name_en", sa.String(length=50), nullable=False),
        sa.Column("major_region_name_sv", sa.String(length=50), nullable=False),
        sa.Column("region_code", sa.String(length=50), nullable=True),
        sa.Column("region_name_fi", sa.String(length=50), nullable=True),
        sa.Column("region_name_en", sa.String(length=50), nullable=True),
        sa.Column("region_name_sv", sa.String(length=50), nullable=True),
        sa.Column("subregion_code", sa.String(length=50), nullable=True),
        sa.Column("subregion_name_fi", sa.String(length=50), nullable=True),
        sa.Column("subregion_name_en", sa.String(length=50), nullable=True),
        sa.Column("subregion_name_sv", sa.String(length=50), nullable=True),
        sa.Column("municipality_code", sa.String(length=50), nullable=True),
        sa.Column("municipality_name_fi", sa.String(length=50), nullable=True),
        sa.Column("municipality_name_en", sa.String(length=50), nullable=True),
        sa.Column("municipality_name_sv", sa.String(length=50), nullable=True),
        sa.Column("district_code", sa.String(length=50), nullable=True),
        sa.Column("district_name_fi", sa.String(length=50), nullable=True),
        sa.Column("district_name_en", sa.String(length=50), nullable=True),
        sa.Column("district_name_sv", sa.String(length=50), nullable=True),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_fi_location")),
    )


def downgrade():
    op.drop_table("fi_location")
