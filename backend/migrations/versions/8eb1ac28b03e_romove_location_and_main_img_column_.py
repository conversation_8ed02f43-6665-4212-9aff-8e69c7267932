"""Romove location and main_img column from property

Revision ID: 8eb1ac28b03e
Revises: 2c6dc5a7812a
Create Date: 2024-07-01 13:37:08.267095

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8eb1ac28b03e'
down_revision = '2c6dc5a7812a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_spain', 'location')
    op.drop_column('property_spain', 'main_img')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_spain', sa.Column('main_img', mysql.TEXT(), nullable=True))
    op.add_column('property_spain', sa.Column('location', mysql.VARCHAR(length=100), nullable=True))
    # ### end Alembic commands ###
