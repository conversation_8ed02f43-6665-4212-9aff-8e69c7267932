"""use numeric

Revision ID: e845ac393ec6
Revises: f1fc27358a14
Create Date: 2025-08-21 13:11:20.767848

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e845ac393ec6"
down_revision = "f1fc27358a14"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_counter_offer",
        "unencumbered_price",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=False,
    )
    op.alter_column(
        "fi_details_of_sale",
        "highest_rejected_offer",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "sale_price",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "debt",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "debt_free_price",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_total",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_without_vat",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_vat_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_with_vat",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_lead_details_of_sale",
        "commission_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "unencumbered_price",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "loan_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "price_including_loan",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "availability_delay_fee",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "delay_fee_per_started_week",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "standard_compensation",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "down_payment",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_recipient_details_of_sale",
        "commission_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "unencumbered_price_request",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "unencumbered_price_request_estimate",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "loan_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "price_including_loan",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "lease_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "lease_deposit",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "commission_fixed",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "unpaid_maintenance_charge_amount",
        existing_type=mysql.FLOAT(),
        type_=sa.Numeric(precision=14, scale=2),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "fi_sales_agreement",
        "unpaid_maintenance_charge_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "commission_fixed",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "lease_deposit",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "lease_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "price_including_loan",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "loan_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "unencumbered_price_request_estimate",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_sales_agreement",
        "unencumbered_price_request",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_recipient_details_of_sale",
        "commission_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "down_payment",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "standard_compensation",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "delay_fee_per_started_week",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "availability_delay_fee",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "price_including_loan",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "loan_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_purchase_offer",
        "unencumbered_price",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_lead_details_of_sale",
        "commission_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_with_vat",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_vat_amount",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_without_vat",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "commission_amount_total",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "debt_free_price",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "debt",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "sale_price",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_details_of_sale",
        "highest_rejected_offer",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=True,
    )
    op.alter_column(
        "fi_counter_offer",
        "unencumbered_price",
        existing_type=sa.Numeric(precision=14, scale=2),
        type_=mysql.FLOAT(),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
