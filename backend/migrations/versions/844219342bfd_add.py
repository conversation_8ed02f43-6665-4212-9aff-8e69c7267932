"""Add

Revision ID: 844219342bfd
Revises: fc665bfec15e
Create Date: 2025-04-10 11:14:54.168876

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '844219342bfd'
down_revision = 'fc665bfec15e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('advertisement', sa.Column('country', sa.String(length=255), nullable=False))
    op.add_column('advertisement', sa.Column('municipality', sa.String(length=255), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('advertisement', 'municipality')
    op.drop_column('advertisement', 'country')
    # ### end Alembic commands ###
