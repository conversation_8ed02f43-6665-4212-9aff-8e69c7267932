"""rename for field zonings, beaches and digital share group

Revision ID: 3666c014d17c
Revises: 218af0fd6fa0
Create Date: 2024-10-28 11:33:56.063868

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '3666c014d17c'
down_revision = '218af0fd6fa0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_housing_company', sa.Column('digital_share_group_identifier', sa.String(length=50), nullable=True))
    op.drop_column('fi_housing_company', 'digital_share_group_identifiers')
    op.add_column('fi_plot_overview', sa.Column('zonings', sa.JSON(), nullable=True))
    op.add_column('fi_plot_overview', sa.Column('beaches', sa.JSON(), nullable=True))
    op.drop_column('fi_plot_overview', 'beach')
    op.drop_column('fi_plot_overview', 'zoning')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_plot_overview', sa.Column('zoning', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=True))
    op.add_column('fi_plot_overview', sa.Column('beach', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=True))
    op.drop_column('fi_plot_overview', 'beaches')
    op.drop_column('fi_plot_overview', 'zonings')
    op.add_column('fi_housing_company', sa.Column('digital_share_group_identifiers', mysql.VARCHAR(length=50), nullable=True))
    op.drop_column('fi_housing_company', 'digital_share_group_identifier')
    # ### end Alembic commands ###
