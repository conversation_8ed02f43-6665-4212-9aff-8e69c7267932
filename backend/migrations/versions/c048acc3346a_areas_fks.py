"""Areas FKs

Revision ID: c048acc3346a
Revises: 54148c3d48e9
Create Date: 2023-06-14 10:39:07.887253

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c048acc3346a"
down_revision = "d594ad659e19"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        op.f("fk_dw_areas2_area1_id_dw_areas1"),
        "dw_areas2",
        "dw_areas1",
        ["area1_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_dw_areas3_area2_id_dw_areas2"),
        "dw_areas3",
        "dw_areas2",
        ["area2_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_dw_areas4_area3_id_dw_areas3"),
        "dw_areas4",
        "dw_areas3",
        ["area3_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_dw_areas5_area4_id_dw_areas4"),
        "dw_areas5",
        "dw_areas4",
        ["area4_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_dw_areas5_area4_id_dw_areas4"), "dw_areas5", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas4_area3_id_dw_areas3"), "dw_areas4", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas3_area2_id_dw_areas2"), "dw_areas3", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_dw_areas2_area1_id_dw_areas1"), "dw_areas2", type_="foreignkey"
    )
    # ### end Alembic commands ###
