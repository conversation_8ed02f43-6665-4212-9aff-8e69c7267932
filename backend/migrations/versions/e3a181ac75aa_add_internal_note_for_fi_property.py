"""add internal note for fi property

Revision ID: e3a181ac75aa
Revises: 73ef40d5a1b9
Create Date: 2024-10-02 11:19:56.001547

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e3a181ac75aa"
down_revision = "73ef40d5a1b9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_property", sa.Column("internal_note", sa.Text(length=200), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("fi_property", "internal_note")
    # ### end Alembic commands ###
