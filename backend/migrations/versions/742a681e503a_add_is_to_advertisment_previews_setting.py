"""Add is to advertisment previews setting

Revision ID: 742a681e503a
Revises: bf8df696dd77
Create Date: 2025-05-24 22:29:12.264341

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '742a681e503a'
down_revision = 'bf8df696dd77'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    
    # Check if table exists
    table_exists = conn.execute(text("""
        SELECT COUNT(1) 
        FROM information_schema.tables 
        WHERE table_name = 'advertisement_preview_settings'
    """)).scalar()
    
    if table_exists:
        op.drop_table('advertisement_preview_settings')
    
    # Create the new table with desired structure
    op.create_table('advertisement_preview_settings',
        sa.Column('id', mysql.BIGINT(), nullable=False, autoincrement=True),
        sa.Column('advertisement_id', mysql.BIGINT(), nullable=False),
        sa.Column('display_metrics', sa.Boolean(), nullable=False, default=False),
        sa.Column('display_details', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['advertisement_id'], ['advertisement.id'], 
                              name='fk_advertisement_preview_settings_advertisement_id_advertisement',
                              ondelete='CASCADE'),
        sa.UniqueConstraint('advertisement_id', name='uq_advertisement_preview_settings_advertisement_id'),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci'
    )
    
    # Create the index
    op.create_index('ix_advertisement_preview_settings_advertisement_id', 
                    'advertisement_preview_settings', 
                    ['advertisement_id'])


def downgrade():
    # Drop the new table
    op.drop_table('advertisement_preview_settings')
