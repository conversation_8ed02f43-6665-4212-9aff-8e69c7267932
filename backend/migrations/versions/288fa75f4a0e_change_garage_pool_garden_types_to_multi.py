"""change garage,pool,garden types to multi

Revision ID: 288fa75f4a0e
Revises: 862a5936abd4
Create Date: 2024-01-31 11:53:15.726987

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "288fa75f4a0e"
down_revision = "862a5936abd4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "garage_type",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_garage_type")),
    )
    op.create_table(
        "garden_type",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_garden_type")),
    )
    op.create_table(
        "pool_type",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_pool_type")),
    )
    op.create_table(
        "property_garage_type",
        sa.Column("garage_type_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["garage_type_id"],
            ["garage_type.id"],
            name=op.f("fk_property_garage_type_garage_type_id_garage_type"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_garage_type_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_garage_type")),
        sa.UniqueConstraint(
            "garage_type_id", "property_id", name="uq_garagetype_property"
        ),
    )
    op.create_index(
        "ix_property_garagetype_garagetype",
        "property_garage_type",
        ["garage_type_id"],
        unique=False,
    )
    op.create_index(
        "ix_property_garagetype_property",
        "property_garage_type",
        ["property_id"],
        unique=False,
    )
    op.create_table(
        "property_garden_type",
        sa.Column("garden_type_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["garden_type_id"],
            ["garden_type.id"],
            name=op.f("fk_property_garden_type_garden_type_id_garden_type"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_garden_type_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_garden_type")),
        sa.UniqueConstraint(
            "garden_type_id", "property_id", name="uq_gardentype_property"
        ),
    )
    op.create_index(
        "ix_property_gardentype_gardentype",
        "property_garden_type",
        ["garden_type_id"],
        unique=False,
    )
    op.create_index(
        "ix_property_gardentype_property",
        "property_garden_type",
        ["property_id"],
        unique=False,
    )
    op.create_table(
        "property_pool_type",
        sa.Column("pool_type_id", sa.BigInteger(), nullable=False),
        sa.Column("property_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["pool_type_id"],
            ["pool_type.id"],
            name=op.f("fk_property_pool_type_pool_type_id_pool_type"),
        ),
        sa.ForeignKeyConstraint(
            ["property_id"],
            ["property.id"],
            name=op.f("fk_property_pool_type_property_id_property"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_property_pool_type")),
        sa.UniqueConstraint("pool_type_id", "property_id", name="uq_pooltype_property"),
    )
    op.create_index(
        "ix_property_pooltype_pooltype",
        "property_pool_type",
        ["pool_type_id"],
        unique=False,
    )
    op.create_index(
        "ix_property_pooltype_property",
        "property_pool_type",
        ["property_id"],
        unique=False,
    )
    op.drop_column("property", "garden_type")
    op.drop_column("property", "garage_type")
    op.drop_column("property", "pool_type")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "property", sa.Column("pool_type", mysql.VARCHAR(length=20), nullable=True)
    )
    op.add_column(
        "property", sa.Column("garage_type", mysql.VARCHAR(length=20), nullable=True)
    )
    op.add_column(
        "property", sa.Column("garden_type", mysql.VARCHAR(length=20), nullable=True)
    )
    op.drop_index("ix_property_pooltype_property", table_name="property_pool_type")
    op.drop_index("ix_property_pooltype_pooltype", table_name="property_pool_type")
    op.drop_table("property_pool_type")
    op.drop_index("ix_property_gardentype_property", table_name="property_garden_type")
    op.drop_index(
        "ix_property_gardentype_gardentype", table_name="property_garden_type"
    )
    op.drop_table("property_garden_type")
    op.drop_index("ix_property_garagetype_property", table_name="property_garage_type")
    op.drop_index(
        "ix_property_garagetype_garagetype", table_name="property_garage_type"
    )
    op.drop_table("property_garage_type")
    op.drop_table("pool_type")
    op.drop_table("garden_type")
    op.drop_table("garage_type")
    # ### end Alembic commands ###
