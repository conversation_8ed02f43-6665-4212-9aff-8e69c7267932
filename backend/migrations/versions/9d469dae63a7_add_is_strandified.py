"""Add is strandified

Revision ID: 9d469dae63a7
Revises: eb084f78c5cb
Create Date: 2024-04-17 11:59:50.722205

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9d469dae63a7"
down_revision = "eb084f78c5cb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("property", sa.Column("is_strandified", sa.<PERSON>(), nullable=False))
    op.execute("UPDATE property SET is_strandified=1 WHERE reference like 'STRAND%';")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("property", "is_strandified")
    # ### end Alembic commands ###
