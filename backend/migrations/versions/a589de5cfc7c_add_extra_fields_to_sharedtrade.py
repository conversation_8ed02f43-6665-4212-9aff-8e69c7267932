"""add extra fields to SharedTrade

Revision ID: a589de5cfc7c
Revises: 142b1df19012
Create Date: 2024-09-13 17:27:01.782062

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "a589de5cfc7c"
down_revision = "142b1df19012"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade", sa.Column("dias_updated_at", sa.DateTime(), nullable=True)
    )
    op.add_column(
        "dias_shared_trade", sa.Column("trade_state", sa.JSON(), nullable=True)
    )
    op.add_column("dias_shared_trade", sa.Column("events", sa.JSON(), nullable=True))
    op.add_column(
        "dias_shared_trade",
        sa.Column("ownership_transfer_power_of_attorney", sa.<PERSON><PERSON><PERSON>(), nullable=True),
    )
    op.drop_column("dias_shared_trade", "trade_phase")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_shared_trade",
        sa.Column(
            "trade_phase",
            mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
            nullable=True,
        ),
    )
    op.drop_column("dias_shared_trade", "ownership_transfer_power_of_attorney")
    op.drop_column("dias_shared_trade", "events")
    op.drop_column("dias_shared_trade", "trade_state")
    op.drop_column("dias_shared_trade", "dias_updated_at")
    # ### end Alembic commands ###
