"""Add organization_id to lead table

Revision ID: 59357dc0446e
Revises: e26415466903
Create Date: 2024-06-25 04:40:20.714398

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "59357dc0446e"
down_revision = "e26415466903"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    op.add_column("lead", sa.Column("organization_id", sa.BigInteger(), nullable=True))
    op.create_index(
        op.f("ix_lead_organization_id"), "lead", ["organization_id"], unique=False
    )
    op.create_foreign_key(
        op.f("fk_lead_organization_id_organization"),
        "lead",
        "organization",
        ["organization_id"],
        ["id"],
    )
    conn.execute(
        sa.text(
            """
            UPDATE lead SET organization_id=(select id from organization where name = 'Strand Spain');
            """
        )
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_lead_organization_id_organization"), "lead", type_="foreignkey"
    )
    op.drop_index(op.f("ix_lead_organization_id"), table_name="lead")
    op.drop_column("lead", "organization_id")
