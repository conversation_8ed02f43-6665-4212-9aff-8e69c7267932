"""image_property_base

Revision ID: 0a3e462b9d70
Revises: 52a185862836
Create Date: 2024-06-12 16:35:06.404915

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a3e462b9d70'
down_revision = '52a185862836'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_image_property_id_property', 'image', type_='foreignkey')
    op.create_foreign_key(op.f('fk_image_property_id_property'), 'image', 'property', ['property_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_image_property_id_property'), 'image', type_='foreignkey')
    op.create_foreign_key('fk_image_property_id_property', 'image', 'property_spain', ['property_id'], ['id'])
    # ### end Alembic commands ###
