"""Add portals to fi property

Revision ID: a8e66e6a8477
Revises: ca9d8064986e
Create Date: 2025-02-20 12:10:43.994519

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a8e66e6a8477'
down_revision = 'ca9d8064986e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fi_property', sa.Column('portals', sa.JSON(), nullable=False, server_default=sa.text("'{}'")))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fi_property', 'portals')

    # ### end Alembic commands ###
