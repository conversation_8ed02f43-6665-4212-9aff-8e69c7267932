"""check spell for field in fi housing company

Revision ID: 549f748875d0
Revises: 2b7828f9a3fe
Create Date: 2024-09-26 11:14:14.433401

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "549f748875d0"
down_revision = "2b7828f9a3fe"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_plot_overview",
        sa.Column(
            "lease_hold_transfer_limitation", sa.String(length=50), nullable=True
        ),
    )
    op.add_column(
        "fi_plot_overview",
        sa.Column(
            "lease_hold_transfer_limitation_description", sa.JSON(), nullable=True
        ),
    )
    op.drop_column("fi_plot_overview", "lease_hold_tranfer_limitation")
    op.drop_column("fi_plot_overview", "lease_hold_tranfer_limitation_description")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "fi_plot_overview",
        sa.Column(
            "lease_hold_tranfer_limitation_description",
            mysql.LONGTEXT(charset="utf8mb4", collation="utf8mb4_bin"),
            nullable=True,
        ),
    )
    op.add_column(
        "fi_plot_overview",
        sa.Column(
            "lease_hold_tranfer_limitation", mysql.VARCHAR(length=50), nullable=True
        ),
    )
    op.drop_column("fi_plot_overview", "lease_hold_transfer_limitation_description")
    op.drop_column("fi_plot_overview", "lease_hold_transfer_limitation")
    # ### end Alembic commands ###
