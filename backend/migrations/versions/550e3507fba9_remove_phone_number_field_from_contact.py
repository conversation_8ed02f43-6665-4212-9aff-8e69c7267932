"""remove phone_number field from contact

Revision ID: 550e3507fba9
Revises: 9ef6835ffed8
Create Date: 2024-01-08 19:33:44.966730

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "550e3507fba9"
down_revision = "9ef6835ffed8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute(
        sa.text(
            f"""
            INSERT INTO contact_phone (phone_number, contact_id, created_at, updated_at)
            select phone_number, id, now(), now()
            from contact where phone_number is not NULL;
        """
        )
    )

    op.drop_column("contact", "phone_number")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact", sa.Column("phone_number", mysql.TEXT(), nullable=True))
    # ### end Alembic commands ###
