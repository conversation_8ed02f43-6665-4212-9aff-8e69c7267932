"""Change type for debt share additional info

Revision ID: a1209c5dd2be
Revises: 517fa6dd3f58
Create Date: 2024-11-27 14:51:24.322242

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a1209c5dd2be'
down_revision = '517fa6dd3f58'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_residential_share_overview', 'debt_share_additional_info',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fi_residential_share_overview', 'debt_share_additional_info',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    # ### end Alembic commands ###
