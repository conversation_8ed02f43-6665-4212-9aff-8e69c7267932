"""add lead match making table

Revision ID: 41c3c2451bf0
Revises: c4597547ed1f
Create Date: 2024-03-22 08:17:07.182128

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "41c3c2451bf0"
down_revision = "9d469dae63a7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Create match making table
    op.create_table(
        "match_making",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("params", sa.Text, nullable=False),
        sa.Column("lead_id", sa.<PERSON>nteger(), nullable=True),
        sa.ForeignKeyConstraint(
            ["lead_id"],
            ["lead.id"],
            name=op.f("fk_match_making_lead_id_lead"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making")),
    )

    # Create match making user
    op.create_table(
        "match_making_user",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("match_making_id", sa.BigInteger(), nullable=False),
        sa.Column("user_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_user_match_making_id_match_making"),
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_match_making_user_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_user")),
        sa.UniqueConstraint(
            "match_making_id", "user_id", name=op.f("uq_match_making_user")
        ),
    )

    # Create match making contact
    op.create_table(
        "match_making_contact",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("match_making_id", sa.BigInteger(), nullable=False),
        sa.Column("contact_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["match_making_id"],
            ["match_making.id"],
            name=op.f("fk_match_making_contact_match_making_id_match_making"),
        ),
        sa.ForeignKeyConstraint(
            ["contact_id"],
            ["contact.id"],
            name=op.f("fk_match_making_contact_contact_id_contact"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_match_making_contact")),
        sa.UniqueConstraint(
            "match_making_id", "contact_id", name=op.f("uq_match_making_contact")
        ),
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop relationships
    op.drop_constraint(
        op.f("fk_match_making_contact_match_making_id_match_making"),
        "match_making_contact",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_contact_contact_id_contact"),
        "match_making_contact",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_user_match_making_id_match_making"),
        "match_making_user",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_user_user_id_user"),
        "match_making_user",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_match_making_lead_id_lead"),
        "match_making",
        type_="foreignkey",
    )

    # Drop match making tables
    op.drop_table("match_making_contact")
    op.drop_table("match_making_user")
    op.drop_table("match_making")
    # ### end Alembic commands ###
