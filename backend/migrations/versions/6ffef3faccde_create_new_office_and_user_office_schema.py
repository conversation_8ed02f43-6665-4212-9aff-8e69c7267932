"""create new office and user_office schema

Revision ID: 6ffef3faccde
Revises: 41725074f7b7
Create Date: 2024-07-22 15:21:13.387972

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6ffef3faccde"
down_revision = "41725074f7b7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "office",
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_office")),
        sa.UniqueConstraint("name", name=op.f("uq_office_name")),
    )
    op.create_table(
        "user_office",
        sa.Column("user_id", sa.<PERSON>Integer(), nullable=False),
        sa.Column("office_id", sa.BigInteger(), nullable=False),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["office_id"], ["office.id"], name=op.f("fk_user_office_office_id_office")
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_user_office_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_user_office")),
        sa.UniqueConstraint("user_id", "office_id", name="uq_user_office"),
    )
    op.create_index(
        op.f("ix_user_office_office_id"), "user_office", ["office_id"], unique=False
    )
    op.create_index(
        op.f("ix_user_office_user_id"), "user_office", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_user_office_user_id"), table_name="user_office")
    op.drop_index(op.f("ix_user_office_office_id"), table_name="user_office")
    op.drop_table("user_office")
    op.drop_table("office")
    # ### end Alembic commands ###
