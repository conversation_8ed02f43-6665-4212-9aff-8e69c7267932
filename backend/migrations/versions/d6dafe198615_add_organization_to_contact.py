"""Add organization to contact

Revision ID: d6dafe198615
Revises: 7607e0bb65cd
Create Date: 2024-06-24 06:43:09.382426

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "d6dafe198615"
down_revision = "7fabf14f245f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    op.add_column(
        "contact", sa.Column("organization_id", sa.BigInteger(), nullable=True)
    )
    op.create_index(
        op.f("ix_contact_organization_id"), "contact", ["organization_id"], unique=False
    )
    op.create_foreign_key(
        op.f("fk_contact_organization_id_organization"),
        "contact",
        "organization",
        ["organization_id"],
        ["id"],
    )

    conn.execute(
        sa.text(
            """
            UPDATE contact SET organization_id=(select id from organization where name = 'Strand Spain');
            """
        )
    )


def downgrade():
    op.drop_constraint(
        op.f("fk_contact_organization_id_organization"), "contact", type_="foreignkey"
    )
    op.drop_index(op.f("ix_contact_organization_id"), table_name="contact")
    op.drop_column("contact", "organization_id")
