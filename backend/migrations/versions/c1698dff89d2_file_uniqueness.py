"""file uniqueness

Revision ID: c1698dff89d2
Revises: 686c3a8ad371
Create Date: 2024-02-19 16:05:38.479343

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "c1698dff89d2"
down_revision = "686c3a8ad371"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("uq_file_key", table_name="file")
    op.create_unique_constraint(
        "uq_file_property_id_key", "file", ["property_id", "key"]
    )


def downgrade():

    op.drop_constraint("uq_file_property_id_key", "file", type_="unique")
    op.create_index("uq_file_key", "file", ["key"], unique=False)
