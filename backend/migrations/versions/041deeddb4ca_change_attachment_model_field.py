"""change Attachment model field

Revision ID: 041deeddb4ca
Revises: e3a181ac75aa
Create Date: 2024-09-29 15:43:25.935215

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "041deeddb4ca"
down_revision = "e3a181ac75aa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_attachment", sa.Column("file_name", sa.String(length=200), nullable=True)
    )
    op.drop_column("dias_attachment", "business_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dias_attachment",
        sa.Column("business_id", mysql.VARCHAR(length=100), nullable=True),
    )
    op.drop_column("dias_attachment", "file_name")
    # ### end Alembic commands ###
