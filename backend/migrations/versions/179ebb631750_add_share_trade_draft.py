"""Add share_trade_draft

Revision ID: 179ebb631750
Revises: f42e9abe2a37
Create Date: 2024-08-29 11:27:30.615533

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "179ebb631750"
down_revision = "f42e9abe2a37"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "dias_shared_trade_draft",
        sa.<PERSON>umn("attachments", sa.JSON(), nullable=False),
        sa.Column("require_initiator_confirmation", sa.<PERSON>(), nullable=False),
        sa.Column("bill_of_sale", sa.JSO<PERSON>(), nullable=False),
        sa.Column("realtor_bank_account", sa.JSON(), nullable=False),
        sa.Column("sellers", sa.<PERSON><PERSON><PERSON>(), nullable=False),
        sa.Column("buyers", sa.<PERSON>(), nullable=False),
        sa.Column("initiator_contact_info", sa.JSON(), nullable=False),
        sa.Column("initiator_person_id", sa.String(length=100), nullable=False),
        sa.Column("apartment", sa.JSON(), nullable=False),
        sa.Column("trade_initiated_timestamp", sa.String(length=100), nullable=False),
        sa.Column("initiator_trade_reference_id", sa.String(length=100), nullable=True),
        sa.Column(
            "deadline_for_signing_bill_of_sale", sa.String(length=100), nullable=True
        ),
        sa.Column(
            "seller_share_certificate_status", sa.String(length=100), nullable=True
        ),
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_dias_shared_trade_draft")),
    )
    op.rename_table("property_trade_draft", "dias_property_trade_draft")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("dias_shared_trade_draft")
    op.rename_table("dias_property_trade_draft", "property_trade_draft")
    # ### end Alembic commands ###
