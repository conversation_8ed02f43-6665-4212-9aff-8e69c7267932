"""contact email not mandatory

Revision ID: 00ea6c87790a
Revises: 550e3507fba9
Create Date: 2024-01-10 19:28:48.831378

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '00ea6c87790a'
down_revision = '550e3507fba9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contact', 'email',
               existing_type=mysql.TEXT(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contact', 'email',
               existing_type=mysql.TEXT(),
               nullable=False)
    # ### end Alembic commands ###
