from pyramid.exceptions import HTTPNotFound
from pyramid.view import view_config
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from strandproperties.constants import (
    DocumentSigningEntityType,
    EventLogAction,
    EventLogObjectType,
    FISalesAgreementStatusEnum,
)
from strandproperties.libs.utils import (
    parse_fi_datetime_or_date_to_signing_dl_utc,
    update_model_from_schema,
)
from strandproperties.logger import logger
from strandproperties.models.document_library import DocumentType
from strandproperties.models.document_signing import (
    DocumentSigner,
    DocumentSigning,
    get_document_signing_where_entity,
)
from strandproperties.models.fi_sales_agreement import (
    FISalesAgreement,
    FISalesAgreementConsenter,
    FISalesAgreementContact,
    FISalesAgreementContactAcquisition,
    FISalesAgreementRealtor,
)
from strandproperties.schemas.fi_sales_agreement import (
    FISalesAgreementContactAcquisitionBase,
    FISalesAgreementCreate,
    FISalesAgreementEdit,
    FISalesAgreementList,
    FISalesAgreementParams,
    FISalesAgreementRead,
    FISalesAgreementSign,
    validate_complete_schema,
)
from strandproperties.schemas.param import IdParam, ValidateParam
from strandproperties.services.document_library.service import (
    create_fi_property_document_library,
    create_fi_sales_agreement_document_library,
)
from strandproperties.services.documents.document_renderer import (
    DocumentRenderer,
    TemplatesEnum,
    recursive_format,
)
from strandproperties.services.documents.document_signing_service import (
    fetch_signers,
    process_dokobit_create_response,
    update_signing_status,
)
from strandproperties.services.dokobit_service import DokobitService, FileData
from strandproperties.utils.event_log import create_event_log
from strandproperties.utils.file.file import generate_document_filename
from strandproperties.utils.handle_many_to_many_relationship import (
    handle_many_to_many_relationship,
)
from strandproperties.utils.openapi_meta_object import create_openapi_metadata
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class FISalesAgreementViews(BaseApi):
    def _set_realtors(self, sales_agreement_id: int, user_ids: list[int]):
        """Persist ordered realtor list using nested-set columns."""
        from sqlalchemy import delete, select

        from strandproperties.logger import logger
        from strandproperties.models.fi_sales_agreement import FISalesAgreementRealtor

        # Current state before update (for debugging)
        current_ids = self.db_session.scalars(
            select(FISalesAgreementRealtor.user_id).where(
                FISalesAgreementRealtor.fi_sales_agreement_id == sales_agreement_id
            )
        ).all()

        logger.info(
            "Updating sales agreement realtors",
            extra={
                "salesAgreementId": sales_agreement_id,
                "beforeUserIds": current_ids,
                "afterUserIds": user_ids,
            },
        )

        self.db_session.execute(
            delete(FISalesAgreementRealtor).where(
                FISalesAgreementRealtor.fi_sales_agreement_id == sales_agreement_id
            )
        )

        inserts = [
            FISalesAgreementRealtor(
                fi_sales_agreement_id=sales_agreement_id,
                user_id=uid,
                sorting_index=idx,
            )
            for idx, uid in enumerate(user_ids or [])
        ]

        self.db_session.add_all(inserts)
        self.db_session.flush()

    @view_config(
        route_name="fi_sales_agreement.list_create",
        request_method="POST",
        openapi_param_schema=ValidateParam,
        openapi_response_schema=FISalesAgreementRead,
        openapi_request_schema=FISalesAgreementCreate,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Create a new FI Sales Agreement",
            response_schema=FISalesAgreementRead,
            success_description="Sales agreement created",
        ),
    )
    def create_fi_sales_agreement(self):
        validate_param = ValidateParam(**self.request.params)
        should_validate = validate_param.should_validate
        status = FISalesAgreementStatusEnum.DRAFT
        try:
            payload = {
                **self.request.json_body,
                "created_by": self.user_id,
            }
            create_schema = FISalesAgreementCreate(**payload)

            if should_validate is True:
                validate_complete_schema(create_schema)
                status = FISalesAgreementStatusEnum.VALIDATED

            sales_agreement_data = create_schema.model_dump(
                exclude={
                    "realtor_user_ids",
                    "contact_ids",
                    "consenter_ids",
                    "contact_acquisitions",
                }
            )

            sales_agreement_data["status"] = status

            sales_agreement = FISalesAgreement(**sales_agreement_data)

            self.db_session.add(sales_agreement)
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Unexpected error occurred", status=500)

        try:
            self._set_realtors(
                sales_agreement.id,
                create_schema.model_dump().get("realtor_user_ids", []),
            )
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError(
                f"Could not create fi sales agreement relationship with realtor(s): {str(e)}",
                status=500,
            )

        # Create the contacts
        try:
            contact_ids = create_schema.model_dump().get("contact_ids", [])
            for contact_id in contact_ids:
                property_contact = FISalesAgreementContact(
                    fi_sales_agreement_id=sales_agreement.id, contact_id=contact_id
                )
                self.db_session.add(property_contact)
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError(
                f"Could not create fi sales agreement relationship with contact(s): {str(e)}",
                status=500,
            )

        # create consenters
        try:
            consenters = create_schema.model_dump().get("consenter_ids", [])
            logger.error(consenters)
            for consenter_contact_id in consenters:
                fi_sales_agreement_consenter = FISalesAgreementConsenter(
                    fi_sales_agreement_id=sales_agreement.id,
                    contact_id=consenter_contact_id,
                )
                self.db_session.add(fi_sales_agreement_consenter)
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError(
                f"Could not create fi sales agreement relationship with consenter(s): {str(e)}",
                status=500,
            )

        try:
            contact_acquisitions = create_schema.model_dump().get(
                "contact_acquisitions", []
            )
            for contact_acquisition in contact_acquisitions:
                fi_sales_agreement_contact_acquisition = (
                    FISalesAgreementContactAcquisition(
                        **{
                            "fi_sales_agreement_id": sales_agreement.id,
                            **contact_acquisition,
                        }
                    )
                )
                self.db_session.add(fi_sales_agreement_contact_acquisition)
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError(
                f"Could not create fi sales agreement relationship with contact acquisition(s): {str(e)}",
                status=500,
            )

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.FI_SALES_AGREEMENT,
            object_id=sales_agreement.id,
            actor_id=self.user_id,
            action=EventLogAction.CREATED,
        )

        self.request.response.status = 201
        return FISalesAgreementRead.model_validate(sales_agreement)

    @view_config(
        route_name="fi_sales_agreement.list_create",
        request_method="GET",
        openapi_param_schema=FISalesAgreementParams,
        openapi_response_schema=FISalesAgreementList,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="List FI Sales Agreements",
            response_schema=FISalesAgreementList,
            success_description="Sales agreements listed",
        ),
    )
    def list_fi_sales_agreements(self):
        try:
            params = FISalesAgreementParams(**self.request.GET)
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid fi sales agreement params")

        try:

            stmt = select(FISalesAgreement).where(
                FISalesAgreement.property_id == params.property_id
            )

            fi_sales_agreements = self.db_session.execute(stmt).unique().scalars().all()

            return FISalesAgreementList(
                items=[
                    FISalesAgreementRead.from_orm(item) for item in fi_sales_agreements
                ]
            )

        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not get list of fi sales agreements")

    @view_config(
        route_name="fi_sales_agreement.read_edit",
        request_method="GET",
        openapi_param_schema=IdParam,
        openapi_response_schema=FISalesAgreementRead,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Get a single FI Sales Agreement by ID",
            response_schema=FISalesAgreementRead,
            success_description="Sales agreement retrieved",
        ),
    )
    def read_fi_sales_agreement(self):
        params = IdParam(**self.request.matchdict)
        try:

            fi_sales_agreement = self.db_session.scalars(
                select(FISalesAgreement)
                .options(joinedload(FISalesAgreement.property))
                .where(FISalesAgreement.id == params.id)
            ).one()

        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound(
                "You are not authorized to view this group or group not found"
            ) from e

        return FISalesAgreementRead.model_validate(fi_sales_agreement)

    @view_config(
        route_name="fi_sales_agreement.read_edit",
        request_method="PATCH",
        openapi_param_schema=ValidateParam,
        openapi_request_schema=FISalesAgreementEdit,
        openapi_response_schema=FISalesAgreementRead,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Edit a FI Sales Agreement",
            response_schema=FISalesAgreementRead,
            success_description="Sales agreement updated",
        ),
    )
    def edit_fi_sales_agreement(self):
        params = IdParam(**self.request.matchdict)
        validate_param = ValidateParam(**self.request.params)
        should_validate = validate_param.should_validate

        fi_sales_agreement = self.db_session.scalars(
            select(FISalesAgreement).where(FISalesAgreement.id == params.id)
        ).one_or_none()

        if not self.is_admin and self.user_id != fi_sales_agreement.created_by:
            raise HTTPNotFound("You are not authorized to edit this property")

        if not fi_sales_agreement:
            raise HTTPNotFound("Sales agreement not found")

        if (
            fi_sales_agreement.status == FISalesAgreementStatusEnum.PENDING_SIGNATURES
            or fi_sales_agreement.status == FISalesAgreementStatusEnum.COMPLETED
        ):
            raise ApiError("Sales agreement cannot be updated anymore", status=400)

        status = FISalesAgreementStatusEnum.DRAFT
        if fi_sales_agreement.status != FISalesAgreementStatusEnum.DRAFT:
            status = fi_sales_agreement.status

        edit_schema = FISalesAgreementEdit(**self.request.json_body)
        if should_validate is True:
            validate_complete_schema(edit_schema)
            if status == FISalesAgreementStatusEnum.DRAFT:
                status = FISalesAgreementStatusEnum.VALIDATED
        else:
            status = FISalesAgreementStatusEnum.DRAFT
        edit_schema = edit_schema.model_copy(update={"status": status})
        update_model_from_schema(
            model_obj=fi_sales_agreement,
            schema_obj=edit_schema,
            exclude=[
                "id",
                "created_by",
                "contact_ids",
                "realtor_user_ids",
                "contact_acquisitions",
            ],
        )

        if edit_schema.realtor_user_ids is not None:
            self._set_realtors(
                fi_sales_agreement.id,
                edit_schema.realtor_user_ids,
            )

        if edit_schema.contact_ids is not None:
            handle_many_to_many_relationship(
                self.db_session,
                fi_sales_agreement.id,
                edit_schema.contact_ids,
                FISalesAgreementContact,
                "fi_sales_agreement_id",
                "contact_id",
            )
        if edit_schema.consenter_ids is not None:
            handle_many_to_many_relationship(
                self.db_session,
                fi_sales_agreement.id,
                edit_schema.consenter_ids,
                FISalesAgreementConsenter,
                "fi_sales_agreement_id",
                "contact_id",
            )
        if edit_schema.contact_acquisitions is not None:
            self._handle_contact_acquisitions(
                fi_sales_agreement.id, edit_schema.contact_acquisitions
            )

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.FI_SALES_AGREEMENT,
            object_id=fi_sales_agreement.id,
            actor_id=self.user_id,
            action=EventLogAction.UPDATED,
        )
        self.db_session.flush()

        self.request.response.status = 200
        return FISalesAgreementRead.model_validate(fi_sales_agreement)

    @view_config(
        route_name="fi_sales_agreement.send_for_signing",
        request_method="POST",
        openapi_param_schema=IdParam,
        openapi_request_schema=FISalesAgreementSign,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Send a FI Sales Agreement for signing",
            success_description="Sales agreement sent for signing",
        ),
    )
    def create_fi_sales_agreement_signing(self):
        params = IdParam(**self.request.matchdict)
        sign_schema = FISalesAgreementSign(**self.request.json_body)
        end_of_day = parse_fi_datetime_or_date_to_signing_dl_utc(
            sign_schema.last_signing_date
        )

        if not sign_schema.signers:
            raise ApiError("At least one signer is required", status=400)

        try:
            sales_agreement = self.db_session.scalars(
                select(FISalesAgreement).where(FISalesAgreement.id == params.id)
            ).one_or_none()

            if not sales_agreement:
                raise ApiError("Sales agreement not found", status=404)

            if sales_agreement.status != FISalesAgreementStatusEnum.VALIDATED:
                raise ApiError("Sales agreement cannot be sent for signing", status=400)

            formatted_context = recursive_format(
                FISalesAgreementRead.model_validate(sales_agreement).model_dump(), "fi"
            )

            renderer = DocumentRenderer()
            html_content = renderer.render_template(
                TemplatesEnum.SALES_AGREEMENT, formatted_context
            )
            cancellation_form_html = renderer.render_template(
                TemplatesEnum.CANCELLATION_FORM, {}
            )

            full_html = f"{html_content}{cancellation_form_html}"

            pdf = renderer.generate_pdf(full_html, TemplatesEnum.BASE_TEXT)

            dokobit_service = DokobitService()
            response = dokobit_service.create_document(
                name=f"Sales Agreement {sales_agreement.id}",
                files=[
                    FileData(
                        name=f"sales_agreement_{sales_agreement.id}.pdf",
                        content=pdf,
                    )
                ],
                signers=fetch_signers(
                    self.db_session,
                    [s.model_dump() for s in sign_schema.signers],
                    sign_schema.language,
                    require_ssn=True,
                ),
                comment=sign_schema.comment,
                deadline=end_of_day.strftime("%Y-%m-%dT%H:%M:%SZ"),
                require_ssn=True,
            )

            process_dokobit_create_response(
                self.db_session,
                self.user_id,
                DocumentSigningEntityType.FI_SALES_AGREEMENT,
                [sales_agreement.id],
                response,
                deadline=end_of_day,
            )
            sales_agreement.status = FISalesAgreementStatusEnum.PENDING_SIGNATURES
            self.db_session.flush()

            create_event_log(
                db_session=self.db_session,
                object_type=EventLogObjectType.FI_SALES_AGREEMENT,
                object_id=sales_agreement.id,
                actor_id=self.user_id,
                action=EventLogAction.UPDATED,
                data_after={
                    "status": sales_agreement.status,
                },
            )

            try:
                property_document_library = create_fi_property_document_library(
                    db_session=self.db_session,
                    user_id=self.user_id,
                    organization_id=self.organization_id,
                    property_id=sales_agreement.property_id,
                    is_admin=self.is_admin,
                )

                filename = (
                    generate_document_filename(
                        "Unsigned", f"Sales_Agreement_{sales_agreement.id}"
                    )
                    + ".pdf"
                )

                document_item = property_document_library.create_item_from_content(
                    content=pdf,
                    filename=filename,
                    mime_type="application/pdf",
                    document_type=DocumentType.FI_OTHER,
                    description=f"Original unsigned sales agreement saved before sending for signing",
                )

                sales_agreement_document_library = (
                    create_fi_sales_agreement_document_library(
                        db_session=self.db_session,
                        user_id=self.user_id,
                        organization_id=self.organization_id,
                        sales_agreement_id=sales_agreement.id,
                        is_admin=self.is_admin,
                    )
                )
                sales_agreement_document_library.add_to_library(document_item)

                logger.info(
                    "Successfully saved unsigned sales agreement to document library",
                    extra={
                        "sales_agreement_id": sales_agreement.id,
                        "property_id": sales_agreement.property_id,
                        "document_filename": filename,
                        "user_id": self.user_id,
                    },
                )
            except Exception as e:
                # Log the error but don't fail the signing process
                logger.error(
                    "Failed to save unsigned sales agreement to document library",
                    extra={
                        "sales_agreement_id": sales_agreement.id,
                        "property_id": sales_agreement.property_id,
                        "error": str(e),
                        "user_id": self.user_id,
                    },
                )

            self.request.response.status = 201
            return response

        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Database error while fetching sales agreement", status=500)

    @view_config(
        route_name="fi_sales_agreement.fetch_signing_status",
        request_method="GET",
        openapi_param_schema=IdParam,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Update signing status for a FI Sales Agreement",
            success_description="Signing status updated",
        ),
    )
    def update_fi_sales_agreement_signing_status(self):
        params = IdParam(**self.request.matchdict)

        try:
            document_external_ids = self.db_session.scalars(
                select(DocumentSigning.document_external_id).where(
                    get_document_signing_where_entity(
                        params.id, DocumentSigningEntityType.FI_SALES_AGREEMENT
                    ),
                    DocumentSigning.status != "completed",
                )
            ).all()

            if not document_external_ids:
                raise ApiError(
                    "No pending signing requests found for this agreement", status=404
                )

            dokobit_service = DokobitService()
            updated_count = 0

            for document_external_id in document_external_ids:
                dokobit_response = dokobit_service.get_signing_status(
                    document_external_id
                )

                update_signing_status(
                    self.db_session,
                    document_external_id,
                    dokobit_response.get("status"),
                    dokobit_response.get("signers", []),
                )
                updated_count += 1

            logger.info(
                "Updated signing statuses for FI sales agreement",
                extra={
                    "salesAgreementId": params.id,
                    "updatedCount": updated_count,
                },
            )

            self.request.response.status = 204

        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Database error while updating signing status", status=500)

        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Unexpected error occurred", status=500)

    @view_config(
        route_name="fi_sales_agreement.remind_signers",
        request_method="GET",
        openapi_param_schema=IdParam,
        openapi_metadata=create_openapi_metadata(
            tags=["FI Sales Agreement"],
            description="Remind signers for a FI Sales Agreement",
            success_description="Reminders sent",
        ),
    )
    def remind_fi_sales_agreement_signers(self):
        params = IdParam(**self.request.matchdict)

        try:
            document_signing = self.db_session.scalars(
                select(DocumentSigning).where(
                    get_document_signing_where_entity(
                        params.id, DocumentSigningEntityType.FI_SALES_AGREEMENT
                    )
                )
            ).one_or_none()

            if not document_signing:
                raise ApiError(
                    "No signing request found for this agreement", status=404
                )

            pending_signers = self.db_session.scalars(
                select(DocumentSigner.signing_external_id).where(
                    DocumentSigner.document_signing_id == document_signing.id,
                    DocumentSigner.status == "pending",
                )
            ).all()

            if not pending_signers:
                raise ApiError("No pending signers found", status=400)

            dokobit_service = DokobitService()
            for signer_token in pending_signers:
                dokobit_service.remind_signer(
                    document_signing.document_external_id, signer_token
                )

            self.request.response.status = 204

        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Database error while processing request", status=500)

        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Unexpected error occurred", status=500)

    def _handle_contact_acquisitions(
        self, fi_sales_agreement_id: int, contact_acquisitions_data: list
    ) -> None:
        """
        Handles contact acquisitions by removing items not in the list and adding/updating the rest.

        Args:
            fi_sales_agreement_id (int): The sales agreement ID
            contact_acquisitions_data (list): List of contact acquisition data
        """

        existing_acquisitions = self.db_session.scalars(
            select(FISalesAgreementContactAcquisition).where(
                FISalesAgreementContactAcquisition.fi_sales_agreement_id
                == fi_sales_agreement_id
            )
        ).all()

        existing_by_contact = {acq.contact_id: acq for acq in existing_acquisitions}

        new_contact_ids = {item.contact_id for item in contact_acquisitions_data}
        existing_contact_ids = set(existing_by_contact.keys())

        contact_ids_to_delete = existing_contact_ids - new_contact_ids
        if contact_ids_to_delete:
            for contact_id in contact_ids_to_delete:
                self.db_session.delete(existing_by_contact[contact_id])

        for item_data in contact_acquisitions_data:
            if item_data.contact_id in existing_by_contact:
                existing_acquisition = existing_by_contact[item_data.contact_id]
                update_model_from_schema(
                    existing_acquisition,
                    item_data,
                    exclude=["contact_id"],
                )
            else:
                new_acquisition = FISalesAgreementContactAcquisition(
                    fi_sales_agreement_id=fi_sales_agreement_id,
                    **item_data.model_dump(),
                )
                self.db_session.add(new_acquisition)
