from json import JSONDecodeError

import stripe
from pydantic import ValidationError
from pyramid.view import view_config
from sqlalchemy import func, or_, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload

from strandproperties.config import app_cfg
from strandproperties.constants import OrderBy
from strandproperties.logger import logger
from strandproperties.models.organization import Organization
from strandproperties.models.transaction import (
    ExpenseTransaction,
    ExpenseTransactionTarget,
)
from strandproperties.models.user import User
from strandproperties.schemas.base import PaginatedList, build_page_metadata
from strandproperties.schemas.transaction import (
    ExpenseTransactionCreate,
    ExpenseTransactionFilterParam,
    ExpenseTransactionOrderBy,
    ExpenseTransactionRead,
    ExpenseTransactionTargetRead,
    ExpenseTransactionType,
)
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError

stripe.api_key = app_cfg.stripe_api_key

FINAL_EXPENSE_STATUSES = {"succeeded", "canceled", "failed"}

STRIPE_STATUS_TO_EXPENSE_STATUS = {
    "succeeded": "succeeded",
    "canceled": "canceled",
    "requires_payment_method": "failed",
    "processing": "processing",
    "requires_action": "requires_action",
    "requires_confirmation": "requires_action",
}


class Expenses(BaseApi):
    @view_config(route_name="expense.create_expense_account", request_method="POST")
    def create_expense_account(self):
        user = self.db_session.scalar(select(User).where(User.id == self.user_id))
        if not user:
            raise ApiError("User not found", status=404)

        if not user.stripe_customer_id:
            try:
                stripe_customer = stripe.Customer.create(email=user.email)
                user.stripe_customer_id = stripe_customer.id
                self.db_session.flush()
            except Exception:
                logger.error("Failed to create Stripe customer", exc_info=True)
                raise ApiError("Could not create Stripe customer", status=500)
        else:
            try:
                stripe_customer = stripe.Customer.retrieve(user.stripe_customer_id)
            except Exception:
                logger.error(
                    "Failed to retrieve existing Stripe customer", exc_info=True
                )
                raise ApiError("Could not retrieve Stripe customer", status=500)

        try:
            setup_intent = stripe.SetupIntent.create(
                customer=user.stripe_customer_id,
                usage="off_session",
            )
        except Exception:
            logger.error("Failed to create setup intent", exc_info=True)
            raise ApiError("Could not create setup intent", status=500)

        return {"status": "ok", "clientSecret": setup_intent.client_secret}

    @view_config(
        route_name="expense.create_list",
        request_method="POST",
        permission="expense:create",
    )
    def create_expense_transaction(self, raw_body=None):
        raw_body = raw_body or self.request.json_body

        try:
            create_data = ExpenseTransactionCreate(**raw_body)
        except (JSONDecodeError, ValidationError) as exc:
            logger.error(
                f"Invalid transaction data: {exc}",
                extra={"requestId": self.request.request_id},
            )
            raise ApiError(f"Invalid request body: {exc}", status=400)

        user_id = create_data.user_id or self.user_id
        user = self.db_session.scalar(select(User).where(User.id == user_id))
        if not user:
            raise ApiError(f"User with id={user_id} not found", status=404)

        org = self.db_session.scalar(
            select(Organization).where(Organization.id == self.organization_id)
        )

        bal_attr = user.balance
        balance_val = bal_attr() if callable(bal_attr) else bal_attr or 0
        current_balance = balance_val or 0

        expense_amount = create_data.amount

        if current_balance >= expense_amount:
            expense_transaction = ExpenseTransaction(
                user_id=user_id,
                amount=expense_amount,
                type=create_data.type,
                description=create_data.description,
                status=create_data.status,
                stripe_transaction_id=None,
                meta={},
            )
            self.db_session.add(expense_transaction)
            self.db_session.flush()

            if create_data.targets:
                for target_data in create_data.targets:
                    target = ExpenseTransactionTarget(
                        expense_transaction_id=expense_transaction.id,
                        target_type=target_data.target_type,
                        target_id=target_data.target_id,
                        reference=target_data.reference,
                    )
                    self.db_session.add(target)
                self.db_session.flush()

            try:
                invoice_id = self._create_and_send_stripe_invoice(
                    user, expense_transaction
                )
                if invoice_id:
                    expense_transaction.meta = expense_transaction.meta or {}
                    expense_transaction.meta["stripe_invoice_id"] = invoice_id
                    self.db_session.flush()
            except Exception as e:
                logger.warning(
                    f"Failed to create Stripe invoice: {str(e)}", exc_info=True
                )

            payload = ExpenseTransactionRead.model_validate(expense_transaction)
            return payload.model_dump()

        else:
            deficit = expense_amount - current_balance
            deposit_amount = max(deficit, 100)

            meta = create_data.meta or {}
            payment_method_id = meta.get("payment_method_id")
            if not payment_method_id:
                try:
                    payment_methods = stripe.PaymentMethod.list(
                        customer=user.stripe_customer_id,
                        type="card",
                    )
                    if payment_methods.data:
                        payment_method_id = payment_methods.data[0].id
                    else:
                        raise ApiError(
                            "No payment method provided and no card on file", status=400
                        )
                except Exception:
                    logger.error("Error retrieving payment methods", exc_info=True)
                    raise ApiError("Could not retrieve payment methods", status=500)

            try:
                deposit_intent = stripe.PaymentIntent.create(
                    customer=user.stripe_customer_id,
                    amount=deposit_amount,
                    currency="eur",
                    payment_method=payment_method_id,
                    off_session=True,
                    confirm=True,
                    description="Deposit to cover expense",
                    transfer_data={"destination": org.stripe_account_id},
                    expand=["charges"],
                )
            except stripe.error.CardError as e:
                logger.error("Stripe card error during deposit", exc_info=True)
                raise ApiError(f"Stripe error: {str(e)}", status=400)
            except Exception:
                logger.error("Stripe deposit payment failed", exc_info=True)
                raise ApiError("Could not complete deposit", status=500)

            charges = getattr(deposit_intent, "charges", None)
            first_charge = charges.data[0] if charges and charges.data else None

            deposit_transaction = ExpenseTransaction(
                user_id=user_id,
                amount=deposit_amount,
                type=ExpenseTransactionType.DEPOSIT,
                description="Deposit for covering expense",
                status="succeeded",
                stripe_transaction_id=deposit_intent.id,
                meta={
                    "intent_status": deposit_intent.status,
                    "charge_status": first_charge.status if first_charge else None,
                    "receipt_url": first_charge.receipt_url if first_charge else None,
                },
            )
            self.db_session.add(deposit_transaction)

            expense_transaction = ExpenseTransaction(
                user_id=user_id,
                amount=expense_amount,
                type="expense",
                description=create_data.description,
                status="succeeded",
                stripe_transaction_id=None,
                meta={},
            )
            self.db_session.add(expense_transaction)
            self.db_session.flush()

            if create_data.targets:
                for target_data in create_data.targets:
                    target = ExpenseTransactionTarget(
                        expense_transaction_id=expense_transaction.id,
                        target_type=target_data.target_type,
                        target_id=target_data.target_id,
                        reference=target_data.reference,
                    )
                    self.db_session.add(target)
                self.db_session.flush()

            try:
                invoice_id = self._create_and_send_stripe_invoice(
                    user, expense_transaction
                )
                if invoice_id:
                    expense_transaction.meta = expense_transaction.meta or {}
                    expense_transaction.meta["stripe_invoice_id"] = invoice_id
                    self.db_session.flush()
            except Exception as e:
                logger.warning(
                    f"Failed to create Stripe invoice: {str(e)}", exc_info=True
                )

            deposit_payload = ExpenseTransactionRead.model_validate(deposit_transaction)
            expense_payload = ExpenseTransactionRead.model_validate(expense_transaction)

            return {
                "deposit_transaction": deposit_payload.model_dump(),
                "expense_transaction": expense_payload.model_dump(),
            }

    def _update_transaction_with_stripe(self, transaction: ExpenseTransaction):
        if not transaction.stripe_transaction_id:
            return transaction
        status_key = transaction.meta.get("intent_status")
        mapped_status = STRIPE_STATUS_TO_EXPENSE_STATUS.get(status_key)
        if transaction.meta and mapped_status in FINAL_EXPENSE_STATUSES:
            return transaction
        try:
            intent = stripe.PaymentIntent.retrieve(
                transaction.stripe_transaction_id,
                expand=["latest_charge.payment_method_details"],
            )
            charge = getattr(intent, "latest_charge", None)
            new_meta = {
                "intent_status": intent.status,
                "charge_status": getattr(charge, "status", None),
                "receipt_url": getattr(charge, "receipt_url", None),
                "card": (
                    charge.payment_method_details.get("card")
                    if charge and getattr(charge, "payment_method_details", None)
                    else None
                ),
            }
            if transaction.meta != new_meta:
                transaction.meta = new_meta
                self.db_session.add(transaction)
        except stripe.error.StripeError as exc:
            logger.warning(
                f"Stripe error while fetching intent {transaction.stripe_transaction_id}: {str(exc)}"
            )
        return transaction

    @view_config(
        route_name="expense.create_list",
        request_method="GET",
        permission="expense:read",
    )
    def list_expense_transactions(self):
        params = dict(self.request.params)
        try:
            filter_data = ExpenseTransactionFilterParam(**params)
        except ValidationError as exc:
            logger.error(
                f"Invalid query params: {exc}",
                extra={"requestId": self.request.request_id},
            )
            raise ApiError(f"Invalid query params: {exc}", status=400)

        page = filter_data.page or 1
        page_size = filter_data.page_size or 10
        offset = (page - 1) * page_size

        stmt = (
            select(ExpenseTransaction)
            .options(
                selectinload(ExpenseTransaction.user),
                selectinload(ExpenseTransaction.expense_targets),
            )
            .join(ExpenseTransaction.user)
        )

        if filter_data.keyword:
            keyword_lower = f"%{filter_data.keyword.lower()}%"
            stmt = stmt.where(
                or_(
                    func.lower(ExpenseTransaction.description).like(keyword_lower),
                    func.lower(func.concat(User.first_name, " ", User.last_name)).like(
                        keyword_lower
                    ),
                )
            )
        if not self.is_admin or bool(filter_data.filter_by_me):
            stmt = stmt.where(ExpenseTransaction.user_id == self.user_id)
        elif filter_data.user_id is not None:
            stmt = stmt.where(ExpenseTransaction.user_id == filter_data.user_id)
        if filter_data.types is not None:
            type_list = (
                filter_data.types
                if isinstance(filter_data.types, list)
                else [filter_data.types]
            )
            stmt = stmt.where(ExpenseTransaction.type.in_([t.value for t in type_list]))
        if filter_data.statuses is not None:
            status_list = (
                filter_data.statuses
                if isinstance(filter_data.statuses, list)
                else [filter_data.statuses]
            )
            stmt = stmt.where(
                ExpenseTransaction.status.in_([s.value for s in status_list])
            )
        if filter_data.sort_column and filter_data.sort_direction:
            stmt = stmt.order_by(
                getattr(
                    getattr(ExpenseTransaction, filter_data.sort_column),
                    filter_data.sort_direction,
                )()
            )
        elif filter_data.order_by == ExpenseTransactionOrderBy.ALPHABETICAL_ASC:
            stmt = stmt.order_by(ExpenseTransaction.description.asc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.ALPHABETICAL_DESC:
            stmt = stmt.order_by(ExpenseTransaction.description.desc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.AMOUNT_ASC:
            stmt = stmt.order_by(ExpenseTransaction.amount.asc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.AMOUNT_DESC:
            stmt = stmt.order_by(ExpenseTransaction.amount.desc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.LATEST:
            stmt = stmt.order_by(ExpenseTransaction.created_at.desc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.OLDEST:
            stmt = stmt.order_by(ExpenseTransaction.created_at.asc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.LATEST_UPDATED:
            stmt = stmt.order_by(ExpenseTransaction.updated_at.desc())
        elif filter_data.order_by == ExpenseTransactionOrderBy.OLDEST_UPDATED:
            stmt = stmt.order_by(ExpenseTransaction.updated_at.asc())
        else:
            stmt = stmt.order_by(ExpenseTransaction.created_at.desc())

        stmt = stmt.offset(offset).limit(page_size)
        transactions = self.db_session.scalars(stmt).all()

        transaction_read_list = []
        for transaction in transactions:
            transaction = self._update_transaction_with_stripe(transaction)
            targets = [
                ExpenseTransactionTargetRead(
                    id=t.id,
                    target_type=t.target_type,
                    target_id=t.target_id,
                    reference=t.reference,
                )
                for t in (transaction.expense_targets or [])
            ]
            transaction_read_list.append(
                ExpenseTransactionRead(
                    id=transaction.id,
                    user_id=transaction.user_id,
                    amount=transaction.amount,
                    type=transaction.type,
                    description=transaction.description,
                    meta=transaction.meta or {},
                    stripe_transaction_id=transaction.stripe_transaction_id,
                    created_at=transaction.created_at,
                    updated_at=transaction.updated_at,
                    status=transaction.status,
                    user=transaction.user,
                    targets=targets,
                )
            )
        try:
            self.db_session.flush()
        except SQLAlchemyError:
            logger.warning("Failed to persist updated Stripe meta to DB", exc_info=True)

        page_metadata = build_page_metadata(
            db=self.db_session,
            query=stmt,
            page=page,
            page_size=page_size,
        )
        return PaginatedList[ExpenseTransactionRead](
            metadata=page_metadata, records=transaction_read_list
        )

    @view_config(
        route_name="expense.card_details",
        request_method="GET",
        permission="expense:read",
    )
    def get_card_details(self):
        user = self.db_session.scalar(select(User).where(User.id == self.user_id))
        if not user:
            raise ApiError("User not found", status=404)
        if not user.stripe_customer_id:
            raise ApiError("User does not have a Stripe connection", status=400)
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=user.stripe_customer_id,
                type="card",
            )
        except Exception:
            logger.error("Error retrieving card details", exc_info=True)
            raise ApiError("Could not fetch card details", status=500)
        if payment_methods.data:
            return payment_methods.data[0].card.last4
        return ""

    @view_config(
        route_name="expense.disconnect_card",
        request_method="POST",
        permission="expense:update",
    )
    def disconnect_card(self):
        user = self.db_session.scalar(select(User).where(User.id == self.user_id))
        if not user:
            raise ApiError("User not found", status=404)
        if not user.stripe_customer_id:
            raise ApiError("User does not have a Stripe connection", status=400)

        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=user.stripe_customer_id,
                type="card",
            )
        except Exception:
            logger.error("Error retrieving payment methods", exc_info=True)
            raise ApiError("Could not fetch card details", status=500)

        if not payment_methods.data:
            raise ApiError("No card on file to disconnect", status=404)

        payment_method = payment_methods.data[0]

        try:
            stripe.PaymentMethod.detach(payment_method.id)
        except Exception:
            logger.error("Error detaching payment method", exc_info=True)
            raise ApiError("Could not disconnect card", status=500)

        return {"status": "ok", "message": f"Card {payment_method.id} disconnected"}

    def _create_and_send_stripe_invoice(
        self, user: User, expense_transaction: ExpenseTransaction
    ) -> str:
        if not user.stripe_customer_id:
            logger.warning(f"User {user.id} does not have a Stripe customer ID")
            return None

        if not user.email or user.email.strip() == "":
            logger.warning(f"User {user.id} does not have an email address")
            return None

        try:
            stripe.InvoiceItem.create(
                customer=user.stripe_customer_id,
                amount=expense_transaction.amount,
                currency="eur",
                description=expense_transaction.description or "Expense Transaction",
            )

            invoice = stripe.Invoice.create(
                customer=user.stripe_customer_id,
                collection_method="send_invoice",
                days_until_due=30,
                description=f"Invoice for expense transaction #{expense_transaction.id}",
            )

            finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)
            stripe.Invoice.send_invoice(finalized_invoice.id)

            logger.info(
                f"Stripe invoice {invoice.id} created and sent to {user.email} for expense transaction {expense_transaction.id}"
            )
            return invoice.id

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating invoice: {str(e)}", exc_info=True)
            raise e
        except Exception as e:
            logger.error(f"Unexpected error creating invoice: {str(e)}", exc_info=True)
            raise e
