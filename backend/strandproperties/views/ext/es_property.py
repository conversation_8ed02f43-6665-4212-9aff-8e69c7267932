from pyramid.exceptions import HTTPNotFound
from pyramid.security import NO_PERMISSION_REQUIRED
from pyramid.view import view_config
from sqlalchemy import and_, asc, case, desc, func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload

from strandproperties.constants import DataSource, OrderBy, Status
from strandproperties.models.property import Property, PropertyRealtor
from strandproperties.schemas.base import PaginatedList, build_page_metadata
from strandproperties.schemas.external import ExternalListRead, ExternalRead
from strandproperties.schemas.param import ExternalFilterParam
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class ESPropertyExternalViews(BaseApi):
    default_validators = ["validate_external_api_key"]

    @view_config(
        route_name="ext.es_property.list",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
    )
    def list_properties(self):
        try:
            params = ExternalFilterParam(**self.request.GET.mixed())
        except Exception:
            raise ApiError("Invalid params information")

        # TODO: Configure API key & agent_id properly in database
        if params.agent_id != 41:
            # TODO: Use pyramid http exceptions instead
            raise ApiError("Unauthorized", status=401)

        stmt = (
            select(Property)
            .options(
                selectinload(Property._property_type),
                selectinload(Property.listing_types),
                selectinload(Property.descriptions),
                selectinload(Property.images),
            )
            .where(Property.status == Status.PUBLISHED)
            .order_by(Property.is_strandified.desc())
            .order_by(
                case(
                    (Property.data_source == DataSource.STRAND, 1), else_=2
                ).asc()  # This will bring properties from Strand first (after the strandified ones)
            )
        )

        if params.agent_id:
            stmt = stmt.join(
                PropertyRealtor, Property.id == PropertyRealtor.property_id
            ).where(PropertyRealtor.user_id == params.agent_id)

        if params.order_by == OrderBy.LATEST:
            stmt = stmt.order_by(Property.created_at.desc())
        if params.order_by == OrderBy.OLDEST:
            stmt = stmt.order_by(Property.created_at.asc())
        if params.order_by == OrderBy.LOWEST_PRICE:
            stmt = stmt.order_by(
                asc(
                    case(
                        (
                            Property.price_sale != None,
                            Property.price_sale,
                        ),
                        else_=case(
                            (
                                Property.price_rent_long_term != None,
                                Property.price_rent_long_term,
                            ),
                            else_=case(
                                (
                                    Property.price_rent_short_term != None,
                                    Property.price_rent_short_term,
                                ),
                                else_=(Property.price_sale),
                            ),
                        ),
                    )
                )
            )
        if params.order_by == OrderBy.HIGHEST_PRICE:
            stmt = stmt.order_by(
                desc(
                    case(
                        (
                            Property.price_sale != None,
                            Property.price_sale,
                        ),
                        else_=case(
                            (
                                Property.price_rent_long_term != None,
                                Property.price_rent_long_term,
                            ),
                            else_=case(
                                (
                                    Property.price_rent_short_term != None,
                                    Property.price_rent_short_term,
                                ),
                                else_=(Property.price_sale),
                            ),
                        ),
                    )
                )
            )
        if params.order_by == OrderBy.LOWEST_PRICE_M2:
            stmt = stmt.order_by(Property.price_square_meter.asc())
        if params.order_by == OrderBy.HIGHEST_PRICE_M2:
            stmt = stmt.order_by(Property.price_square_meter.desc())
        if params.order_by == OrderBy.LATEST_UPDATED:
            stmt = stmt.order_by(None).order_by(Property.updated_at.desc())

        try:
            results = (
                self.db_session.scalars(
                    stmt.fetch(params.page_size).offset(
                        (params.page - 1) * params.page_size
                    )
                )
                .unique()
                .all()
            )
        except SQLAlchemyError as e:
            raise ApiError("Could not get list of properties")

        page_metadata = build_page_metadata(
            db=self.db_session,
            query=stmt,
            page=params.page,
            page_size=params.page_size,
        )

        return PaginatedList[ExternalListRead](
            metadata=page_metadata,
            records=results,
        )

    @view_config(
        route_name="ext.es_property.read",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
    )
    def read_property(self):
        reference = self.request.matchdict["reference_code"]

        try:
            property = (
                self.db_session.query(Property)
                .options(selectinload(Property._property_type))
                .options(selectinload(Property.realtor_users))
                .where(
                    and_(
                        func.lower(Property.reference) == func.lower(reference),
                        Property.status == Status.PUBLISHED,
                    )
                )
                .one()
            )
        except Exception as e:
            raise HTTPNotFound(
                "Property with this reference not found or not published to this portal"
            )

        # TODO: Configure API key and permissions properly in database
        if 41 not in [user.id for user in property.realtor_users]:
            raise HTTPNotFound(
                "Property with this reference not found or not published to this portal"
            )

        response = ExternalRead.model_validate(property)
        response.realtor_users = [
            user for user in property.realtor_users if user.id == 41
        ]

        return response
