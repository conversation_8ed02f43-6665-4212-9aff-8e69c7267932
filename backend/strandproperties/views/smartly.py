from typing import Any

from pyramid.response import Response
from pyramid.security import NO_PERMISSION_REQUIRED
from pyramid.view import view_config
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from strandproperties.config import app_cfg
from strandproperties.logger import logger
from strandproperties.models.advertisement import AdStatus, Advertisement
from strandproperties.services.smartly import SmartlyExportService, SmartlyStatsService
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class Smartly(BaseApi):
    default_validators = []
    validators = []

    @view_config(
        route_name="smartly.csv",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
    )
    def csv(self) -> Response:
        try:
            stmt = (
                select(Advertisement)
                .options(
                    joinedload(Advertisement.advertisement_images),
                    joinedload(Advertisement.organization),
                    joinedload(Advertisement.agent),
                    joinedload(Advertisement.property),
                    joinedload(Advertisement.organization),
                    joinedload(Advertisement.owner),
                )
                .where(Advertisement.status.in_([AdStatus.ACTIVE, AdStatus.IN_REVIEW]))
            )
            advertisements = self.db_session.scalars(stmt).unique().all()

            export_service = SmartlyExportService(self.db_session)
            csv_content = export_service.generate_csv(advertisements)
            response = Response(content_type="text/csv", body=csv_content)
            response.headers["Content-Disposition"] = (
                "attachment; filename=smartly_export.csv"
            )
            return response

        except SQLAlchemyError as e:
            self.db_session.rollback()
            logger.error(str(e))
            raise ApiError("Something went wrong while fetching ads", status=500)
        except Exception as e:
            logger.error(str(e))
            raise ApiError("An error occurred while generating the CSV.", status=500)

    @view_config(
        route_name="smartly.update_ads",
        request_method="POST",
        permission=NO_PERMISSION_REQUIRED,
    )
    def update_ads(self):
        try:
            smartly_stats_service = SmartlyStatsService(
                view_id=app_cfg.smartly_ad_view_id
            )
            updated = smartly_stats_service.update_advertisements(
                self.db_session, commit=False
            )
            return {"updated": updated, "message": f"Updated {updated} advertisements"}
        except Exception as e:
            logger.error(f"Error updating advertisements from Smartly: {str(e)}")
            raise ApiError(
                "An error occurred while updating advertisements.", status=500
            )
