from datetime import datetime

from pyramid.exceptions import HTTPNotFound
from pyramid.view import view_config
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.constants import (
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
)
from strandproperties.logger import logger
from strandproperties.models.document_library import DocumentType, OwnerType
from strandproperties.models.user import User
from strandproperties.schemas.brokerage_offer import BrokerageOfferRequest
from strandproperties.services.document_library.service import create_document_library
from strandproperties.services.documents.config import TemplatesEnum
from strandproperties.services.documents.document_renderer import DocumentRenderer
from strandproperties.services.documents.format_value import (
    format_percentage,
    format_price,
)
from strandproperties.utils.event_log import create_event_log
from strandproperties.utils.openapi_meta_object import create_openapi_metadata
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class BrokerageOfferView(BaseApi):
    @view_config(
        route_name="brokerage_offer.generate",
        request_method="POST",
        permission="brokerage_offer:create",
        openapi_request_schema=BrokerageOfferRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["Brokerage Offer"],
            description="Generate a PDF brokerage offer document and save to document library under contacts.",
            success_description="PDF generated successfully",
            responses={
                "200": {
                    "description": "PDF generated and saved to document library, returned for download (print flow)",
                    "content": {"application/pdf": {}},
                    "headers": {
                        "Content-Disposition": {
                            "description": "Attachment filename for the generated PDF",
                            "schema": {
                                "type": "string",
                                "example": 'filename="brokerage_offer_<property_address>.pdf"',
                            },
                        }
                    },
                },
                "201": {
                    "description": "PDF generated, saved to document library, and shared with contacts via email (email flow)",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "message": {"type": "string"},
                                    "item_ids": {
                                        "type": "array",
                                        "items": {"type": "integer"},
                                    },
                                },
                            }
                        }
                    },
                },
            },
        ),
    )
    def generate_brokerage_offer_pdf(self):
        try:
            request = BrokerageOfferRequest.model_validate(self.request.json_body)

            realtor = self.db_session.scalars(
                select(User).where(User.id == request.realtor_id)
            ).one_or_none()

            if not realtor:
                raise HTTPNotFound("Realtor not found")

            if not self.organization_id:
                raise ApiError("Organization not found", status=404)

            if not self.is_admin and self.user_id != realtor.id:
                raise ApiError("Unauthorized access", status=403)

            pdf_content = self._generate_brokerage_offer_pdf_content(
                realtor, request.model_dump()
            )

            created_items = []
            document_item = None

            for contact_id in request.contact_ids:
                try:
                    document_library = create_document_library(
                        db_session=self.db_session,
                        user_id=self.user_id,
                        organization_id=self.organization_id,
                        owner_type=OwnerType.CONTACT,
                        owner_id=contact_id,
                        is_admin=self.is_admin,
                    )

                    if not document_item:
                        filename = f"brokerage_offer_{request.address.replace(' ', '_').replace(',', '')}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
                        document_item = document_library.create_item_from_content(
                            content=pdf_content,
                            filename=filename,
                            mime_type="application/pdf",
                            document_type=DocumentType.FI_BROKERAGE_OFFER,
                            description=f"Brokerage offer for {request.address} - {realtor.first_name} {realtor.last_name}",
                        )

                    else:
                        document_library.add_to_library(document_item)

                    created_items.append((contact_id, document_item))

                except PermissionError:
                    logger.warning(
                        f"Access denied to document library for contact {contact_id}",
                        extra={"requestId": self.request.request_id},
                    )
                    continue

            if not document_item:
                raise ApiError(
                    "No document could be created. Check contact permissions.",
                    status=403,
                )

            for contact_id, _ in created_items:
                create_event_log(
                    db_session=self.db_session,
                    object_type=EventLogObjectType.BROKERAGE_OFFER,
                    object_id=contact_id,
                    action=EventLogAction.CREATED,
                    actor_id=request.realtor_id,
                    actor_type=EventLogActorType.USER,
                    details={
                        "generated_pdf": [
                            {
                                "document_id": item.id,
                                "contact_id": cid,
                            }
                            for cid, item in created_items
                        ]
                    },
                )

            failed_contacts = len(request.contact_ids) - len(created_items)

            self.request.response.status = 201 if not failed_contacts else 207
            self.request.response.content_type = "application/json"

            response_data = {
                "message": f"PDF generated, saved to document library",
                "item_ids": [document_item.id],
                "contacts_processed": len(created_items),
                "failed_contacts": failed_contacts,
            }

            return response_data
        except ValueError as e:
            error_message = str(e)
            if "Validation errors:" in error_message:
                clean_message = error_message.replace("Validation errors: ", "")
                raise ApiError(f"Validation failed: {clean_message}", status=400)
            else:
                raise ApiError("Invalid request data", status=400)
        except SQLAlchemyError as e:
            raise ApiError(f"Database error occurred: {e}", status=500)
        except HTTPNotFound as e:
            raise ApiError(f"Error generating brokerage offer: {e}", status=e.code)
        except ApiError as e:
            raise ApiError(f"Error generating brokerage offer: {e}", status=e.status)
        except Exception as e:
            raise ApiError(f"Error generating brokerage offer: {e}", status=500)

    def _safe_get_nested(self, obj, *keys, default=""):
        try:
            for key in keys:
                obj = obj.get(key) if isinstance(obj, dict) else getattr(obj, key, None)
                if obj is None:
                    return default
            return obj or default
        except (AttributeError, TypeError):
            return default

    def _generate_brokerage_offer_pdf_content(
        self, realtor: User, request_data: dict
    ) -> bytes:
        template_data = {
            "user": {
                "name": f"{realtor.first_name or ''} {realtor.last_name or ''}",
                "email": realtor.email or "",
                "phone": realtor.phone_number or "",
                "title": self._safe_get_nested(realtor, "details", "position"),
                "team_name": realtor.team_name or "",
                "description": request_data.get("realtor_description", ""),
                "business_name": self._safe_get_nested(
                    realtor, "details", "company", "company_name"
                ),
                "business_number": self._safe_get_nested(
                    realtor, "details", "company", "company_id"
                ),
                "profile_picture": (
                    f"{realtor.photo_url}?width=300&height=300&grayscale"
                    if realtor.photo_url
                    else ""
                ),
            },
            "property": {
                "address": request_data.get("address", ""),
                "price_inquiry": format_price(request_data.get("price_inquiry")),
                "commission": format_percentage(request_data.get("commission")),
                "notes": request_data.get("notes", ""),
            },
        }

        renderer = DocumentRenderer()
        html_content = renderer.render_template(
            TemplatesEnum.BROKERAGE_OFFER, template_data, auto_inject_images=True
        )
        return renderer.generate_pdf(
            html_content,
            TemplatesEnum.BASE_VISUAL,
            cover=True,
            sub_header="Välitystarjous",
        )
