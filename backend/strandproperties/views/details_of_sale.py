import base64
import datetime
import decimal
import io
import zipfile
from decimal import ROUND_HALF_UP, Decimal
from pathlib import Path

import requests
from dateutil.relativedelta import relativedelta
from pyramid.response import Response
from pyramid.view import view_config
from sqlalchemy import CHAR, and_, cast, delete, func, or_, select
from sqlalchemy.orm import joinedload

from strandproperties.config import app_cfg
from strandproperties.constants import (
    AttachmentDocumentType,
    DescriptionType,
    DoSDepositAccountType,
    DocumentType,
    DoSCommissionType,
    DOSTransactionOrderBy,
    ErrorCode,
    EventLogAction,
    EventLogObjectType,
    RoleType,
    SortEnum,
    SowiseDocumentUploadType,
    SowiseStatus,
    TemplateType,
    VatEnum,
    VisibilityType,
)
from strandproperties.libs.sowise import SowiseClient
from strandproperties.libs.utils import preprocess_params, update_model_from_schema
from strandproperties.logger import logger
from strandproperties.models.contact import Contact
from strandproperties.models.details_of_sale import (
    BuyerDetailsOfSale,
    DetailsOfSale,
    DetailsOfSaleInvoice,
    RealtorDetailsOfSale,
    SellerDetailsOfSale,
)
from strandproperties.models.document import Document, DocumentAttachments
from strandproperties.models.document_library import OwnerType
from strandproperties.models.offer import BuyerOffer, Offer
from strandproperties.models.office import Office
from strandproperties.models.property import Property
from strandproperties.models.user import Role, User
from strandproperties.schemas.base import PaginatedList, build_page_metadata
from strandproperties.schemas.common import MessageResponseSchema
from strandproperties.schemas.details_of_sale import (
    ApproveDetailsOfSale,
    BuyerDetailsOfSaleWithContact,
    BuyerTransferTaxReturnsErrorResponse,
    BuyerTransferTaxReturnsSuccessResponse,
    DetailsOfSaleAgentCreate,
    DetailsOfSaleBasicData,
    DetailsOfSaleCreateEdit,
    DetailsOfSaleDetailRead,
    DetailsOfSaleListRead,
    DetailsOfSaleRead,
    DetailsOfSaleRequestChanges,
    DetailsOfSaleRequestChangesRead,
    DetailsOfSaleStatus,
    RemoveDetailsOfSaleAttachments,
    ReportKpiRead,
    ReportKpiReadCompare,
    SellerDetailsOfSaleWithContact,
    SendTransferTaxReturnsParam,
    SendTransferTaxReturnsResponse,
    TransactionListRead,
    UpdateNameDetailsOfSaleAttachments,
)
from strandproperties.schemas.param import (
    DetailOfSaleListParam,
    ListTransactionParams,
    ReportKpiParams,
)
from strandproperties.schemas.sowise import DocumentConvert
from strandproperties.services.document_library.service import DocumentLibrary
from strandproperties.services.documents.format_value import format_price
from strandproperties.services.transfer_tax.send_transfer_tax_returns import (
    BuyerTransferTaxReturnsSuccessResult,
    DetailsOfSaleNotFoundError,
    SendTransferTaxReturns,
    UnableToSendTransferTaxReturnsError,
    UserNotAuthorizedError,
)
from strandproperties.utils.email import email_services
from strandproperties.utils.event_log import create_event_log
from strandproperties.utils.file.file import resolve_file_from_sowise
from strandproperties.utils.openapi_meta_object import create_openapi_metadata
from strandproperties.utils.pdf.pdf_data import (
    add_euros_suffix,
    format_commission_display,
    format_deposit_paid,
)
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError
from strandproperties.views.sowise import DocumentBase, SowiseMixin


class DetailsOfSaleViews(BaseApi, SowiseMixin):

    def __init__(self, request, context=None):
        super().__init__(request, context)

        self.sowise_client: SowiseClient = self.request.sowise_client

    def send_for_review(
        self, payload, reference: str, reviewer_office: Office, detail_of_sale_id: int
    ):
        list_reviewers = reviewer_office.users
        realtor = (
            self.db_session.query(User)
            .where(and_(User.id == self.user_id, User.is_active == True))
            .one()
        )
        receivers = [
            reviewer.email
            for reviewer in list_reviewers
            if any(RoleType.ADMIN in role.role for role in reviewer.roles)
        ]
        if not receivers:
            raise Exception(
                f"No admin users are assigned to the office with ID: {reviewer_office.id}."
            )
        template_path = Path(
            "strandproperties/templates/details_of_sale/send_for_review_email_template.html"
        )
        email_services.send_email(
            receivers=receivers,
            subject=f"{realtor.first_name} {realtor.last_name} requested review for {reference} Details of Sale",
            html_body=template_path.read_text(),
            context={
                "first_name": realtor.first_name,
                "last_name": realtor.last_name,
                "reference": reference,
                "note_to_reviewer": payload.notes,
                "link_of_dos_detail": f"{app_cfg.strand_client_base_url}/details-of-sale/{detail_of_sale_id}",
            },
        )
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=detail_of_sale_id,
            actor_id=self.user_id,
            action=EventLogAction.SENT_FOR_REVIEW,
            details={"receivers": receivers, "message": payload.notes},
        )

    def send_as_draft(
        self,
        dos_create_payload: DetailsOfSaleCreateEdit,
        reference: str,
        detail_of_sale_id: int,
    ):
        realtor = (
            self.db_session.query(User)
            .where(and_(User.id == self.user_id, User.is_active == True))
            .one()
        )

        receivers = app_cfg.admin_emails

        template_path = Path(
            "strandproperties/templates/details_of_sale/send_for_draft_email_template.html"
        )
        email_services.send_email(
            receivers=receivers,
            subject=f"[Draft] {realtor.first_name} {realtor.last_name} has sent a Details of Sale draft for {reference} for your review",
            html_body=template_path.read_text(),
            context={
                "first_name": realtor.first_name,
                "last_name": realtor.last_name,
                "reference": reference,
                "note_to_reviewer": dos_create_payload.notes,
                "link_of_dos_detail": f"{app_cfg.strand_client_base_url}/details-of-sale/{detail_of_sale_id}",
            },
        )

    def _get_reviewer_office(self, reviewer_office_id):
        """Retrieve reviewer office by ID"""
        reviewer_office = self.db_session.scalars(
            select(Office).where(Office.id == reviewer_office_id)
        ).first()
        if not reviewer_office:
            raise Exception(f"Reviewer Office ID: {reviewer_office_id} does not exist.")
        return reviewer_office

    def _build_metadata(self, reference: str, property: Property, payload) -> dict:
        return {
            "property": {
                "reference": reference,
                "address": (
                    property.private_info.get("location", {}).get("address")
                    if property
                    else " "
                ),
            },
            "offer_agreed_date": (
                payload.offer_agreed_date.strftime("%d/%m/%Y")
                if payload.offer_agreed_date
                else None
            ),
            "sales_price": add_euros_suffix(payload.sale_price),
            "deposit_paid": format_deposit_paid(
                payload.deposit_percentage, payload.deposit_amount
            ),
            "deposit_paid_date": (
                payload.deposit_paid_date.strftime("%d/%m/%Y")
                if payload.deposit_paid_date
                else None
            ),
            "date_ppc": (
                payload.ppc_date.strftime("%d/%m/%Y") if payload.ppc_date else None
            ),
            "notary_deadline": None,
            "notary_date_booked": (
                payload.notary_day_booked.strftime("%d/%m/%Y")
                if payload.notary_day_booked
                else None
            ),
            "total_commission": format_commission_display(
                payload.total_commission_amount,
                payload.total_commission_type,
                payload.language,
            ),
            "strand_commission": format_commission_display(
                payload.strand_commission_amount,
                payload.strand_commission_type,
                payload.language,
            ),
            "external_lead_with_share": payload.external_lead_percentage or " ",
            "notes": payload.notes or " ",
            "agent_1_with_share": " ",
            "agent_2_with_share": " ",
            "agent_3_with_share": " ",
            "agent_4_with_share": " ",
            "seller1": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
            "seller2": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
            "seller3": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
            "seller4": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
            "buyer1": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
            "buyer2": {
                "full_name": " ",
                "nie_number": " ",
                "address": " ",
                "nationality": " ",
                "contact_info": " ",
            },
        }

    def _populate_and_get_buyers_info(self, list_buyers, details_of_sale):
        """
        This method clears the existing buyers in the `details_of_sale` object
        and appends the new buyers from the provided `list_buyers`
        """
        buyers_info = {}
        details_of_sale.buyers.clear()  # Ensure buyers are cleared before adding new ones
        for idx, buyer in enumerate(list_buyers):
            details_of_sale.buyers.append(buyer)
            buyers_info[f"buyer{idx + 1}"] = {
                "full_name": buyer.name or " ",
                "nie_number": buyer.social_security_number or " ",
                "address": buyer.address or " ",
                "nationality": buyer.nationality or " ",
                "contact_info": buyer.email or " ",
            }
        return buyers_info

    def _get_list_sellers_info(self, payload, details_of_sale):
        sellers_info = {}
        details_of_sale.sellers.clear()  # Ensure sellers are cleared before adding new ones
        for idx, seller in enumerate(payload.sellers):
            seller_data = {
                **seller.model_dump(),
                "details_of_sale_id": details_of_sale.id,
            }
            seller_obj = SellerDetailsOfSale(**seller_data)
            details_of_sale.sellers.append(seller_obj)

            seller_contact = (
                self.db_session.query(Contact)
                .where(Contact.id == seller_obj.seller_id)
                .one()
            )
            sellers_info[f"seller{idx + 1}"] = {
                "full_name": seller_contact.name or " ",
                "nie_number": seller_contact.social_security_number or " ",
                "address": seller_contact.address or " ",
                "nationality": seller_contact.nationality or " ",
                "contact_info": seller_contact.email or " ",
            }
        return sellers_info

    @view_config(
        route_name="details_of_sale.create",
        request_method="POST",
        openapi_param_schema=DetailsOfSaleCreateEdit,
        openapi_response_schema=DetailsOfSaleRead,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def create_details_of_sale(self):
        """Create Details of Sale"""
        dos_create_payload = DetailsOfSaleCreateEdit(**self.request.json_body)
        property = self.db_session.scalars(
            select(Property).where(
                Property.reference == dos_create_payload.property_reference
            )
        ).first()

        # Check if the property has an approved offer
        offer = self.db_session.scalars(
            select(Offer).where(Offer.id == dos_create_payload.offer_id)
        ).first()

        dos_exists = self.db_session.scalars(
            select(DetailsOfSale).where(
                DetailsOfSale.custom_reference_property
                == dos_create_payload.property_reference
            )
        ).first()

        if not property and dos_exists:
            raise ApiError(
                status=404,
                message="Details of sale is exists for this custom reference property",
            )

        if offer:
            signed_documents = self.db_session.scalars(
                select(Document)
                .where(Document.id == offer.document_id)
                .where(Document.status == SowiseStatus.SIGNED)
            ).all()

            if not signed_documents and property:
                raise ApiError(
                    status=409,
                    message="Details of Sale can only be created for properties with at least one signed document.",
                )

        # Check if user is admin or assigned to property (either as realtor_user or assigned_to)
        is_assigned_realtor = not property or self.user_id in [
            agent.id for agent in property.realtor_users
        ]

        if not self.is_admin and not is_assigned_realtor:
            raise ApiError(
                status=403,
                message="You don't have permission to create details of sale for this property",
            )

        reference = (
            property.reference if property else dos_create_payload.property_reference
        )

        send_for_review = dos_create_payload.send_for_review

        (template_id, dos_name) = self._get_template_id_and_name_based_on_type(
            dos_create_payload.language, TemplateType.DETAILS_OF_SALE.value
        )
        document_name = f"{reference}_{dos_name}"

        if offer:
            list_buyers = self.db_session.scalars(
                select(Contact)
                .join(BuyerOffer, Contact.id == BuyerOffer.buyer_id)
                .where(BuyerOffer.offer_id == offer.id)
            ).all()
        else:
            list_buyers = self.db_session.scalars(
                select(Contact).where(Contact.id.in_(dos_create_payload.buyers))
            ).all()

        # Convert document
        convert_doc_params = DocumentConvert(
            name=document_name,
            include_children=None,
            parent=app_cfg.sowise_folder_details_of_sale,
        )
        resp = self.sowise_client.convert_document(template_id, convert_doc_params)
        document_id = resp.document_id

        document_base_params = DocumentBase(
            name=document_name,
            type=DocumentType.DETAILS_OF_SALE,
            visibility=VisibilityType.PUBLIC,
            sowise_id=document_id,
            status=(
                SowiseStatus.IN_REVIEW
                if (
                    send_for_review
                    and dos_create_payload.status == SowiseStatus.IN_REVIEW
                )
                else SowiseStatus.DRAFT
            ),
            language=dos_create_payload.language,
        )
        document = Document(**document_base_params.dict())
        self.db_session.add(document)
        self.db_session.flush()

        cleaned_payload = DetailsOfSaleBasicData.model_validate(
            dos_create_payload.model_dump()
        )
        details_of_sale = DetailsOfSale(
            **cleaned_payload.model_dump(),
            property_id=property.id if property else None,
            custom_reference_property=None if property else reference,
            document_id=document.id,
            created_by=self.user_id,
            offer_id=dos_create_payload.offer_id,
        )
        self.db_session.add(details_of_sale)

        buyers_info = self._populate_and_get_buyers_info(list_buyers, details_of_sale)

        self.db_session.flush()

        sellers_info = self._get_list_sellers_info(dos_create_payload, details_of_sale)

        if send_for_review and dos_create_payload.status == SowiseStatus.DRAFT:
            self.send_as_draft(dos_create_payload, reference, details_of_sale.id)

        if send_for_review and dos_create_payload.status == SowiseStatus.IN_REVIEW:
            reviewer_office = self._get_reviewer_office(
                dos_create_payload.reviewer_office_id
            )
            self.send_for_review(
                dos_create_payload, reference, reviewer_office, details_of_sale.id
            )

            create_event_log(
                db_session=self.db_session,
                object_type=EventLogObjectType.DETAILS_OF_SALE,
                object_id=details_of_sale.id,
                actor_id=self.user_id,
                action=EventLogAction.SENT_FOR_REVIEW,
                details={"receivers": [reviewer_office.name]},
            )

        details_of_sale_metadata = self._build_metadata(
            reference, property, dos_create_payload
        )
        # Update details of sale data with all relevant information
        details_of_sale_metadata.update({**sellers_info, **buyers_info})

        self.sowise_client.fill_document(
            document_id,
            self._convert_to_string_and_checkbox_unicode(details_of_sale_metadata),
        )
        details_of_sale.metadata_json = details_of_sale_metadata

        self._create_dos_proforma(
            details_of_sale, dos_create_payload.language, reference
        )

        # Send email to the buyer agent
        if details_of_sale and details_of_sale.offer_id:
            buyer_agent = self.db_session.scalars(
                select(User).where(User.id == offer.created_by)
            ).one_or_none()
            template_path = Path(
                "strandproperties/templates/details_of_sale/send_for_buyer_agent_template.html"
            )
            if buyer_agent:
                email_services.send_email(
                    receivers=[buyer_agent.email],
                    subject=f"Details of Sale created for {reference}",
                    html_body=template_path.read_text(),
                    context={
                        "first_name": buyer_agent.first_name,
                        "last_name": buyer_agent.last_name,
                        "seller_agent_name": f"{offer.created_user.first_name} {offer.created_user.last_name}",
                        "reference": reference,
                        "link_of_dos_detail": f"{app_cfg.strand_client_base_url}/details-of-sale/{details_of_sale.id}",
                    },
                )

        self.request.response.status = 201
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            actor_id=self.user_id,
            action=EventLogAction.CREATED,
            details={"sales_agreement": {"after": {"id": details_of_sale.id}}},
            data_after={"id": details_of_sale.id},
        )
        return DetailsOfSaleRead.model_validate(details_of_sale)

    @view_config(
        route_name="details_of_sale.agent.upload_invoice",
        request_method="POST",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def create_agent(self):
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale)
            .where(DetailsOfSale.id == details_of_sale_id)
            .with_for_update()
        ).one_or_none()
        if not self.is_admin and details_of_sale.created_by != self.user_id:
            raise ApiError(
                status=403,
                message="You don't have permission to create details of sale for this property",
            )
        if not details_of_sale:
            raise ApiError(f"Details of Sale ID: {details_of_sale_id} does not exist.")

        payload = DetailsOfSaleAgentCreate(**self.request.POST)
        realtor_details_of_sale = RealtorDetailsOfSale(
            **payload.model_dump(), details_of_sale_id=details_of_sale.id
        )
        details_of_sale.realtors.append(realtor_details_of_sale)
        agent_order = self.request.POST["order"]
        for name, data in self.request.POST.items():
            # Can upload multiple files with names starting with invoice, such as invoice_1, invoice.1 ...
            if not name.startswith("invoice"):
                continue
            # check attachment document exist or not before creating new one
            if isinstance(data, str):
                document_attachment = self.db_session.scalars(
                    select(DocumentAttachments).where(
                        cast(
                            DocumentAttachments.sowise_id,
                            CHAR(collation="utf8mb4_general_ci"),
                        )
                        == data
                    )
                ).first()
                if document_attachment:
                    continue
            file_name, file_type, file_content = resolve_file_from_sowise(
                self.sowise_client, data
            )

            resp = self.sowise_client.upload_document(
                name=file_name,
                filename=file_name,
                file=file_content,
                content_type=file_type,
                type=SowiseDocumentUploadType.FILE,
                folder=app_cfg.sowise_folder_details_of_sale,
            )
            document_attachment = DocumentAttachments(
                sowise_id=resp["document"]["id"],
                document_id=details_of_sale.document.id,
                type=file_type,
                name=file_name,
                attachment_type=AttachmentDocumentType.AGENT_INVOICES,
                realtor_id=realtor_details_of_sale.id,
            )
            self.db_session.add(document_attachment)

        meta_data = (
            self.sowise_client.get_document_info(details_of_sale.document.sowise_id)
            .get("document", {})
            .get("metadata", {})
            .get("strand", {})
        )
        meta_data.update(
            {
                f"agent_{agent_order}_with_share": (
                    realtor_details_of_sale.commission_percentage
                    or realtor_details_of_sale.commission_amount
                )
            }
        )
        self.sowise_client.fill_document(
            details_of_sale.document.sowise_id,
            self._convert_to_string_and_checkbox_unicode(meta_data),
        )
        details_of_sale.metadata_json = meta_data
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            actor_id=self.user_id,
            action=EventLogAction.CREATE_AGENT,
            details={
                "sales_agreement": {
                    "after": {
                        "id": details_of_sale.id,
                        "metadata_json": details_of_sale.metadata_json,
                    }
                }
            },
        )

        self.request.response.status = 201
        return {"message": "Create agent invoice successfully."}

    @view_config(
        route_name="details_of_sale.upload_attachments",
        request_method="POST",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def upload_attachments(self):
        """Upload attachments"""
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale)
            .where(DetailsOfSale.id == details_of_sale_id)
            .with_for_update()
        ).one_or_none()
        if not details_of_sale:
            raise ApiError(f"Details of Sale ID: {details_of_sale_id} does not exist.")

        valid_attachment_type = [item.value for item in AttachmentDocumentType]
        for name, file in self.request.POST.items():
            attachment_type = (
                AttachmentDocumentType.UNKNOW
                if name not in valid_attachment_type
                else name
            )
            file_name, file_type, file_content = resolve_file_from_sowise(
                self.sowise_client, file
            )
            resp = self.sowise_client.upload_document(
                name=file_name,
                filename=file_name,
                file=file_content,
                content_type=file_type,
                type=SowiseDocumentUploadType.FILE,
                folder=app_cfg.sowise_folder_details_of_sale,
            )
            logger.info("file uploaded: ", resp)
            document_attachment = DocumentAttachments(
                sowise_id=resp["document"]["id"],
                document_id=details_of_sale.document.id,
                type=file_type,
                name=file_name,
                attachment_type=attachment_type,
            )
            self.db_session.add(document_attachment)
        self.request.response.status = 201
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            actor_id=self.user_id,
            action=EventLogAction.UPLOAD_ATTACHMENTS,
            data_after={"id": details_of_sale.id},
            details={
                "uploaded_files": [
                    {
                        "name": attachment.name,
                        "type": attachment.type,
                        "attachment_type": attachment.attachment_type,
                    }
                    for attachment in details_of_sale.document.document_attachments
                ]
            },
        )
        return {"message": "Uploaded attachments successfully."}

    @view_config(
        route_name="details_of_sale.remove_attachments",
        request_method="POST",
        openapi_request_schema=RemoveDetailsOfSaleAttachments,
        openapi_response_schema=MessageResponseSchema,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def remove_attachments(self):
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()
        if not details_of_sale:
            raise ApiError(f"Details of Sale ID: {details_of_sale_id} does not exist.")
        payload = RemoveDetailsOfSaleAttachments(**self.request.json_body)
        for sowise_id in payload.sowise_ids:
            document_attachments = self.db_session.scalars(
                select(DocumentAttachments).where(
                    (DocumentAttachments.sowise_id == sowise_id)
                )
            ).first()
            self.db_session.delete(document_attachments)
            res = self.sowise_client.delete_document(sowise_id)
            if res.get("status") == "ERROR":
                logger.error(
                    "Has error when remove document with sowise id %s", sowise_id
                )
        self.request.response.status = 204
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            actor_id=self.user_id,
            action=EventLogAction.REMOVE_ATTACHMENTS,
            data_after={"id": details_of_sale.id},
            details={
                "removed_files": [
                    {
                        "name": attachment.name,
                        "type": attachment.type,
                        "attachment_type": attachment.attachment_type,
                    }
                    for attachment in details_of_sale.document.document_attachments
                ]
            },
        )
        return {"message": "Removed attachments successfully."}

    @view_config(
        route_name="details_of_sale.rename_attachments",
        request_method="POST",
        openapi_request_schema=UpdateNameDetailsOfSaleAttachments,
        openapi_response_schema=MessageResponseSchema,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def rename_attachments(self):
        payload: UpdateNameDetailsOfSaleAttachments = (
            UpdateNameDetailsOfSaleAttachments(**self.request.json_body)
        )
        for item in payload.update_infos:
            document_attachment = self.db_session.scalars(
                select(DocumentAttachments).where(
                    (DocumentAttachments.sowise_id == item.sowise_id)
                )
            ).first()

            if not document_attachment:
                raise ApiError(f"Document file cannot found.")

            res = self.sowise_client.document_rename(item.sowise_id, item.name)
            if res.get("status") == "ERROR":
                logger.error(
                    "Has error when rename document with sowise id %s", item.sowise_id
                )
                raise ApiError(f"Document cannot rename.")
            document_attachment.name = item.name

        self.request.response.status = 200
        return {"message": "Update name attachments successfully."}

    @view_config(
        route_name="details_of_sale.list",
        request_method="GET",
        openapi_param_schema=DetailOfSaleListParam,
        openapi_response_schema=DetailsOfSaleListRead,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def list_details_of_sale(self):

        params = DetailOfSaleListParam(
            **preprocess_params(self.request.GET.mixed(), ["status", "created_by"])
        )

        stmt = (
            select(DetailsOfSale)
            .join(DetailsOfSale.document)
            .join(DetailsOfSale.property)
            .options(
                joinedload(DetailsOfSale.created_user),
                joinedload(DetailsOfSale.document),
            )
            .order_by(DetailsOfSale.created_at.desc())
        )

        if params.reference_code:
            stmt = stmt.where(Property.reference == params.reference_code)

        if params.status:
            status_lower = [stt.lower() for stt in params.status]
            stmt = stmt.where(func.lower(Document.status).in_(status_lower))

        if self.is_admin:
            if params.created_by:
                stmt = stmt.where(DetailsOfSale.created_by.in_(params.created_by))
        else:
            stmt = stmt.where(DetailsOfSale.created_by == self.user_id)

        results = self.db_session.scalars(
            stmt.fetch(params.page_size).offset((params.page - 1) * params.page_size)
        ).all()

        page_metadata = build_page_metadata(
            self.db_session, query=stmt, page=params.page, page_size=params.page_size
        )

        return PaginatedList[DetailsOfSaleListRead](
            metadata=page_metadata, records=results
        )

    @view_config(
        route_name="details_of_sale.retrieve",
        request_method="DELETE",
    )
    def remove_details_of_sale(self):
        """Delete Details of Sale endpoint
        ---
        delete:
            description: Delete Details of Sale
            tags: ["Details of Sale"]
            security:
                - BearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                    type: string
            responses:
                204:
                    description: Removed Details of Sale successfully
        """
        details_of_sale_id = self.request.matchdict["id"]
        user_id = self.user_id
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(
                (DetailsOfSale.id == details_of_sale_id)
                & ((DetailsOfSale.created_by == user_id) | self.is_admin)
            )
        ).one()

        self.sowise_client.delete_document(details_of_sale.document.sowise_id)
        self.db_session.delete(details_of_sale)
        self.db_session.execute(
            delete(BuyerDetailsOfSale).where(
                BuyerDetailsOfSale.details_of_sale_id == details_of_sale_id
            )
        )
        self.db_session.execute(
            delete(SellerDetailsOfSale).where(
                SellerDetailsOfSale.details_of_sale_id == details_of_sale_id
            )
        )
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale_id,
            actor_id=self.user_id,
            action=EventLogAction.DELETED,
            details={"details_of_sale": {"before": {"id": details_of_sale_id}}},
            data_before={"id": details_of_sale_id},
        )
        return {"message": "Removed Details of Sale successfully."}

    @view_config(
        route_name="details_of_sale.status",
        request_method="POST",
        openapi_param_schema=DetailsOfSaleStatus,
        openapi_response_schema=DetailsOfSaleRead,
        permission="details_of_sale.status:update",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def set_status_details_of_sale(self):
        dos_update_status_payload = DetailsOfSaleStatus(**self.request.json_body)
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()
        if not details_of_sale:
            raise ApiError(f"Details of Sale ID: {details_of_sale_id} does not exist.")
        details_of_sale.document.status = dos_update_status_payload.status
        details_of_sale.document.notary_date = dos_update_status_payload.notary_date
        details_of_sale.document.comment = dos_update_status_payload.comment
        self.request.response.status = 200
        return DetailsOfSaleRead.model_validate(details_of_sale)

    @view_config(
        route_name="details_of_sale.request_changes",
        request_method="POST",
        openapi_param_schema=DetailsOfSaleRequestChanges,
        openapi_response_schema=DetailsOfSaleRequestChangesRead,
        permission="details_of_sale.request_changes:update",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def request_changes_details_of_sale(self):
        dos_update_payload = DetailsOfSaleRequestChanges(**self.request.json_body)
        message = dos_update_payload.message
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()
        if not details_of_sale:
            raise ApiError(f"Details of Sale ID: {details_of_sale_id} does not exist.")
        details_of_sale.document.status = SowiseStatus.REQUESTING_CHANGES.value
        property = details_of_sale.property
        reference = property.reference
        template_path = Path(
            "strandproperties/templates/details_of_sale/requests_change_template.html"
        )
        detail_of_sale_link = (
            f"{app_cfg.strand_client_base_url}/details-of-sale/{details_of_sale_id}"
        )
        email_services.send_email(
            receivers=[realtor.user.email for realtor in details_of_sale.realtors],
            subject=f"Admin requested changes for {reference} Details of sale",
            html_body=template_path.read_text(),
            context={
                "reference": reference,
                "detail_of_sale_link": detail_of_sale_link,
                "message": message,
            },
        )
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale_id,
            actor_id=self.user_id,
            action=EventLogAction.REQUEST_CHANGE,
            details={
                "receivers": [
                    realtor.user.email for realtor in details_of_sale.realtors
                ],
                "message": message,
            },
        )
        self.request.response.status = 200
        return DetailsOfSaleRequestChangesRead(id=details_of_sale_id, message=message)

    @view_config(
        route_name="details_of_sale.retrieve",
        request_method="PATCH",
        openapi_response_schema=DetailsOfSaleDetailRead,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def edit_details_of_sale(self):
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = (
            self.db_session.query(DetailsOfSale)
            .join(DetailsOfSale.document)
            .filter(
                DetailsOfSale.id == details_of_sale_id,
                or_(
                    Document.status.in_(
                        [
                            SowiseStatus.DRAFT,
                            SowiseStatus.IN_REVIEW,
                            SowiseStatus.REQUESTING_CHANGES,
                        ]
                    )
                ),
            )
            .options(joinedload(DetailsOfSale.document))
            .first()
        )
        if not self.is_admin and details_of_sale.created_by != self.user_id:
            raise ApiError(
                status=403,
                message="You don't have permission to edit details of sale for this property",
            )
        if not details_of_sale:
            raise ApiError("Detail of sale not found", status=404)
        self._remove_dos_proforma(details_of_sale)

        dos_edit_payload = DetailsOfSaleCreateEdit(**self.request.json_body)
        property = self.db_session.scalars(
            select(Property).where(
                Property.reference == dos_edit_payload.property_reference
            )
        ).first()
        reference = (
            property.reference if property else dos_edit_payload.property_reference
        )

        send_for_review = dos_edit_payload.send_for_review
        list_buyers = self.db_session.scalars(
            select(Contact).where(Contact.id.in_(dos_edit_payload.buyers))
        ).all()

        (template_id, dos_name) = self._get_template_id_and_name_based_on_type(
            dos_edit_payload.language, TemplateType.DETAILS_OF_SALE.value
        )
        document_name = f"{reference}_{dos_name}"
        sowise_document_id = details_of_sale.document.sowise_id
        if document_name != details_of_sale.document.name:
            convert_doc_params = DocumentConvert(
                name=document_name,
                include_children=None,
                parent=app_cfg.sowise_folder_details_of_sale,
            )
            resp = self.sowise_client.convert_document(template_id, convert_doc_params)
            sowise_document_id = resp.document_id
            self.sowise_client.delete_document(details_of_sale.document.sowise_id)
            details_of_sale.document.sowise_id = sowise_document_id
            details_of_sale.document.name = document_name
        details_of_sale.document.language = dos_edit_payload.language

        cleaned_payload = DetailsOfSaleBasicData.model_validate(
            dos_edit_payload.model_dump()
        )

        buyers_info = self._populate_and_get_buyers_info(list_buyers, details_of_sale)
        self.db_session.flush()

        sellers_info = self._get_list_sellers_info(dos_edit_payload, details_of_sale)

        old_agents = self.db_session.scalars(
            select(RealtorDetailsOfSale).where(
                RealtorDetailsOfSale.details_of_sale_id == details_of_sale.id
            )
        ).all()
        # remove agents and their invoices
        for agent in old_agents:
            for document_attachment in agent.document_attachments:
                # Keep documents in sowise so user can choose the exist document
                self.db_session.delete(document_attachment)
            self.db_session.delete(agent)
        agents_info = {}
        for agent in dos_edit_payload.agents or []:
            agent_order = agent.agent_order
            del agent.agent_order
            realtor_details_of_sale = RealtorDetailsOfSale(
                **agent.model_dump(), details_of_sale_id=details_of_sale.id
            )

            details_of_sale.realtors.append(realtor_details_of_sale)
            agents_info[f"agent_{agent_order}_with_share"] = (
                realtor_details_of_sale.commission_amount
                or realtor_details_of_sale.commission_percentage
            )

        details_of_sale_metadata = self._build_metadata(
            reference, property, dos_edit_payload
        )

        # Update details of sale data with all relevant information
        details_of_sale_metadata.update({**sellers_info, **buyers_info, **agents_info})
        self.sowise_client.fill_document(
            sowise_document_id,
            self._convert_to_string_and_checkbox_unicode(details_of_sale_metadata),
        )
        details_of_sale.metadata_json = details_of_sale_metadata

        changed_fields = update_model_from_schema(
            model_obj=details_of_sale,
            schema_obj=cleaned_payload,
        )
        if send_for_review and dos_edit_payload.status == SowiseStatus.IN_REVIEW:
            details_of_sale.document.status = SowiseStatus.IN_REVIEW
            reviewer_office = self._get_reviewer_office(
                dos_edit_payload.reviewer_office_id
            )
            self.send_for_review(
                dos_edit_payload, reference, reviewer_office, details_of_sale_id
            )

        self._create_dos_proforma(details_of_sale, dos_edit_payload.language, reference)

        self.request.response.status = 200

        def convert_dates_to_str(data):
            return {
                k: (
                    str(v)
                    if isinstance(v, (datetime.datetime, datetime.date))
                    else float(v) if isinstance(v, decimal.Decimal) else v
                )
                for k, v in data.items()
            }

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            actor_id=self.user_id,
            action=EventLogAction.UPDATED,
            details={
                "details_of_sale": {
                    "before": convert_dates_to_str(
                        {k: v["before"] for k, v in changed_fields.items()}
                    ),
                    "after": convert_dates_to_str(
                        {k: v["after"] for k, v in changed_fields.items()}
                    ),
                }
            },
            data_before=convert_dates_to_str(
                {k: v["before"] for k, v in changed_fields.items()}
            ),
            data_after=convert_dates_to_str(
                {k: v["after"] for k, v in changed_fields.items()}
            ),
        )
        return DetailsOfSaleDetailRead.model_validate(details_of_sale)

    @view_config(
        route_name="details_of_sale.retrieve",
        request_method="GET",
        openapi_response_schema=DetailsOfSaleDetailRead,
        openapi_metadata={
            "tags": ["Details of Sale detail"],
        },
    )
    def detail_details_of_sale(self):

        dos_id = self.request.matchdict["id"]

        dos = self.db_session.scalars(
            select(DetailsOfSale)
            .join(DetailsOfSale.document)
            .outerjoin(Offer, Offer.id == DetailsOfSale.offer_id)
            .options(
                joinedload(DetailsOfSale.document),
                joinedload(DetailsOfSale.buyers),
                joinedload(DetailsOfSale.sellers),
                joinedload(DetailsOfSale.review_office),
                joinedload(DetailsOfSale.offer),
            )
            .where(
                DetailsOfSale.id == dos_id,
                or_(
                    self.is_admin,
                    and_(not self.is_admin, DetailsOfSale.created_by == self.user_id),
                    and_(
                        DetailsOfSale.offer_id.isnot(None),
                        Offer.created_by == self.user_id,
                    ),
                ),
            )
        ).first()

        if not dos:
            raise ApiError("Detail of sale not found", status=404)

        dos_read = DetailsOfSaleDetailRead.model_validate(dos)
        dos_read.sellers = []
        dos_read.buyers = []

        if not dos.offer:
            # Show all information if there's no offer
            dos_read.sellers = [
                SellerDetailsOfSaleWithContact.from_orm(seller)
                for seller in dos.sellers
            ]
            dos_read.buyers = [
                BuyerDetailsOfSaleWithContact.from_orm(buyer) for buyer in dos.buyers
            ]
        else:
            # Apply visibility rules only when there's an offer
            if self.is_admin or dos.created_by == self.user_id:
                dos_read.sellers = [
                    SellerDetailsOfSaleWithContact.from_orm(seller)
                    for seller in dos.sellers
                ]

            if self.is_admin or dos.created_by != self.user_id:
                dos_read.buyers = [
                    BuyerDetailsOfSaleWithContact.from_orm(buyer)
                    for buyer in dos.buyers
                ]
        dos_read.offer_created_by = dos.offer.created_by if dos.offer else None

        return dos_read

    @view_config(
        route_name="details_of_sale.approve",
        request_method="POST",
        openapi_param_schema=ApproveDetailsOfSale,
        openapi_response_schema=DetailsOfSaleRead,
        openapi_metadata={
            "tags": ["Approve Details of Sale"],
        },
    )
    def approve_details_of_sale(self):
        if not self.is_admin:
            raise ApiError("Not authorize", status=403)

        dos_approve_payload = ApproveDetailsOfSale(**self.request.json_body)
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()
        if not details_of_sale:
            raise ApiError(
                f"Details of Sale ID: {details_of_sale_id} does not exist.", status=404
            )

        invoice_numbers_exist = []
        for seller in dos_approve_payload.sellers:
            if self._check_invoice_number_exists(seller.invoice_number):
                invoice_numbers_exist.append(seller.invoice_number)
        if invoice_numbers_exist:
            return Response(
                json_body={
                    "message": "Invoice number already exists.",
                    "code": ErrorCode.DOS_INVOICE_NUMBER_DUPLICATE,
                    "data": invoice_numbers_exist,
                },
                status_code=409,
            )

        for seller in dos_approve_payload.sellers:
            # invoice
            dos_seller = (
                self.db_session.query(SellerDetailsOfSale)
                .filter(SellerDetailsOfSale.id == seller.id)
                .first()
            )

            if dos_seller and dos_seller.details_of_sale_invoice:
                dos_seller.details_of_sale_invoice.invoice_date = (
                    dos_approve_payload.invoice_date
                )
                dos_seller.details_of_sale_invoice.invoice_due_date = (
                    dos_approve_payload.invoice_due_date
                )
                dos_seller.details_of_sale_invoice.invoice_number = (
                    seller.invoice_number
                )
                dos_seller.details_of_sale_invoice.issued_by = (
                    dos_approve_payload.issued_by
                )
                dos_seller.details_of_sale_invoice.agent_id = (
                    dos_approve_payload.agent_id
                )

        details_of_sale.document.status = SowiseStatus.APPROVED.value

        self._create_invoice_document(details_of_sale, dos_approve_payload.language)

        # Send email for seller agent and buyer agent
        template_path = Path(
            "strandproperties/templates/details_of_sale/details_of_sale_approved_template.html"
        )

        # agent_ids = [details_of_sale.created_by, details_of_sale.buyer_agent_id]
        agent_ids = [details_of_sale.created_by]
        for agent_id in agent_ids:
            self._send_email_for_agent(
                user_id=agent_id,
                details_of_sale=details_of_sale,
                template_path=template_path,
            )

        return DetailsOfSaleRead.model_validate(details_of_sale)

    @view_config(
        route_name="details_of_sale.download",
        request_method="GET",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def download(self):
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()

        if not details_of_sale:
            raise ApiError(
                f"Details of Sale with id: {details_of_sale_id} does not exist"
            )
        if details_of_sale.created_by != self.user_id and not self.is_admin:
            raise ApiError(f"Do not have permission to download Details of Sale")
        document_id = details_of_sale.document.sowise_id
        sowise_client = self.request.sowise_client

        url = f"{sowise_client.base_url}/documents/download/{document_id}/base64"
        r = requests.get(
            url,
            headers=sowise_client.headers,
        )

        r.raise_for_status()
        return r.json()

    @view_config(
        route_name="details_of_sale.download_attachments",
        request_method="GET",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def download_attachments(self):
        details_of_sale_id = self.request.matchdict["id"]
        details_of_sale = self.db_session.scalars(
            select(DetailsOfSale).where(DetailsOfSale.id == details_of_sale_id)
        ).one_or_none()

        if not details_of_sale:
            raise ApiError(
                f"Details of Sale with id: {details_of_sale_id} does not exist"
            )
        if details_of_sale.created_by != self.user_id and not self.is_admin:
            raise ApiError(f"Do not have permission to download Details of Sale")

        sowise_client = self.request.sowise_client

        attachments_file = details_of_sale.document.document_attachments
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_files:
            for attachment in attachments_file:
                url = f"{sowise_client.base_url}/documents/download/{attachment.sowise_id}/base64"
                r = requests.get(
                    url,
                    headers=sowise_client.headers,
                )
                filename = r.json().get("name", "").replace(".pdf", "")
                zip_files.writestr(
                    zinfo_or_arcname=f"{filename}.pdf",
                    data=base64.b64decode(r.json().get("content")),
                )
        zip_buffer.seek(0)

        res = Response(
            body=zip_buffer.getvalue(),
            content_type="application/zip",
        )
        res.headers["Content-Disposition"] = (
            f"attachment; filename={details_of_sale.property.reference}_dos_attachment.zip"
        )
        return res

    def _create_invoice_document(self, details_of_sale, language):
        # Convert document
        (template_id, dos_name) = self._get_template_id_and_name_based_on_type(
            language, TemplateType.DOS_INVOICE.value
        )

        for seller in details_of_sale.sellers:
            property_description = None
            if details_of_sale.property and details_of_sale.property.descriptions:
                property_description = next(
                    (
                        des
                        for des in details_of_sale.property.descriptions
                        if des.language == language
                    ),
                    None,
                )

            commission_amount, vat = self._cal_commission_and_vat_dos(
                details_of_sale, seller
            )

            prop_reference = (
                details_of_sale.property.reference
                if details_of_sale.property
                else details_of_sale.custom_reference_property
            )

            filled_data = self._prepare_filled_data_dos_invoice(
                commission_amount,
                details_of_sale,
                details_of_sale.property,
                property_description,
                prop_reference,
                vat,
            )

            filled_data["invoice"] = {
                "number": seller.details_of_sale_invoice.invoice_number or " ",
                "date": datetime.datetime.now().strftime("%d.%m.%Y"),
            }
            filled_data["agent"] = {
                "name": f"{seller.details_of_sale_invoice.agent.first_name or ''} {seller.details_of_sale_invoice.agent.last_name or ''}"
            }

            if details_of_sale.sellers:
                seller_1 = details_of_sale.sellers[0]
                filled_data["seller_1"] = {
                    "address": seller_1.seller.address or " ",
                    "nie": seller_1.seller.social_security_number or " ",
                    "name": seller_1.seller.name or " ",
                }
            if len(details_of_sale.sellers) > 1:
                seller_2 = details_of_sale.sellers[1]
                filled_data["seller_2"] = {
                    "address": seller_2.seller.address or " ",
                    "nie": seller_2.seller.social_security_number or " ",
                    "name": seller_2.seller.name or " ",
                }

            if details_of_sale.buyers:
                buyer_1 = details_of_sale.buyers[0]
                filled_data["buyer_1"] = {
                    "nie": buyer_1.social_security_number or " ",
                    "name": buyer_1.name or " ",
                }
            if len(details_of_sale.buyers) > 1:
                buyer_2 = details_of_sale.buyers[1]
                filled_data["buyer_2"] = {
                    "nie": buyer_2.social_security_number or " ",
                    "name": buyer_2.name or " ",
                }

            prop_ref = None
            if details_of_sale.property and details_of_sale.property.reference:
                prop_ref = details_of_sale.property.reference
            else:
                prop_ref = details_of_sale.custom_reference_property

            document_name = (
                f"{prop_ref}-Invoice-#{seller.details_of_sale_invoice.invoice_number}"
            )

            convert_doc_params = DocumentConvert(
                name=document_name,
                include_children=None,
                parent=app_cfg.sowise_folder_details_of_sale,
            )

            resp = self.sowise_client.convert_document(template_id, convert_doc_params)
            document_id = resp.document_id

            document_params = DocumentBase(
                name=document_name,
                type=DocumentType.DOS_INVOICE,
                visibility=VisibilityType.PUBLIC,
                sowise_id=document_id,
                status=SowiseStatus.PENDING,
                language=language,
            )
            document = Document(**document_params.dict())
            self.db_session.add(document)
            self.db_session.flush()

            self.sowise_client.fill_document(
                document_id,
                self._convert_to_string_and_checkbox_unicode(filled_data),
            )

            if details_of_sale.separate_invoice_for_each_seller:
                seller.details_of_sale_invoice.document_id = document.id
            else:
                for seller_ in details_of_sale.sellers:
                    seller_.details_of_sale_invoice.document_id = document.id
                    break
                break

        return {"status": "success"}

    def _remove_dos_proforma(self, details_of_sale):
        seller_details_of_sales = self.db_session.scalars(
            select(SellerDetailsOfSale).where(
                SellerDetailsOfSale.details_of_sale_id == details_of_sale.id
            )
        ).all()
        for seller_details_of_sale in seller_details_of_sales:
            self.db_session.delete(seller_details_of_sale)
        details_of_sale_invoices = self.db_session.scalars(
            select(DetailsOfSaleInvoice).where(
                DetailsOfSaleInvoice.details_of_sale_id == details_of_sale.id
            )
        ).all()
        for details_of_sale_invoice in details_of_sale_invoices:
            self.sowise_client.delete_document(
                details_of_sale_invoice.proforma.sowise_id
            )
            self.db_session.delete(details_of_sale_invoice)

    def _create_dos_proforma(self, details_of_sale, language, reference):

        # Convert document
        (template_id, dos_name) = self._get_template_id_and_name_based_on_type(
            language, TemplateType.DOS_PROFORMA.value
        )

        for seller in details_of_sale.sellers:
            property_description = None
            # Only get descriptions if the property exists
            if details_of_sale.property:
                property_description = next(
                    (
                        des
                        for des in details_of_sale.property.descriptions
                        if des.language == language
                    ),
                    None,
                )

            commission_amount, vat = self._cal_commission_and_vat_dos(
                details_of_sale, seller
            )

            property = details_of_sale.property

            filled_data = self._prepare_filled_data_dos_invoice(
                commission_amount,
                details_of_sale,
                property,
                property_description,
                reference,
                vat,
            )

            document_name = f"{reference}-Proforma"

            convert_doc_params = DocumentConvert(
                name=document_name,
                include_children=None,
                parent=app_cfg.sowise_folder_details_of_sale,
            )

            resp = self.sowise_client.convert_document(template_id, convert_doc_params)
            document_id = resp.document_id

            document_params = DocumentBase(
                name=document_name,
                type=DocumentType.DOS_PROFORMA,
                visibility=VisibilityType.PUBLIC,
                sowise_id=document_id,
                status=SowiseStatus.PENDING,
                language=language,
            )
            document = Document(**document_params.dict())
            self.db_session.add(document)
            self.db_session.flush()

            self.sowise_client.fill_document(
                document_id,
                self._convert_to_string_and_checkbox_unicode(filled_data),
            )

            dos_invoice = DetailsOfSaleInvoice(
                details_of_sale_id=details_of_sale.id,
            )
            self.db_session.add(dos_invoice)
            self.db_session.flush()

            if details_of_sale.separate_invoice_for_each_seller:
                seller.details_of_sale_invoice_id = dos_invoice.id
                seller.details_of_sale_invoice.proforma_id = document.id
            else:
                for seller_ in details_of_sale.sellers:
                    seller_.details_of_sale_invoice_id = dos_invoice.id
                    seller_.details_of_sale_invoice.proforma_id = document.id
                    break
                break

    def _prepare_filled_data_dos_invoice(
        self,
        commission_amount,
        details_of_sale,
        property_,
        property_description,
        reference,
        vat,
    ):
        filled_data = {
            "property": {
                "name": property_.title if property_ else " ",
                "reference": reference,
                "description": (
                    property_description.description or " "
                    if property_description
                    else " "
                ),
                "address": (
                    property_.private_info.get("location", {}).get("address", " ")
                    if property_
                    else " "
                ),
                "city": (
                    f"{property_.private_info.get('location', {}).get('postCode', ' ')} {property_.private_info.get('location', {}).get('city', ' ')}"
                    if property_
                    else " "
                ),
            },
            "dos": {
                "sale_price": (
                    format_price(
                        price=details_of_sale.sale_price, remove_currency_symbol=True
                    )
                    if details_of_sale.sale_price
                    else " "
                ),
                "deposit_amount": (
                    format_price(
                        price=details_of_sale.deposit_amount,
                        remove_currency_symbol=True,
                    )
                    if details_of_sale.deposit_amount
                    else " "
                ),
                "deposit_paid_date": (
                    details_of_sale.deposit_paid_date.strftime("%Y/%m/%d")
                    if details_of_sale.deposit_paid_date
                    else " "
                )
                or " ",
                "commission_amount": format_price(
                    commission_amount, remove_currency_symbol=True
                ),
                "vat": format_price(vat, remove_currency_symbol=True),
                "total": format_price(
                    commission_amount + vat,
                    remove_currency_symbol=True,
                ),
                "total_final": format_price(
                    commission_amount + vat - (details_of_sale.deposit_amount or 0),
                    remove_currency_symbol=True,
                ),
                "deposit_paid_statement": " ",
            },
            "seller_1": {
                "address": " ",
                "nie": " ",
                "name": " ",
            },
            "seller_2": {
                "address": " ",
                "nie": " ",
                "name": " ",
            },
            "buyer_1": {
                "nie": " ",
                "name": " ",
            },
            "buyer_2": {
                "nie": " ",
                "name": " ",
            },
        }
        if (
            details_of_sale.deposit_paid_date
            and details_of_sale.deposit_account_type
            == DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT
        ):
            filled_data["dos"][
                "deposit_paid_statement"
            ] = f"THE DEPOSIT (€ -{filled_data['dos']['deposit_amount']}) PAID TO STRAND CLIENTS ACCOUNT WAS PAID TO THE SELLER {filled_data['dos']['deposit_paid_date']} / EL DEPÓSITO (€ -{filled_data['dos']['deposit_amount']}) RECIBIDO EN LA CUENTA DE CLIENTES DE STRAND FUE PAGADO AL VENDEDOR EL {filled_data['dos']['deposit_paid_date']}"
        if details_of_sale.sellers:
            seller_1 = details_of_sale.sellers[0]
            filled_data["seller_1"] = {
                "address": seller_1.seller.address or " ",
                "nie": seller_1.seller.social_security_number or " ",
                "name": seller_1.seller.name or " ",
            }
        if len(details_of_sale.sellers) > 1:
            seller_2 = details_of_sale.sellers[1]
            filled_data["seller_2"] = {
                "address": seller_2.seller.address or " ",
                "nie": seller_2.seller.social_security_number or " ",
                "name": seller_2.seller.name or " ",
            }
        if details_of_sale.buyers:
            buyer_1 = details_of_sale.buyers[0]
            filled_data["buyer_1"] = {
                "nie": buyer_1.social_security_number or " ",
                "name": buyer_1.name or " ",
            }
        if len(details_of_sale.buyers) > 1:
            buyer_2 = details_of_sale.buyers[1]
            filled_data["buyer_2"] = {
                "nie": buyer_2.social_security_number or " ",
                "name": buyer_2.name or " ",
            }
        return filled_data

    def _cal_commission_and_vat_dos(self, details_of_sale, seller):
        commission_amount = Decimal(0)
        vat = Decimal(0)
        if details_of_sale.total_commission_type == DoSCommissionType.AMOUNT_PLUS_VAT:
            commission_amount = details_of_sale.total_commission_amount or 0
            vat = Decimal(commission_amount) * VatEnum.ES.value.normalize()
        if details_of_sale.total_commission_type == DoSCommissionType.PERCENT_PLUS_VAT:
            commission_amount = (
                Decimal(details_of_sale.sale_price or 0)
                * (Decimal(details_of_sale.total_commission_amount or 0))
                / 100
            )
            vat = Decimal(commission_amount) * VatEnum.ES.value.normalize()
        if (
            details_of_sale.total_commission_type
            == DoSCommissionType.PERCENT_VAT_INCLUDED
        ):
            commission_amount = (
                details_of_sale.sale_price
                * (details_of_sale.total_commission_amount or 0)
                / 100
            )
            vat = Decimal(0)
        if (
            details_of_sale.total_commission_type
            == DoSCommissionType.AMOUNT_VAT_INCLUDED
        ):
            commission_amount = details_of_sale.total_commission_amount
            vat = Decimal(0)
        if not details_of_sale.separate_invoice_for_each_seller:
            commission_amount = (
                Decimal(commission_amount or 0)
                * Decimal(seller.invoice_percentage or 0)
                / 100
            )
        commission_amount = (
            Decimal(commission_amount)
            .normalize()
            .quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        )
        vat = vat.normalize().quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        return int(commission_amount), int(vat)

    def _check_invoice_number_exists(self, invoice_number):
        dos_invoice = (
            self.db_session.query(DetailsOfSaleInvoice)
            .filter(DetailsOfSaleInvoice.invoice_number == invoice_number)
            .first()
        )

        if dos_invoice:
            return True
        return False

    @view_config(
        route_name="details_of_sale.transaction",
        request_method="GET",
        openapi_param_schema=ListTransactionParams,
        openapi_response_schema=TransactionListRead,
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def dos_transactions(self):
        params = ListTransactionParams(
            **preprocess_params(self.request.GET.mixed(), ["offices", "agents"])
        )

        stmt = (
            select(DetailsOfSale, Property, User)
            .outerjoin(DetailsOfSale.property)
            .outerjoin(DetailsOfSale.created_user)
        )

        stmt = self._apply_filters(
            stmt, params.from_time, params.to_time, params.offices, params.agents
        )

        results = self.db_session.execute(
            stmt.limit(params.page_size).offset((params.page - 1) * params.page_size)
        ).all()

        page_metadata = build_page_metadata(
            self.db_session, query=stmt, page=params.page, page_size=params.page_size
        )

        records = self._process_results(results, params)

        return PaginatedList[TransactionListRead](
            metadata=page_metadata, records=records
        )

    @view_config(
        route_name="details_of_sale.send_transfer_tax_returns",
        request_method="POST",
        permission="property:write",
        openapi_param_schema=SendTransferTaxReturnsParam,
        openapi_response_schema=SendTransferTaxReturnsResponse,
        openapi_metadata=create_openapi_metadata(
            tags=["Details of Sale"],
            description="Send transfer tax returns for a details of sale",
            success_description="Transfer tax returns were successfully sent.",
            success_status_code="200",
        ),
    )
    def send_transfer_tax_returns(self):
        try:
            params = SendTransferTaxReturnsParam(**self.request.matchdict)
            send_transfer_tax_returns = SendTransferTaxReturns(
                db_session=self.db_session,
                document_library=DocumentLibrary(
                    db_session=self.db_session,
                    user_id=self.user_id,
                    owner_type=OwnerType.FI_PROPERTY,
                    owner_id=params.id,
                ),
                api_client=app_cfg.get_transfer_tax_client(),
            )
            result = send_transfer_tax_returns.run(
                details_of_sale_id=params.id, user_id=self.user_id
            )
            return SendTransferTaxReturnsResponse(
                success=result.success,
                buyers=[
                    (
                        BuyerTransferTaxReturnsSuccessResponse(
                            buyer_id=buyer.buyer_id,
                            unique_identifier=buyer.unique_identifier,
                            document_id=buyer.document_id,
                        )
                        if isinstance(buyer, BuyerTransferTaxReturnsSuccessResult)
                        else BuyerTransferTaxReturnsErrorResponse(
                            buyer_id=buyer.buyer_id,
                            error_text=buyer.error_text,
                            error_description_fi=buyer.error_description_fi,
                            error_description_en=buyer.error_description_en,
                        )
                    )
                    for buyer in result.buyers
                ],
            )
        except DetailsOfSaleNotFoundError:
            raise ApiError("Details of sale not found", status=404)
        except UserNotAuthorizedError:
            raise ApiError("User not authorized", status=403)
        except UnableToSendTransferTaxReturnsError:
            raise ApiError("Unable to send transfer tax returns", status=409)

    @view_config(
        route_name="details_of_sale.report_kpi",
        request_method="GET",
        openapi_metadata={
            "tags": ["Details of Sale"],
        },
    )
    def report_kpi(self):
        params = ReportKpiParams(
            **preprocess_params(self.request.GET.mixed(), ["offices", "agents"])
        )

        stmt = (
            select(DetailsOfSale, Property, User)
            .outerjoin(DetailsOfSale.property)
            .outerjoin(DetailsOfSale.created_user)
        )

        stmt_current = self._apply_filters(
            stmt, params.from_time, params.to_time, params.offices, params.agents
        )
        results = self.db_session.execute(stmt_current).all()
        records = self._process_kpi_results(results, params)

        data = records
        if params.compare:
            data = ReportKpiReadCompare()
            data.current = records

            if not params.from_time:
                data.previous_month = ReportKpiRead()
                data.last_year = ReportKpiRead()
                return data

            input_from_time = datetime.datetime.strptime(params.from_time, "%Y-%m-%d")
            previous_from_date = (
                input_from_time - relativedelta(months=1) if params.from_time else None
            )
            previous_to_date = (
                input_from_time - relativedelta(days=1) if params.from_time else None
            )

            last_year_from_date = (
                input_from_time - relativedelta(years=1) if params.from_time else None
            )
            last_year_to_date = (
                input_from_time - relativedelta(years=1)
                if params.to_time
                else datetime.datetime(input_from_time.year - 1, 12, 31)
            )

            stmt_previous_equivalent_period = self._apply_filters(
                stmt,
                previous_from_date,
                previous_to_date,
                params.offices,
                params.agents,
            )
            stmt_last_year = self._apply_filters(
                stmt,
                last_year_from_date,
                last_year_to_date,
                params.offices,
                params.agents,
            )

            result_previous_equivalent_period = self.db_session.execute(
                stmt_previous_equivalent_period
            ).all()
            records_previous_equivalent_period = self._process_kpi_results(
                result_previous_equivalent_period, params
            )
            result_last_year = self.db_session.execute(stmt_last_year).all()
            records_last_year = self._process_kpi_results(result_last_year, params)

            data.previous_month = records_previous_equivalent_period
            data.last_year = records_last_year

        return data

    def _process_kpi_results(self, results, params):
        vat_val = (
            VatEnum.ES.value.normalize()
            if self.is_es_org
            else VatEnum.FI.value.normalize()
        )
        records = {} if params.by_month else None

        for dos, property_, user_ in results:
            transaction_ = self._build_kpi_transaction(dos, vat_val)

            if params.by_month:
                created_at = dos.created_at.strftime("%Y-%m")
                records = self._aggregate_monthly_records(
                    records, created_at, transaction_
                )
            else:
                records = self._aggregate_total_records(records, transaction_)

        if not params.by_month and not records:
            return ReportKpiRead()

        return records

    def _build_kpi_transaction(self, dos, vat_val):
        transaction_ = ReportKpiRead.model_validate(dos)

        sale_price = dos.sale_price or 0
        transaction_.total_commission = self._calculate_commission(
            dos.total_commission_amount, dos.total_commission_type, sale_price, vat_val
        )
        transaction_.total_commission_percent = (
            (transaction_.total_commission / sale_price) * 100 if sale_price > 0 else 0
        )

        transaction_.total_strand_commission_earned = self._calculate_commission(
            dos.strand_commission_amount,
            dos.strand_commission_type,
            sale_price,
            vat_val,
        )
        transaction_.strand_commission_percent = (
            (transaction_.total_strand_commission_earned / sale_price) * 100
            if sale_price > 0
            else 0
        )

        transaction_.total_agent_commission_earned = self._calculate_commission(
            dos.other_agency_commission_amount,
            dos.other_agency_commission_type,
            sale_price,
            vat_val,
        )

        transaction_.total_sale_price = round(float(sale_price or 0), 2)
        transaction_.total_commission = round(
            float(transaction_.total_commission or 0), 2
        )
        transaction_.total_commission_percent = round(
            float(transaction_.total_commission_percent or 0), 2
        )
        transaction_.total_strand_commission_earned = round(
            float(transaction_.total_strand_commission_earned or 0), 2
        )
        transaction_.strand_commission_percent = round(
            float(transaction_.strand_commission_percent or 0), 2
        )
        transaction_.total_agent_commission_earned = round(
            float(transaction_.total_agent_commission_earned or 0), 2
        )

        return transaction_

    def _aggregate_monthly_records(self, records, created_at, transaction_):
        if created_at in records:
            data = records[created_at]
            self._update_aggregate_data(data, transaction_)
            records[created_at] = data
        else:
            transaction_.total_number_of_transactions = 1
            records[created_at] = transaction_
        return records

    def _aggregate_total_records(self, records, transaction_):
        if records:
            self._update_aggregate_data(records, transaction_)
        else:
            records = transaction_
            records.total_number_of_transactions = 1
        return records

    def _update_aggregate_data(self, data, transaction_):
        data.total_number_of_transactions += 1
        data.total_sale_price = round(
            data.total_sale_price + transaction_.total_sale_price, 2
        )
        data.total_commission = round(
            data.total_commission + transaction_.total_commission, 2
        )
        data.total_commission_percent = round(
            float(data.total_commission * 100 / data.total_sale_price), 2
        )
        data.total_strand_commission_earned = round(
            data.total_strand_commission_earned
            + transaction_.total_strand_commission_earned,
            2,
        )
        data.strand_commission_percent = round(
            float(data.total_strand_commission_earned * 100 / data.total_sale_price), 2
        )
        data.total_agent_commission_earned = round(
            data.total_agent_commission_earned
            + transaction_.total_agent_commission_earned,
            2,
        )
        data.strand_commission_of_full_commission_percent = round(
            float(data.total_strand_commission_earned * 100 / data.total_commission), 2
        )

    def _apply_filters(self, stmt, from_time, to_time, offices, agents):
        result = stmt._clone()
        if from_time:
            result = result.where(DetailsOfSale.created_at >= from_time)
        if to_time:
            result = result.where(DetailsOfSale.created_at <= to_time)
        if offices:
            result = result.where(DetailsOfSale.reviewer_office_id.in_(offices))
        if agents:
            result = result.where(DetailsOfSale.created_by.in_(agents))

        if not self.is_admin:
            result = result.where(DetailsOfSale.created_by == self.user_id)

        return result

    def _process_results(self, results, params):
        vat_val = (
            VatEnum.ES.value.normalize()
            if self.is_es_org
            else VatEnum.FI.value.normalize()
        )
        records = []

        for dos, property_, user_ in results:
            transaction_ = self._build_transaction(dos, property_, vat_val)
            records.append(transaction_)

        return self._sort_records(records, params)

    def _build_transaction(self, dos, property_, vat_val):
        transaction_ = TransactionListRead.model_validate(dos)

        if property_:
            transaction_.transaction_reference = property_.reference
            transaction_.property_description = next(
                (
                    desc_.description
                    for desc_ in property_.descriptions
                    if desc_.type == DescriptionType.FULL.value
                ),
                None,
            )
        else:
            transaction_.transaction_reference = dos.custom_reference_property

        sale_price = dos.sale_price or 0
        transaction_.total_commission = self._calculate_commission(
            dos.total_commission_amount, dos.total_commission_type, sale_price, vat_val
        )
        transaction_.total_commission_percent = (
            (transaction_.total_commission / sale_price) * 100 if sale_price > 0 else 0
        )

        transaction_.strand_commission_earned = self._calculate_commission(
            dos.strand_commission_amount,
            dos.strand_commission_type,
            sale_price,
            vat_val,
        )

        transaction_.agent_commission_earned = self._calculate_commission(
            dos.other_agency_commission_amount,
            dos.other_agency_commission_type,
            sale_price,
            vat_val,
        )

        transaction_.sale_price = float(transaction_.sale_price or 0)
        transaction_.total_commission = float(transaction_.total_commission or 0)
        transaction_.total_commission_percent = float(
            transaction_.total_commission_percent or 0
        )
        transaction_.strand_commission_earned = float(
            transaction_.strand_commission_earned or 0
        )
        transaction_.agent_commission_earned = float(
            transaction_.agent_commission_earned or 0
        )

        return transaction_

    def _calculate_commission(self, amount, commission_type, sale_price, vat_val):
        if not amount:
            return 0

        if DoSCommissionType.AMOUNT_PLUS_VAT.value == commission_type:
            return amount + (amount * vat_val)
        elif DoSCommissionType.AMOUNT_VAT_INCLUDED.value == commission_type:
            return amount
        elif DoSCommissionType.PERCENT_PLUS_VAT.value == commission_type:
            commission = (amount * sale_price) / 100
            return commission + (commission * vat_val)
        elif DoSCommissionType.PERCENT_VAT_INCLUDED.value == commission_type:
            return (amount * sale_price) / 100
        return 0

    def _sort_records(self, records, params):
        desc_sort = params.sort_direction == SortEnum.DESC.value

        if params.sort_column:
            sort_key_map = {
                DOSTransactionOrderBy.TRANSACTION_REFERENCE: lambda x: x.transaction_reference,
                DOSTransactionOrderBy.PROPERTY_DESCRIPTION: lambda x: x.property_description,
                DOSTransactionOrderBy.TOTAL_COMMISSION: lambda x: x.total_commission,
                DOSTransactionOrderBy.SALES_PRICE: lambda x: x.sale_price,
                DOSTransactionOrderBy.TOTAL_COMMISSION_PERCENT: lambda x: x.total_commission_percent,
                DOSTransactionOrderBy.STRAND_COMMISSION_EARNED: lambda x: x.strand_commission_earned,
                DOSTransactionOrderBy.AGENT_COMMISSION_EARNED: lambda x: x.agent_commission_earned,
            }

            sort_key = sort_key_map.get(params.sort_column)
            if sort_key:
                records = sorted(
                    records,
                    key=lambda x: (sort_key(x) is None, sort_key(x)),
                    reverse=desc_sort,
                )

        return records

    def _send_email_for_agent(
        self,
        user_id,
        details_of_sale,
        template_path,
    ):
        user = self.db_session.scalars(
            select(User).where(User.id == user_id)
        ).one_or_none()
        if user:
            email_services.send_email(
                receivers=[user.email],
                subject=f"Details of Sale {details_of_sale.id} has been approved",
                html_body=template_path.read_text(),
                context={
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "reference": (
                        details_of_sale.property.reference
                        if details_of_sale.property
                        else details_of_sale.custom_reference_property
                    ),
                    "link_of_dos_detail": f"{app_cfg.strand_client_base_url}/details-of-sale/{details_of_sale.id}",
                },
            )
