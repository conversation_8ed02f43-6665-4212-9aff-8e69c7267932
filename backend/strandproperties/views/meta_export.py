import csv
import itertools
import time
from datetime import datetime, timezone
from io import <PERSON><PERSON>
from typing import Any, List, Union

from pyramid.response import Response
from pyramid.security import NO_PERMISSION_REQUIRED
from pyramid.view import view_config
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload, load_only, selectinload

from strandproperties.constants import ListingTypeEnum
from strandproperties.logger import logger
from strandproperties.models.area_levels import AreaLevel1, AreaLevel2
from strandproperties.models.fi_property import FIProperty
from strandproperties.models.image import Image
from strandproperties.models.listing_type import ListingType
from strandproperties.models.property import Property, PropertyDescription, PropertyType
from strandproperties.schemas.fi_property.fi_property_type import FIPropertyTypeEnum
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class MetaCsv(BaseApi):
    default_validators: List[Any] = []
    validators: List[Any] = []

    HEADERS: List[str] = [
        "home_listing_id",
        "name",
        "availability",
        "price",
        "image[0].url",
        "image[0].tag[0]",
        "url",
        "address.addr1",
        "address.city",
        "address.country",
        "address.postal_code",
        "latitude",
        "longitude",
        "neighborhood[0]",
        "description",
        "video[0].url",
        "video[0].tag[0]",
        "home_listing_group_id",
        "custom_label_0",
        "custom_label_1",
        "custom_label_2",
        "custom_label_3",
        "custom_label_4",
        "custom_number_0",
        "custom_number_1",
        "custom_number_2",
        "custom_number_3",
        "custom_number_4",
        "ac_type",
        "agent_name",
        "agent_company",
        "furnish_type",
        "tenure_type",
        "sale_type",
        "garden_type",
        "days_on_market",
        "fee_schedule_url",
        "heating_type",
        "laundry_type",
        "listing_type",
        "agent_rera_id",
        "property_rera_id",
        "num_baths",
        "num_beds",
        "num_rooms",
        "num_units",
        "parking_type",
        "partner_verification",
        "pet_policy",
        "min_price",
        "max_price",
        "property_type",
        "area_size",
        "built_up_area_size",
        "property_tax",
        "condo_fee",
        "coownership_charge",
        "parking_spaces",
        "area_unit",
        "year_built",
        "energy_rating_eu.grade",
        "energy_rating_eu.value",
        "co2_emission_rating_eu.grade",
        "co2_emission_rating_eu.value",
        "additional_fees_description",
        "num_pets_allowed",
        "land_area_size",
        "security_deposit",
        "holding_deposit",
        "application_fee",
        "pet_deposit",
        "pet_monthly_fee",
        "floor_types[0]",
        "unit_features[0]",
        "construction_status",
        "coownership_num_lots",
        "coownership_proceedings_status",
        "coownership_status",
        "special_offers[0]",
        "pet_restrictions[0]",
        "building_amenities[0]",
        "broker_fee",
        "first_month_rent",
        "last_month_rent",
        "utilities_included_in_rent[0]",
        "rental_room_type",
        "private_room_bathroom_type",
        "number_of_co_renters",
        "private_room_area_size",
        "virtual_tour_url",
        "address.addr2",
        "address.addr3",
        "address.city_id",
        "address.region",
        "address.unit_number",
        "applink.android_app_name",
        "applink.android_package",
        "applink.android_url",
        "applink.ios_app_name",
        "applink.ios_app_store_id",
        "applink.ios_url",
        "applink.ipad_app_name",
        "applink.ipad_app_store_id",
        "applink.ipad_url",
        "applink.iphone_app_name",
        "applink.iphone_app_store_id",
        "applink.iphone_url",
        "applink.windows_phone_app_id",
        "applink.windows_phone_app_name",
        "applink.windows_phone_url",
        "product_tags[0]",
        "product_tags[1]",
    ]

    @view_config(
        route_name="meta.export",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
    )
    def csv(self) -> Response:
        format_param = self.request.params.get("format", "tsv").lower()

        if format_param == "csv":
            delimiter = ","
            file_extension = "csv"
        else:
            delimiter = "\t"
            file_extension = "tsv"

        try:
            output = StringIO()
            writer = csv.writer(output, delimiter=delimiter)
            writer.writerow(self.HEADERS)
            try:
                spanish_stmt = (
                    select(Property)
                    .options(
                        load_only(
                            Property.id,
                            Property.reference,
                            Property.title,
                            Property.price_sale,
                            Property.currency,
                            Property.bathrooms,
                            Property.bedrooms,
                            Property.rooms_total,
                            Property.built_area,
                            Property.plot_area,
                            Property.latitude,
                            Property.longitude,
                            Property.certificate_consumption_rating,
                            Property.certificate_consumption_value,
                            Property.certificate_emission_rating,
                            Property.certificate_emission_value,
                            Property.built_year,
                            Property.parking_spaces,
                            Property.created_at,
                            Property.private_info,
                        ),
                        selectinload(Property.images).load_only(
                            Image.url, Image.is_hidden
                        ),
                        selectinload(Property.descriptions).load_only(
                            PropertyDescription.description,
                            PropertyDescription.language,
                        ),
                        selectinload(Property.listing_types).load_only(
                            ListingType.name
                        ),
                        joinedload(Property._area_level_1).load_only(AreaLevel1.name),
                        joinedload(Property._area_level_2).load_only(AreaLevel2.name),
                        joinedload(Property._property_type).load_only(
                            PropertyType.name
                        ),
                    )
                    .where(
                        getattr(
                            Property, "portals", {"is_strandproperties_enabled": False}
                        )["is_strandproperties_enabled"]
                        == True,
                        Property.listing_types.any(name=ListingTypeEnum.SALE),
                    )
                    .order_by(Property.id.desc())
                    .execution_options(stream_results=True)
                )
                # WARNING: TEMPORARY disable finnish properties
                #
                # from sqlalchemy.orm import selectinload, load_only, joinedload
                # from strandproperties.models.fi_property import FIProperty, FIPropertyType, FIRealty
                # from strandproperties.models.fi_address import FIAddress
                # from strandproperties.models.video import Stream, Tour
                # from strandproperties.models.image import Image
                # from strandproperties.models.fi_residential_share_overview import FIResidentialShareOverview
                # from strandproperties.models.fi_plot_overview import FIPlotOverview
                #
                # finnish_stmt = (
                #     select(FIProperty)
                #     .options(
                #         load_only(
                #             FIProperty.id,
                #             FIProperty.reference,
                #             FIProperty.created_at,
                #             FIProperty.latitude,
                #             FIProperty.longitude,
                #         ),
                #         joinedload(FIProperty.fi_realty)
                #             .load_only(
                #                 FIRealty.title,
                #                 FIRealty.selling_price,
                #                 FIRealty.currency_code,
                #             )
                #             .joinedload(FIRealty.fi_address)
                #             .load_only(
                #                 FIAddress.street_address,
                #                 FIAddress.municipality,
                #                 FIAddress.postal_code,
                #                 FIAddress.region,
                #             ),
                #         joinedload(FIProperty.fi_property_type).load_only(
                #             FIPropertyType.listing_type,
                #             FIPropertyType.property_type,
                #         ),
                #         selectinload(FIProperty.video_streams).load_only(Stream.url, Stream.is_hidden),
                #         selectinload(FIProperty.video_tours).load_only(Tour.url, Tour.is_hidden),
                #         selectinload(FIProperty.images).load_only(Image.url, Image.is_hidden),
                #         selectinload(FIProperty.descriptions).load_only(
                #             PropertyDescription.description,
                #             PropertyDescription.language,
                #         ),
                #         joinedload(FIProperty.fi_residential_share_overview).load_only(
                #             FIResidentialShareOverview.apartment,
                #         ),
                #         joinedload(FIProperty.fi_plot_overview).load_only(
                #             FIPlotOverview.area,
                #         ),
                #     )
                #     .where(
                #         FIProperty.portals["is_strandproperties_enabled"].as_boolean() == True,
                #         FIProperty.fi_property_type.has(listing_type=FIListingTypeEnum.SALE),
                #     )
                #     .order_by(FIProperty.created_at.desc())
                #     .execution_options(stream_results=True)
                # )
                # finnish_iter = (
                #     self.db_session.scalars(finnish_stmt).unique().yield_per(batch_size)
                # )
                #
                # To activate later, merge finnish_iter into result processing loop similar to spanish properties.

                batch_size = 1000
                total_processed = 0

                query_start = time.perf_counter()
                total_query_time = 0.0
                total_csv_time = 0.0

                result_iter = (
                    self.db_session.scalars(spanish_stmt).unique().yield_per(batch_size)
                )

                while True:
                    fetch_start = time.perf_counter()
                    batch_properties = list(itertools.islice(result_iter, batch_size))
                    fetch_duration = time.perf_counter() - fetch_start
                    total_query_time += fetch_duration

                    if not batch_properties:
                        logger.info("No more properties to process")
                        break

                    csv_batch_start = time.perf_counter()
                    for prop in batch_properties:
                        writer.writerow(self.create_row_data(prop))
                    csv_batch_duration = time.perf_counter() - csv_batch_start
                    total_csv_time += csv_batch_duration

                    total_processed += len(batch_properties)

                    logger.info(
                        "Batch %d: Query: %.2fs, CSV: %.2fs, Records: %d, Total: %d",
                        (total_processed // batch_size)
                        + (1 if total_processed % batch_size else 0),
                        fetch_duration,
                        csv_batch_duration,
                        len(batch_properties),
                        total_processed,
                    )

                total_duration = time.perf_counter() - query_start
                logger.info(
                    "CSV: Total processing took %.2fs (query time: %.2fs, CSV time: %.2fs) for %d properties",
                    total_duration,
                    total_query_time,
                    total_csv_time,
                    total_processed,
                )

            except SQLAlchemyError as e:
                logger.error(str(e))
                raise ApiError(
                    "Something went wrong while fetching properties", status=500
                )

        except Exception as e:
            logger.error(str(e))
            raise ApiError("An error occurred while generating the CSV.", status=500)

        output.seek(0)
        response = Response(content_type="text/csv", body=output.read())
        response.headers["Content-Disposition"] = (
            f"attachment; filename=meta_export.{file_extension}"
        )

        return response

    def _create_empty_row(self) -> List[str]:
        return [""] * len(self.HEADERS)

    def _set_field(self, row_data: List[str], field: str, value: str) -> None:
        row_data[self.HEADERS.index(field)] = value

    def _get_english_text(self, translations: List[dict], default: str = "") -> str:
        if not translations:
            return default
        return next(
            (t.get("text") for t in translations if t.get("language") == "en"), default
        )

    def _format_price(self, price: float, currency: str) -> str:
        return f"{price:.2f} {currency.upper()}"

    def _create_finnish_row(self, prop: FIProperty) -> List[str]:
        row_data = self._create_empty_row()

        self._set_field(row_data, "home_listing_id", str(prop.reference))
        self._set_field(row_data, "name", str(prop.reference))
        self._set_field(row_data, "availability", "available_soon")

        if prop.fi_realty and prop.fi_realty.selling_price:
            price = self._format_price(
                prop.fi_realty.selling_price, prop.fi_realty.currency_code or "EUR"
            )
            self._set_field(row_data, "price", price)
            self._set_field(row_data, "min_price", price)
            self._set_field(row_data, "max_price", price)

        if prop.images:
            visible_images = [img for img in prop.images if not img.is_hidden]
            if visible_images:
                self._set_field(row_data, "image[0].url", visible_images[0].url)
                self._set_field(row_data, "image[0].tag[0]", "Property")

        self._set_field(
            row_data,
            "url",
            f"https://strandproperties.com/single-property/?ref={prop.reference}",
        )

        if prop.fi_realty and prop.fi_realty.fi_address:
            address = prop.fi_realty.fi_address
            self._set_field(row_data, "address.addr1", address.street_address or "")
            self._set_field(row_data, "address.city", address.municipality or "")
            self._set_field(row_data, "address.country", "Finland")
            self._set_field(row_data, "address.postal_code", address.postal_code or "")

            if address.region:
                self._set_field(row_data, "address.region", address.region)
            elif address.municipality:
                self._set_field(row_data, "address.region", address.municipality)

        if prop.latitude:
            self._set_field(row_data, "latitude", str(prop.latitude))
        if prop.longitude:
            self._set_field(row_data, "longitude", str(prop.longitude))

        eng_desc = next(
            (d.description for d in prop.descriptions if d.language == "en"), None
        )
        if eng_desc:
            self._set_field(row_data, "description", eng_desc)

        if prop.video_streams:
            visible_videos = [v for v in prop.video_streams if not v.is_hidden]
            if visible_videos:
                self._set_field(row_data, "video[0].url", visible_videos[0].url)
                self._set_field(row_data, "video[0].tag[0]", "Property Tour")

        if prop.video_tours:
            visible_tours = [t for t in prop.video_tours if not t.is_hidden]
            if visible_tours:
                self._set_field(row_data, "virtual_tour_url", visible_tours[0].url)

        if prop.fi_realty and prop.fi_realty.agents:
            first_agent = prop.fi_realty.agents[0]
            if first_agent.name:
                self._set_field(row_data, "agent_name", first_agent.name)
            if first_agent.company:
                self._set_field(row_data, "agent_company", first_agent.company)

        if prop.fi_property_type:
            property_type = prop.fi_property_type.property_type
            if property_type in [
                FIPropertyTypeEnum.APARTMENT_HOUSE._value_,
                FIPropertyTypeEnum.WOODEN_HOUSE_APARTMENT._value_,
                FIPropertyTypeEnum.BALCONY_ACCESS_BLOCK._value_,
            ]:
                self._set_field(row_data, "property_type", "apartment")
            elif property_type in [
                FIPropertyTypeEnum.DETACHED_HOUSE._value_,
                FIPropertyTypeEnum.SEMI_DETACHED_HOUSE._value_,
                FIPropertyTypeEnum.SEPARATE_HOUSE._value_,
            ]:
                self._set_field(row_data, "property_type", "house")
            elif property_type == FIPropertyTypeEnum.ROW_HOUSE._value_:
                self._set_field(row_data, "property_type", "townhouse")
            elif property_type in [FIPropertyTypeEnum.COTTAGE_OR_VILLA._value_]:
                self._set_field(row_data, "property_type", "villa")
            elif property_type in [
                FIPropertyTypeEnum.BUSINESS_OR_INDUSTRIAL_PLOT._value_,
                FIPropertyTypeEnum.COMMERCIAL_PLOT._value_,
                FIPropertyTypeEnum.INDUSTRIAL_PLOT._value_,
                FIPropertyTypeEnum.STORAGE_PLOT._value_,
            ]:
                self._set_field(row_data, "property_type", "commercial")
            elif property_type in [
                FIPropertyTypeEnum.APARTMENT_PLOT._value_,
                FIPropertyTypeEnum.HOLIDAY_PLOT._value_,
                FIPropertyTypeEnum.HOUSE_PLOT._value_,
                FIPropertyTypeEnum.ROW_HOUSE_PLOT._value_,
            ]:
                self._set_field(row_data, "property_type", "land")
            else:
                self._set_field(row_data, "property_type", "other")

            self._set_field(
                row_data, "listing_type", prop.fi_property_type.listing_type._value_
            )

        area_value = None

        if (
            prop.fi_residential_share_overview
            and prop.fi_residential_share_overview.apartment
        ):
            apartment = prop.fi_residential_share_overview.apartment
            if apartment.get("area") and apartment["area"].get("total_area"):
                total_area = apartment["area"]["total_area"]
                if isinstance(total_area, dict):
                    area_value = total_area.get("value")

        if not area_value and prop.fi_plot_overview and prop.fi_plot_overview.area:
            area_dict = prop.fi_plot_overview.area
            if isinstance(area_dict, dict):
                area_value = area_dict.get("value")

        if area_value:
            self._set_field(row_data, "area_size", str(area_value))
            self._set_field(row_data, "built_up_area_size", str(area_value))

        self._set_field(row_data, "area_unit", "m²")

        if hasattr(prop, "created_at") and prop.created_at:
            days = (
                datetime.now(timezone.utc)
                - prop.created_at.replace(tzinfo=timezone.utc)
            ).days
            self._set_field(row_data, "days_on_market", str(days))

        return row_data

    def _create_spanish_row(self, prop: Property) -> List[str]:
        row_data = self._create_empty_row()

        self._set_field(row_data, "home_listing_id", str(prop.reference))
        self._set_field(row_data, "name", str(prop.reference))
        self._set_field(row_data, "availability", "available_soon")

        if prop.price_sale:
            price = self._format_price(prop.price_sale, prop.currency or "EUR")
            self._set_field(row_data, "price", price)
            self._set_field(row_data, "min_price", price)
            self._set_field(row_data, "max_price", price)

        if prop.images:
            visible_images = [img for img in prop.images if not img.is_hidden]
            if visible_images:
                self._set_field(row_data, "image[0].url", visible_images[0].url)
                self._set_field(row_data, "image[0].tag[0]", "Property")

        self._set_field(
            row_data,
            "url",
            f"https://strandproperties.com/single-property/?ref={prop.reference}",
        )

        location = (
            prop.private_info.get("location", {})
            if hasattr(prop, "private_info")
            else {}
        )
        self._set_field(row_data, "address.addr1", location.get("address", ""))
        self._set_field(
            row_data,
            "address.city",
            prop._area_level_1.name if prop._area_level_1 else "",
        )
        self._set_field(row_data, "address.country", "Spain")
        self._set_field(row_data, "address.postal_code", location.get("postCode", ""))

        if prop._area_level_1:
            self._set_field(row_data, "address.region", prop._area_level_1.name)
        elif prop._area_level_2:
            self._set_field(row_data, "address.region", prop._area_level_2.name)

        if prop.latitude:
            self._set_field(row_data, "latitude", str(prop.latitude))
        if prop.longitude:
            self._set_field(row_data, "longitude", str(prop.longitude))

        if prop._area_level_1:
            self._set_field(row_data, "neighborhood[0]", prop._area_level_1.name)

        eng_desc = next(
            (d.description for d in prop.descriptions if d.language == "en"), None
        )
        if eng_desc:
            self._set_field(row_data, "description", eng_desc)

        if prop.bathrooms:
            self._set_field(row_data, "num_baths", str(prop.bathrooms))
        if prop.bedrooms:
            self._set_field(row_data, "num_beds", str(prop.bedrooms))
        if prop.rooms_total:
            self._set_field(row_data, "num_rooms", str(prop.rooms_total))

        if prop.built_area:
            self._set_field(row_data, "area_size", str(prop.built_area))
            self._set_field(row_data, "built_up_area_size", str(prop.built_area))
        if prop.plot_area:
            self._set_field(row_data, "land_area_size", str(prop.plot_area))

        property_type = prop.property_type.lower() if prop.property_type else ""
        if (
            "apartment" in property_type
            or "flat" in property_type
            or "duplex" in property_type
            or "penthouse" in property_type
        ):
            self._set_field(row_data, "property_type", "apartment")
        elif "house" in property_type:
            self._set_field(row_data, "property_type", "house")
        elif "villa" in property_type:
            self._set_field(row_data, "property_type", "villa")
        elif "townhouse" in property_type:
            self._set_field(row_data, "property_type", "townhouse")
        elif "plot" in property_type or "land" in property_type:
            self._set_field(row_data, "property_type", "land")
        elif any(
            commercial in property_type
            for commercial in ["commercial", "office", "retail", "shop", "business"]
        ):
            self._set_field(row_data, "property_type", "commercial")
        else:
            self._set_field(row_data, "property_type", "other")

        if prop.certificate_consumption_rating:
            self._set_field(
                row_data, "energy_rating_eu.grade", prop.certificate_consumption_rating
            )
            if prop.certificate_consumption_value:
                self._set_field(
                    row_data,
                    "energy_rating_eu.value",
                    str(prop.certificate_consumption_value),
                )
        if prop.certificate_emission_rating:
            self._set_field(
                row_data,
                "co2_emission_rating_eu.grade",
                prop.certificate_emission_rating,
            )
            if prop.certificate_emission_value:
                self._set_field(
                    row_data,
                    "co2_emission_rating_eu.value",
                    str(prop.certificate_emission_value),
                )

        if prop.built_year:
            self._set_field(row_data, "year_built", str(prop.built_year))

        self._set_field(row_data, "area_unit", "m²")

        if prop.parking_spaces:
            self._set_field(row_data, "parking_spaces", str(prop.parking_spaces))

        if prop.listing_types:
            first_listing_type = prop.listing_types[0]
            self._set_field(row_data, "listing_type", first_listing_type.name)

        if hasattr(prop, "created_at") and prop.created_at:
            days = (
                datetime.now(timezone.utc)
                - prop.created_at.replace(tzinfo=timezone.utc)
            ).days
            self._set_field(row_data, "days_on_market", str(days))

        return row_data

    def create_row_data(self, prop: Union[Property, FIProperty]) -> List[str]:
        if isinstance(prop, FIProperty):
            return self._create_finnish_row(prop)
        else:
            return self._create_spanish_row(prop)
