from pyramid.exceptions import HTTPBadRequest
from pyramid.response import Response
from pyramid.view import view_config

from strandproperties.logger import logger
from strandproperties.schemas.brochure import GenerateBrochureRequest
from strandproperties.services.brochure.service import BrochureService
from strandproperties.utils.openapi_meta_object import create_openapi_metadata
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class BrochureView(BaseApi):
    @view_config(
        route_name="brochure.generate_pdf",
        request_method="POST",
        permission="brochure:create",
        openapi_request_schema=GenerateBrochureRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["Brochure"],
            description="Generate Brochure PDF for a property. Input JSON can contain property_information_data, and property_images_data. If data is not provided, the latest version of the data will be used.",
        ),
    )
    def generate_brochure_pdf(self) -> Response:
        """
        Generate a brochure PDF and return it as a response.
        """
        try:
            request = GenerateBrochureRequest.model_validate(self.request.json_body)

            brochure_service = BrochureService(
                db_session=self.db_session,
                user_id=self.user_id,
                organization_id=self.organization_id,
                is_admin=self.is_admin,
            )

            combined_pdf = brochure_service.generate_brochure_pdf(
                request.property_id,
                request.realtor_id,
                request.is_complete,
                request.theme,
                request.language,
                request.include_cover,
                request.cover_page_type,
                request.property_blueprint_image,
                request.property_images_data,
                request.property_information_data,
            )

            response = Response(
                body=combined_pdf,
                content_type="application/pdf",
                content_disposition=f'attachment; filename="brochure_{request.property_id}.pdf"',
            )
            return response

        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="brochure.save_brochure_draft",
        request_method="POST",
        permission="brochure:create",
        openapi_request_schema=GenerateBrochureRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["Brochure"],
            description="Save a draft of a brochure",
        ),
    )
    def save_brochure_draft(self) -> Response:
        try:
            request = GenerateBrochureRequest.model_validate(self.request.json_body)

            brochure_service = BrochureService(
                db_session=self.db_session,
                user_id=self.user_id,
                organization_id=self.organization_id,
                is_admin=self.is_admin,
            )

            brochure = brochure_service.save_brochure_draft(
                request.property_id,
                request.realtor_id,
                request.theme,
                request.language,
                request.include_cover,
                request.cover_page_type,
                request.property_blueprint_image,
                request.property_information_data,
                request.property_images_data,
            )

            return Response(
                status=200,
                json_body={
                    "message": "Brochure draft saved",
                    "property_id": brochure.property_id,
                    "version_number": brochure.version_number,
                },
            )

        except Exception as e:
            self._handle_api_error(e)

    def _handle_api_error(self, error: Exception):
        """Handle API errors and convert to HTTP responses."""
        logger.error(f"API Error: {error}")

        if isinstance(error, ApiError):
            raise error
        else:
            raise HTTPBadRequest(f"Request failed: {str(error)}")
