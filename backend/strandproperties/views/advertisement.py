from datetime import datetime, timezone
from typing import Any, Dict, Optional

from pydantic import ValidationError
from pyramid.security import NO_PERMISSION_REQUIRED
from pyramid.view import view_config
from sqlalchemy import and_, func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from strandproperties.constants import CountryCode, EventLogAction, EventLogObjectType
from strandproperties.logger import logger
from strandproperties.models.advertisement import (
    AdStatus,
    Advertisement,
    AdvertisementImage,
    AdvertisementPreviewSettings,
)
from strandproperties.models.event import Event
from strandproperties.models.organization import Organization
from strandproperties.schemas.advertisement import (
    AdType,
    AdvertisementCreate,
    AdvertisementPreviewSettingsRead,
    AdvertisementRead,
    AdvertisementUpdate,
)
from strandproperties.schemas.base import PaginatedList, build_page_metadata
from strandproperties.schemas.event import EventRead
from strandproperties.schemas.transaction import (
    ExpenseTransactionStatus,
    ExpenseTransactionTargetEntity,
    ExpenseTransactionType,
)
from strandproperties.utils.event_log import create_event_log
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError
from strandproperties.views.expense import Expenses

_SECONDS_PER_DAY: int = (
    24 * 60 * 60
)  # Number of seconds in a day; avoids the magic value 86400


class Advertisements(BaseApi):
    def _is_finland(self) -> bool:
        try:
            org = self.db_session.scalar(
                select(Organization).where(Organization.id == self.organization_id)
            )
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            return False
        return org.country_code == CountryCode.FINLAND

    def _convert_budget_to_cents(self, budget: float) -> float:
        return float(budget * 100)

    def _convert_response_to_euros(self, data: dict) -> dict:
        money_fields = [
            "budgetTotal",
            "budgetDaily",
            "cpm",  # cost per mille – cost per thousand impressions (values stored in cents)
            "cpc",  # cost per click (values stored in cents)
        ]

        for field in money_fields:
            if field in data and data[field] is not None:
                data[field] = float(data[field]) / 100
        return data

    def _get_advertisement_by_id(self, ad_id: int) -> Advertisement:
        query = (
            select(Advertisement)
            .options(
                joinedload(Advertisement.property),
                joinedload(Advertisement.fi_property),
            )
            .where(Advertisement.id == ad_id)
        )
        if not self.is_admin:
            query = query.where(Advertisement.owner_id == self.user_id)

        advertisement = self.db_session.scalars(query).one_or_none()
        if not advertisement:
            raise ApiError("Advertisement not found", status=404)
        return advertisement

    def _log_advertisement_event(
        self,
        advertisement: Advertisement,
        action: EventLogAction,
        data_before: Optional[Dict[str, Any]] = None,
        data_after: Optional[Dict[str, Any]] = None,
    ) -> None:
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.ADVERTISEMENT,
            object_id=advertisement.id,
            actor_id=self.user_id,
            action=action,
            data_before=data_before,
            data_after=data_after,
        )

    def _handle_status_change(
        self, advertisement: Advertisement, old_status: AdStatus, new_status: AdStatus
    ) -> None:
        if old_status == AdStatus.DRAFT and new_status != AdStatus.DRAFT:
            self._create_expense_transaction(advertisement)

    def _create_expense_transaction(self, advertisement: Advertisement) -> None:
        try:
            expenses_view = Expenses(self.request)

            targets = [
                {
                    "target_id": advertisement.id,
                    "target_type": ExpenseTransactionTargetEntity.ADVERTISEMENT,
                    "reference": None,
                }
            ]

            if advertisement.property_id and advertisement.property:
                targets.append(
                    {
                        "target_id": advertisement.property_id,
                        "target_type": ExpenseTransactionTargetEntity.PROPERTY,
                        "reference": advertisement.property.reference,
                    }
                )

            if advertisement.fi_property_id and advertisement.fi_property:
                targets.append(
                    {
                        "target_id": advertisement.fi_property_id,
                        "target_type": ExpenseTransactionTargetEntity.FI_PROPERTY,
                        "reference": advertisement.fi_property.reference,
                    }
                )

            expense_body = {
                "user_id": self.user_id,
                "amount": advertisement.budget_total,
                "type": ExpenseTransactionType.EXPENSE,
                "description": f"Expense transaction for advertisement: {advertisement.title}",
                "status": ExpenseTransactionStatus.ON_HOLD,
                "targets": targets,
            }
            expenses_view.create_expense_transaction(expense_body)

            deposit_body = {
                "user_id": self.user_id,
                "amount": advertisement.budget_total,
                "type": ExpenseTransactionType.DEPOSIT,
                "description": f"Deposit transaction for advertisement: {advertisement.title}",
                "status": ExpenseTransactionStatus.SUCCEEDED,
                "targets": targets,
            }
            expenses_view.create_expense_transaction(deposit_body)
        except Exception as e:
            logger.error(f"Expense creation failed: {e}", exc_info=True)
            raise ApiError(
                "Advertisement was created but expense creation failed", status=500
            )

    def _process_advertisement_images(
        self, advertisement: Advertisement, images_payload: list
    ) -> None:
        if not images_payload:
            return

        for idx, img in enumerate(images_payload):
            url = img.get("url")
            if url:
                try:
                    advertisement_image = AdvertisementImage(
                        url=url, order=idx, is_hidden=False
                    )
                    advertisement.advertisement_images.append(advertisement_image)
                except Exception as e:
                    logger.error(f"Error creating advertisement image: {e}")

    def _duplicate_advertisement_images(
        self, original_ad: Advertisement, new_ad: Advertisement
    ) -> None:
        if not original_ad.advertisement_images:
            return

        try:
            for original_image in original_ad.advertisement_images:
                try:
                    new_image = AdvertisementImage(
                        advertisement_id=new_ad.id,
                        url=original_image.url,
                        order=original_image.order,
                        is_hidden=original_image.is_hidden,
                    )
                    new_ad.advertisement_images.append(new_image)

                    logger.info(
                        f"Successfully duplicated image link for advertisement {new_ad.id}: {original_image.url}",
                        extra={"requestId": getattr(self.request, "request_id", None)},
                    )

                except Exception as image_error:
                    logger.error(
                        f"Error duplicating image {original_image.id}: {image_error}",
                        extra={"requestId": getattr(self.request, "request_id", None)},
                    )
                    continue

        except Exception as e:
            logger.error(
                f"Error duplicating advertisement images: {e}",
                extra={"requestId": getattr(self.request, "request_id", None)},
            )
            return

    def _calculate_budget_daily(
        self,
        budget_total: float,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> Optional[float]:
        if not (start_date and end_date and budget_total):
            return None

        if start_date.tzinfo is None:
            start_date = start_date.replace(tzinfo=timezone.utc)
        if end_date.tzinfo is None:
            end_date = end_date.replace(tzinfo=timezone.utc)

        delta_seconds = (end_date - start_date).total_seconds()
        days = max(1, int(delta_seconds // _SECONDS_PER_DAY) or 1)
        return budget_total / days

    @view_config(
        route_name="advertisement.list_create",
        request_method="POST",
        permission="advertisement:create",
    )
    def create(self):
        json_data = dict(self.request.json_body)

        json_data["owner_id"] = int(self.user_id)
        json_data["organization_id"] = self.organization_id

        if "budgetTotal" in json_data:
            json_data["budgetTotal"] = self._convert_budget_to_cents(
                json_data["budgetTotal"]
            )

        # For AGENT type ads, agentId refers to user.id directly
        if json_data.get("agentId") and json_data.get("type") == "AGENT":
            json_data["agentId"] = int(json_data["agentId"])

        if self._is_finland():
            json_data["fiPropertyId"] = (
                int(json_data["propertyId"]) if json_data.get("propertyId") else None
            )

        if not json_data.get("title"):
            current_date = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
            json_data["title"] = f"Draft - {current_date}"

        for date_field in ["startDate", "endDate"]:
            if date_field in json_data and not json_data[date_field]:
                del json_data[date_field]

        try:
            params = AdvertisementCreate(**json_data)
        except ValidationError as e:
            logger.error(
                "Validation error: %s", e, extra={"requestId": self.request.request_id}
            )
            raise ApiError("Invalid advertisement data")

        advertisement = Advertisement(
            **params.dict(
                exclude={
                    "owner_id",
                    "agent_id",
                    "organization_id",
                    "advertisement_images",
                    "event",
                }
            ),
            owner_id=self.user_id,
            organization_id=self.organization_id,
        )

        advertisement.budget_daily = self._calculate_budget_daily(
            advertisement.budget_total,
            advertisement.start_date,
            advertisement.end_date,
        )

        self._process_advertisement_images(
            advertisement, self.request.json_body.get("advertisementImages")
        )

        try:
            self.db_session.add(advertisement)
            self.db_session.flush()
            self.db_session.refresh(advertisement)

            self._log_advertisement_event(
                advertisement,
                EventLogAction.CREATED,
                data_after=AdvertisementRead.model_validate(advertisement).model_dump(),
            )

            if advertisement.status != AdStatus.DRAFT:
                self.db_session.refresh(advertisement, ["property", "fi_property"])
                self._create_expense_transaction(advertisement)

        except SQLAlchemyError as e:
            self.db_session.rollback()
            logger.error(
                f"Database error: {e}", extra={"requestId": self.request.request_id}
            )
            raise ApiError("Could not create advertisement")

        self.request.response.status = 201
        return AdvertisementRead.model_validate(advertisement)

    @view_config(
        route_name="advertisement.list_create",
        request_method="GET",
        permission="advertisement:read",
    )
    def list(self):
        page = int(self.request.params.get("page", 1))
        page_size = int(self.request.params.get("pageSize", 10))
        sort_column = self.request.params.get("sortColumn")
        sort_direction = self.request.params.get("sortDirection")
        types = self.request.params.getall("types")
        statuses = self.request.params.getall("status")
        languages = self.request.params.getall("languages")
        keyword = self.request.params.get("keyword")
        property_id = self.request.params.get("propertyId")

        stmt = select(Advertisement).where(
            Advertisement.organization_id == self.organization_id,
            Advertisement.status != AdStatus.CANCELLED,
        )
        if not self.is_admin:
            stmt = stmt.where(Advertisement.owner_id == self.user_id)
        if types:
            stmt = stmt.where(Advertisement.type.in_(types))
        if statuses:
            stmt = stmt.where(Advertisement.status.in_(statuses))
        if languages:
            stmt = stmt.where(Advertisement.language.in_(languages))
        if keyword:
            stmt = stmt.where(
                func.lower(Advertisement.title).like(f"%{keyword.lower()}%")
            )
        if property_id:
            if self._is_finland():
                stmt = stmt.where(Advertisement.fi_property_id == property_id)
            else:
                stmt = stmt.where(Advertisement.property_id == property_id)

        if sort_column and sort_direction:
            stmt = stmt.order_by(
                getattr(getattr(Advertisement, sort_column), sort_direction)()
            )
        else:
            stmt = stmt.order_by(Advertisement.created_at.desc())

        results = (
            self.db_session.scalars(
                stmt.limit(page_size).offset((page - 1) * page_size)
            )
            .unique()
            .all()
        )
        page_metadata = build_page_metadata(
            db=self.db_session, query=stmt, page=page, page_size=page_size
        )

        return PaginatedList[AdvertisementRead](
            metadata=page_metadata,
            records=[
                AdvertisementRead.model_validate(ad).model_dump(by_alias=True)
                | self._convert_response_to_euros(
                    AdvertisementRead.model_validate(ad).model_dump(by_alias=True)
                )
                for ad in results
            ],
        )

    @view_config(
        route_name="advertisement.read_edit_delete",
        request_method="GET",
        permission="advertisement:read",
    )
    def read(self):
        ad_id = self.request.matchdict["id"]
        stmt = select(Advertisement).options(joinedload(Advertisement.owner))

        if not self.is_admin:
            stmt = stmt.where(
                and_(Advertisement.id == ad_id, Advertisement.owner_id == self.user_id)
            )
        else:
            stmt = stmt.where(Advertisement.id == ad_id)

        advertisement = self.db_session.scalars(stmt).one_or_none()
        if not advertisement:
            raise ApiError("Advertisement not found", status=404)

        return self._convert_response_to_euros(
            AdvertisementRead.model_validate(advertisement).model_dump(by_alias=True)
        )

    @view_config(
        route_name="advertisement.read_edit_delete",
        request_method="PATCH",
        permission="advertisement:update",
    )
    def update(self):
        advertisement = self._get_advertisement_by_id(int(self.request.matchdict["id"]))

        json_data = dict(self.request.json_body)

        for key, value in json_data.items():
            if isinstance(value, str) and not value.strip():
                json_data[key] = None

        if "budgetTotal" in json_data:
            json_data["budgetTotal"] = self._convert_budget_to_cents(
                json_data["budgetTotal"]
            )

        data_before = AdvertisementRead.model_validate(advertisement).model_dump()

        try:
            params = AdvertisementUpdate(**json_data)
        except ValidationError:
            raise ApiError("Invalid data provided")

        advertisement_images = json_data.get("advertisementImages")
        if advertisement_images is not None:
            advertisement.advertisement_images = []
            self._process_advertisement_images(advertisement, advertisement_images)

        for field, value in params.dict(
            exclude_unset=True, exclude={"advertisement_images"}
        ).items():
            setattr(advertisement, field, value)

        advertisement.budget_daily = self._calculate_budget_daily(
            advertisement.budget_total,
            advertisement.start_date,
            advertisement.end_date,
        )

        data_after = AdvertisementRead.model_validate(advertisement).model_dump()

        if data_before["status"] != data_after["status"]:
            self._log_advertisement_event(
                advertisement,
                EventLogAction.STATUS_CHANGED,
                data_before={"status": data_before["status"]},
                data_after={"status": data_after["status"]},
            )
            self._handle_status_change(
                advertisement, data_before["status"], data_after["status"]
            )
        elif (
            "budgetTotal" in data_before
            and "budgetTotal" in data_after
            and data_before["budgetTotal"] != data_after["budgetTotal"]
        ):
            self._log_advertisement_event(
                advertisement,
                EventLogAction.UPDATED,
                data_before={"budgetTotal": data_before["budgetTotal"]},
                data_after={"budgetTotal": data_after["budgetTotal"]},
            )

        return AdvertisementRead.model_validate(advertisement)

    @view_config(
        route_name="advertisement.read_edit_delete",
        request_method="DELETE",
        permission="advertisement:delete",
    )
    def delete(self):
        advertisement = self._get_advertisement_by_id(int(self.request.matchdict["id"]))

        if advertisement.status == AdStatus.COMPLETED:
            raise ApiError("Completed advertisements cannot be cancelled", status=403)

        self._log_advertisement_event(
            advertisement,
            EventLogAction.STATUS_CHANGED,
            data_before={"status": advertisement.status},
            data_after={"status": AdStatus.CANCELLED},
        )

        advertisement.status = AdStatus.CANCELLED
        self.request.response.status = 200
        return {"message": "Advertisement cancelled successfully."}

    @view_config(
        route_name="advertisement.publish",
        request_method="PATCH",
        permission="advertisement:read",
    )
    def publish(self):
        ad_id = self.request.matchdict["id"]

        query = select(Advertisement).where(Advertisement.id == ad_id)

        if not self.is_admin:
            query = query.where(Advertisement.owner_id == self.user_id)

        advertisement = self.db_session.scalars(query).one_or_none()
        if not advertisement:
            raise ApiError("Advertisement not found", status=404)

        previous_status = advertisement.status
        advertisement.status = AdStatus.IN_REVIEW
        self._handle_status_change(advertisement, previous_status, AdStatus.IN_REVIEW)

        return AdvertisementRead.model_validate(advertisement)

    @view_config(
        route_name="advertisement.duplicate",
        request_method="POST",
        permission="advertisement:create",
    )
    def duplicate(self):
        ad_id = self.request.matchdict["id"]

        query = (
            select(Advertisement)
            .options(joinedload(Advertisement.advertisement_images))
            .where(Advertisement.id == ad_id)
        )

        if not self.is_admin:
            query = query.where(Advertisement.owner_id == self.user_id)

        original = self.db_session.scalars(query).unique().one_or_none()
        if not original:
            raise ApiError("Advertisement not found", status=404)
        new_data = {
            col.name: getattr(original, col.name)
            for col in Advertisement.__table__.columns
            if col.name not in ["id", "created_at", "updated_at", "owner_id"]
        }
        now = datetime.now(timezone.utc)
        new_data["status"] = AdStatus.DRAFT
        new_data["owner_id"] = self.user_id
        new_data["created_at"] = now
        new_data["updated_at"] = now
        new = Advertisement(**new_data)
        self.db_session.add(new)
        self.db_session.flush()

        self._duplicate_advertisement_images(original, new)
        self.db_session.flush()

        self.request.response.status = 201
        return AdvertisementRead.model_validate(new)

    @view_config(
        route_name="advertisement.end",
        request_method="PATCH",
        permission="advertisement:update",
    )
    def end(self):
        ad_id = self.request.matchdict["id"]

        query = select(Advertisement).where(Advertisement.id == ad_id)

        if not self.is_admin:
            query = query.where(Advertisement.owner_id == self.user_id)

        advertisement = self.db_session.scalars(query).one_or_none()
        if not advertisement:
            raise ApiError("Advertisement not found", status=404)

        if advertisement.status == AdStatus.DRAFT:
            raise ApiError("Cannot end a draft advertisement", status=400)

        advertisement.status = AdStatus.COMPLETED
        advertisement.end_date = datetime.now(timezone.utc)
        return AdvertisementRead.model_validate(advertisement)


class AdvertisementsPublic(BaseApi):
    default_validators: list[str] = []
    validators: list[str] = []

    @view_config(
        route_name="advertisement.public_read",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
    )
    def read(self):
        ad_id = int(self.request.matchdict["id"])

        try:
            row = self.db_session.execute(
                select(Advertisement, Event)
                .options(joinedload(Advertisement.owner))
                .outerjoin(Event, Advertisement.event_id == Event.id)
                .where(Advertisement.id == ad_id)
            ).first()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Database error", status=500)

        if not row:
            raise ApiError("Advertisement not found", status=404)

        ad, event = row

        full = AdvertisementRead.model_validate(ad)
        payload = full.model_dump(by_alias=True)

        public_keys = {
            "id",
            "title",
            "type",
            "startDate",
            "endDate",
            "advertisementImages",
            "content",
            "startDate",
            "endDate",
            "targetAreaRadiusKm",
            "country",
            "municipality",
            "impressions",
            "ctr",
            "linkClicks",
            "cpc",
            "previewSettings",
            "owner",
        }
        payload = {k: v for k, v in payload.items() if k in public_keys}

        if ad.preview_settings:
            settings = AdvertisementPreviewSettingsRead.model_validate(
                ad.preview_settings
            )
            payload["previewSettings"] = settings.model_dump(by_alias=True)

        if event:
            evt = EventRead.model_validate(event).model_dump(by_alias=True)
            for fld in ("internalNotes", "contacts", "users", "leads"):
                evt.pop(fld, None)
            payload.setdefault("ad_content", {})["event"] = evt

        return payload

    @view_config(
        route_name="advertisement.preview_settings",
        request_method="PATCH",
        permission="advertisement:update",
    )
    def preview_settings(self):
        ad_id = int(self.request.matchdict["id"])
        body = self.request.json_body

        key_map = {
            "displayMetrics": "display_metrics",
            "displayDetails": "display_details",
        }

        if len(body) != 1:
            raise ApiError("Request must contain exactly one key", status=400)

        incoming_key, value = next(iter(body.items()))
        attr = key_map.get(incoming_key)
        if not attr or not isinstance(value, bool):
            raise ApiError(
                f"Invalid key or value; expected one of {list(key_map)} with a boolean",
                status=400,
            )

        ad = self.db_session.scalar(
            select(Advertisement).where(
                Advertisement.id == ad_id,
            )
        )
        if not ad:
            raise ApiError("Advertisement not found", status=404)

        settings = ad.preview_settings
        if settings is None:
            settings = AdvertisementPreviewSettings(advertisement_id=ad_id)
            ad.preview_settings = settings
            self.db_session.add(settings)

        setattr(settings, attr, value)
        try:
            self.db_session.flush()
        except SQLAlchemyError as e:
            self.db_session.rollback()
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not update preview settings", status=500)

        return AdvertisementPreviewSettingsRead.model_validate(settings)
