import io
import zipfile
from datetime import datetime

from pydantic import ValidationError
from pyramid.httpexceptions import HTTPBadRequest, HTTPFound, HTTPNoContent
from pyramid.security import NO_PERMISSION_REQUIRED
from pyramid.view import view_config
from sqlalchemy import select

from strandproperties.config import app_cfg
from strandproperties.constants import DocumentSigningEntityType, EventLogActorType
from strandproperties.libs.utils import parse_fi_datetime_or_date_to_signing_dl_utc
from strandproperties.logger import logger
from strandproperties.models.document_library import (
    DocumentLibraryItem,
    DocumentLibraryItemOwner,
    OwnerType,
)
from strandproperties.schemas.document_library import (
    CancelDocumentLibraryItemUploadsRequest,
    ConfirmDocumentLibraryItemUploadsRequest,
    DocumentLibraryItemParam,
    DocumentLibraryItemRead,
    DocumentLibraryItemUploadRead,
    DocumentLibraryOwnerParam,
    DownloadDocumentLibraryItemsRequest,
    DownloadDocumentLibraryItemTokenPayload,
    GetDocumentLibraryItemsResponse,
    GetDocumentTypesResponse,
    SendDocumentLibraryItemForSigning,
    SharedDocumentLibraryItem,
    SharedDocumentLibraryItemsResponse,
    ShareDocumentLibraryItemsRequest,
    StartDocumentLibraryItemUploadsRequest,
    StartDocumentLibraryItemUploadsResponse,
    TokenParam,
    UpdateDocumentLibraryItemRequest,
)
from strandproperties.services.document_library.service import (
    DocumentLibrary,
    DocumentLibraryItemNotFoundError,
    UploadedFileNotFoundError,
    UploadNotPendingError,
    create_document_library,
)
from strandproperties.services.document_library.share_items_to_contacts import (
    PublicDownloadPageUrlGenerator,
    ShareItemsToContacts,
    SmtpMessageSender,
)
from strandproperties.services.document_library.token import (
    DownloadDocumentLibraryItemTokenHandler,
    SharedDocumentLibraryItemsTokenHandler,
)
from strandproperties.services.documents.document_signing_service import (
    fetch_signers,
    process_dokobit_create_response,
)
from strandproperties.services.dokobit_service import DokobitService, FileData
from strandproperties.utils.openapi_meta_object import create_openapi_metadata
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError, CustomValidationError
from strandproperties.schemas.common import ErrorLabel


class DocumentLibraryViews(BaseApi):
    organization_id: int

    def __init__(self, request, context=None):
        super().__init__(request, context)

        if not self.organization_id:
            raise ApiError("Missing organization id", status=400)

    @view_config(
        route_name="document_library.items",
        request_method="GET",
        permission="document_library:read",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Get all items in the document library for the given owner",
            response_schema=GetDocumentLibraryItemsResponse,
            success_description="Items successfully retrieved",
        ),
    )
    def get_items(self):
        try:
            document_library = self._create_document_library_from_request()
            return GetDocumentLibraryItemsResponse(
                items=[
                    self._to_document_library_item_read(item, document_library)
                    for item in document_library.get_all()
                ]
            )
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.item",
        request_method="DELETE",
        permission="document_library:delete",
        openapi_param_schema=DocumentLibraryItemParam,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Delete item from the document library",
            success_description="Item was successfully deleted",
            success_status_code="204",
        ),
    )
    def delete_item(self):
        try:
            params = DocumentLibraryItemParam(**self.request.matchdict)
            document_library = self._create_document_library_from_request()
            document_library.delete_item(params.item_id)
            return HTTPNoContent()
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.start_uploads",
        request_method="POST",
        permission="document_library:create",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=StartDocumentLibraryItemUploadsRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Add new items to the document library in a pending state",
            response_schema=StartDocumentLibraryItemUploadsResponse,
            success_description="Items were successfully created in a pending state. Upload URLs are returned.",
            success_status_code="201",
        ),
    )
    def start_uploads(self):
        try:
            request = StartDocumentLibraryItemUploadsRequest.model_validate(
                self.request.json_body
            )

            document_library = self._create_document_library_from_request()

            items = [
                document_library.create_pending_upload(
                    filename=item.filename,
                    mime_type=item.mime_type,
                    file_size=item.file_size,
                    document_type=item.document_type,
                    description=item.description,
                )
                for item in request.items
            ]

            self.request.response.status = 201
            return StartDocumentLibraryItemUploadsResponse(
                items=[
                    DocumentLibraryItemUploadRead(
                        id=item.id,
                        filename=item.filename,
                        upload_url=document_library.generate_upload_url(item),
                    )
                    for item in items
                ]
            )
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.confirm_pending_uploads",
        request_method="POST",
        permission="document_library:create",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=ConfirmDocumentLibraryItemUploadsRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Confirm that files have been uploaded for the specified items",
            success_description="Items were successfully moved to the ready state.",
            success_status_code="204",
            responses={
                "409": {
                    "description": "One or more items could not be cancelled because they are not in a correct upload state."
                },
            },
        ),
    )
    def confirm_pending_uploads(self):
        try:
            request = ConfirmDocumentLibraryItemUploadsRequest.model_validate(
                self.request.json_body
            )

            document_library = self._create_document_library_from_request()

            document_library.confirm_pending_uploads(
                [item.id for item in request.items]
            )
            return HTTPNoContent()
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.cancel_pending_uploads",
        request_method="POST",
        permission="document_library:create",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=CancelDocumentLibraryItemUploadsRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Cancel document library items that were pending upload.",
            success_description="Pending upload items were successfully cancelled.",
            success_status_code="204",
            responses={
                "409": {
                    "description": "One or more items could not be cancelled because they are not in a correct upload state."
                },
            },
        ),
    )
    def cancel_pending_uploads(self):
        try:
            request = CancelDocumentLibraryItemUploadsRequest.model_validate(
                self.request.json_body
            )

            document_library = self._create_document_library_from_request()

            document_library.cancel_pending_uploads([item.id for item in request.items])
            return HTTPNoContent()
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.send_for_signing",
        request_method="POST",
        permission="document_library:create",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=SendDocumentLibraryItemForSigning,
        openapi_metadata=create_openapi_metadata(
            tags=["Document Library"],
            description="Send a document from the library for signing",
            success_description="Document sent for signing",
        ),
    )
    def create_document_signing(self):
        try:
            sign_schema = SendDocumentLibraryItemForSigning(**self.request.json_body)
            end_of_day = parse_fi_datetime_or_date_to_signing_dl_utc(
                sign_schema.signing.last_signing_date
            )
            if not sign_schema.signing.signers:
                raise ApiError("At least one signer is required", status=400)

            entity_ids = [entity.entity_id for entity in sign_schema.signing.entities]
            document_library = self._create_document_library_from_request()
            items = document_library.get_items(entity_ids)

            files = []
            for item in items:
                if item.mime_type != "application/pdf":
                    raise ApiError(
                        "Only PDF files are supported for signing",
                        status=400,
                        label=ErrorLabel.CREATE_DOCUMENT_SIGNING_ONLY_PDF_FILES_SUPPORTED,
                    )
                file_content = document_library.get_item_content(item).read()
                file_name = item.filename
                files.append(FileData(name=file_name, content=file_content))

            dokobit_service = DokobitService()
            require_ssn_flag = getattr(sign_schema.signing, "require_ssn", False)

            response = dokobit_service.create_document(
                name=f"Documents to sign",
                files=files,
                signers=fetch_signers(
                    self.db_session,
                    [s.model_dump() for s in sign_schema.signing.signers],
                    sign_schema.signing.language,
                    require_ssn=require_ssn_flag,
                ),
                comment=sign_schema.signing.comment,
                deadline=end_of_day.strftime("%Y-%m-%dT%H:%M:%SZ"),
                require_ssn=require_ssn_flag,
            )

            process_dokobit_create_response(
                self.db_session,
                self.user_id,
                DocumentSigningEntityType.DOCUMENT_LIBRARY_ITEM,
                entity_ids,
                response,
                deadline=end_of_day,
            )
            self.db_session.flush()
            self.request.response.status = 201
            return response

        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.item",
        request_method="PUT",
        permission="document_library:update",
        openapi_param_schema=DocumentLibraryItemParam,
        openapi_request_schema=UpdateDocumentLibraryItemRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Update document type and description for a document library item.",
            response_schema=DocumentLibraryItemRead,
            success_description="Item was successfully updated.",
            success_status_code="200",
        ),
    )
    def update_item(self):
        try:
            params = DocumentLibraryItemParam(**self.request.matchdict)
            request = UpdateDocumentLibraryItemRequest.model_validate(
                self.request.json_body
            )
            document_library = self._create_document_library_from_request()
            item = document_library.update_item(
                id=params.item_id,
                document_type=request.document_type,
                description=request.description,
            )
            return self._to_document_library_item_read(item, document_library)
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.item",
        request_method="GET",
        permission="document_library:read",
        openapi_param_schema=DocumentLibraryItemParam,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Get a single document library item by id.",
            response_schema=DocumentLibraryItemRead,
            success_description="Item was successfully retrieved.",
            success_status_code="200",
        ),
    )
    def get_item(self):
        try:
            params = DocumentLibraryItemParam(**self.request.matchdict)
            document_library = self._create_document_library_from_request()
            item = document_library.get_item(params.item_id)
            return self._to_document_library_item_read(item, document_library)
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.share_items",
        request_method="POST",
        permission="document_library:read",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=ShareDocumentLibraryItemsRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["Document Library"],
            description="Share items with contacts",
            success_description="Items were successfully shared with contacts",
            success_status_code="204",
        ),
    )
    def share_items(self):
        try:
            params = DocumentLibraryOwnerParam(**self.request.matchdict)
            request = ShareDocumentLibraryItemsRequest.model_validate(
                self.request.json_body
            )
            share_items = ShareItemsToContacts(
                db_session=self.db_session,
                document_library=self._create_document_library_from_request(),
                message_sender=SmtpMessageSender(),
                download_url_generator=PublicDownloadPageUrlGenerator(
                    shared_by=self.user_id,
                    organization_id=self.organization_id,
                    is_admin=self.is_admin,
                    owner_id=params.owner_id,
                    owner_type=params.owner_type,
                ),
            )

            share_items.share_items(
                item_ids=request.item_ids,
                contact_ids=request.contact_ids,
                message=request.message,
            )

            return HTTPNoContent()
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.document_types",
        request_method="GET",
        openapi_param_schema=DocumentLibraryOwnerParam,
        permission="document_library:read",
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Get allowed document types for given owner",
            response_schema=GetDocumentTypesResponse,
            success_description="Document types were successfully retrieved.",
            success_status_code="200",
        ),
    )
    def get_document_types(self):
        try:
            document_library = self._create_document_library_from_request()
            return GetDocumentTypesResponse(
                document_types=document_library.get_document_types()
            )
        except Exception as e:
            self._handle_api_error(e)

    @view_config(
        route_name="document_library.download_items",
        request_method="POST",
        permission="document_library:read",
        openapi_param_schema=DocumentLibraryOwnerParam,
        openapi_request_schema=DownloadDocumentLibraryItemsRequest,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Download multiple document library items as a ZIP archive.",
            success_description="ZIP archive returned",
            success_status_code="200",
        ),
    )
    def download_items(self):
        logger.info(
            "Starting ZIP download request",
            extra={"requestId": self.request.request_id},
        )

        try:
            item_ids_raw = self.request.json_body
            logger.info(
                "Received item IDs: %s",
                item_ids_raw,
                extra={"requestId": self.request.request_id},
            )

            if not isinstance(item_ids_raw, list) or not all(
                isinstance(item_id, int) for item_id in item_ids_raw
            ):
                raise ApiError(
                    "Request body must be an array of integers",
                    status=400,
                )

            if not item_ids_raw:
                raise ApiError("No item IDs provided", status=400)

            document_library = self._create_document_library_from_request()

            try:
                items = document_library.get_items(item_ids_raw)
                logger.info(
                    "Found %d items for ZIP download",
                    len(items),
                    extra={"requestId": self.request.request_id},
                )
            except DocumentLibraryItemNotFoundError:
                raise ApiError("Document library items not found", status=404)

            if not items:
                raise ApiError("Document library items not found", status=404)

            if len(items) == 1:
                item = items[0]
                logger.info(
                    "Single item download - checking file existence",
                    extra={"requestId": self.request.request_id},
                )

                if not item.s3_key or not document_library._s3_service.exists(
                    document_library._add_document_library_path_prefix(item.s3_key)
                ):
                    raise ApiError("Document not available for download", status=404)

                logger.info(
                    "Single item download - returning file content directly",
                    extra={"requestId": self.request.request_id},
                )

                file_bytes = self._get_item_content_bytes(document_library, item)
                response = self.request.response
                response.content_type = item.mime_type or "application/octet-stream"
                response.content_disposition = f'attachment; filename="{item.filename}"'
                response.body = file_bytes

                logger.info(
                    "Returning single file response: filename='%s', size=%d bytes",
                    item.filename,
                    len(file_bytes),
                    extra={"requestId": self.request.request_id},
                )

                return response

            logger.info(
                "Creating ZIP archive for %d items",
                len(items),
                extra={"requestId": self.request.request_id},
            )
            zip_buffer = io.BytesIO()
            names_in_zip: set[str] = set()
            files_added = 0

            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as archive:
                for item in items:
                    try:
                        logger.info(
                            "Processing item %d: %s",
                            item.id,
                            item.filename,
                            extra={"requestId": self.request.request_id},
                        )
                        file_bytes = self._get_item_content_bytes(
                            document_library, item
                        )
                        logger.info(
                            "Retrieved %d bytes for item %d",
                            len(file_bytes),
                            item.id,
                            extra={"requestId": self.request.request_id},
                        )
                        name = self._unique_zip_name(
                            names_in_zip, item.filename, str(item.id)
                        )
                        archive.writestr(name, file_bytes)
                        files_added += 1
                        logger.info(
                            "Added item %d to ZIP as '%s'",
                            item.id,
                            name,
                            extra={"requestId": self.request.request_id},
                        )
                    except Exception as exc:
                        logger.error(
                            "Error adding item %s to ZIP: %s",
                            item.id,
                            exc,
                            extra={"requestId": self.request.request_id},
                        )

            zip_size = zip_buffer.tell()
            zip_buffer.seek(0)

            logger.info(
                "ZIP creation complete: %d files added, %d bytes total",
                files_added,
                zip_size,
                extra={"requestId": self.request.request_id},
            )

            filename = f"strandproperties-{datetime.now():%d%m%Y-%H%M}.zip"

            response = self.request.response
            response.content_type = "application/zip"
            response.content_disposition = f'attachment; filename="{filename}"'
            response.body = zip_buffer.getvalue()

            logger.info(
                "Returning ZIP response: filename='%s', size=%d bytes",
                filename,
                len(response.body),
                extra={"requestId": self.request.request_id},
            )
            return response

        except Exception as exc:
            self._handle_api_error(exc)

    def _get_item_content_bytes(
        self, document_library: DocumentLibrary, item: DocumentLibraryItem
    ) -> bytes:
        try:
            content_stream = document_library.get_item_content(item)
            if content_stream is None:
                logger.warning(
                    "S3 object for item %s not found – writing empty file to ZIP",
                    item.id,
                )
                return b""

            try:
                return (
                    content_stream.read()
                    if hasattr(content_stream, "read")
                    else bytes(content_stream)
                )
            except Exception:
                logger.exception(
                    "Failed to read content for item %s – writing empty file",
                    item.id,
                )
                return b""

        except Exception:
            logger.exception(
                "Unexpected error while obtaining content for item %s",
                item.id,
            )
            raise

    @staticmethod
    def _unique_zip_name(existing: set[str], base: str, prefix: str) -> str:
        if base not in existing:
            existing.add(base)
            return base

        candidate = f"{prefix}_{base}"
        counter = 1
        while candidate in existing:
            candidate = f"{prefix}_{counter}_{base}"
            counter += 1

        existing.add(candidate)
        return candidate

    def _create_document_library_from_request(self) -> DocumentLibrary:
        params = DocumentLibraryOwnerParam(**self.request.matchdict)
        return self._create_document_library(params.owner_type, params.owner_id)

    def _create_document_library(
        self, owner_type: OwnerType, owner_id: int
    ) -> DocumentLibrary:
        return create_document_library(
            self.db_session,
            self.user_id,
            self.organization_id,
            owner_type,
            owner_id,
            self.is_admin,
        )

    def _handle_api_error(self, error: Exception):
        logger.error(error, extra={"requestId": self.request.request_id})

        if isinstance(error, ApiError):
            raise error

        if isinstance(error, ValidationError):
            self._handle_validation_error(error)

        error_map: dict[type[Exception], tuple[str, int]] = {
            ValueError: ("Bad Request", 400),
            PermissionError: ("Forbidden", 403),
            DocumentLibraryItemNotFoundError: ("Upload not found", 404),
            UploadedFileNotFoundError: ("Uploaded file not found in S3", 409),
            UploadNotPendingError: ("Upload not in pending state", 409),
        }

        message, status = error_map.get(type(error), ("Internal Server Error", 500))
        raise ApiError(message, status=status)

    @staticmethod
    def _handle_validation_error(error: ValidationError) -> None:
        formatted_errors = {
            ".".join(str(p) for p in err["loc"]): {
                "message": err["msg"],
                "code": err["type"].split(".")[-1],
            }
            for err in error.errors()
        }
        raise CustomValidationError(formatted_errors, status=422)

    def _to_document_library_item_read(
        self, item: DocumentLibraryItem, document_library: DocumentLibrary
    ):
        return DocumentLibraryItemRead(
            id=item.id,
            owner_type=document_library.get_owner_type(),
            owner_id=document_library.get_owner_id(),
            filename=item.filename,
            mime_type=item.mime_type,
            document_type=item.document_type,
            description=item.description,
            file_size=item.file_size,
            created_at=item.created_at,
            created_by=f"{item.created_by.first_name} {item.created_by.last_name}",
            download_url=generate_tracked_download_url(
                item=item,
                user_id=self.user_id,
                organization_id=self.organization_id,
                is_admin=self.is_admin,
                owner_id=document_library.get_owner_id(),
                owner_type=document_library.get_owner_type(),
                actor_id=self.user_id,
                actor_type=EventLogActorType.USER,
            ),
            signings=item.signings,
        )


class PublicDocumentLibraryViews(BaseApi):
    default_validators = []

    @view_config(
        route_name="document_library.shared_items",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
        openapi_param_schema=TokenParam,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Get shared items",
            response_schema=SharedDocumentLibraryItemsResponse,
            success_description="Item",
            success_status_code="200",
        ),
    )
    def get_shared_items(self):
        try:
            params = TokenParam(**self.request.matchdict)
            payload = SharedDocumentLibraryItemsTokenHandler().validate_token(
                params.token,
            )

            document_library = create_document_library(
                db_session=self.db_session,
                user_id=payload.shared_by,
                organization_id=payload.organization_id,
                owner_type=payload.owner_type,
                owner_id=payload.owner_id,
                is_admin=payload.is_admin,
            )

            items = document_library.get_items(payload.item_ids)

            return SharedDocumentLibraryItemsResponse(
                items=[
                    SharedDocumentLibraryItem(
                        id=item.id,
                        filename=item.filename,
                        mime_type=item.mime_type,
                        document_type=item.document_type,
                        file_size=item.file_size,
                        download_url=generate_tracked_download_url(
                            item,
                            user_id=payload.shared_by,
                            organization_id=payload.organization_id,
                            is_admin=False,
                            owner_id=payload.owner_id,
                            owner_type=payload.owner_type,
                            actor_id=payload.contact_id,
                            actor_type=EventLogActorType.EXTERNAL_USER,
                        ),
                    )
                    for item in items
                ],
            )
        except Exception as e:
            return HTTPBadRequest()

    @view_config(
        route_name="document_library.download_item",
        request_method="GET",
        permission=NO_PERMISSION_REQUIRED,
        openapi_param_schema=TokenParam,
        openapi_metadata=create_openapi_metadata(
            tags=["DocumentLibrary"],
            description="Redirects user to the S3 download URL",
            success_description="User was redirected to the document download URL",
            success_status_code="302",
        ),
    )
    def get_download_item(self):
        try:
            params = TokenParam(**self.request.matchdict)
            payload = DownloadDocumentLibraryItemTokenHandler().validate_token(
                params.token,
            )

            document_library = create_document_library(
                db_session=self.db_session,
                user_id=payload.user_id,
                organization_id=payload.organization_id,
                owner_type=payload.owner_type,
                owner_id=payload.owner_id,
                is_admin=payload.is_admin,
            )

            item = document_library.get_item(payload.item_id)

            document_library._log_viewed_event(
                item,
                actor_id=payload.actor_id,
                actor_type=payload.actor_type,
            )

            download_url = document_library.generate_download_url(item)
            return HTTPFound(location=download_url)
        except Exception as e:
            return HTTPBadRequest()


def generate_tracked_download_url(
    item: DocumentLibraryItem,
    *,
    user_id: int,
    organization_id: int,
    is_admin: bool,
    owner_id: int,
    owner_type: OwnerType,
    actor_id: int,
    actor_type: EventLogActorType = EventLogActorType.USER,
) -> str:
    if not item.s3_key:
        return ""

    token = DownloadDocumentLibraryItemTokenHandler().generate_token(
        DownloadDocumentLibraryItemTokenPayload(
            user_id=user_id,
            organization_id=organization_id,
            is_admin=is_admin,
            owner_id=owner_id,
            owner_type=owner_type,
            item_id=item.id,
            actor_type=actor_type,
            actor_id=actor_id,
        )
    )
    return app_cfg.get_document_library_download_url(token)
