import json
import traceback
from typing import Optional

from pydantic import ValidationError
from pyramid import httpexceptions
from pyramid.view import exception_view_config

from strandproperties.config import app_cfg
from strandproperties.logger import logger


class ApiError(Exception):
    def __init__(self, message, *, status=400, label=None):
        self.message = message
        self.label = label
        self.status = status


# Custom class to handle validation errors and format them consistently
# This can be used when implementing extra validation for the view schemas
class CustomValidationError(Exception):
    """Custom validation error to format validation messages consistently."""

    def __init__(self, errors: dict, *, status=400):
        self.errors = errors  # Store errors in the required format
        self.status = status

    def to_dict(self) -> dict:
        """Converts errors to a structured dictionary for API responses."""
        return {"error": self.errors}

    def __str__(self):
        return str(self.to_dict())  # Ensure proper string formatting


@exception_view_config(ApiError, renderer="json")
def api_error_view(exc, request):
    request.response.status = exc.status
    status_code = request.response.status_code
    label = exc.label

    # TODO: consider lower logging level to warning since this will send exception to Sentry
    logger.error(
        f"{label}: {exc.message}",
        extra={
            "endpoint": request.path,
            "method": request.method,
            "requestId": f"{request.request_id}",
            "statusCode": status_code,
        },
    )

    return {
        "status_code": status_code,
        "error": exc.message,
        "label": label,
    }


@exception_view_config(CustomValidationError, renderer="json")
def custom_validation_error_view(exc, request):
    """Handles CustomValidationError exceptions and formats response JSON."""
    request.response.status = exc.status
    status_code = request.response.status_code

    # Log the validation error
    logger.error(
        f"Validation Error: {exc.errors}",
        extra={
            "endpoint": request.path,
            "method": request.method,
            "requestId": getattr(request, "request_id", "unknown"),
            "statusCode": status_code,
        },
    )

    return exc.to_dict()  # ✅ Response now has only the "error" object


@exception_view_config(ValidationError, renderer="json")
def validation_error_view(exc, request):
    request.response.status = 400
    status_code = request.response.status_code

    errors = {
        e["loc"][0]: {
            "message": e["msg"],
            "code": e["type"],
        }
        for e in exc.errors()
    }

    return {
        "status_code": status_code,
        "error": errors,
    }


@exception_view_config(httpexceptions.HTTPError, renderer="json")
def pyramid_http_error_view(exc, request):
    request.response.status = exc.code
    status_code = request.response.status_code

    return {
        "status_code": status_code,
        "error": str(exc),
    }


@exception_view_config(Exception, renderer="json")
def pyramid_unexpected_exception_view(exc, request):
    request.response.status = 500
    status_code = request.response.status_code

    logger.exception(
        exc,
        extra={
            "endpoint": request.path,
            "method": request.method,
            "requestId": f"{request.request_id}",
            "statusCode": status_code,
        },
    )

    if app_cfg.is_local_development():
        traceback.print_exc()

    return {
        "status_code": status_code,
        "error": "Unexpected error occurred",
    }
