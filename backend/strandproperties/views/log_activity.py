from pydantic import ValidationError
from pyramid.view import view_config
from sqlalchemy import select

from strandproperties.logger import logger
from strandproperties.models.activity import Activity
from strandproperties.schemas.log_activity import LogActivityCreateEdit, LogActivityRead
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError


class LogActivities(BaseApi):
    @view_config(
        route_name="activity.create",
        request_method="POST",
    )
    def create(self):
        """Create activity endpoint"""
        try:
            payload = LogActivityCreateEdit(**self.request.json_body)
        except ValidationError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid activity information")

        activity = Activity(
            description=payload.description.dict(),
            object_type=payload.object_type,
            object_id=payload.object_id,
            actor_id=self.user_id,
        )

        self.db_session.add(activity)
        self.db_session.flush()

        return LogActivityRead.model_validate(activity)

    @view_config(
        route_name="activity.read_edit",
        request_method="GET",
    )
    def read(self):
        activity_id = self.request.matchdict["id"]

        activity = self.db_session.scalars(
            select(Activity).where(Activity.id == activity_id)
        ).one_or_none()

        if activity is None:
            raise ApiError("Activity not found", status=404)

        return LogActivityRead.model_validate(activity)

    @view_config(
        route_name="activity.read_edit",
        request_method="PATCH",
    )
    def update(self):
        activity_id = self.request.matchdict["id"]
        try:
            payload = LogActivityCreateEdit(**self.request.json_body)
        except ValidationError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid activity information")

        activity = self.db_session.scalars(
            select(Activity).where(Activity.id == activity_id)
        ).one_or_none()

        if activity is None:
            raise ApiError("Activity not found", status=404)

        activity.description = payload.description.dict()
        activity.object_type = payload.object_type
        activity.object_id = payload.object_id

        self.db_session.flush()

        return LogActivityRead.model_validate(activity)

    @view_config(
        route_name="activity.read_edit",
        request_method="DELETE",
    )
    def delete(self):
        activity_id = self.request.matchdict["id"]

        activity = self.db_session.scalars(
            select(Activity).where(Activity.id == activity_id)
        ).one_or_none()

        if activity is None:
            raise ApiError("Activity not found", status=404)

        self.db_session.delete(activity)
        self.db_session.flush()

        return {"message": "Activity deleted successfully"}
