import typing as t

from strandproperties.constants import RoleType
from strandproperties.models.user import Role, User
from strandproperties.scripts.base import BaseScript


class UserScript(BaseScript):
    def run(
        self,
        *,
        email: str,
        password: str,
        role: t.Optional[RoleType] = None,
        organization: t.List[int] = [],
        is_superadmin: bool = False,
        first_name: str = "",
        last_name: str = "",
    ):
        with self.db_session.begin():
            user = User(
                email=email,
                first_name=first_name,
                last_name=last_name,
                is_active=True,
                is_verified=True,
                is_superadmin=is_superadmin,
            )
            user.set_password(password)
            self.db_session.add(user)
            self.db_session.flush()

            if role:
                if not organization:
                    raise RuntimeError(
                        "organization is required when role is specified"
                    )

                # Create roles for each organization
                for organization_id in organization:
                    self.db_session.add(
                        Role(
                            user_id=user.id,
                            organization_id=organization_id,
                            role=role,
                        )
                    )

                self.db_session.flush()

        print(f"Created user {user.email} with ID {user.id}")
