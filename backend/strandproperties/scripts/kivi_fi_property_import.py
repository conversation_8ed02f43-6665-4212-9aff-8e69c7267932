from datetime import datetime
from pathlib import Path
from typing import List, Optional

import requests
from pydantic import BaseModel
from requests.auth import HTTPBasi<PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from strandproperties.config import app_cfg
from strandproperties.constants import KIVI_API_BASE_URL, Currency, Language
from strandproperties.libs.aws import S3Service
from strandproperties.logger import logger
from strandproperties.mapping import (
    ADMINSTRATION_TYPE_CODE_MAPPING,
    AREA_BASIS_MAPPING,
    AVAILABILITY_KIVI_MAPPING,
    BALCONY_MAPPING,
    CHARGE_PERIOD_MAPPING,
    CHARGE_TYPE_MAPPING,
    COMPASS_POINTS_MAPPING,
    CONDITION_KIVI_MAPPING,
    CONSTRUCTION_MATERIAL_MAPPING,
    COST_FIELDS,
    DEVELOPMENT_PHASE_MAPPING,
    DISTRIBUTION_SYSTEM_MAPPING,
    ENERGY_TYPE_MAPPING,
    FINNISH_FEATURE_MAPPING,
    FIR<PERSON>LACE_MAPPING,
    HEATING_SYSTEM_MAPPING,
    HOLDING_TYPE_CODE_MAPPING,
    LIVING_FORM_TYPE_CODE_MAPPING,
    MAINTENANCE_TYPE_CODE_MAPPING,
    OUTER_ROOF_MAPPING_TYPE_CODE,
    OWNERSHIP_TYPE_MAPPING,
    PREMISE_TYPE_MAPPING,
    PROPERTY_TYPE_GROUP_MAPPING,
    REALTYOPTIONS_TO_STORAGE_TYPE,
    ROOF_MATERIAL_MAPPING,
    ROOM_COUNT_MAPPING,
    SYSTEM_KIVI_MAPPING,
    TELEVISION_TYPE_MAPPING,
    VENTILATIONSYSYTEM_MAPPING,
    WATERCHARGE_TYPE_MAPPING,
    WATERFRONT_MAPPING,
    ZONING_TYPE_CODE_MAPPING,
)
from strandproperties.models.document_library import DocumentLibraryItem, DocumentType
from strandproperties.models.fi_address import FIAddress
from strandproperties.models.fi_area import FIArea
from strandproperties.models.fi_estate_overview import FIEstateOverview
from strandproperties.models.fi_housing_company import FIHousingCompany
from strandproperties.models.fi_other_share_overview import FIOtherShareOverview
from strandproperties.models.fi_plot_overview import FIPlotOverview
from strandproperties.models.fi_property import FIProperty, FIPropertyType, FIRealty
from strandproperties.models.fi_property_overview import FIPropertyOverview
from strandproperties.models.fi_residential_property_overview import (
    FIResidentialPropertyOverview,
)
from strandproperties.models.fi_residential_share_overview import (
    FIResidentialShareOverview,
)
from strandproperties.models.image import Image
from strandproperties.models.kivi import KiviDocumentLink
from strandproperties.models.organization import Organization
from strandproperties.models.property import (
    DescriptionType,
    PropertyDescription,
    PropertyRealtor,
)
from strandproperties.models.user import User
from strandproperties.schemas.fi_property.fi_area import FIAreaKindEnum
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministrationTypeCodeEnum,
    FIAreaUnitCodeEnum,
    FIChoiceEnum,
    FIFloorSurfaceMaterialCodeEnum,
    FIOuterRoofMaterialCodeEnum,
    FIOuterRoofTypeCodeEnum,
    FIParkingSpaceTypeCodeEnum,
    FIRenovationStatusCodeEnum,
    FITypeCodeEnum,
    FIVentilationSystemCodeEnum,
    FIWallSurfaceMaterialCodeEnum,
)
from strandproperties.schemas.fi_property.fi_estate import (
    FIEstateAreaTypeCodeEnum,
    FIEstatePropertyTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_housing_company import (
    FIHousingCompanyInspectionTypeCodeEnum,
    FIHousingCompanyMaintenanceTypeCodeEnum,
    FIHousingCompanyPremiseTypeCodeEnum,
    FIPremiseTypeCodeEnum,
    FISchemeIdEnum,
)
from strandproperties.schemas.fi_property.fi_plot import FIZoningTypeCodeEnum
from strandproperties.schemas.fi_property.fi_property_details import (
    FIGroundAreaTypeCodeEnum,
    FIWaterSupplyTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_property_type import (
    FIListingTypeEnum,
    FIOwnershipTypeEnum,
    FIPropertyTypeEnum,
    FIPropertyTypeGroupEnum,
)
from strandproperties.schemas.fi_property.fi_realty import (
    FIChargePeriodCodeEnum,
    FICostTypeCodeEnum,
    FIDamageTypeCodeEnum,
    FILivingComfortFactorCodeEnum,
    FILivingFormTypeCodeEnum,
    FIRealtyAvailabilityCodeEnum,
    FIWaterChargeTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_residential_share import (
    FIFloorLevelCodeEnum,
    FIOwnershipTypeCodeEnum,
)
from strandproperties.scripts.base import BaseScript
from strandproperties.services.document_library.service import (
    DocumentLibrary,
    create_fi_property_document_library,
)
from strandproperties.utils.tracking_processed_field import TrackingProcessedField
from strandproperties.views.exceptions import ApiError


def create_text_field(
    text: str | None = None, language: str = Language.FINNISH
) -> list[dict[str, str]]:
    return [{"language_code": language, "text": text or ""}]


class ImportFIPropertyFromKiviScript(BaseScript):
    KIVI_REALTIES_API = "{kivi_api_base_url}/realties/{reference}"
    KIVI_DOCUMENT_API = "{kivi_api_base_url}/documents"

    def create_new_fi_area(self, area_name: str) -> FIArea:
        # Get the maximum area_code
        max_area_code = (
            self.db_session.query(FIArea.area_code)
            .order_by(FIArea.area_code.desc())
            .first()
        )

        # Generate new area_code by incrementing the maximum
        new_area_code = str(int(max_area_code[0]) + 1) if max_area_code else "801"

        fi_area = FIArea(
            kind=FIAreaKindEnum.REGION,
            area_code=new_area_code,
            area_name_fi=area_name,
            area_name_en=area_name,
            area_name_sv=area_name,
        )
        self.db_session.add(fi_area)
        self.db_session.flush()
        return fi_area

    def _process_address_data(self, property_: dict, address_prefix: str = "") -> dict:
        """Helper function to process and extract address data from property dictionary.

        Args:
            property_: Dictionary containing property data
            address_prefix: Prefix for address fields (e.g. 'RC_' for housing company)

        Returns:
            Dictionary containing processed address data
        """
        street_key = f"{address_prefix}STREET_NAME" if address_prefix else "STREET_NAME"
        postcode_key = f"{address_prefix}POSTCODE" if address_prefix else "POSTCODE"
        postarea_key = f"{address_prefix}POSTAREA" if address_prefix else "POSTAREA"

        return {
            "street": property_.get(street_key, ""),
            "postal_code": property_.get(postcode_key, ""),
            "postarea": property_.get(postarea_key, ""),
            "district": (
                property_.get("QUARTEROFTOWN", "").split(",")[-1].strip()
                if property_.get("QUARTEROFTOWN", "")
                else ""
            ),
            "municipality": property_.get("MUNICIPALITY", ""),
            "apartment_number": property_.get("DOOR_NUMBER", ""),
            "stairwell": property_.get("STAIRWAY", ""),
        }

    def update_or_create_fi_address(
        self,
        property_: dict,
        fi_address: FIAddress = None,
    ) -> FIAddress:
        address_data = self._process_address_data(property_)

        area = (
            self.db_session.query(FIArea)
            .filter(FIArea.area_name_fi == address_data["postarea"])
            .first()
        )

        fi_area = (
            area
            if area
            else self.create_new_fi_area(area_name=address_data["postarea"])
        )

        if not fi_address:
            fi_address = FIAddress(
                street_address=address_data["street"],
                postal_code=address_data["postal_code"],
                district=address_data["district"],
                municipality=address_data["municipality"],
                area_id=fi_area.id,
                apartment_number=address_data["apartment_number"],
                stairwell=address_data["stairwell"],
            )
            self.db_session.add(fi_address)
            self.db_session.flush()

        fi_address.street_address = address_data["street"]
        fi_address.postal_code = address_data["postal_code"]
        fi_address.municipality = address_data["municipality"]
        fi_address.area_id = fi_area.id
        fi_address.apartment_number = address_data["apartment_number"]
        fi_address.stairwell = address_data["stairwell"]
        fi_address.district = address_data["district"]

        return fi_address

    def update_or_create_fi_property_overview(
        self,
        property_: dict,
        fi_property_overview: FIPropertyOverview = None,
    ) -> FIPropertyOverview:
        if not fi_property_overview:
            fi_property_overview = FIPropertyOverview()
            self.db_session.add(fi_property_overview)

        realty_options = property_.get("REALTYOPTIONS", {})
        realty_option = realty_options.get("OTHERMAINTAINROOM", "")
        raw_sewage_codes = realty_options.get("SEWER", [])

        realty_option_list = (
            realty_option if isinstance(realty_option, list) else [realty_option]
        )
        storage_types = [
            REALTYOPTIONS_TO_STORAGE_TYPE.get(option)
            for option in realty_option_list
            if REALTYOPTIONS_TO_STORAGE_TYPE.get(option)
        ]

        mapped_sewage_codes = [
            SYSTEM_KIVI_MAPPING.get(code)
            for code in raw_sewage_codes
            if code in SYSTEM_KIVI_MAPPING
        ]
        sewage_disposal = {"system_codes": mapped_sewage_codes}

        fi_property_overview.property_id = property_.get("REALTYIDENTIFIER")
        fi_property_overview.floor_plan = property_.get("FLAT_STRUCTURE", "")
        fi_property_overview.property_name = property_.get("ESTATENAME")
        fi_property_overview.sewage_disposal = sewage_disposal
        fi_property_overview.buildings = [
            {
                "description": create_text_field(property_.get("BUILDING_INFO"), ""),
            }
        ]
        fi_property_overview.encumbrance = {
            "mortgage_amount": property_.get("MORTGAGE_AMOUNT", 0),
            "mortgage_free_amount": property_.get("MORTGAGE_FREE_AMOUNT", 0),
            "unpaid_fees": create_text_field(property_.get("UNPAID_BILLS_AMOUNT", "")),
            "descriptions": create_text_field(property_.get("ENCUMBRANCE_DESC", "")),
            **(
                {
                    "no_unreported_public_encumbrances": (
                        FIChoiceEnum.YES
                        if property_.get("ENCUMBRANCE_REPORTED_FLAG") == 1
                        else FIChoiceEnum.NO
                    )
                }
                if property_.get("ENCUMBRANCE_REPORTED_FLAG") is not None
                else {}
            ),
        }
        if waterfront_length := property_.get("WATERFRONT_LENGTH_M"):
            fi_property_overview.ground_area = {
                "type_code": [FIGroundAreaTypeCodeEnum.WATERFRONT],
                "description": create_text_field(str(waterfront_length)),
            }
        if water_supply_desc := property_.get("WATER_SUPPLY_DESC"):
            fi_property_overview.water_supply = {
                "type_codes": [FIWaterSupplyTypeCodeEnum.MAINS_WATER_SUPPLY],
                "description": create_text_field(water_supply_desc),
            }

        if storage_types:
            fi_property_overview.storages = [{"type_code": storage_types}]

        if not fi_property_overview.id:
            self.db_session.flush()

        return fi_property_overview

    def update_or_create_fi_plot_overview(
        self,
        property_: dict,
        fi_plot_overview: FIPlotOverview = None,
    ) -> FIPlotOverview:
        if not fi_plot_overview:
            fi_plot_overview = FIPlotOverview()
            self.db_session.add(fi_plot_overview)

        if property_.get("RC_ZONING_INFO"):
            zoning_type_codes = [
                ZONING_TYPE_CODE_MAPPING.get(zoning, FIZoningTypeCodeEnum.MASTER_PLAN)
                for zoning in property_.get("ZONINGS", [])
            ]

            fi_plot_overview.zonings = {
                "type_codes": list(zoning_type_codes),
                "description": create_text_field(property_.get("RC_ZONING_INFO", "")),
            }

        fi_plot_overview.area = {
            "value": property_.get("LOT_AREA"),
            "area_unit_code": (property_.get("LOT_MEASURETYPE") or "M2").upper(),
        }
        fi_plot_overview.lease_holder = property_.get("RC_LOT_RENTER", "")
        fi_plot_overview.lease_end_date = property_.get("RC_LOT_RENTTIME", "")
        fi_plot_overview.holding_type_description = create_text_field(
            property_.get("CONSTRUCTIONRIGHT", "")
        )
        fi_plot_overview.construction_right = {
            "density_rate": property_.get("CONSTRUCTIONRIGHT_E", 0),
            "floor_area": {
                "value": property_.get("CONSTRUCTIONRIGHT_AREA_M2", ""),
                "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
            },
        }
        fi_plot_overview.plot_redemption_info = create_text_field(
            str(property_.get("LOT_REDEMPTION_PRICE", ""))
        )
        fi_plot_overview.plot_number = property_.get("LOTNO", "")
        fi_plot_overview.yearly_rent = property_.get("CHARGES_LOT_RENT_MONTH", "")
        fi_plot_overview.holding_type_code = HOLDING_TYPE_CODE_MAPPING.get(
            property_.get("RC_LOTHOLDING", "")
        )

        realtytype = property_.get("REALTYTYPE", "").lower()
        ownership_type = next(
            (
                group_type
                for key, group_type in OWNERSHIP_TYPE_MAPPING.items()
                if key in realtytype
            ),
            FIOwnershipTypeEnum.PROPERTY,
        )
        if ownership_type == FIOwnershipTypeEnum.PROPERTY:
            realty_options = property_.get("REALTYOPTIONS", {})
            fi_plot_overview.beaches = self.get_beaches(realty_options, property_)

        lot_redeemed_flag = property_.get("LOT_REDEEMED_FLAG")
        if lot_redeemed_flag is not None:
            fi_plot_overview.is_redeemable = (
                FIChoiceEnum.YES
                if property_.get("LOT_REDEEMED_FLAG") == 1
                else FIChoiceEnum.NO
            )

        if not fi_plot_overview.id:
            self.db_session.flush()

        return fi_plot_overview

    def update_or_create_fi_realty(
        self,
        property_: dict,
        fi_address: FIAddress,
        fi_realty: FIRealty = None,
    ) -> FIRealty:
        if not fi_realty:
            fi_realty = FIRealty(fi_address_id=fi_address.id)

        fi_realty.additional_area_measurement_information = create_text_field(
            property_.get("AREA_DESC", "")
        )

        assignment = property_.get("ASSIGNMENT", {})
        fi_realty.selling_price = assignment.get("PRICE", 0)
        fi_realty.is_rented = (
            FIChoiceEnum.YES if assignment.get("RENTED_FLAG", "") else FIChoiceEnum.NO
        )

        availability_code = AVAILABILITY_KIVI_MAPPING.get(
            assignment.get("FREE_TYPE"), FIRealtyAvailabilityCodeEnum.OTHER
        )
        fi_realty.availability = {
            "code": availability_code,
            "date": assignment.get("FREE_DATE"),
        }
        fi_realty.costs = [
            {
                key: value
                for key, value in {
                    "amount": self.parse_float(property_.get(field, 0)),
                    "currency_code": Currency.EUR,
                    "type_code": type_code,
                    "water_charge_type_code": (
                        WATERCHARGE_TYPE_MAPPING.get(
                            property_.get("WATERCHARGE_TYPE"),
                            FIWaterChargeTypeCodeEnum.BASIC_FEE,
                        )
                        if type_code == FICostTypeCodeEnum.WATER
                        else None
                    ),
                    "charge_period_code": (
                        CHARGE_PERIOD_MAPPING.get(
                            property_.get(
                                CHARGE_TYPE_MAPPING.get(field, "PARKINGCHARGE_TYPE"),
                                FIChargePeriodCodeEnum.OTHER,
                            )
                        )
                    ),
                    "description": create_text_field(
                        (
                            str(self.parse_float(property_.get(field, 0)))
                            if field == "CHARGES_WATER"
                            else ""
                        )
                    ),
                }.items()
                if self.parse_float(property_.get(field, None)) is not None
            }
            for field, type_code in COST_FIELDS
            if self.parse_float(property_.get(field, None)) is not None
        ]

        fi_realty.additional_information = {
            "driving_instructions": create_text_field(
                property_.get("DRIVEINSTRUCTION", "")
            ),
            "additional_description": create_text_field(
                property_.get("OTHER_IMPORTANT_INFO", "")
            ),
            "school_childcare_description": create_text_field(
                property_.get("DAYCARE", "") + ", " + property_.get("SCHOOL", "")
            ),
            "nearby_amenities_description": create_text_field(
                property_.get("SERVICES", "")
            ),
            "transportation_services_description": create_text_field(
                property_.get("CONNECTIONS", "")
            ),
            "activities_and_recreation_description": create_text_field(
                property_.get("SERVICES_OTHER", "")
            ),
        }

        fi_realty.costs_description = create_text_field(
            property_.get("CHARGES_OTHER", "")
        )
        damage_confirmed = property_.get("DAMAGE_CONFIRMED_FLAG") == 1
        damage_water = property_.get("DAMAGE_WATER_FLAG") == 1
        damage_moisture = property_.get("DAMAGE_MOISTURE_FLAG") == 1

        fi_realty.damages = [
            {
                "damages_exists_code": (
                    FIChoiceEnum.YES if damage_confirmed else FIChoiceEnum.NO
                ),
                "damage_type_code": (
                    FIDamageTypeCodeEnum.WATER_DAMAGE
                    if damage_water == 1
                    else (
                        FIDamageTypeCodeEnum.MOISTURE_DAMAGE
                        if damage_moisture == 1
                        else FIDamageTypeCodeEnum.OTHER
                    )
                ),
                "damage_date": property_.get("DAMAGE_DATE", ""),
                "cause_description": create_text_field(
                    property_.get("DAMAGE_REASON", "")
                ),
                "extent_description": create_text_field(
                    property_.get("DAMAGE_EXTENT", "")
                ),
                "repair_description": create_text_field(
                    property_.get("DAMAGE_RENOVATION_MADE", "")
                ),
            }
        ]

        if property_.get("CONDITION_CLASS"):
            fi_realty.condition = {
                "code": CONDITION_KIVI_MAPPING.get(property_.get("CONDITION_CLASS")),
                "description": create_text_field(property_.get("CONDITION", "")),
            }
        fi_realty.living_form_type_code = LIVING_FORM_TYPE_CODE_MAPPING.get(
            (property_.get("HOLDINGTYPE", "") or "").lower(),
            FILivingFormTypeCodeEnum.NONSUBSIDISED,
        )
        comfort_factors = []
        comfort_factor_mappings = [
            (
                FILivingComfortFactorCodeEnum.NOISE_SMELL_VIBRATION_ETC,
                "KNOWN_PROBLEMS_NOISE_ETC",
            ),
            (FILivingComfortFactorCodeEnum.OTHER, "KNOWN_PROBLEMS_OTHER"),
            (FILivingComfortFactorCodeEnum.PESTS_RODENTS_ETC, "KNOWN_PROBLEMS_PESTS"),
            (
                FILivingComfortFactorCodeEnum.FUNCTIONAL_OR_TECHNICAL_DEFECTS,
                "KNOWN_PROBLEMS_TECHNICAL",
            ),
            (
                FILivingComfortFactorCodeEnum.HEAT_COLD_DRAUGHT_ETC,
                "KNOWN_PROBLEMS_TEMPERATURE",
            ),
        ]

        for factor_code, property_key in comfort_factor_mappings:
            if description := property_.get(property_key):
                comfort_factors.append(
                    {
                        "living_comfort_factor_code": factor_code,
                        "description": description,
                    }
                )

        fi_realty.living_comfort_factors = comfort_factors if comfort_factors else None
        fi_realty.transaction_includes = create_text_field(
            property_.get("NOT_SOLD_DESC", "")
        )
        fi_realty.updated_at = (
            datetime.strptime(property_.get("UPDATEDATE", ""), "%Y-%m-%dT%H:%M:%S.%fZ")
            if property_.get("UPDATEDATE", "")
            else None
        )

        self.db_session.add(fi_realty)
        self.db_session.flush()

        return fi_realty

    def update_or_create_fi_other_share_overview(
        self,
        property_: dict,
        fi_other_share_overview: FIOtherShareOverview = None,
    ) -> FIOtherShareOverview:
        starting_debt_free_price = property_.get("ASSIGNMENT", {}).get("PRICE", 0)
        if not fi_other_share_overview:
            fi_other_share_overview = FIOtherShareOverview()
        fi_other_share_overview.starting_debt_free_price = starting_debt_free_price
        self.db_session.add(fi_other_share_overview)
        self.db_session.flush()
        return fi_other_share_overview

    def update_or_create_fi_housing_company(
        self, property_: dict, fi_housing_company: FIHousingCompany = None
    ) -> FIHousingCompany:
        def map_value(mapping, key, default=None):
            return mapping.get(key, default)

        def create_or_update_address(property_, fi_housing_company):
            address_data = self._process_address_data(property_, address_prefix="RC_")

            if fi_housing_company.fi_address_id:
                fi_address = self.db_session.query(FIAddress).get(
                    fi_housing_company.fi_address_id
                )
                fi_address.postal_code = address_data["postal_code"]
            else:
                area = (
                    self.db_session.query(FIArea)
                    .filter(FIArea.area_name_fi == address_data["postarea"])
                    .first()
                )
                fi_area = (
                    area
                    if area
                    else self.create_new_fi_area(area_name=address_data["postarea"])
                )
                fi_address = FIAddress(
                    area_id=fi_area.id,
                    postal_code=address_data["postal_code"],
                    street_address=address_data["street"],
                    municipality=address_data["postarea"],
                    district=address_data["district"],
                )
                self.db_session.add(fi_address)
                self.db_session.flush()
                fi_housing_company.fi_address_id = fi_address.id

        def create_buildings_info(property_, realty_options):
            construction_material = realty_options.get("CONSTRUCTIONMATERIAL", [None])
            collection = realty_options.get("COLLECTION", "")

            distribution_system_codes = DISTRIBUTION_SYSTEM_MAPPING.get(
                realty_options.get("HEATDISTRIBUTION", [None])[0], []
            )

            floor_heating_desc = property_.get("FLOOR_HEATING_DECS", "")
            if floor_heating_desc:
                distribution_system_codes.extend(
                    ["ELECTRIC_UNDERFLOOR_HEATING", "WATER_UNDERFLOOR_HEATING"]
                )

            premise_types = [
                PREMISE_TYPE_MAPPING.get(
                    item, FIHousingCompanyPremiseTypeCodeEnum.OTHER
                )
                for item in realty_options.get("REALTYCOMPANY", [])
            ]

            return {
                "heating": {
                    "system_codes": HEATING_SYSTEM_MAPPING.get(
                        (
                            realty_options.get("HEATING", [None])[0]
                            if realty_options.get("HEATING")
                            else None
                        ),
                        [],
                    ),
                    "distribution_system_codes": distribution_system_codes,
                    "distribution_system_other_description": create_text_field(
                        floor_heating_desc
                    ),
                    "description": create_text_field(property_.get("HEATING", "")),
                    "system_other_description": create_text_field(
                        property_.get("HEATING", "")
                    ),
                },
                "ventilation": {
                    "system_codes": [
                        VENTILATIONSYSYTEM_MAPPING.get(
                            realty_options.get("VENTILATIONSYSTEMS", [None])[0],
                            FIVentilationSystemCodeEnum.FORCED_EXHAUST,
                        )
                    ]
                },
                "construction_material": {
                    "type_code": [
                        map_value(
                            CONSTRUCTION_MATERIAL_MAPPING,
                            material,
                            "OTHER",
                        )
                        for material in construction_material
                    ],
                },
                "has_sauna": (
                    FIChoiceEnum.YES if property_.get("SAUNATYPE") else FIChoiceEnum.NO
                ),
                "outer_roof": {
                    "type_code": map_value(
                        OUTER_ROOF_MAPPING_TYPE_CODE,
                        property_.get("RC_ROOF"),
                        FIOuterRoofTypeCodeEnum.OTHER,
                    ),
                    "material_code": ROOF_MATERIAL_MAPPING.get(
                        property_.get("RC_ROOFING"), FIOuterRoofMaterialCodeEnum.OTHER
                    ),
                },
                "has_elevator": (
                    FIChoiceEnum.YES if "hissi" in collection else FIChoiceEnum.NO
                ),
                "premises": {
                    "type_codes": premise_types,
                    "type_other_description": create_text_field(
                        ", ".join(property_.get("BUILDINGS", []))
                    ),
                    "description": create_text_field(
                        ", ".join(property_.get("BUILDINGS", []))
                    ),
                },
            }

        def create_finances_info(property_):
            return {
                "loan_amount": property_.get("RC_DEBT", 0),
                "currency_code": Currency.EUR,
                "mortgage_amount": property_.get("RC_MORTGAGE", 0),
                "financing_fee_interest_only_period": create_text_field(
                    property_.get("FINANCING_FEE_INTEREST_ONLY_PERIOD", "")
                ),
                "financing_fee_after_interest_only": property_.get(
                    "FINANCING_FEE_AFTER_INTEREST_ONLY", ""
                ),
                "financing_fee_interest_only_startdate": property_.get(
                    "FINANCING_FEE_INTEREST_ONLY_STARTDATE", ""
                ),
                "financing_fee_interest_only_enddate": property_.get(
                    "FINANCING_FEE_INTEREST_ONLY_ENDDATE", ""
                ),
                "management_charge_after_interest_only": property_.get(
                    "MANAGEMENT_CHARGE_AFTER_INTEREST_ONLY", ""
                ),
            }

        def create_housing_company_premise_statistics(property_):
            statistics = []

            apartment_count = property_.get("RC_APARTMENTS_COUNT") or property_.get(
                "RC_OWN_APARTMENTS_COUNT"
            )
            if apartment_count:
                statistics.append(
                    {
                        "premise_type_code": FIPremiseTypeCodeEnum.APARTMENT,
                        "count": apartment_count,
                        "area": {
                            "value": property_.get("RC_OWN_APARTMENTS_M2", 0),
                            "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                        },
                    }
                )

            business_count = property_.get("RC_BUSINESSPREMISE_COUNT")
            if business_count:
                statistics.append(
                    {
                        "premise_type_code": FIPremiseTypeCodeEnum.BUSINESS_PREMISE,
                        "count": business_count,
                        "area": {
                            "value": property_.get("RC_OWN_BUSINESSPREMISE_M2", 0),
                            "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                        },
                    }
                )

            carplug_count = property_.get("RC_CARPLUG_COUNT")
            if carplug_count:
                statistics.append(
                    {
                        "premise_type_code": FIPremiseTypeCodeEnum.OTHER_PREMISE,
                        "count": carplug_count,
                        "area": {
                            "value": 0,
                            "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                        },
                    }
                )

            carshelter_count = property_.get("RC_CARSHELTER_COUNT")
            if carshelter_count:
                statistics.append(
                    {
                        "premise_type_code": FIPremiseTypeCodeEnum.OTHER_PREMISE,
                        "count": carshelter_count,
                        "area": {
                            "value": 0,
                            "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                        },
                    }
                )

            return statistics

        def create_parking_spaces(property_):
            return [
                {
                    "parking_space_type_code": FIParkingSpaceTypeCodeEnum.GARAGE,
                    "count": property_.get("RC_GARAGE_COUNT", 0),
                },
                {
                    "parking_space_type_code": FIParkingSpaceTypeCodeEnum.PARKING_SPACE_SHARE,
                    "count": property_.get("RC_PARKINGSPACE_COUNT", 0),
                },
            ]

        if not fi_housing_company:
            energy_class = property_.get("RC_ENERGYCLASS", "")
            fi_housing_company = FIHousingCompany(
                scheme_id=FISchemeIdEnum.FI_ORGNR,
                energy_certificate={
                    "type_code": map_value(ENERGY_TYPE_MAPPING, energy_class),
                    "description": create_text_field(energy_class),
                },
            )

        create_or_update_address(property_, fi_housing_company)

        realty_options = property_.get("REALTYOPTIONS", {})
        fi_housing_company.buildings = create_buildings_info(property_, realty_options)
        fi_housing_company.name = property_.get("REALTYCOMPANY", "")
        fi_housing_company.yard_description = create_text_field(
            property_.get("COURTYARD_INFO", "")
        )
        fi_housing_company.property_identifier = property_.get("REALTYIDENTIFIER", "")
        fi_housing_company.manager_name = property_.get("RC_HOUSEMANAGER", "")
        fi_housing_company.house_manager_certificate = property_.get(
            "HOUSEMANAGER_CERTIFICATE_DATE", ""
        )
        fi_housing_company.postal_area = property_.get("RC_POSTAREA", "")
        fi_housing_company.street_address = property_.get("RC_STREET", "")
        fi_housing_company.business_id = property_.get("RC_BUSINESS_ID_CODE", "")
        fi_housing_company.maintenance_charge = property_.get(
            "CHARGES_MAINT_BASE_MONTH", 0
        )
        fi_housing_company.digital_share_group_identifier = property_.get(
            "STOCK_NUMBERS", 0
        )
        fi_housing_company.finances = create_finances_info(property_)
        fi_housing_company.asbestos_mapping = {
            "is_asbestos_mapping_report_available": (
                FIChoiceEnum.YES
                if property_.get("RC_ASBESTOS_REPORT_FLAG") == 1
                else FIChoiceEnum.NO
            ),
            "is_asbestos_mapping_done": (
                FIChoiceEnum.YES
                if property_.get("RC_ASBESTOS_FLAG") == 1
                or property_.get("RC_ASBESTOS_FLAG") == 2
                else FIChoiceEnum.NO
            ),
        }
        fi_housing_company.renovations = [
            {
                "status_code": (
                    FIRenovationStatusCodeEnum.PLANNED
                    if property_.get("RC_RENOVATION_PLANNED_FLAG") == 1
                    else FIRenovationStatusCodeEnum.PLANNED
                ),
                "description": create_text_field(
                    property_.get("RC_RENOVATION_PLANNED", "")
                ),
            }
        ]
        fi_housing_company.construction = {
            "construction_year": property_.get("RC_BUILDYEAR2", ""),
            "usage_start_year": property_.get("RC_BUILDYEAR2", ""),
            "construction_and_usage_year_description": create_text_field(
                property_.get("BUILDYEAR_DESC", "")
            ),
            "development_phase_code": DEVELOPMENT_PHASE_MAPPING.get(
                property_.get("RC_NEW_DEVELOPMENT_STATUS"),
                None,
            ),
        }
        fi_housing_company.additional_description = create_text_field(
            property_.get("RC_HAS_OTHER", "")
        )
        fi_housing_company.financing_charge = {
            "amount": property_.get("CHARGESMAINT2_MONTH", "")
        }
        fi_housing_company.renovations_description = create_text_field(
            property_.get("RC_RENOVATION_MADE", "")
        )
        fi_housing_company.renovations_planned_description = create_text_field(
            property_.get("RC_RENOVATION_PLANNED", "")
        )
        fi_housing_company.charges_description = create_text_field(
            property_.get("MANAGEMENT_CHARGES_INFO", "")
        )
        fi_housing_company.housing_company_premise_statistics = (
            create_housing_company_premise_statistics(property_)
        )
        fi_housing_company.parking_spaces = create_parking_spaces(property_)
        fi_housing_company.parking_spaces_description = create_text_field(
            property_.get("RC_PARKING_INFO", "")
        )
        fi_housing_company.maintenance = {
            "type_codes": [
                MAINTENANCE_TYPE_CODE_MAPPING.get(
                    property_.get("RC_MAINTENANCE", ""),
                    FIHousingCompanyMaintenanceTypeCodeEnum.PROPERTY_MAINTENANCE_COMPANY,
                )
            ],
            "description": create_text_field(property_.get("RC_MAINTENANCE_OTHER", "")),
        }
        collection = property_.get("REALTYOPTIONS", {}).get("COLLECTION", [])

        television_types = [
            TELEVISION_TYPE_MAPPING[item.lower()]
            for item in collection
            if item.lower() in TELEVISION_TYPE_MAPPING
        ]

        if television_types:
            fi_housing_company.television = {
                "type_codes": television_types,
            }
        fi_housing_company.special_charge = property_.get("CHARGES_SPECIAL_MONTH", None)

        inspection_mappings = [
            (
                "RC_CONDITIONCHECK_YEAR",
                FIHousingCompanyInspectionTypeCodeEnum.CONDITION_INSPECTION,
            ),
            (
                "RC_MOISTURECHECK_YEAR",
                FIHousingCompanyInspectionTypeCodeEnum.HUMIDITY_MEASUREMENT,
            ),
        ]

        inspections = [
            {
                "date": str(property_[field]),
                "type_code": type_code,
            }
            for field, type_code in inspection_mappings
            if property_.get(field)
        ]

        if inspections:
            fi_housing_company.inspections = inspections

        fi_housing_company.inspections_description = create_text_field(
            property_.get("RC_CONDITION_INFO", "")
        )

        # Create or update fi plot overview in fi_housing_company
        if len(fi_housing_company.fi_plot_overviews) > 0:
            fi_plot_overview = fi_housing_company.fi_plot_overviews[0]
        else:
            fi_plot_overview = None

        realtytype = property_.get("REALTYTYPE", "").lower()
        ownership_type = next(
            (
                group_type
                for key, group_type in OWNERSHIP_TYPE_MAPPING.items()
                if key in realtytype
            ),
            FIOwnershipTypeEnum.PROPERTY,
        )

        if not fi_plot_overview:
            fi_plot_overview = FIPlotOverview()
            fi_plot_overview.zonings = {
                "type_codes": [FIZoningTypeCodeEnum.FINAL_INSPECTION],
                "description": create_text_field(
                    property_.get("RC_FINALINSPECTION_YEAR", "")
                ),
            }
            if ownership_type == FIOwnershipTypeEnum.SHARE:
                realty_options = property_.get("REALTYOPTIONS", {})
                fi_plot_overview.beaches = self.get_beaches(realty_options, property_)
            self.db_session.add(fi_plot_overview)
            self.db_session.flush()
        else:
            if "type_codes" not in fi_plot_overview.zonings:
                fi_plot_overview.zonings["type_codes"] = []
            if (
                FIZoningTypeCodeEnum.FINAL_INSPECTION
                not in fi_plot_overview.zonings["type_codes"]
            ):
                fi_plot_overview.zonings["type_codes"].append(
                    FIZoningTypeCodeEnum.FINAL_INSPECTION
                )
            fi_plot_overview.zonings["description"] = create_text_field(
                property_.get("RC_FINALINSPECTION_YEAR", "")
            )
            if ownership_type == FIOwnershipTypeEnum.SHARE:
                realty_options = property_.get("REALTYOPTIONS", {})
                fi_plot_overview.beaches = self.get_beaches(realty_options, property_)

        self.db_session.add(fi_housing_company)
        self.db_session.flush()
        return fi_housing_company

    def update_or_create_fi_residential_property_overview(
        self,
        property_: dict,
        fi_residential_property_overview: FIResidentialPropertyOverview = None,
    ):
        if not fi_residential_property_overview:
            fi_residential_property_overview = FIResidentialPropertyOverview()
            self.db_session.add(fi_residential_property_overview)

        building_license = property_.get("BUILDING_LICENCE", "0")
        try:
            if isinstance(building_license, str) and "." in building_license:
                year = int(building_license.split(".")[-1])
            else:
                year = int(building_license)
        except (ValueError, IndexError):
            year = 0

        property_type = FIPropertyTypeEnum.from_finnish_name(
            property_.get("REALTYTYPE")
        )

        fi_residential_property_overview.residential_type_code = property_type.name
        fi_residential_property_overview.construction_permit_grant_year = year
        fi_residential_property_overview.apartment = {
            "foundation": {
                "description": create_text_field(property_.get("FOUNDATION", ""))
            }
        }

        self.db_session.flush()
        return fi_residential_property_overview

    def update_or_create_fi_estate_overview(
        self,
        property_: dict,
        fi_estate_overview: FIEstateOverview = None,
    ):
        if not fi_estate_overview:
            fi_estate_overview = FIEstateOverview(
                estate_property_type_code=FIEstatePropertyTypeCodeEnum.OTHER
            )
            self.db_session.add(fi_estate_overview)

        fi_estate_overview.areas = [
            {
                "type_code": FIEstateAreaTypeCodeEnum.FOREST,
                "area": {
                    "value": property_.get("WOODLAND_AREA_HA", 0),
                    "area_unit_code": FIAreaUnitCodeEnum.HECTARES,
                },
            }
        ]
        fi_estate_overview.immediate_forest_logging_possibilities_in_cubic_meters = (
            property_.get("LOGGING_POTENTIAL_M3", 0)
        )
        fi_estate_overview.land_areas = [
            {
                "type_code": FIEstateAreaTypeCodeEnum.ARABLE,
                "area": {
                    "value": property_.get("ARABLELAND_AREA_HA", 0),
                    "area_unit_code": FIAreaUnitCodeEnum.HECTARES,
                },
            }
        ]

        self.db_session.flush()

        return fi_estate_overview

    def update_or_create_fi_residential_share_overview(
        self,
        property_: dict,
        ownership_type_code: FIOwnershipTypeCodeEnum,
        fi_residential_share_overview: FIResidentialShareOverview = None,
    ) -> FIResidentialShareOverview:
        realty_options = property_.get("REALTYOPTIONS", {})

        flat_type = property_.get("FLATTYPE", "")
        room_count_code = ROOM_COUNT_MAPPING.get(
            flat_type,
            "5H+" if isinstance(flat_type, str) and "enemmän" in flat_type else None,
        )

        try:
            floor = int(property_.get("FLOOR") or 0)
            floors = int(property_.get("FLOORS") or 1)
        except ValueError:
            floor = 0
            floors = 1
        ratio = floor / floors if floors != 0 else 0
        floor_level_code = (
            FIFloorLevelCodeEnum.ON_TOP_FLOOR
            if ratio > 0.5
            else (
                FIFloorLevelCodeEnum.ON_BOTTOM_FLOOR
                if ratio < 0.5
                else FIFloorLevelCodeEnum.ON_MIDDLE_FLOOR
            )
        )

        balcony_values = realty_options.get("BALCONY", [])
        compass_points = realty_options.get("BALCONYDIRECTION", [])
        mapped_compass_points = [
            COMPASS_POINTS_MAPPING.get(point) for point in compass_points
        ]

        balcony = [
            {
                "balcony_type_code": BALCONY_MAPPING.get(balcony_value),
                "compass_points": mapped_compass_points,
            }
            for balcony_value in balcony_values
        ]

        def get_material_codes(materials, mapping_enum):
            codes = [
                mapping_enum.from_finnish_name(material)
                for material in materials
                if material
            ]
            return [c for c in codes if c is not None]

        def get_room_info(
            type_code,
            description_keys,
            floor_key=None,
            wall_key=None,
            fixture_keys=None,
        ):
            return {
                "type_code": type_code,
                "description": create_text_field(
                    " ".join(
                        filter(
                            None,
                            [
                                (
                                    ", ".join(realty_options.get(key, []))
                                    if isinstance(realty_options.get(key), list)
                                    else (
                                        realty_options.get(key)
                                        or property_.get(key, "")
                                    )
                                )
                                for key in description_keys
                            ],
                        )
                    )
                ),
                "floor_material_codes": (
                    get_material_codes(
                        realty_options.get(floor_key, []),
                        FIFloorSurfaceMaterialCodeEnum,
                    )
                    if floor_key
                    else []
                ),
                "wall_material_codes": (
                    get_material_codes(
                        realty_options.get(wall_key, []), FIWallSurfaceMaterialCodeEnum
                    )
                    if wall_key
                    else []
                ),
                "ceiling_material_codes": [],
                "features": (
                    [
                        {
                            "description": create_text_field(
                                ", ".join(realty_options.get(fixture_key, []))
                            ),
                            "feature_codes": [
                                FINNISH_FEATURE_MAPPING.get(feature.lower())
                                for feature in realty_options.get(fixture_key, [])
                                if FINNISH_FEATURE_MAPPING.get(feature.lower())
                            ],
                        }
                        for fixture_key in fixture_keys
                    ]
                    if fixture_keys
                    else []
                ),
            }

        fireplace_values = property_.get("REALTYOPTIONS", {}).get("FIREPLACE") or []
        fireplace_other = property_.get("FIREPLACE_OTHER")

        hearth_data = {}

        if fireplace_other:
            hearth_data["description"] = create_text_field(fireplace_other)

        mapped_enums = [
            FIREPLACE_MAPPING[val]
            for val in fireplace_values
            if val in FIREPLACE_MAPPING
        ]
        if mapped_enums:
            hearth_data["type_codes"] = mapped_enums

        if "muu" in fireplace_values and fireplace_other:
            hearth_data["type_other_description"] = create_text_field(fireplace_other)

        apartment_info = {
            "area_basis_codes": [
                (
                    AREA_BASIS_MAPPING.get(property_.get("AREABASIS", [])[0])
                    if property_.get("AREABASIS")
                    else None
                )
            ],
            "official_room_structure": property_.get("FLAT_STRUCTURE", ""),
            "room_count_code": room_count_code,
            "area": {
                "living_area": {
                    "value": property_.get("LIVING_AREA_M2"),
                    "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                },
                "other_space_area": {
                    "value": property_.get("OTHER_AREA_M2"),
                    "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                },
                "total_area": {
                    "value": property_.get("TOTAL_AREA_M2"),
                    "area_unit_code": FIAreaUnitCodeEnum.SQUARE_METERS,
                },
            },
            "floors": {
                "floor_level": floor,
                "floor_level_code": floor_level_code,
                "living_floor_count": str(floor),
                "total_floor_count": floors,
            },
            "hearth": hearth_data,
            "interior_material_description": create_text_field(
                property_.get("CONSTRUCTIONMATERIAL_DESC", "")
            ),
            "rooms": [
                get_room_info(FITypeCodeEnum.OTHER, ["OTHERSPACE_DESC"]),
                get_room_info(
                    FITypeCodeEnum.KITCHEN,
                    ["KITCHEN_EQUIPMENT_DESC", "KITCHEN_DESC", "STOVE"],
                    "KITCHENFLOOR",
                    "KITCHENWALL",
                    [
                        "FRIDGEANDFREEZER",
                        "KITCHENOTHERSEQUIPMENT",
                        "KITCHEN_EQUIPMENT_DESC",
                    ],
                ),
                get_room_info(FITypeCodeEnum.DINING_ROOM, ["DININGROOM_DESC"]),
                get_room_info(
                    FITypeCodeEnum.BATH_ROOM,
                    ["BATHROOM_DESC"],
                    "BATHROOMFLOOR",
                    "BATHROOMWALL",
                    ["BATHROOMFIXTURE"],
                ),
                get_room_info(
                    FITypeCodeEnum.BEDROOM,
                    ["BEDROOM_DESC"],
                    "BEDROOMFLOOR",
                    "BEDROOMWALL",
                ),
                get_room_info(
                    FITypeCodeEnum.SAUNA,
                    ["SAUNA_DESC"],
                    "SAUNAFLOOR",
                    "SAUNAWALL",
                    ["SAUNAOVEN"],
                ),
                get_room_info(
                    FITypeCodeEnum.TOILET,
                    ["TOILET_INFO"],
                    "TOILETFLOOR",
                    "TOILETWALL",
                    ["TOILETFIXTURE"],
                ),
                get_room_info(FITypeCodeEnum.UTILITY_ROOM, ["UTILITYROOM_DESC"]),
                get_room_info(
                    FITypeCodeEnum.LAUNDRY,
                    ["LAUNDRY"],
                    "LAUNDRYFLOOR",
                    "LAUNDRYWALL",
                    ["LAUNDRYFIXTURE"],
                ),
                get_room_info(
                    FITypeCodeEnum.LIVING_ROOM,
                    ["LIVINGROOM_INFO"],
                    "LIVINGROOMFLOOR",
                    "LIVINGROOMWALL",
                    "LIVINGROOMWALL",
                ),
            ],
            "foundation": {
                "description": create_text_field(property_.get("FOUNDATION", ""))
            },
            "has_own_sauna": (
                FIChoiceEnum.YES if property_.get("SAUNATYPE") else FIChoiceEnum.NO
            ),
            "has_balcony": (
                FIChoiceEnum.YES if realty_options.get("BALCONY") else FIChoiceEnum.NO
            ),
            "has_terrace": (
                FIChoiceEnum.YES
                if any(
                    space in ["Terassi", "Lasitettu terassi"]
                    for space in realty_options.get("OTHERSPACES", [])
                )
                else FIChoiceEnum.NO
            ),
            "balcony": balcony,
            "number_of_toilets": property_.get("TOILET_COUNT") or 0,
            "number_of_bedrooms": property_.get("BEDROOM_COUNT") or 0,
            "storage_description": create_text_field(
                property_.get("STORAGE_CONDITION", "")
            ),
            "renovations_description": create_text_field(
                property_.get("RENOVATION_MADE")
            ),
            "furnished": (
                FIChoiceEnum.YES
                if property_.get("FURNISHED_FLAG") == 1
                else FIChoiceEnum.NO
            ),
        }

        if not fi_residential_share_overview:
            fi_residential_share_overview = FIResidentialShareOverview()
            self.db_session.add(fi_residential_share_overview)

        property_type = FIPropertyTypeEnum.from_finnish_name(
            property_.get("REALTYTYPE")
        )
        other_spaces = property_.get("OTHERSPACES") or []
        other_spaces_str = ", ".join(other_spaces) if other_spaces else None

        fi_residential_share_overview.residential_type_code = property_type.name

        fi_residential_share_overview.administration = {
            "type_code": ADMINSTRATION_TYPE_CODE_MAPPING.get(
                (property_.get("HOLDINGTYPE", "") or "").lower(),
                FIAdministrationTypeCodeEnum.APARTMENT_HOUSING_COMPANY,
            ),
        }
        fi_residential_share_overview.apartment = apartment_info
        fi_residential_share_overview.ownership_type_code = ownership_type_code
        fi_residential_share_overview.debt_free_price = property_.get(
            "ASSIGNMENT", {}
        ).get("UNENCUMBERED_PRICE")
        fi_residential_share_overview.parking_space_description = create_text_field(
            property_.get("PARKINGPLACE_INFO", "")
        )
        fi_residential_share_overview.storages = [
            {
                "basis_for_possession_code": realty_options.get("STORAGERENTED"),
                "transfer_code": realty_options.get("STORAGERENTINGTRANSFERABLE"),
            }
        ]
        fi_residential_share_overview.redemption = {
            "redeemable_by_housing_company": (
                FIChoiceEnum.YES
                if property_.get("CLAIM_RIGHT_COMPANY") == 1
                else FIChoiceEnum.NO
            ),
            "redeemable_by_existing_shareholders": (
                FIChoiceEnum.YES
                if property_.get("CLAIM_RIGHT_SHAREHOLDER") == 1
                else FIChoiceEnum.NO
            ),
            "other_restrictions": create_text_field(
                property_.get("TRANSFER_RESTRICTION", "")
            ),
        }
        fi_residential_share_overview.more_information_about_the_materials = (
            create_text_field(
                " ".join(
                    filter(
                        None,
                        [
                            property_.get("WALLMATERIAL_INFO"),
                            property_.get("FLOORMATERIAL_INFO"),
                            property_.get("ROOFMATERIAL_INFO"),
                            property_.get("FLOOR_HEATING_DECS"),
                            other_spaces_str,
                        ],
                    )
                )
            )
        )

        self.db_session.flush()

        return fi_residential_share_overview

    def update_exist_fi_property(
        self,
        fi_property: FIProperty,
        property_: dict,
        reference: str,
        uploaded_images: list,
    ):
        fi_property_type = (
            self.db_session.query(FIPropertyType)
            .filter(FIPropertyType.id == fi_property.fi_property_type_id)
            .first()
        )
        fi_address = (
            self.db_session.query(FIAddress)
            .filter(FIAddress.id == fi_property.fi_realty.fi_address_id)
            .first()
        )
        fi_realty = (
            self.db_session.query(FIRealty)
            .filter(FIRealty.id == fi_property.fi_realty_id)
            .first()
        )
        fi_property_overview = (
            self.db_session.query(FIPropertyOverview)
            .filter(FIPropertyOverview.id == fi_property.fi_property_overview_id)
            .first()
        )
        fi_plot_overview = (
            self.db_session.query(FIPlotOverview)
            .filter(FIPlotOverview.id == fi_property.fi_plot_overview_id)
            .first()
        )
        fi_residential_share_overview = (
            self.db_session.query(FIResidentialShareOverview)
            .filter(
                FIResidentialShareOverview.id
                == fi_property.fi_residential_share_overview_id
            )
            .first()
        )
        fi_estate_overview = (
            self.db_session.query(FIEstateOverview)
            .filter(FIEstateOverview.id == fi_property.fi_estate_overview_id)
            .first()
        )
        fi_residential_property_overview = (
            self.db_session.query(FIResidentialPropertyOverview)
            .filter(
                FIResidentialPropertyOverview.id
                == fi_property.fi_residential_property_overview_id
            )
            .first()
        )
        property_description = (
            self.db_session.query(PropertyDescription)
            .filter(PropertyDescription.property_id == fi_property.id)
            .first()
        )
        property_realtor = (
            self.db_session.query(PropertyRealtor)
            .filter(PropertyRealtor.property_id == fi_property.id)
            .first()
        )
        fi_other_share_overview = (
            self.db_session.query(FIOtherShareOverview)
            .filter(FIOtherShareOverview.id == fi_property.fi_other_share_overview_id)
            .first()
        )
        fi_housing_company = (
            self.db_session.query(FIHousingCompany)
            .filter(FIHousingCompany.id == fi_property.fi_housing_company_id)
            .first()
        )

        realtytype = property_.get("REALTYTYPE", "").lower()
        property_type = FIPropertyTypeEnum.from_finnish_name(realtytype)
        if not property_type:
            logger.error(
                f"Cannot map property type from Finnish name: {property_.get('REALTYTYPE')}"
            )
        listing_type = (
            FIListingTypeEnum.RENTAL
            if property_.get("STATUS_CODE") == "FOR_RENT"
            else FIListingTypeEnum.SALE
        )
        ownership_type = next(
            (
                group_type
                for key, group_type in OWNERSHIP_TYPE_MAPPING.items()
                if key in realtytype
            ),
            FIOwnershipTypeEnum.PROPERTY,
        )
        property_type_group = next(
            (
                group_type
                for key, group_type in PROPERTY_TYPE_GROUP_MAPPING.items()
                if key in realtytype
            ),
            FIPropertyTypeGroupEnum.OTHER,
        )
        fi_property_type = (
            self.db_session.query(FIPropertyType)
            .filter_by(
                property_type=property_type,
                listing_type=listing_type,
                ownership_type=ownership_type,
                property_type_group=property_type_group,
            )
            .first()
        )

        if not fi_property_type:
            fi_property_type = FIPropertyType(
                property_type=property_type,
                listing_type=listing_type,
                ownership_type=ownership_type,
                property_type_group=property_type_group,
            )
            self.db_session.add(fi_property_type)
            self.db_session.flush()
        fi_address = self.update_or_create_fi_address(
            property_=property_,
            fi_address=fi_address,
        )
        fi_realty = self.update_or_create_fi_realty(property_, fi_address, fi_realty)
        fi_other_share_overview = self.update_or_create_fi_other_share_overview(
            property_, fi_other_share_overview
        )
        fi_property_overview = self.update_or_create_fi_property_overview(
            property_, fi_property_overview
        )
        fi_plot_overview = self.update_or_create_fi_plot_overview(
            property_, fi_plot_overview
        )
        fi_housing_company = self.update_or_create_fi_housing_company(
            property_, fi_housing_company
        )
        fi_estate_overview = self.update_or_create_fi_estate_overview(
            property_, fi_estate_overview
        )
        fi_residential_property_overview = (
            self.update_or_create_fi_residential_property_overview(
                property_, fi_residential_property_overview
            )
        )

        ownership_type_code = FIOwnershipTypeCodeEnum.from_finnish_name(
            property_.get("OWNINGTYPE")
        )
        if not ownership_type_code:
            logger.warning(
                f"Cannot map ownership type code from Finnish name: {property_.get('OWNINGTYPE')}"
            )
        fi_residential_share_overview = (
            self.update_or_create_fi_residential_share_overview(
                property_,
                ownership_type_code,
                fi_residential_share_overview,
            )
        )

        org = self.db_session.query(Organization).filter_by(country_code="FI").first()
        fi_property.market = "finland"
        fi_property.reference = reference
        fi_property.data_source = "kivi"
        fi_property.organization_id = org.id
        fi_property.fi_property_type_id = fi_property_type.id
        fi_property.fi_realty_id = fi_realty.id
        fi_property.fi_property_overview_id = fi_property_overview.id
        fi_property.fi_residential_share_overview_id = fi_residential_share_overview.id
        fi_property.fi_plot_overview_id = fi_plot_overview.id
        fi_property.fi_other_share_overview_id = fi_other_share_overview.id
        fi_property.fi_housing_company_id = fi_housing_company.id
        fi_property.imported_data = property_
        fi_property.latitude = property_.get("LAT")
        fi_property.longitude = property_.get("LON")
        fi_property.updated_at = (
            datetime.strptime(property_.get("CHANGEDATE", ""), "%Y-%m-%dT%H:%M:%S.%fZ")
            if property_.get("CHANGEDATE", "")
            else None
        )

        rc_street = property_.get("RC_STREET", "")
        quarter_of_town = (
            property_.get("QUARTEROFTOWN", "").split(",")[-1].strip()
            if property_.get("QUARTEROFTOWN", "")
            else ""
        )
        municipality = property_.get("MUNICIPALITY", "")
        assignment = property_.get("ASSIGNMENT", {}).get("AGENT", {})
        assignment_email = assignment.get("EMAIL", "")

        if property_realtor:
            realtor = (
                self.db_session.query(User)
                .filter(User.email == assignment_email)
                .first()
            )
            if realtor:
                property_realtor.user_id = realtor.id
        else:
            user = (
                self.db_session.query(User)
                .filter(User.email == assignment_email)
                .first()
            )
            if not user:
                user = User(
                    email=assignment_email,
                    first_name=assignment.get("FIRSTNAME", ""),
                    last_name=assignment.get("LASTNAME", ""),
                )
                self.db_session.add(user)
                self.db_session.flush()

            property_realtor = PropertyRealtor(
                property_id=fi_property.id,
                user_id=user.id,
            )
            self.db_session.add(property_realtor)
            self.db_session.flush()

        if property_description:
            property_description.tagline = " ".join(
                [rc_street, quarter_of_town, municipality]
            )
            property_description.description = property_.get("PRESENTATION")
            property_description.type = DescriptionType.FULL
            property_description.language = Language.FINNISH
        else:
            property_description = PropertyDescription(
                property_id=fi_property.id,
                tagline=" ".join([rc_street, quarter_of_town, municipality]),
                description=property_.get("PRESENTATION"),
                type=DescriptionType.FULL,
                language=Language.FINNISH,
            )
            self.db_session.add(property_description)
            self.db_session.flush()

        self._import_documents_to_document_library(
            kivi_property=property_,
            fi_property=fi_property,
        )
        self.extract_image_data(
            fi_property=fi_property, uploaded_images=uploaded_images
        )

        self.db_session.commit()

    def extract_property_basic_info(
        self,
        property_: dict,
        statistic: bool,
        uploaded_images: list,
    ) -> dict:
        property_ = TrackingProcessedField(property_)
        reference = property_.get("REALTY_UNIQUE_NO")
        fi_property = (
            self.db_session.query(FIProperty)
            .filter(FIProperty.reference == reference)
            .first()
        )
        if fi_property:
            self.update_exist_fi_property(
                fi_property, property_, reference, uploaded_images
            )
        else:
            property_type = FIPropertyTypeEnum.from_finnish_name(
                property_.get("REALTYTYPE")
            )
            if not property_type:
                logger.error(
                    f"Can not map property type from finish name: {property_.get('REALTYTYPE')}"
                )

            ownership_type_group = OWNERSHIP_TYPE_MAPPING.get(
                property_.get("OWNINGTYPE"), FIOwnershipTypeEnum.PROPERTY
            )
            listing_type = (
                FIListingTypeEnum.RENTAL
                if property_.get("STATUS_CODE") == "FOR_RENT"
                else FIListingTypeEnum.SALE
            )
            fi_property_type = (
                self.db_session.query(FIPropertyType)
                .filter_by(
                    property_type=property_type,
                    listing_type=listing_type,
                    ownership_type=ownership_type_group,
                    property_type_group=FIPropertyTypeGroupEnum.PLOT,
                )
                .first()
            )
            if not fi_property_type:
                fi_property_type = FIPropertyType(
                    listing_type=listing_type,
                    ownership_type=ownership_type_group,
                    property_type_group=FIPropertyTypeGroupEnum.PLOT,
                    property_type=property_type,
                )
                self.db_session.add(fi_property_type)

            fi_address = self.update_or_create_fi_address(
                property_=property_,
            )
            fi_realty = self.update_or_create_fi_realty(property_, fi_address)
            fi_other_share_overview = self.update_or_create_fi_other_share_overview(
                property_
            )
            fi_property_overview = self.update_or_create_fi_property_overview(property_)
            fi_plot_overview = self.update_or_create_fi_plot_overview(property_)
            fi_housing_company = self.update_or_create_fi_housing_company(property_)
            fi_estate_overview = self.update_or_create_fi_estate_overview(property_)
            fi_residential_property_overview = (
                self.update_or_create_fi_residential_property_overview(property_)
            )

            ownership_type_code = FIOwnershipTypeCodeEnum.from_finnish_name(
                property_.get("OWNINGTYPE")
            )
            if not ownership_type_code:
                logger.error(
                    f"Can not map ownership type code from finish name: {property_.get('OWNINGTYPE')}"
                )

            fi_residential_share_overview = (
                self.update_or_create_fi_residential_share_overview(
                    property_, ownership_type_code
                )
            )

            self.db_session.flush()

            org = (
                self.db_session.query(Organization).filter_by(country_code="FI").first()
            )
            fi_property = FIProperty(
                market="finland",
                reference=reference,
                data_source="kivi",
                latitude=property_.get("LAT"),
                longitude=property_.get("LON"),
                imported_data=property_,
                organization_id=org.id,
                fi_property_type_id=fi_property_type.id,
                fi_realty_id=fi_realty.id,
                fi_property_overview_id=fi_property_overview.id,
                fi_residential_share_overview_id=fi_residential_share_overview.id,
                fi_plot_overview_id=fi_plot_overview.id,
                fi_other_share_overview_id=fi_other_share_overview.id,
                fi_housing_company_id=fi_housing_company.id,
                fi_estate_overview_id=fi_estate_overview.id,
                fi_residential_property_overview_id=fi_residential_property_overview.id,
            )
            self.db_session.add(fi_property)
            self.db_session.flush()

            property_description = PropertyDescription(
                property_id=fi_property.id,
                tagline="",
                description=property_.get("PRESENTATION"),
                type=DescriptionType.FULL,
                language=Language.FINNISH,
            )
            self.db_session.add(property_description)
            self.db_session.flush()

            assignment = property_.get("ASSIGNMENT", {}).get("AGENT", {})
            assignment_email = assignment.get("EMAIL", "")
            user = (
                self.db_session.query(User)
                .filter(User.email == assignment_email)
                .first()
            )
            if not user:
                user = User(
                    email=assignment_email,
                    first_name=assignment.get("FIRSTNAME", ""),
                    last_name=assignment.get("LASTNAME", ""),
                )
                self.db_session.add(user)
                self.db_session.flush()

            property_realtor = PropertyRealtor(
                property_id=fi_property.id,
                user_id=user.id,
            )
            self.db_session.add(property_realtor)
            self.db_session.flush()

            self._import_documents_to_document_library(
                kivi_property=property_,
                fi_property=fi_property,
            )
            self.extract_image_data(
                fi_property=fi_property, uploaded_images=uploaded_images
            )

            self.db_session.commit()

        if statistic:
            property_.log_unprocessed_fields(
                property_dict=property_,
                processed_fields=property_.get_processed_fields(),
            )

        return {
            "message": f"FIProperty with reference '{reference}' updated successfully"
        }

    def extract_image_data(self, fi_property: FIProperty, uploaded_images: list):
        self.db_session.query(Image).filter_by(property_id=fi_property.id).delete()
        self.db_session.flush()

        for img in uploaded_images:
            filename = img["filename"]
            image = img["image_data"]
            url = self.generate_s3_url(fi_property, filename)
            self.update_or_create_image_record(fi_property.id, url, image)

    def generate_s3_url(self, fi_property: FIProperty, filename: str):
        filepath = Path(filename)
        upload_path = self._s3_file_path(fi_property.reference, filepath, "image")
        s3_url = f"{app_cfg.aws_s3_bucket_url}/{upload_path}"
        cloudfront_url = s3_url.replace(
            app_cfg.aws_s3_bucket_url, app_cfg.aws_cloudfront_url
        )

        return cloudfront_url

    def update_or_create_image_record(self, property_id: int, url: str, image):
        existing_image = (
            self.db_session.query(Image)
            .filter_by(property_id=property_id, url=url)
            .one_or_none()
        )

        image_data = {
            "property_id": property_id,
            "url": url,
            "order": image.get("SEQ", 0),
            "is_hidden": image.get("IMAGE_IV_SELECT_FLAG", 0) == 0,
        }

        if existing_image:
            for key, value in image_data.items():
                setattr(existing_image, key, value)
        else:
            new_image = Image(**image_data)
            self.db_session.add(new_image)
            self.db_session.flush()

    def upload_file_to_s3(
        self,
        reference: str,
        filename: str,
        url: str,
        content_type: str,
        type: str,
    ):
        bucket_name = (
            app_cfg.aws_s3_bucket_name
            if type == "image"
            else app_cfg.aws_s3_files_bucket_name
        )
        s3_service = S3Service(app_cfg, bucket_name)
        filepath = Path(filename)
        upload_path = self._s3_file_path(reference, filepath, type)
        file_data = self.download_document(url=url, type=type)
        s3_service.upload(path=upload_path, file=file_data, content_type=content_type)

    def download_document(self, url: str, type: str):
        modified_url = (
            url.replace("download", "printDocument") if type == "document" else url
        )
        response = requests.get(modified_url, stream=True)
        response.raise_for_status()
        return response.content

    def _s3_file_path(self, reference: str, key: str | Path, type: str) -> str:
        base_path = "images" if type == "image" else "files"
        if type == "image":
            return f"{base_path}/{datetime.utcnow():%Y-%m-%d}/{reference}-{key}"
        return f"{base_path}/{reference}/{key}"

    def run(self, *, reference: str, statistic: bool = False):
        params = {"FIELD_SET": "extensive"}
        try:
            response = requests.get(
                self.KIVI_REALTIES_API.format(
                    kivi_api_base_url=KIVI_API_BASE_URL,
                    reference=reference,
                ),
                params=params,
                auth=HTTPBasicAuth(app_cfg.kivi_username, app_cfg.kivi_password),
                timeout=10,
            )
            response.raise_for_status()
            data = response.json()

            # Extract images and documents before transaction
            images = data.get("IMAGES", [])
            uploaded_images = self.prepare_images_data(images, reference)

            with self.db_session.begin():
                return self.extract_property_basic_info(
                    data, statistic, uploaded_images
                )

        except requests.exceptions.RequestException as e:
            logger.error(f"Kivi API error on final request: {e}")
            raise ApiError("Failed to import FI property from Kivi")

    def parse_float(self, value):
        if value is None:
            return None
        try:
            return float(str(value).replace(",", "."))
        except ValueError:
            return None

    def prepare_images_data(self, images: list, reference: str):
        uploaded_images = []
        for image in images:
            image_desc = image.get("IMAGE_DESC") or ""
            image_id = image.get("IMAGE_ID") or ""
            filename = f"{image_desc} {image_id}.jpeg"
            download_url = image.get("IMAGE_URL", "")
            content_type = "image/jpeg"
            if not download_url:
                continue

            try:
                self.upload_file_to_s3(
                    reference=reference,
                    filename=filename,
                    url=download_url,
                    content_type=content_type,
                    type="image",
                )
                uploaded_images.append(
                    {
                        "filename": filename,
                        "image_data": image,
                    }
                )
            except Exception as e:
                logger.error(f"Error uploading image: {image} and error: {e}")
                logger.exception(e)
                continue
        return uploaded_images

    def _import_documents_to_document_library(
        self,
        kivi_property: dict,
        fi_property: FIProperty,
    ):
        assignment_id = kivi_property.get("ASSIGNMENT", {}).get("ASSIGNMENT_ID", None)
        if not assignment_id:
            return

        kivi_username = app_cfg.kivi_username
        if not kivi_username:
            raise ValueError("Kivi username is not set")

        kivi_password = app_cfg.kivi_password
        if not kivi_password:
            raise ValueError("Kivi password is not set")

        importer = KiviDocumentImporter(
            db_session=self.db_session,
            kivi_document_api=self.KIVI_DOCUMENT_API.format(
                kivi_api_base_url=KIVI_API_BASE_URL,
            ),
            kivi_username=kivi_username,
            kivi_password=kivi_password,
        )
        importer.import_documents(fi_property, assignment_id)

    def get_beaches(self, realty_options, property_):
        waterfront_values = realty_options.get("WATERFRONT", [])
        beaches = []

        for idx, waterfront in enumerate(waterfront_values):
            code = WATERFRONT_MAPPING.get(waterfront.lower())
            if not code:
                continue

            beach = {
                "code": code,
            }

            if idx == 0:
                desc = create_text_field(
                    realty_options.get("WATERFRONT_DESC")
                    or property_.get("WATERFRONT_DESC", "")
                )
                if desc:
                    beach["description"] = desc

            beaches.append(beach)
        return beaches


class KiviDocumentDownloadLink(BaseModel):
    EXPIRE_DAYS: Optional[int] = None
    URL: Optional[str] = None


class KiviDocument(BaseModel):
    ASSIGNMENT_ID: int
    CONTENT_TYPE: str
    CREATEDATE: str
    DOCUMENT_ID: int
    DOCUMENT_TYPE: Optional[str] = None
    FILENAME: str
    DOWNLOAD_LINK: KiviDocumentDownloadLink = KiviDocumentDownloadLink()
    SENDABLE_FLAG: int
    UPDATEDATE: Optional[str] = None


class KiviDocumentImporter:
    def __init__(
        self,
        db_session: Session,
        kivi_document_api: str,
        kivi_username: str,
        kivi_password: str,
    ):
        self._db_session = db_session
        self._kivi_document_api = kivi_document_api
        self._kivi_username = kivi_username
        self._kivi_password = kivi_password

    def import_documents(self, fi_property: FIProperty, assignment_id: int):
        document_library = create_fi_property_document_library(
            db_session=self._db_session,
            user_id=fi_property.realtor_users[0].id,
            organization_id=fi_property.organization_id,
            property_id=fi_property.id,
            is_admin=True,
        )

        documents = self._fetch_kivi_documents(assignment_id)
        previously_imported_documents = self._get_existing_kivi_documents(assignment_id)
        imported_document_ids: List[int] = []

        for document in documents:
            existing_document = self._find_existing_document(
                document, previously_imported_documents
            )
            if existing_document:
                item = self._reimport_document(
                    document_library,
                    existing_document.document_library_item_id,
                    document,
                )
                if item:
                    imported_document_ids.append(item.id)

            else:
                item = self._import_document(document_library, assignment_id, document)
                if item:
                    imported_document_ids.append(item.id)

        self._db_session.flush()

        document_library.delete_items(
            [
                doc.document_library_item_id
                for doc in previously_imported_documents
                if doc.document_library_item_id not in imported_document_ids
            ]
        )

    def _fetch_kivi_documents(self, assignment_id: int) -> List[KiviDocument]:
        if not assignment_id:
            return []

        params = {"ASSIGNMENT_ID": assignment_id}
        response = requests.get(
            self._kivi_document_api,
            params=params,
            auth=HTTPBasicAuth(self._kivi_username, self._kivi_password),
            timeout=10,
        )
        response.raise_for_status()
        return [KiviDocument.model_validate(document) for document in response.json()]

    def _get_existing_kivi_documents(
        self, assignment_id: int
    ) -> List[KiviDocumentLink]:
        if not assignment_id:
            return []

        return (
            self._db_session.query(KiviDocumentLink)
            .filter_by(kivi_assignment_id=assignment_id)
            .all()
        )

    def _import_document(
        self,
        document_library: DocumentLibrary,
        assignment_id: int,
        document: KiviDocument,
    ) -> DocumentLibraryItem | None:
        if not document.DOWNLOAD_LINK or not document.DOWNLOAD_LINK.URL:
            return None

        content = self._download_kivi_document_content(document.DOWNLOAD_LINK.URL)

        item = document_library.create_item_from_content(
            content=content,
            filename=document.FILENAME,
            mime_type=document.CONTENT_TYPE,
            document_type=self._map_document_type(document),
        )

        self._db_session.add(
            KiviDocumentLink(
                kivi_assignment_id=assignment_id,
                kivi_document_id=document.DOCUMENT_ID,
                kivi_document_type=document.DOCUMENT_TYPE,
                document_library_item_id=item.id,
            )
        )

        return item

    def _reimport_document(
        self,
        document_library: DocumentLibrary,
        existing_document_id: int,
        document: KiviDocument,
    ) -> DocumentLibraryItem | None:
        if not document.DOWNLOAD_LINK or not document.DOWNLOAD_LINK.URL:
            return None

        content = self._download_kivi_document_content(document.DOWNLOAD_LINK.URL)

        item = document_library.update_item_content(
            id=existing_document_id,
            content=content,
            filename=document.FILENAME,
            mime_type=document.CONTENT_TYPE,
        )

        new_document_type = self._map_document_type(document)
        if new_document_type != item.document_type:
            document_library.update_item(
                id=existing_document_id,
                document_type=new_document_type,
                description=item.description,
            )

        return item

    def _download_kivi_document_content(self, url: str):
        modified_url = url.replace("download", "printDocument")
        response = requests.get(modified_url, stream=True)
        response.raise_for_status()
        return response.content

    def _map_document_type(self, document: KiviDocument) -> DocumentType:
        return DocumentType.FI_OTHER

    def _find_existing_document(
        self,
        document: KiviDocument,
        previously_imported_documents: List[KiviDocumentLink],
    ) -> KiviDocumentLink | None:
        return next(
            (
                doc
                for doc in previously_imported_documents
                if doc.kivi_document_id == document.DOCUMENT_ID
            ),
            None,
        )
