from strandproperties.config import app_cfg
from strandproperties.libs.sowise import SowiseClient
from strandproperties.models.organization import Organization


class SowiseClientMiddleware:
    def __init__(self, handler, registry):
        self.handler = handler
        self.registry = registry

    def __call__(self, request):
        if getattr(request, "_apply_sowise_middleware", False):
            org = request.db_session.query(Organization).filter_by(id=1).first()
            if "sowise_api_key" not in org.details:
                raise Exception("Missing sowise api key")

            c = SowiseClient(
                access_key=app_cfg.sowise_access_key,
                base_url=app_cfg.sowise_base_url,
                api_key=org.details["sowise_api_key"],
                selected_company=app_cfg.sowise_selected_company,
            )

            c._validate_headers()

            request.sowise_client = c
        response = self.handler(request)
        return response
