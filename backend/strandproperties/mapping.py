from strandproperties.schemas.fi_property.fi_common import (
    FeatureCodeEnum,
    FIAdministrationTypeCodeEnum,
    FIAreaBasisCodeEnum,
    FICompassPointEnum,
    FIHearthTypeCodeEnum,
    FIHeatingSystemCodeEnum,
    FIOuterRoofMaterialCodeEnum,
    FIOuterRoofTypeCodeEnum,
    FIPropertyStorageTypeCodeEnum,
    FIResidentialPropertyBalconyTypeCodeEnum,
    FITelevisionTypeCodeEnum,
    FIVentilationSystemCodeEnum,
)
from strandproperties.schemas.fi_property.fi_housing_company import (
    FIDevelopmentPhaseCodeEnum,
    FIHousingCompanyMaintenanceTypeCodeEnum,
    FIHousingCompanyPremiseTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_plot import (
    FIBeachCodeEnum,
    FIHoldingTypeCodeEnum,
    FIZoningTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_property_details import (
    FISewageDisposalSystemCodeEnum,
)
from strandproperties.schemas.fi_property.fi_property_type import (
    FIOwnershipTypeEnum,
    FIPropertyTypeGroupEnum,
)
from strandproperties.schemas.fi_property.fi_realty import (
    FIChargePeriodCodeEnum,
    FIConditionCodeEnum,
    FICostTypeCodeEnum,
    FILivingFormTypeCodeEnum,
    FIRealtyAvailabilityCodeEnum,
    FIWaterChargeTypeCodeEnum,
)

SYSTEM_KIVI_MAPPING = {
    "kunnan": FISewageDisposalSystemCodeEnum.MUNICIPAL,
    "antenni": FISewageDisposalSystemCodeEnum.NO_SEWER,
    "omistuksessa": FISewageDisposalSystemCodeEnum.OWNED,
    "absorptiokenttä": FISewageDisposalSystemCodeEnum.ABSORPTION_FIELD,
    "maan suodatus": FISewageDisposalSystemCodeEnum.LAND_FILTRATION,
    "pieni jätevedenkäsittelysuunnitelma": FISewageDisposalSystemCodeEnum.SMALL_SEWAGE_TREATMENT_PLANT,
    "septinen säiliö": FISewageDisposalSystemCodeEnum.SEPTIC_TANK,
    "suljettu säiliö": FISewageDisposalSystemCodeEnum.CLOSED_TANK,
    "vesiosuuskunta": FISewageDisposalSystemCodeEnum.WATER_COOPERATIVE,
    "ulkovarasto": "OUTDOOR",
}


ZONING_TYPE_CODE_MAPPING = {
    "Asemakaava": FIZoningTypeCodeEnum.CITY_PLAN,
    "Yleiskaava": FIZoningTypeCodeEnum.MASTER_PLAN,
    "Kaupunkisuunnitelma": FIZoningTypeCodeEnum.CITY_PLAN,
    "Maaseutualue": FIZoningTypeCodeEnum.RURAL_AREA,
    "Käyttöönottotarkastus": FIZoningTypeCodeEnum.COMMISSIONING_INSPECTION,
    "Lopputarkastus": FIZoningTypeCodeEnum.FINAL_INSPECTION,
    "Komponenttien yleissuunnitelma": FIZoningTypeCodeEnum.COMPONENT_MASTER_PLAN,
    "Poikkeama päätös": FIZoningTypeCodeEnum.DEVIATION_DECISION,
    "Rakentaminen kielletty": FIZoningTypeCodeEnum.BUILDING_FORBID,
    "Rakennuslupa": FIZoningTypeCodeEnum.BUILDING_PERMIT,
    "Yksityiskohtainen rantasuunnitelma": FIZoningTypeCodeEnum.DETAILED_SHORE_PLAN,
    "Rantasuunnitelman alue": FIZoningTypeCodeEnum.SHORE_PLAN_AREA,
    "Suunnittelua vaativa alue": FIZoningTypeCodeEnum.AREA_REQUIRING_PLANNING,
    "Toimintakielto": FIZoningTypeCodeEnum.ACTION_BAN,
}


AVAILABILITY_KIVI_MAPPING = {
    "sopimuksen mukaan": FIRealtyAvailabilityCodeEnum.NEGOTIABLE,
    "heti": FIRealtyAvailabilityCodeEnum.AVAILABLE,
    "muu ehto": FIRealtyAvailabilityCodeEnum.OTHER,
    "neuvoteltavissa": FIRealtyAvailabilityCodeEnum.NEGOTIABLE,
    "päivämäärä": FIRealtyAvailabilityCodeEnum.DATE,
    "vuokrattu": FIRealtyAvailabilityCodeEnum.RENTED,
}

COST_FIELDS = [
    ("CHARGES_REALTYTAX", FICostTypeCodeEnum.PROPERTY_TAX),
    ("CHARGES_SAUNA", FICostTypeCodeEnum.SAUNA),
    ("CHARGES_HEATING", FICostTypeCodeEnum.OTHER_HEATING_COSTS),
    ("CHARGES_EHEATING", FICostTypeCodeEnum.ELECTRIC_HEATING_COSTS),
    ("CHARGES_WATER", FICostTypeCodeEnum.WATER),
    ("CHARGES_SEWAGE", FICostTypeCodeEnum.WATER_AND_WASTE),
    ("CHARGES_PARKINGSPACE", FICostTypeCodeEnum.PARKING_SPACE_FEE),
    ("CHARGES_TV", FICostTypeCodeEnum.TV),
]

CONDITION_KIVI_MAPPING = {
    "hyvä": FIConditionCodeEnum.GOOD,
    "tyydyttävä": FIConditionCodeEnum.SATISFACTORY,
    "välttävä": FIConditionCodeEnum.TOLERABLE,
    "uusi": FIConditionCodeEnum.NEW,
    "luokittelematon": FIConditionCodeEnum.UNCLASSIFIED,
}


ENERGY_TYPE_MAPPING = {
    "F2013": "F_2013",
    "G2013": "G_2013",
    "A2018": "A_2018",
    "B2018": "B_2018",
    "C2018": "C_2018",
    "D2018": "D_2018",
    "E2018": "E_2018",
    "F2018": "F_2018",
    "G2018": "G_2018",
    "A2007": "A_2007",
    "B2007": "B_2007",
    "C2007": "C_2007",
    "D2007": "D_2007",
    "E2007": "E_2007",
    "F2007": "F_2007",
    "G2007": "G_2007",
    "NOT_AVAILABLE": "NOT_AVAILABLE",
    "NOT_REQUIRED": "NOT_REQUIRED",
}

CONSTRUCTION_MATERIAL_MAPPING = {
    "tiili": "BRICK",
    "betoni": "CONCRETE",
    "elementti": "ELEMENT",
    "teräs": "STEEL",
    "kivi": "STONE",
    "puutavara": "TIMBER",
    "puu": "WOOD",
    "muu": "OTHER",
}

OUTER_ROOF_MAPPING_TYPE_CODE = {
    "Tasakatto": FIOuterRoofTypeCodeEnum.FLAT,
    "Harjakatto": FIOuterRoofTypeCodeEnum.GABLED,
    "Lappikatto": FIOuterRoofTypeCodeEnum.HIPPED,
    "Katettukatto": FIOuterRoofTypeCodeEnum.PENT,
    "Pellinkatto": FIOuterRoofTypeCodeEnum.GAMBREL,
    "Taitekatto": FIOuterRoofTypeCodeEnum.MANSARD,
    "Saterikatto": FIOuterRoofTypeCodeEnum.SATERI,
    "Toinenkatto": FIOuterRoofTypeCodeEnum.OTHER,
}

HEATING_SYSTEM_MAPPING = {
    "Energiavaraaja": [FIHeatingSystemCodeEnum.OTHER],
    "poistoilmalämpöpumppu": [FIHeatingSystemCodeEnum.EXHAUST_AIR_HEAT_PUMP],
    "maalämpö": [FIHeatingSystemCodeEnum.GEOTHERMAL_HEATING],
    "kaukolämpö": [FIHeatingSystemCodeEnum.DISTRICT_HEATING],
    "sähköinen": [FIHeatingSystemCodeEnum.ELECTRIC],
    "kaasua": [FIHeatingSystemCodeEnum.GAS],
    "öljy": [FIHeatingSystemCodeEnum.OIL],
    "aurinko": [FIHeatingSystemCodeEnum.SUN],
    "vesilämpöpumppu": [FIHeatingSystemCodeEnum.WATER_HEAT_PUMP],
    "puu": [FIHeatingSystemCodeEnum.WOOD],
    # Add more mappings as needed
}

DISTRIBUTION_SYSTEM_MAPPING = {
    "Ilmalämpöpumppu, muu": ["OTHER", "AIR_HEAT_PUMP"],
    # Add more mappings as needed
}

BALCONY_MAPPING = {
    "ulostyönnetty": FIResidentialPropertyBalconyTypeCodeEnum.PROTRUDING,
    "ranskalainen": FIResidentialPropertyBalconyTypeCodeEnum.FRENCH_WINDOW,
    "lasitettu": FIResidentialPropertyBalconyTypeCodeEnum.GLAZED,
    "sisäänvedetty": FIResidentialPropertyBalconyTypeCodeEnum.RETRACTED,
    "muu": FIResidentialPropertyBalconyTypeCodeEnum.OTHER,
}

COMPASS_POINTS_MAPPING = {
    "pohjoinen": FICompassPointEnum.NORTH,
    "itä": FICompassPointEnum.EAST,
    "etelä": FICompassPointEnum.SOUTH,
    "länsi": FICompassPointEnum.WEST,
    "koillinen": FICompassPointEnum.NORTHEAST,
    "kaakko": FICompassPointEnum.SOUTHEAST,
    "lounas": FICompassPointEnum.SOUTHWEST,
    "luode": FICompassPointEnum.NORTHWEST,
}

OWNERSHIP_TYPE_MAPPING = {
    "osaomistus": FIOwnershipTypeEnum.SHARE,
    "delägarandel": FIOwnershipTypeEnum.SHARE,
    "oma": FIOwnershipTypeEnum.PROPERTY,
    "kiinteistö": FIOwnershipTypeEnum.PROPERTY,
    "omakotitalotontti": FIOwnershipTypeEnum.PROPERTY,
    "mökki tai huvila": FIOwnershipTypeEnum.PROPERTY,
    "omakotitalo": FIOwnershipTypeEnum.PROPERTY,
    "paritalo": FIOwnershipTypeEnum.PROPERTY,
    "rivitalo": FIOwnershipTypeEnum.SHARE,
    "erillistalo": FIOwnershipTypeEnum.SHARE,
    "osake": FIOwnershipTypeEnum.SHARE,
    "kerrostalo": FIOwnershipTypeEnum.SHARE,
}

PROPERTY_TYPE_GROUP_MAPPING = {
    # Plot/Land types
    "omakotitalotontti": FIPropertyTypeGroupEnum.PLOT,  # Single-family house plot
    "vapaa-ajan tontti": FIPropertyTypeGroupEnum.PLOT,  # Leisure plot
    "rivitalotontti": FIPropertyTypeGroupEnum.PLOT,  # Townhouse plot
    "kerrostalotontti": FIPropertyTypeGroupEnum.PLOT,  # Apartment building plot
    "muu tontti": FIPropertyTypeGroupEnum.PLOT,  # Other plot
    "tontti": FIPropertyTypeGroupEnum.PLOT,  # Plot/Land
    # Residential types
    "omakotitalo": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Single-family house
    "paritalo": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Semi-detached house
    "rivitalo": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Townhouse
    "erillistalo": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Detached house
    "kerrostalo": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Apartment building
    "puutalo-osake": FIPropertyTypeGroupEnum.RESIDENTIAL,  # Wooden house share
    # Leisure types
    "vapaa-ajan asunto": FIPropertyTypeGroupEnum.LEISURE,  # Holiday home
    "mökki": FIPropertyTypeGroupEnum.LEISURE,  # Cottage
    # Estate types
    "maatila": FIPropertyTypeGroupEnum.ESTATE,  # Farm
    "metsätila": FIPropertyTypeGroupEnum.ESTATE,  # Forest property
    # Default to OTHER if type not found
    "muu": FIPropertyTypeGroupEnum.OTHER,  # Other
}

ROOM_COUNT_MAPPING = {
    "1 h": "1H",
    "2 h": "2H",
    "3 h": "3H",
    "4 h": "4H",
    "5 h": "5H",
    "kolmio": "3H",
}

WATERCHARGE_TYPE_MAPPING = {
    "oma mittari": FIWaterChargeTypeCodeEnum.BY_USE,
    "perusmaksu": FIWaterChargeTypeCodeEnum.BASIC_FEE,
    "käyttämällä etukäteen": FIWaterChargeTypeCodeEnum.BY_USE_WITH_ADVANCE,
    "sisältyy vastikkeeseen": FIWaterChargeTypeCodeEnum.INCLUDED_IN_MAINTENACE_CHARGE,
    "sisältyy vuokraan": FIWaterChargeTypeCodeEnum.INCLUDED_IN_RENT,
    "henkilöluvun mukaan": FIWaterChargeTypeCodeEnum.PER_PERSON,
}

VENTILATIONSYSYTEM_MAPPING = {
    "koneellinen tulo": FIVentilationSystemCodeEnum.FORCED_EXHAUST
}

AREA_BASIS_MAPPING = {
    "yhtiöjärjestyksen mukainen": FIAreaBasisCodeEnum.ARTICLES_OF_ASSOCIATION,
    "isännöitsijäntodistuksen": FIAreaBasisCodeEnum.MANAGERS_CERTIFICATE,
    "tarkistusmitattu": FIAreaBasisCodeEnum.VERIFYING_MEASUREMENT,
    "toimeksiantajan ilmoittama": FIAreaBasisCodeEnum.CLIENT_REPORTED,
}

FIREPLACE_MAPPING = {
    "takka": FIHearthTypeCodeEnum.FIREPLACE,
    "takkavaraus": FIHearthTypeCodeEnum.PLACE_ALLOCATED_FOR_FIREPLACE,
    "hormi olemassa": FIHearthTypeCodeEnum.FLUE_IN_PLACE,
    "muu": FIHearthTypeCodeEnum.OTHER,
}

LIVING_FORM_TYPE_CODE_MAPPING = {
    "asunto-osakeyhtiö": FILivingFormTypeCodeEnum.SENIOR_HOUSE,
    "ei-tuettua": FILivingFormTypeCodeEnum.NONSUBSIDISED,
    "korkotuet": FILivingFormTypeCodeEnum.INTEREST_SUBSIDIED,
    "jaettu asunto": FILivingFormTypeCodeEnum.SHARED_APARTMENT,
    "alivuokraus": FILivingFormTypeCodeEnum.SUBTENANCY,
    "palvelutalo": FILivingFormTypeCodeEnum.SERVICE_HOUSE,
    "työttömyyskorvausasunto": FILivingFormTypeCodeEnum.EMPLOYMENT_BENEFIT_APARTMENT,
    "opiskelija-asunto": FILivingFormTypeCodeEnum.STUDENT_APARTMENT,
}

ADMINSTRATION_TYPE_CODE_MAPPING = {
    "asunto-osakeyhtiö": FIAdministrationTypeCodeEnum.APARTMENT_HOUSING_COMPANY,
    "kiinteistöosakeyhtiö": FIAdministrationTypeCodeEnum.REAL_ESTATE_COMPANY,
    "osaomistus": FIAdministrationTypeCodeEnum.PART_OWNERSHIP,
    "muu": FIAdministrationTypeCodeEnum.OTHER,
}

HOLDING_TYPE_CODE_MAPPING = {
    "oma": FIHoldingTypeCodeEnum.OWN,
    "vuokraus": FIHoldingTypeCodeEnum.LEASEHOLD,
}

MAINTENANCE_TYPE_CODE_MAPPING = {
    "huoltoyhtiö": FIHousingCompanyMaintenanceTypeCodeEnum.PROPERTY_MAINTENANCE_COMPANY,
}

CHARGE_PERIOD_MAPPING = {
    "e / kk": FIChargePeriodCodeEnum.MONTH,
    "e / hlö / kk": FIChargePeriodCodeEnum.MONTH_PER_PERSON,
    "e / pers. / mån.": FIChargePeriodCodeEnum.YEAR,
}

CHARGE_TYPE_MAPPING = {
    "CHARGES_SAUNA": "SAUNACHARGE_TYPE",
    "CHARGES_PARKINGSPACE": "PARKINGCHARGE_TYPE",
}

REALTYOPTIONS_TO_STORAGE_TYPE = {
    "ullakko": FIPropertyStorageTypeCodeEnum.ATTIC,
    "kellari": FIPropertyStorageTypeCodeEnum.CELLAR,
    "ulkovarasto": FIPropertyStorageTypeCodeEnum.OUTDOOR,
    "jäähdytetty_kellari": FIPropertyStorageTypeCodeEnum.REFRIGERATED_CELLAR,
    "muu": FIPropertyStorageTypeCodeEnum.OTHER,
}

DEVELOPMENT_PHASE_MAPPING = {
    "valmis": FIDevelopmentPhaseCodeEnum.MOVE_IN_READY,  # Ready
    "rakenteilla": FIDevelopmentPhaseCodeEnum.IN_CONSTRUCTION,  # Under construction
    "ennakkomarkkinoinnissa": FIDevelopmentPhaseCodeEnum.PRE_MARKETING,  # Pre-marketing
}

ROOF_MATERIAL_MAPPING = {
    "bitumi": FIOuterRoofMaterialCodeEnum.BITUMEN_FELT,
    "tiili": FIOuterRoofMaterialCodeEnum.BRICK,
    "pelti": FIOuterRoofMaterialCodeEnum.SHEET_METAL,
    "huopa": FIOuterRoofMaterialCodeEnum.FELT,
    "betoni": FIOuterRoofMaterialCodeEnum.REINFORCED_CONCRETE,
    "muovi": FIOuterRoofMaterialCodeEnum.PVC,
    "kivipelti": FIOuterRoofMaterialCodeEnum.STONE_COATED_METAL,
    "kupari": FIOuterRoofMaterialCodeEnum.COPPER,
    "viherkatto": FIOuterRoofMaterialCodeEnum.GREEN_ROOF,
}

PREMISE_TYPE_MAPPING = {
    "asunnon_oma_varasto": FIHousingCompanyPremiseTypeCodeEnum.APARTMENT_SPECIFIC_STORAGE,
    "vinttikomero": FIHousingCompanyPremiseTypeCodeEnum.ATTIC_STORAGE,
    "parveke": FIHousingCompanyPremiseTypeCodeEnum.BALCONY,
    "kellarikomero": FIHousingCompanyPremiseTypeCodeEnum.CELLAR_STORAGE,
    "askarteluhuone": FIHousingCompanyPremiseTypeCodeEnum.CRAFT_ROOM,
    "kuivaushuone": FIHousingCompanyPremiseTypeCodeEnum.DRYING_ROOM,
    "väestönsuoja": FIHousingCompanyPremiseTypeCodeEnum.EMERGENCY_SHELTER,
    "talopesula": FIHousingCompanyPremiseTypeCodeEnum.LAUNDRY_ROOM,
    "mankeli": FIHousingCompanyPremiseTypeCodeEnum.MANGLE_ROOM,
    "irtaimistovarasto": FIHousingCompanyPremiseTypeCodeEnum.MOVABLE_PROPERTY_STORAGE,
    "jäähdytetty kellari": FIHousingCompanyPremiseTypeCodeEnum.REFRIGERATED_CELLAR,
    "urheiluvälinevarasto": FIHousingCompanyPremiseTypeCodeEnum.SPORTS_EQUIPMENT_STORAGE,
    "uima-allas": FIHousingCompanyPremiseTypeCodeEnum.SWIMMING_POOL,
    "muu": FIHousingCompanyPremiseTypeCodeEnum.OTHER,
}

WATERFRONT_MAPPING = {
    "ranta": FIBeachCodeEnum.NEXT_TO_RELICTION_AREA,
    "oma ranta": FIBeachCodeEnum.OWN_BEACH,
    "omaa rantaviivaa": FIBeachCodeEnum.OWN_BEACH,
    "oikeus vesialueisiin": FIBeachCodeEnum.RIGHT_TO_WATER_AREA,
    "rantaoikeus": FIBeachCodeEnum.BEACH_RIGHT,
    "yhteisranta": FIBeachCodeEnum.SHARED_BEACH,
    "ei rantaa": FIBeachCodeEnum.NO_BEACH,
}

NATIONALITY_MAPPINGS = {
    "AF": ["AF", "AFGHANISTAN", "AFGHAN"],
    "AX": ["AX", "ALAND ISLANDS", "ALANDER"],
    "AL": ["AL", "ALBANIA", "ALBANIAN"],
    "DZ": ["DZ", "ALGERIA", "ALGERIAN"],
    "AS": ["AS", "AMERICAN SAMOA", "AMERICAN SAMOAN"],
    "AD": ["AD", "ANDORRA", "ANDORRAN"],
    "AO": ["AO", "ANGOLA", "ANGOLAN"],
    "AI": ["AI", "ANGUILLA", "ANGUILLAN"],
    "AQ": ["AQ", "ANTARCTICA", "ANTARCTIC"],
    "AG": ["AG", "ANTIGUA AND BARBUDA", "ANTIGUAN", "BARBUDAN"],
    "AR": ["AR", "ARGENTINA", "ARGENTINIAN"],
    "AM": ["AM", "ARMENIA", "ARMENIAN"],
    "AW": ["AW", "ARUBA", "ARUBAN"],
    "AU": ["AU", "AUSTRALIA", "AUSTRALIAN"],
    "AT": ["AT", "AUSTRIA", "AUSTRIAN"],
    "AZ": ["AZ", "AZERBAIJAN", "AZERBAIJANI"],
    "BS": ["BS", "BAHAMAS", "BAHAMIAN"],
    "BH": ["BH", "BAHRAIN", "BAHRAINI"],
    "BD": ["BD", "BANGLADESH", "BANGLADESHI"],
    "BB": ["BB", "BARBADOS", "BARBADIAN"],
    "BY": ["BY", "BELARUS", "BELARUSIAN"],
    "BE": ["BE", "BELGIUM", "BELGIAN"],
    "BZ": ["BZ", "BELIZE", "BELIZEAN"],
    "BJ": ["BJ", "BENIN", "BENINESE"],
    "BM": ["BM", "BERMUDA", "BERMUDIAN"],
    "BT": ["BT", "BHUTAN", "BHUTANESE"],
    "BO": ["BO", "BOLIVIA", "BOLIVIAN"],
    "BQ": ["BQ", "BONAIRE, SINT EUSTATIUS, AND SABA", "BONAIREAN"],
    "BA": ["BA", "BOSNIA AND HERZEGOVINA", "BOSNIAN", "HERZEGOVINIAN"],
    "BW": ["BW", "BOTSWANA", "BOTSWANAN"],
    "BV": ["BV", "BOUVET ISLAND", "BOUVET ISLANDER"],
    "BR": ["BR", "BRAZIL", "BRAZILIAN"],
    "IO": ["IO", "BRITISH INDIAN OCEAN TERRITORY", "BIOT"],
    "BN": ["BN", "BRUNEI", "BRUNEIAN"],
    "BG": ["BG", "BULGARIA", "BULGARIAN"],
    "BF": ["BF", "BURKINA FASO", "BURKINABE"],
    "BI": ["BI", "BURUNDI", "BURUNDIAN"],
    "CV": ["CV", "CAPE VERDE", "CAPE VERDEAN"],
    "KH": ["KH", "CAMBODIA", "CAMBODIAN"],
    "CM": ["CM", "CAMEROON", "CAMEROONIAN"],
    "CA": ["CA", "CANADA", "CANADIAN"],
    "KY": ["KY", "CAYMAN ISLANDS", "CAYMANIAN"],
    "CF": ["CF", "CENTRAL AFRICAN REPUBLIC", "CENTRAL AFRICAN"],
    "TD": ["TD", "CHAD", "CHADIAN"],
    "CL": ["CL", "CHILE", "CHILEAN"],
    "CN": ["CN", "CHINA", "CHINESE"],
    "CX": ["CX", "CHRISTMAS ISLAND", "CHRISTMAS ISLANDER"],
    "CC": ["CC", "COCOS (KEELING) ISLANDS", "COCOS ISLANDER"],
    "CO": ["CO", "COLOMBIA", "COLOMBIAN"],
    "KM": ["KM", "COMOROS", "COMORAN"],
    "CG": ["CG", "CONGO", "CONGOLESE"],
    "CD": ["CD", "DEMOCRATIC REPUBLIC OF THE CONGO", "CONGOLESE"],
    "CK": ["CK", "COOK ISLANDS", "COOK ISLANDER"],
    "CR": ["CR", "COSTA RICA", "COSTA RICAN"],
    "HR": ["HR", "CROATIA", "CROATIAN"],
    "CU": ["CU", "CUBA", "CUBAN"],
    "CW": ["CW", "CURACAO", "CURACAOAN"],
    "CY": ["CY", "CYPRUS", "CYPRIOT"],
    "CZ": ["CZ", "CZECH REPUBLIC", "CZECH"],
    "DK": ["DK", "DENMARK", "DANISH"],
    "DJ": ["DJ", "DJIBOUTI", "DJIBOUTIAN"],
    "DM": ["DM", "DOMINICA", "DOMINICAN"],
    "DO": ["DO", "DOMINICAN REPUBLIC", "DOMINICAN"],
    "EC": ["EC", "ECUADOR", "ECUADORIAN"],
    "EG": ["EG", "EGYPT", "EGYPTIAN"],
    "SV": ["SV", "EL SALVADOR", "SALVADORAN"],
    "GQ": ["GQ", "EQUATORIAL GUINEA", "EQUATORIAL GUINEAN"],
    "ER": ["ER", "ERITREA", "ERITREAN"],
    "EE": ["EE", "ESTONIA", "ESTONIAN"],
    "SZ": ["SZ", "ESWATINI", "SWAZI"],
    "ET": ["ET", "ETHIOPIA", "ETHIOPIAN"],
    "FJ": ["FJ", "FIJI", "FIJIAN"],
    "FI": ["FI", "FINLAND", "FINNISH"],
    "FR": ["FR", "FRANCE", "FRENCH"],
    "DE": ["DE", "GERMANY", "GERMAN"],
    "GH": ["GH", "GHANA", "GHANAIAN"],
    "GR": ["GR", "GREECE", "GREEK"],
    "HK": ["HK", "HONG KONG", "HONG KONGER"],
    "HU": ["HU", "HUNGARY", "HUNGARIAN"],
    "IS": ["IS", "ICELAND", "ICELANDIC"],
    "IN": ["IN", "INDIA", "INDIAN"],
    "ID": ["ID", "INDONESIA", "INDONESIAN"],
    "IE": ["IE", "IRELAND", "IRISH"],
    "IL": ["IL", "ISRAEL", "ISRAELI"],
    "IT": ["IT", "ITALY", "ITALIAN"],
    "JP": ["JP", "JAPAN", "JAPANESE"],
    "JO": ["JO", "JORDAN", "JORDANIAN"],
    "KZ": ["KZ", "KAZAKHSTAN", "KAZAKH"],
    "KR": ["KR", "KOREA", "SOUTH KOREA", "KOREAN"],
    "KW": ["KW", "KUWAIT", "KUWAITI"],
    "MY": ["MY", "MALAYSIA", "MALAYSIAN"],
    "MX": ["MX", "MEXICO", "MEXICAN"],
    "NP": ["NP", "NEPAL", "NEPALESE"],
    "NL": ["NL", "NETHERLANDS", "DUTCH"],
    "NZ": ["NZ", "NEW ZEALAND", "NEW ZEALANDER"],
    "PH": ["PH", "PHILIPPINES", "FILIPINO"],
    "PL": ["PL", "POLAND", "POLISH"],
    "PT": ["PT", "PORTUGAL", "PORTUGUESE"],
    "RU": ["RU", "RUSSIA", "RUSSIAN"],
    "US": ["US", "USA", "UNITED STATES", "AMERICAN"],
    "VN": ["VN", "VIETNAM", "VIET NAM"],
    "ZA": ["ZA", "SOUTH AFRICA", "SOUTH AFRICAN"],
}

TELEVISION_TYPE_MAPPING = {
    "kaapeli-tv": FITelevisionTypeCodeEnum.CABLE,
    "antenni-tv": FITelevisionTypeCodeEnum.ANTENNA,
    "iptv": FITelevisionTypeCodeEnum.IPTV,
    "satelliitti": FITelevisionTypeCodeEnum.SATELLITE,
}

FINNISH_FEATURE_MAPPING = {
    "wc-istuin": FeatureCodeEnum.WC,
    "suihku": FeatureCodeEnum.SHOWER,
    "kylpyamme": FeatureCodeEnum.BATHTUB,
    "poreamme": FeatureCodeEnum.JACUZZI,
    "suihkuseinä": FeatureCodeEnum.SHOWER_WALL,
    "suihkukaappi": FeatureCodeEnum.WALK_IN_SHOWER,
    "pesukone": FeatureCodeEnum.WASHING_MACHINE,
    "kuivausrumpu": FeatureCodeEnum.TUMBLE_DRYER,
    "pesukonelittäntä": FeatureCodeEnum.WASHING_MACHINE_CONNECTION,
    "kuivauskaappi": FeatureCodeEnum.DRYING_CABINET,
    "lattialämmitys": FeatureCodeEnum.UNDERFLOOR_HEATING,
    "peilikaappi": FeatureCodeEnum.MIRROR_CABINET,
    "peili": FeatureCodeEnum.MIRROR,
    "kiinteät valaisimet": FeatureCodeEnum.FIXED_LAMPS,
    "kylpyhuonekaapisto": FeatureCodeEnum.BATHROOM_CABINETS,
    "asennettuja erikoisvarusteita": FeatureCodeEnum.ELECTRIC_HEATER,
    "puukiuas": FeatureCodeEnum.WOOD_HEATED_SAUNA_STOVE,
    "heti valmis -sähkökiuas": FeatureCodeEnum.READY_FOR_ELECTRIC_HEATER,
    "kaasuliesi": FeatureCodeEnum.GAS_STOVE,
    "keraaminen liesi": FeatureCodeEnum.CERAMIC_STOVE,
    "sähköliesi": FeatureCodeEnum.ELECTRIC_STOVE,
    "integroitu liesi": FeatureCodeEnum.INTEGRATED_STOVE,
    "leivinuuni": FeatureCodeEnum.BAKING_OVEN,
    "keittolevy": FeatureCodeEnum.HOB,
    "induktioliesi": FeatureCodeEnum.INDUCTION_STOVE,
    "erillisuuni": FeatureCodeEnum.SEPARATE_OVEN,
    "puuliesi": FeatureCodeEnum.WOOD_BURNING_STOVE,
    "liesitaso": FeatureCodeEnum.COOKTOP,
    "muu liesi/uuni": FeatureCodeEnum.MICROWAVE_OVEN,
    "liesikupu": FeatureCodeEnum.EXTRACTOR_HOOD,
    "liesituuletin / hormi": FeatureCodeEnum.EXTRACTOR_HOOD_WITH_FLUE,
    "liesituuletin / suodatin": FeatureCodeEnum.COOKER_HOOD,
    "liesituuletin": FeatureCodeEnum.EXTRACTOR_HOOD,
    "mikroaaltouuni": FeatureCodeEnum.MICROWAVE_OVEN,
    "astianpesukone": FeatureCodeEnum.DISHWASHER,
    "varattu paikka astianpesukoneelle": FeatureCodeEnum.RESERVED_LOCATION_FOR_DISHWASHER,
    "lattiakaivo": FeatureCodeEnum.FLOOR_DRAIN,
    "irralliset kaapistot": FeatureCodeEnum.FREE_STANDING_CABINETS,
    "irralliset saarekkeet": FeatureCodeEnum.FREE_STANDING_ISLANDS,
    "jääkaappi/pakastin": FeatureCodeEnum.REFRIGERATOR_FREEZER,
    "jää/viileäkaappi": FeatureCodeEnum.REFRIGERATOR_CHILLER,
    "jääkaappi": FeatureCodeEnum.REFRIGERATOR,
    "pakastin": FeatureCodeEnum.FREEZER,
    "kylmäkaappi": FeatureCodeEnum.REFRIGERATED_CABINET,
    "kylmiö": FeatureCodeEnum.COLD_ROOM,
    "jenkkikaappi": FeatureCodeEnum.REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT,
    "pakastekaappi": FeatureCodeEnum.FREEZER,
    "viinikaappi": FeatureCodeEnum.WINE_CABINET,
    "muu kylmälaite": FeatureCodeEnum.REFRIGERATED_CABINET,
    "bidee-suihku": FeatureCodeEnum.BIDET,
    "pesuallas": FeatureCodeEnum.SINK,
    "lastenhoitopöytä/taso": FeatureCodeEnum.BABY_CHANGING_TABLE,
    "silityspöytä/taso": FeatureCodeEnum.IRONING_TABLE_BOARD,
    "pyykkikaapit": FeatureCodeEnum.LAUNDRY_CABINETS,
    "kurapiste": FeatureCodeEnum.SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR,
    "pöytätaso": FeatureCodeEnum.TABLE_TOP,
    "keskuspölynimuri": FeatureCodeEnum.CENTRAL_VACUUM_UNIT,
    "irralliset kaapit": FeatureCodeEnum.FREE_STANDING_CABINETS,
    "kiinteät valaisimet": FeatureCodeEnum.FIXED_LAMPS,
    "sälekaihtimet/rullaverhot": FeatureCodeEnum.FIXED_LAMPS,
    "verhotangot": FeatureCodeEnum.FIXED_LAMPS,
    "naulakko": FeatureCodeEnum.FIXED_LAMPS,
    "keskuspölynimuri": FeatureCodeEnum.CENTRAL_VACUUM_UNIT,
    "keskuspölynimuriin varaus": FeatureCodeEnum.CENTRAL_VACUUM_UNIT,
    "satelliittiantenni": FeatureCodeEnum.FIXED_LAMPS,
    "lämminvesivaraaja": FeatureCodeEnum.FIXED_LAMPS,
    "ilmalämpöpumppu": FeatureCodeEnum.FIXED_LAMPS,
    "huoneistokohtainen ilmanvaihtokone": FeatureCodeEnum.FIXED_LAMPS,
    "huoneistokohtainen jäähdytyskone": FeatureCodeEnum.FIXED_LAMPS,
    "hälytys- /valvontajärjestelmä": FeatureCodeEnum.FIXED_LAMPS,
    "markiisi": FeatureCodeEnum.FIXED_LAMPS,
    "pyykinpesukone": FeatureCodeEnum.WASHING_MACHINE,
    "kuivauskaappi/-rumpu": FeatureCodeEnum.DRYING_CABINET,
}
