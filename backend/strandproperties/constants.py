from collections import defaultdict
from decimal import Decimal
from enum import Enum, StrEnum, auto
from typing import Dict, Set

from pydantic import Field


class CountryCode(str, Enum):
    SPAIN = "ES"
    FINLAND = "FI"


class RoleType(StrEnum):
    ADMIN = "Admin"  # NOTE: this role is read-only, it's used for superadmin
    # OWNER = "Owner"
    # OFFICE_MANAGER = "Office Manager"
    REALTOR = "Realtor"
    PHOTOGRAPHER = "Photographer"


DEFAULT_ROLE = RoleType.REALTOR
ROLE_TAG_KEYWORDS = ["support", "manager"]

PALMA_OFFICE_NAME = "mallorca, spain"

PROPERTY_LIST_TYPE_PARAMS = [
    "bedrooms",
    "bathrooms",
    "settings",
    "orientations",
    "views",
    "features",
    "areas",
    "listing_types",
    "types",
    "garage_types",
    "garden_types",
    "pool_types",
    "assigned_to",
    "status",
    "portals",
]


class CompanyType(StrEnum):
    PRIVATE_TRADER = auto()
    LIMITED_COMPANY = auto()


class ListingTypeEnum(str, Enum):
    SALE = "Sale"
    RENT_LONG = "Rent Long Term"
    RENT_SHORT = "Rent Short Term"


class GarageTypeEnum(str, Enum):
    PRIVATE = "Private"
    COMMUNAL = "Communal"
    CARPORT = "Carport"
    OTHER = "Other"


class PoolTypeEnum(str, Enum):
    PRIVATE = "Private"
    COMMUNAL = "Communal"
    JACUZZI = "Jacuzzi"
    DROP_DESIGN_SPA = "Drop Design Spa"


class GardenTypeEnum(str, Enum):
    PRIVATE = "Private"
    COMMUNAL = "Communal"


class Currency(str, Enum):
    EUR = "EUR"  # Following ISO 4217
    USD = "USD"


currency_to_symbol_mapper = {
    Currency.EUR.value: "€",
    Currency.USD.value: "$",
}


DEFAULT_CURRENCY = Currency.EUR


class DataSource(StrEnum):
    STRAND = "strand"
    RESALES_ONLINE = "resales"
    INMOBALIA = "inmoba"


class Status(StrEnum):
    DRAFT = "Draft"
    PUBLISHED = "Published"
    CANCELLED = "Cancelled"
    OFFERING = "Offering"
    SOLD = "Sold"
    RENTED = "Rented"
    ARCHIVED = "Archived"


class ActivityObjectType(StrEnum):
    PROPERTY = "PROPERTY"
    SALES_ACTIVITY = "SALES_ACTIVITY"
    CONTACT = "CONTACT"


class EventLogObjectType(StrEnum):
    PROPERTY = "PROPERTY"
    SALES_ACTIVITY = "SALES_ACTIVITY"
    CONTACT = "CONTACT"
    GROUP = "GROUP"
    DOCUMENT = "DOCUMENT"
    USER = "USER"
    SALES_AGREEMENT = "SALES_AGREEMENT"
    EVENT = "EVENT"
    OFFER = "OFFER"
    RESERVATION = "RESERVATION"
    DETAILS_OF_SALE = "DETAILS_OF_SALE"
    EMAIL = "EMAIL"
    DOCUMENT_LIBRARY_ITEM = "DOCUMENT_LIBRARY_ITEM"
    ADVERTISEMENT = "ADVERTISEMENT"
    CONTACT_MARKETING_CONSENT = "CONTACT_MARKETING_CONSENT"
    BROKERAGE_OFFER = "BROKERAGE_OFFER"
    BROCHURE = "BROCHURE"
    DOCUMENT_SIGNING = "DOCUMENT_SIGNING"
    FI_SALES_AGREEMENT = "FI_SALES_AGREEMENT"
    FI_DETAILS_OF_SALE = "FI_DETAILS_OF_SALE"
    FI_PURCHASE_OFFER = "FI_PURCHASE_OFFER"
    FI_COUNTER_OFFER = "FI_COUNTER_OFFER"


class EventLogActorType(StrEnum):
    USER = "USER"  # Internal system user
    SYSTEM = "SYSTEM"  # Automated system actions
    EXTERNAL_USER = "EXTERNAL_USER"  # Actions initiated by users outside the system (e.g. via email links)


class EventLogAction(StrEnum):
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    DELETED = "DELETED"
    PRICE_CHANGED = "PRICE_CHANGED"  # Action for Property price change
    STATUS_CHANGED = "STATUS_CHANGED"  # Action for Property status change
    SYNCED = "SYNCED"  # Action for Property synced
    VIEWED = "VIEWED"
    ASSIGNED = "ASSIGNED"  # Action for Contact assigned to Realtor
    ATTACH = "ATTACH"  # Action for Contact attached to Sales Activity
    DISCARDED = "DISCARDED"
    MEMBER_ADDED = "MEMBER_ADDED"  # Action for Group member added
    SENT = "SENT"
    SENT_FOR_REVIEW = "SENT_FOR_REVIEW"
    DEACTIVATED = "DEACTIVATED"
    ACTIVATED = "ACTIVATED"
    UPDATED_DOCUMENT = "UPDATED_DOCUMENT"  # Action for Document updated
    REQUEST_CHANGE = "REQUEST_CHANGE"
    CREATE_AGENT = "CREATE_AGENT"
    UPLOAD_ATTACHMENTS = "UPLOAD_ATTACHMENTS"
    REMOVE_ATTACHMENTS = "REMOVE_ATTACHMENTS"
    CREATE_SALES_AGREEMENT = "CREATE_SALES_AGREEMENT"
    UPLOADED_SIGNED_SALE_AGREEMENT = "UPLOADED_SIGNED_SALE_AGREEMENT"
    UPLOAD_SIGNED_COPY = "UPLOAD_SIGNED_COPY"
    CREATE_OFFER = "CREATE_OFFER"
    REJECT_OFFER = "REJECT_OFFER"
    MANUAL_LOG = "MANUAL_LOG"
    ADDED_SIGNER = "ADDED_SIGNER"
    DELETED_SIGNER = "DELETED_SIGNER"
    TRANSFER_TAX_RETURN_SENT = "TRANSFER_TAX_RETURN_SENT"
    SENT_FOR_SIGNING = "SENT_FOR_SIGNING"


class SubStatus(str, Enum):
    "Check figma"


DEFAULT_STATUS = Status.DRAFT
STRAND_IBAN_NUMBER = "IBAN ES69 0081 2090 4400 0126 9933"
STRAND_DEPOSIT_PAYEE = "Strand Properties SL"

# Finnish social security number (Henkilötunnus) validation regex pattern.
# Matches formats like "131052-308T" or "310823A123A".
# Components:
#  \d{6}   -> birth date (DDMMYY)
#  [A+\-]  -> century sign ("-"=1800s, "+"=1900s, "A"=2000s)
#  \d{3}   -> individual number
#  [0-9A-Z] -> control character
# 2023 update: the century sign can now also be letters (Y, X, W, V, U, T, …).
# Allow any uppercase letter in that position plus the historical "-" and "+".
FINNISH_SSN_REGEX = r"^\d{6}[A-Z\+\-]\d{3}[0-9A-Z]$"


class StatusResetCode(str, Enum):
    """Reset code status"""

    new = "new"
    used = "used"
    expired = "expired"


class Language(StrEnum):
    ENGLISH = "en"
    SPANISH = "es"
    FINNISH = "fi"
    GERMAN = "de"
    SWEDISH = "sv"


language_isocode_mapper = {
    Language.ENGLISH.value: "english",
    Language.SPANISH.value: "spanish",
    Language.FINNISH.value: "finnish",
    Language.GERMAN.value: "german",
    Language.SWEDISH.value: "swedish",
}


class ConditionType(str, Enum):
    EXCELLENT = "Excellent"
    GOOD = "Good"
    FAIR = "Fair"
    RENOVATION_REQUIRED = "Renovation required"
    RESTORATION_REQUIRED = "Restoration required"


class CommercialType(str, Enum):
    PREMISE = "Premise"
    OTHER = "Other"


class PlotType(str, Enum):
    HOUSE = "House"
    OTHER = "Other"


class DescriptionType(str, Enum):
    EXTRA = "Extra"
    FULL = "Full"


class PropertyTypeOrigin(str, Enum):
    RESALES = "Resales"
    NEW_DEVELOPMENT = "New development"


class Country(str, Enum):
    SPAIN = "Spain"
    FINLAND = "Finland"


class SowiseDocumentUploadType(StrEnum):
    # Via https://api.sowise.fi/#/paths/documents-upload-type---parent/post
    FOLDER = auto()
    CONTRACT = auto()
    CONTRACT_PDF = "contract-pdf"
    TEMPLATE = auto()
    FILE = auto()


class AttachmentDocumentType(StrEnum):
    NOTE_SIMPLE_PDF = auto()
    PASSPORT_PDF = auto()
    NIE_DNI = auto()
    RESERVATION_AGREEMENT_SIGNED_PDF = auto()
    PROOF_OF_TRANSFER_DEPOSIT = auto()
    SALE_AGREEMENT_SIGNED = auto()
    IBI_RECEIPT = auto()
    BASURA_RECEIPT = auto()
    COPY_OF_THE_TITLE_DEED = auto()
    AGENT_INVOICES = auto()
    SELLER_INVOICES = auto()
    UNKNOW = auto()
    EXTRA_DOS = auto()


class DocumentType(str, Enum):
    SALES_AGREEMENT = "Sales Agreement"
    OFFER = "Offer"
    GENERIC = "Generic"
    DETAILS_OF_SALE = "Details Of Sale"
    DOS_INVOICE = "DOS Invoice"
    DOS_PROFORMA = "DOS Proforma"


class TemplateType(str, Enum):
    SALES_AGREEMENT_EXCLUSIVE = "Sales Agreement Exclusive"
    SALES_AGREEMENT_NOT_EXCLUSIVE = "Sales Agreement Not Exclusive"
    SALES_AGREEMENT_EXCLUSIVE_PALMA_OFFICE = "Sales Agreement Exclusive Palma"
    SALES_AGREEMENT_NOT_EXCLUSIVE_PALMA_OFFICE = "Sales Agreement Not Exclusive Palma"
    OFFER = "Offer"
    DETAILS_OF_SALE = "Details Of Sale"
    DOS_PROFORMA = "DOS Proforma"
    DOS_INVOICE = "DOS Invoice"


class VisibilityType(str, Enum):
    PUBLIC = "Public"
    PRIVATE = "Private"


class SowiseStatus(str, Enum):
    DRAFT = "Draft"
    PENDING = "Pending"
    SIGNED = "Signed"
    ARCHIVED = "Archived"
    GENERIC = "Generic"
    APPROVED = "Approved"
    REQUESTING_CHANGES = "RequestingChanges"
    NOTARIZED = "Notarized"
    IN_REVIEW = "InReview"


class OrderBy(str, Enum):
    ALPHABETICAL_ASC = "alphabetical_asc"
    ALPHABETICAL_DESC = "alphabetical_desc"
    LATEST = "latest"
    OLDEST = "oldest"
    LATEST_STRAND = "latest_strand"
    OLDEST_STRAND = "oldest_strand"
    LATEST_UPDATED = "latest_updated"
    OLDEST_UPDATED = "oldest_updated"
    LOWEST_SALE_PRICE = "lowest_sale_price"
    HIGHEST_SALE_PRICE = "highest_sale_price"
    LOWEST_PRICE = "lowest_price"
    HIGHEST_PRICE = "highest_price"
    LOWEST_PRICE_M2 = "lowest_price_m2"
    HIGHEST_PRICE_M2 = "highest_price_m2"


class SortEnum(str, Enum):
    ASC = "asc"
    DESC = "desc"


class MostSpokenLanguages(str, Enum):
    ARABIC = "Arabic"
    BENGALI = "Bengali"
    BHOJPURI = "Bhojpuri"
    BULGARIAN = "Bulgarian"
    Catalan = "Catalan"
    CROATIAN = "Croatian"
    CZECH = "Czech"
    DANISH = "Danish"
    DUTCH = "Dutch"
    ENGLISH = "English"
    ESTONIAN = "Estonian"
    FILIPINO = "Filipino"
    FINNISH = "Finnish"
    FRENCH = "French"
    GERMAN = "German"
    GREEK = "Greek"
    GUJARATI = "Gujarati"
    HINDI = "Hindi"
    HUNGARIAN = "Hungarian"
    INDONESIAN = "Indonesian"
    IRISH = "Irish"
    ITALIAN = "Italian"
    JAPANESE = "Japanese"
    JAVANESE = "Javanese"
    KOREAN = "Korean"
    LATVIAN = "Latvian"
    LITHUANIAN = "Lithuanian"
    MALTESE = "Maltese"
    MANDARIN_CHINESE = "Mandarin Chinese"
    MARATHI = "Marathi"
    NORWEGIAN = "Norwegian"
    PERSIAN = "Persian"
    POLISH = "Polish"
    PORTUGUESE = "Portuguese"
    ROMANIAN = "Romanian"
    RUSSIAN = "Russian"
    SLOVAK = "Slovak"
    SLOVENIAN = "Slovenian"
    SPANISH = "Spanish"
    SWAHILI = "Swahili"
    SWEDISH = "Swedish"
    TAMIL = "Tamil"
    TELUGU = "Telugu"
    THAI = "Thai"
    TURKISH = "Turkish"
    UKRAINIAN = "Ukrainian"
    URDU = "Urdu"
    VIETNAMESE = "Vietnamese"
    WU_CHINESE = "Wu Chinese"
    YORUBA = "Yoruba"


class LeadStatus(str, Enum):
    NEW = "new"
    CONTACTED = "contacted"
    QUALIFICATION = "qualification"
    PROPOSAL_OFFERING = "proposal_offering"
    CLOSED_WON = "closed_won"
    CLOSED_LOST = "closed_lost"


class LeadAssignmentStatus(str, Enum):
    UNASSIGNED = "unassigned"
    INACTIVE_AGENTS = "inactive_agents"


class LeadRelevance(str, Enum):
    COLD = "cold"
    WARM = "warm"
    HOT = "hot"
    NEUTRAL = "neutral"


class LeadType(str, Enum):
    BUYING = "buying"
    SELLING = "selling"
    OTHER = "other"


class LeadSource(str, Enum):
    PERSONAL_CONTACT = "personal_contact"
    EMAIL = "email"
    PHONE_SMS = "phone_sms"
    PORTAL = "portal"
    WEBSITE = "website"
    OPEN_HOUSE = "open_house"
    OTHER = "other"
    FACEBOOK = "Facebook"


class EnergyCertificateRating(str, Enum):
    A = "A"
    B = "B"
    C = "C"
    D = "D"
    E = "E"
    F = "F"
    G = "G"


class ImagesQuality(str, Enum):
    GOOD = "good"
    POOR = "poor"
    INSUFFICIENT = "insufficient"


TitleValidation = Field(
    ..., description="Title of the lead", min_length=1, max_length=255
)

MINIMUM_PRICE: int = 0
MAXIMUM_PRICE: int = 999_999_999_999


class SigningMethod(StrEnum):
    PEN_AND_PAPER = auto()
    # Simple Electronic Signature (Google etc) --> Email address required
    SIMPLE = auto()
    # Advanced Electronic Signature (Band ID etc) --> Email address required
    ADVANCED = auto()
    # Qualified Electronic Signature (passport etc) --> Email address required
    QUALIFIED = auto()


class LevelOfSignatureSoWise:
    PEN_AND_PAPER = 0
    SIMPLE = 1
    ADVANCED = 2
    QUALIFIED = 3


class OfferDepositePaymentPaidTo(StrEnum):
    STRAND_PROPERTIES_SL = auto()
    OTHER = auto()


class PropertyType(StrEnum):
    APARTMENT = "Apartment"
    BAR = "Bar"
    BOAT = "Boat"
    BUILDING = "Building"
    BUNGALOW = "Bungalow"
    BUSINESS = "Business"
    CASTLE = "Castle"
    CHALET = "Chalet"
    COMMERCIAL_OTHER = "Commercial Other"
    COMMERCIAL_PREMISES = "Commercial Premises"
    CORTIJO = "Cortijo"
    COUNTRY_HOUSE = "Country House"
    DEVELOPMENT_LAND = "Development Land"
    DISCOTHEQUE = "Discotheque"
    DUPLEX = "Duplex"
    DUPLEX_PENTHOUSE = "Duplex Penthouse"
    ESTATE = "Estate"
    FINCA = "Finca"
    FLAT = "Flat"
    GOLF_COURSE = "Golf Course"
    GOLF_PLOT = "Golf Plot"
    GROUND_FLOOR_APARTMENT = "Ground Floor Apartment"
    GROUND_FLOOR_DUPLEX = "Ground Floor Duplex"
    HOTEL = "Hotel"
    HOTEL_PLOT = "Hotel Plot"
    HOUSE = "House"
    INDUSTRIAL_LAND = "Industrial Land"
    INDUSTRIAL_PREMISES = "Industrial Premises"
    INVESTMENT = "Investment"
    LOFT = "Loft"
    MANSION = "Mansion"
    MOORING = "Mooring"
    OFFICE = "Office"
    OFFICE_UNITS = "Office Units"
    PALACE = "Palace"
    PARKING = "Parking"
    PENTHOUSE = "Penthouse"
    PLOT = "Plot"
    RESIDENTIAL_PLOT = "Residential Plot"
    RESTAURANT = "Restaurant"
    RIAD = "Riad"
    RUSTIC_PLOT = "Rustic Plot"
    SEMI_DETACHED_HOUSE = "Semi Detached House"
    SEMI_DETACHED_VILLA = "Semi Detached Villa"
    SHOP = "Shop"
    SHOPPING_CENTRE = "Shopping Centre"
    STORE_ROOM = "Store Room"
    STUDIO = "Studio"
    SUPERMARKET = "Supermarket"
    TOWN_HOUSE = "Town House"
    TRIPLEX = "Triplex"
    UNIQUE_BUILDING = "Unique Building"
    VILLA = "Villa"


class PropertyField(StrEnum):
    BATHROOMS = "bathrooms"
    BEDROOMS = "bedrooms"
    CITY = "city"
    BUILT_AREA = "built_area"
    PLOT_AREA = "plot_area"
    CADASTRAL_REFERENCE = "cadastral_reference"
    COMMISSION = "commission"
    LATITUDE = "latitude"
    LONGITUDE = "longitude"
    LOCATION_ADDRESS = "location_address"
    LOCATION_POSTCODE = "location_postCode"


DEFAULT_PROPERTY_FIELDS_REQUIRED_FOR_PUBLISHING: Set[PropertyField] = {
    PropertyField.BATHROOMS,
    PropertyField.BEDROOMS,
    PropertyField.CITY,
    PropertyField.BUILT_AREA,
    PropertyField.PLOT_AREA,
    PropertyField.CADASTRAL_REFERENCE,
    PropertyField.COMMISSION,
    PropertyField.LATITUDE,
    PropertyField.LONGITUDE,
    PropertyField.LOCATION_ADDRESS,
    PropertyField.LOCATION_POSTCODE,
}


PROPERTY_FIELDS_REQUIRED_FOR_PUBLISHING: Dict[PropertyType, Set[PropertyField]] = (
    defaultdict(
        lambda: DEFAULT_PROPERTY_FIELDS_REQUIRED_FOR_PUBLISHING,
        {
            PropertyType.APARTMENT: DEFAULT_PROPERTY_FIELDS_REQUIRED_FOR_PUBLISHING
            - {PropertyField.PLOT_AREA},
            PropertyType.PLOT: DEFAULT_PROPERTY_FIELDS_REQUIRED_FOR_PUBLISHING
            - {
                PropertyField.BATHROOMS,
                PropertyField.BEDROOMS,
                PropertyField.BUILT_AREA,
            },
        },
    )
)


class IVATaxEnum(str, Enum):
    TAX_INCLUDED = "Tax included"
    TAX_ADDED = "Tax added"
    NO_TAX = "No tax"


class CommissionTypeEnum(str, Enum):
    FIXED = "Fixed"
    PERCENT = "Percent"


class SoldBy(str, Enum):
    STRAND = "Strand"
    OTHER = "Other"


class TranslationsLanguages(str, Enum):
    ENGLISH = "English"
    FINNISH = "Finnish"


class CheckboxStatus:
    CHECKED = "☑"
    UNCHECKED = "☐"


class DoSDepositAccountType(StrEnum):
    STRAND_CLIENTS_ACCOUNT = auto()
    STRAND_COMMISSION = auto()
    LAWYERS_ACCOUNT_BUYER = auto()
    LAWYERS_ACCOUNT_VENDOR = auto()
    DEVELOPERS_ACCOUNT = auto()
    VENDORS_ACCOUNT = auto()
    NO_DEPOSIT_PAID = auto()
    OTHER = auto()


class DoSCommissionType(StrEnum):
    PERCENT_PLUS_VAT = auto()
    AMOUNT_PLUS_VAT = auto()
    PERCENT_VAT_INCLUDED = auto()
    AMOUNT_VAT_INCLUDED = auto()


class ContactType(str, Enum):
    PERSON = "Person"
    ORGANIZATION = "Organization"
    ESTATE = "Estate"


class ContactStatus(str, Enum):
    LEAD = "lead"
    VIEWING = "viewing"
    OFFERING = "offering"
    SELLING = "selling"
    BOUGHT = "bought"
    SOLD = "sold"


class ContactRelatedPartyType(str, Enum):
    COMPANY_BENEFICIARY = "company_beneficiary"
    COMPANY_SIGNING_RIGHTS = "company_signing_rights"
    ESTATE_PARTY = "estate_party"
    ESTATE_AUTHORIZED_PARTY = "estate_authorized_party"


class EventType(str, Enum):
    OPEN_HOUSE_VIEWING = "open_house_viewing"
    PRIVATE_VIEWING = "private_viewing"
    CLIENT_MEETING = "client_meeting"
    NOTARY_MEETING = "notary_meeting"
    OTHER = "other"


class EventFormat(str, Enum):
    PHYSICAL = "physical"
    VIRTUAL = "virtual"
    HYBRID = "hybrid"


class ExternalEventSource(str, Enum):
    MAILGUN = "mailgun"
    OUTLOOK = "outlook"


class StatusProcessLeadData(StrEnum):
    PROCESSED = "processed"
    UNPROCESSED = "unprocessed"
    DISCARDED = "discarded"


class Captures(StrEnum):
    FACEBOOK_CAPTURE = "facebook_capture"
    SERVICEFORM_CAPTURE = "serviceform_capture"


class ContractTypes(StrEnum):
    OFFER = "offer"
    SALE_AGREEMENT = "sale_agreement"
    DETAILS_OF_SALE = "details_of_sale"


FB_GRAPH_API_BASE_URL = "https://graph.facebook.com/v20.0"

DEV_EMAILS = ["<EMAIL>", "<EMAIL>"]

MAIL_GUN_WHITE_LIST_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]

WHITELIST_EMAIL_DOMAINS = ["@strand.fi", "@strand.es", "@loisto.ai", "@reaktor.com"]

DEVELOPER_EXCLUDE_EMAIL_DOMAINS = [
    "test",
    "px8.fi",
    "reaktor.com",
    "loisto.ai",
    "reactron.dev",
    "reactronvietnam.vn",
    "tekai.vn",
]


class StatusLeadProcess(Enum):
    SUCCESS = 1000
    ERROR_IGNORE_LEAD = 4001
    ERROR_MISSING_EMAIL_OR_PHONE = 4002


class VatEnum(Enum):
    FI = Decimal(0.255)
    ES = Decimal(0.21)


KIVI_API_BASE_URL = "https://api.prod.kivi.etuovi.com/ext-api/v3"


class ErrorCode:
    DOS_INVOICE_NUMBER_DUPLICATE = "EDUP-INV"


class DOSTransactionOrderBy(str, Enum):
    TRANSACTION_REFERENCE = "transaction_ref"
    PROPERTY_DESCRIPTION = "property_description"
    SALES_PRICE = "sales_price"
    TOTAL_COMMISSION = "total_commissions"
    TOTAL_COMMISSION_PERCENT = "total_commission_percent"
    STRAND_COMMISSION_EARNED = "strand_commission_earned"
    AGENT_COMMISSION_EARNED = "agent_commission_earned"


class FISalesAgreementStatusEnum(str, Enum):
    DRAFT = "draft"
    VALIDATED = "validated"
    PENDING_SIGNATURES = "pending_signatures"
    COMPLETED = "completed"


class FISalesAgreementAvailabilityEnum(str, Enum):
    IMMEDIATELY = "immediately"
    NEGOTIABLE = "negotiable"
    DATE = "date"
    RENTED = "rented"
    OTHER = "other"


class FISalesAgreementPaymentTermsEnum(str, Enum):
    CASH = "cash"
    EXCHANGE = "exchange"
    OTHER = "other"


class FISalesAgreementLeaseAgreementEnum(str, Enum):
    WRITTEN = "written_agreement"
    ORAL = "oral_agreement"


class FISalesAgreementTermEnum(str, Enum):
    FIXED = "fixed"
    INDEFINITE = "indefinite"


class FISalesAgreementCommissionBasisEnum(str, Enum):
    DEBT_FREE_PURCHASE_PRICE = "debt_free_purchase_price"
    PURCHASE_PRICE = "purchase_price"


class FISalesAgreementCommissionTypeEnum(str, Enum):
    PERCENTAGE = "percentage"
    FIXED = "fixed"
    OTHER = "other"


class FISalesAgreementAcquisitionEnum(str, Enum):
    PURCHASE = "purchase"
    EXCHANGE = "exchange"
    GIFT = "gift"
    INHERITAGE = "inheritage"
    TESTAMENT = "testament"
    PARTITIONING = "partitioning"
    OTHER = "other"


class FISalesAgreementTaxConsequenceEnum(str, Enum):
    TAX_ON_CAPITAL_GAIN_PAYABLE = "tax_on_capital_gain_payable"
    TAX_ON_CAPITAL_GAIN_NOT_PAYABLE = "tax_on_capital_gain_not_payable"
    NO_CHANCE_TO_FIND_OUT = "no_chance_to_find_out"


class FISalesAgreementShareRegisterFormatEnum(str, Enum):
    DIGITAL = "digital"
    PAPER = "paper"


class FIPurchaseOfferStatusEnum(str, Enum):
    DRAFT = "draft"
    VALIDATED = "validated"
    PENDING_OFFEROR_SIGNATURE = "pending_offeror_signatures"
    OFFEROR_SIGNED = "offeror_signed"
    PENDING_OFFEREE_SIGNATURE = "pending_offeree_signatures"
    OFFEREE_SIGNED = "offeree_signed"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"


class FIPurchaseOfferPaymentMethod(str, Enum):
    CASH = "cash"
    INSTALLMENT = "installment"


class FIPurchaseOfferRightOfUseTransfer(str, Enum):
    PURCHASE = "purchase"
    LATER = "later"


class FIPurchaseOfferDigitalPurchaseExpenses(str, Enum):
    BUYER = "buyer"
    SELLER = "seller"
    BOTH = "both"
    OTHER = "other"


class FIPurchaseOfferDownPaymentTerm(str, Enum):
    MARK_AS_PAID = "mark_as_paid"
    TO_BE_PAID = "to_be_paid"
    OTHER = "other"


class DocumentSigningEntityType(str, Enum):
    FI_SALES_AGREEMENT = "fi_sales_agreement"
    FI_PURCHASE_OFFER = "fi_purchase_offer"
    FI_COUNTER_OFFER = "fi_counter_offer"
    DOCUMENT_LIBRARY_ITEM = "document_library_item"


class DocumentStatus(str, Enum):
    CREATED = "created"
    PENDING = "pending"
    COMPLETED = "completed"
    DECLINED = "declined"


class DocumentSignatureStatus(str, Enum):
    PENDING = "pending"
    DECLINED = "declined"
    COMPLETED = "signed"


class DocumentSignatureUserType(str, Enum):
    USER = "user"
    CONTACT = "contact"


class DocumentEventAction(str, Enum):
    SIGNER_SIGNED = "signer_signed"
    SIGNING_COMPLETED = "signing_completed"
    # TODO: double check if this can be deprecated
    SIGNING_DECLINED = "signing_declined"
    SIGNING_CREATED = "signing_created"
    SIGNER_DECLINED = "signer_declined"


class DocumentEventStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    DECLINED = "declined"


class PortalIntegration(StrEnum):
    KYERO = "kyero"
    THINKSPAIN = "thinkspain"
    RESALES_ONLINE = "resalesonline"
    IDEALISTA = "idealista"
    PULL_RESALES_ONLINE_ALL = "pull_resales_online_all"  # pull information from resalesonline using getall=True
    PULL_RESALES_ONLINE_RELATIVE = "pull_resales_online_relative"  # pull information from resalesonline using getall=False
    FACEBOOK = "facebook"
    LEADINGRE = "leadingre"
    A_PLACE_IN_THE_SUN = "aplaceinthesun"
    LUXURY_ESTATE = "luxuryestate"
    PISOS = "pisos"
    JAMES_EDITION = "jamesedition"
    GOOGLE = "google"
    INMOBALIA = "inmobalia"
