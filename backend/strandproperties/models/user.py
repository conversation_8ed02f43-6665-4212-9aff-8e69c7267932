from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (
    JSO<PERSON>,
    TEX<PERSON>,
    BigInteger,
    Boolean,
    Column,
    Foreign<PERSON>ey,
    String,
    Text,
    UniqueConstraint,
    select,
)
from sqlalchemy.ext import declarative
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, Session, mapped_column, relationship, synonym
from tet.util.crypt import crypt, verify

from strandproperties.constants import DEFAULT_ROLE, RoleType
from strandproperties.libs.utils import EncryptionError, decrypt, encrypt
from strandproperties.models.activity import Activity
from strandproperties.models.base import BaseModel
from strandproperties.models.event import Event
from strandproperties.models.event_log import EventLog
from strandproperties.models.office import Office
from strandproperties.models.tag import Tag
from strandproperties.models.transaction import ExpenseTransaction

if TYPE_CHECKING:
    from strandproperties.models.group import Group
    from strandproperties.models.lead import Lead
    from strandproperties.models.match_making import MatchMaking
    from strandproperties.models.organization import Organization


class User(BaseModel):
    __tablename__ = "user"

    legacy_username: Mapped[Optional[str]] = mapped_column(String(20))
    first_name: Mapped[str] = mapped_column(Text, nullable=False)
    last_name: Mapped[str] = mapped_column(Text, nullable=False)
    email: Mapped[str] = mapped_column(Text, nullable=False, unique=True)
    _password = Column("password", Text, nullable=True)

    phone_number: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    social_security_number: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_superadmin: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    details = Column(
        MutableDict.as_mutable(JSON),
        nullable=False,
        default={},
    )
    roaiib: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    liability_insurance: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    photo_url: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    _roles: Mapped[List["Role"]] = relationship("Role", back_populates="user")
    organizations = association_proxy("_roles", "organization")
    team_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    reference_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    cognito_sub: Mapped[str | None] = mapped_column(
        String(255), unique=True, nullable=True, index=True
    )

    _contacts = relationship(
        "Contact", secondary="contact_realtor", back_populates="assigned_to_users"
    )

    events: Mapped[List["Event"]] = relationship(
        "Event", secondary="event_user", back_populates="users"
    )

    leads: Mapped[List["Lead"]] = relationship(
        "Lead", secondary="lead_user", back_populates="assigned_to_users"
    )

    match_makings: Mapped[List["MatchMaking"]] = relationship(
        "MatchMaking", secondary="match_making_user", back_populates="assigned_to_users"
    )

    properties = relationship(
        "Property", secondary="property_realtor", back_populates="realtor_users"
    )

    tags: Mapped[List["Tag"]] = relationship(
        "Tag", secondary="user_tag", back_populates="users"
    )

    groups: Mapped[List["Group"]] = relationship(
        "Group", secondary="group_user", back_populates="assigned_to_users"
    )

    offices: Mapped[List["Office"]] = relationship(
        "Office", secondary="user_office", back_populates="users"
    )

    event_logs: Mapped[List["EventLog"]] = relationship(
        "EventLog", back_populates="actor"
    )

    activities: Mapped[List["Activity"]] = relationship(
        "Activity", back_populates="actor"
    )

    slug: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True, unique=True)

    company_id = Column(BigInteger, ForeignKey("company.id"), nullable=True)

    company = relationship("Company", foreign_keys=[company_id], back_populates="users")

    # to store the encrypted value of the dias api key
    _dias_api_key: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    expense_transactions = relationship("ExpenseTransaction", back_populates="user")
    stripe_customer_id = Column(String(255), nullable=True)

    @property
    def has_dias_api_key(self) -> bool:
        return self._dias_api_key is not None and self._dias_api_key != ""

    def set_dias_api_key(self, dias_api_key):
        try:
            encrypted_value = encrypt(dias_api_key)
        except Exception as e:
            raise EncryptionError(
                f"Unexpected error during set_dias_api_key: {e}"
            ) from e
        self._dias_api_key = encrypted_value

    def get_dias_api_key(self) -> Optional[str]:
        if not self.has_dias_api_key:
            return None
        try:
            return decrypt(self._dias_api_key)
        except Exception as e:
            raise EncryptionError(
                f"Unexpected error during get_dias_api_key: {e}"
            ) from e

    @hybrid_property
    def balance(self):
        # The balance is in euro cents
        return sum(transaction.amount for transaction in self.expense_transactions)

    @hybrid_property
    def offices_name(self) -> List[str]:
        return [office.name for office in self.offices]

    @hybrid_property
    def organizations_name(self) -> List[str]:
        return [organization.name for organization in self.organizations]

    @hybrid_property
    def tags_name(self) -> List[str]:
        return [tag.name for tag in self.tags]

    def set_password(self, password):
        self._password = crypt(password)

    def _get_password(self):
        """Return the hashed version of the password."""
        return self._password

    def validate_password(self, password):
        if self._password is None:
            return False

        return verify(password, self._password)

    @declarative.declared_attr
    def password(cls):
        return synonym(
            "_password", descriptor=property(cls._get_password, cls.set_password)
        )

    @property
    def roles(self):
        all_roles = list(self._roles)

        # if self.is_superadmin:
        #     # NOTE: this is expensive operation, but atm only used for listing role
        #     db_session = Session.object_session(self)
        #     all_roles += [
        #         {"role": RoleType.ADMIN, "organization": org}
        #         for org in db_session.query(Organization).all()
        #     ]

        return all_roles


class Role(BaseModel):
    __tablename__ = "role"
    __table_args__ = (UniqueConstraint("organization_id", "user_id"),)

    organization_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("organization.id"), nullable=False, index=True
    )
    user_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("user.id"), nullable=False, index=True
    )
    role: Mapped[RoleType] = mapped_column(Text, nullable=False, default=DEFAULT_ROLE)

    user = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="_roles",
    )
    organization = relationship(
        "Organization",
        foreign_keys=[organization_id],
        # back_populates="roles",
    )


def resolve_principals(userid, request):
    if userid:
        return ["role:%s" % role for role in request.jwt_claims.get("roles", [])]
    return []


def get_user(request):
    userid = request.unauthenticated_userid
    db_session = request.find_service(Session)
    user = db_session.scalar(select(User).where(User.cognito_sub == userid))
    if user:
        return user

    return None
