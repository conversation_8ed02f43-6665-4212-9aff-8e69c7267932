from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Index, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import EventLogActorType

from .base import BaseModel


class EventLog(BaseModel):
    """
    EventLog model table schema definition
    """

    __tablename__ = "event_log"

    object_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    object_id: Mapped[int] = mapped_column(Integer, nullable=False)
    actor_id: Mapped[int] = mapped_column(
        ForeignKey("user.id"), nullable=True, index=True
    )
    actor_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default=EventLogActorType.USER,
        server_default=EventLogActorType.USER,
    )
    action: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    data_before: Mapped[dict] = mapped_column(JSON, nullable=True)
    data_after: Mapped[dict] = mapped_column(JSON, nullable=True)
    details: Mapped[dict] = mapped_column(JSON, nullable=True)
    relation_object_type: Mapped[str] = mapped_column(
        String(50), nullable=True, index=True
    )
    relation_object_id: Mapped[int] = mapped_column(Integer, nullable=True)

    actor = relationship("User", back_populates="event_logs")

    __table_args__ = (
        Index("idx_object_type_object_id", "object_type", "object_id"),
        Index(
            "idx_relation_object_type_object_id", "relation_object_type", "object_id"
        ),
    )

    def __repr__(self):
        return f"<EventLog(object_type={self.object_type}, object_id={self.object_id}, actor_type={self.actor_type}, actor_id={self.actor_id}, action={self.action})>"
