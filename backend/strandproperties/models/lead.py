from typing import List, Optional

from sqlalchemy import ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.contact import Contact
from strandproperties.models.event import Event
from strandproperties.models.user import User


class Lead(BaseModel):
    """
    Lead model table schema definition
    """

    __tablename__ = "lead"

    status: Mapped[str] = mapped_column(Text, nullable=False)
    relevance: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    assigned_to_users: Mapped[List["User"]] = relationship(
        "User", secondary="lead_user", back_populates="leads"
    )
    property_reference: Mapped[Optional[str]] = mapped_column(
        ForeignKey("property.reference"), nullable=True
    )
    contacts: Mapped[List["Contact"]] = relationship(
        "Contact", secondary="lead_contact", back_populates="leads"
    )
    events: Mapped[List["Event"]] = relationship(
        "Event", secondary="event_lead", back_populates="leads"
    )
    title: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    type: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    source: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    hs_object_id: Mapped[Optional[str]] = mapped_column(Text)
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("organization.id"), nullable=False, index=True
    )
    raw_lead_data = relationship("RawLeadData", back_populates="sale_activity")
    match_makings = relationship("MatchMaking", back_populates="lead")


class LeadUser(BaseModel):
    """
    LeadUser model table schema definition
    """

    __tablename__ = "lead_user"
    __table_args__ = (
        UniqueConstraint("lead_id", "user_id", name="uq_lead_user"),
        Index("ix_lead_user_lead", "lead_id"),
        Index("ix_lead_user_user", "user_id"),
    )
    lead_id = mapped_column("lead_id", ForeignKey("lead.id"), nullable=False)
    user_id = mapped_column("user_id", ForeignKey("user.id"), nullable=False)


class LeadContact(BaseModel):
    """
    LeadContact model table schema definition
    """

    __tablename__ = "lead_contact"
    __table_args__ = (
        UniqueConstraint("lead_id", "contact_id", name="uq_lead_contact"),
        Index("ix_lead_contact_lead", "lead_id"),
        Index("ix_lead_contact_contact", "contact_id"),
    )
    lead_id = mapped_column("lead_id", ForeignKey("lead.id"), nullable=False)
    contact_id = mapped_column("contact_id", ForeignKey("contact.id"), nullable=False)
