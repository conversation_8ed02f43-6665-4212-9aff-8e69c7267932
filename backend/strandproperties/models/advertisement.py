import uuid
from datetime import datetime, timezone
from typing import List, Optional

from sqlalchemy import (
    JSON,
    Boolean,
    DateTime,
    Enum,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.models.organization import Organization
from strandproperties.models.property import Property
from strandproperties.models.user import User
from strandproperties.schemas.advertisement import AdLanguage, AdStatus, AdType


class Advertisement(BaseModel):
    __tablename__ = "advertisement"

    owner_id: Mapped[int] = mapped_column(
        ForeignKey("user.id"), nullable=False, index=True
    )
    owner: Mapped[Optional["User"]] = relationship("User", foreign_keys=[owner_id])

    type: Mapped[AdType] = mapped_column(Enum(AdType), nullable=False)

    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    primary_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    country: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    municipality: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    status: Mapped[AdStatus] = mapped_column(
        Enum(AdStatus, values_callable=lambda x: [e.value for e in x]),
        nullable=False,
        default=AdStatus.DRAFT,
    )
    status_reason: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)

    start_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, default=None
    )
    end_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    budget_total: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    budget_daily: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, default=0.0
    )

    target_area_radius_km: Mapped[Optional[float]] = mapped_column(
        Float, nullable=False, default=50.0
    )
    conversion_location_url: Mapped[Optional[str]] = mapped_column(
        String(512), nullable=True
    )

    language: Mapped[AdLanguage] = mapped_column(Enum(AdLanguage), nullable=True)
    entry_point: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    metrics: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    ad_content: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    preview_url: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)

    is_smartly_ad: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    link_clicks: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, default=0
    )
    impressions: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, default=0
    )
    ctr: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, default=0.0
    )  # Click-through rate – percentage of clicks per impression
    cpm: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, default=0.0
    )  # Cost per mille – cost per thousand impressions (stored in cents)
    cpc: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, default=0.0
    )  # Cost per click (stored in cents)

    organization_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("organization.id"), nullable=True, index=True
    )
    organization: Mapped[Optional["Organization"]] = relationship("Organization")

    property_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("property.id"), nullable=True, index=True
    )
    property: Mapped[Optional["Property"]] = relationship(
        "Property", foreign_keys=[property_id]
    )

    event_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("event.id"), nullable=True, index=True
    )
    ad_template_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("ad_template.id"), nullable=True, index=True
    )
    fi_property_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_property.id"), nullable=True, index=True
    )
    fi_property: Mapped[Optional["FIProperty"]] = relationship(
        "FIProperty", foreign_keys=[fi_property_id]
    )

    agent_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("user.id"), nullable=True, index=True
    )
    agent: Mapped[Optional["User"]] = relationship("User", foreign_keys=[agent_id])

    advertisement_images: Mapped[List["AdvertisementImage"]] = relationship(
        "AdvertisementImage",
        back_populates="advertisement",
        cascade="all, delete-orphan",
        order_by="AdvertisementImage.order",
    )

    preview_settings: Mapped[Optional["AdvertisementPreviewSettings"]] = relationship(
        "AdvertisementPreviewSettings",
        back_populates="advertisement",
        uselist=False,
        cascade="all, delete-orphan",
    )


class AdvertisementImage(BaseModel):
    __tablename__ = "advertisement_image"

    advertisement_id: Mapped[int] = mapped_column(
        ForeignKey("advertisement.id"), nullable=False, index=True
    )
    is_hidden: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    url: Mapped[str] = mapped_column(String(3000), nullable=False)
    order: Mapped[Optional[int]] = mapped_column(Integer)

    advertisement: Mapped["Advertisement"] = relationship(
        "Advertisement", back_populates="advertisement_images"
    )


class AdvertisementPreviewSettings(BaseModel):
    __tablename__ = "advertisement_preview_settings"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, nullable=False
    )

    advertisement_id: Mapped[int] = mapped_column(
        ForeignKey("advertisement.id", ondelete="CASCADE"),
        unique=True,
        index=True,
        nullable=False,
    )

    display_metrics: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False
    )
    display_details: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False
    )
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=True)

    advertisement: Mapped["Advertisement"] = relationship(
        "Advertisement", back_populates="preview_settings", uselist=False
    )
