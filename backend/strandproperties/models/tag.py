from sqlalchemy import Column, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Tag(BaseModel):
    __tablename__ = "tag"

    name: Mapped[str] = mapped_column(Text, nullable=False, unique=True)

    contacts = relationship("Contact", secondary="contact_tag", back_populates="tags")
    users = relationship("User", secondary="user_tag", back_populates="tags")
    groups = relationship("Group", secondary="group_tag", back_populates="tags")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
        }


class ContactTag(BaseModel):
    __tablename__ = "contact_tag"

    tag_id = Column(ForeignKey("tag.id"), nullable=False)

    contact_id = Column(ForeignKey("contact.id"), nullable=False)


class UserTag(BaseModel):
    __tablename__ = "user_tag"
    __table_args__ = (
        UniqueConstraint("user_id", "tag_id", name="uq_user_tag"),
        Index("ix_user_tag_user", "user_id"),
        Index("ix_user_tag_tag", "tag_id"),
    )

    user_id = Column(ForeignKey("user.id"), nullable=False)

    tag_id = Column(ForeignKey("tag.id"), nullable=False)


class GroupTag(BaseModel):
    __tablename__ = "group_tag"
    __table_args__ = (
        UniqueConstraint("group_id", "tag_id", name="uq_group_tag"),
        Index("ix_group_tag_group", "group_id"),
        Index("ix_group_tag_tag", "tag_id"),
    )

    group_id = Column(ForeignKey("group.id"), nullable=False)

    tag_id = Column(ForeignKey("tag.id"), nullable=False)
