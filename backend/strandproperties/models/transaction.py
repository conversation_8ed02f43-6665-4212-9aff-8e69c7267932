from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy_utils import generic_relationship  # Utility for generic associations

from strandproperties.models.base import BaseModel
from strandproperties.schemas.transaction import (
    ExpenseTransactionStatus,
    ExpenseTransactionTargetEntity,
    ExpenseTransactionType,
)


class ExpenseTransaction(BaseModel):
    __tablename__ = "transactions"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("user.id"), nullable=False, index=True
    )
    amount: Mapped[int] = mapped_column(nullable=False)  # Amount is in euro cents
    type: Mapped[Optional[ExpenseTransactionType]] = mapped_column(String(20))
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    meta: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    stripe_transaction_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )
    status: Mapped[ExpenseTransactionStatus] = mapped_column(String(20), nullable=False)

    expense_targets = relationship(
        "ExpenseTransactionTarget",
        back_populates="expense_transaction",
        cascade="all, delete-orphan",
    )

    user = relationship("User", back_populates="expense_transactions")


class ExpenseTransactionTarget(BaseModel):
    __tablename__ = "expense_transaction_targets"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    expense_transaction_id: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("transactions.id"), nullable=False
    )

    target_type: Mapped[ExpenseTransactionTargetEntity] = mapped_column(
        String(20), nullable=False
    )
    target_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    reference: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    target = generic_relationship(target_type, target_id)

    expense_transaction = relationship(
        "ExpenseTransaction", back_populates="expense_targets"
    )
