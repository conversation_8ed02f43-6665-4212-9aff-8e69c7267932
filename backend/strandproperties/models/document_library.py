from enum import Enum
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import ForeignKey, Index, Integer, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.document_signing import DocumentSigning
from strandproperties.models.user import User


class OwnerType(str, Enum):
    FI_PROPERTY = "FI_PROPERTY"
    FI_SALES_AGREEMENT = "FI_SALES_AGREEMENT"
    FI_PURCHASE_OFFER = "FI_PURCHASE_OFFER"
    CONTACT = "CONTACT"


class UploadState(str, Enum):
    PENDING_UPLOAD = "PENDING_UPLOAD"
    READY = "READY"


class DocumentType(str, Enum):
    FI_ASBESTOS_SURVEY = "FI_ASBESTOS_SURVEY"
    FI_SHARE_CERTIFICATE = "FI_SHARE_CERTIFICATE"
    FI_ENERGY_CERTIFICATE = "FI_ENERGY_CERTIFICATE"
    FI_COMPREHENSIVE_BROCHURE = "FI_COMPREHENSIVE_BROCHURE"
    FI_BRIEF_BROCHURE = "FI_BRIEF_BROCHURE"
    FI_WINDOW_CARD = "FI_WINDOW_CARD"
    FI_PROPERTY_MANAGERS_CERTIFICATE = "FI_PROPERTY_MANAGERS_CERTIFICATE"
    FI_PLANNING_DOCUMENTS = "FI_PLANNING_DOCUMENTS"
    FI_PURCHASE_AGREEMENT = "FI_PURCHASE_AGREEMENT"
    FI_PROPERTY_REGISTER_EXTRACT = "FI_PROPERTY_REGISTER_EXTRACT"
    FI_PROPERTY_REGISTER_MAP = "FI_PROPERTY_REGISTER_MAP"
    FI_PROPERTY_TAX_STATEMENT = "FI_PROPERTY_TAX_STATEMENT"
    FI_MOISTURE_MEASUREMENT = "FI_MOISTURE_MEASUREMENT"
    FI_MAINTENANCE_PLAN = "FI_MAINTENANCE_PLAN"
    FI_MAINTENANCE_NEED_ASSESSMENT = "FI_MAINTENANCE_NEED_ASSESSMENT"
    FI_CONDITION_INSPECTION_REPORT = "FI_CONDITION_INSPECTION_REPORT"
    FI_USAGE_RIGHTS_EXTRACT = "FI_USAGE_RIGHTS_EXTRACT"
    FI_CERTIFICATE_OF_TITLE = "FI_CERTIFICATE_OF_TITLE"
    FI_OTHER = "FI_OTHER"
    FI_OWNER_APARTMENT_PRINTOUT = "FI_OWNER_APARTMENT_PRINTOUT"
    FI_SHARE_REGISTER_PRINT = "FI_SHARE_REGISTER_PRINT"
    FI_LONG_TERM_MAINTENANCE_PLAN = "FI_LONG_TERM_MAINTENANCE_PLAN"
    FI_SPOUSES_CONSENT = "FI_SPOUSES_CONSENT"
    FI_FLOOR_PLAN = "FI_FLOOR_PLAN"
    FI_BUILDING_PERMIT = "FI_BUILDING_PERMIT"
    FI_BUILDING_DRAWINGS = "FI_BUILDING_DRAWINGS"
    FI_ENCUMBRANCE_CERTIFICATE = "FI_ENCUMBRANCE_CERTIFICATE"
    FI_LISTING_FORM = "FI_LISTING_FORM"
    FI_FINANCIAL_STATEMENT = "FI_FINANCIAL_STATEMENT"
    FI_BROKERAGE_AGREEMENT = "FI_BROKERAGE_AGREEMENT"
    FI_LEASE_RIGHTS_CERTIFICATE = "FI_LEASE_RIGHTS_CERTIFICATE"
    FI_LEASE_AGREEMENT = "FI_LEASE_AGREEMENT"
    FI_BROKERAGE_OFFER = "FI_BROKERAGE_OFFER"
    FI_ARTICLES_OF_ASSOCIATION = "FI_ARTICLES_OF_ASSOCIATION"
    FI_GENERAL_MEETING_MINUTES = "FI_GENERAL_MEETING_MINUTES"
    FI_HOUSING_COMPANY_CONDITION_REPORTS = "FI_HOUSING_COMPANY_CONDITION_REPORTS"


class DocumentLibraryItem(BaseModel):
    __tablename__ = "document_library_item"

    owners: Mapped[List["DocumentLibraryItemOwner"]] = relationship(
        "DocumentLibraryItemOwner",
        back_populates="item",
        cascade="all, delete-orphan",
    )

    created_by_id: Mapped[int] = mapped_column(
        BigInteger(),
        ForeignKey("user.id", ondelete="SET NULL"),
        nullable=True,
    )
    created_by: Mapped["User"] = relationship("User", lazy="select")

    s3_key: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True, unique=True
    )

    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    file_size: Mapped[int] = mapped_column(Integer(), nullable=False)

    document_type: Mapped[DocumentType] = mapped_column(
        SQLAlchemyEnum(DocumentType, name="document_type_enum"), nullable=False
    )
    description: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    upload_state: Mapped[UploadState] = mapped_column(
        SQLAlchemyEnum(UploadState, name="upload_state_enum"),
        nullable=False,
    )

    signings: Mapped[List["DocumentSigning"]] = relationship(
        "DocumentSigning",
        secondary="document_signing_entity",
        primaryjoin=(
            "and_("
            "DocumentLibraryItem.id == foreign(DocumentSigningEntity.entity_id), "
            "DocumentSigningEntity.entity_type == 'document_library_item'"
            ")"
        ),
        secondaryjoin="foreign(DocumentSigningEntity.document_signing_id) == DocumentSigning.id",
        viewonly=True,
    )

    def is_owned_by(self, owner_type: OwnerType, owner_id: int) -> bool:
        return any(
            owner.owner_type == owner_type and owner.owner_id == owner_id
            for owner in self.owners
        )

    def _add_owner(self, owner_type: OwnerType, owner_id: int) -> None:
        """Internal method to add an owner to this document library item.
        This method should only be called by the DocumentLibrary service.
        If you need to add or remove owners, please use the DocumentLibrary service methods instead.
        """
        if self.is_owned_by(owner_type, owner_id):
            raise ValueError("Item already owned by given owner")

        self.owners.append(
            DocumentLibraryItemOwner(
                owner_type=owner_type,
                owner_id=owner_id,
            )
        )

    def _remove_owner(self, owner_type: OwnerType, owner_id: int) -> None:
        """Internal method to remove an owner from this document library item.
        This method should only be called by the DocumentLibrary service.
        If you need to add or remove owners, please use the DocumentLibrary service methods instead.
        """
        if not self.is_owned_by(owner_type, owner_id):
            raise ValueError("Item not owned by given owner")

        owner_to_remove = next(
            (
                o
                for o in self.owners
                if o.owner_type == owner_type and o.owner_id == owner_id
            ),
            None,
        )
        if owner_to_remove:
            self.owners.remove(owner_to_remove)


class DocumentLibraryItemOwner(BaseModel):
    __tablename__ = "document_library_item_owner"
    __table_args__ = (
        Index("ix_document_library_item_owner_document", "item_id"),
        Index("ix_document_library_item_owner_owner", "owner_type", "owner_id"),
        UniqueConstraint(
            "item_id",
            "owner_type",
            "owner_id",
            name="uq_document_library_item_owner",
        ),
    )

    item_id: Mapped[int] = mapped_column(
        BigInteger,
        ForeignKey("document_library_item.id", ondelete="CASCADE"),
        nullable=False,
    )
    owner_type: Mapped[OwnerType] = mapped_column(
        SQLAlchemyEnum(OwnerType, name="owner_type_enum"),
        nullable=False,
    )
    owner_id: Mapped[int] = mapped_column(BigInteger(), nullable=False)

    item: Mapped["DocumentLibraryItem"] = relationship(
        "DocumentLibraryItem",
        back_populates="owners",
    )
