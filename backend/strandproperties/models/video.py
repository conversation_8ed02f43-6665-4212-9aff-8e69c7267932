from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Video(BaseModel):
    __tablename__ = "video"

    url: Mapped[str] = mapped_column(String(255), nullable=False)
    property_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("property.id"), nullable=False
    )
    is_hidden: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False)

    __mapper_args__ = {
        "polymorphic_on": "type",
        "polymorphic_identity": "video",
    }


class Tour(Video):
    __mapper_args__ = {
        "polymorphic_identity": "tour",
    }
    property = relationship("Property", back_populates="video_tours")


class Stream(Video):
    __mapper_args__ = {
        "polymorphic_identity": "stream",
    }
    property = relationship("Property", back_populates="video_streams")
