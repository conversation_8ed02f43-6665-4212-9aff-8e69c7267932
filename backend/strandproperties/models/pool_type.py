from sqlalchemy import Column, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import relationship

from strandproperties.models.base import BaseModel


class PoolType(BaseModel):
    __tablename__ = "pool_type"

    name = Column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_pool_type", back_populates="pool_types"
    )


class PropertyPoolType(BaseModel):
    __tablename__ = "property_pool_type"
    __table_args__ = (
        UniqueConstraint("pool_type_id", "property_id", name="uq_pooltype_property"),
        Index("ix_property_pooltype_pooltype", "pool_type_id"),
        Index("ix_property_pooltype_property", "property_id"),
    )

    pool_type_id = Column(ForeignKey("pool_type.id"), nullable=False)
    property_id = Column(ForeignKey("property_spain.id"), nullable=False)
