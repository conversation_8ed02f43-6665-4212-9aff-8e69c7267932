from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, String
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_area import FIArea
from strandproperties.schemas.fi_property.fi_common import FIChoiceEnum, Location

if TYPE_CHECKING:
    from strandproperties.models.fi_property import FIRealty


class FIAddress(BaseModel):
    __tablename__ = "fi_address"

    street_address: Mapped[str] = mapped_column(String(200), nullable=False)
    stairwell: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    apartment_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    postal_code: Mapped[str] = mapped_column(String(10), nullable=False)
    district: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    municipality: Mapped[str] = mapped_column(String(100), nullable=False)
    location: Mapped[Optional[Location]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )

    # TODO: Check the meaning of this field
    show_map_on_announcement: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )

    area_id: Mapped[int] = mapped_column(
        ForeignKey("fi_area.id"), nullable=False, index=True
    )

    area: Mapped["FIArea"] = relationship(
        "FIArea", primaryjoin="foreign(FIAddress.area_id) == FIArea.id"
    )

    fi_realty: Mapped["FIRealty"] = relationship(
        "FIRealty", uselist=False, back_populates="fi_address"
    )
