from datetime import datetime, timed<PERSON>ta
from typing import Optional

from sqlalchemy import Column, DateTime, ForeignKey, Text
from sqlalchemy.orm import Mapped, mapped_column

from strandproperties.constants import StatusResetCode
from strandproperties.models.base import BaseModel


class ResetCode(BaseModel):
    __tablename__ = "reset_code"

    code: Mapped[str] = mapped_column(Text, nullable=False)

    status: Mapped[Optional[StatusResetCode]] = mapped_column(
        Text, default=StatusResetCode.new
    )
    expired_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, default=lambda: datetime.utcnow() + timedelta(hours=2)
    )
    user_id = Column(ForeignKey("user.id"))
