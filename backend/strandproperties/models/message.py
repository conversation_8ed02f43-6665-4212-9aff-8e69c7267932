from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column

from strandproperties.schemas.match_making import EmailStatus

from .base import BaseModel


class Message(BaseModel):
    """
    Messages model table schema definition
    """

    __tablename__ = "message"
    __table_args__ = (
        UniqueConstraint(
            "realtor_id", "contact_id", "id", name="uq_realtor_contact_message"
        ),
        Index("ix_contact_realtor_contact", "contact_id"),
        Index("ix_contact_realtor_realtor", "realtor_id"),
    )

    event_uuid: Mapped[str] = mapped_column(
        ForeignKey("external_event.event_uuid"), nullable=False
    )
    realtor_id: Mapped[int] = mapped_column(ForeignKey("user.id"), nullable=False)
    contact_id: Mapped[int] = mapped_column(ForeignKey("contact.id"), nullable=False)
    message_data: Mapped[Optional[dict]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    status: Mapped[EmailStatus] = mapped_column(
        Text, nullable=False, default=EmailStatus.DELIVERED
    )
