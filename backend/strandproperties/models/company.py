from typing import List, Optional

from sqlalchemy import Text, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from strandproperties.models.base import BaseModel
from strandproperties.models.user import User
from strandproperties.libs.utils import encrypt, decrypt, EncryptionError


class Company(BaseModel):
    __tablename__ = "company"

    name: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    users: Mapped[List["User"]] = relationship("User", back_populates="company")
    address: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    website: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    business_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # to store the encrypted value of the dias api key
    _dias_api_key: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    @property
    def has_dias_api_key(self) -> bool:
        return self._dias_api_key is not None and self._dias_api_key != ""

    def set_dias_api_key(self, dias_api_key):
        try:
            encrypted_value = encrypt(dias_api_key)
        except Exception as e:
            raise EncryptionError(
                f"Unexpected error during set_dias_api_key: {e}"
            ) from e
        self._dias_api_key = encrypted_value

    def get_dias_api_key(self):
        if not self.has_dias_api_key:
            return None
        try:
            return decrypt(self._dias_api_key)
        except Exception as e:
            raise EncryptionError(
                f"Unexpected error during get_dias_api_key: {e}"
            ) from e
