from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, String, Text
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import StatusProcessLeadData
from strandproperties.models.base import BaseModel


class RawLeadData(BaseModel):
    """
    Raw lead data model table schema definition
    """

    __tablename__ = "raw_lead_data"

    source_lead_id: Mapped[str] = mapped_column(String(100), nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(
        String(200, collation="utf8mb4_bin"), nullable=True
    )
    email: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    phone: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    source: Mapped[Optional[str]] = mapped_column(String(100), nullable=False)
    campaign_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    status: Mapped[Optional[StatusProcessLeadData]] = mapped_column(
        Text, default=StatusProcessLeadData.UNPROCESSED
    )
    contact_id = Column(ForeignKey("contact.id"), nullable=True)
    sale_activity_id = Column(ForeignKey("lead.id"), nullable=True)
    property_reference: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    contact = relationship("Contact", back_populates="raw_lead_datas")
    sale_activity = relationship("Lead", back_populates="raw_lead_data")
    content: Mapped[Optional[JSON]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    is_manual_assign: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    is_new_contact: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    realtor_email: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    organization: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
