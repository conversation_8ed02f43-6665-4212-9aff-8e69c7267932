from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>n,
    DateTime,
    Foreign<PERSON>ey,
    String,
    UniqueConstraint,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.schemas.contact_marketing_consent import NewsletterType


class ContactMarketingConsentStatus(BaseModel):
    """
    Stores the latest known subscription state per contact and newsletter.
    """

    __tablename__ = "contact_marketing_consent_status"

    contact_id: Mapped[int] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )
    newsletter_type: Mapped[NewsletterType] = mapped_column(String(64), nullable=False)
    is_subscribed: Mapped[bool] = mapped_column(Boolean, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    contact = relationship("Contact", back_populates="marketing_consents")

    __table_args__ = (
        UniqueConstraint("contact_id", "newsletter_type", name="uq_contact_newsletter"),
    )

    def __repr__(self):
        return f"<ContactMarketingConsentStatus(contact_id={self.contact_id}, newsletter_type={self.newsletter_type}, is_subscribed={self.is_subscribed})>"
