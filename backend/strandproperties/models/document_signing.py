from typing import List, Optional
from datetime import datetime

from sqlalchemy import Column, String, Integer, Foreign<PERSON>ey, JSON, DateTime, and_
from sqlalchemy.orm import Mapped, mapped_column, relationship
from strandproperties.constants import (
    DocumentSigningEntityType,
    DocumentStatus,
    DocumentSignatureStatus,
    DocumentSignatureUserType,
    DocumentEventAction,
    DocumentEventStatus,
)
from strandproperties.models.base import BaseModel


class DocumentSigner(BaseModel):
    __tablename__ = "document_signer"

    document_signing_id = Column(
        ForeignKey("document_signing.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_id: Mapped[int] = mapped_column(Integer, nullable=False)
    user_type: Mapped[DocumentSignatureUserType] = mapped_column(
        String(50), nullable=False
    )
    signing_external_id: Mapped[str] = mapped_column(String(200), nullable=True)
    status: Mapped[DocumentSignatureStatus] = mapped_column(String(50), nullable=False)
    signing: Mapped["DocumentSigning"] = relationship(
        "DocumentSigning", back_populates="signers"
    )

    user: Mapped[Optional["User"]] = relationship(
        "User",
        primaryjoin="and_(DocumentSigner.user_id == foreign(User.id), "
        "DocumentSigner.user_type == 'user')",
        viewonly=True,
        lazy="joined",
    )

    contact: Mapped[Optional["Contact"]] = relationship(
        "Contact",
        primaryjoin="and_(DocumentSigner.user_id == foreign(Contact.id), "
        "DocumentSigner.user_type == 'contact')",
        viewonly=True,
        lazy="joined",
    )


class DocumentEvent(BaseModel):
    __tablename__ = "document_event"

    document_signing_id = Column(
        ForeignKey("document_signing.id", ondelete="CASCADE"),
        nullable=False,
    )
    action: Mapped[DocumentEventAction] = mapped_column(String(50), nullable=False)
    status: Mapped[DocumentEventStatus] = mapped_column(String(50), nullable=False)
    raw_payload = Column(JSON, nullable=False)
    # TODO: might want to add user ids

    signing: Mapped["DocumentSigning"] = relationship(
        "DocumentSigning", back_populates="events"
    )


class DocumentSigningEntity(BaseModel):
    __tablename__ = "document_signing_entity"

    document_signing_id = Column(
        ForeignKey("document_signing.id", ondelete="CASCADE"),
        nullable=False,
    )
    entity_id: Mapped[int] = mapped_column(Integer, nullable=False)
    entity_type: Mapped[DocumentSigningEntityType] = mapped_column(
        String(50), nullable=False
    )

    document_signing = relationship("DocumentSigning", back_populates="entities")


class DocumentSigning(BaseModel):
    __tablename__ = "document_signing"

    entities = relationship(
        "DocumentSigningEntity",
        back_populates="document_signing",
        cascade="all, delete-orphan",
    )

    document_external_id: Mapped[str] = mapped_column(String(200), nullable=True)
    status: Mapped[DocumentStatus] = mapped_column(String(50), nullable=False)
    deadline: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    signers: Mapped[List[DocumentSigner]] = relationship(
        "DocumentSigner", back_populates="signing", cascade="all, delete-orphan"
    )
    events: Mapped[List[DocumentEvent]] = relationship(
        "DocumentEvent", back_populates="signing", cascade="all, delete-orphan"
    )

    # sales_agreement = relationship(
    #     "FISalesAgreement",
    #     primaryjoin="and_(DocumentSigning.entities.any(FISalesAgreement.id), DocumentSigning.entity_type == 'sales_agreement')",
    #     viewonly=True,
    # )


def get_document_signing_where_entity(
    entity_id: str, entity_type: DocumentSigningEntityType
):
    return DocumentSigning.entities.any(
        and_(
            DocumentSigningEntity.entity_id == entity_id,
            DocumentSigningEntity.entity_type == entity_type,
        )
    )
