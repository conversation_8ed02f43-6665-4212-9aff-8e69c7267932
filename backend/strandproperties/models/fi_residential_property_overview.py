from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import <PERSON><PERSON><PERSON><PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIResidentialTypeCodeEnum,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_residential_property import (
    FIHousingCompanyParkingSpace,
    FIPropertyInspections,
    FIResidentialPropertyApartment,
)
from strandproperties.schemas.fi_property.fi_residential_share import (
    FIOwnershipTypeCodeEnum,
)


class FIResidentialPropertyOverview(BaseModel):
    __tablename__ = "fi_residential_property_overview"

    residential_type_code: Mapped[Optional[FIResidentialTypeCodeEnum]] = mapped_column(
        String(50), nullable=True, index=True
    )
    ownership_type_code: Mapped[Optional[FIOwnershipTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )
    construction_permit_grant_year: Mapped[Optional[Integer]] = mapped_column(
        Integer, nullable=True
    )
    construction_start_year: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )
    construction_end_year: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    usage_start_year: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    development_phase_code: Mapped[Optional[FIResidentialTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )
    property_inspections: Mapped[Optional[FIPropertyInspections]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    construction_permit_usage_purpose: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    apartment: Mapped[Optional[FIResidentialPropertyApartment]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    parking_spaces: Mapped[Optional[list[FIHousingCompanyParkingSpace]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_residential_property_overview"
    )
