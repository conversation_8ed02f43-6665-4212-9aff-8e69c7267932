from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (
    JSO<PERSON>,
    BigInteger,
    Boolean,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
    event,
)
from sqlalchemy.ext.mutable import MutableDict, MutableList
from sqlalchemy.orm import Mapped, Session, mapped_column, relationship

from strandproperties.constants import ContactRelatedPartyType, CountryCode
from strandproperties.models.base import BaseModel
from strandproperties.models.details_of_sale import DetailsOfSale
from strandproperties.models.document import Document
from strandproperties.models.event import Event
from strandproperties.models.offer import Offer
from strandproperties.models.organization import Organization
from strandproperties.models.property import Property, PropertyType
from strandproperties.models.tag import Tag
from strandproperties.models.user import User
from strandproperties.schemas.contact import ContactRelatedPartyRead
from strandproperties.views.exceptions import ApiError

if TYPE_CHECKING:
    from strandproperties.models.group import Group
    from strandproperties.models.lead import Lead
    from strandproperties.models.match_making import MatchMaking


class Contact(BaseModel):
    __tablename__ = "contact"
    __table_args__ = (
        Index(
            "ix_contact_fulltext",
            "name",
            "email",
            "first_name",
            "last_name",
            mysql_prefix="FULLTEXT",
        ),
    )

    name: Mapped[Optional[str]] = mapped_column(String(255))
    email: Mapped[Optional[str]] = mapped_column(String(255))
    phones: Mapped[List["ContactPhone"]] = relationship(back_populates="contact")
    company: Mapped[Optional[str]] = mapped_column(Text)
    website: Mapped[Optional[str]] = mapped_column(Text)
    address: Mapped[Optional[str]] = mapped_column(Text)
    city: Mapped[Optional[str]] = mapped_column(Text)
    post_code: Mapped[Optional[str]] = mapped_column(Text)
    country: Mapped[Optional[str]] = mapped_column(Text)
    preferred_language: Mapped[Optional[str]] = mapped_column(Text)
    passport_number: Mapped[Optional[str]] = mapped_column(Text)
    nationality: Mapped[Optional[str]] = mapped_column(Text)
    source: Mapped[Optional[str]] = mapped_column(Text)
    notes: Mapped[Optional[str]] = mapped_column(Text)
    country_specific = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True, default={}
    )
    hs_object_id: Mapped[Optional[str]] = mapped_column(Text)
    status = None  # TODO: for future use

    social_security_number: Mapped[Optional[str]] = mapped_column(Text)
    type: Mapped[Optional[str]] = mapped_column(Text)
    business_id: Mapped[Optional[str]] = mapped_column(Text)
    vat_number: Mapped[Optional[str]] = mapped_column(Text)
    iban: Mapped[Optional[str]] = mapped_column(Text)
    bic: Mapped[Optional[str]] = mapped_column(Text)
    bank_id: Mapped[int] = mapped_column(
        ForeignKey("bank.id"), nullable=True, index=True
    )
    bank = relationship("Bank")

    legacy_id: Mapped[Optional[int]] = mapped_column(Integer)
    legacy_data: Mapped[Optional[str]] = mapped_column(Text)

    areas: Mapped[Optional[List[str]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True, default=list
    )
    min_bedrooms: Mapped[Optional[int]] = mapped_column(Integer)
    min_price: Mapped[Optional[int]] = mapped_column(Integer)
    max_price: Mapped[Optional[int]] = mapped_column(Integer)

    assigned_to_users: Mapped[List["User"]] = relationship(
        "User", secondary="contact_realtor", back_populates="_contacts"
    )

    related_parties_assoc: Mapped[List["ContactRelatedParty"]] = relationship(
        "ContactRelatedParty",
        foreign_keys="[ContactRelatedParty.contact_id]",
        back_populates="contact",
        cascade="all, delete-orphan",
    )

    @property
    def related_parties(self) -> List["ContactRelatedPartyRead"]:
        return [
            ContactRelatedPartyRead(
                id=related_party.related_party_id,
                name=related_party.related_party.name,
                email=related_party.related_party.email,
                phone_numbers=related_party.related_party.phone_numbers,
                contact_type=related_party.related_party.type,
                type=related_party.related_party_type,
            )
            for related_party in self.related_parties_assoc
        ]

    documents: Mapped[List["Document"]] = relationship(
        "Document", secondary="contact_document", back_populates="contacts"
    )
    buyer_offers: Mapped[Optional[List["Offer"]]] = relationship(
        "Offer", secondary="buyer_offer", back_populates="buyers"
    )
    seller_offers: Mapped[Optional[List["Offer"]]] = relationship(
        "Offer", secondary="seller_offer", back_populates="sellers"
    )
    buyer_details_of_sales: Mapped[Optional[List["DetailsOfSale"]]] = relationship(
        "DetailsOfSale", secondary="buyer_details_of_sale", back_populates="buyers"
    )
    properties: Mapped[List["Property"]] = relationship(
        "Property", secondary="property_contact", back_populates="contacts"
    )
    tags: Mapped[List["Tag"]] = relationship(
        "Tag", secondary="contact_tag", back_populates="contacts"
    )
    leads: Mapped[List["Lead"]] = relationship(
        "Lead", secondary="lead_contact", back_populates="contacts"
    )
    events: Mapped[List["Event"]] = relationship(
        "Event", secondary="event_contact", back_populates="contacts"
    )
    match_makings: Mapped[List["MatchMaking"]] = relationship(
        "MatchMaking", secondary="match_making_contact", back_populates="contacts"
    )
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("organization.id"), nullable=False, index=True
    )
    groups: Mapped[List["Group"]] = relationship(
        "Group", secondary="group_contact", back_populates="contacts"
    )
    raw_lead_datas = relationship("RawLeadData", back_populates="contact")

    property_types: Mapped[List["PropertyType"]] = relationship(
        "PropertyType", secondary="buyer_property_type", back_populates="buyers"
    )
    subscription_id: Mapped[Optional[str]] = mapped_column(Text)

    # New finnish organization properties
    first_name: Mapped[Optional[str]] = mapped_column(String(50))
    last_name: Mapped[Optional[str]] = mapped_column(String(50))
    date_of_birth: Mapped[Optional[str]] = mapped_column(String(50))
    confidential_customer: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    consent_email_marketing: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    consent_newsletter_marketing: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )

    marketing_consents = relationship(
        "ContactMarketingConsentStatus",
        back_populates="contact",
        cascade="all, delete-orphan",
    )

    @property
    def phone_numbers(self) -> List[str]:
        return [p.phone_number for p in self.phones]


class ContactPhone(BaseModel):
    __tablename__ = "contact_phone"

    __table_args__ = (
        Index(
            "ix_contact_phone_fulltext",
            "phone_number",
            mysql_prefix="FULLTEXT",
        ),
    )

    phone_number: Mapped[str] = mapped_column(String(50), nullable=False)
    contact_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("contact.id"), nullable=False
    )
    contact: Mapped["Contact"] = relationship(back_populates="phones")


class ContactRealtor(BaseModel):
    __tablename__ = "contact_realtor"
    __table_args__ = (
        UniqueConstraint("contact_id", "realtor_id", name="uq_contact_realtor"),
        Index("ix_contact_realtor_contact", "contact_id"),
        Index("ix_contact_realtor_realtor", "realtor_id"),
    )

    contact_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("contact.id"), nullable=False
    )
    realtor_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("user.id"), nullable=False
    )


class ContactRelatedParty(BaseModel):
    __tablename__ = "contact_related_party"

    contact_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )
    related_party_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )
    related_party_type: Mapped[ContactRelatedPartyType] = mapped_column(
        String(50), nullable=False
    )

    contact: Mapped["Contact"] = relationship(
        "Contact", foreign_keys=[contact_id], back_populates="related_parties_assoc"
    )
    related_party: Mapped["Contact"] = relationship(
        "Contact", foreign_keys=[related_party_id]
    )


@event.listens_for(Contact, "before_insert")
def validate_spanish_contact(mapper, connection, target: Contact):
    # Create a session from the connection to run lookups
    session = Session(bind=connection)

    # Get the org to check country_code
    org = session.query(Organization).get(target.organization_id)

    if org and org.country_code == CountryCode.SPAIN:
        # Enforce email uniqueness inside Spanish org
        if target.email:
            exists = (
                session.query(Contact)
                .filter_by(
                    organization_id=target.organization_id,
                    email=target.email,
                )
                .first()
            )

            if exists:
                raise ApiError(
                    status=409,
                    message="Email already exists in this Spanish organization",
                )


@event.listens_for(Contact, "before_update")
def validate_spanish_contact_update(mapper, connection, target: Contact):
    session = Session(bind=connection)

    org = session.query(Organization).get(target.organization_id)

    if org and org.country_code == CountryCode.SPAIN:
        if target.email:
            exists = (
                session.query(Contact)
                .filter(
                    Contact.organization_id == target.organization_id,
                    Contact.email == target.email,
                    Contact.id != target.id,  # ignore self
                )
                .first()
            )

            if exists:
                raise ApiError(
                    status=409,
                    message="Email already exists in this Spanish organization",
                )
