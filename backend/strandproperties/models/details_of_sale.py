from datetime import datetime
from typing import Optional

from sqlalchemy import (
    JSON,
    BigInteger,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Numeric,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import expression

from strandproperties.constants import DoSCommissionType, DoSDepositAccountType
from strandproperties.models.base import BaseModel


class DetailsOfSale(BaseModel):
    __tablename__ = "details_of_sale"

    document_id: Mapped[int] = Column(
        BigInteger, ForeignKey("document.id", ondelete="CASCADE"), nullable=False
    )
    document: Mapped[str] = relationship(
        "Document", uselist=False, back_populates="details_of_sale"
    )
    property_id: Mapped[int] = Column(
        BigInteger, ForeignKey("property.id", ondelete="CASCADE"), nullable=True
    )
    property = relationship("Property", back_populates="details_of_sales")
    custom_reference_property = mapped_column(String(50), nullable=True)
    offer_agreed_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    sale_price: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2)
    )
    deposit_percentage: Mapped[Optional[Float]] = mapped_column(Float, nullable=True)
    deposit_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    deposit_account_type: Mapped[Optional[DoSDepositAccountType]] = mapped_column(
        String(50), nullable=True
    )
    deposit_paid_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    ppc_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completion_notary_deadline: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    notary_day_booked: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    total_commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2)
    )
    total_commission_type: Mapped[Optional[DoSCommissionType]] = mapped_column(
        String(50)
    )
    strand_commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2)
    )
    strand_commission_type: Mapped[Optional[DoSCommissionType]] = mapped_column(
        String(50)
    )
    other_agency_name: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    other_agency_commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    other_agency_commission_type: Mapped[Optional[DoSCommissionType]] = mapped_column(
        String(50), nullable=True
    )
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    external_lead: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    external_lead_percentage: Mapped[Optional[Float]] = mapped_column(
        Float, nullable=True
    )
    separate_invoice_for_each_seller: Mapped[Boolean] = mapped_column(
        Boolean, server_default=expression.true()
    )
    metadata_json = Column(
        MutableDict.as_mutable(JSON),
        nullable=False,
        default={},
    )
    reviewer_office_id: Mapped[int] = Column(
        BigInteger, ForeignKey("office.id", ondelete="CASCADE"), nullable=True
    )
    review_office = relationship(
        "Office", uselist=False, back_populates="details_of_sales"
    )
    sellers = relationship(
        "SellerDetailsOfSale",
        back_populates="details_of_sale",
    )
    buyers = relationship(
        "Contact",
        secondary="buyer_details_of_sale",
        back_populates="buyer_details_of_sales",
    )
    realtors = relationship(
        "RealtorDetailsOfSale",
        back_populates="details_of_sale",
    )
    details_of_sale_invoices = relationship(
        "DetailsOfSaleInvoice", back_populates="details_of_sale"
    )
    created_by = Column(ForeignKey("user.id"), nullable=False)

    offer_id: Mapped[int] = Column(
        BigInteger, ForeignKey("offer.id", ondelete="RESTRICT"), nullable=True
    )
    offer = relationship("Offer", uselist=False, back_populates="details_of_sale")
    created_user = relationship("User", foreign_keys=[created_by])


class SellerDetailsOfSale(BaseModel):
    __tablename__ = "seller_details_of_sale"
    __table_args__ = (UniqueConstraint("details_of_sale_id", "seller_id"),)

    details_of_sale_id: Mapped[int] = Column(ForeignKey(DetailsOfSale.id), index=True)
    details_of_sale = relationship("DetailsOfSale", back_populates="sellers")
    seller_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    seller = relationship("Contact", foreign_keys=[seller_id])
    invoice_percentage: Mapped[Optional[Numeric]] = mapped_column(Float, nullable=True)
    agent_id: Mapped[int] = Column(ForeignKey("user.id"), nullable=True)
    details_of_sale_invoice_id: Mapped[int] = Column(
        ForeignKey("details_of_sale_invoice.id"), nullable=True
    )
    details_of_sale_invoice = relationship(
        "DetailsOfSaleInvoice", back_populates="sellers"
    )


class BuyerDetailsOfSale(BaseModel):
    __tablename__ = "buyer_details_of_sale"
    __table_args__ = (UniqueConstraint("details_of_sale_id", "buyer_id"),)

    details_of_sale_id: Mapped[int] = Column(ForeignKey(DetailsOfSale.id), index=True)

    buyer_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    buyer = relationship("Contact", foreign_keys=[buyer_id], viewonly=True)


class RealtorDetailsOfSale(BaseModel):
    __tablename__ = "realtor_details_of_sale"

    details_of_sale_id: Mapped[int] = Column(ForeignKey(DetailsOfSale.id), index=True)
    details_of_sale = relationship("DetailsOfSale", back_populates="realtors")
    user_id: Mapped[int] = Column(ForeignKey("user.id"), index=True)
    user = relationship("User", foreign_keys=[user_id])
    commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    commission_percentage: Mapped[Optional[Float]] = mapped_column(Float, nullable=True)
    document_id: Mapped[int] = Column(
        BigInteger, ForeignKey("document.id"), nullable=True
    )
    document: Mapped[str] = relationship(
        "Document", uselist=False, back_populates="realtor_details_of_sales"
    )
    document_attachments = relationship(
        "DocumentAttachments", foreign_keys="DocumentAttachments.realtor_id"
    )


class DetailsOfSaleInvoice(BaseModel):
    __tablename__ = "details_of_sale_invoice"

    issued_by = Column(BigInteger, ForeignKey("office.id"), nullable=True)
    invoice_number: Mapped[str] = mapped_column(Text, nullable=True, unique=True)
    details_of_sale_id: Mapped[int] = Column(
        ForeignKey(DetailsOfSale.id), nullable=False, index=True
    )
    details_of_sale = relationship(
        "DetailsOfSale", back_populates="details_of_sale_invoices"
    )
    invoice_date: Mapped[datetime] = Column(DateTime, nullable=True)
    invoice_due_date: Mapped[datetime] = Column(DateTime, nullable=True)
    document_id: Mapped[int] = Column(
        BigInteger, ForeignKey("document.id"), nullable=True
    )
    document: Mapped["Document"] = relationship(
        "Document",
        uselist=False,
        foreign_keys=[document_id],
        back_populates="details_of_sale_invoices",
    )
    proforma_id: Mapped[int] = Column(
        BigInteger, ForeignKey("document.id"), nullable=True
    )
    proforma: Mapped["Document"] = relationship(
        "Document",
        uselist=False,
        foreign_keys=[proforma_id],
        back_populates="dos_proforma_invoices",
    )
    sellers = relationship(
        "SellerDetailsOfSale",
        back_populates="details_of_sale_invoice",
    )
    agent_id: Mapped[int] = Column(ForeignKey("user.id"), nullable=True)
    agent = relationship("User", foreign_keys=[agent_id], viewonly=True)
