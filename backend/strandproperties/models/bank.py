from strandproperties.models.base import BaseModel
from sqlalchemy import Text
from sqlalchemy.orm import Mapped, mapped_column


class Bank(BaseModel):
    __tablename__ = "bank"

    name: Mapped[str] = mapped_column(Text, nullable=False, unique=True)
    business_id: Mapped[str] = mapped_column(Text)
    group_name: Mapped[str] = mapped_column(Text)
    group_business_id: Mapped[str] = mapped_column(Text)
