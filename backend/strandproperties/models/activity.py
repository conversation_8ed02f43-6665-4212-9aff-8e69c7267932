from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Index, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Activity(BaseModel):
    """
    Activity model table schema definition
    """

    __tablename__ = "activity"

    description: Mapped[dict] = mapped_column(JSON, nullable=False)
    object_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    object_id: Mapped[int] = mapped_column(Integer, nullable=False)
    actor_id: Mapped[int] = mapped_column(
        ForeignKey("user.id"), nullable=True, index=True
    )

    actor = relationship("User", back_populates="activities")

    __table_args__ = (Index("idx_object_type_object_id", "object_type", "object_id"),)

    def __repr__(self):
        return f"<Activity(description={self.description}, object_type={self.object_type}, object_id={self.object_id}, actor_id={self.actor_id})>"
