from typing import List

from sqlalchemy import Column, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Office(BaseModel):
    __tablename__ = "office"

    name: Mapped[str] = mapped_column(Text, nullable=False, unique=True)

    users = relationship("User", secondary="user_office", back_populates="offices")
    details_of_sales: Mapped[List["DetailsOfSale"]] = relationship(
        "DetailsOfSale", back_populates="review_office"
    )
    sales_agreements = relationship("SalesAgreement", back_populates="office")
    offers = relationship("Offer", back_populates="office")


class UserOffice(BaseModel):
    __tablename__ = "user_office"
    __table_args__ = (UniqueConstraint("user_id", "office_id", name="uq_user_office"),)

    user_id = Column(ForeignKey("user.id"), nullable=False, index=True)
    office_id = Column(ForeignKey("office.id"), nullable=False, index=True)
