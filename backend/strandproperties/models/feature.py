from sqlalchemy import Column, ForeignKey, Text, UniqueConstraint
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Feature(BaseModel):
    __tablename__ = "feature"

    name: Mapped[str] = mapped_column(Text, nullable=False)

    feature_group_id = Column(
        ForeignKey("feature_group.id"),
        nullable=False,
    )
    feature_group = relationship(
        "FeatureGroup", foreign_keys=[feature_group_id], back_populates="features"
    )

    properties = relationship(
        "Property", secondary="property_feature", back_populates="features"
    )

    @hybrid_property
    def feature_group_name(self) -> str | None:
        if self.feature_group is None:
            return None
        return self.feature_group.name


class FeatureGroup(BaseModel):
    __tablename__ = "feature_group"

    name = Column(Text, nullable=False)

    features = relationship(
        "Feature",
        primaryjoin="foreign(Feature.feature_group_id) == FeatureGroup.id",
        back_populates="feature_group",
    )


class PropertyFeature(BaseModel):
    __tablename__ = "property_feature"
    __table_args__ = (
        UniqueConstraint("property_id", "feature_id", name="uq_property_feature"),
    )

    feature_id = Column(
        ForeignKey("feature.id"),
        nullable=False,
    )

    property_id = Column(
        ForeignKey("property_spain.id"),
        nullable=False,
    )
