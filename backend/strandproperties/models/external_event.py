from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, String
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column

from strandproperties.constants import ExternalEventSource

from .base import BaseModel


class ExternalEvent(BaseModel):
    """
    ExternalEvent model table schema definition
    """

    __tablename__ = "external_event"

    event_uuid: Mapped[str] = mapped_column(String(36), nullable=False, unique=True)
    event_source: Mapped[ExternalEventSource] = mapped_column(
        String(50), nullable=False
    )
    data: Mapped[Optional[dict]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
