from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Image(BaseModel):
    __tablename__ = "image"

    url: Mapped[str] = mapped_column(String(3000), nullable=False)
    order: Mapped[Optional[int]] = mapped_column(Integer)
    is_hidden: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    property_id = Column(
        ForeignKey("property.id"),
        nullable=False,
    )
    property = relationship("Property", back_populates="images")
