from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import Mu<PERSON>D<PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FIRedemption,
    FIResidentialTypeCodeEnum,
    FIShareCertificate,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_residential_share import (
    FIApartmentOverview,
    FIOwnershipTypeCodeEnum,
    FIResidentialShareParkingSpace,
    FIResidentialShareStorage,
)


class FIResidentialShareOverview(BaseModel):
    __tablename__ = "fi_residential_share_overview"

    residential_type_code: Mapped[Optional[FIResidentialTypeCodeEnum]] = mapped_column(
        String(50), nullable=True, index=True
    )
    ownership_type_code: Mapped[Optional[FIOwnershipTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )
    administration: Mapped[Optional[FIAdministration]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    apartment: Mapped[Optional[FIApartmentOverview]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, index=True
    )
    debt_share_amount: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    debt_share_additional_info: Mapped[Optional[list[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    currency_code: Mapped[Optional[str]] = mapped_column(String(3), nullable=True)
    starting_debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )
    storages: Mapped[Optional[List[FIResidentialShareStorage]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    limits_of_storage_usage: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    parking_spaces: Mapped[Optional[List[FIResidentialShareParkingSpace]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    parking_space_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    share_certificate: Mapped[Optional[FIShareCertificate]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    redemption: Mapped[Optional[FIRedemption]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    more_information_about_the_materials: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    more_information_about_the_premises: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )

    # Relationships
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_residential_share_overview"
    )
