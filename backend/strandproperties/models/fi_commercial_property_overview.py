from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import MutableD<PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_commercial import (
    FIAdditionalServicesEnum,
    FICommercialPropertyTypeCodeEnum,
    FICommercialSpace,
    FISustainableDevelopment,
    FITypicalUsesEnum,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FICommonArea,
    FIRedemption,
    FIShareCertificate,
)


class FICommercialPropertyOverview(BaseModel):
    __tablename__ = "fi_commercial_property_overview"

    commercial_property_type_code: Mapped[FICommercialPropertyTypeCodeEnum] = (
        mapped_column(String(100), nullable=False, index=True)
    )
    typical_uses: Mapped[List[FITypicalUsesEnum]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    administration: Mapped[Optional[FIAdministration]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    floor_area: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    total_area: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, index=True
    )
    debt_share_amount: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    starting_debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )
    currency_code: Mapped[Optional[str]] = mapped_column(String(3), nullable=True)
    share_certificate: Mapped[Optional[FIShareCertificate]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    redemption: Mapped[Optional[FIRedemption]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    sustainable_development: Mapped[Optional[FISustainableDevelopment]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    additional_services: Mapped[List[FIAdditionalServicesEnum]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    spaces: Mapped[List[FICommercialSpace]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )

    # Relationships
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_commercial_property_overview"
    )
