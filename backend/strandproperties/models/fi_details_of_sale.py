from datetime import date
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>olean, Column, Date
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import (
    Float,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.schemas.fi_details_of_sale import (
    FIDetailsOfSaleExpense,
    FIDetailsOfSaleLeadBasis,
    FIDetailsOfSaleOwnershipType,
    FIDetailsOfSaleRecipientRole,
    FIDetailsOfSaleRecipientType,
    FIDetailsOfSaleStatus,
    FIDetailsOfSaleTransactionMethod,
)


class FIDetailsOfSale(BaseModel):
    __tablename__ = "fi_details_of_sale"

    property_id = Column(
        ForeignKey("fi_property.id", ondelete="CASCADE"),
        nullable=False,
    )
    status: Mapped[FIDetailsOfSaleStatus] = mapped_column(
        SQLEnum(FIDetailsOfSaleStatus),
        nullable=False,
        default=FIDetailsOfSaleStatus.DRAFT,
    )

    property = relationship("FIProperty", foreign_keys=[property_id], viewonly=True)

    ownership_type: Mapped[Optional[FIDetailsOfSaleOwnershipType]] = mapped_column(
        SQLEnum(FIDetailsOfSaleOwnershipType), nullable=True
    )
    transaction_method: Mapped[Optional[FIDetailsOfSaleTransactionMethod]] = (
        mapped_column(SQLEnum(FIDetailsOfSaleTransactionMethod), nullable=True)
    )
    property_published_at: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    estimated_transaction_date: Mapped[Optional[date]] = mapped_column(
        Date, nullable=True
    )
    sale_duration_days: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    offer_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    highest_rejected_offer: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    sale_price: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    debt: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    debt_free_price: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    mortgage_bank: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    sale_final_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    commission_amount_total: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    commission_percent: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_vat_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    commission_vat_included: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    commission_amount_without_vat: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    commission_vat_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    commission_amount_with_vat: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    strand_commission_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )

    sellers = relationship("FISellerDetailsOfSale", back_populates="details_of_sale")
    buyers = relationship("FIBuyerDetailsOfSale", back_populates="details_of_sale")
    recipients = relationship(
        "FIRecipientDetailsOfSale", back_populates="details_of_sale"
    )
    leads = relationship("FILeadDetailsOfSale", back_populates="details_of_sale")

    expenses: Mapped[List[FIDetailsOfSaleExpense]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True, default=list
    )


class FISellerDetailsOfSale(BaseModel):
    __tablename__ = "fi_seller_details_of_sale"
    __table_args__ = (UniqueConstraint("fi_details_of_sale_id", "seller_id"),)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    ownership_share_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="sellers")

    seller_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    seller = relationship("Contact", foreign_keys=[seller_id], viewonly=True)


class FIBuyerDetailsOfSale(BaseModel):
    __tablename__ = "fi_buyer_details_of_sale"
    __table_args__ = (UniqueConstraint("fi_details_of_sale_id", "buyer_id"),)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    buyer_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    buyer = relationship("Contact", foreign_keys=[buyer_id], viewonly=True)
    ownership_share_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="buyers")


class FIRecipientDetailsOfSale(BaseModel):
    __tablename__ = "fi_recipient_details_of_sale"
    user_id: Mapped[int] = Column(ForeignKey("user.id"), index=True)
    user = relationship("User", foreign_keys=[user_id], viewonly=True)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    role: Mapped[FIDetailsOfSaleRecipientRole] = mapped_column(
        SQLEnum(FIDetailsOfSaleRecipientRole), nullable=False
    )
    commission_percent: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_vat_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    type: Mapped[FIDetailsOfSaleRecipientType] = mapped_column(
        SQLEnum(FIDetailsOfSaleRecipientType), nullable=False
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="recipients")


class FILeadDetailsOfSale(BaseModel):
    __tablename__ = "fi_lead_details_of_sale"
    user_id: Mapped[int] = Column(ForeignKey("user.id"), index=True)
    user = relationship("User", foreign_keys=[user_id], viewonly=True)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    commission_percent: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_vat_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    lead_basis: Mapped[Optional[FIDetailsOfSaleLeadBasis]] = mapped_column(
        SQLEnum(FIDetailsOfSaleLeadBasis), nullable=True
    )
    commission_amount: Mapped[Optional[Numeric]] = mapped_column(
        Numeric(precision=14, scale=2), nullable=True
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="leads")
