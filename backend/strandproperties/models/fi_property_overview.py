from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>an, String
from sqlalchemy.ext.mutable import <PERSON><PERSON><PERSON><PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FITelevision,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_property_details import (
    FIGroundArea,
    FIIslandProperty,
    FIPropertyBuilding,
    FIPropertyConnections,
    FIPropertyEncumbrance,
    FIPropertyInternetConnections,
    FIPropertyStorage,
    FIRealEstateTypeCodeEnum,
    FIRenovationGrant,
    FISewageDisposal,
    FIWaterSupply,
)


class FIPropertyOverview(BaseModel):
    __tablename__ = "fi_property_overview"

    real_estate_type_code: Mapped[Optional[FIRealEstateTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )
    property_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    property_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    floor_plan: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_property_overview"
    )
    has_possession_or_divide_agreement: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    estate_has_residence: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    electricity_consumption_description: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    internet_connections: Mapped[Optional[FIPropertyInternetConnections]] = (
        mapped_column(MutableDict.as_mutable(JSON), nullable=True)
    )
    television: Mapped[Optional[FITelevision]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    restriction_for_use_and_assignment: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    restriction_for_use_and_assignment_description: Mapped[
        Optional[List[FITranslatedText]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    water_supply: Mapped[Optional[FIWaterSupply]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    sewage_disposal: Mapped[Optional[FISewageDisposal]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    renovation_grant: Mapped[Optional[FIRenovationGrant]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    road_available_till_property: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    road_access_rights_and_restrictions: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    property_on_island: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    island_property: Mapped[Optional[FIIslandProperty]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    property_connections: Mapped[Optional[FIPropertyConnections]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    yard_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    view_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    ground_area: Mapped[Optional[FIGroundArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    buildings: Mapped[Optional[List[FIPropertyBuilding]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    storages: Mapped[Optional[List[FIPropertyStorage]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    encumbrance: Mapped[Optional[FIPropertyEncumbrance]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
