from sqlalchemy import Column, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import relationship

from strandproperties.models.base import BaseModel


class GarageType(BaseModel):
    __tablename__ = "garage_type"

    name = Column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_garage_type", back_populates="garage_types"
    )


class PropertyGarageType(BaseModel):
    __tablename__ = "property_garage_type"
    __table_args__ = (
        UniqueConstraint(
            "garage_type_id", "property_id", name="uq_garagetype_property"
        ),
        Index("ix_property_garagetype_garagetype", "garage_type_id"),
        Index("ix_property_garagetype_property", "property_id"),
    )

    garage_type_id = Column(ForeignKey("garage_type.id"), nullable=False)
    property_id = Column(ForeignKey("property_spain.id"), nullable=False)
