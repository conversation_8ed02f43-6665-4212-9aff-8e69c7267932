from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.orm import Mapped, backref, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.document_library import DocumentLibraryItem


class KiviDocumentLink(BaseModel):
    __tablename__ = "kivi_document_link"
    __table_args__ = (
        UniqueConstraint(
            "kivi_assignment_id", "kivi_document_id", name="uq_kivi_document_id"
        ),
    )

    kivi_assignment_id: Mapped[int] = mapped_column(Integer(), nullable=False)
    kivi_document_id: Mapped[int] = mapped_column(Integer(), nullable=False)
    kivi_document_type: Mapped[str] = mapped_column(String(100), nullable=True)

    document_library_item_id: Mapped[int] = mapped_column(
        BigInteger(),
        ForeignKey("document_library_item.id", ondelete="CASCADE"),
        nullable=False,
    )
    document_library_item: Mapped["DocumentLibraryItem"] = relationship(
        "DocumentLibraryItem",
        backref=backref("kivi_document_link", passive_deletes=True),
        lazy="selectin",
    )
