from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import Mu<PERSON><PERSON><PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FICommonArea,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_estate import (
    FIEstateArea,
    FIEstateLandArea,
    FIEstatePropertyTypeCodeEnum,
    FIForestStand,
    FIPartitionTypeCodeEnum,
)


class FIEstateOverview(BaseModel):
    __tablename__ = "fi_estate_overview"

    estate_property_type_code: Mapped[FIEstatePropertyTypeCodeEnum] = mapped_column(
        String(50), nullable=False, index=True
    )
    areas: Mapped[Optional[List[FIEstateArea]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    partition_type_code: Mapped[Optional[FIPartitionTypeCode<PERSON>num]] = mapped_column(
        String(50), nullable=True
    )
    partition_min_size: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    forest_land_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    immediate_forest_logging_possibilities_in_cubic_meters: Mapped[Optional[int]] = (
        mapped_column(Integer, nullable=True)
    )
    forest_logging_potential_description: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    arable_land_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    forest_stand: Mapped[Optional[List[FIForestStand]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    land_areas: Mapped[Optional[List[FIEstateLandArea]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )

    # Relationships
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_estate_overview"
    )
