from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>ger, DateTime
from sqlalchemy.orm import Mapped, RelationshipProperty, mapped_column
from tet.sqlalchemy.simple import declarative_base

from strandproperties.libs.utils import import_all

Base = declarative_base()


class BaseModel(Base):
    __abstract__ = True
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    created_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        default=datetime.utcnow,
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
    )


def _scan_models(path: str):
    if not path:
        raise ValueError("path can't be empty")

    imported = import_all(path)
    if imported == 0:
        raise ImportError(f"No module can be found under {path}")


def get_model_class_by_table_name(table_name: str):
    for cls in Base.registry._class_registry.values():
        if hasattr(cls, "__tablename__") and cls.__tablename__ == table_name:
            return cls
    return None


def is_many_to_many(r: RelationshipProperty) -> bool:
    return r.back_populates is not None and r.secondary is not None


def is_one_to_many(r: RelationshipProperty) -> bool:
    return r.back_populates is not None and r.secondary is None
