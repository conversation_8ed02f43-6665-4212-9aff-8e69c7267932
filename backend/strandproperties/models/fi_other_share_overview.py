from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import Mu<PERSON><PERSON><PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FICommonArea,
    FIRedemption,
    FIShareCertificate,
)
from strandproperties.schemas.fi_property.fi_other_share import FIOtherShareTypeCodeEnum


class FIOtherShareOverview(BaseModel):
    __tablename__ = "fi_other_share_overview"

    is_auction_listing: Mapped[Optional[bool]] = mapped_column(<PERSON><PERSON><PERSON>, nullable=True)
    other_share_type_code: Mapped[Optional[FIOtherShareTypeCodeEnum]] = mapped_column(
        String(50), nullable=True, index=True
    )
    administration: Mapped[Optional[FIAdministration]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    area_basis_code: Mapped[Optional[List[str]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    floor_area: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    total_area: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, index=True
    )
    debt_share_amount: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    starting_debt_free_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )
    currency_code: Mapped[Optional[str]] = mapped_column(String(3), nullable=True)
    share_certificate: Mapped[Optional[FIShareCertificate]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    redemption: Mapped[Optional[FIRedemption]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )

    # Relationships
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_other_share_overview"
    )
