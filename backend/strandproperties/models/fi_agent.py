from strandproperties.models.base import BaseModel

from typing import List, TYPE_CHECKING

from sqlalchemy import String, ForeignKey, UniqueConstraint

from sqlalchemy.orm import Mapped, mapped_column, relationship
from strandproperties.schemas.fi_property.fi_realty import (
    FIRoleCodeEnum,
    FIStatusCodeEnum,
)

if TYPE_CHECKING:
    from strandproperties.models.fi_property import FIRealty
    from strandproperties.models.user import User


class FIAgent(BaseModel):
    __tablename__ = "fi_agent"

    role_code: Mapped[FIRoleCodeEnum] = mapped_column(String(50), nullable=False)
    status_code: Mapped[FIStatusCodeEnum] = mapped_column(String(50), nullable=False)

    realtor_id: Mapped[int] = mapped_column(
        ForeignKey("user.id"), nullable=False, index=True
    )

    realties: Mapped[List["FIRealty"]] = relationship(
        "FIRealty", secondary="fi_realty_agent", back_populates="agents"
    )

    _realtor: Mapped["User"] = relationship(
        "User", primaryjoin="foreign(FIAgent.realtor_id) == User.id"
    )


class FIRealtyAgent(BaseModel):
    __tablename__ = "fi_realty_agent"
    __table_args__ = (
        UniqueConstraint("realty_id", "agent_id", name="uq_fi_realty_agent"),
    )

    realty_id: Mapped[int] = mapped_column(
        ForeignKey("fi_realty.id"), nullable=False, index=True
    )

    agent_id: Mapped[int] = mapped_column(
        ForeignKey("fi_agent.id"), nullable=False, index=True
    )
