from typing import List

from sqlalchemy import Column, ForeignKey, Index, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class AreaLevel1(BaseModel):
    __tablename__ = "area_level_1"
    name: Mapped[str] = mapped_column(Text, nullable=False)
    internal_name: Mapped[str] = mapped_column(Text, nullable=True)

    # enum Country
    country: Mapped[str] = mapped_column(Text, nullable=False)
    province: Mapped[str] = mapped_column(Text, nullable=False)

    es_custom_area_group_2_id = Column(
        ForeignKey("es_custom_area_group_2.id"), nullable=True
    )

    area_level_2: Mapped[List["AreaLevel2"]] = relationship(
        "AreaLevel2", back_populates="area_level_1"
    )

    es_custom_area_group_2: Mapped["ESCustomAreaGroup2"] = relationship(
        "ESCustomAreaGroup2"
    )

    Index("ix_name_fulltext", name, mariadb_prefix="FULLTEXT")

    def get_name(self):
        if self.internal_name:
            return self.internal_name
        return self.name

    def to_dict(self, include_sub_areas=True, include_parent_area=False):
        d = {
            "id": self.id,
            "name": get_flatten_area_name(self),
            "level": 1,
        }

        if include_parent_area:
            d["area_groups"] = get_flatten_area_groups(self)

        if include_sub_areas:
            d["subareas"] = [area.to_dict() for area in self.area_level_2]

        return d


class AreaLevel2(BaseModel):
    __tablename__ = "area_level_2"
    name: Mapped[str] = mapped_column(Text, nullable=False)
    internal_name: Mapped[str] = mapped_column(Text, nullable=True)

    area_level_1_id = Column(ForeignKey("area_level_1.id"))

    area_level_1: Mapped[List["AreaLevel1"]] = relationship(
        "AreaLevel1", back_populates="area_level_2"
    )
    area_level_3: Mapped[List["AreaLevel3"]] = relationship(
        "AreaLevel3", back_populates="area_level_2"
    )

    Index("ix_name_fulltext", name, mariadb_prefix="FULLTEXT")

    def get_name(self):
        if self.internal_name:
            return self.internal_name
        return self.name

    def to_dict(self, include_sub_areas=True, include_parent_area=False):
        d = {
            "id": self.id,
            "level": 2,
        }

        if include_parent_area:
            d["name"] = get_flatten_area_name(self)
            d["area_groups"] = get_flatten_area_groups(self)
        else:
            d["name"] = self.name

        if include_sub_areas:
            d["subareas"] = [area.to_dict() for area in self.area_level_3]

        return d


class AreaLevel3(BaseModel):
    __tablename__ = "area_level_3"
    name: Mapped[str] = mapped_column(Text, nullable=False)
    internal_name: Mapped[str] = mapped_column(Text, nullable=True)

    area_level_2_id = Column(ForeignKey("area_level_2.id"))

    area_level_2: Mapped[List["AreaLevel2"]] = relationship(
        "AreaLevel2", back_populates="area_level_3"
    )

    area_level_4: Mapped[List["AreaLevel4"]] = relationship(
        "AreaLevel4", back_populates="area_level_3"
    )

    Index("ix_name_fulltext", name, mariadb_prefix="FULLTEXT")

    def get_name(self):
        if self.internal_name:
            return self.internal_name
        return self.name

    def to_dict(self, include_sub_areas=True, include_parent_area=False):
        d = {
            "id": self.id,
            "level": 3,
        }

        if include_parent_area:
            d["name"] = get_flatten_area_name(self)
            d["area_groups"] = get_flatten_area_groups(self)
        else:
            d["name"] = self.name

        if include_sub_areas:
            d["subareas"] = [area.to_dict() for area in self.area_level_4]

        return d


class AreaLevel4(BaseModel):
    __tablename__ = "area_level_4"
    name: Mapped[str] = mapped_column(Text, nullable=False)

    area_level_3_id = Column(ForeignKey("area_level_3.id"))

    area_level_3: Mapped[List["AreaLevel3"]] = relationship("AreaLevel3")
    area_level_5: Mapped[List["AreaLevel5"]] = relationship(
        "AreaLevel5", back_populates="area_level_4"
    )

    Index("ix_name_fulltext", name, mariadb_prefix="FULLTEXT")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "level": 4,
            "subareas": [area.to_dict() for area in self.area_level_5],
        }


class AreaLevel5(BaseModel):
    __tablename__ = "area_level_5"
    name: Mapped[str] = mapped_column(Text, nullable=False)

    area_level_4_id = Column(ForeignKey("area_level_4.id"))

    area_level_4: Mapped[List["AreaLevel4"]] = relationship(
        "AreaLevel4", back_populates="area_level_5"
    )

    Index("ix_name_fulltext", name, mariadb_prefix="FULLTEXT")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "level": 5,
        }


class ESCustomAreaGroup1(BaseModel):
    __tablename__ = "es_custom_area_group_1"

    name: Mapped[str] = mapped_column(Text, nullable=False, index=True)

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "group_level": 1,
        }


class ESCustomAreaGroup2(BaseModel):
    __tablename__ = "es_custom_area_group_2"

    name: Mapped[str] = mapped_column(Text, nullable=False, index=True)

    es_custom_area_group_1_id = Column(ForeignKey("es_custom_area_group_1.id"))

    es_custom_area_group_1: Mapped["ESCustomAreaGroup1"] = relationship(
        "ESCustomAreaGroup1"
    )

    def get_name(self):
        if self.es_custom_area_group_1:
            return f"{self.name}, {self.es_custom_area_group_1.name}"
        return self.name

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.get_name(),
            "group_level": 2,
        }


def get_flatten_area_name(obj: AreaLevel1 | AreaLevel2 | AreaLevel3) -> str:
    """Flatten area name to include parent areas' names"""
    if isinstance(obj, AreaLevel1):
        return obj.get_name()
    elif isinstance(obj, AreaLevel2):
        return f"{obj.get_name()}, {obj.area_level_1.name}"
    elif isinstance(obj, AreaLevel3):
        return f"{obj.get_name()}, {obj.area_level_2.name}, {obj.area_level_2.area_level_1.name}"
    else:
        raise ValueError(f"Invalid object type: {type(obj)}")


def get_flatten_area_groups(obj: AreaLevel1 | AreaLevel2 | AreaLevel3) -> str:
    """Flatten area group to include parent groups' names"""
    if isinstance(obj, AreaLevel1):
        if not obj.es_custom_area_group_2:
            return ""
        else:
            return obj.es_custom_area_group_2.get_name()

    elif isinstance(obj, AreaLevel2):
        return get_flatten_area_groups(obj.area_level_1)
    elif isinstance(obj, AreaLevel3):
        return get_flatten_area_groups(obj.area_level_2.area_level_1)
    else:
        raise ValueError(f"Invalid object type: {type(obj)}")
