from sqlalchemy import Column, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Setting(BaseModel):
    __tablename__ = "setting"

    name: Mapped[str] = mapped_column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_setting", back_populates="settings"
    )


class PropertySetting(BaseModel):
    __tablename__ = "property_setting"
    __table_args__ = (
        UniqueConstraint("property_id", "setting_id", name="uq_property_setting"),
    )

    setting_id = Column(
        ForeignKey("setting.id"),
        nullable=False,
    )

    property_id = Column(
        ForeignKey("property_spain.id"),
        nullable=False,
    )
