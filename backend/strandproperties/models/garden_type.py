from sqlalchemy import Column, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import relationship

from strandproperties.models.base import BaseModel


class GardenType(BaseModel):
    __tablename__ = "garden_type"

    name = Column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_garden_type", back_populates="garden_types"
    )


class PropertyGardenType(BaseModel):
    __tablename__ = "property_garden_type"
    __table_args__ = (
        UniqueConstraint(
            "garden_type_id", "property_id", name="uq_gardentype_property"
        ),
        Index("ix_property_gardentype_gardentype", "garden_type_id"),
        Index("ix_property_gardentype_property", "property_id"),
    )

    garden_type_id = Column(ForeignKey("garden_type.id"), nullable=False)
    property_id = Column(ForeignKey("property_spain.id"), nullable=False)
