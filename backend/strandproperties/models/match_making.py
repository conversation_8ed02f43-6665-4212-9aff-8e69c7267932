from datetime import datetime
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.contact import Contact
from strandproperties.models.property import Property
from strandproperties.models.user import User


class MatchMaking(BaseModel):
    """
    MatchMaking model table schema definition
    """

    __tablename__ = "match_making"
    title: Mapped[str] = mapped_column(Text, nullable=False)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    params: Mapped[str] = mapped_column(Text, nullable=False)
    lead_id: Mapped[Optional[int]] = mapped_column(ForeignKey("lead.id"), nullable=True)
    email_template_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("email_template.id"), nullable=True
    )
    assigned_to_users: Mapped[List["User"]] = relationship(
        "User", secondary="match_making_user", back_populates="match_makings"
    )
    contacts: Mapped[List["Contact"]] = relationship(
        "Contact", secondary="match_making_contact", back_populates="match_makings"
    )
    properties: Mapped[List["Property"]] = relationship(
        "Property", secondary="match_making_property", back_populates="match_makings"
    )
    lead = relationship("Lead", back_populates="match_makings")
    email_template = relationship("EmailTemplate", back_populates="match_makings")
    is_auto_sent: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    last_sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)


class MatchMakingUser(BaseModel):
    """
    MatchMakingUser model table schema definition
    """

    __tablename__ = "match_making_user"
    __table_args__ = (
        UniqueConstraint("match_making_id", "user_id", name="uq_match_making_user"),
    )
    match_making_id = mapped_column(ForeignKey("match_making.id"), nullable=False)
    user_id = mapped_column(ForeignKey("user.id"), nullable=False)


class MatchMakingContact(BaseModel):
    """
    MatchMakingContact model table schema definition
    """

    __tablename__ = "match_making_contact"
    __table_args__ = (
        UniqueConstraint(
            "match_making_id", "contact_id", name="uq_match_making_contact"
        ),
    )
    match_making_id = mapped_column(ForeignKey("match_making.id"), nullable=False)
    contact_id = mapped_column(ForeignKey("contact.id"), nullable=False)


class MatchMakingProperty(BaseModel):
    """
    MatchMakingProperty model table schema definition
    """

    __tablename__ = "match_making_property"
    __table_args__ = (
        UniqueConstraint(
            "match_making_id", "property_id", name="uq_match_making_property"
        ),
    )
    is_favorite: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    match_making_id = mapped_column(ForeignKey("match_making.id"), nullable=False)
    property_id = mapped_column(ForeignKey("property_spain.id"), nullable=False)


class EmailTemplate(BaseModel):
    """
    EmailTemplate model table schema definition
    """

    __tablename__ = "email_template"
    __table_args__ = (
        UniqueConstraint(
            "uuid", name="uq_email_template_uuid"
        ),  # Unique constraint added
    )
    version_tag: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    subject: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    uuid: Mapped[str] = mapped_column(Text, nullable=False)
    match_makings = relationship("MatchMaking", back_populates="email_template")
    name: Mapped[str] = mapped_column(Text, nullable=True)


class MatchMakingVersion(BaseModel):
    """
    MatchMakingVersion model table schema definition
    """

    __tablename__ = "match_making_version"
    match_making_id = mapped_column(ForeignKey("match_making.id"), nullable=False)
    version: Mapped[str] = mapped_column(Text, nullable=False)
    click: Mapped[bool] = mapped_column(Boolean, nullable=False)


class MatchMakingTrack(BaseModel):
    """
    MatchMakingTrack model table schema definition
    """

    __tablename__ = "match_making_track"
    __table_args__ = (
        UniqueConstraint(
            "match_making_id",
            "property_id",
            "realtor_id",
            name="uq_match_making_track",
        ),
    )
    match_making_id = mapped_column(ForeignKey("match_making.id"), nullable=False)
    property_id = mapped_column(ForeignKey("property_spain.id"), nullable=False)
    realtor_id = mapped_column(ForeignKey("user.id"), nullable=False)
    click_count: Mapped[int] = mapped_column(BigInteger)


class MatchMakingPropertyContact(BaseModel):
    """
    MatchMakingPropertyContact model table schema definition
    """

    __tablename__ = "match_making_property_contact"
    __table_args__ = (
        UniqueConstraint(
            "property_id", "contact_id", name="uq_match_making_property_id_contact_id"
        ),
    )
    property_id = mapped_column(ForeignKey("property_spain.id"), nullable=False)
    contact_id = mapped_column(ForeignKey("contact.id"), nullable=False)
    is_sent: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=False)
    email_id: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
