from sqlalchemy import <PERSON><PERSON><PERSON>ger, Foreign<PERSON>ey, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class File(BaseModel):
    __tablename__ = "file"
    __table_args__ = (
        UniqueConstraint("property_id", "key", name="uq_file_property_id_key"),
    )

    key: Mapped[str] = mapped_column(String(255), nullable=False)

    property_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("property.id"), nullable=False
    )
    property = relationship("Property", back_populates="files")
