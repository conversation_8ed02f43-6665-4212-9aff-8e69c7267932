from typing import List, Optional

from sqlalchemy import ForeignKey, Index, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import Language
from strandproperties.models.base import BaseModel
from strandproperties.models.contact import Contact
from strandproperties.models.tag import Tag
from strandproperties.models.user import User


class Group(BaseModel):
    """
    Group model table schema definition
    """

    __tablename__ = "group"

    assigned_to_users: Mapped[List["User"]] = relationship(
        "User", secondary="group_user", back_populates="groups"
    )
    contacts: Mapped[List["Contact"]] = relationship(
        "Contact", secondary="group_contact", back_populates="groups"
    )
    name: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    language: Mapped[Optional[Language]] = mapped_column(String(30), nullable=True)
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("organization.id"), nullable=False, index=True
    )
    tags: Mapped[List["Tag"]] = relationship(
        "Tag", secondary="group_tag", back_populates="groups"
    )


class GroupUser(BaseModel):
    """
    GroupUser model table schema definition
    """

    __tablename__ = "group_user"
    __table_args__ = (
        UniqueConstraint("group_id", "user_id", name="uq_group_user"),
        Index("ix_group_user_group", "group_id"),
        Index("ix_group_user_user", "user_id"),
    )
    group_id = mapped_column("group_id", ForeignKey("group.id"), nullable=False)
    user_id = mapped_column("user_id", ForeignKey("user.id"), nullable=False)


class GroupContact(BaseModel):
    """
    GroupContact model table schema definition
    """

    __tablename__ = "group_contact"
    __table_args__ = (
        UniqueConstraint("group_id", "contact_id", name="uq_group_contact"),
        Index("ix_group_contact_group", "group_id"),
        Index("ix_group_contact_contact", "contact_id"),
    )
    group_id = mapped_column("group_id", ForeignKey("group.id"), nullable=False)
    contact_id = mapped_column("contact_id", ForeignKey("contact.id"), nullable=False)
