from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    <PERSON>SO<PERSON>,
    Boolean,
    DateTime,
    Float,
    Integer,
    String,
    Text,
    Column,
    BigInteger,
    ForeignKey,
    UniqueConstraint,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.mutable import MutableList, MutableDict
from strandproperties.models.base import BaseModel
from strandproperties.models.user import User
from strandproperties.schemas.dias_common import (
    Attachment,
    AttachmentDiasField,
    AttachmentStatus,
    BankAccount,
    DiasBuyer,
    DiasSeller,
    DiasWebhookEvent,
    InitiatorContactInfo,
    InternalBuyer,
    InternalRealtor,
    InternalSeller,
    InvoiceVerificationType,
)
from strandproperties.schemas.dias_property_trade import (
    Consenter,
    ConfigTerm,
    DeedType,
    DownPayment,
    AmountInCents,
    TermsOfTransfer,
    TransferNotification,
    TransferObject,
    TransferredShares,
)
from strandproperties.schemas.dias_shared_trade import (
    Apartment,
    AttachmentSharedTrade,
    BuyersMortgageStatus,
    Event,
    ShareCertificateStatus,
    Step,
    TradeState,
)

if TYPE_CHECKING:
    from strandproperties.models.fi_property import FIProperty


class DiasAttachment(BaseModel):
    __tablename__ = "dias_attachment"

    ALLOWED_EXTENSIONS = [".pdf"]

    status: Mapped[AttachmentStatus] = mapped_column(
        String(20), nullable=True, default=AttachmentStatus.ACTIVE
    )
    # S3 key for the file, in case we change the structure of the file, we still know where to find it
    s3_key: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)

    # For example: attachments, billOfSale, TransferTaxReceipt etc
    dias_field: Mapped[AttachmentDiasField] = mapped_column(String(200), nullable=False)
    file_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    # Dias_id is only available when we upload a file or fetch from DIAS API
    dias_id: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # AttachmentTypeSharedTrade or AttachmentTypePropertyTrade or None for receipts, bills of sale etc
    document_type: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    shared_trades: Mapped[List["SharedTrade"]] = relationship(
        "SharedTrade",
        secondary="dias_shared_trade_attachment",
        back_populates="dias_attachments",
    )

    property_reference: Mapped[str] = mapped_column(String(200), nullable=True)

    created_by_id: Mapped[Optional[int]] = Column(ForeignKey("user.id"), nullable=False)
    created_by: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[created_by_id], backref="created_dias_attachments"
    )


class PropertyTradeDraft(BaseModel):
    __tablename__ = "dias_property_trade_draft"

    # Required Fields
    transferred_shares: Mapped[List[TransferredShares]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    attachments: Mapped[List[Attachment]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    initiator_contact_info: Mapped[InitiatorContactInfo] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=False
    )
    consenters: Mapped[List[Consenter]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    deed_type: Mapped[DeedType] = mapped_column(Text)
    terms_of_transfer: Mapped[List[TermsOfTransfer]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    transfer_object: Mapped[TransferObject] = mapped_column(Text)
    sellers: Mapped[List[DiasSeller]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    buyers: Mapped[List[DiasBuyer]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=False
    )
    purchase_price: Mapped[AmountInCents] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=False
    )
    initiator_person_id: Mapped[str] = mapped_column(Text)
    trade_initiated_timestamp: Mapped[str] = mapped_column(Text)
    initiator_trade_reference_id: Mapped[str] = mapped_column(Text)

    # Optional Fields
    # laitostunnus
    institution_code: Mapped[Optional[str]] = mapped_column(Text)

    # kiinteistotunnus
    real_estate_code: Mapped[Optional[str]] = mapped_column(Text)
    mandatory_deed_terms: Mapped[Optional[List[ConfigTerm]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    transfer_notification: Mapped[Optional[TransferNotification]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    pledge_codes: Mapped[Optional[List[str]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    realtor_bank_account: Mapped[Optional[BankAccount]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    non_transferable_pledge_codes: Mapped[Optional[List[str]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    penalty_for_non_payment: Mapped[Optional[AmountInCents]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    down_payment: Mapped[Optional[DownPayment]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )


# We only use KiinteistokauppaDraft when we communicate with DIAS API but internally, we use PropertyTrade
# TODO : Implement Adapter to convert KiinteistokauppaDraft to PropertyTrade and vice versa
# class KiinteistokauppaDraft(BaseModel):
#     __tablename__ = "kiinteistokauppa_draft"

#     # Required Fields
#     luovutettavatOsuudet: Mapped[List[LuovutettavatOsuudet]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     attachments: Mapped[List[Attachment]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     initiatorContactInfo: Mapped[InitiatorContactInfo] = mapped_column(
#         MutableDict.as_mutable(JSON), nullable=False
#     )
#     suostujat: Mapped[List[Suostujat]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     luovutuskirjanTyyppi: Mapped[LuovutuskirjanTyyppi] = mapped_column(Text)
#     luovutuksenEhdot: Mapped[List[LuovutuksenEhdot]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     luovutuksenKohde: Mapped[LuovutuksenKohde] = mapped_column(Text)
#     sellers: Mapped[List[DiasSeller]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     buyers: Mapped[List[DiasBuyer]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=False
#     )
#     kauppahinta: Mapped[AmountInCents] = mapped_column(
#         MutableDict.as_mutable(JSON), nullable=False
#     )
#     initiatorPersonId: Mapped[str] = mapped_column(Text)
#     tradeInitiatedTimestamp: Mapped[str] = mapped_column(Text)
#     initiatorTradeReferenceId: Mapped[str] = mapped_column(Text)

#     # Optional Fields
#     laitostunnus: Mapped[Optional[str]] = mapped_column(Text)
#     kiinteistotunnus: Mapped[Optional[str]] = mapped_column(Text)
#     luovutuskirjanPakollisetEhdot: Mapped[Optional[List[ConfigTerm]]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=True
#     )
#     luovutusilmoitus: Mapped[Optional[Luovutusilmoitus]] = mapped_column(
#         MutableDict.as_mutable(JSON), nullable=True
#     )
#     panttikirjojenKoodit: Mapped[Optional[List[str]]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=True
#     )
#     realtorBankAccount: Mapped[Optional[BankAccount]] = mapped_column(
#         MutableDict.as_mutable(JSON), nullable=True
#     )
#     eiSiirrettavatPanttikirjojenKoodit: Mapped[Optional[List[str]]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=True
#     )
#     sopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana: Mapped[
#         Optional[AmountInCents]
#     ] = mapped_column(MutableDict.as_mutable(JSON), nullable=True)
#     kasiraha: Mapped[Optional[Kasiraha]] = mapped_column(
#         MutableList.as_mutable(JSON), nullable=True
#     )
#     # TODO: Add more fields
#     # events  # This field only appears in GET /api/v1/kiinteistokauppa/draft/{trade-id} not in POST


class SharedTrade(BaseModel):
    __tablename__ = "dias_shared_trade"

    # Required Fields from DIAS, Optional Fields from Strand.
    attachments: Mapped[Optional[List[AttachmentSharedTrade]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    bill_of_sale: Mapped[Optional[Attachment]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    realtor_bank_account: Mapped[Optional[BankAccount]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    sellers: Mapped[Optional[List[DiasSeller]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    buyers: Mapped[Optional[List[DiasBuyer]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    initiator_contact_info: Mapped[Optional[InitiatorContactInfo]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    require_initiator_confirmation: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    initiator_person_id: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    apartment: Mapped[Optional[Apartment]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    trade_initiated_timestamp: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    initiator_trade_reference_id: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )

    # Optional Fields
    deadline_for_signing_bill_of_sale: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    seller_share_certificate_status: Mapped[Optional[ShareCertificateStatus]] = (
        mapped_column(String(100), nullable=True)
    )
    buyers_mortgage_status: Mapped[Optional[BuyersMortgageStatus]] = mapped_column(
        String(50), nullable=True
    )

    # Below fields are not submitted DIAS API when creating a shared trade draft, only for Strand BE

    # When shared trade is initiated, the trade_id is no more None
    trade_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # when was the last time we fetched the from paper-trade/{trade_id} from DIAS
    dias_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    trade_state: Mapped[Optional[TradeState]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    events: Mapped[Optional[List[Event]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    ownership_transfer_power_of_attorney: Mapped[Optional[Attachment]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )

    fi_property_id: Mapped[Optional[int]] = Column(
        BigInteger, ForeignKey("fi_property.id", ondelete="SET NULL"), nullable=True
    )
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", back_populates="shared_trades"
    )

    initiator_id: Mapped[Optional[int]] = Column(ForeignKey("user.id"), nullable=True)
    initiator: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[initiator_id], backref="initiated_shared_trades"
    )
    created_by_id: Mapped[Optional[int]] = Column(ForeignKey("user.id"), nullable=False)
    created_by: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[created_by_id], backref="created_shared_trades"
    )

    dias_attachments: Mapped[List["DiasAttachment"]] = relationship(
        "DiasAttachment",
        secondary="dias_shared_trade_attachment",
        back_populates="shared_trades",
    )

    latest_webhook_event: Mapped[Optional[DiasWebhookEvent]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )

    # Internal Fields: # To store some extra info that is not sent to DIAS but is needed for internal processing like in Detail View and in Edit Modal in FE
    internal_sellers: Mapped[Optional[List[InternalSeller]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    internal_buyers: Mapped[Optional[List[InternalBuyer]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    internal_realtor: Mapped[Optional[InternalRealtor]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    include_commission: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    realtor_sum_commission: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    invoice_verification_type: Mapped[Optional[InvoiceVerificationType]] = (
        mapped_column(String(100), nullable=True)
    )
    invoice_verification_value: Mapped[Optional[str]] = mapped_column(
        String(
            250
        ),  # Note: DIAS docs has no info about the length for reference number or message
        nullable=True,
    )
    current_step: Mapped[Step] = mapped_column(
        Integer, nullable=False, default=Step.PREPARATION
    )


class SharedTradeAttachment(BaseModel):
    __tablename__ = "dias_shared_trade_attachment"
    __table_args__ = (
        UniqueConstraint(
            "shared_trade_id", "attachment_id", name="uq_dias_shared_trade_attachment"
        ),
    )

    shared_trade_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("dias_shared_trade.id"), nullable=False
    )
    attachment_id: Mapped[BigInteger] = mapped_column(
        ForeignKey("dias_attachment.id"), nullable=False  # Update this line
    )
