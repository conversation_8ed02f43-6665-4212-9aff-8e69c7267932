from sqlalchemy import Column, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class View(BaseModel):
    __tablename__ = "view"

    name: Mapped[str] = mapped_column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_view", back_populates="views"
    )


class PropertyView(BaseModel):
    __tablename__ = "property_view"
    __table_args__ = (
        UniqueConstraint("property_id", "view_id", name="uq_property_view"),
    )

    view_id = Column(
        ForeignKey("view.id"),
        nullable=False,
    )

    property_id = Column(
        ForeignKey("property_spain.id"),
        nullable=False,
    )
