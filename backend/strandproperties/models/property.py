from datetime import datetime
from typing import TYPE_CHECKING, Any, List, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    TEX<PERSON>,
    BigInteger,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
    event,
)
from sqlalchemy.dialects.mysql import LONGTEXT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import (
    DEFAULT_CURRENCY,
    DEFAULT_STATUS,
    DescriptionType,
    Language,
    SoldBy,
)
from strandproperties.models.area_levels import (
    AreaLevel1,
    AreaLevel2,
    AreaLevel3,
    AreaLevel4,
    AreaLevel5,
)
from strandproperties.models.base import BaseModel
from strandproperties.models.details_of_sale import DetailsOfSale
from strandproperties.models.event import Event
from strandproperties.models.feature import Feature
from strandproperties.models.file import File
from strandproperties.models.garage_type import GarageType
from strandproperties.models.garden_type import GardenType
from strandproperties.models.image import Image
from strandproperties.models.listing_type import ListingType
from strandproperties.models.offer import Offer
from strandproperties.models.orientation import Orientation
from strandproperties.models.pool_type import PoolType
from strandproperties.models.user import User
from strandproperties.models.video import Stream, Tour
from strandproperties.schemas.property import TelecommunicationSystems

if TYPE_CHECKING:
    from strandproperties.models.match_making import MatchMaking


class PropertyBase(BaseModel):
    __tablename__ = "property"
    market = mapped_column(String(50), nullable=False)
    reference: Mapped[str] = mapped_column(
        String(20), nullable=False, unique=True, index=True
    )
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("organization.id"), nullable=False, index=True
    )

    data_source: Mapped[Optional[str]] = mapped_column(String(100), index=True)
    is_exclusive: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, index=True
    )
    is_strandified: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, index=True
    )

    created_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        default=datetime.utcnow,
        index=True,
    )

    images: Mapped[List["Image"]] = relationship(
        "Image", back_populates="property", cascade="all, delete-orphan"
    )
    contacts = relationship(
        "Contact", secondary="property_contact", back_populates="properties"
    )
    realtor_users: Mapped[List["User"]] = relationship(
        "User",
        secondary="property_realtor",
        back_populates="properties",
        order_by="asc(PropertyRealtor.created_at)",
    )
    descriptions: Mapped[List["PropertyDescription"]] = relationship(
        "PropertyDescription", back_populates="property", cascade="all, delete-orphan"
    )
    events: Mapped[List["Event"]] = relationship(
        "Event", secondary="event_property", back_populates="properties"
    )
    details_of_sales: Mapped[List["DetailsOfSale"]] = relationship(
        "DetailsOfSale", back_populates="property", cascade="all, delete-orphan"
    )

    @hybrid_property
    def sorted_visible_images(self) -> List["Image"]:
        return sorted(
            filter(lambda i: not i.is_hidden, self.images), key=lambda i: i.order or 0
        )

    @hybrid_property
    def main_img(self) -> Optional[str]:
        return next(iter(map(lambda i: i.url, self.sorted_visible_images)), None)

    __mapper_args__ = {
        "polymorphic_identity": "base",
        "polymorphic_on": market,
    }


@event.listens_for(PropertyBase, "before_update", propagate=True)
def receive_before_update(mapper, connection, target: PropertyBase):
    target.updated_at = datetime.utcnow()


class Property(PropertyBase):
    __tablename__ = "property_spain"
    __mapper_args__ = {
        "polymorphic_identity": "spain",
    }

    id: Mapped[int] = mapped_column(ForeignKey("property.id"), primary_key=True)

    idealista_code: Mapped[Optional[str]] = mapped_column(String(20), unique=True)

    title: Mapped[Optional[str]] = mapped_column(TEXT)
    slug: Mapped[Optional[str]] = mapped_column(TEXT)

    imported_data = Column(JSON)

    area_level_1_id: Mapped[Optional[BigInteger]] = mapped_column(
        ForeignKey("area_level_1.id")
    )
    area_level_2_id: Mapped[Optional[BigInteger]] = mapped_column(
        ForeignKey("area_level_2.id")
    )
    area_level_3_id: Mapped[Optional[BigInteger]] = mapped_column(
        ForeignKey("area_level_3.id")
    )
    area_level_4_id: Mapped[Optional[BigInteger]] = mapped_column(
        ForeignKey("area_level_4.id")
    )
    area_level_5_id: Mapped[Optional[BigInteger]] = mapped_column(
        ForeignKey("area_level_5.id")
    )

    property_type_id = Column(ForeignKey("property_type.id"), nullable=False)

    bedrooms: Mapped[Optional[int]] = mapped_column(Integer, index=True)
    bathrooms: Mapped[Optional[int]] = mapped_column(Integer, index=True)

    pax: Mapped[Optional[int]] = mapped_column(Integer)

    price_sale_old: Mapped[Optional[int]] = mapped_column(Integer)
    price_rent_short_term_old: Mapped[Optional[int]] = mapped_column(
        Integer
    )  # per month
    price_rent_long_term_old: Mapped[Optional[int]] = mapped_column(
        Integer
    )  # per month
    price_sale: Mapped[Optional[int]] = mapped_column(Integer, index=True)
    price_rent_short_term: Mapped[Optional[int]] = mapped_column(Integer, index=True)
    price_rent_long_term: Mapped[Optional[int]] = mapped_column(Integer, index=True)

    price_square_meter: Mapped[Optional[int]] = mapped_column(Integer)
    currency: Mapped[Optional[str]] = mapped_column(
        String(20), default=DEFAULT_CURRENCY
    )

    built_year: Mapped[Optional[int]] = mapped_column(Integer)
    built_area: Mapped[Optional[int]] = mapped_column(Integer)

    interior_area: Mapped[Optional[int]] = mapped_column(Integer)
    plot_area: Mapped[Optional[int]] = mapped_column(Integer)
    terrace_area: Mapped[Optional[int]] = mapped_column(Integer)

    levels = Column(Integer)
    floor: Mapped[Optional[int]] = mapped_column(Integer)

    status = Column(String(20), nullable=False, default=DEFAULT_STATUS, index=True)

    commission: Mapped[Optional[float]] = mapped_column(Float)
    commission_notes: Mapped[Optional[str]] = mapped_column(Text)
    commission_type: Mapped[Optional[str]] = mapped_column(String(20))
    iva_tax: Mapped[Optional[str]] = mapped_column(String(50))

    internal_notes: Mapped[Optional[str]] = mapped_column(Text)

    latitude: Mapped[Optional[float]] = mapped_column(Float)
    longitude: Mapped[Optional[float]] = mapped_column(Float)
    is_public_coordinates: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False
    )

    private_info: Mapped[dict[str, Any]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=False, default={}
    )

    # etuovi, leadingre, idealista, etc
    portals: Mapped[dict[str, bool]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=False, default={}
    )

    # enum ConditionType
    condition: Mapped[Optional[str]] = mapped_column(String(50))

    # move all the legacy fields here
    legacy_data = Column(MutableDict.as_mutable(JSON), nullable=False, default={})

    legal_representative = Column(
        MutableDict.as_mutable(JSON), nullable=True, default={}
    )

    communal_fees: Mapped[Optional[float]] = mapped_column(Float)
    ibi = Column(Float)
    garbage_tax = Column(Float)
    water_fee = Column(Float)
    electricity = Column(Float)

    cadastral_reference: Mapped[Optional[str]] = mapped_column(String(50))

    hostaway_property_id: Mapped[Optional[str]] = mapped_column(String(50))

    building_constructor = Column(String(20))
    total_floors: Mapped[Optional[int]] = mapped_column(Integer)
    building_has_elevator: Mapped[Optional[bool]] = mapped_column(
        Boolean, default=False
    )

    foundation_and_structure = Column(String(50))
    roof = Column(String(50))
    exterior_walls = Column(String(50))

    property_has_certificate: Mapped[Optional[bool]] = mapped_column(
        Boolean, default=False
    )
    # enum EnergyCertificateRating
    certificate_consumption_rating: Mapped[Optional[str]] = mapped_column(String(10))
    certificate_consumption_value: Mapped[Optional[float]] = mapped_column(Float)
    # enum EnergyCertificateRating
    certificate_emission_rating: Mapped[Optional[str]] = mapped_column(String(10))
    certificate_emission_value: Mapped[Optional[float]] = mapped_column(Float)

    rooms_total: Mapped[Optional[int]] = mapped_column(Integer)
    toilets = Column(Integer)
    suite_baths = Column(Integer)

    parking_spaces: Mapped[Optional[int]] = mapped_column(Integer)

    telecommunication_systems: Mapped[Optional[TelecommunicationSystems]] = (
        mapped_column(MutableDict.as_mutable(JSON), nullable=True, default={})
    )

    keys_and_handoff = Column(MutableDict.as_mutable(JSON), nullable=True, default={})

    renovations = Column(MutableDict.as_mutable(JSON), nullable=True, default={})

    damages_and_defects = Column(
        MutableDict.as_mutable(JSON), nullable=True, default={}
    )

    other_damages = Column(MutableDict.as_mutable(JSON), nullable=True, default={})

    sold_by: Mapped[Optional[SoldBy]] = mapped_column(String(20))

    development_name: Mapped[Optional[str]] = mapped_column(Text)

    orientations: Mapped[List["Orientation"]] = relationship(
        "Orientation", secondary="property_orientation", back_populates="properties"
    )

    listing_types: Mapped[List["ListingType"]] = relationship(
        "ListingType", secondary="property_listing_type", back_populates="properties"
    )

    garage_types: Mapped[List["GarageType"]] = relationship(
        "GarageType", secondary="property_garage_type", back_populates="properties"
    )

    pool_types: Mapped[List["PoolType"]] = relationship(
        "PoolType", secondary="property_pool_type", back_populates="properties"
    )

    garden_types: Mapped[List["GardenType"]] = relationship(
        "GardenType", secondary="property_garden_type", back_populates="properties"
    )

    features: Mapped[List["Feature"]] = relationship(
        "Feature", secondary="property_feature", back_populates="properties"
    )

    settings = relationship(
        "Setting", secondary="property_setting", back_populates="properties"
    )

    views = relationship("View", secondary="property_view", back_populates="properties")

    documents = relationship(
        "Document", secondary="property_document", back_populates="properties"
    )

    _property_type: Mapped["PropertyType"] = relationship(
        "PropertyType",
        primaryjoin="foreign(Property.property_type_id) == PropertyType.id",
        uselist=False,
    )

    _area_level_1: Mapped[Optional["AreaLevel1"]] = relationship(
        "AreaLevel1", primaryjoin="foreign(Property.area_level_1_id) == AreaLevel1.id"
    )
    _area_level_2: Mapped[Optional["AreaLevel2"]] = relationship(
        "AreaLevel2", primaryjoin="foreign(Property.area_level_2_id) == AreaLevel2.id"
    )
    _area_level_3: Mapped[Optional["AreaLevel3"]] = relationship(
        "AreaLevel3", primaryjoin="foreign(Property.area_level_3_id) == AreaLevel3.id"
    )
    _area_level_4: Mapped[Optional["AreaLevel4"]] = relationship(
        "AreaLevel4", primaryjoin="foreign(Property.area_level_4_id) == AreaLevel4.id"
    )
    _area_level_5: Mapped[Optional["AreaLevel5"]] = relationship(
        "AreaLevel5", primaryjoin="foreign(Property.area_level_5_id) == AreaLevel5.id"
    )

    files: Mapped[List["File"]] = relationship(
        "File", back_populates="property", cascade="all, delete-orphan"
    )
    offers: Mapped[List["Offer"]] = relationship(
        "Offer", back_populates="property", cascade="all, delete-orphan"
    )
    video_streams: Mapped[List["Stream"]] = relationship(
        "Stream", back_populates="property", cascade="all, delete-orphan"
    )
    match_makings: Mapped[List["MatchMaking"]] = relationship(
        "MatchMaking", secondary="match_making_property", back_populates="properties"
    )

    @hybrid_property
    def visible_video_streams(self):
        return filter(lambda i: not i.is_hidden, self.video_streams)

    video_tours: Mapped[List["Tour"]] = relationship(
        "Tour", back_populates="property", cascade="all, delete-orphan"
    )

    @hybrid_property
    def visible_video_tours(self):
        return filter(lambda i: not i.is_hidden, self.video_tours)

    @hybrid_property
    def property_type(self) -> str:
        return self._property_type.name

    @hybrid_property
    def property_type_origin(self) -> str:
        return self._property_type.origin

    @hybrid_property
    def property_type_category(self) -> str:
        return self._property_type.category

    @hybrid_property
    def country(self) -> str | None:
        if self._area_level_1 is None:
            return None
        return self._area_level_1.country

    @hybrid_property
    def city(self) -> str | None:
        if self._area_level_1 is None:
            return None
        return self._area_level_1.name

    @hybrid_property
    def location_from_areas(self) -> Optional[str]:
        new_location = ""
        if self._area_level_1:
            new_location += f"{self._area_level_1.name}"
        else:
            return None

        if self._area_level_2:
            new_location += f", {self._area_level_2.name}"

        if self._area_level_3:
            new_location += f", {self._area_level_3.name}"

        if self._area_level_4:
            new_location += f", {self._area_level_4.name}"

        if self._area_level_5:
            new_location += f", {self._area_level_5.name}"

        return new_location


class PropertyDescription(BaseModel):
    __tablename__ = "property_description"

    property_id = Column(ForeignKey("property.id"), nullable=False)
    tagline: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[str] = mapped_column(LONGTEXT, nullable=False)
    type: Mapped[DescriptionType] = mapped_column(String(100), nullable=False)
    language: Mapped[Language] = mapped_column(String(100), nullable=False)

    property = relationship("Property", back_populates="descriptions")


class PropertyType(BaseModel):
    __tablename__ = "property_type"

    origin: Mapped[str] = mapped_column(String(100), nullable=False)
    category: Mapped[str] = mapped_column(String(100), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)

    buyers = relationship(
        "Contact", secondary="buyer_property_type", back_populates="property_types"
    )


class PropertyRealtor(BaseModel):
    __tablename__ = "property_realtor"
    __table_args__ = (
        UniqueConstraint(
            "property_id", "user_id", name="uq_property_realtor2_property_id"
        ),
    )

    user_id = Column(ForeignKey("user.id"), nullable=False)

    property_id = Column(
        ForeignKey("property.id"),
        nullable=False,
    )


class PropertyContact(BaseModel):
    __tablename__ = "property_contact"
    __table_args__ = (
        UniqueConstraint("property_id", "contact_id", name="uq_property_contact"),
        Index("ix_property_contact_property", "property_id"),
        Index("ix_property_contact_contact", "contact_id"),
    )

    contact_id = Column(ForeignKey("contact.id"), nullable=False)

    property_id = Column(
        ForeignKey("property.id"),
        nullable=False,
    )


class BuyerPropertyType(BaseModel):
    __tablename__ = "buyer_property_type"
    __table_args__ = (
        UniqueConstraint(
            "property_type_id", "contact_id", name="uq_buyer_property_type"
        ),
        Index("ix_buyer_property_type_property", "property_type_id"),
        Index("ix_buyer_property_type_contact", "contact_id"),
    )

    contact_id = Column(ForeignKey("contact.id"), nullable=False)

    property_type_id = Column(
        ForeignKey("property_type.id"),
        nullable=False,
    )
