from enum import Enum
from typing import List, Optional

from sqlalchemy import <PERSON>ole<PERSON>
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import Foreign<PERSON>ey, Integer, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class BrochureLanguage(str, Enum):
    FI = "fi"
    EN = "en"


class CoverTheme(int, Enum):
    WHITE = 1
    FLOWER = 2
    LIVINGROOM = 3
    LIVINGROOM2 = 4
    OLIVE = 5


class ImagePosition(str, Enum):
    # Full coverage
    FULL_PAGE = "full_page"

    # Halves (50% coverage)
    TOP = "top"
    BOTTOM = "bottom"
    LEFT = "left"
    RIGHT = "right"
    CENTER = "center"  # Blueprint image etc.

    # Quarters (25% coverage)
    TOP_LEFT = "top_left"
    TOP_RIGHT = "top_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"


class Brochure(BaseModel):
    __tablename__ = "brochure"

    property_id = mapped_column(
        ForeignKey("property.id", ondelete="CASCADE"), nullable=False
    )

    realtor_id = mapped_column(ForeignKey("user.id"), nullable=False)

    theme: Mapped[Optional[CoverTheme]] = mapped_column(
        SQLAlchemyEnum(CoverTheme, name="cover_theme_enum"),
        default=CoverTheme.WHITE,
        nullable=False,
    )

    language: Mapped[Optional[BrochureLanguage]] = mapped_column(
        SQLAlchemyEnum(BrochureLanguage, name="language_enum"),
        default=BrochureLanguage.FI,
        nullable=False,
    )

    version_number: Mapped[int] = mapped_column(Integer, nullable=False)

    is_complete: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    property_information_version_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("brochure_information.id"), nullable=True
    )
    property_images_version_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("brochure_images.id"), nullable=True
    )

    __table_args__ = (
        UniqueConstraint(
            "property_id", "version_number", name="uix_brochure_property_version"
        ),
    )


class BrochureImages(BaseModel):
    __tablename__ = "brochure_images"

    pages: Mapped[List["BrochureImagePage"]] = relationship(
        "BrochureImagePage",
        back_populates="brochure_images",
        cascade="all, delete-orphan",
    )


class BrochureInformation(BaseModel):
    __tablename__ = "brochure_information"

    property_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)


class BrochureImagePage(BaseModel):
    __tablename__ = "brochure_image_page"

    brochure_images_id: Mapped[int] = mapped_column(
        ForeignKey("brochure_images.id", ondelete="CASCADE"), nullable=False
    )
    page_number: Mapped[int] = mapped_column(Integer, nullable=False)

    brochure_images: Mapped["BrochureImages"] = relationship(
        "BrochureImages", back_populates="pages"
    )
    images: Mapped[List["BrochureImageWithPosition"]] = relationship(
        "BrochureImageWithPosition", back_populates="page", cascade="all, delete-orphan"
    )


class BrochureImageWithPosition(BaseModel):
    __tablename__ = "brochure_image_with_position"

    page_id: Mapped[int] = mapped_column(
        ForeignKey("brochure_image_page.id", ondelete="CASCADE"), nullable=False
    )
    image_url: Mapped[str] = mapped_column(String(500), nullable=False)
    position: Mapped[ImagePosition] = mapped_column(
        SQLAlchemyEnum(ImagePosition, name="image_position_enum"), nullable=False
    )

    page: Mapped["BrochureImagePage"] = relationship(
        "BrochureImagePage", back_populates="images"
    )
