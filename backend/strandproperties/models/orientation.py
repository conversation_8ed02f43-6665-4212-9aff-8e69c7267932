from sqlalchemy import Column, Foreign<PERSON>ey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Orientation(BaseModel):
    __tablename__ = "orientation"

    name: Mapped[str] = mapped_column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_orientation", back_populates="orientations"
    )


class PropertyOrientation(BaseModel):
    __tablename__ = "property_orientation"
    __table_args__ = (
        UniqueConstraint(
            "property_id", "orientation_id", name="uq_property_orientation"
        ),
    )

    orientation_id = Column(ForeignKey("orientation.id"), nullable=False)

    property_id = Column(ForeignKey("property_spain.id"), nullable=False)
