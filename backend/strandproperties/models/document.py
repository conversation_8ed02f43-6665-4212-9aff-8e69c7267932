from datetime import date
from typing import Optional

from sqlalchemy import (
    Big<PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    ForeignKey,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import AttachmentDocumentType
from strandproperties.models.base import BaseModel
from strandproperties.models.offer import Offer


class Document(BaseModel):
    __tablename__ = "document"

    name = Column(Text, nullable=False)
    type = Column(Text, nullable=False)
    visibility = Column(Text, nullable=False)
    sowise_id = Column(Text, nullable=False)
    status = Column(Text, nullable=False)
    language = Column(Text, nullable=False)
    expires_at = Column(DateTime, nullable=True)
    notary_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    comment: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    properties = relationship(
        "Property", secondary="property_document", back_populates="documents"
    )

    contacts = relationship(
        "Contact", secondary="contact_document", back_populates="documents"
    )

    offer: Mapped[Offer] = relationship(
        "Offer", uselist=False, back_populates="document"
    )

    details_of_sale = relationship(
        "DetailsOfSale",
        uselist=False,
        back_populates="document",
        cascade="all, delete-orphan",
    )

    property_document = relationship(
        "PropertyDocument", uselist=False, back_populates="document", viewonly=True
    )

    document_attachments = relationship(
        "DocumentAttachments",
        back_populates="document",
        cascade="all, delete-orphan",
    )

    details_of_sale_invoices = relationship(
        "DetailsOfSaleInvoice",
        foreign_keys="[DetailsOfSaleInvoice.document_id]",
        back_populates="document",
    )
    dos_proforma_invoices = relationship(
        "DetailsOfSaleInvoice",
        foreign_keys="[DetailsOfSaleInvoice.proforma_id]",
        back_populates="proforma",
    )
    realtor_details_of_sales = relationship(
        "RealtorDetailsOfSale", back_populates="document"
    )


class PropertyDocument(BaseModel):
    __tablename__ = "property_document"
    __table_args__ = (UniqueConstraint("property_id", "document_id"),)

    document_id = Column(ForeignKey("document.id"), nullable=False)
    document = relationship(
        "Document",
        uselist=False,
        back_populates="property_document",
        viewonly=True,
    )

    property_id = Column(
        ForeignKey("property_spain.id"),
        nullable=False,
    )
    property = relationship("Property", foreign_keys=[property_id], viewonly=True)
    sales_agreement = relationship(
        "SalesAgreement",
        uselist=False,
        back_populates="property_document",
        cascade="all, delete-orphan",
        single_parent=True,
    )


class ContactDocument(BaseModel):
    __tablename__ = "contact_document"
    __table_args__ = (UniqueConstraint("contact_id", "document_id"),)

    document_id = Column(ForeignKey("document.id"), nullable=False)

    contact_id = Column(
        ForeignKey("contact.id"),
        nullable=False,
    )


class DocumentAttachments(BaseModel):
    __tablename__ = "document_attachments"

    name = Column(Text, nullable=False)
    type = Column(Text, nullable=False)
    attachment_type = mapped_column(
        String(50), nullable=False, default=AttachmentDocumentType.UNKNOW
    )
    sowise_id = Column(Text, nullable=False)

    document_id: Mapped[int] = Column(
        BigInteger, ForeignKey("document.id", ondelete="CASCADE"), nullable=False
    )
    document: Mapped[str] = relationship(
        "Document", uselist=False, back_populates="document_attachments"
    )
    realtor_id: Mapped[int] = Column(
        BigInteger,
        ForeignKey("realtor_details_of_sale.id", ondelete="CASCADE"),
        nullable=True,
    )
    realtor: Mapped[str] = relationship(
        "RealtorDetailsOfSale", uselist=False, back_populates="document_attachments"
    )
