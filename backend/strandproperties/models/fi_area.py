from sqlalchemy.orm import mapped_column, Mapped
from strandproperties.models.base import BaseModel
from sqlalchemy import (
    BigInteger,
    ForeignKey,
    String,
    UniqueConstraint,
)
from typing import Optional

from strandproperties.schemas.fi_property.fi_area import FIAreaKindEnum
from sqlalchemy.orm import declarative_base
from sqlalchemy import Integer


DeclarativeBase = declarative_base()


class FIAreaAncestorView(DeclarativeBase):
    __tablename__ = "fi_area_ancestor_view"
    id: Mapped[BigInteger] = mapped_column(Integer, primary_key=True)
    ancestor_id: Mapped[BigInteger] = mapped_column(Integer, nullable=False)
    level: Mapped[int] = mapped_column(Integer, nullable=False)


class FIArea(BaseModel):
    __tablename__ = "fi_area"

    kind: Mapped[FIAreaKindEnum] = mapped_column(String(50), nullable=False)
    area_code: Mapped[str] = mapped_column(String(20), nullable=False)
    area_name_en: Mapped[str] = mapped_column(String(50), nullable=False)
    area_name_fi: Mapped[str] = mapped_column(String(50), nullable=False)
    area_name_sv: Mapped[str] = mapped_column(String(50), nullable=False)
    parent_id: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        ForeignKey("fi_area.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )

    __table_args__ = (
        UniqueConstraint("area_code", "parent_id", name="uq_area_code_parent_id"),
    )
