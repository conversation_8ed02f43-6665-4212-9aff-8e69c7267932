from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, BigInteger, Column, Foreign<PERSON>ey, String, Text
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import CountryCode
from strandproperties.models.base import BaseModel


class AdTemplate(BaseModel):
    __tablename__ = "ad_template"

    id: Mapped[int] = mapped_column(primary_key=True)
    organization_id: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("organization.id"), nullable=False
    )
    image_url: Mapped[str] = mapped_column(String(255), nullable=False)
    smartly_id: Mapped[str] = mapped_column(String(255), nullable=False)

    organization = relationship("Organization", back_populates="ad_templates")


class Organization(BaseModel):
    __tablename__ = "organization"

    name: Mapped[str] = mapped_column(Text, nullable=False, unique=True)
    currency: Mapped[Optional[str]] = mapped_column(Text)
    language: Mapped[Optional[str]] = mapped_column(Text)
    country_code: Mapped[Optional[CountryCode]] = mapped_column(String(2))
    stripe_account_id: Mapped[Optional[str]] = mapped_column(Text)
    details = Column(MutableDict.as_mutable(JSON), nullable=False, default=lambda: {})

    ad_templates: Mapped[List[AdTemplate]] = relationship(
        "AdTemplate",
        back_populates="organization",
        cascade="all, delete-orphan",
    )
