from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>ole<PERSON>, Column, Float, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.ext.mutable import MutableDict, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import (
    DEFAULT_CURRENCY,
    DEFAULT_STATUS,
    CommissionTypeEnum,
    Currency,
    SoldBy,
    Status,
)
from strandproperties.models.base import BaseModel
from strandproperties.models.dias import SharedTrade
from strandproperties.models.fi_address import FIAddress
from strandproperties.models.fi_agent import FIAgent
from strandproperties.models.property import PropertyBase
from strandproperties.models.video import Stream, Tour
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FiPortals,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_property_type import (
    FIListingTypeEnum,
    FIOwnershipTypeEnum,
    FIPropertyTypeEnum,
    FIPropertyTypeGroupEnum,
)
from strandproperties.schemas.fi_property.fi_realty import (
    FIConsentToChange,
    FICost,
    FIDamages,
    FIKeyManagement,
    FILeaseDetails,
    FILivingComfortFactors,
    FILivingFormTypeCodeEnum,
    FIRealtyAdditionalInformation,
    FIRealtyAvailability,
    FIRealtyCondition,
    FIRealtyShare,
    FISupplierAssignedIdentifiers,
)

if TYPE_CHECKING:
    from strandproperties.models.fi_commercial_property_overview import (
        FICommercialPropertyOverview,
    )
    from strandproperties.models.fi_estate_overview import FIEstateOverview
    from strandproperties.models.fi_housing_company import FIHousingCompany
    from strandproperties.models.fi_other_share_overview import FIOtherShareOverview
    from strandproperties.models.fi_plot_overview import FIPlotOverview
    from strandproperties.models.fi_property_overview import FIPropertyOverview
    from strandproperties.models.fi_residential_property_overview import (
        FIResidentialPropertyOverview,
    )
    from strandproperties.models.fi_residential_share_overview import (
        FIResidentialShareOverview,
    )


class FIPropertyType(BaseModel):
    __tablename__ = "fi_property_type"

    listing_type: Mapped[FIListingTypeEnum] = mapped_column(
        String(50), nullable=False, index=True
    )
    ownership_type: Mapped[FIOwnershipTypeEnum] = mapped_column(
        String(50), nullable=False, index=True
    )
    property_type_group: Mapped[FIPropertyTypeGroupEnum] = mapped_column(
        String(50), nullable=False, index=True
    )
    property_type: Mapped[FIPropertyTypeEnum] = mapped_column(
        String(50), nullable=False, index=True
    )
    fi_properties: Mapped[List["FIProperty"]] = relationship(
        "FIProperty", back_populates="fi_property_type"
    )


class FIRealty(BaseModel):
    __tablename__ = "fi_realty"

    new_building: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    agents: Mapped[List["FIAgent"]] = relationship(
        "FIAgent", secondary="fi_realty_agent", back_populates="realties"
    )
    consent_to_change: Mapped[Optional[FIConsentToChange]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    costs: Mapped[Optional[List[FICost]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    costs_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    monthly_rent: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    auction_allowed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    starting_price_amount: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    selling_price: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, index=True
    )
    currency_code: Mapped[Optional[Currency]] = mapped_column(
        String(3), default=DEFAULT_CURRENCY
    )
    availability: Mapped[Optional[FIRealtyAvailability]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    agency_office_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    is_rented: Mapped[Optional[FIChoiceEnum]] = mapped_column(String(50), nullable=True)
    fi_address_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_address.id"), nullable=True, index=True
    )
    notify_if_price_changed: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    title: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    status: Mapped[Status] = mapped_column(
        String(20), nullable=False, default=DEFAULT_STATUS, index=True
    )
    transaction_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    transaction_does_not_include: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    transaction_includes: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    condition: Mapped[Optional[FIRealtyCondition]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    share: Mapped[Optional[FIRealtyShare]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    additional_area_measurement_information: Mapped[
        Optional[List[FITranslatedText]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    additional_information: Mapped[Optional[FIRealtyAdditionalInformation]] = (
        mapped_column(MutableDict.as_mutable(JSON), nullable=True)
    )
    supplier_assigned_identifiers: Mapped[
        Optional[list[FISupplierAssignedIdentifiers]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    damages: Mapped[Optional[list[FIDamages]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    living_comfort_factors: Mapped[Optional[list[FILivingComfortFactors]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    key_management: Mapped[Optional[FIKeyManagement]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    lease_details: Mapped[Optional[FILeaseDetails]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    living_form_type_code: Mapped[Optional[FILivingFormTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )

    fi_address: Mapped[Optional["FIAddress"]] = relationship(
        "FIAddress", primaryjoin="foreign(FIRealty.fi_address_id) == FIAddress.id"
    )
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_realty"
    )


def default_portals_dict():
    """Generate a dictionary of portal settings with default values from FiPortals."""
    return {field: getattr(FiPortals(), field) for field in FiPortals.model_fields}


class FIProperty(PropertyBase):
    __tablename__ = "fi_property"
    __mapper_args__ = {
        "polymorphic_identity": "finland",
    }

    # TODO: Consider removing these fields because they are already in the FIAddress model
    latitude: Mapped[Optional[float]] = mapped_column(Float)
    longitude: Mapped[Optional[float]] = mapped_column(Float)

    id: Mapped[int] = mapped_column(ForeignKey("property.id"), primary_key=True)
    commission: Mapped[Optional[float]] = mapped_column(Float)
    commission_note: Mapped[Optional[str]] = mapped_column(Text(200))
    commission_type: Mapped[Optional[CommissionTypeEnum]] = mapped_column(String(20))
    sold_by: Mapped[Optional[SoldBy]] = mapped_column(String(20))
    internal_note: Mapped[Optional[str]] = mapped_column(Text(200))
    imported_data = Column(JSON)

    portals: Mapped[dict[str, bool]] = mapped_column(
        MutableDict.as_mutable(JSON),
        nullable=False,
        default=default_portals_dict,
    )

    video_streams: Mapped[List["Stream"]] = relationship(
        "Stream", back_populates="property", cascade="all, delete-orphan"
    )

    video_tours: Mapped[List["Tour"]] = relationship(
        "Tour", back_populates="property", cascade="all, delete-orphan"
    )

    # Required fields
    fi_property_type_id: Mapped[int] = mapped_column(
        ForeignKey("fi_property_type.id"), nullable=False, index=True
    )
    fi_property_type: Mapped[FIPropertyType] = relationship(
        "FIPropertyType", uselist=False, back_populates="fi_properties"
    )

    fi_realty_id: Mapped[int] = mapped_column(
        ForeignKey("fi_realty.id"), nullable=False, index=True
    )
    fi_realty: Mapped[FIRealty] = relationship(
        "FIRealty", uselist=False, back_populates="fi_property"
    )

    fi_property_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_property_overview.id"), nullable=True, index=True
    )
    fi_property_overview: Mapped[Optional["FIPropertyOverview"]] = relationship(
        "FIPropertyOverview", uselist=False, back_populates="fi_property"
    )

    fi_residential_property_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_residential_property_overview.id"), nullable=True, index=True
    )
    fi_residential_property_overview: Mapped[
        Optional["FIResidentialPropertyOverview"]
    ] = relationship(
        "FIResidentialPropertyOverview", uselist=False, back_populates="fi_property"
    )

    fi_plot_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_plot_overview.id"), nullable=True, index=True
    )
    fi_plot_overview: Mapped[Optional["FIPlotOverview"]] = relationship(
        "FIPlotOverview", uselist=False, back_populates="fi_property"
    )

    fi_estate_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_estate_overview.id"), nullable=True, index=True
    )
    fi_estate_overview: Mapped[Optional["FIEstateOverview"]] = relationship(
        "FIEstateOverview", uselist=False, back_populates="fi_property"
    )

    fi_commercial_property_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_commercial_property_overview.id"), nullable=True, index=True
    )
    fi_commercial_property_overview: Mapped[
        Optional["FICommercialPropertyOverview"]
    ] = relationship(
        "FICommercialPropertyOverview", uselist=False, back_populates="fi_property"
    )

    fi_housing_company_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_housing_company.id"), nullable=True, index=True
    )
    fi_housing_company: Mapped[Optional["FIHousingCompany"]] = relationship(
        "FIHousingCompany",
        uselist=False,
        back_populates="fi_properties",
    )

    fi_residential_share_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_residential_share_overview.id"), nullable=True, index=True
    )
    fi_residential_share_overview: Mapped[Optional["FIResidentialShareOverview"]] = (
        relationship(
            "FIResidentialShareOverview", uselist=False, back_populates="fi_property"
        )
    )

    fi_other_share_overview_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_other_share_overview.id"), nullable=True, index=True
    )
    fi_other_share_overview: Mapped[Optional["FIOtherShareOverview"]] = relationship(
        "FIOtherShareOverview", uselist=False, back_populates="fi_property"
    )

    shared_trades: Mapped[Optional[List["SharedTrade"]]] = relationship(
        "SharedTrade",
        back_populates="fi_property",
    )
