from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (
    JSO<PERSON>,
    BigInteger,
    Boolean,
    DateTime,
    ForeignKey,
    Index,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import EventFormat, EventType
from strandproperties.schemas.fi_property.fi_common import FITranslatedText

from .base import BaseModel

if TYPE_CHECKING:
    from .contact import Contact
    from .lead import Lead
    from .property import PropertyBase
    from .user import User


class Event(BaseModel):
    """
    Event model table schema definition
    """

    __tablename__ = "event"

    event_type: Mapped[EventType] = mapped_column(String(50), nullable=False)
    event_format: Mapped[EventFormat] = mapped_column(String(50), nullable=False)
    title: Mapped[str] = mapped_column(String(120), nullable=True)
    descriptions: Mapped[List[FITranslatedText]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    internal_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    start_time: Mapped[DateTime] = mapped_column(DateTime, nullable=False)
    end_time: Mapped[DateTime] = mapped_column(DateTime, nullable=False)
    location: Mapped[Optional[str]] = mapped_column(String(300), nullable=True)
    url: Mapped[Optional[str]] = mapped_column(String(300), nullable=True)
    additional_details: Mapped[Optional[dict]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    is_cancelled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    contacts: Mapped[List["Contact"]] = relationship(
        "Contact", secondary="event_contact", back_populates="events"
    )
    properties: Mapped[List["PropertyBase"]] = relationship(
        "PropertyBase", secondary="event_property", back_populates="events"
    )
    users: Mapped[List["User"]] = relationship(
        "User", secondary="event_user", back_populates="events"
    )
    leads: Mapped[List["Lead"]] = relationship(
        "Lead", secondary="event_lead", back_populates="events"
    )

    organization_id: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("organization.id"), nullable=False, index=True
    )

    def validate_associations(self):
        if len(self.properties) == 0:
            raise ValueError("Event must be associated with at least one Property")
        if len(self.users) == 0:
            raise ValueError("Event must be associated with at least one User")


class EventContact(BaseModel):
    """
    EventContact model table schema definition
    """

    __tablename__ = "event_contact"
    __table_args__ = (
        UniqueConstraint("event_id", "contact_id", name="uq_event_contact"),
        Index("ix_event_contact_event", "event_id"),
        Index("ix_event_contact_contact", "contact_id"),
    )
    event_id: Mapped[int] = mapped_column(ForeignKey("event.id"), nullable=False)
    contact_id: Mapped[int] = mapped_column(ForeignKey("contact.id"), nullable=False)


class EventProperty(BaseModel):
    """
    EventProperty model table schema definition
    """

    __tablename__ = "event_property"
    __table_args__ = (
        UniqueConstraint("event_id", "property_id", name="uq_event_property"),
        Index("ix_event_property_event", "event_id"),
        Index("ix_event_property_property", "property_id"),
    )
    event_id: Mapped[int] = mapped_column(ForeignKey("event.id"), nullable=False)
    property_id: Mapped[int] = mapped_column(ForeignKey("property.id"), nullable=False)


class EventUser(BaseModel):
    """
    EventUser model table schema definition
    """

    __tablename__ = "event_user"
    __table_args__ = (
        UniqueConstraint("event_id", "user_id", name="uq_event_user"),
        Index("ix_event_user_event", "event_id"),
        Index("ix_event_user_user", "user_id"),
    )
    event_id: Mapped[int] = mapped_column(ForeignKey("event.id"), nullable=False)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), nullable=False)


class EventLead(BaseModel):
    """
    EventLead model table schema definition
    """

    __tablename__ = "event_lead"
    __table_args__ = (
        UniqueConstraint("event_id", "lead_id", name="uq_event_lead"),
        Index("ix_event_lead_event", "event_id"),
        Index("ix_event_lead_lead", "lead_id"),
    )
    event_id: Mapped[int] = mapped_column(ForeignKey("event.id"), nullable=False)
    lead_id: Mapped[int] = mapped_column(ForeignKey("lead.id"), nullable=False)
