from sqlalchemy import Column, ForeignKey, Index, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class ListingType(BaseModel):
    __tablename__ = "listing_type"
    __table_args__ = (Index("ix_listingtype_name", "name"),)

    name: Mapped[str] = mapped_column(Text, nullable=False)

    properties = relationship(
        "Property", secondary="property_listing_type", back_populates="listing_types"
    )


class PropertyListingType(BaseModel):
    __tablename__ = "property_listing_type"
    __table_args__ = (
        UniqueConstraint(
            "listing_type_id", "property_id", name="uq_listingtype_property"
        ),
        Index("ix_property_listingtype_listingtype", "listing_type_id"),
        Index("ix_property_listingtype_property", "property_id"),
    )

    listing_type_id = Column(ForeignKey("listing_type.id"), nullable=False)
    property_id = Column(ForeignKey("property_spain.id"), nullable=False)
