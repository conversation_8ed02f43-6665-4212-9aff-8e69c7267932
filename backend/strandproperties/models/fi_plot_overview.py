from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.ext.mutable import Mu<PERSON><PERSON><PERSON>, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_housing_company import FIHousingCompany
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FICommonArea,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_plot import (
    FIHoldingTypeCodeEnum,
    FIPlotBeach,
    FIPlotConstructionRight,
    FIPlotPropertyTypeCodeEnum,
    FIPlotZoning,
)


class FIPlotOverview(BaseModel):
    __tablename__ = "fi_plot_overview"

    plot_property_type_code: Mapped[Optional[FIPlotPropertyTypeCodeEnum]] = (
        mapped_column(String(50), nullable=True)
    )
    holding_type_code: Mapped[Optional[FIHoldingTypeCodeEnum]] = mapped_column(
        String(50), nullable=True
    )
    holding_type_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    landlord: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    yearly_rent: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    currency_code: Mapped[Optional[str]] = mapped_column(String(3), nullable=True)
    is_redeemable: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    redeemable_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    redemption_portion: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    optional_rental_plot: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    optional_rental_plot_description: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    lease_end_date: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    lease_period_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    plot_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    identification_number_of_land_charge: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    lease_holder: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    lease_hold_transfer_limitation: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    lease_hold_transfer_limitation_description: Mapped[
        Optional[List[FITranslatedText]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    legal_confirmation_of_title_to_real_property: Mapped[
        Optional[List[FITranslatedText]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    size_of_plot: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    area: Mapped[Optional[FICommonArea]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    zonings: Mapped[Optional[FIPlotZoning]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    beaches: Mapped[Optional[List[FIPlotBeach]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    construction_right: Mapped[Optional[FIPlotConstructionRight]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    unbuilt_plot: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    plot_redemption_info: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    plot_redemption_info_link: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )
    plot_rental_agreement: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    plot_rental_agreement_link: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )
    fi_housing_company_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_housing_company.id"), nullable=True, index=True
    )

    # Relationships
    fi_property: Mapped["FIProperty"] = relationship(
        "FIProperty", uselist=False, back_populates="fi_plot_overview"
    )

    fi_housing_company: Mapped["FIHousingCompany"] = relationship(
        "FIHousingCompany", uselist=False, back_populates="fi_plot_overviews"
    )
