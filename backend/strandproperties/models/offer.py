from datetime import datetime
from typing import Optional

from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class Offer(BaseModel):
    __tablename__ = "offer"

    property_id = Column(ForeignKey("property_spain.id"), nullable=True)
    property = relationship("Property", back_populates="offers")
    custom_reference_property = mapped_column(String(50), nullable=True)
    sale_price: Mapped[int] = mapped_column(Integer)
    special_condition: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    deposit_amount: Mapped[int] = mapped_column(Numeric)
    iban_number: Mapped[Optional[str]] = mapped_column(String(40), nullable=True)
    deposit_payee: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    deposit_payment_paid_to: Mapped[str] = mapped_column(String(25))
    price_settled_by: Mapped[datetime] = mapped_column(DateTime)
    private_purchase_contract_due_date: Mapped[datetime] = mapped_column(
        DateTime, nullable=True
    )
    signing_method: Mapped[str] = mapped_column(String(15))
    document_id: Mapped[int] = Column(ForeignKey("document.id"), nullable=False)
    document: Mapped[str] = relationship(
        "Document", uselist=False, back_populates="offer"
    )
    office_id: Mapped[int] = Column(BigInteger, ForeignKey("office.id"), nullable=True)
    office = relationship("Office", uselist=False, back_populates="offers")

    buyers = relationship(
        "Contact", secondary="buyer_offer", back_populates="buyer_offers"
    )
    sellers = relationship(
        "Contact", secondary="seller_offer", back_populates="seller_offers"
    )
    created_by = Column(ForeignKey("user.id"), nullable=False)
    details_of_sale = relationship(
        "DetailsOfSale", uselist=False, back_populates="offer"
    )
    created_user = relationship("User", foreign_keys=[created_by])


class BuyerOffer(BaseModel):
    __tablename__ = "buyer_offer"
    __table_args__ = (UniqueConstraint("offer_id", "buyer_id"),)

    offer_id = Column(ForeignKey(Offer.id), index=True)

    buyer_id = Column(ForeignKey("contact.id"), index=True)


class SellerOffer(BaseModel):
    __tablename__ = "seller_offer"
    __table_args__ = (UniqueConstraint("offer_id", "seller_id"),)

    offer_id = Column(ForeignKey(Offer.id), index=True)

    seller_id = Column(ForeignKey("contact.id"), index=True)
