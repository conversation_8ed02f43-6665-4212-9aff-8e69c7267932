from typing import List, Optional

from sqlalchemy import Column, DateTime, ForeignKey, Numeric, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import FIPurchaseOfferStatusEnum
from strandproperties.models.base import BaseModel
from strandproperties.models.document_signing import DocumentSigning


class FICounterOffer(BaseModel):
    __tablename__ = "fi_counter_offer"

    created_by = Column(ForeignKey("user.id"), nullable=True)
    unencumbered_price: Mapped[Numeric] = mapped_column(
        Numeric(precision=14, scale=2), nullable=False
    )
    valid_until: Mapped[DateTime] = mapped_column(DateTime, nullable=False)
    additional_details: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True
    )

    status: Mapped[Optional[FIPurchaseOfferStatusEnum]] = mapped_column(
        String(50), nullable=True, default=FIPurchaseOfferStatusEnum.DRAFT
    )

    purchase_offer_id: Mapped[int] = mapped_column(
        ForeignKey("fi_purchase_offer.id", ondelete="CASCADE"), nullable=False
    )
    previous_counter_offer_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_counter_offer.id", ondelete="SET NULL"), nullable=True
    )

    signings: Mapped[List["DocumentSigning"]] = relationship(
        "DocumentSigning",
        secondary="document_signing_entity",
        primaryjoin=(
            "and_("
            "FICounterOffer.id == foreign(DocumentSigningEntity.entity_id), "
            "DocumentSigningEntity.entity_type == 'fi_counter_offer'"
            ")"
        ),
        secondaryjoin="foreign(DocumentSigningEntity.document_signing_id) == DocumentSigning.id",
        viewonly=True,
    )

    previous_counter_offer = relationship(
        "FICounterOffer",
        remote_side="FICounterOffer.id",
        lazy="select",
        back_populates="next_counter_offers",
    )
    next_counter_offers = relationship(
        "FICounterOffer",
        back_populates="previous_counter_offer",
        lazy="noload",
    )
    purchase_offer = relationship(
        "FIPurchaseOffer",
        back_populates="counter_offers",
        lazy="select",
    )
    offerors = relationship(
        "Contact",
        secondary="fi_counter_offer_offeror",
    )
    offerees = relationship(
        "Contact",
        secondary="fi_counter_offer_offeree",
    )


class FICounterOfferOfferor(BaseModel):
    __tablename__ = "fi_counter_offer_offeror"

    counter_offer_id: Mapped[int] = mapped_column(
        ForeignKey("fi_counter_offer.id", ondelete="CASCADE"), nullable=False
    )
    offeror_id: Mapped[int] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )


class FICounterOfferOfferee(BaseModel):
    __tablename__ = "fi_counter_offer_offeree"

    counter_offer_id: Mapped[int] = mapped_column(
        ForeignKey("fi_counter_offer.id", ondelete="CASCADE"), nullable=False
    )
    offeree_id: Mapped[int] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )
