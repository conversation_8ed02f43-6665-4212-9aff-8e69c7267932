from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Float, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.ext.mutable import MutableDict, MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel
from strandproperties.models.fi_property import FIProperty
from strandproperties.schemas.fi_property.fi_common import (
    FIAsbestosMapping,
    FIChoiceEnum,
    FIEnergyCertificate,
    FITelevision,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_housing_company import (
    FIHousingCompanyBuildings,
    FIHousingCompanyConstruction,
    FIHousingCompanyFinances,
    FIHousingCompanyInspection,
    FIHousingCompanyInternetConnections,
    FIHousingCompanyMaintenance,
    FIHousingCompanyParkingSpace,
    FIHousingCompanyPremiseStatistic,
    FIRenovationShare,
    FISchemeIdEnum,
)
from strandproperties.schemas.fi_property.fi_housing_company_charges import (
    FIFinancingCharge,
    FIPlotCharges,
)

if TYPE_CHECKING:
    from strandproperties.models.fi_address import FIAddress
    from strandproperties.models.fi_plot_overview import FIPlotOverview


class FIHousingCompany(BaseModel):
    __tablename__ = "fi_housing_company"

    business_id: Mapped[str] = mapped_column(String(10), nullable=True)
    scheme_id: Mapped[FISchemeIdEnum] = mapped_column(String(10), nullable=True)
    name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    fi_address_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("fi_address.id"), nullable=True, index=True
    )
    street_address: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    postal_area: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    manager_name: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    manager_contact_details: Mapped[Optional[str]] = mapped_column(
        Text(20), nullable=True
    )
    maintenance: Mapped[Optional[FIHousingCompanyMaintenance]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    property_identifier: Mapped[str] = mapped_column(String(50), nullable=True)
    construction: Mapped[Optional[FIHousingCompanyConstruction]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    buildings: Mapped[Optional[FIHousingCompanyBuildings]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    yard_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    internet_connections: Mapped[Optional[FIHousingCompanyInternetConnections]] = (
        mapped_column(MutableDict.as_mutable(JSON), nullable=True)
    )
    television: Mapped[Optional[FITelevision]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    last_annual_general_meeting_date: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    next_annual_general_meeting_date: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    identified_deficiencies: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    cost_incurring_liabilities: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    repairs_and_maintenance_agreements: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    house_manager_certificate: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    finances: Mapped[Optional[FIHousingCompanyFinances]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    energy_certificate: Mapped[Optional[FIEnergyCertificate]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    asbestos_mapping: Mapped[Optional[FIAsbestosMapping]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    parking_spaces: Mapped[Optional[List[FIHousingCompanyParkingSpace]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    parking_spaces_description: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    housing_company_premise_statistics: Mapped[
        Optional[List[FIHousingCompanyPremiseStatistic]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    housing_company_premise_statistics_description: Mapped[
        Optional[List[FITranslatedText]]
    ] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    renovations: Mapped[Optional[List[FIRenovationShare]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    renovations_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    renovations_planned_description: Mapped[Optional[List[FITranslatedText]]] = (
        mapped_column(MutableList.as_mutable(JSON), nullable=True)
    )
    inspections: Mapped[Optional[List[FIHousingCompanyInspection]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    inspections_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    additional_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    list_of_shares_transferred: Mapped[Optional[FIChoiceEnum]] = mapped_column(
        String(50), nullable=True
    )
    digital_share_group_identifier: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    # Charges
    management_charge: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    financing_charge: Mapped[Optional[FIFinancingCharge]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    plot_charges: Mapped[Optional[FIPlotCharges]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True
    )
    special_charge: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    maintenance_charge: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    charges_description: Mapped[Optional[List[FITranslatedText]]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )
    charges_currency_code: Mapped[Optional[str]] = mapped_column(
        String(3), nullable=True
    )

    # Relationships

    fi_address: Mapped[Optional["FIAddress"]] = relationship(
        "FIAddress",
        foreign_keys="[FIHousingCompany.fi_address_id]",
    )

    fi_plot_overviews: Mapped[Optional[List["FIPlotOverview"]]] = relationship(
        "FIPlotOverview",
        back_populates="fi_housing_company",
    )

    fi_properties: Mapped[List["FIProperty"]] = relationship(
        "FIProperty", back_populates="fi_housing_company"
    )
