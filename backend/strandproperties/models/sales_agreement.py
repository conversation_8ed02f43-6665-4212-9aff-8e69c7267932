from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    BigInteger,
    CheckConstraint,
    Column,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.models.base import BaseModel


class SalesAgreement(BaseModel):
    __tablename__ = "sales_agreement"

    property_document_id = Column(
        ForeignKey("property_document.id", ondelete="CASCADE"), nullable=False
    )
    property_document = relationship(
        "PropertyDocument", uselist=False, back_populates="sales_agreement"
    )
    first_seller_id = Column(ForeignKey("contact.id"), nullable=False)
    validity_in_months: Mapped[int] = mapped_column(
        Integer,
        CheckConstraint("validity_in_months > 0", name="positive_validity_in_months"),
        nullable=False,
    )
    signing_method: Mapped[str] = mapped_column(String(15))
    metadata_json = Column(
        MutableDict.as_mutable(JSON),
        nullable=False,
        default={},
    )
    office_id: Mapped[int] = Column(BigInteger, Foreign<PERSON>ey("office.id"), nullable=True)
    office = relationship("Office", uselist=False, back_populates="sales_agreements")
    created_by = Column(ForeignKey("user.id"), nullable=True)
    created_user = relationship("User", foreign_keys=[created_by])
