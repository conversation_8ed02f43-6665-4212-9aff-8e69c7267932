import copy
import xml.dom.minidom
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import cast

from strandproperties.config import app_cfg
from strandproperties.constants import Currency, Language
from strandproperties.libs.aws import S3Service
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.thinkspain.mapper import (
    map_descriptions,
    map_if_has_feature,
    map_new_build_and_property_type,
    map_price_and_listing_type,
)
from strandproperties.libs.utils import allowed_streaming_video
from strandproperties.logger import logger
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript

has_pool_feature_ids = [33, 40]


class ThinkSpainXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element("root")
        think_spain_header = ET.SubElement(root, "think_spain")
        think_spain_version = ET.SubElement(think_spain_header, "import_version")
        think_spain_version.text = "1.16"
        think_spain_agent = ET.SubElement(root, "agent")
        think_spain_agent_name = ET.SubElement(think_spain_agent, "name")
        think_spain_agent_name.text = "Strand Properties"

        self.root = root
        self.encoding = encoding

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}
        ### All mandatory
        property_tag["property"] = ET.SubElement(self.root, "property")
        property_tag["updated_at"] = ET.SubElement(
            property_tag["property"], "last_amended_date"
        )  # format YYYY-MM-DD HH:MM:SS  (strftime("%Y-%m-%d %H:%M:%S")) --> This needs to be updated whenever a property is updated
        property_tag["property_id"] = ET.SubElement(
            property_tag["property"], "unique_id"
        )  # alphanumeric, max 50 chars

        property_tag["property_reference"] = ET.SubElement(
            property_tag["property"], "agent_ref"
        )  # alphanumeric, max 250 chars, the ref that the customer will see
        property_tag["price"] = ET.SubElement(
            property_tag["property"], "euro_price"
        )  # numeric, max 8 chars
        property_tag["listing_type"] = ET.SubElement(
            property_tag["property"], "sale_type"
        )
        # Property types:
        """
            Beach Apartment
            Flat
            Penthouse
            Studio
            Villa
            Finca/Country House
            Semi-detached Villa
            Terraced Villa
            Undeveloped Land
            Building Plot
            Restaurant/Bar
            Hotel
            Office
            Business
            Townhouse
            Garage
            Apartment
            Ruin
            Cave House
            Bungalow
            Wooden Home
            Loft
            Shop
            Commercial
            Mobile Home
        """
        property_tag["property_type"] = ET.SubElement(
            property_tag["property"], "property_type"
        )  # alpha

        property_tag["city"] = ET.SubElement(
            property_tag["property"], "town"
        )  # alpha, area_level_1
        property_tag["province"] = ET.SubElement(
            property_tag["property"], "province"
        )  # alpha, area_level_1 - province

        ## ------------------------------------------------------------------------

        property_tag["beds"] = ET.SubElement(
            property_tag["property"], "bedrooms"
        )  # numeric; empty, missing tag or "0" if unknown
        property_tag["baths"] = ET.SubElement(
            property_tag["property"], "bathrooms"
        )  # numeric; empty, missing tag or "0" if unknown
        property_tag["pool"] = ET.SubElement(
            property_tag["property"], "pool"
        )  # numeric; empty, missing tag or "0" if unknown. "1" if a pool is available

        # Descriptions
        property_tag["desc"] = ET.SubElement(
            property_tag["property"], "description"
        )  # No character limit. No HTML, Use &#13; to force a line break in the text
        property_tag[Language.ENGLISH.value] = ET.SubElement(
            property_tag["desc"], Language.ENGLISH.value
        )
        property_tag[Language.SPANISH.value] = ET.SubElement(
            property_tag["desc"], Language.SPANISH.value
        )
        property_tag[Language.FINNISH.value] = ET.SubElement(
            property_tag["desc"], Language.FINNISH.value
        )
        property_tag[Language.GERMAN.value] = ET.SubElement(
            property_tag["desc"], Language.GERMAN.value
        )
        property_tag[Language.SWEDISH.value] = ET.SubElement(
            property_tag["desc"], Language.SWEDISH.value
        )

        # images
        # - maximum of 50 image ids per property
        # - URL must end with valid image format (<xs:pattern value="(https?|ftp)://(.*)\.(gif|jpe?g|png|GIF|JPE?G|PNG)"/>)
        # - ids from 1 to 50
        # property_tag["images"] = ET.SubElement(property_tag["property"], "images")
        # image = ET.SubElement(images, "image", id="1") ### EXAMPLE
        # url = ET.SubElement(image, "url") ### EXAMPLE

        ### Optional
        property_tag["currency"] = ET.SubElement(
            property_tag["property"], "currency"
        )  # ISO 4217 3-character currency code of the property. If omitted, assumed EUR/Euro (optional).
        property_tag["new_build"] = ET.SubElement(
            property_tag["property"], "new_build"
        )  # 1 if new build property, 0 or exclude tag if not (optional)
        property_tag["built_year"] = ET.SubElement(
            property_tag["property"], "year_built"
        )  # year property built, 4-digit year format or exclude tag

        property_tag["location_detail"] = ET.SubElement(
            property_tag["property"], "location_detail"
        )  # Town/Village within Municipality, District, Neighbourhood, or Area (optional)

        property_tag["postal_code"] = ET.SubElement(
            property_tag["property"], "postcode"
        )  # Postcode (optional)

        property_tag["street_address"] = ET.SubElement(
            property_tag["property"], "full_address"
        )
        # Full address (optional)

        property_tag["street_address"] = ET.SubElement(
            property_tag["property"], "display_address"
        )
        # 1 = Display the address on the portal, 0 or missing tag = Do not display the address (optional)

        property_tag["location"] = ET.SubElement(property_tag["property"], "location")
        # Start of GEO Location node, recommended (optional)

        property_tag["latitude"] = ET.SubElement(property_tag["location"], "latitude")
        #  GEO latitude coordinate (optional)
        property_tag["longitude"] = ET.SubElement(property_tag["location"], "longitude")
        #  GEO longitude coordinate (optional)

        property_tag["built_area"] = ET.SubElement(
            property_tag["property"], "living_area"
        )  # Size of living area / build in metres squared (optional)
        property_tag["plot_area"] = ET.SubElement(
            property_tag["property"], "plot_size"
        )  # Size of plot in metres squared (optional)

        # Features
        # property_tag["features"] = ET.SubElement(
        #     property_tag["property"], "features"
        # )  # alpha, max 35 chars per property feature, either Spanish or English
        # feature = ET.SubElement(features, "feature") ## EXAMPLE

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        property_images = [image for image in property.images if not image.is_hidden]
        property_images.sort(key=lambda x: x.order)  # type: ignore
        property_images: list[Image] = property_images[:50]

        feature_ids: list[int] = [feature.id for feature in property.features]
        feature_names: list[str] = [feature.name[:35] for feature in property.features]
        descriptions = map_descriptions(property=property)

        (new_build, property_type) = map_new_build_and_property_type(
            property_type_origin=cast(str, property.property_type_origin) or "",
            property_type_category=cast(str, property.property_type_category) or "",
            property_type_name=cast(str, property.property_type) or "",
        )

        property_tag["property_id"].text = f"{property.id}"
        property_tag["updated_at"].text = (
            property.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            if property.updated_at
            else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        property_tag["property_reference"].text = f"{property.reference}"

        (price, listing_type) = map_price_and_listing_type(property)
        property_tag["listing_type"].text = listing_type
        property_tag["price"].text = price[:8]  # max of 8 chars (99_999_999)

        property_tag["property_type"].text = property_type

        property_tag["city"].text = (
            f"{property._area_level_1.name}"
            if property._area_level_1
            else "Not defined"
        )
        property_tag["province"].text = (
            f"{property._area_level_1.province}"
            if property._area_level_1
            else "Not defined"
        )
        property_tag["beds"].text = f"{property.bedrooms}" if property.bedrooms else "0"
        property_tag["baths"].text = (
            f"{property.bathrooms}" if property.bathrooms else "0"
        )
        property_tag["pool"].text = map_if_has_feature(
            feature_ids=feature_ids, features_to_compare=has_pool_feature_ids
        )
        for description in descriptions:
            property_tag[description.language].text = description.description

        property_tag["images"] = ET.SubElement(property_tag["property"], "images")
        for index, image in enumerate(property_images):
            img_tag = ET.SubElement(
                property_tag["images"],
                "photo",
                id=f"{(image.order + 1) if image.order else (index + 1)}",
            )
            ET.SubElement(img_tag, "url").text = image.url

        property_tag["media"] = ET.SubElement(property_tag["property"], "media")
        for v in property.video_streams:
            if not v.is_hidden and allowed_streaming_video(v.url):
                video = ET.SubElement(property_tag["media"], "video")
                video.text = v.url
                break

        for v in property.video_tours:
            if not v.is_hidden:
                tour = ET.SubElement(property_tag["media"], "virtualtour")
                tour.text = v.url
                break

        ### optional tags
        property_tag["currency"].text = (
            f"{property.currency.upper()}" if property.currency else Currency.EUR
        )
        property_tag["new_build"].text = new_build
        property_tag["location_detail"].text = f"{property.location_from_areas}" or ""
        property_tag["latitude"].text = (
            f"{property.latitude}" if property.latitude else "0"
        )
        property_tag["longitude"].text = (
            f"{property.longitude}" if property.longitude else "0"
        )
        property_tag["built_area"].text = (
            f"{property.built_area}" if property.built_area else "0"
        )
        property_tag["plot_area"].text = (
            f"{property.plot_area}" if property.plot_area else "0"
        )

        if feature_names:
            property_tag["features"] = ET.SubElement(
                property_tag["property"], "features"
            )
            for feature in feature_names:
                ET.SubElement(property_tag["features"], "feature").text = feature

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        ### Check all required fields first
        # images are REQUIRED
        if not property.images:
            logger.info(
                f"> Skipping property [{property.reference}][Thinkspain]: images are required",
                extra={"requestId": request_id},
            )
            return False
        images = [image for image in property.images if not image.is_hidden]
        if not images:
            logger.info(
                f"> Skipping property [{property.reference}][Thinkspain]: non-hidden images are required",
                extra={"requestId": request_id},
            )
            return False

        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][Thinkspain]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/thinkspain",
            portal=PortalNames.THINKSPAIN,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
