from typing import List, cast

from strandproperties.constants import ListingTypeEnum
from strandproperties.libs.kyero.mapper import (
    DescriptionByLanguage,
    replace_description_characters,
)
from strandproperties.models.listing_type import ListingType
from strandproperties.models.property import Property, PropertyDescription

"""
    Beach Apartment
    Flat
    Penthouse
    Studio
    Villa
    Finca/Country House
    Semi-detached Villa
    Terraced Villa
    Undeveloped Land
    Building Plot
    Restaurant/Bar
    Hotel
    Office
    Business
    Townhouse
    Garage
    Apartment
    Ruin
    Cave House
    Bungalow
    Wooden Home
    Loft
    Shop
    Commercial
    Mobile Home
"""
property_type_mapper: dict[str, str] = {
    "Flat": "Flat",
    "Penthouse": "Penthouse",
    "Studio": "Studio",
    "Villa": "Villa",
    "Finca": "Finca/Country House",
    "Country House": "Finca/Country House",
    "Semi Detached Villa": "Semi-detached Villa",
    "Residential Plot": "Building Plot",
    "Restaurant": "Restaurant/Bar",
    "Bar": "Restaurant/Bar",
    "Hotel": "Hotel",
    "Office": "Office",
    "Business": "Business",
    "Town House": "Townhouse",
    "Apartment": "Apartment",
    "Bungalow": "Bungalow",
    "Loft": "Loft",
    "Shop": "Shop",
    "Commercial Premises": "Commercial",
    "Commercial Other": "Commercial",
}


def map_new_build_and_property_type(
    *, property_type_origin: str, property_type_category: str, property_type_name: str
) -> tuple[str, str]:
    is_new_build: str = "1" if property_type_origin == "New development" else "0"

    thinkspain_property_type: str = "Townhouse"

    if property_type_name in property_type_mapper:
        thinkspain_property_type = property_type_mapper[property_type_name]

    if (
        property_type_category == "Apartments"
        and thinkspain_property_type == "Townhouse"
    ):
        thinkspain_property_type = "Apartment"

    return (is_new_build, thinkspain_property_type)


def map_descriptions(property: Property) -> list[DescriptionByLanguage]:
    descriptions: list[PropertyDescription] = property.descriptions or []

    response: list[DescriptionByLanguage] = []

    for description in descriptions:
        response.append(
            DescriptionByLanguage(
                **{
                    "language": description.language,
                    "description": replace_description_characters(
                        description.description
                    ),
                }
            )
        )

    return response


def map_if_has_feature(feature_ids: list[int], features_to_compare: list[int]) -> str:
    for id_to_compare in features_to_compare:
        if id_to_compare in feature_ids:
            return "1"

    return "0"


sale_type_mapper = {
    ListingTypeEnum.SALE: "sale",
    ListingTypeEnum.RENT_LONG: "longterm",
    ListingTypeEnum.RENT_SHORT: "longterm",
}


def map_price_and_listing_type(property: Property) -> tuple[str, str]:
    listing_types: List[ListingType] = property.listing_types
    price_sale = f"{property.price_sale}" if cast(int, property.price_sale) else "0"

    listing_type_mapped = sale_type_mapper[ListingTypeEnum.SALE]

    for listing_type in listing_types:
        listing_type_mapped = sale_type_mapper[cast(ListingTypeEnum, listing_type.name)]

        match listing_type.name:
            case ListingTypeEnum.SALE:
                return (price_sale, listing_type_mapped)
            case ListingTypeEnum.RENT_LONG:
                return (f"{property.price_rent_long_term}", listing_type_mapped)
            case ListingTypeEnum.RENT_SHORT:
                return (f"{property.price_rent_short_term}", listing_type_mapped)
            case _:
                return (price_sale, listing_type_mapped)

    return (price_sale, listing_type_mapped)
