import xml.etree.ElementTree as ET

from strandproperties.libs.kyero.xml_builder import KyeroV3XMLBuilder
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


class ResalesOnlineXMLBuilder(BaseScript):
    def set_headers(self):
        return self.kyero_builder.set_headers()

    def set_property_tags(self) -> dict[str, ET.Element]:
        return self.kyero_builder.set_property_tags()

    def fill_property_tags(
        self,
        original_property_tag: dict[str, ET.Element],
        property: Property,
    ):
        return self.kyero_builder.fill_property_tags(
            original_property_tag, property, is_resales_kyero=True
        )

    def write_to_file(self, file_location: str):
        return self.kyero_builder.write_to_file(file_location)

    def upload_to_s3(self, file_location: str, file_name: str):
        return self.kyero_builder.upload_to_s3(file_location, file_name)

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        return self.kyero_builder._validate_fields(property, request_id)

    def run(self):
        ### Generates XML and Uploads it to S3
        ### INFO: ResalesOnline uses the same XML as Kyero, but
        ### we are using one with more fields (called Kyero+)
        self.kyero_builder = KyeroV3XMLBuilder()

        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/resalesonline",
            portal=PortalNames.RESALESONLINE,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
