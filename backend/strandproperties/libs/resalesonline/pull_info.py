import json
import re
from datetime import UTC, datetime
from math import ceil, floor
from typing import Literal, Optional, Sequence, TypeVar

import dateutil.relativedelta
import httpx
from slugify import slugify
from sqlalchemy import and_, delete, select
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.config import app_cfg
from strandproperties.constants import (
    DEFAULT_CURRENCY,
    ConditionType,
    DataSource,
    DescriptionType,
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
    Language,
    ListingTypeEnum,
    Status,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.utils import purge_website_cache
from strandproperties.logger import logger
from strandproperties.models.area_levels import (
    AreaLevel1,
    AreaLevel2,
    AreaLevel3,
    AreaLevel4,
    AreaLevel5,
)
from strandproperties.models.base import BaseModel
from strandproperties.models.feature import Feature, PropertyFeature
from strandproperties.models.image import Image
from strandproperties.models.listing_type import ListingType, PropertyListingType
from strandproperties.models.orientation import Orientation, PropertyOrientation
from strandproperties.models.property import Property, PropertyDescription, PropertyType
from strandproperties.models.setting import PropertySetting, Setting
from strandproperties.models.view import PropertyView, View
from strandproperties.schemas.mapping import PropertyTypeRead
from strandproperties.scripts.base import BaseScript
from strandproperties.utils.event_log import create_event_log
from strandproperties.views.exceptions import ApiError

QueryId = str
PropertyCount = int


class PullResalesOnlineProperties(BaseScript):
    """
    Pulling flow:
    - Pull properties from ResalesOnline API
    - Iterate through Resales properties:
        - If exists in MyStrand:
            - Skip if archived
            - Skip and set updated_at to None if it wasn't updated in ResalesOnline in last 2 hours
            - Otherwise, update property
        - If not exists in MyStrand:
            - Create new property
    - Iterate through properties in MyStrand with criterias:
        - Data source: ResalesOnline
        - Status: not archived
        - Updated at is older than 2 hours ago
            - Soft delete property (set status to archived)
            - Purge website cache
    """

    def _create_client(self):
        return httpx.Client(
            params={
                "p1": self.contact_id,
                "p2": self.api_key,
                "P_SortType": 3,  # By last updated date (most recent first)
                "p_ShowLastUpdateDate": True,
            },
            timeout=15.0,
        )

    def get_only_own_properties(self) -> list[str]:
        """
        Filter defined in the API Keys in ResalesOnline.
        Means:
        - Country: Spain
        - Province/Area: Costa del Sol
        - Only Resales properties
        - Price range: 200_000 - 1_000_000_000
        - Status: Available, Under Offer, Sale Agreed
        - ✅ Resales Properties > ✅ Only own properties
        """
        filter_id = 6

        query_id = 0
        property_count = 0
        with self._create_client() as client:
            res = client.get(
                "https://webapi.resales-online.com/V6/SearchProperties",
                params={
                    "p_new_devs": "include",
                    "p_PageSize": 1,
                    "p_agency_filterid": filter_id,
                },
            )
            res.raise_for_status()
            json = res.json()
            query_id = json["QueryInfo"]["QueryId"]
            property_count = json["QueryInfo"]["PropertyCount"]

        #####
        page_size = 40
        page_count = ceil(property_count / page_size)

        properties_list = []
        with self._create_client() as client:
            for i in range(1, page_count + 1):

                res = client.get(
                    "https://webapi.resales-online.com/V6/SearchProperties",
                    params={
                        "p_PageSize": page_size,  # max 40
                        "p_PageNo": i,
                        "p_QueryId": query_id,  # The unique identifier for this search
                        "p_agency_filterid": filter_id,
                    },
                )

                properties: list[dict] = res.json()["Property"]
                properties_list += properties

        refs = [property.get("Reference") for property in properties_list]

        return refs

    def get_query_id_and_count(
        self, *, filter_id: int
    ) -> tuple[QueryId, PropertyCount]:
        with self._create_client() as client:
            res = client.get(
                "https://webapi.resales-online.com/V6/SearchProperties",
                params={
                    "p_new_devs": "include",
                    "p_PageSize": 1,
                    "p_agency_filterid": filter_id,
                },
            )
            res.raise_for_status()
            json = res.json()
            return (
                json["QueryInfo"]["QueryId"],
                json["QueryInfo"]["PropertyCount"],
            )

    def fetch_properties_from_api(
        self,
        *,
        query_id: str,
        total_count: int,
        filter_id: int,
        page_size: int = 40,
        page_start: int = 1,
        page_end: Optional[int] = None,
        get_all: bool = True,
        request_id: str,
    ) -> list[dict]:
        page_count = ceil(total_count / page_size)
        if not page_end:
            page_end = page_count
        if page_start < 1:
            page_start = 1
        if page_end > page_count:
            page_end = page_count

        all_properties = []

        with self._create_client() as client:
            for i in range(page_start, page_end + 1):
                res = client.get(
                    "https://webapi.resales-online.com/V6/SearchProperties",
                    params={
                        "p_PageSize": page_size,  # max 40
                        "p_PageNo": i,
                        "p_QueryId": query_id,  # The unique identifier for this search
                        "p_agency_filterid": filter_id,
                    },
                )

                properties: list[dict] = res.json()["Property"]
                all_properties.extend(properties)

        return all_properties

    def get_properties_and_save_them_in_database(
        self,
        *,
        query_id: str,
        total_count: int,
        filter_id: int,
        page_size: int = 40,
        page_start: int = 1,
        page_end: Optional[int] = None,
        get_all: bool = False,  # if true, it will only get properties updated in the last 2 days
        properties_grouped_by_category: dict[str, list[PropertyTypeRead]],
        all_features: Sequence[Feature],
        all_views: Sequence[View],
        all_settings: Sequence[Setting],
        all_orientations: Sequence[Orientation],
        own_properties: list[str],
        request_id: str,
    ) -> None:
        listing_type_id = self.db_session.scalars(
            select(ListingType.id).where(ListingType.name == ListingTypeEnum.SALE)
        ).one()

        if get_all:
            # Fetch all properties at once if get_all is True
            start = datetime.now()
            all_properties = self.fetch_properties_from_api(
                query_id=query_id,
                total_count=total_count,
                filter_id=filter_id,
                page_size=page_size,
                page_start=page_start,
                page_end=page_end,
                get_all=True,
                request_id=request_id,
            )
            end = datetime.now()
            logger.info(
                "Fetched %s properties, duration: %s seconds",
                len(all_properties),
                (end - start).total_seconds(),
                extra={
                    "tag": "ResalesOnlinePull",
                    "requestId": request_id,
                },
            )

            start = datetime.now()
            self._write_property_to_database(
                properties=all_properties,
                properties_grouped_by_category=properties_grouped_by_category,
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
                own_properties=own_properties,
                listing_type_id=listing_type_id,
                request_id=request_id,
            )
            end = datetime.now()
            logger.info(
                "Saved %s properties to DB, duration: %s seconds",
                len(all_properties),
                (end - start).total_seconds(),
            )

            # DEBUG: Save all properties to S3
            try:
                self.save_to_s3(properties=all_properties, filter_id=filter_id)
            except Exception as e:
                logger.error(
                    f"Error saving properties to S3: {str(e)}",
                )
        else:
            start = datetime.now()
            # Process page by page if get_all is False to check for changes
            page_count = ceil(total_count / page_size)
            if not page_end:
                page_end = page_count
            if page_start < 1:
                page_start = 1
            if page_end > page_count:
                page_end = page_count

            total_changed_properties = 0
            stopped_page = 0
            with self._create_client() as client:
                for i in range(page_start, page_end + 1):
                    res = client.get(
                        "https://webapi.resales-online.com/V6/SearchProperties",
                        params={
                            "p_PageSize": page_size,
                            "p_PageNo": i,
                            "p_QueryId": query_id,
                            "p_agency_filterid": filter_id,
                        },
                    )

                    properties: list[dict] = res.json()["Property"]

                    n_changed_properties = self._write_property_to_database(
                        properties=properties,
                        properties_grouped_by_category=properties_grouped_by_category,
                        all_features=all_features,
                        all_views=all_views,
                        all_settings=all_settings,
                        all_orientations=all_orientations,
                        own_properties=own_properties,
                        listing_type_id=listing_type_id,
                        request_id=request_id,
                    )

                    total_changed_properties += n_changed_properties

                    if n_changed_properties < len(properties):
                        stopped_page = i
                        break

            end = datetime.now()
            logger.info(
                "Updated %s properties, stopped at page %s, duration: %s seconds",
                total_changed_properties,
                stopped_page,
                (end - start).total_seconds(),
                extra={
                    "tag": "ResalesOnlinePull",
                    "requestId": request_id,
                },
            )

    def _delete_resalesonline_properties_from_database(self, now: datetime):
        cut_off_time = now + dateutil.relativedelta.relativedelta(days=-1)

        n_properties_deleted = 0

        # find all published properties that were updated long time ago
        stmt = select(Property).where(
            Property.data_source == DataSource.RESALES_ONLINE,
            Property.updated_at <= cut_off_time,
            Property.status != Status.ARCHIVED,
        )

        properties = self.db_session.scalars(stmt).unique().all()

        n_properties_deleted += len(properties)
        for property in properties:
            # soft deletes the property and all its relationships
            property.status = Status.ARCHIVED

            create_event_log(
                db_session=self.db_session,
                object_type=EventLogObjectType.PROPERTY,
                object_id=property.id,
                actor_type=EventLogActorType.SYSTEM,
                action=EventLogAction.STATUS_CHANGED,
                data_before={"status": property.status},
                data_after={"status": Status.ARCHIVED},
            )

            self.db_session.add(property)
            purge_website_cache(property.reference)

        self.db_session.commit()

        return n_properties_deleted

    def _check_if_property_updated_since(self, *, property: dict, since: datetime):
        import pytz

        local = pytz.timezone("Europe/Madrid")

        last_updated = property.get(
            "LastUpdated", datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        last_updated_datetime = datetime.strptime(last_updated, "%Y-%m-%d %H:%M:%S")

        local_dt = local.localize(last_updated_datetime, is_dst=None)
        utc_dt = local_dt.astimezone(pytz.utc)
        naive = utc_dt.replace(tzinfo=None)

        return naive > since

    def _check_if_property_updated_last_two_days(self, *, property: dict):
        last_updated = property.get(
            "LastUpdated", datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        last_updated_datetime = datetime.strptime(last_updated, "%Y-%m-%d %H:%M:%S")
        difference = datetime.now(UTC) - last_updated_datetime
        return True if difference.days < 2 else False

    def _write_property_to_database(
        self,
        *,
        properties: list[dict],
        properties_grouped_by_category: dict[str, list[PropertyTypeRead]],
        all_features: Sequence[Feature],
        all_views: Sequence[View],
        all_settings: Sequence[Setting],
        all_orientations: Sequence[Orientation],
        own_properties: list[str],
        listing_type_id: int,
        request_id: str,
    ):
        n_changed_properties = 0

        for property in properties:
            p_ref = property.get("Reference")

            # Do not save "Own properties" in our DB
            if property.get("Reference") in own_properties:
                continue

            new_property = self.db_session.scalar(
                select(Property).where(
                    and_(
                        Property.data_source == DataSource.RESALES_ONLINE,
                        Property.reference == property.get("Reference"),
                    )
                )
            )

            if new_property:
                # update the property if exists, but skip if it's archived
                if new_property.status == Status.ARCHIVED:
                    logger.info(
                        "Skipping archived property with reference %s",
                        p_ref,
                        extra={
                            "tag": "ResalesOnlinePull",
                            "requestId": request_id,
                        },
                    )
                    continue

                # If property doesn't have changes from Resales since last SYNC
                # then don't do anything apart from setting updated_at to avoid the final delete
                if not self._check_if_property_updated_since(
                    property=property, since=new_property.updated_at
                ):
                    # Update plot area if it differs from the current value
                    new_plot_area = ceil(float(property.get("GardenPlot", 0)))
                    if new_plot_area != new_property.plot_area:
                        new_property.plot_area = new_plot_area
                    new_property.updated_at = None
                    self.db_session.add(new_property)
                    self.db_session.commit()
                    continue

                logger.info(
                    "Updating property with id %s and reference %s",
                    new_property.id,
                    p_ref,
                    extra={
                        "tag": "ResalesOnlinePull",
                        "requestId": request_id,
                    },
                )
            else:
                # create new property
                new_property = Property(
                    reference=property.get("Reference"),
                    imported_data=property,
                    organization_id=1,
                    status=Status.PUBLISHED,
                    is_public_coordinates=0,
                    private_info={},
                    is_exclusive=0,
                    portals={"is_resalesonline_enabled": True},
                    legacy_data={},
                    data_source=DataSource.RESALES_ONLINE,
                )

                logger.info(
                    "Inserting new property with reference %s",
                    p_ref,
                    extra={
                        "tag": "ResalesOnlinePull",
                        "requestId": request_id,
                    },
                )

            n_changed_properties += 1

            new_property.portals["is_resalesonline_enabled"] = True
            agency_ref = property.get("AgencyRef", None)
            new_property.legacy_data["agencyref"] = agency_ref
            new_property.status = Status.PUBLISHED

            (area1_id, area2_id, area3_id, area4_id, area5_id) = self._get_area_ids(
                location=property.get("Location", None)
            )
            new_property.area_level_1_id = area1_id
            new_property.area_level_2_id = area2_id
            new_property.area_level_3_id = area3_id
            new_property.area_level_4_id = area4_id
            new_property.area_level_5_id = area5_id

            property_type = property.get("PropertyType", {})
            if property_type:
                new_property.property_type_id = self._get_property_type_id(
                    category=property_type.get("Type", ""),
                    property_type=property_type.get("Subtype1", ""),
                    description=property_type.get("Description", ""),
                    properties_grouped_by_category=properties_grouped_by_category,
                )

            new_property.bedrooms = floor(float(property.get("Bedrooms", 0)))
            new_property.bathrooms = floor(float(property.get("Bathrooms", 0)))
            new_property.currency = str(
                property.get("Currency", DEFAULT_CURRENCY)
            ).lower()
            new_property.price_sale = ceil(float(property.get("Price", 0)))
            new_property.price_sale_old = ceil(float(property.get("OriginalPrice", 0)))
            new_property.built_area = ceil(float(property.get("Built", 0)))
            new_property.terrace_area = ceil(float(property.get("Terrace", 0)))
            new_property.plot_area = ceil(float(property.get("GardenPlot", 0)))

            title = f"{property.get('PropertyType', {}).get('NameType', '-')} in {property['Location']}"
            new_property.title = title
            new_property.slug = f'{slugify(property.get("Area", "-"))}/{slugify(property.get("Location", "-"))}/{slugify(property_type.get("Subtype1", ""))}/{slugify(new_property.reference)}'

            new_property.price_square_meter = (
                int(new_property.price_sale / new_property.built_area)
                if new_property.built_area
                else 0
            )
            new_property.price_change_percent = (
                (1.0 - (new_property.price_sale_old / new_property.price_sale)) * 100
                if new_property.price_sale and new_property.price_sale_old
                else 0
            )

            ### Many2Many relationships
            all_resales_property_features = property.get("PropertyFeatures", {}).get(
                "Category", []
            )

            # Get specifics from all property features
            new_property.condition = self._get_condition(
                features=all_resales_property_features
            )

            feature_ids = self._get_features(
                features=all_resales_property_features,
                type="Features",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            ### Add other specific features
            # climate control
            has_climate_control = self._get_climate_control(
                features=all_resales_property_features
            )
            if has_climate_control:
                feature_ids.append(123)
            climate_control_ids = self._get_features(
                features=all_resales_property_features,
                type="Climate Control",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if climate_control_ids:
                feature_ids.extend(climate_control_ids)
            # pool
            has_pool = self._get_has_pool(features=all_resales_property_features)
            if has_pool:
                feature_ids.append(40)
            pool_ids = self._get_features(
                features=all_resales_property_features,
                type="Pool",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if pool_ids:
                feature_ids.extend(pool_ids)
            # furniture
            furniture_ids = self._get_features(
                features=all_resales_property_features,
                type="Furniture",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if furniture_ids:
                feature_ids.extend(furniture_ids)
            # kitchen
            kitchen_ids = self._get_features(
                features=all_resales_property_features,
                type="Kitchen",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if kitchen_ids:
                feature_ids.extend(kitchen_ids)
            # security
            security_ids = self._get_features(
                features=all_resales_property_features,
                type="Security",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if security_ids:
                feature_ids.extend(security_ids)
            # parking
            parking_ids = self._get_features(
                features=all_resales_property_features,
                type="Parking",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if parking_ids:
                feature_ids.extend(parking_ids)
            # utilities
            utilities_ids = self._get_features(
                features=all_resales_property_features,
                type="Utilities",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if utilities_ids:
                feature_ids.extend(utilities_ids)
            # rentals
            rentals_ids = self._get_features(
                features=all_resales_property_features,
                type="Rentals",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            if rentals_ids:
                feature_ids.extend(rentals_ids)

            orientation_ids = self._get_features(
                features=all_resales_property_features,
                type="Orientation",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            setting_ids = self._get_features(
                features=all_resales_property_features,
                type="Setting",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )
            view_ids = self._get_features(
                features=all_resales_property_features,
                type="Views",
                all_features=all_features,
                all_views=all_views,
                all_settings=all_settings,
                all_orientations=all_orientations,
            )

            # Flushes new_property so we can have its `id`
            self.db_session.add(new_property)
            self.db_session.flush()

            # Set their relationships to the property
            if feature_ids:
                self._handle_property_many_to_many_relationship(
                    property_id=new_property.id,
                    selected_ids=feature_ids,
                    table=PropertyFeature,
                    key="feature_id",
                )
            if orientation_ids:
                self._handle_property_many_to_many_relationship(
                    property_id=new_property.id,
                    selected_ids=orientation_ids,
                    table=PropertyOrientation,
                    key="orientation_id",
                )
            if setting_ids:
                self._handle_property_many_to_many_relationship(
                    property_id=new_property.id,
                    selected_ids=setting_ids,
                    table=PropertySetting,
                    key="setting_id",
                )
            if view_ids:
                self._handle_property_many_to_many_relationship(
                    property_id=new_property.id,
                    selected_ids=view_ids,
                    table=PropertyView,
                    key="view_id",
                )

            # property listing type
            self._handle_property_many_to_many_relationship(
                property_id=new_property.id,
                selected_ids=[listing_type_id],
                table=PropertyListingType,
                key="listing_type_id",
            )

            ### Description
            description = property.get("Description", None)
            if description:
                transformed_description = self._transform_description(
                    description=description
                )

                existent_property_description = self.db_session.scalars(
                    select(PropertyDescription).where(
                        and_(
                            PropertyDescription.property_id == new_property.id,
                            PropertyDescription.language == Language.ENGLISH,
                        )
                    )
                ).first()

                if existent_property_description:
                    existent_property_description.description = transformed_description
                    existent_property_description.tagline = title
                    existent_property_description.type = DescriptionType.FULL
                    existent_property_description.language = Language.ENGLISH
                else:
                    # add to property_description
                    new_property_description = PropertyDescription(
                        property_id=new_property.id,
                        tagline=title,
                        description=transformed_description,
                        type=DescriptionType.FULL,
                        language=Language.ENGLISH,
                    )
                    self.db_session.add(new_property_description)

            ### Images
            pictures = property.get("Pictures", {}).get("Picture", [])
            # Delete all current images from property
            self.db_session.execute(
                delete(Image).where(Image.property_id == new_property.id)
            )
            images = [
                Image(
                    url=picture["PictureURL"].split("?")[0],
                    order=index,
                    is_hidden=False,
                    property_id=new_property.id,
                )
                for index, picture in enumerate(pictures)
            ]
            self.db_session.add_all(images)
            self.db_session.commit()

        return n_changed_properties

    def _get_area_ids(self, *, location: str | None):
        area_level_1 = None
        area_level_2 = None
        area_level_3 = None
        area_level_4 = None
        area_level_5 = None

        if not location:
            return (
                area_level_1,
                area_level_2,
                area_level_3,
                area_level_4,
                area_level_5,
            )

        # Prepare areas
        area5 = self.db_session.scalar(
            select(AreaLevel5).where(AreaLevel5.name == location)
        )
        area4 = self.db_session.scalar(
            select(AreaLevel4).where(AreaLevel4.name == location)
        )
        area3 = self.db_session.scalar(
            select(AreaLevel3).where(AreaLevel3.name == location)
        )
        area2 = self.db_session.scalar(
            select(AreaLevel2).where(AreaLevel2.name == location)
        )
        area1 = self.db_session.scalar(
            select(AreaLevel1).where(AreaLevel1.name == location)
        )

        if area5:
            area_level_5 = area5.id
            area_level_4 = area5.area_level_4_id
            area_level_3 = area5.area_level_4.area_level_3_id
            area_level_2 = area5.area_level_4.area_level_3.area_level_2_id
            area_level_1 = area5.area_level_4.area_level_3.area_level_2.area_level_1_id

        if area4:
            area_level_4 = area4.id
            area_level_3 = area4.area_level_3_id
            area_level_2 = area4.area_level_3.area_level_2_id
            area_level_1 = area4.area_level_3.area_level_2.area_level_1_id

        if area3:
            area_level_3 = area3.id
            area_level_2 = area3.area_level_2_id
            area_level_1 = area3.area_level_2.area_level_1_id

        if area2:
            area_level_2 = area2.id
            area_level_1 = area2.area_level_1_id

        if area1:
            area_level_1 = area1.id

        return (area_level_1, area_level_2, area_level_3, area_level_4, area_level_5)

    def _get_property_type_id(
        self,
        *,
        category: str,
        property_type: str,
        description: str,
        properties_grouped_by_category: dict[str, list[PropertyTypeRead]],
    ):
        new_dev = "New Development:" in description
        origin = "New development" if new_dev else "Resales"

        category_name_from_db = "Houses"
        default = "House"

        # get right category naming (mapping from resales to our DB)
        match category:
            case "Apartment":
                category_name_from_db = "Apartments"
                default = "Apartment"
            case "House":
                category_name_from_db = "Houses"
                default = "House"
            case "Plot":
                category_name_from_db = "Plots and Lands"
                default = "Plot"
            case "Commercial":
                category_name_from_db = "Commercials"
                default = "Commercial Other"

        properties_of_category = []
        key = f"{origin},{category_name_from_db}"
        if key in properties_grouped_by_category:
            properties_of_category = properties_grouped_by_category[key]

        reversed_property_type = property_type.split(" ")
        reversed_property_type.reverse()
        reversed_property_type = " ".join(reversed_property_type)

        found = [
            property
            for property in properties_of_category
            if property_type.lower() == property.property_type.lower()  # compares
            or reversed_property_type.lower()
            == property.property_type.lower()  # compares penthouse duplex == duplex penthouse
            or property_type.lower()
            == property.property_type.lower().replace(
                " ", ""
            )  # compares townhouse == town house
            or property_type.lower().replace("-", " ")
            == property.property_type.lower()  # compares semi-detached house == semi detached house
            or property_type.lower().split(" - ")[0]
            == property.property_type.lower()  # compares finca - cortijo == finca
        ]
        if found:
            return found[0].id

        # not found, return default based on category
        default_found = [
            property
            for property in properties_of_category
            if default.lower() == property.property_type.lower()
        ]
        return default_found[0].id

    def _get_climate_control(self, *, features: list) -> bool:
        for feature in features:
            if feature["Type"] == "Climate Control":
                for value in feature["Value"]:
                    if value in {"Air Conditioning", "Cold A/C", "Pre Installed A/C"}:
                        return True
        return False

    def _get_has_pool(self, *, features: list) -> bool:
        for feature in features:
            if feature["Type"] == "Pool":
                for value in feature["Value"]:
                    if value in {
                        "Communal Pool",
                        "Private Pool",
                        "Indoor Pool",
                        "Heated Pool",
                    }:
                        return True
        return False

    def _get_condition(self, *, features: list) -> str:
        maps = {
            "Condition": {
                "Excellent Condition": ConditionType.EXCELLENT,
                "Good Condition": ConditionType.GOOD,
                "Fair Condition": ConditionType.FAIR,
                "Renovation Required": ConditionType.RENOVATION_REQUIRED,
                "Restoration Required": ConditionType.RESTORATION_REQUIRED,
            }
        }

        for item in features:
            if item["Type"] == "Condition":
                for condition in item["Value"]:
                    return (
                        maps["Condition"][condition]
                        if condition in maps["Condition"]
                        else ConditionType.GOOD
                    )

        return ConditionType.GOOD

    def _get_features(
        self,
        *,
        features: list,
        type: Literal[
            "Condition",
            "Features",
            "Orientation",
            "Setting",
            "Views",
            "Rentals",
            "Climate Control",
            "Kitchen",
            "Pool",
            "Furniture",
            "Parking",
            "Security",
            "Utilities",
        ],
        all_features: Sequence[Feature],
        all_views: Sequence[View],
        all_settings: Sequence[Setting],
        all_orientations: Sequence[Orientation],
    ) -> list[int]:
        def _get_id(
            all: (
                Sequence[Feature]
                | Sequence[View]
                | Sequence[Setting]
                | Sequence[Orientation]
            ),
            name: str,
        ) -> int:
            try:
                return next(item.id for item in all if item.name == name)
            except StopIteration:
                raise Exception(f"feature/view/setting/orientation {name} not found")

        maps = {
            "Features": {
                "24 Hour Reception": _get_id(all_features, "24h Service"),
                "ADSL / WIFI": _get_id(all_features, "Internet - Wifi"),
                "Bar": _get_id(all_features, "Bar"),
                "Barbeque": _get_id(all_features, "Barbeque"),
                "Basement": _get_id(all_features, "Basement"),
                "Covered Terrace": _get_id(all_features, "Covered Terrace"),
                "Disabled Access": _get_id(all_features, "Disabled Access"),
                "Double Glazing": _get_id(all_features, "Double Glazing"),
                "Ensuite Bathroom": _get_id(all_features, "Ensuite Bathroom"),
                "Fiber Optic": _get_id(all_features, "Fiber Optic"),
                "Fitted Wardrobes": _get_id(all_features, "Fitted Wardrobes"),
                "Games Room": _get_id(all_features, "Games Room"),
                "Guest Apartment": _get_id(all_features, "Guest Apartment"),
                "Guest House": _get_id(all_features, "Guest House"),
                "Gym": _get_id(all_features, "Gym"),
                "Handicap access": _get_id(all_features, "Handicap Accessible"),
                "Jacuzzi": _get_id(all_features, "Jacuzzi"),
                "Lift": _get_id(all_features, "Lift"),
                "Marble Flooring": _get_id(all_features, "Marble floors"),
                "Near Church": _get_id(all_features, "Near Church"),
                "Near Transport": _get_id(all_features, "Near Transport"),
                "Paddle Tennis": _get_id(all_features, "Paddle Tennis"),
                "Private Terrace": _get_id(all_features, "Private Terrace"),
                "Restaurant On Site": _get_id(all_features, "Restaurant On Site"),
                "Satellite TV": _get_id(all_features, "Satellite TV"),
                "Sauna": _get_id(all_features, "Sauna"),
                "Solarium": _get_id(all_features, "Solarium"),
                "Staff Accommodation": _get_id(all_features, "Staff Accommodation"),
                "Storage Room": _get_id(all_features, "Storage Room"),
                "Tennis Court": _get_id(all_features, "Tennis Court"),
                "Utility Room": _get_id(all_features, "Utility Room"),
                "Wood Flooring": _get_id(all_features, "Wooden floors"),
            },
            "Orientation": {
                "North": _get_id(all_orientations, "North"),
                "North East": _get_id(all_orientations, "North East"),
                "East": _get_id(all_orientations, "East"),
                "South East": _get_id(all_orientations, "South East"),
                "South": _get_id(all_orientations, "South"),
                "South West": _get_id(all_orientations, "South West"),
                "West": _get_id(all_orientations, "West"),
                "North West": _get_id(all_orientations, "North West"),
            },
            "Setting": {
                "Beachfront": _get_id(all_settings, "Beachfront"),
                "Beachside": _get_id(all_settings, "Beachside"),
                "Close To Golf": _get_id(all_settings, "Close to golf"),
                "Close To Marina": _get_id(all_settings, "Close to marina"),
                "Close To Port": _get_id(all_settings, "Close to port"),
                "Close To Schools": _get_id(all_settings, "Close to schools"),
                "Close To Sea": _get_id(all_settings, "Close to sea"),
                "Close To Shops": _get_id(all_settings, "Close to shops"),
                "Close To Town": _get_id(all_settings, "Close to town"),
                "Country": _get_id(all_settings, "Country"),
                "Frontline Golf": _get_id(all_settings, "Frontline golf"),
                "Marina": _get_id(all_settings, "Marina"),
                "Mountain Pueblo": _get_id(all_settings, "Mountain pueblo"),
                "Urbanisation": _get_id(all_settings, "Urbanisation"),
            },
            "Views": {
                "Country": _get_id(all_views, "Country"),
                "Garden": _get_id(all_views, "Garden"),
                "Mountain": _get_id(all_views, "Mountain"),
                "Panoramic": _get_id(all_views, "Panoramic"),
                "Pool": _get_id(all_views, "Pool"),
                "Port": _get_id(all_views, "Port"),
                "Sea": _get_id(all_views, "Sea"),
                "Street": _get_id(all_views, "Street"),
                "Urban": _get_id(all_views, "Urban"),
            },
            "Climate Control": {
                "Air Conditioning": _get_id(all_features, "Air Conditioning"),
                "Central Heating": _get_id(all_features, "Central Heating"),
                "Fireplace": _get_id(all_features, "Fireplace"),
            },
            "Pool": {
                "Communal Pool": _get_id(all_features, "Community Pool"),
            },
            "Furniture": {
                "Fully Furnished": _get_id(all_features, "Fully Furnished"),
                "Not Furnished": _get_id(all_features, "Unfurnished"),
                "Optional Furniture": _get_id(all_features, "Optional Furniture"),
                "Part Furnished": _get_id(all_features, "Partly Furnished"),
            },
            "Kitchen": {
                "Fully Fitted Kitchen": _get_id(all_features, "Fully fitted kitchen"),
                "Partially Fitted Kitchen": _get_id(all_features, "Fitted Kitchen"),
            },
            "Security": {
                "Electric Blinds": _get_id(all_features, "Electric Blinds"),
                "Alarm System": _get_id(all_features, "Alarm System"),
                "24 Hour Security": _get_id(all_features, "24h Security Service"),
            },
            "Parking": {
                "Multiple Parking Spaces": _get_id(all_features, "Parking Spaces")
            },
            "Utilities": {
                "Telephone": _get_id(all_features, "Telephone"),
                "Photovoltaic solar panels": _get_id(all_features, "Solar Panels"),
            },
            "Rentals": {"Pets Allowed": _get_id(all_features, "Pets Allowed")},
        }
        result = []
        for item in features:
            if item["Type"] == type:
                for value in item["Value"]:
                    if value in maps[type]:
                        result.append(maps[type][value])
        return result

    def _transform_description(self, *, description: str):
        result = re.sub(r"/\b[^\s]+@[^\s]+/", "", description)
        result = re.sub(
            r"/[0-9]{3}[\-][0-9]{6}|[0-9]{3}[\s][0-9]{6}|[0-9]{3}[\s][0-9]{3}[\s][0-9]{4}|[0-9]{9}|[0-9]{3}[\-][0-9]{3}[\-][0-9]{4}|[0-9]{4}[\s][0-9]{3}[\s][0-9]{6}/",
            "",
            description,
        )
        return result

    def _handle_property_many_to_many_relationship(
        self, *, property_id: int, selected_ids: list[int], table, key: str
    ):
        current_ids = self.db_session.scalars(
            select(getattr(table, key)).where(table.property_id == property_id)
        ).all()

        for selected_id in current_ids:
            if selected_id not in selected_ids:
                self.db_session.execute(
                    delete(table).where(
                        and_(
                            getattr(table, "property_id") == property_id,
                            getattr(table, key) == selected_id,
                        )
                    )
                )

        for selected_id in selected_ids:
            if selected_id not in current_ids:
                model_item = table()
                setattr(model_item, key, selected_id)
                model_item.property_id = property_id
                self.db_session.add(model_item)

    F = TypeVar("F", bound=BaseModel)

    def _generate_features_mapping(self, *, model: F) -> Sequence[F]:
        return self.db_session.scalars(select(model)).all()

    def save_to_s3(self, *, properties, filter_id) -> None:
        timestamp = datetime.now(UTC).strftime("%Y%m%d_%H%M%S")
        filename = f"resales_properties_debug_{filter_id}_{timestamp}.json"
        json_content = json.dumps(properties, indent=4, ensure_ascii=False)

        with open(filename, "w", encoding="utf-8") as f:
            f.write(json_content)
            logger.info(
                f"Successfully saved {len(properties)} properties to {filename}",
            )

        bucket_name = app_cfg.aws_s3_files_bucket_name
        s3_service = S3Service(app_cfg, bucket_name)
        s3_key = f"resales/{filename}"

        if s3_service.exists(s3_key):
            logger.info(
                f"File {s3_key} already exists in S3 bucket {bucket_name}, skipping upload.",
            )
            return

        with open(filename, "rb") as file:
            s3_service.upload(path=s3_key, file=file, content_type="application/json")

        logger.info(
            f"Successfully uploaded {filename} to S3 bucket {bucket_name} at {s3_key}",
        )

    def run(self, get_all: bool = False):
        ### Creates request_id to log the process
        now = datetime.now(UTC)
        now_str = now.strftime("%d/%m/%Y %H:%M:%S")
        request_id = f"[{now_str}]_integrations_pulling_resalesonline_get_all_{get_all}"
        logger.info(
            "Started pulling of information from ResalesOnline",
            extra={
                "tag": "ResalesOnlinePull",
                "requestId": request_id,
            },
        )

        ### Get all property types
        all_property_types = [
            PropertyTypeRead.model_validate(property_type)
            for property_type in self.db_session.scalars(select(PropertyType)).all()
        ]

        all_resales_apartments = []
        all_newdev_apartments = []
        all_resales_houses = []
        all_newdev_houses = []
        all_resales_plots = []
        all_newdev_plots = []
        all_resales_commercials = []
        all_newdev_commercials = []

        for item in all_property_types:
            if item.category == "Apartments" and item.origin == "Resales":
                all_resales_apartments.append(item)
            if item.category == "Apartments" and item.origin == "New development":
                all_newdev_apartments.append(item)
            if item.category == "Houses" and item.origin == "Resales":
                all_resales_houses.append(item)
            if item.category == "Houses" and item.origin == "New development":
                all_newdev_houses.append(item)
            if item.category == "Plots and Lands" and item.origin == "Resales":
                all_resales_plots.append(item)
            if item.category == "Plots and Lands" and item.origin == "New development":
                all_newdev_plots.append(item)
            if item.category == "Commercials" and item.origin == "Resales":
                all_resales_commercials.append(item)
            if item.category == "Commercials" and item.origin == "New development":
                all_newdev_commercials.append(item)
            if item.category == "Country Properties" and item.origin == "Resales":
                all_resales_houses.append(item)
            if (
                item.category == "Country Properties"
                and item.origin == "New development"
            ):
                all_newdev_houses.append(item)
            if item.category == "Villas" and item.origin == "Resales":
                all_resales_houses.append(item)
            if item.category == "Villas" and item.origin == "New development":
                all_newdev_houses.append(item)

        properties_grouped_by_category: dict[str, list[PropertyTypeRead]] = {
            "Resales,Apartments": all_resales_apartments,
            "New development,Apartments": all_newdev_apartments,
            "Resales,Houses": all_resales_houses,
            "New development,Houses": all_newdev_houses,
            "Resales,Plots and Lands": all_resales_plots,
            "New development,Plots and Lands": all_newdev_plots,
            "Resales,Commercials": all_resales_commercials,
            "New development,Commercials": all_newdev_commercials,
        }

        all_features = self._generate_features_mapping(model=Feature)
        all_views = self._generate_features_mapping(model=View)
        all_settings = self._generate_features_mapping(model=Setting)
        all_orientations = self._generate_features_mapping(model=Orientation)

        self.api_key = app_cfg.resale_online_api_key
        self.contact_id = app_cfg.resale_online_contact_id

        start = datetime.now()
        own_properties = self.get_only_own_properties()
        end = datetime.now()
        logger.info(
            "Got %s own properties, duration: %s seconds",
            len(own_properties),
            (end - start).total_seconds(),
            extra={
                "tag": "ResalesOnlinePull",
                "requestId": request_id,
            },
        )

        """
          Filter defined in the API Keys in ResalesOnline.
          Means:
          - Country: Spain
          - Province/Area: Costa del Sol
          - Only Resales properties
          - Price range: 200_000 - 1_000_000_000
          - Status: Available, Under Offer, Sale Agreed
          - ✅ Resales Properties (everything: own properties and from 3rd real estates)
        """
        filter_id = 5

        start = datetime.now()
        (query_id, property_count) = self.get_query_id_and_count(filter_id=filter_id)
        end = datetime.now()
        logger.info(
            "Will import %s properties, duration: %s seconds",
            property_count - len(own_properties),
            (end - start).total_seconds(),
            extra={
                "tag": "ResalesOnlinePull",
                "requestId": request_id,
            },
        )

        start = datetime.now()
        self.get_properties_and_save_them_in_database(
            query_id=query_id,
            total_count=property_count,
            filter_id=filter_id,
            get_all=get_all,
            properties_grouped_by_category=properties_grouped_by_category,
            all_features=all_features,
            all_views=all_views,
            all_settings=all_settings,
            all_orientations=all_orientations,
            own_properties=own_properties,
            request_id=request_id,
        )
        end = datetime.now()
        logger.info(
            "Finished saving properties to DB, duration: %s seconds",
            (end - start).total_seconds(),
            extra={
                "tag": "ResalesOnlinePull",
                "requestId": request_id,
            },
        )
        if get_all:
            # deletes all resalesonline properties (that weren't just updated) from the DB
            # (because some of them may not be valid anymore or they went to different statuses, like Off Market)
            start = datetime.now()
            n_properties_deleted = self._delete_resalesonline_properties_from_database(
                now
            )
            end = datetime.now()
            logger.info(
                "Deleted %s properties, duration: %s seconds",
                n_properties_deleted,
                (end - start).total_seconds(),
                extra={
                    "tag": "ResalesOnlinePull",
                    "requestId": request_id,
                },
            )
