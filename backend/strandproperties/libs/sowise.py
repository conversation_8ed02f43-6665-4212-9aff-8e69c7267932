import requests
from pydantic import BaseModel
from requests_toolbelt import MultipartEncoder

from strandproperties.config import app_cfg
from strandproperties.constants import DocumentType
from strandproperties.logger import logger
from strandproperties.schemas.sowise import AddSignee, DocumentConvert, DocumentRead


class SowiseClient:
    def __init__(
        self,
        access_key: str,
        base_url: str,
        selected_company: str,
        api_key: str = "",
    ) -> None:
        self.base_url = base_url
        self.application = "Strand"
        self.headers = {
            "X-Application-Id": self.application,
            "X-Access-Key": access_key,
            "X-Selected-Company": selected_company,  # TODO: this is done to select the right company in the test environment
        }
        if api_key:
            self.set_api_key(api_key)

    def set_api_key(self, api_key):
        del self.headers["X-Access-Key"]
        self.headers["X-Api-Key"] = api_key

    def _validate_headers(self):
        if "X-Api-Key" not in self.headers:
            raise Exception("API key not set")

    def create_account(self, owner_email: str) -> dict:
        url = f"{self.base_url}/authentication/create-account"
        r = requests.post(
            url,
            json={
                "email": owner_email,
                "integration": self.application,
            },
            headers=self.headers,
        )
        r.raise_for_status()

        return r.json()

    def create_company(self, name: str) -> dict:
        self._validate_headers()
        url = f"{self.base_url}/authentication/create-company"
        r = requests.post(
            url,
            json={"name": name},
            headers=self.headers,
        )
        r.raise_for_status()

        return r.json()

    def delete_document(self, document_id: str) -> dict:
        self._validate_headers()
        url = f"{self.base_url}/documents/delete/permanently"
        r = requests.post(
            url,
            json={"documentId": document_id},
            headers=self.headers,
        )
        r.raise_for_status()

        return r.json()

    def convert_document(self, template_id: str, params: DocumentConvert) -> dict:
        self._validate_headers()
        url = f"{self.base_url}/documents/convert/{template_id}"
        r = requests.post(
            url,
            json={
                "name": params.name,
                "includeChildren": params.include_children,
                "parent": params.parent,
            },
            headers=self.headers,
        )
        r.raise_for_status()

        return DocumentRead(**r.json())

    def get_document_info(self, document_id: str):
        self._validate_headers()
        url = f"{self.base_url}/documents/info/{document_id}"

        r = requests.get(
            url,
            headers=self.headers,
        )

        r.raise_for_status()
        return r.json()

    def fill_document(self, document_id: str, data: dict) -> dict:
        """
        The /documents/metadata/{docId} endpoint only accepts values as strings
        """
        self._validate_headers()
        url = f"{self.base_url}/documents/metadata/{document_id}"

        # If the body has arrays and objects together, the Sowise endpoint will raise an error.
        # It also looks like it only accepts string as value
        payload = {"data": {"strand": {**data}}}

        r = requests.post(
            url,
            json=payload,
            headers=self.headers,
        )
        r.raise_for_status()

        return r.json()

    def add_signee(self, document_id: str, params: AddSignee) -> dict:
        self._validate_headers()
        url = f"{self.base_url}/signing/signee/{document_id}"
        r = requests.post(
            url,
            json={
                "givenName": params.given_name,
                "familyName": params.family_name,
                "email": params.email,
                "level": params.level,
            },
            headers=self.headers,
        )
        r.raise_for_status()

        return r.json()

    def upload_document(
        self,
        *,
        name: str,
        filename: str,
        file,
        content_type: str,
        type: str = "file",
        folder: str = app_cfg.sowise_folder_generic,
    ) -> dict:
        self._validate_headers()
        url = f"{self.base_url}/documents/upload/{type}/{folder}"
        logger.info(f"upload document to folder {folder} with type is: {type}")

        multipart_data = MultipartEncoder(
            fields={
                "file": (
                    filename,
                    file,
                    content_type,
                ),
                "name": name,
            }
        )

        headers = {**self.headers, "Content-Type": multipart_data.content_type}

        r = requests.post(
            url,
            data=multipart_data,
            headers=headers,
        )
        r.raise_for_status()
        logger.info("response when upload file to sowise: %s", r.json())

        return r.json()

    def document_rename(self, document_id, new_name):
        self._validate_headers()
        url = f"{self.base_url}/documents/rename"

        r = requests.post(
            url,
            headers=self.headers,
            json={"documentId": document_id, "name": new_name},
        )

        r.raise_for_status()
        return r.json()
