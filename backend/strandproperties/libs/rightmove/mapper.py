from enum import Enum
from typing import List, cast

from strandproperties.constants import ConditionType, Language, ListingTypeEnum
from strandproperties.models.image import Image
from strandproperties.models.property import Property, PropertyDescription
from strandproperties.models.video import Video


class RightMovePropertyTypeEnum(int, Enum):
    NOT_SPECIFIED = 0
    TERRACED_HOUSE = 1
    END_OF_TERRACE_HOUSE = 2
    SEMI_DETACHED_HOUSE = 3
    DETACHED_HOUSE = 4
    MEWS_HOUSE = 5
    CLUSTER_HOUSE = 6
    GROUND_FLOOR_FLAT = 7
    FLAT = 8
    STUDIO_FLAT = 9
    GROUND_FLOOR_MAISONETTE = 10
    MAISONETTE = 11
    BUNGALOW = 12
    TERRACED_BUNGALOW = 13
    SEMI_DETACHED_BUNGALOW = 14
    DETACHED_BUNGALOW = 15
    MOBILE_HOME = 16
    LAND = 20
    LINK_DETACHED_HOUSE = 21
    TOWN_HOUSE = 22
    COTTAGE = 23
    CHALET = 24
    CHARACTER_PROPERTY = 25
    HOUSE_UNSPECIFIED = 26
    VILLA = 27
    APARTMENT = 28
    PENTHOUSE = 29
    FINCA = 30
    BARN_CONVERSION = 43
    SERVICED_APARTMENT = 44
    PARKING = 45
    SHELTERED_HOUSING = 46
    RETIREMENT_PROPERTY = 47
    HOUSE_SHARE = 48
    FLAT_SHARE = 49
    PARK_HOME = 50
    GARAGES = 51
    FARM_HOUSE = 52
    EQUESTRIAN_FACILITY = 53
    DUPLEX = 56
    TRIPLEX = 59
    LONGERE = 62
    GITE = 65
    BARN = 68
    TRULLI = 71
    MILL = 74
    RUINS = 77
    RESTAURANT = 80
    CAFE = 83
    CASTLE = 92
    VILLAGE_HOUSE = 95
    CAVE_HOUSE = 101
    CORTIJO = 104
    FARM_LAND = 107
    PLOT = 110
    COUNTRY_HOUSE = 113
    STONE_HOUSE = 116
    CARAVAN = 117
    LODGE = 118
    LOG_CABIN = 119
    MANOR_HOUSE = 120
    STATELY_HOME = 121
    OFF_PLAN = 125
    SEMI_DETACHED_VILLA = 128
    DETACHED_VILLA = 131
    BAR_NIGHTCLUB = 134
    SHOP = 137
    RIAD = 140
    HOUSE_BOAT = 141
    HOTEL_ROOM = 142
    BLOCK_OF_APARTMENTS = 143
    PRIVATE_HALLS = 144
    OFFICE = 178
    BUSINESS_PARK = 181
    SERVICED_OFFICE = 184
    RETAIL_PROPERTY_HIGH_STREET = 187
    RETAIL_PROPERTY_OUT_OF_TOWN = 190
    CONVENIENCE_STORE = 193
    HAIRDRESSER_BARBER_SHOP = 199
    HOTEL = 202
    PETROL_STATION = 205
    POST_OFFICE = 208
    PUB = 211
    WORKSHOP_RETAIL_SPACE = 214
    DISTRIBUTION_WAREHOUSE = 217
    FACTORY = 220
    HEAVY_INDUSTRIAL = 223
    INDUSTRIAL_PARK = 226
    LIGHT_INDUSTRIAL = 229
    STORAGE = 232
    SHOWROOM = 235
    WAREHOUSE = 238
    COMMERCIAL_DEVELOPMENT = 244
    INDUSTRIAL_DEVELOPMENT = 247
    RESIDENTIAL_DEVELOPMENT = 250
    COMMERCIAL_PROPERTY = 253
    DATA_CENTRE = 256
    FARM = 259
    HEALTHCARE_FACILITY = 262
    MARINE_PROPERTY = 265
    MIXED_USE = 268
    RESEARCH_DEVELOPMENT_FACILITY = 271
    SCIENCE_PARK = 274
    GUEST_HOUSE = 277
    HOSPITALITY = 280
    LEISURE_FACILITY = 283
    TAKEAWAY = 298
    CHILDCARE_FACILITY = 301
    SMALLHOLDING = 304
    PLACE_OF_WORSHIP = 307
    TRADE_COUNTER = 310
    COACH_HOUSE = 511
    HOUSE_OF_MULTIPLE_OCCUPATION = 512
    SPORTS_FACILITIES = 535
    SPA = 538
    CAMPSITE_HOLIDAY_VILLAGE = 541
    RETAIL_PROPERTY_SHOPPING_CENTRE = 544
    RETAIL_PROPERTY_PARK = 547
    RETAIL_PROPERTY_POP_UP = 550


class MediaTypeEnum(int, Enum):
    IMAGE = 1
    FLOORPLAN = 2
    BROCHURE = 3
    VIRTUAL_TOUR = 4
    AUDIO_TOUR = 5
    EPC = 6
    EPC_GRAPH = 7


"""
Maps from Property Type (Strand) to LeadingRE property types:
"""
_property_type_mapper = {
    "Apartment": RightMovePropertyTypeEnum.APARTMENT.value,
    "Bar": RightMovePropertyTypeEnum.BAR_NIGHTCLUB.value,
    "Boat": RightMovePropertyTypeEnum.HOUSE_BOAT.value,
    "Building": RightMovePropertyTypeEnum.NOT_SPECIFIED.value,
    "Bungalow": RightMovePropertyTypeEnum.BUNGALOW.value,
    "Business": RightMovePropertyTypeEnum.BUSINESS_PARK.value,
    "Castle": RightMovePropertyTypeEnum.CASTLE.value,
    "Chalet": RightMovePropertyTypeEnum.CHALET.value,
    "Commercial Other": RightMovePropertyTypeEnum.COMMERCIAL_DEVELOPMENT.value,
    "Commercial Premises": RightMovePropertyTypeEnum.COMMERCIAL_PROPERTY.value,
    "Cortijo": RightMovePropertyTypeEnum.CORTIJO.value,
    "Country House": RightMovePropertyTypeEnum.COUNTRY_HOUSE.value,
    "Development Land": RightMovePropertyTypeEnum.LAND.value,
    "Discotheque": RightMovePropertyTypeEnum.BAR_NIGHTCLUB.value,
    "Duplex": RightMovePropertyTypeEnum.DUPLEX.value,
    "Duplex Penthouse": RightMovePropertyTypeEnum.PENTHOUSE.value,
    "Estate": RightMovePropertyTypeEnum.HOUSE_UNSPECIFIED.value,
    "Finca": RightMovePropertyTypeEnum.LAND.value,
    "Flat": RightMovePropertyTypeEnum.FLAT.value,
    "Golf Course": RightMovePropertyTypeEnum.LAND.value,
    "Golf Plot": RightMovePropertyTypeEnum.LAND.value,
    "Ground Floor Apartment": RightMovePropertyTypeEnum.GROUND_FLOOR_FLAT.value,
    "Ground Floor Duplex": RightMovePropertyTypeEnum.GROUND_FLOOR_FLAT.value,
    "Hotel": RightMovePropertyTypeEnum.HOTEL.value,
    "Hotel Plot": RightMovePropertyTypeEnum.LAND.value,
    "House": RightMovePropertyTypeEnum.HOUSE_UNSPECIFIED.value,
    "Industrial Land": RightMovePropertyTypeEnum.LAND.value,
    "Industrial Premises": RightMovePropertyTypeEnum.INDUSTRIAL_PARK.value,
    "Investment": RightMovePropertyTypeEnum.BUSINESS_PARK.value,
    "Loft": RightMovePropertyTypeEnum.APARTMENT.value,
    "Mansion": RightMovePropertyTypeEnum.APARTMENT.value,
    "Mooring": RightMovePropertyTypeEnum.NOT_SPECIFIED.value,
    "Office": RightMovePropertyTypeEnum.OFFICE.value,
    "Office Units": RightMovePropertyTypeEnum.OFFICE.value,
    "Palace": RightMovePropertyTypeEnum.CASTLE.value,
    "Parking": RightMovePropertyTypeEnum.PARKING.value,
    "Penthouse": RightMovePropertyTypeEnum.PENTHOUSE.value,
    "Plot": RightMovePropertyTypeEnum.PLOT.value,
    "Residential Plot": RightMovePropertyTypeEnum.RESIDENTIAL_DEVELOPMENT.value,
    "Restaurant": RightMovePropertyTypeEnum.RESTAURANT.value,
    "Riad": RightMovePropertyTypeEnum.RIAD.value,
    "Rustic Plot": RightMovePropertyTypeEnum.LAND.value,
    "Semi Detached House": RightMovePropertyTypeEnum.SEMI_DETACHED_HOUSE.value,
    "Semi Detached Villa": RightMovePropertyTypeEnum.SEMI_DETACHED_VILLA.value,
    "Shop": RightMovePropertyTypeEnum.SHOP.value,
    "Shopping Centre": RightMovePropertyTypeEnum.COMMERCIAL_PROPERTY.value,
    "Store Room": RightMovePropertyTypeEnum.STORAGE.value,
    "Studio": RightMovePropertyTypeEnum.STUDIO_FLAT.value,
    "Supermarket": RightMovePropertyTypeEnum.COMMERCIAL_PROPERTY.value,
    "Town House": RightMovePropertyTypeEnum.TOWN_HOUSE.value,
    "Triplex": RightMovePropertyTypeEnum.TRIPLEX.value,
    "Unique Building": RightMovePropertyTypeEnum.NOT_SPECIFIED.value,
    "Villa": RightMovePropertyTypeEnum.VILLA.value,
}


def map_new_home_and_property_type(
    *, property_type_origin: str, property_type_name: str
) -> tuple[bool, int]:
    is_new_home: bool = property_type_origin == "New development"

    right_move_property_type_id = _property_type_mapper[property_type_name]

    return (is_new_home, right_move_property_type_id)


# The ISO-3316 two letter code for the country the property is located in: AD Andorra, AE United Arab Emirates, AF Afghanistan, AG Antigua And Barbuda, AI Anguilla, AL Albania, AM Armenia, AN Netherlands Antilles, AO Angola, AR Argentina, AS American Samoa, AT Austria, AU Australia, AW Aruba, AZ Azerbaijan, BA Bosnia-Herzegovina, BB Barbados, BD Bangladesh, BE Belgium, BF Burkina, BG Bulgaria, BH Bahrain, BI Burundi, BJ Benin, BM Bermuda, BN Brunei, BO Bolivia, BR Brazil, BS The Bahamas, BT Bhutan, BV Bouvetoya, BW Botswana, BY Belarus, BZ Belize, CA Canada, CB Canouan Island, CC Cocos Islands, CD Democratic Republic of the Congo, CF Central African Republic, CG Congo, CH Switzerland, CI Cote d'Ivoire, CK Cook Islands, CL Chile, CM Cameroon, CN China, CO Colombia, CR Costa Rica, CS Serbia and Montenegro, CU Cuba, CV Cape Verde, CX Christmas Island, CY Cyprus, CZ Czech Republic, DE Germany, DJ Djibouti, DK Denmark, DM Dominica, DO Dominican Republic, DU Dubai, DZ Algeria, EC Ecuador, EE Estonia, EG Egypt, ER Eritrea, ES Spain, ET Ethiopia, FI Finland, FJ Fiji, FK Falkland Islands, FM Federated States of Micronesia, FO Faroe Islands, FR France, GA Gabon, GD Grenada, GE Georgia, GF French Guiana, GH Ghana, GI Gibraltar, GL Greenland, GM The Gambia, GN Guinea, GO Goa, GP Guadeloupe, GQ Equatorial Guinea, GR Greece, GS South Georgia and South Sandwich Islands, GT Guatemala, GU Guam, GW Guinea-Bissau, GY Guyana, HM Heard and McDonald Islands, HN Honduras, HR Croatia, HT Haiti, HU Hungary, ID Indonesia, IE Ireland, IL Israel, IM Margarita Island, IN India, IO British Indian Ocean Territory, IQ Iraq, IR Iran, IS Iceland, IT Italy, JM Jamaica, JO Jordan, JP Japan, KE Kenya, KG Kyrgyzstan, KH Cambodia, KI Kiribati, KM Comoros, KN St Kitts and Nevis, KP North Korea, KR South Korea, KW Kuwait, KY Cayman Islands, KZ Kazakhstan, LA Laos, LB Lebanon, LC St Lucia, LI Liechtenstein, LK Sri Lanka, LR Liberia, LS Lesotho, LT Lithuania, LU Luxembourg, LV Latvia, LY Libya, MA Morocco, MC Monaco, MD Moldova, ME Montenegro, MG Madagascar, MH Marshall Islands, MK Macedonia, ML Mali, MM Myanmar, MN Mongolia, MP Northern Mariana Islands, MQ Martinique, MR Mauritania, MS Montserrat, MT Malta, MU Mauritius, MV Maldives, MW Malawi, MX Mexico, MY Malaysia, MZ Mozambique, NA Namibia, NC New Caledonia, NE Niger, NF Norfolk Island, NG Nigeria, NI Nicaragua, NL Netherlands, NO Norway, NP Nepal, NR Nauru, NU Niue, NY Northern Cyprus, NZ New Zealand, OM Oman, PA Panama, PE Peru, PF French Polynesia, PG Papua New Guinea, PH Philippines, PK Pakistan, PL Poland, PM St Pierre and Miquelon, PN Pitcairn Islands, PR Puerto Rico, PT Portugal, PW Palau, PY Paraguay, QA Qatar, RE Reunion, RO Romania, RU Russian Federation, RW Rwanda, SA Saudi Arabia, SB Solomon Islands, SC Seychelles, SD Sudan, SE Sweden, SG Singapore, SH St Helena, SI Slovenia, SJ Svalbard, SK Slovakia, SL Sierra Leone, SM San Marino, SN Senegal, SO Somalia, SP Serbia, SR Suriname, ST Sao Tome and Principe, SV El Salvador, SY Syria, SZ Swaziland, TC Turks And Caicos Islands, TD Chad, TF French Southern and Antarctic Lands, TG Togo, TH Thailand, TJ Tajikistan, TK Tokelau, TL East Timor, TM Turkmenistan, TN Tunisia, TO Tonga, TR Turkey, TT Trinidad And Tobago, TV Tuvalu, TW Taiwan, TZ Tanzania, UA Ukraine, UG Uganda, US United States of America, UY Uruguay, UZ Uzbekistan, VC St Vincent and the Grenadines, VE Venezuela, VG British Virgin Islands, VI Virgin Islands (U.S.A.), VN Vietnam, VU Vanuatu, WF Wallis and Futuna Islands, WS Samoa, XB St Barthelemy, XM St Maarten, YE Yemen, YT Mayotte, ZA South Africa, ZM Zambia, ZW Zimbabwe
country_code_mapper = {"Spain": "ES", "Finland": "FI"}


def map_country_code(country: str) -> str:
    return country_code_mapper.get(country, "ES")


"""
Maps from ConditionType (Strand) to conditions (Right move):
  Condition of the property being sent: 1 Good, 2 Some work needed, 3 Work required throughout, 4 Major renovation required
"""
conditions_mapper = {
    ConditionType.EXCELLENT.value: 1,
    ConditionType.GOOD.value: 1,
    ConditionType.FAIR.value: 2,
    ConditionType.RENOVATION_REQUIRED.value: 3,
    ConditionType.RESTORATION_REQUIRED.value: 4,
}


def map_conditions(condition: str) -> int:
    return conditions_mapper.get(condition, 1)


def property_has_features(*, property: Property, features: List[str]) -> bool:
    has_feature = next(
        (feature for feature in property.features if feature.name in features),
        None,
    )

    return True if has_feature else False


class RightMoveOutsideSpaceEnum(int, Enum):
    BACK_GARDEN = 29
    COMMUNAL_GARDEN = 30
    ENCLOSED_GARDEN = 31
    FRONT_GARDEN = 32
    PRIVATE_GARDEN = 33
    REAR_GARDEN = 34
    TERRACE = 35
    PATIO = 35


TERRACE_FEATURES = [
    "Terrace",
    "Uncovered terrace",
    "Covered Terrace",
    "Private Terrace",
]

ALL_ACCESSIBILITY_ACCESS_FEATURES = ["Disabled Access"]

ALL_ELEVATOR_FEATURES = ["Lift"]


def property_has_elevator(*, property: Property) -> bool:
    has_elevator = property.building_has_elevator
    has_lift = property_has_features(property=property, features=ALL_ELEVATOR_FEATURES)

    if has_elevator or has_lift:
        return True

    return False


class RightMoveAccessibilityEnum(int, Enum):
    NOT_SUITABLE_FOR_WHEELCHAIR = 42
    LEVEL_ACCESS = 37
    LIFT_ACCESS = 38
    RAMPED_ACCESS = 39
    WET_ROOM = 40
    WIDE_DOORWAYS = 41


def map_accessibility(*, property: Property) -> list[int]:
    accessibility = []
    if property_has_elevator(property=property):
        accessibility.append(RightMoveAccessibilityEnum.LIFT_ACCESS.value)
    if property_has_features(
        property=property, features=ALL_ACCESSIBILITY_ACCESS_FEATURES
    ):
        accessibility.append(RightMoveAccessibilityEnum.RAMPED_ACCESS.value)

    return accessibility


ALL_GOLF_FEATURES = [
    "Front Line Golf",
    "Golf view",
    "Close to Golf",
    "Inside Golf Resort",
]

ALL_BEACH_FEATURES = ["Front Line Beach", "Beachside", "Close to Sea/Beach"]

ALL_SEA_VIEWS = ["Sea view", "Partial sea views"]

AIR_CONDITIONING_FEATURES = ["Air Conditioning", "Individual A/C Units"]


ALL_SECURITY_SYSTEM_FEATURES = [
    "24h Security Service",
    "Security Entrance",
    "Security Shutters",
    "Surveillance Cameras",
    "Alarm System",
    "Alarm",
]

ALL_GATED_ENTRY_FEATURES = ["Gated community"]

ALL_BALCONY_FEATURES = ["Balcony"]


ALL_GROUND_FLOOR_TERRACE_FEATURES = ["Ground floor patio"]

ALL_LOG_FIREPLACE_FEATURES = ["Fireplace"]


def get_property_price(*, property: Property) -> int:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]
    if ListingTypeEnum.SALE in listing_types_names:
        if property.price_sale is None:
            raise Exception("Price is required")
        return property.price_sale
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        if property.price_rent_long_term is None:
            raise Exception("Price is required")
        return property.price_rent_long_term
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        if property.price_rent_short_term is None:
            raise Exception("Price is required")
        return property.price_rent_short_term
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


def get_outside_spaces(*, property: Property) -> list[int]:
    outside_spaces = []

    garden_types_names = [garden.name for garden in property.garden_types]
    has_private_garden = "Private" in garden_types_names
    has_communal_garden = "Communal" in garden_types_names

    if has_private_garden:
        outside_spaces.append(RightMoveOutsideSpaceEnum.PRIVATE_GARDEN.value)
    if has_communal_garden:
        outside_spaces.append(RightMoveOutsideSpaceEnum.COMMUNAL_GARDEN.value)
    if property_has_features(property=property, features=TERRACE_FEATURES):
        outside_spaces.append(RightMoveOutsideSpaceEnum.TERRACE.value)

    return outside_spaces


def get_images_media(images: list[Image]):
    return [
        {
            "media_type": MediaTypeEnum.IMAGE.value,
            "media_url": image.url[:250],
            "caption": None,
            "sort_order": image.order,
            "media_update_date": (
                image.updated_at.strftime("%d-%m-%Y %H:%M:%S")
                if image.updated_at
                else None
            ),
        }
        for image in images
    ]


def get_videos_media(videos: list[Video]):
    return [
        {
            "media_type": MediaTypeEnum.VIRTUAL_TOUR.value,
            "media_url": video.url[:250],
            "caption": None,
            "sort_order": idx,
            "media_update_date": (
                video.updated_at.strftime("%d-%m-%Y %H:%M:%S")
                if video.updated_at
                else None
            ),
        }
        for idx, video in enumerate(videos)
    ]


def get_rightmove_region_from_province(province: str):
    province_map = {
        "Alicante": "Valencia",
        "Almeria": "Andalucia",
        "Badajoz": "Extremadura",
        "Cadiz": "Andalucia",
        "Castellon": "Valencia",
        "Ciudad Real": "Castilla-La Mancha",
        "Cordoba": "Andalucia",
        "Granada": "Andalucia",
        "Islas Baleares": "Illes Balears",
        "Jaen": "Andalucia",
        "Malaga": "Andalucia",
        "Santa Cruz de Tenerife": "Islas Canarias",
        "Sevilla": "Andalucia",
        "Teruel": "Aragon",
    }

    return province_map.get(province, province)


def get_rightmove_sub_region_from_province(province: str):
    province_map = {
        "Castellon": "Castellon de la Plana",
        "Santa Cruz de Tenerife": "Tenerife",
    }

    return province_map.get(province, province)


def get_rightmove_city_from_area_level_1_name(area_level_1_name: str):
    area_level_1_name_map = {
        "Malaga": "Malaga City",
    }

    return area_level_1_name_map.get(area_level_1_name, area_level_1_name)


def map_property_to_rightmove_property(*, property: Property):
    feature_names: list[str] = [feature.name[:200] for feature in property.features]
    descriptions: list[PropertyDescription] = property.descriptions
    eng_description = [
        desc for desc in descriptions if desc.language == Language.ENGLISH
    ]
    main_description = {"tagline": "-", "description": "-"}
    if eng_description:
        main_description["tagline"] = eng_description[0].tagline
        main_description["description"] = eng_description[0].description

    (new_home, property_type_id) = map_new_home_and_property_type(
        property_type_origin=cast(str, property.property_type_origin),
        property_type_name=cast(str, property.property_type),
    )

    pool_types_names = [pool.name for pool in property.pool_types]
    has_private_pool = "Private" in pool_types_names
    has_communal_pool = "Communal" in pool_types_names

    # Get sorted non hidden images
    images = [x for x in property.images if not x.is_hidden]
    images.sort(key=lambda x: x.order)  # type: ignore

    # Get sorted non hidden images
    videos = [x for x in property.video_streams if not x.is_hidden]

    images_as_media = get_images_media(images)
    videos_as_media = get_videos_media(videos)

    medias = images_as_media + videos_as_media

    return {
        "agent_ref": f"{property.reference}",
        "published": True,
        "property_type": property_type_id,
        "os_status": 1,  # Available
        "new_home": new_home,
        "create_date": (
            property.created_at.strftime("%d-%m-%Y %H:%M:%S")
            if property.created_at
            else None
        ),
        "update_date": (
            property.updated_at.strftime("%d-%m-%Y %H:%M:%S")
            if property.updated_at
            else None
        ),
        "address": {
            "country_code": map_country_code(cast(str, property.country) or ""),
            "region": (
                get_rightmove_region_from_province(property._area_level_1.province)
                if property._area_level_1
                else None
            ),
            "sub_region": (
                get_rightmove_sub_region_from_province(property._area_level_1.province)
                if property._area_level_1
                else None
            ),
            "town_city": (
                get_rightmove_city_from_area_level_1_name(property._area_level_1.name)
                if property._area_level_1
                else None
            ),
            "latitude": property.latitude or None,
            "longitude": property.longitude or None,
        },
        "price_information": {
            "price": get_property_price(
                property=property
            ),  # Minimum of 1, Max of 999_999_999
        },
        "details": {
            "summary": main_description["tagline"][:1000],  # min 1, max 1000 length
            "description": main_description["description"][
                :32000
            ],  # min 1, max 32000 length
            "features": feature_names[
                :10
            ],  # max 10 items, each item minLength of 1 and maxLength of 200
            "bedrooms": property.bedrooms or 0,
            "bathrooms": property.bathrooms or None,
            "outside_space": get_outside_spaces(property=property),
            "year_built": property.built_year or None,
            "internal_area": property.interior_area or None,
            "internal_area_unit": 2,  # square meters
            "land_area": property.plot_area or None,
            "land_area_unit": 2,  # square meters
            "floors": property.total_floors or 0,
            "condition": map_conditions(property.condition or ""),
            "accessibility": map_accessibility(property=property),
            "golf_course_on_site_or_within_10_minutes_walk": property_has_features(
                property=property, features=ALL_GOLF_FEATURES
            ),
            "private_pool": has_private_pool,
            "communal_pool": has_communal_pool,
            "at_beach_or_within_10_minute_walk": property_has_features(
                property=property, features=ALL_BEACH_FEATURES
            ),
            "sea_view": property_has_features(
                property=property, features=ALL_SEA_VIEWS
            ),
            "air_conditioning": property_has_features(
                property=property, features=AIR_CONDITIONING_FEATURES
            ),
            "security_system": property_has_features(
                property=property, features=ALL_SECURITY_SYSTEM_FEATURES
            ),
            "gated_entry": property_has_features(
                property=property, features=ALL_GATED_ENTRY_FEATURES
            ),
            "balcony": property_has_features(
                property=property, features=ALL_BALCONY_FEATURES
            ),
            "ground_floor_terrace": property_has_features(
                property=property, features=ALL_GROUND_FLOOR_TERRACE_FEATURES
            ),
            "log_fireplace": property_has_features(
                property=property, features=ALL_LOG_FIREPLACE_FEATURES
            ),
        },
        "media": medias,
    }
