from enum import Enum
from typing import List, Optional

from pydantic import BaseModel

from strandproperties.constants import Currency, ListingTypeEnum
from strandproperties.logger import logger
from strandproperties.models.image import Image
from strandproperties.models.property import Property


def get_property_currency(*, property: Property) -> str:
    return property.currency.upper() if property.currency else str(Currency.EUR).upper()


class ContactPersonInfo(BaseModel):
    contact_person_fullname: str
    contact_person_email: str
    contact_person_phone: str
    contact_person_address: str
    contact_person_reference: str
    contact_person_image: Optional[str]


def get_contact_person_info(*, property: Property) -> ContactPersonInfo | None:
    try:
        realtors = property.realtor_users
        contact_person = realtors[0]
        return ContactPersonInfo(
            contact_person_fullname=f"{contact_person.first_name} {contact_person.last_name}",
            contact_person_email="<EMAIL>",
            contact_person_phone="+34676901519",
            contact_person_address=property._area_level_1.name,
            contact_person_reference=f"CONTACT_PERSON_{contact_person.id}",
            contact_person_image=contact_person.photo_url,
        )
    except Exception as e:
        logger.error(
            "JamesEdition: Problem getting contact person info for property %s",
            property.reference,
        )
        return None


class LocationInfo(BaseModel):
    country: str
    region: str
    city: str
    zip: str
    address: str
    latitude: Optional[str]
    longitude: Optional[str]


def get_location_info(*, property: Property) -> LocationInfo:
    location_country: str = property._area_level_1.country  # required
    location_region: str = property._area_level_1.province  # required
    location_city: str = property._area_level_1.name  # required

    private_info = property.private_info
    location = private_info["location"]

    location_address: str
    location_zip: str

    location_address = location["address"]  # required
    location_zip = location["postCode"]  # required

    latitude = property.latitude
    longitude = property.longitude

    return LocationInfo(
        country=location_country,
        region=location_region,
        city=location_city,
        zip=location_zip,
        address=location_address,
        latitude=str(latitude) if latitude else None,
        longitude=str(longitude) if longitude else None,
    )


class JamesEditionPropertyTypeEnum(str, Enum):
    HOUSE = "House"
    APARTMENT = "Apartment"
    CHATEAU = "Chateau"
    CHALET = "Chalet"
    LAND = "Land"
    CASTLE = "Castle"
    VILLA = "Villa"
    FARM_RANCH = "Farm/Ranch"
    ESTATE = "Estate"
    OTHER = "Other"


"""
Maps from Property Type (Strand) to JamesEdition property types:
"""
_property_type_mapper = {
    "Apartment": JamesEditionPropertyTypeEnum.APARTMENT,
    "Bar": JamesEditionPropertyTypeEnum.OTHER,
    "Boat": JamesEditionPropertyTypeEnum.OTHER,
    "Building": JamesEditionPropertyTypeEnum.OTHER,
    "Bungalow": JamesEditionPropertyTypeEnum.CHALET,
    "Business": JamesEditionPropertyTypeEnum.OTHER,
    "Castle": JamesEditionPropertyTypeEnum.CASTLE,
    "Chalet": JamesEditionPropertyTypeEnum.CHALET,
    "Commercial Other": JamesEditionPropertyTypeEnum.OTHER,
    "Commercial Premises": JamesEditionPropertyTypeEnum.OTHER,
    "Cortijo": JamesEditionPropertyTypeEnum.OTHER,
    "Country House": JamesEditionPropertyTypeEnum.HOUSE,
    "Development Land": JamesEditionPropertyTypeEnum.LAND,
    "Discotheque": JamesEditionPropertyTypeEnum.OTHER,
    "Duplex": JamesEditionPropertyTypeEnum.APARTMENT,
    "Duplex Penthouse": JamesEditionPropertyTypeEnum.APARTMENT,
    "Estate": JamesEditionPropertyTypeEnum.ESTATE,
    "Finca": JamesEditionPropertyTypeEnum.LAND,
    "Flat": JamesEditionPropertyTypeEnum.APARTMENT,
    "Golf Course": JamesEditionPropertyTypeEnum.LAND,
    "Golf Plot": JamesEditionPropertyTypeEnum.LAND,
    "Ground Floor Apartment": JamesEditionPropertyTypeEnum.APARTMENT,
    "Ground Floor Duplex": JamesEditionPropertyTypeEnum.APARTMENT,
    "Hotel": JamesEditionPropertyTypeEnum.OTHER,
    "Hotel Plot": JamesEditionPropertyTypeEnum.LAND,
    "House": JamesEditionPropertyTypeEnum.HOUSE,
    "Industrial Land": JamesEditionPropertyTypeEnum.LAND,
    "Industrial Premises": JamesEditionPropertyTypeEnum.OTHER,
    "Investment": JamesEditionPropertyTypeEnum.OTHER,
    "Loft": JamesEditionPropertyTypeEnum.APARTMENT,
    "Mansion": JamesEditionPropertyTypeEnum.CASTLE,
    "Mooring": JamesEditionPropertyTypeEnum.OTHER,
    "Office": JamesEditionPropertyTypeEnum.OTHER,
    "Office Units": JamesEditionPropertyTypeEnum.OTHER,
    "Palace": JamesEditionPropertyTypeEnum.CASTLE,
    "Parking": JamesEditionPropertyTypeEnum.OTHER,
    "Penthouse": JamesEditionPropertyTypeEnum.APARTMENT,
    "Plot": JamesEditionPropertyTypeEnum.LAND,
    "Residential Plot": JamesEditionPropertyTypeEnum.LAND,
    "Restaurant": JamesEditionPropertyTypeEnum.OTHER,
    "Riad": JamesEditionPropertyTypeEnum.OTHER,
    "Rustic Plot": JamesEditionPropertyTypeEnum.LAND,
    "Semi Detached House": JamesEditionPropertyTypeEnum.HOUSE,
    "Semi Detached Villa": JamesEditionPropertyTypeEnum.VILLA,
    "Shop": JamesEditionPropertyTypeEnum.OTHER,
    "Shopping Centre": JamesEditionPropertyTypeEnum.OTHER,
    "Store Room": JamesEditionPropertyTypeEnum.OTHER,
    "Studio": JamesEditionPropertyTypeEnum.OTHER,
    "Supermarket": JamesEditionPropertyTypeEnum.OTHER,
    "Town House": JamesEditionPropertyTypeEnum.HOUSE,
    "Triplex": JamesEditionPropertyTypeEnum.OTHER,
    "Unique Building": JamesEditionPropertyTypeEnum.OTHER,
    "Villa": JamesEditionPropertyTypeEnum.VILLA,
}


def get_jamesedition_property_type(*, property: Property) -> str:
    return _property_type_mapper[property.property_type]


def map_listing_type_to_price_and_type(*, property: Property) -> tuple[str, str]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]

    if ListingTypeEnum.SALE in listing_types_names:
        return (
            f"{property.price_sale}",
            "sale",
        )
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        return (
            f"{property.price_rent_long_term}",
            "rent",
        )
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        return (
            f"{property.price_rent_short_term}",
            "rent",
        )
    else:
        raise Exception(
            f"Property {property.reference} doesn't have any type of price. This should not happen."
        )


features_mapper = {
    "mountain_view": ["Mountain View"],
    "tennis_court": [
        "Paddle Tennis",
        "Tennis Court",
        "Tennis / paddle court",
    ],
    "ocean_front": [
        "Sea View",
        "Close to Sea/Beach",
        "Partial Sea Views",
    ],
    "wine_cellar": ["Wine Cellar"],
    "pool": [
        "Community Pool",
        "Pool",
        "Indoor pool",
        "Heated pool",
        "Saltwater swimming pool",
    ],
    "gym": ["Gym"],
    "elevator": ["Lift"],
}


def property_has_feature(*, jamesedition_feature: str, property: Property) -> bool:
    features = property.features

    has_feature = next(
        (
            feature
            for feature in features
            if feature.name in features_mapper[jamesedition_feature]
        ),
        None,
    )

    return True if has_feature else False


def get_sorted_non_hidden_images(*, property: Property) -> List[Image]:
    non_hidden_images = [image for image in property.images if not image.is_hidden]
    non_hidden_images.sort(key=lambda x: x.order)
    return non_hidden_images
