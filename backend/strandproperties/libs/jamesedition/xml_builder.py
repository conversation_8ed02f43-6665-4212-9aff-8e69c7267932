import copy
import re
import xml.dom.minidom
import xml.etree.ElementTree as ET
from datetime import datetime

from strandproperties.config import app_cfg
from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.libs.aws import S3Service
from strandproperties.libs.jamesedition.mapper import (
    features_mapper,
    get_contact_person_info,
    get_jamesedition_property_type,
    get_location_info,
    get_property_currency,
    get_sorted_non_hidden_images,
    map_listing_type_to_price_and_type,
    property_has_feature,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import (
    allowed_streaming_video,
    remove_some_ASCII_control_characters,
)
from strandproperties.logger import logger
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


class JamesEditionXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        jameslist_feed = ET.Element(
            "jameslist_feed",
            attrib={"version": "3.7"},
        )

        feed_information = ET.SubElement(jameslist_feed, "feed_information")
        feed_reference = ET.SubElement(feed_information, "reference")
        feed_reference.text = "Strand Properties"
        feed_title = ET.SubElement(feed_information, "title")
        feed_title.text = "Strand Properties Feed"
        feed_description = ET.SubElement(feed_information, "feed_description")
        feed_description.text = (
            "A feed with real estate listings from Strand Properties"
        )
        feed_created = ET.SubElement(feed_information, "created")
        # TODO Set the creation date to the first time the file was generated
        feed_created.text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        feed_updated = ET.SubElement(feed_information, "updated")
        feed_updated.text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # TODO Add these details when we go to production
        feed_dealer = ET.SubElement(jameslist_feed, "dealer")
        dealer_reference = ET.SubElement(feed_dealer, "reference")
        dealer_reference.text = ""
        dealer_id = ET.SubElement(feed_dealer, "id")
        dealer_id.text = ""
        dealer_name = ET.SubElement(feed_dealer, "name")
        dealer_name.text = ""

        adverts = ET.SubElement(jameslist_feed, "adverts")

        self.root = jameslist_feed
        self.encoding = encoding
        self.adverts = adverts

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        ### Property tag
        property_tag["advert"] = ET.SubElement(
            self.adverts,
            "advert",
            attrib={
                "reference": property.reference,
                "category": "real estate",
            },
        )

        # ------

        ### Main tags

        (price, type) = map_listing_type_to_price_and_type(property=property)

        ET.SubElement(property_tag["advert"], "preowned").text = (
            "yes" if property.property_type_origin == "Resales" else "no"
        )
        ET.SubElement(property_tag["advert"], "type").text = type
        ET.SubElement(property_tag["advert"], "brand").text = ""
        ET.SubElement(property_tag["advert"], "model").text = ""

        built_year_is_valid = (
            (
                re.match(r"\d{4}", str(property.built_year))
                and len(str(property.built_year)) == 4
            )
            if property.built_year
            else None
        )  # Four digits only

        ET.SubElement(property_tag["advert"], "year").text = (
            f"{property.built_year}" if built_year_is_valid else ""
        )

        ET.SubElement(property_tag["advert"], "price_on_request").text = "no"

        ET.SubElement(
            property_tag["advert"],
            "price",
            attrib={
                "currency": get_property_currency(property=property),
            },
        ).text = price

        # ------

        ### location tag
        property_tag["location"] = ET.SubElement(
            property_tag["advert"],
            "location",
        )

        location_info = get_location_info(property=property)

        # location/country
        ET.SubElement(property_tag["location"], "country").text = location_info.country
        # location/region
        ET.SubElement(property_tag["location"], "region").text = location_info.region
        # location/city
        ET.SubElement(property_tag["location"], "city").text = location_info.city
        # location/zip
        ET.SubElement(property_tag["location"], "zip").text = location_info.zip

        if property.is_public_coordinates:
            # location/latitude
            ET.SubElement(property_tag["location"], "latitude").text = (
                f"{location_info.latitude}" if location_info.latitude else ""
            )
            # location/longitude
            ET.SubElement(property_tag["location"], "longitude").text = (
                f"{location_info.longitude}" if location_info.longitude else ""
            )

        # ------

        ### Headline and Description tags
        for description in property.descriptions:
            # description
            if (
                description.language == Language.ENGLISH
                and description.type == DescriptionType.FULL
            ):
                ET.SubElement(property_tag["advert"], "description").text = (
                    remove_some_ASCII_control_characters(description.description)
                )

        # ------

        ### Real Estate tags
        ET.SubElement(property_tag["advert"], "real_estate_type").text = (
            get_jamesedition_property_type(property=property)
        )

        if property.bedrooms:
            ET.SubElement(property_tag["advert"], "bedrooms").text = (
                f"{property.bedrooms}"
            )

        if property.bathrooms:
            ET.SubElement(property_tag["advert"], "bathrooms").text = (
                f"{property.bathrooms}"
            )

        ET.SubElement(property_tag["advert"], "floors").text = (
            f"{property.total_floors}" if property.total_floors else ""
        )

        ET.SubElement(property_tag["advert"], "guests").text = (
            f"{property.pax}" if property.pax else ""
        )

        ET.SubElement(
            property_tag["advert"],
            "living_area",
            attrib={
                "unit": "sqm",
            },
        ).text = (
            f"{property.built_area}" if property.built_area else ""
        )

        ET.SubElement(
            property_tag["advert"],
            "land_area",
            attrib={
                "unit": "sqm",
            },
        ).text = (
            f"{property.plot_area}" if property.plot_area else ""
        )

        # TODO Add rating information when we add them to our model
        # ET.SubElement(property_tag["advert"], "emission_rating").text = ""

        # ET.SubElement(property_tag["advert"], "consumption_rating").text = ""

        # ------

        ### Amenities (Features) tag
        property_tag["amenities"] = ET.SubElement(
            property_tag["advert"],
            "amenities",
        )

        for feature_tag in features_mapper:
            if property_has_feature(
                property=property, jamesedition_feature=feature_tag
            ):
                ET.SubElement(
                    property_tag["amenities"],
                    feature_tag,
                ).text = "yes"

        # amenities/new_development
        ET.SubElement(
            property_tag["amenities"],
            "new_development",
        ).text = (
            "yes" if property.property_type_origin == "New development" else "no"
        )

        ### Media tag

        images = get_sorted_non_hidden_images(property=property)

        property_tag["media"] = ET.SubElement(
            property_tag["advert"],
            "media",
        )

        for index, image in enumerate(images):
            property_tag["image"] = ET.SubElement(
                property_tag["media"],
                "image",
            )
            ET.SubElement(
                property_tag["image"],
                "image_url",
            ).text = image.url

            if index == 40:  # JamesEdition image limit
                break

        for v in property.video_streams:
            if not v.is_hidden and allowed_streaming_video(v.url):
                video = ET.SubElement(property_tag["media"], "video")
                video_url = ET.SubElement(video, "video_url")
                video_url.text = v.url
                break

        for v in property.video_tours:
            if not v.is_hidden:
                tour = ET.SubElement(property_tag["media"], "virtual_tour_link")
                tour.text = v.url
                break

        # ------

        ### Contact Person tag
        contact_person_info = get_contact_person_info(property=property)
        if contact_person_info:
            property_tag["contact_person"] = ET.SubElement(
                property_tag["advert"],
                "contact_person",
            )
            # contact_person/name
            ET.SubElement(property_tag["contact_person"], "name").text = (
                contact_person_info.contact_person_fullname
            )
            # contact_person/email
            ET.SubElement(property_tag["contact_person"], "email").text = (
                contact_person_info.contact_person_email
            )
            # contact_person/phone
            ET.SubElement(property_tag["contact_person"], "phone").text = (
                contact_person_info.contact_person_phone
            )
            # contact_person/cell
            ET.SubElement(property_tag["contact_person"], "cell").text = (
                contact_person_info.contact_person_phone
            )
            # contact_person/address
            ET.SubElement(property_tag["contact_person"], "address").text = (
                contact_person_info.contact_person_address
            )
            # contact_person/reference
            ET.SubElement(property_tag["contact_person"], "reference").text = (
                contact_person_info.contact_person_reference
            )
            # contact_person/image
            if contact_person_info.contact_person_image:
                ET.SubElement(property_tag["contact_person"], "image").text = (
                    contact_person_info.contact_person_image
                )

            # ------

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][James Edition]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # we need info from location which is REQUIRED
        if not (property.private_info and "location" in property.private_info):  # type: ignore
            logger.info(
                f"> Skipping property [{property.reference}][James Edition]: private_info.location is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][James Edition]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][James Edition]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][James Edition]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][James Edition]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        try:
            get_location_info(property=property)
        except Exception as e:
            logger.info(
                f"> Skipping property [{property.reference}][James Edition]: could not get location",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/jamesedition",
            portal=PortalNames.JAMESEDITION,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
