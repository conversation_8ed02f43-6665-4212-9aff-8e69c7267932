"""
  From [https://help.kyero.com/property-types-used-in-kyero]
  These are the property groups we use currently:
    Apartment: apartment, duplex, penthouse, studio, triplex
    Villa: bungalow, villa
    Town house: terraced house, town house, village house
    Country house: farm, cortijo, country house, farmhouse, finca
    Land: land, ruin
    Cave house: cave house
    Garage: garage, parking space,
    Commercial property: all commercial property types
    Wooden home:  mobile home, wooden chalet
"""

from typing import List, cast

from strandproperties.constants import Language, ListingTypeEnum
from strandproperties.models.listing_type import ListingType
from strandproperties.models.property import Property, PropertyDescription
from strandproperties.schemas.base import BaseSchema

PRICE_FREQ_SALE = "sale"
PRICE_FREQ_MONTH = "month"

"""
  The `key` is our property_type `category` and the `value` are the values from kyero
  The comment after each line represents which default value should we use if we have
  the right category (`key`) but not the right `value`.
"""
kyero_property_types = {
    "Apartments": [
        "apartment",
        "duplex",
        "penthouse",
        "studio",
        "triplex",
    ],  # apartment
    "Villas": ["bungalow", "villa"],  # villa
    "Houses": [
        "terraced house",
        "town house",
        "village house",
        "cave house",
    ],  # town house
    "Country Properties": [
        "farm",
        "cortijo",
        "country house",
        "farmhouse",
        "finca",
    ],  # country house
    "Plots and Lands": ["land", "ruin"],  # land
    "Commercials": [
        "commercial premises",
        "commercial other",
        "bar",
        "building",
        "business",
        "development land",
        "discotheque",
        "golf course",
        "golf plot",
        "hotel",
        "hotel plot",
        "industrial land",
        "industrial premises",
        "investment",
        "office",
        "office units",
        "residential plot",
        "restaurant",
        "rustic plot",
        "shop",
        "shopping centre",
        "supermarket",
    ],  # whatever we have
    "Other": ["garage", "parking space"],  # garage
}

default_kyero_property_type = {
    "Apartments": "apartment",
    "Villas": "villa",
    "Houses": "town house",
    "Country Properties": "country house",
    "Plots and Lands": "land",
    "Other": "garage",
}


def map_property_type_to_kyero_property_type(
    *, property_type_name: str, property_type_category: str
) -> str:
    if property_type_category not in kyero_property_types:
        return "town house"

    possible_types = kyero_property_types[property_type_category]

    if property_type_name.lower() in possible_types:
        return property_type_name.lower()

    return default_kyero_property_type[property_type_category]


def map_listing_type_to_kyero_price_and_price_freq(
    property: Property,
) -> tuple[str, str]:
    listing_types: List[ListingType] = property.listing_types
    sale_price = f"{property.price_sale}" if cast(int, property.price_sale) else "0"

    for listing_type in listing_types:
        match listing_type.name:
            case ListingTypeEnum.SALE:
                return (sale_price, PRICE_FREQ_SALE)
            case ListingTypeEnum.RENT_LONG:
                return (f"{property.price_rent_long_term}", PRICE_FREQ_MONTH)
            case ListingTypeEnum.RENT_SHORT:
                return (f"{property.price_rent_short_term}", PRICE_FREQ_MONTH)
            case _:
                return (sale_price, PRICE_FREQ_SALE)

    return (sale_price, PRICE_FREQ_SALE)


def map_if_has_feature(feature_ids: list[int], features_to_compare: list[int]) -> str:
    for id_to_compare in features_to_compare:
        if id_to_compare in feature_ids:
            return "1"

    return "0"


def replace_description_characters(description: str) -> str:
    """
    From Kyero [https://help.kyero.com/estate-agents/xml-import-specification] specification:
    5. About Character Encoding
      There are five characters which must be encoded if you want to use them in your feed. This is because they have predefined functions in XML: (you could use their numeric entities too).
        &lt; - The less than sign (<) - &#60;
        &gt; - The greater than sign (>) - &#62;
        &amp; - The ampersand (&) - &#38;
        &apos; - The single quote or apostrophe (') - &#39;
        &quot; - The double quote (") - &#34;
        &#13;- To force line breaks in descriptions.

    If you're wondering why this "__amp__" is being used in here,
    it's because the minidom xml parser will parse everything into
    XML valid format, which is nice.

    The problem arises when we want to have "&#34;" code to mean that
    we are using double quotes, for example. This would be translated into
    "&amp;#38;" which doesn't mean anything to any parser.

    Therefore, here we replace the &{code} for __amp__{code} and then, after
    minidom has done its parsing, we replace "__amp__" for "&".

    """
    description = description.replace("&", "__amp__#38;")
    description = description.replace("\r\n", "__amp__#13;")
    description = description.replace("\n", "__amp__#13;")
    description = description.replace("\r", "__amp__#13;")
    description = description.replace("<br/>", "__amp__#13;")
    description = description.replace('"', "__amp__#34;")
    description = description.replace("'", "__amp__#39;")
    description = description.replace("<", "__amp__#60;")
    description = description.replace(">", "__amp__#62;")
    description = description.replace("", " ")
    description = description.replace(chr(0), "")

    return description


class DescriptionByLanguage(BaseSchema):
    language: Language
    description: str


def map_descriptions(property: Property) -> list[DescriptionByLanguage]:
    descriptions: list[PropertyDescription] = property.descriptions or []

    response: list[DescriptionByLanguage] = []

    for description in descriptions:
        response.append(
            DescriptionByLanguage(
                **{
                    "language": description.language,
                    "description": replace_description_characters(
                        description.description
                    ),
                }
            )
        )

    return response


"""
Maps from Property Type (Strand) to New build (Kyero):
  - if it is "New Development" => "1"; otherwise, "0"
"""


def map_new_build(*, property_type_origin: str) -> str:
    return "0" if property_type_origin == "Resales" else "1"
