import copy
import xml.dom.minidom
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Optional, cast

from strandproperties.config import app_cfg
from strandproperties.constants import CommissionTypeEnum, Currency, Language
from strandproperties.libs.aws import S3Service
from strandproperties.libs.kyero.mapper import (
    map_descriptions,
    map_if_has_feature,
    map_listing_type_to_kyero_price_and_price_freq,
    map_new_build,
    map_property_type_to_kyero_property_type,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import (
    get_formatted_commission_based_on_commission_type,
)
from strandproperties.logger import logger
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript

has_pool_feature_ids = [33, 40]


class KyeroV3XMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element("root")
        kyero_header = ET.SubElement(root, "kyero")
        kyero_version = ET.SubElement(kyero_header, "feed_version")
        kyero_version.text = "3"

        self.root = root
        self.encoding = encoding

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}
        ### All mandatory
        property_tag["property"] = ET.SubElement(self.root, "property")
        property_tag["property_id"] = ET.SubElement(
            property_tag["property"], "id"
        )  # alphanumeric, max 50 chars
        property_tag["updated_at"] = ET.SubElement(
            property_tag["property"], "date"
        )  # format YYYY-MM-DD HH:MM:SS  (strftime("%Y-%m-%d %H:%M:%S")) --> This needs to be updated whenever a property is updated
        property_tag["property_reference"] = ET.SubElement(
            property_tag["property"], "ref"
        )  # alphanumeric, max 250 chars, the ref that the customer will see
        property_tag["price"] = ET.SubElement(
            property_tag["property"], "price"
        )  # numeric, max 8 chars
        property_tag["price_freq"] = ET.SubElement(
            property_tag["property"], "price_freq"
        )  # alpha, "sale" for properties for sale, "month" for long term rental

        # Property types:
        """
          From [https://help.kyero.com/property-types-used-in-kyero]
          These are the property groups we use currently:
            Apartment: apartment, duplex, penthouse, studio, triplex
            Villa: bungalow, villa
            Town house: terraced house, town house, village house
            Country house: farm, cortijo, country house, farmhouse, finca
            Land: land, ruin
            Cave house: cave house
            Garage: garage, parking space,
            Commercial property: all commercial property types
            Wooden home:  mobile home, wooden chalet
        """
        property_tag["property_type"] = ET.SubElement(
            property_tag["property"], "type"
        )  # alpha

        property_tag["city"] = ET.SubElement(
            property_tag["property"], "town"
        )  # alpha, area_level_1
        property_tag["province"] = ET.SubElement(
            property_tag["property"], "province"
        )  # alpha, area_level_2
        property_tag["beds"] = ET.SubElement(
            property_tag["property"], "beds"
        )  # numeric; empty, missing tag or "0" if unknown
        property_tag["baths"] = ET.SubElement(
            property_tag["property"], "baths"
        )  # numeric; empty, missing tag or "0" if unknown
        property_tag["pool"] = ET.SubElement(
            property_tag["property"], "pool"
        )  # numeric; empty, missing tag or "0" if unknown. "1" if a pool is available

        # Descriptions
        property_tag["desc"] = ET.SubElement(
            property_tag["property"], "desc"
        )  # No character limit. No HTML, Use &#13; to force a line break in the text
        property_tag[Language.ENGLISH.value] = ET.SubElement(
            property_tag["desc"], Language.ENGLISH.value
        )
        property_tag[Language.SPANISH.value] = ET.SubElement(
            property_tag["desc"], Language.SPANISH.value
        )
        property_tag[Language.FINNISH.value] = ET.SubElement(
            property_tag["desc"], Language.FINNISH.value
        )
        property_tag[Language.GERMAN.value] = ET.SubElement(
            property_tag["desc"], Language.GERMAN.value
        )
        property_tag[Language.SWEDISH.value] = ET.SubElement(
            property_tag["desc"], Language.SWEDISH.value
        )

        # images
        # - maximum of 50 image ids per property
        # - URL must end with valid image format (<xs:pattern value="(https?|ftp)://(.*)\.(gif|jpe?g|png|GIF|JPE?G|PNG)"/>)
        # - ids from 1 to 50
        # property_tag["images"] = ET.SubElement(property_tag["property"], "images")
        # image = ET.SubElement(images, "image", id="1") ### EXAMPLE
        # url = ET.SubElement(image, "url") ### EXAMPLE

        property_tag["email"] = ET.SubElement(property_tag["property"], "email")

        ## ------------------------------------------------------------------------

        ### Optional
        property_tag["currency"] = ET.SubElement(
            property_tag["property"], "currency"
        )  # alpha, if empty, EUR or missing, default to EUR. Options: EUR, GBP or USD
        property_tag["new_build"] = ET.SubElement(
            property_tag["property"], "new_build"
        )  # numeric, Empty, missing or "0" if property is resale. "1" if property is less than 12 months old
        property_tag["country"] = ET.SubElement(
            property_tag["property"], "country"
        )  # alpha, if empty or missing will default to Spain
        property_tag["location_detail"] = ET.SubElement(
            property_tag["property"], "location_detail"
        )  # alphanumeric, max 50 chars. Free text. Usually the areas that are too small (area level 3 and so on)

        # latitude and longitude are subtags of location
        property_tag["location"] = ET.SubElement(property_tag["property"], "location")
        property_tag["latitude"] = ET.SubElement(
            property_tag["location"], "latitude"
        )  # numeric; missing, empty or 0 if unknown
        property_tag["longitude"] = ET.SubElement(
            property_tag["location"], "longitude"
        )  # numeric; missing, empty or 0 if unknown

        # built and plot area are subtags of surface_area
        property_tag["surface_area"] = ET.SubElement(
            property_tag["property"], "surface_area"
        )
        property_tag["built_area"] = ET.SubElement(
            property_tag["surface_area"], "built"
        )  # numeric, square meters, empty, missing or "0" if unknown
        property_tag["plot_area"] = ET.SubElement(
            property_tag["surface_area"], "plot"
        )  # numeric, square meters, empty, missing or "0" if unknown

        # Features
        # property_tag["features"] = ET.SubElement(
        #     property_tag["property"], "features"
        # )  # alpha, max 35 chars per property feature, either Spanish or English
        # feature = ET.SubElement(features, "feature") ## EXAMPLE

        return property_tag

    def fill_property_tags(
        self,
        original_property_tag: dict[str, ET.Element],
        property: Property,
        is_resales_kyero: bool = False,
    ):
        property_tag = copy.copy(original_property_tag)

        property_images = property.images
        property_images.sort(key=lambda x: x.order)  # type: ignore
        property_images: list[Image] = property_images[:50]

        feature_ids: list[int] = [feature.id for feature in property.features]
        feature_names: list[str] = [feature.name[:35] for feature in property.features]
        descriptions = map_descriptions(property=property)

        ### INFO: Currently Strand properties in Kyero are not using any email
        # realtors: list[User] = property.realtor_users or []
        # email = cast(str, realtors[0].email) if realtors else "Not available"
        property_tag["email"].text = ""

        property_tag["property_id"].text = f"{property.id}"
        property_tag["updated_at"].text = (
            property.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            if property.updated_at
            else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        property_tag["property_reference"].text = f"{property.reference}"

        (price, listing_type) = map_listing_type_to_kyero_price_and_price_freq(property)
        property_tag["price"].text = price[:8]  # max of 8 chars (99_999_999)
        property_tag["price_freq"].text = listing_type

        property_tag["property_type"].text = map_property_type_to_kyero_property_type(
            property_type_name=cast(str, property.property_type) or "",
            property_type_category=cast(str, property.property_type_category) or "",
        )
        property_tag["province"].text = (
            f"{property._area_level_1.province}"
            if property._area_level_1
            else "Not defined"
        )
        property_tag["city"].text = (
            f"{property._area_level_1.name}"
            if property._area_level_1
            else "Not defined"
        )
        property_tag["beds"].text = f"{property.bedrooms}" if property.bedrooms else "0"
        property_tag["baths"].text = (
            f"{property.bathrooms}" if property.bathrooms else "0"
        )
        property_tag["pool"].text = map_if_has_feature(
            feature_ids=feature_ids, features_to_compare=has_pool_feature_ids
        )
        for description in descriptions:
            property_tag[description.language].text = description.description

        property_tag["images"] = ET.SubElement(property_tag["property"], "images")
        for index, image in enumerate(property_images):
            img_tag = ET.SubElement(
                property_tag["images"],
                "image",
                id=f"{(image.order + 1) if image.order else (index + 1)}",
            )
            ET.SubElement(img_tag, "url").text = image.url

        for v in property.video_streams:
            if not v.is_hidden:
                video_tag = ET.SubElement(property_tag["property"], "video_url")
                video_tag.text = v.url
                break

        for v in property.video_tours:
            if not v.is_hidden:
                tour_tag = ET.SubElement(property_tag["property"], "virtual_tour_url")
                tour_tag.text = v.url
                break

        # get most detailed location
        detailed_location = ""
        if property.area_level_5_id is not None:
            detailed_location = property._area_level_5.name
        elif property.area_level_4_id is not None:
            detailed_location = property._area_level_4.name
        elif property.area_level_3_id is not None:
            detailed_location = property._area_level_3.name
        elif property.area_level_2_id is not None:
            detailed_location = property._area_level_2.name
        elif property.area_level_1_id is not None:
            detailed_location = property._area_level_1.name

        ### optional tags
        property_tag["currency"].text = (
            f"{property.currency.upper()}" if property.currency else Currency.EUR
        )
        property_tag["new_build"].text = map_new_build(
            property_type_origin=cast(str, property.property_type_origin)
        )
        property_tag["country"].text = (
            f"{property.country}" if property.country else "Spain"
        )
        property_tag["location_detail"].text = detailed_location[:50]
        property_tag["latitude"].text = (
            f"{property.latitude}" if property.latitude else "0"
        )
        property_tag["longitude"].text = (
            f"{property.longitude}" if property.longitude else "0"
        )
        property_tag["built_area"].text = (
            f"{property.built_area}" if property.built_area else "0"
        )
        property_tag["plot_area"].text = (
            f"{property.plot_area}" if property.plot_area else "0"
        )

        if feature_names:
            property_tag["features"] = ET.SubElement(
                property_tag["property"], "features"
            )
            for feature in feature_names[:50]:
                ET.SubElement(property_tag["features"], "feature").text = feature

        # Handle resales Kyero+ xml (which adds cadastral ref, commission and address)
        if is_resales_kyero:
            # Cadastral Reference
            if property.cadastral_reference is not None:
                ET.SubElement(property_tag["property"], "cadastralRef").text = (
                    property.cadastral_reference
                )

            # Commission
            if property.commission is not None:
                property_tag["commission"] = ET.SubElement(
                    property_tag["property"], "commission"
                )
                commission = get_formatted_commission_based_on_commission_type(
                    commission=property.commission,
                    commission_type=cast(
                        Optional[CommissionTypeEnum], property.commission_type
                    ),
                    currency=property.currency,
                )
                ET.SubElement(property_tag["commission"], "value").text = commission

            # Address
            if (
                property.private_info  # type: ignore
                and "location" in property.private_info  # type: ignore
                and "address" in property.private_info["location"]
            ):  # type: ignore
                address: Optional[str] = property.private_info["location"]["address"]  # type: ignore
                if address is not None:
                    ET.SubElement(property_tag["property"], "address").text = (
                        address.replace("\r\n", " ")
                    )

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        ### Check all required fields first
        # images are REQUIRED
        if not property.images:
            logger.info(
                f"> Skipping property [{property.reference}][KyeroV3 - {request_id}]: images are required",
                extra={"requestId": request_id},
            )
            return False
        images = [image for image in property.images if not image.is_hidden]
        if not images:
            logger.info(
                f"> Skipping property [{property.reference}][KyeroV3 - {request_id}]: non-hidden images are required",
                extra={"requestId": request_id},
            )
            return False

        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][KyeroV3 - {request_id}]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/kyero",
            portal=PortalNames.KYERO,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
