import csv
from typing import Any, Dict, List, Union
from venv import logger

from strandproperties.config import app_cfg
from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.libs.aws import S3Service
from strandproperties.libs.portals_common import (
    FileType,
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import remove_some_ASCII_control_characters
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript
from strandproperties.wsgi import application

LISTING_TYPE_PRIORITY = {
    ListingTypeEnum.SALE: 1,
    ListingTypeEnum.RENT_SHORT: 2,
    ListingTypeEnum.RENT_LONG: 3,
}

# CSV Field specs are based on CSV template of Google Feed docs (Real estate section) :
# https://support.google.com/google-ads/answer/6053288?sjid=1914013835767495995-EU#zippy=%2Creal-estate

LISTING_ID = "Listing ID"
LISTING_NAME = "Listing name"
FINAL_URL = "Final URL"
IMAGE_URL = "Image URL"
CITY_NAME = "City name"
DESCRIPTION = "Description"
PRICE = "Price"
PROPERTY_TYPE = "Property type"
LISTING_TYPE = "Listing type"
CONTEXTUAL_KEYWORDS = "Contextual keywords"
ADDRESS = "Address"
TRACKING_TEMPLATE = "Tracking template"
CUSTOM_PARAMETER = "Custom parameter"
FINAL_MOBILE_URL = "Final mobile URL"
ANDROID_APP_LINK = "Android app link"
IOS_APP_LINK = "iOS app link"
IOS_APP_STORE_ID = "iOS app store ID"

HEADERS: List[Dict[str, Union[str, bool]]] = [
    {
        "csv_field": LISTING_ID,
        "strand_field": "reference",
        "is_required": True,
    },  # Ex: mtv_HC1234 . The Property ID should be unique across all feeds in your account.
    {
        "csv_field": LISTING_NAME,
        "strand_field": "title",
        "is_required": True,
    },  # Ex: Heavenly Condos . Recommended maximum length is 25 characters (12 for double-width languages).
    {
        "csv_field": FINAL_URL,
        "strand_field": "custom",
        "is_required": True,
    },  # Ex: http://www.listing.com/homedetails/heavenlycondos1234?
    {
        "csv_field": IMAGE_URL,
        "strand_field": "main_img",
        "is_required": False,
    },  # Ex: http://ww.listing.com/HC1235photo.jpg
    {
        "csv_field": CITY_NAME,
        "strand_field": "location",
        "is_required": False,
    },  # Ex: Mountain View, CA . Recommended maximum length is 25 characters (12 for double-width languages).
    {
        "csv_field": DESCRIPTION,
        "strand_field": "custom",
        "is_required": False,
    },  # Ex: 3 beds, 2 baths, 1234 sq ft
    {
        "csv_field": PRICE,
        "strand_field": "price_sale",
        "is_required": False,
    },  # Ex: 1,000,000 USD . Number followed by the currency code, ISO 4217 standard. Use "." as the decimal mark.
    {"csv_field": PROPERTY_TYPE, "strand_field": "id", "is_required": False},
    {"csv_field": LISTING_TYPE, "strand_field": "id", "is_required": False},
    {
        "csv_field": CONTEXTUAL_KEYWORDS,
        "strand_field": "id",
        "is_required": False,
    },  # Ex: apartment;pool;3 bed;2 bath;rental
    {"csv_field": ADDRESS, "strand_field": "id", "is_required": False},
    {"csv_field": TRACKING_TEMPLATE, "strand_field": "id", "is_required": False},
    {"csv_field": CUSTOM_PARAMETER, "strand_field": "id", "is_required": False},
    {"csv_field": FINAL_MOBILE_URL, "strand_field": "id", "is_required": False},
    {"csv_field": ANDROID_APP_LINK, "strand_field": "id", "is_required": False},
    {"csv_field": IOS_APP_LINK, "strand_field": "id", "is_required": False},
    {"csv_field": IOS_APP_STORE_ID, "strand_field": "id", "is_required": False},
]


class GoogleCSVBuilder(BaseScript):

    def _validate_fields(self, property: Property, request_id: str) -> bool:

        # We use english property.description for "Listing name"
        property_tagline = next(
            (
                remove_some_ASCII_control_characters(description.tagline)
                for description in property.descriptions
                if description.language == Language.ENGLISH
                and description.type == DescriptionType.FULL
            ),
            "",
        )
        if not property_tagline:
            logger.info(
                f"> Skipping property [{property.reference}][Google]: Listing name is required",
                extra={"requestId": request_id},
            )
            return False
        return True

    def get_dict_property(self, property: Property) -> dict[str, Any]:
        dict_property = {}
        dict_property[LISTING_ID] = property.reference
        property_tagline = next(
            (
                remove_some_ASCII_control_characters(description.tagline)
                for description in property.descriptions
                if description.language == Language.ENGLISH
                and description.type == DescriptionType.FULL
            ),
            "",
        )
        dict_property[LISTING_NAME] = property_tagline
        url = f"https://strandproperties.com/listings/single-listing/?reference={property.reference}"
        dict_property[FINAL_URL] = url
        dict_property[IMAGE_URL] = property.main_img if property.main_img else ""
        dict_property[CITY_NAME] = (
            f"{property._area_level_1.name}, {property._area_level_1.province}"
        )
        description = ""
        description += (
            f"{property._property_type.name}, ".lower()
            if property._property_type and property._property_type.name
            else ""
        )
        description += f"{property.bedrooms} bedroom(s), " if property.bedrooms else ""
        description += (
            f"{property.bathrooms} bathroom(s), " if property.bathrooms else ""
        )
        description += f"{property.built_year}, " if property.built_year else ""
        description += f"{property.built_area} m2" if property.built_area else ""
        dict_property[DESCRIPTION] = description
        dict_property[PROPERTY_TYPE] = property._property_type.name
        dict_property[LISTING_TYPE] = next(
            (
                lt.name
                for lt in sorted(
                    property.listing_types,
                    key=lambda t: LISTING_TYPE_PRIORITY.get(t.name, 999),
                )
            )
        )
        price = None
        if dict_property[LISTING_TYPE] == ListingTypeEnum.SALE:
            price = property.price_sale
        elif dict_property[LISTING_TYPE] == ListingTypeEnum.RENT_LONG:
            price = property.price_rent_long_term
        elif dict_property[LISTING_TYPE] == ListingTypeEnum.RENT_SHORT:
            price = property.price_rent_short_term
        dict_property[PRICE] = f"{price} EUR" if price is not None else ""

        dict_property[CONTEXTUAL_KEYWORDS] = description
        address = (
            property.private_info.get("location", {}).get("address")
            if property.private_info
            else None
        )
        dict_property[ADDRESS] = (
            address.replace("\r", "").replace("\n", "") if address else ""
        )
        dict_property[TRACKING_TEMPLATE] = None
        dict_property[CUSTOM_PARAMETER] = None
        dict_property[FINAL_MOBILE_URL] = url
        dict_property[ANDROID_APP_LINK] = None
        dict_property[IOS_APP_LINK] = None
        dict_property[IOS_APP_STORE_ID] = None

        return dict_property

    def write_to_file(self, file_location: str, data: dict[str, Any]):

        with open(file_location, "w") as file:

            headers = [header["csv_field"] for header in HEADERS]
            writer = csv.DictWriter(file, fieldnames=headers)
            # Write the headers
            writer.writeheader()

            # Write the rows
            writer.writerows(data)

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "text/csv")

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/google",
            portal=PortalNames.GOOGLE,
            validate_fields=self._validate_fields,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
            file_type=FileType.CSV,
            get_dict_property=self.get_dict_property,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
