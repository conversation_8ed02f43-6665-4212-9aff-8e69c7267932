import typing as t

from apispec_pydantic_plugin import <PERSON><PERSON>BaseModel
from pyramid.config import Configurator
from pyramid.config.views import ViewDeriverInfo
from pyramid.request import Request
from pyramid.response import Response
from pyramid_apispec import helpers
from pyramid_apispec.helpers import (
    ALL_METHODS,
    is_string,
    load_operations_from_docstring,
    load_yaml_from_docstring,
)

Context = t.TypeVar("Context")
View = t.Callable[[Context, Request], Response]


def pydantic_openapi_view(view: View, info: ViewDeriverInfo) -> View:
    def wrapper_view(context: Context, request: Request) -> Response:
        return view(context, request)

    return wrapper_view


pydantic_openapi_view.options = (
    "openapi_param_schema",
    "openapi_request_schema",
    "openapi_response_schema",
    "openapi_metadata",
)


ANNOTATION_TYPE_MAP = {
    int: "integer",
    str: "string",
}


def patch_get_operations(view, operations, autodoc=True):
    """
    Monkeypatch `pyramid_apispec.helpers.get_operations` to support
    Pydantic models defined in `pydantic_openapi_view`
    """
    if operations is not None:
        return operations

    operations = {}
    view_operations = {}
    methods = view["request_methods"]

    if any(
        schema in view
        for schema in [
            "openapi_param_schema",
            "openapi_request_schema",
            "openapi_response_schema",
            "openapi_metadata",
        ]
    ):
        # if view has openapi schemas defined
        if isinstance(methods, str):
            methods = [methods]
        else:
            assert isinstance(methods, (list, tuple))

        for method in methods:
            view_operations[method.lower()] = {}

            if view.get("openapi_param_schema"):
                view_operations[method.lower()]["parameters"] = []
                for field in view["openapi_param_schema"].model_fields.values():
                    param_schema = {
                        "name": field.alias,
                        "required": field.is_required(),
                        "schema": {
                            "type": ANNOTATION_TYPE_MAP.get(field.annotation, "string"),
                        },
                    }
                    param_schema["in"] = "query"
                    if field.json_schema_extra:
                        param_schema["in"] = field.json_schema_extra.get(
                            "param_in", "query"
                        )
                        assert param_schema["in"] in ["query", "path", "header"]

                    view_operations[method.lower()]["parameters"].append(param_schema)

            if view.get("openapi_request_schema"):
                view_operations[method.lower()]["requestBody"] = {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": view["openapi_request_schema"].__name__,
                        }
                    },
                }

            if view.get("openapi_response_schema"):
                if isinstance(view["openapi_response_schema"], list):
                    view_operations[method.lower()]["responses"] = {
                        "200": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": view["openapi_response_schema"][
                                            0
                                        ].__name__,
                                    },
                                }
                            }
                        }
                    }
                else:
                    view_operations[method.lower()]["responses"] = {
                        # TODO: status code shouldn't be 200 always
                        "200": {
                            "content": {
                                "application/json": {
                                    "schema": view["openapi_response_schema"].__name__,
                                }
                            }
                        }
                    }

            view_operations[method.lower()].update(view.get("openapi_metadata", {}))
    else:
        # fallback to loading schema from docstring
        if view.get("attr"):
            global_meta = load_operations_from_docstring(view["callable"].__doc__)
            if global_meta:
                operations.update(global_meta)
            f_view = getattr(view["callable"], view["attr"])
        # or just function callables
        else:
            f_view = view.get("callable")

        view_operations = load_operations_from_docstring(f_view.__doc__)
        if view_operations:
            pass
        else:
            view_operations = {}
            if is_string(methods):
                methods = [methods]
            if not methods:
                methods = ALL_METHODS[:]
            operation = load_yaml_from_docstring(f_view.__doc__)
            if operation:
                for method in methods:
                    view_operations[method.lower()] = operation
            elif autodoc:
                for method in methods:
                    view_operations.setdefault(method.lower(), {"responses": {}})

    operations.update(view_operations)

    return operations


def includeme(config: Configurator) -> None:
    config.add_view_deriver(pydantic_openapi_view)

    helpers.get_operations = patch_get_operations

    # this takes care of datetime, date
    config.include("tet.renderers.json")

    # add another adapter for Pydantic
    config.add_json_adapter(
        for_=ApiBaseModel,
        adapter=lambda d, req: d.dict(by_alias=True),
    )
