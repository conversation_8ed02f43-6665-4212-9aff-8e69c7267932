import logging

import cronitor

from strandproperties.config import app_cfg

logger = logging.getLogger(__name__)


def includeme(config):
    if app_cfg.cronitor_api_key:
        cronitor.api_key = app_cfg.cronitor_api_key
        cronitor.environment = app_cfg.env.value

        # for integration in PortalIntegration:
        #     cronitor.Monitor.put(
        #         key=f"portal_{integration}",
        #         type="job",
        #     )

    return cronitor
