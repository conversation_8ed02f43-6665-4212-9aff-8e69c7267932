import hashlib
import json
from datetime import datetime
from ftplib import FTP
from typing import Any

import httpx
from jsonschema import Draft4V<PERSON><PERSON><PERSON>, ValidationError
from referencing import Registry, Resource
from sqlalchemy import and_, select
from sqlalchemy.orm import joinedload

from strandproperties.config import Env, app_cfg
from strandproperties.constants import Status
from strandproperties.libs.aws import S3Service
from strandproperties.libs.idealista.mapper import map_property_to_idealista_property
from strandproperties.logger import logger
from strandproperties.models.office import Office, UserOffice
from strandproperties.models.property import Property, PropertyRealtor
from strandproperties.scripts.base import BaseScript

resource_by_url: dict[str, Resource] = {}

IDEALISTA_CUSTOMER_CODE = {
    "Malaga, Spain": "ilc901563fa9fceddbe7c65fa9e0993ede873f55d66",
    "Fuengirola, Spain": "ilcc127f510b7fece0ec89e72ddae4bdd313de52cbf",
    "Mallorca, Spain": "ilc8b97dd12da8396240dbe2694b952ef74d7fccdaa",
    "Marbella, Spain": "ilc5c31fa588c3bba24818305ad48740cd621eb4ac6",
}


def retrieve_via_httpx_with_cache(uri: str):
    resource = resource_by_url.get(uri, None)
    if resource:
        return resource
    response = httpx.get(uri)
    r = Resource.from_contents(response.json())
    resource_by_url[uri] = r
    return r


def create_idealista_validator(
    ref_url: str = "https://feeds.idealista.com/v6/schemas/properties/customer.json",
) -> Draft4Validator:
    return Draft4Validator(
        {"$ref": ref_url},
        registry=Registry(retrieve=retrieve_via_httpx_with_cache),
    )


class IdealistaJSONBuilder(BaseScript):
    s3: S3Service
    aws_filename: str

    def write_to_file(self, idealista_json: dict[str, Any], file_path: str):
        with open(file_path, "w") as f:
            json.dump(idealista_json, f)

    def upload_to_idealista_ftp(
        self, file_location: str, request_id: str, filename: str
    ):
        session = FTP(
            app_cfg.idealista_ftp_server,
            app_cfg.idealista_ftp_user,
            app_cfg.idealista_ftp_password,
        )
        with open(file_location, "rb") as file:
            session.storbinary(f"STOR {filename}", file)
        session.quit()

        logger.info(
            f"Finished generating and uploading idealista json",
            extra={
                "requestId": request_id,
            },
        )

    def upload_to_s3(self, file_location: str):
        with open(file_location, "rb") as file:
            self.s3.upload(self.aws_filename, file, "application/json")

    def filter_not_valid_properties(
        self, properties: list[dict[str, Any]], request_id: str
    ) -> list[dict[str, Any]]:
        valid_properties: list[dict[str, Any]] = []
        validator = create_idealista_validator(
            "https://feeds.idealista.com/v6/schemas/properties/property.json"
        )

        for property in filter(lambda p: p.get("propertyReference", None), properties):
            p_ref = property.get("propertyReference", None)
            try:
                validator.validate(property)
                valid_properties.append(property)
            except ValidationError as e:
                logger.info(
                    "Property validation failed. [ref={ref}] [path={path}] {message}".format(
                        ref=p_ref, path=e.json_path, message=e.message
                    ),
                    extra={
                        "requestId": request_id,
                    },
                )

        return valid_properties

    def equal_hash(self, *a: Any | None) -> bool:
        hashes = list(
            map(lambda d: hashlib.md5(json.dumps(d).encode("utf-8")).hexdigest(), a)
        )
        return all(i == hashes[0] for i in hashes)

    def has_differences(self, idealista_json: dict[str, Any]):
        latest_file_contents = self.s3.get_content(self.aws_filename)

        latest_file_properties = (
            json.loads(latest_file_contents.read())["customerProperties"]
            if latest_file_contents
            else None
        )
        generated_properties = idealista_json["customerProperties"]
        return not self.equal_hash(latest_file_properties, generated_properties)

    def get_properties_by_office(self):
        # Get all offices
        stmt = select(Office)
        offices = self.db_session.scalars(stmt).all()

        office_properties = {}
        for office in offices:
            # Get all properties linked to these users through PropertyRealtor
            stmt = (
                select(Property)
                .distinct()
                .options(joinedload(Property._property_type))
                .join(PropertyRealtor, Property.id == PropertyRealtor.property_id)
                .join(UserOffice, UserOffice.user_id == PropertyRealtor.user_id)
                .where(
                    and_(
                        Property.status == Status.PUBLISHED,
                        getattr(Property, "portals", {"is_idealista_enabled": False})[
                            "is_idealista_enabled"
                        ]
                        == True,
                        UserOffice.office_id == office.id,
                    )
                )
                .order_by(Property.updated_at.desc())
            )

            # Set limit based on office name
            limit = 75 if "Mallorca, Spain" in office.name else 300
            properties = self.db_session.scalars(stmt.limit(limit)).all()

            # Map properties to idealista format
            mapped_properties = [
                map_property_to_idealista_property(property) for property in properties
            ]
            filtered_properties = list(filter(None, mapped_properties))

            if filtered_properties:
                office_properties[office.name] = filtered_properties

        return office_properties

    def run(self):
        date_now = datetime.now()
        self.s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)

        ### Creates request_id to log the process
        request_id = "[{0}]_integrations_json_idealista".format(
            date_now.strftime("%d/%m/%Y %H:%M:%S")
        )

        logger.info(
            "Started generating idealista json files by office",
            extra={"requestId": request_id},
        )

        try:
            ### Get Properties by Office
            office_properties = self.get_properties_by_office()

            for office_name, properties in office_properties.items():
                if office_name not in IDEALISTA_CUSTOMER_CODE:
                    logger.info(
                        f"Skipping office {office_name} because it doesn't have an ICC",
                        extra={"requestId": request_id},
                    )
                    continue

                ### Validate mapped Properties
                valid_properties = self.filter_not_valid_properties(
                    properties, request_id
                )

                ### Sets portal information
                idealista_json = self.add_envelope(
                    date_now, valid_properties, office_name
                )

                office_name_filename = "_".join(map(str.strip, office_name.split(",")))

                ### Generate filename for this office
                self.aws_filename = f"idealista_{office_name_filename.lower().replace(' ', '_')}{app_cfg.get_filename_environment_suffix()}.json"

                if not self.has_differences(idealista_json):
                    logger.info(
                        f"Idealista properties for office {office_name} are unchanged. JSON file generation aborted.",
                        extra={"requestId": request_id},
                    )
                    continue

                logger.info(
                    f"Idealista properties for office {office_name} are changed. JSON file generation started.",
                    extra={"requestId": request_id},
                )

                ### Generates JSON and Uploads it to FTP Server
                file_location = f"strandproperties/libs/idealista/properties_{office_name_filename.lower().replace(' ', '_')}{app_cfg.get_filename_environment_suffix()}.json"
                self.write_to_file(idealista_json, file_location)
                self.upload_to_s3(file_location)

                if app_cfg.env == Env.PRODUCTION:
                    date = date_now.strftime("%Y-%m-%dT%H:%M:%S")
                    filename = f"{date}_{IDEALISTA_CUSTOMER_CODE[office_name]}_{office_name.lower().replace(' ', '_')}.json"
                    self.upload_to_idealista_ftp(file_location, request_id, filename)
                    logger.info(
                        f"Uploaded idealista JSON for office {office_name}: {len(valid_properties)} properties.",
                    )
                else:
                    logger.info(
                        f"Skipped ftp upload for office {office_name}: only enabled in production",
                        extra={"requestId": request_id},
                    )

        except Exception as e:
            logger.error(e, extra={"requestId": request_id})
            raise e

    def add_envelope(
        self, date_now: datetime, properties: list[dict[str, Any]], office_name: str
    ):
        idealista_json = {
            "customerCountry": "Spain",
            "customerCode": IDEALISTA_CUSTOMER_CODE[office_name],
            "customerReference": "strandproperties",
            "customerSendDate": date_now.strftime("%Y/%m/%d %H:%M:%S"),
            "customerContact": {
                "contactName": "Strand Properties",
                "contactEmail": "<EMAIL>",
                "contactPrimaryPhonePrefix": "34",
                "contactPrimaryPhoneNumber": "676901519",
                "contactSecondaryPhonePrefix": "34",
                "contactSecondaryPhoneNumber": "660331176",
            },
            "customerProperties": properties,
        }

        return idealista_json
