"""
Maps from Property Type (Strand) to featureType (Idealista):
"""

import math
import re
from typing import Any, List, Optional

from strandproperties.constants import (
    DescriptionType,
    Language,
    ListingTypeEnum,
    language_isocode_mapper,
)
from strandproperties.libs.utils import get_strandproperties_property_slug
from strandproperties.models.property import Property, PropertyDescription

LISTING_TYPE_PRIORITY = {
    ListingTypeEnum.SALE: 1,
    ListingTypeEnum.RENT_SHORT: 2,
    ListingTypeEnum.RENT_LONG: 3,
}

_property_type_mapper = {
    "Apartment": "flat",
    "Bar": "premises_commercial",
    "Boat": "flat",
    "Building": "building",
    "Bungalow": "flat",
    "Business": "premises_commercial",
    "Castle": "building",
    "Chalet": "house",
    "Commercial Other": "premises_commercial",
    "Commercial Premises": "premises_commercial",
    "Cortijo": "rustic_cortijo",
    "Country House": "house",
    "Development Land": "land_countrybuildable",
    "Discotheque": "premises_commercial",
    "Duplex": "flat",
    "Duplex Penthouse": "flat",
    "Estate": "house",
    "Finca": "rustic",
    "Flat": "flat",
    "Golf Course": "land_countrynonbuildable",
    "Golf Plot": "land_countrynonbuildable",
    "Ground Floor Apartment": "flat",
    "Ground Floor Duplex": "flat",
    "Hotel": "building",
    "Hotel Plot": "land_countrybuildable",
    "House": "house",
    "Industrial Land": "land",
    "Industrial Premises": "premises_industrial",
    "Investment": "premises_commercial",
    "Loft": "flat",
    "Mansion": "house",
    "Mooring": "building",
    "Office": "office",
    "Office Units": "office",
    "Palace": "building",
    "Parking": "garage",
    "Penthouse": "flat",
    "Plot": "land",
    "Residential Plot": "land_urban",
    "Restaurant": "premises_commercial",
    "Riad": "house",
    "Rustic Plot": "land_countrybuildable",
    "Semi Detached House": "house_semidetached",
    "Semi Detached Villa": "house_semidetached",
    "Shop": "premises_commercial",
    "Shopping Centre": "premises_commercial",
    "Store Room": "storage",
    "Studio": "flat",
    "Supermarket": "premises_commercial",
    "Town House": "house_terraced",
    "Triplex": "flat",
    "Unique Building": "building",
    "Villa": "house",
}


def delete_none_values(json: dict[str, Any]):
    """
    Delete keys with the value ``None`` in a dictionary, recursively.

    This alters the input so you may wish to ``copy`` the dict first.
    """
    for key, value in list(json.items()):
        if value is None or value == "":
            del json[key]
        elif isinstance(value, dict):
            delete_none_values(value)
        elif isinstance(value, str):
            json[key] = json[key].replace("\r\n", " ").strip()
    return json


def get_property_address(property: Property) -> Optional[str]:
    return property.private_info["location"].get("address", None)


def get_formatted_descriptions(descriptions: List[PropertyDescription]):
    descriptions_dict: dict[str, list[str]] = {}
    for lang in Language:
        descriptions_dict[lang.value] = []

    result = []

    for desc in descriptions:
        description = (
            desc.description.replace("\r\n", "\n")
            .rstrip()[: 4000 + 1 - len("...")]
            .split(".")
        )
        if len(description) > 1:
            description = " ".join(description[0:-1]) + "."
        else:
            description = "".join(description[0]) + "."

        if desc.type == DescriptionType.FULL:
            descriptions_dict[desc.language] = [description]

    for key, value in descriptions_dict.items():
        if not value:
            continue

        result.append(
            {
                "descriptionLanguage": language_isocode_mapper[key],
                "descriptionText": value[0],
            },
        )

    return result


idealista_home_types: List[str] = [
    "flat",
    "house",
    "house_andar_moradia",
    "house_independent",
    "house_semidetached",
    "house_terraced",
    "house_villa",
    "rustic",
    "rustic_house",
    "rustic_village",
    "rustic_castle",
    "rustic_palace",
    "rustic_baita",
    "rustic_rural",
    "rustic_casalecascina",
    "rustic_caseron",
    "rustic_cortijo",
    "rustic_masia",
    "rustic_masseria",
    "rustic_moinho",
    "rustic_montealentejano",
    "rustic_quinta",
    "rustic_solar",
    "rustic_terrera",
    "rustic_torre",
    "rustic_trullo",
]

idealista_room_types = ["room_shared_flat", "room_shared_chalet"]


# This method is to round the number to the nearest that is multiple of 0.01
# Due to floating point processing in Python, some value / 0.01 does not give integer
def round_nearest_001(x):
    x = round(x, 2)

    while True:
        if (x / 0.01).is_integer():
            return x

        x = round(x + 0.01, 2)


def get_formatted_features(property: Property):
    _property_features_mapper = {
        "featuresAccess24h": {
            "internal_names": ["24h Service"],
            "property_types": ["storage"],
            "value": True,
        },
        "featuresAllowPets": {
            "internal_names": ["Pets Allowed"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresConditionedAir": {
            "internal_names": ["Air Conditioning"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresDoorman": {
            "internal_names": ["Doorman"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
            ],
            "value": True,
        },
        "featuresEquippedKitchen": {
            "internal_names": ["Kitchen equipped"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresEquippedWithFurniture": {
            "internal_names": ["Fully Furnished"],
            "property_types": idealista_home_types + idealista_room_types,
            "value": True,
        },
        "featuresHandicapAdaptedAccess": {
            "internal_names": ["Handicap Accessible"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresHeating": {
            "internal_names": [
                "Central Heating",
                "Floor Heating",
                "Gasoil heating",
                "Gas Heating",
                "Underfloor heating (throughout)",
            ],
            "property_types": [
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresLiftAvailable": {
            "internal_names": ["Lift"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "garage",
            ],
            "value": True,
        },
        "featuresParkingAvailable": {
            "internal_names": ["Parking Spaces"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresPool": {
            "internal_names": [
                "Community Pool",
                "Indoor pool",
                "Outdoor Pool",
                "Pool",
                "Saltwater swimming pool",
            ],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresSecurity24h": {
            "internal_names": ["24h Security Service"],
            "property_types": ["storage"],
            "value": True,
        },
        "featuresSecurityAlarm": {
            "internal_names": ["Alarm"],
            "property_types": [
                "garage",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresStorage": {
            "internal_names": ["Storage Room"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": True,
        },
        "featuresTerrace": {
            "internal_names": ["Terrace"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresWardrobes": {
            "internal_names": ["Fitted Wardrobes"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresBalcony": {
            "internal_names": ["Balcony"],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": True,
        },
        "featuresAreaConstructed": {
            "internal_names": [],
            "property_types": [
                "building",
                "garage",
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
                "storage",
            ],
            "value": property.built_area or property.interior_area or None,
        },
        "featuresAreaPlot": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "land",
                "land_urban",
                "land_countrybuildable",
                "land_countrynonbuildable",
            ],
            "value": property.plot_area or None,
        },
        "featuresAreaUsable": {
            "internal_names": [],
            "property_types": [
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
            ],
            "value": property.interior_area,
        },
        "featuresBathroomNumber": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": property.bathrooms or 1,
        },
        "featuresBedroomNumber": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": property.bedrooms or 1,
        },
        "featuresBuiltYear": {
            "internal_names": [],
            "property_types": [
                "building",
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": property.built_year or None,
        },
        "featuresGarden": {
            "internal_names": [],
            "property_types": [
                "building",
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": bool(property.garden_types and len(property.garden_types) > 0)
            or None,
        },
        "featuresOrientationEast": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
            ],
            "value": any(
                "East" in orientation.name for orientation in property.orientations
            )
            or None,
        },
        "featuresOrientationNorth": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
            ],
            "value": any(
                "North" in orientation.name for orientation in property.orientations
            )
            or None,
        },
        "featuresOrientationSouth": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
            ],
            "value": any(
                "South" in orientation.name for orientation in property.orientations
            )
            or None,
        },
        "featuresOrientationWest": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
                "office",
            ],
            "value": any(
                "West" in orientation.name for orientation in property.orientations
            )
            or None,
        },
        "featuresTenantNumber": {
            "internal_names": [],
            "property_types": [
                "flat",
                "house",
                "house_semidetached",
                "house_terraced",
                "rustic",
                "rustic_cortijo",
            ],
            "value": property.pax if (property.pax and property.pax <= 10) else None,
        },
        "featuresParkingSpacesNumber": {
            "internal_names": [],
            "property_types": [
                "building",
                "office",
            ],
            "value": property.parking_spaces or None,
        },
        "featuresFloorsProperty": {
            "internal_names": [],
            "property_types": [
                "office",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": property.total_floors or None,
        },
        "featuresFloorsBuilding": {
            "internal_names": [],
            "property_types": [
                "flat",
                "office",
                "building",
                "house",
                "house_semidetached",
                "house_terraced",
            ],
            "value": property.floor if property.floor and property.floor > 0 else None,
        },
        "featuresEnergyCertificatePerformance": {
            "internal_names": [],
            "property_types": [
                "building",
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": (
                round_nearest_001(property.certificate_consumption_value)
                if property.certificate_consumption_value
                else None
            ),
        },
        "featuresEnergyCertificateRating": {
            "internal_names": [],
            "property_types": [
                "building",
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": property.certificate_consumption_rating,
        },
        "featuresEnergyCertificateEmissionsRating": {
            "internal_names": [],
            "property_types": [
                "building",
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": property.certificate_emission_rating,
        },
        "featuresEnergyCertificateEmissionsValue": {
            "internal_names": [],
            "property_types": [
                "building",
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
                "premises",
                "premises_commercial",
                "premises_industrial",
            ],
            "value": (
                round_nearest_001(property.certificate_emission_value)
                if (
                    property.certificate_emission_value
                    and property.certificate_emission_value <= 10000
                )
                else None
            ),
        },
        "featuresWindowsLocation": {
            "internal_names": [
                "Sea View",
                "Country View",
                "Mountain View",
                "Golf View",
                "Panoramic View",
                "Street View",
                "Lake View",
                "Urban View",
                "Marina View",
                "Partial Sea Views",
            ],
            "property_types": [
                "room_shared_flat",
                "room_shared_chalet",
                "office",
                "flat",
                "house",
                "house_andar_moradia",
                "house_independent",
                "house_semidetached",
                "house_terraced",
                "house_villa",
                "rustic",
                "rustic_house",
                "rustic_village",
                "rustic_castle",
                "rustic_palace",
                "rustic_baita",
                "rustic_rural",
                "rustic_casalecascina",
                "rustic_caseron",
                "rustic_cortijo",
                "rustic_masia",
                "rustic_masseria",
                "rustic_moinho",
                "rustic_montealentejano",
                "rustic_quinta",
                "rustic_solar",
                "rustic_terrera",
                "rustic_torre",
                "rustic_trullo",
            ],
            "value": "exterior",
            "missing_feature_value": "interior",
        },
    }

    result = {}

    for idealista_feature in _property_features_mapper:
        if (
            _property_type_mapper[property.property_type]
            in _property_features_mapper[idealista_feature]["property_types"]
        ):
            if (
                any(
                    any(feature.name == internal_name for feature in property.features)
                    for internal_name in _property_features_mapper[idealista_feature][
                        "internal_names"
                    ]
                )
            ) or not _property_features_mapper[idealista_feature]["internal_names"]:
                result[idealista_feature] = _property_features_mapper[
                    idealista_feature
                ]["value"]
            else:
                result[idealista_feature] = _property_features_mapper[
                    idealista_feature
                ].get("missing_feature_value", None)

    return result


MAX_LENGTH_FOR_PHONE_PREFIX = 3
MIN_LENGTH_FOR_PHONE_PREFIX = 1
MAX_LENGTH_FOR_PHONE_NUMBER = 12
MIN_LENGTH_FOR_PHONE_NUMBER = 5
MAX_LENGTH_PHONE_PLUS_PREFIX = MAX_LENGTH_FOR_PHONE_PREFIX + MAX_LENGTH_FOR_PHONE_NUMBER
MIN_LENGTH_PHONE_PLUS_PREFIX = MIN_LENGTH_FOR_PHONE_PREFIX + MIN_LENGTH_FOR_PHONE_NUMBER


# INFO: given that we don't have a prefix + number separated fields
# in the system yet, we're considering a number as a string of 9 digits.
# TODO: change this once we add the prefix and number options in the system.
def get_prefix_and_phone_number(phone_number: str) -> Optional[tuple[str, str]]:
    prefix_pattern = r"^[1-9][0-9]{0,2}$"
    phone_number_pattern = r"^[0-9]{5,12}$"

    def is_length_valid(data: str) -> bool:
        if MIN_LENGTH_PHONE_PLUS_PREFIX <= len(data) <= MAX_LENGTH_PHONE_PLUS_PREFIX:
            return True

        return False

    def sanitise_data(data: str) -> Optional[str]:
        data = data.strip().replace("+", "").replace(" ", "")

        sanitised = []
        for ch in data:
            if ch.isdecimal():
                sanitised.append(ch)
            else:
                break

        number_plus_prefix_sanitised = "".join(sanitised)

        if not is_length_valid(number_plus_prefix_sanitised):
            return None

        return number_plus_prefix_sanitised

    def try_to_get_valid_prefix_and_number(
        phone_number: str,
    ) -> Optional[tuple[str, str]]:
        # considering a number as a 9-digit algarisms length
        number = phone_number[-9:]
        prefix = phone_number[:-9]

        if MIN_LENGTH_FOR_PHONE_PREFIX <= len(prefix) <= MAX_LENGTH_FOR_PHONE_PREFIX:
            return (prefix, number)

        return None

    data_sanitised = sanitise_data(phone_number)
    if not data_sanitised:
        return None

    res = try_to_get_valid_prefix_and_number(data_sanitised)
    if res is None:
        return None

    (prefix_sanitised, phone_number_sanitised) = res
    if not re.search(prefix_pattern, prefix_sanitised):
        return None
    if not re.search(phone_number_pattern, phone_number_sanitised):
        return None

    return res


def are_coordinates_valid(property: Property) -> bool:
    return (
        property.longitude
        and property.latitude
        and (-180 <= property.longitude <= 180)
        and (-90 <= property.latitude <= 90)
    )


def get_property_contact(property: Property):
    property_contact = {
        "contactName": "Strand Properties",
        "contactEmail": "<EMAIL>",
        "contactPrimaryPhonePrefix": "34",
        "contactPrimaryPhoneNumber": "676901519",
        "contactSecondaryPhonePrefix": "34",
        "contactSecondaryPhoneNumber": "660331176",
    }
    realtors = property.realtor_users
    if len(realtors) > 0:
        realtor = realtors[0]
        name = f"{realtor.first_name} {realtor.last_name}"
        property_contact["contactName"] = name

    return property_contact


def map_property_to_idealista_property(property: Property):
    if not property.listing_types:
        return
    else:
        listing_type = next(
            (
                lt.name
                for lt in sorted(
                    property.listing_types,
                    key=lambda t: LISTING_TYPE_PRIORITY.get(t.name, 999),
                )
            )
        )

    valid_coordinates = are_coordinates_valid(property) or None

    property_code = (
        property.idealista_code if property.idealista_code else str(property.id)
    )

    images_to_publish = sorted(
        filter(lambda i: not i.is_hidden, property.images), key=lambda i: i.order or 0
    )

    json_object = {
        "propertyAddress": {
            "addressStreetName": get_property_address(property),
            "addressTown": (
                property._area_level_1.name if property._area_level_1 else None
            ),
            "addressVisibility": "hidden",
            "addressCoordinatesLongitude": (
                property.longitude if valid_coordinates else None
            ),
            "addressCoordinatesLatitude": (
                property.latitude if valid_coordinates else None
            ),
            "addressCoordinatesPrecision": ("moved" if valid_coordinates else None),
            "addressPostalCode": property.private_info["location"].get(
                "postCode", None
            ),
            "addressCountry": "Spain",
            "addressFloor": str(property.floor) if property.floor else None,
        },
        "propertyCode": property_code,
        "propertyReference": property.reference,
        "propertyContact": get_property_contact(property),
        "propertyFeatures": {
            "featuresType": _property_type_mapper[property._property_type.name],
        },
        "propertyOperation": {
            "operationType": (
                "sale" if listing_type == ListingTypeEnum.SALE.value else "rent"
            ),
            "operationPrice": map_listing_type_to_price(property, listing_type),
        },
        "propertyVisibility": "idealista",
        "propertyUrl": get_strandproperties_property_slug(
            reference=property.reference, portals=property.portals
        ),
        "propertyVirtualTours": get_virtual_tour(property),
        "propertyDescriptions": get_formatted_descriptions(property.descriptions),
        "propertyImages": [
            {"imageUrl": image.url, "imageOrder": image.order + 1}
            for image in images_to_publish
        ],
    }

    json_object["propertyFeatures"] = {
        **json_object["propertyFeatures"],
        **get_formatted_features(property),
    }

    return delete_none_values(json_object.copy())


def map_listing_type_to_price(property: Property, listing_type: str) -> int:
    property_type_to_price = {
        ListingTypeEnum.SALE: property.price_sale or 0,
        ListingTypeEnum.RENT_LONG: property.price_rent_long_term or 0,
        ListingTypeEnum.RENT_SHORT: property.price_rent_short_term or 0,
    }

    return property_type_to_price.get(listing_type, 0)


def get_virtual_tour(property: Property):
    visible = list(filter(lambda v: not v.is_hidden, property.video_tours))
    ret = None
    if len(visible) > 0:
        ret = {"virtualTour": {"virtualTourUrl": visible[0].url}}
    return ret
