import json
import time
import uuid

from sqlalchemy import delete, select
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.constants import PROPERTY_LIST_TYPE_PARAMS, CountryCode
from strandproperties.libs.matchmaking.utils import LogLevel, match_making_logging
from strandproperties.libs.utils import preprocess_params, update_model_from_schema
from strandproperties.models.match_making import MatchMaking, MatchMakingProperty
from strandproperties.models.organization import Organization
from strandproperties.schemas.match_making import MatchMakingCreateEdit
from strandproperties.schemas.param import PropertyFilterParam
from strandproperties.scripts.base import BaseScript
from strandproperties.views.property import query_properties


class PropertiesMatchMakingUpdate(BaseScript):
    def _handle_many_to_many_relationship(
        self, match_making_id, selected_ids, table, key
    ):
        current_ids = self.db_session.scalars(
            select(getattr(table, key)).where(table.match_making_id == match_making_id)
        ).all()

        for selected_id in current_ids:
            if selected_id not in selected_ids:
                self.db_session.execute(
                    delete(table).where(
                        table.match_making_id == match_making_id,
                        getattr(table, key) == selected_id,
                    )
                )

        for selected_id in selected_ids:
            if selected_id not in current_ids:
                model_item = table()
                setattr(model_item, key, selected_id)
                model_item.match_making_id = match_making_id
                self.db_session.add(model_item)

    def run(self):
        match_making_logging(
            message="Running update properties match making", log_level=LogLevel.INFO
        )

        try:
            unique_id = f"{int(time.time())}_{uuid.uuid4()}"
            match_making_logging(
                message=f"Start updating properties with ID {unique_id}",
                log_level=LogLevel.INFO,
            )
            es_org = self.db_session.scalars(
                select(Organization).where(
                    Organization.country_code == CountryCode.SPAIN
                )
            ).one_or_none()

            match_makings = self.db_session.scalars(select(MatchMaking)).unique().all()
            match_making_logging(
                message=f"Number match_makings: {len(match_makings)}",
                log_level=LogLevel.INFO,
            )
        except SQLAlchemyError as e:
            match_making_logging(
                message=f"Error querying match making: {e}",
                log_level=LogLevel.ERROR,
            )
            return

        for match_making in match_makings:
            # Query match making properties
            try:
                match_making_logging(
                    message=f"Query properties for {match_making.id=}",
                    log_level=LogLevel.INFO,
                )
                filters = PropertyFilterParam(
                    **preprocess_params(
                        json.loads(match_making.params),
                        PROPERTY_LIST_TYPE_PARAMS,
                    )
                )
                filters.status = ["Published"]
                filters.is_match_making_created = True
                results, _ = query_properties(
                    filters,
                    es_org.id,
                    self.db_session,
                    unique_id,
                )

                property_ids = [property.id for property in results]

                match_making_logging(
                    message=f"Updating match making ID: {match_making.id} and property_ids length: {len(property_ids)}",
                    log_level=LogLevel.INFO,
                )
                params = MatchMakingCreateEdit(
                    title=match_making.title,
                    notes=match_making.notes,
                    params=filters.model_dump_json(),
                    lead_id=match_making.lead_id,
                    assigned_users=[user.id for user in match_making.assigned_to_users],
                    contact_ids=[contact.id for contact in match_making.contacts],
                    property_ids=property_ids,
                )

            except SQLAlchemyError as e:
                match_making_logging(
                    message=f"Error query properties match making id {match_making.id}: {e}",
                    log_level=LogLevel.ERROR,
                )
                continue

            # Update new properties
            try:
                match_making_logging(
                    message=f"Updating params: {params.params=}",
                    log_level=LogLevel.INFO,
                )
                update_model_from_schema(
                    model_obj=match_making,
                    schema_obj=params,
                    exclude={"assigned_users", "contact_ids", "property_ids"},
                )
            except Exception as e:
                match_making_logging(
                    message=f"Error update match making id {match_making.id} with error: {e}",
                    log_level=LogLevel.ERROR,
                )
                continue

            # Update relationships between properties and match making
            try:
                if len(property_ids) > 0:
                    self._handle_many_to_many_relationship(
                        match_making.id,
                        property_ids,
                        MatchMakingProperty,
                        "property_id",
                    )
                self.db_session.flush()
                self.db_session.commit()

            except Exception as e:
                match_making_logging(
                    message=f"Error update match making id {match_making.id} with error: {e}",
                    log_level=LogLevel.ERROR,
                )
                self.db_session.rollback()
                continue

            match_making_logging(
                message=f"Update successfully match making: {match_making.id}",
                log_level=LogLevel.INFO,
            )
