import base64
import json
import time
import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Optional

from pydantic import ValidationError
from requests import HTTPError
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.config import Env, app_cfg
from strandproperties.constants import (
    MAIL_GUN_WHITE_LIST_EMAILS,
    PROPERTY_LIST_TYPE_PARAMS,
    CountryCode,
    ListingTypeEnum,
)
from strandproperties.libs.matchmaking.utils import LogLevel, match_making_logging
from strandproperties.libs.utils import preprocess_params
from strandproperties.models.contact import Contact
from strandproperties.models.match_making import (
    EmailTemplate,
    MatchMaking,
    MatchMaking<PERSON>ontact,
    MatchMakingPropertyContact,
)
from strandproperties.models.organization import Organization
from strandproperties.models.property import Property
from strandproperties.schemas.param import PropertyFilterParam
from strandproperties.scripts.base import BaseScript
from strandproperties.utils.publisher.publish_services import StrandMailGunPublisher
from strandproperties.utils.schema.property import PropertyEmailAttributes
from strandproperties.views.property import query_match_making_properties


class PropertiesNewsletter(BaseScript):
    environment = app_cfg.env
    mailgun_api_key = app_cfg.mailgun_api_key
    mailgun_publisher = StrandMailGunPublisher(
        api_key=mailgun_api_key, whitelist_emails=MAIL_GUN_WHITE_LIST_EMAILS
    )
    is_production_or_local = environment not in {
        Env.DEVELOPMENT,
        Env.STAGING,
    }
    PAGE = 1
    PAGE_SIZE = 10
    PROPERTY_STATUS = ["Published"]
    DEFAULT_EMAIL_SENDER = "<EMAIL>"

    def query_match_making(self, contact_id) -> List[MatchMaking]:
        try:
            db = self.db_session
            stmt = select(MatchMaking).where(
                MatchMaking.contacts.any(Contact.id == contact_id)
                & (MatchMaking.is_auto_sent.is_(True))
            )

            if self.is_production_or_local:
                stmt = stmt.where(
                    (MatchMaking.last_sent_at.is_(None))
                    | (
                        MatchMaking.last_sent_at
                        < datetime.now(timezone.utc) - timedelta(days=2)
                    )
                )

            results = db.scalars(stmt).unique().all()

            return results
        except SQLAlchemyError as e:
            match_making_logging(
                message=f"Error querying match_making: {e}", log_level=LogLevel.ERROR
            )
            return []

    def create_property_list(self, properties: List[Property], realtor_email: str):
        properties_attributes = []
        for prop in properties:
            listing_types_names = [
                listing_type.name for listing_type in prop.listing_types
            ]
            price = prop.price_sale
            if ListingTypeEnum.RENT_LONG in listing_types_names:
                price = prop.price_rent_long_term
            if ListingTypeEnum.RENT_LONG in listing_types_names:
                price = prop.price_rent_short_term

            valid_images = [
                img.url for img in prop.images if "cloudfront.net" in img.url
            ]

            if len(valid_images) > 0:
                image_url = valid_images[0]
            elif len(prop.images) > 0:
                image_url = prop.images[0].url
            else:
                image_url = None

            encoded_bytes = base64.b64encode(
                realtor_email.split("@")[0].encode("utf-8")
            ).decode("utf-8")
            price_string = f"{price:,}".replace(",", " ")
            title = prop.title
            if prop.location_from_areas:
                title = f"{prop.property_type} in {prop.location_from_areas}"

            description = f"{prop.built_area} m² ・&nbsp;{price_string} € ・&nbsp;{prop.bedrooms}&nbsp;bedrooms ・&nbsp;{prop.bathrooms}&nbsp;bathrooms ・&nbsp;#{prop.reference}"
            if not prop.bathrooms and not prop.bedrooms:
                description = f"{prop.built_area} m² ・&nbsp;{price_string} € ・&nbsp;#{prop.reference}"
            elif not prop.bathrooms:
                description = f"{prop.built_area} m² ・&nbsp;{price_string} € ・&nbsp;{prop.bedrooms}&nbsp;bedrooms ・&nbsp;#{prop.reference}"
            elif not prop.bedrooms:
                description = f"{prop.built_area} m² ・&nbsp;{price_string} € ・&nbsp;{prop.bathrooms}&nbsp;bathrooms ・&nbsp;#{prop.reference}"
            properties_attributes.append(
                PropertyEmailAttributes(
                    reference_id=prop.reference,
                    type=prop.property_type,
                    location=prop.location_from_areas,
                    size=prop.built_area,
                    price=price,
                    bedrooms=prop.bedrooms,
                    bathrooms=prop.bathrooms,
                    link=f"https://strandproperties.com/single-property/?ref={prop.reference}&a={encoded_bytes}",
                    image_url=image_url,
                    price_string=price_string,
                    title=title,
                    description=description,
                )
            )
        match_making_logging(
            message=f"Properties number: {len(properties_attributes)}",
            log_level=LogLevel.INFO,
        )
        return properties_attributes

    def record_sent_properties(
        self, properties: List[Property], contact_id: int, email_id: str
    ):
        for prop in properties:
            try:
                property_contact = MatchMakingPropertyContact(
                    property_id=prop.id,
                    contact_id=contact_id,
                    is_sent=True,
                    email_id=email_id,
                )
                self.db_session.add(property_contact)
                self.db_session.commit()
            except SQLAlchemyError as e:
                match_making_logging(
                    f"Error recording sent property: {e}", log_level=LogLevel.ERROR
                )

    def update_last_sent_time(self, match_making_id: int):
        try:
            # Fetch the MatchMaking object
            match_making_to_update = self.db_session.scalars(
                select(MatchMaking).where(MatchMaking.id == match_making_id)
            ).one_or_none()

            if match_making_to_update:
                match_making_to_update.last_sent_at = datetime.now(timezone.utc)
                self.db_session.add(match_making_to_update)
                self.db_session.commit()
                match_making_logging(
                    message=f"Successfully updated last_sent_at for MatchMaking id {match_making_id}",
                    log_level=LogLevel.INFO,
                )
            else:
                match_making_logging(
                    message=f"MatchMaking record with id {match_making_id} not found. Cannot update last_sent_at.",
                    log_level=LogLevel.WARNING,
                )

        except SQLAlchemyError as e:
            self.db_session.rollback()
            match_making_logging(
                message=f"SQLAlchemyError updating last_sent_at for MatchMaking id {match_making_id}: {e}",
                log_level=LogLevel.ERROR,
            )
        except Exception as e:  # Catch any other unexpected errors
            self.db_session.rollback()
            match_making_logging(
                message=f"Unexpected error updating last_sent_at for MatchMaking id {match_making_id}: {e}",
                log_level=LogLevel.ERROR,
            )

    def process_contact(
        self,
        realtor_email: str,
        contact: Contact,
        email_template: Optional[EmailTemplate],
        match_making_ids: list[int],
        properties: list[Property],
    ):
        property_list = self.create_property_list(properties, realtor_email)
        try:
            match_making_logging(
                message=f"Properties newsletter with r_email={realtor_email} contact_email={contact.email} contact_id={contact.id}",
                log_level=LogLevel.INFO,
            )
            response = self.mailgun_publisher.send_properties_newsletter(
                realtor_email=realtor_email,
                receivers=[contact.email],
                subject=app_cfg.newsletter_subject,
                properties=property_list,
                subscription_id=contact.subscription_id,
                match_making_ids=match_making_ids,
                email_template=email_template,
            )

            if not response:
                return
            for res in response:
                if hasattr(res, "status_code") and res.status_code == 200:
                    match_making_logging(
                        message=f"Newsletter from {realtor_email} sent to {contact.email}: {res}",
                        log_level=LogLevel.INFO,
                    )

                    try:
                        email_id = res.json()["id"]
                        # if self.is_production_or_local:        # Enable this code when we do not want to record the properties sent
                        self.record_sent_properties(properties, contact.id, email_id)
                        for match_making_id in match_making_ids:
                            self.update_last_sent_time(match_making_id)
                    except (KeyError, ValueError, AttributeError) as e:
                        match_making_logging(
                            message=f"Error extracting email ID for contact {contact.id}: {e}",
                            log_level=LogLevel.ERROR,
                        )
                else:
                    match_making_logging(
                        message=f"Failed to send email to contact id {contact.id}: {res}",
                        log_level=LogLevel.ERROR,
                    )
        except HTTPError as e:
            match_making_logging(
                message=f"Error sending email to contact id {contact.id}: {e}",
                log_level=LogLevel.ERROR,
            )

    def update_combined_params(self, param_json: dict, combined_params: dict):
        for key, value in param_json.items():
            if value is None or key in {
                "is_match_making_created",
                "match_making_id",
                "is_disliked",
                "is_shortlist",
            }:
                continue  # Skip None values

            if key in {
                "from_created_at",
                "from_updated_at",
                "until_updated_at",
                "until_created_at",
            }:
                # Convert to datetime for comparison
                if isinstance(value, str):
                    value = datetime.fromisoformat(value)
                if key not in combined_params:
                    combined_params[key] = value
                else:
                    # Convert existing value to datetime if it's a string
                    if isinstance(combined_params[key], str):
                        combined_params[key] = datetime.fromisoformat(
                            combined_params[key]
                        )
                    # Use min for 'from' keys and max for 'until' keys
                    if key in {"from_created_at", "from_updated_at"}:
                        combined_params[key] = min(combined_params[key], value)
                    else:
                        combined_params[key] = max(combined_params[key], value)
                continue

            if key in {
                "isStrandified",
                "has_pool",
                "has_air_conditioning",
                "is_exclusive",
            }:
                # Always take True value over False
                if key not in combined_params:
                    combined_params[key] = value
                elif value is True:
                    combined_params[key] = True
                # If value is False, do not change if True already exists
                continue

            if key in {"priceMax", "sizeMax", "price_max", "size_max"}:
                if key not in combined_params:
                    combined_params[key] = value
                else:
                    combined_params[key] = max(combined_params[key], value)

            elif key in {
                "min_bathrooms",
                "min_bedrooms",
                "priceMin",
                "sizeMin",
                "price_min",
                "size_min",
            }:
                if key not in combined_params:
                    combined_params[key] = value
                else:
                    combined_params[key] = min(combined_params[key], value)

            elif key in {"dataSource", "conditions", "soldBy"}:
                if key not in combined_params:
                    combined_params[key] = value
                else:
                    combined_params[key] = None

            else:
                if key not in combined_params:
                    # If the key doesn't exist, create a new entry
                    combined_params[key] = (
                        value
                        if isinstance(value, list)
                        or key in {"page", "page_size", "order_by"}
                        else [value]
                    )
                else:
                    # If the key exists, merge the values
                    if isinstance(value, list):
                        combined_params[key] = list(set(combined_params[key] + value))
                    else:
                        combined_params[key] = value

    def run(self):
        try:
            match_making_logging(
                message="Running querying properties newsletter",
                log_level=LogLevel.INFO,
            )
            match_making_logging(
                message=f"Env: {self.environment=} - {self.is_production_or_local=}",
                log_level=LogLevel.INFO,
            )
            # Subquery to filter contacts that are in MatchMakingContact
            subquery_matchmaking_contact = select(
                MatchMakingContact.contact_id
            ).distinct()
            all_contacts = (
                self.db_session.scalars(
                    select(Contact)
                    .where(Contact.email.is_not(None))
                    .where(Contact.id.in_(subquery_matchmaking_contact))
                )
                .unique()
                .all()
            )
            match_making_logging(
                message=f"{len(all_contacts)=}",
                log_level=LogLevel.INFO,
            )
            es_org = self.db_session.scalars(
                select(Organization).where(
                    Organization.country_code == CountryCode.SPAIN
                )
            ).one_or_none()
            unique_id = f"{int(time.time())}_{uuid.uuid4()}"

            for contact in all_contacts:
                match_making_logging(
                    message=f"Processing contact: {contact.email=}",
                    log_level=LogLevel.INFO,
                )

                combined_params = {}
                realtor = None
                auto_sent_match_making_ids = []
                email_template = None
                match_makings = self.query_match_making(contact.id)

                if len(match_makings) == 0:
                    continue

                oldest_match_making = min(match_makings, key=lambda x: x.created_at)
                for match_making in match_makings:
                    email_template = (
                        match_making.email_template
                        if match_making.email_template
                        else None
                    )
                    if len(match_making.assigned_to_users) > 0:
                        realtor = match_making.assigned_to_users[0]
                    param_json = json.loads(match_making.params)
                    match_making_logging(
                        message=f"param_json: {param_json}",
                        log_level=LogLevel.INFO,
                    )
                    self.update_combined_params(param_json, combined_params)
                    auto_sent_match_making_ids.append(match_making.id)
                match_making_logging(
                    message=f"Number of match making: {len(auto_sent_match_making_ids)}",
                    log_level=LogLevel.INFO,
                )
                if len(auto_sent_match_making_ids) == 0:
                    continue
                if realtor is None:
                    match_making_logging(
                        message=f"No realtor found, using {self.DEFAULT_EMAIL_SENDER} instead",
                        log_level=LogLevel.INFO,
                    )
                combined_params["page"] = self.PAGE
                combined_params["page_size"] = self.PAGE_SIZE
                combined_params["status"] = self.PROPERTY_STATUS
                try:
                    filters = PropertyFilterParam(
                        **preprocess_params(
                            combined_params,
                            PROPERTY_LIST_TYPE_PARAMS,
                        )
                    )
                except ValidationError as e:
                    match_making_logging(
                        message=f"Validation error: {e}, {combined_params=}, continuing to next contact.",
                        log_level=LogLevel.INFO,
                    )
                    continue
                filters.is_match_making_created = True
                if self.is_production_or_local:
                    filters.from_created_at = oldest_match_making.created_at

                properties, _ = query_match_making_properties(
                    params=filters,
                    organization_id=es_org.id,
                    db=self.db_session,
                    request_id=unique_id,
                    contact_id=contact.id,
                )
                match_making_logging(
                    message=f"properties_match_making rows: {len(properties)}",
                    log_level=LogLevel.INFO,
                )
                if len(properties) == 0:
                    match_making_logging(
                        message="No match making properties found",
                        log_level=LogLevel.INFO,
                    )
                    continue

                self.process_contact(
                    realtor_email=(
                        realtor.email if realtor else self.DEFAULT_EMAIL_SENDER
                    ),
                    contact=contact,
                    properties=properties,
                    email_template=email_template,
                    match_making_ids=auto_sent_match_making_ids,
                )

        except SQLAlchemyError as e:
            match_making_logging(
                message=f"Error querying match making: {e}", log_level=LogLevel.ERROR
            )
            return
        except Exception as e:  # Catching other exceptions
            match_making_logging(
                message=f"An unexpected error occurred: {e}", log_level=LogLevel.ERROR
            )
            return
