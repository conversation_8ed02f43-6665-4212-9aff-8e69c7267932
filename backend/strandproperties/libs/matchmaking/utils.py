from enum import Enum

from strandproperties.logger import logger


class LogLevel(Enum):
    """Enumeration of available logging levels for matchmaking operations.

    Attributes:
        DEBUG: Debug level logging
        INFO: Info level logging
        ERROR: Error level logging
    """

    DEBUG = "DEBUG"
    INFO = "INFO"
    ERROR = "ERROR"


def match_making_logging(message: str, log_level: str = None):
    """Log messages with MatchMaking tag for matchmaking-related operations.

    Args:
        message (str): The message to be logged
    """
    extra = {"tag": "MatchMaking"}
    if log_level == LogLevel.ERROR:
        logger.error(message, extra=extra)
    elif log_level == LogLevel.INFO:
        logger.info(message, extra=extra)
    else:
        logger.debug(message, extra=extra)
