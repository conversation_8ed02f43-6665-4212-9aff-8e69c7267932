from strandproperties.models.property import Property

HOUSE_PROPERTY_TYPE_IDS = [1, 3]
APARTMENT_PROPERTY_TYPE_IDS = [2, 4]
TOWNHOUSE_PROPERTY_TYPE_IDS = [9, 10]

FULLY_FURNISHED_FEATURE_ID = 43
PARTLY_FURNISHED_FEATURE_ID = 42
UNFURNISHED_FEATURE_ID = 89

CENTRAL_HEATING_FEATURE_ID = 36
CENTRAL_HEATING_BY_RADIATORS_FEATURE_ID = 144


def map_property_type(property: Property):
    if property.property_type_id in HOUSE_PROPERTY_TYPE_IDS:
        return "house"
    if property.property_type_id in APARTMENT_PROPERTY_TYPE_IDS:
        return "apartment"
    if property.property_type_id in TOWNHOUSE_PROPERTY_TYPE_IDS:
        return "townhouse"
    return "other"


def map_furnish_type(feature_ids: list[int]):
    for feature_id in feature_ids:
        if feature_id == FULLY_FURNISHED_FEATURE_ID:
            return "furnished"
        if feature_id == PARTLY_FURNISHED_FEATURE_ID:
            return "semi-furnished"
        if feature_id == UNFURNISHED_FEATURE_ID:
            return "unfurnished"
    return ""


def map_heating_type(feature_ids: list[int]):
    for feature_id in feature_ids:
        if (
            feature_id == CENTRAL_HEATING_FEATURE_ID
            or feature_id == CENTRAL_HEATING_BY_RADIATORS_FEATURE_ID
        ):
            return "central"
    return ""


def map_parking_type(property: Property):
    if len(property.garage_types) == 0:
        return "none"
    return "garage"


def map_custom_label(property: Property):
    reference_without_numbers = "".join(
        [i for i in property.reference if not i.isdigit()]
    )
    return reference_without_numbers
