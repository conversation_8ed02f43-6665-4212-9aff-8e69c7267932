import copy
import re
import xml
import xml.dom.minidom
import xml.etree.ElementTree as ET
from datetime import datetime
from re import escape

from strandproperties.config import app_cfg
from strandproperties.constants import DescriptionType, Language
from strandproperties.libs.aws import S3Service
from strandproperties.libs.facebook.mapper import (
    map_custom_label,
    map_furnish_type,
    map_heating_type,
    map_parking_type,
    map_property_type,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import remove_some_ASCII_control_characters
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


def clean_text_for_xml(text: str) -> str:

    if not text:
        return ""

    text = text.replace("__amp__#13;", " ")

    text = re.sub(r"[\x00-\x08\x0B-\x1F\x7F]", " ", text)

    text = escape(text)

    return text


def set_clean_text(element: ET.Element, value: str):
    element.text = clean_text_for_xml(value)


class FacebookXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element("listings")
        title = ET.SubElement(root, "title")
        set_clean_text(title, "http://www.strandproperties.com Feed")

        # Example link (self) tag
        link_el = ET.SubElement(
            root, "link", rel="self", href="http://www.strandproperties.com"
        )
        # If you want to set link text, do so with set_clean_text(link_el, "some text")

        self.root = root
        self.encoding = encoding

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}

        property_tag["listing"] = ET.SubElement(self.root, "listing")

        property_tag["home_listing_id"] = ET.SubElement(
            property_tag["listing"], "home_listing_id"
        )
        property_tag["name"] = ET.SubElement(property_tag["listing"], "name")
        property_tag["availability"] = ET.SubElement(
            property_tag["listing"], "availability"
        )
        property_tag["description"] = ET.SubElement(
            property_tag["listing"], "description"
        )
        property_tag["address"] = ET.SubElement(
            property_tag["listing"], "address", format="simple"
        )
        property_tag["latitude"] = ET.SubElement(property_tag["listing"], "latitude")
        property_tag["longitude"] = ET.SubElement(property_tag["listing"], "longitude")
        property_tag["neighborhood"] = ET.SubElement(
            property_tag["listing"], "neighborhood"
        )
        property_tag["listing_type"] = ET.SubElement(
            property_tag["listing"], "listing_type"
        )
        property_tag["num_beds"] = ET.SubElement(property_tag["listing"], "num_beds")
        property_tag["num_baths"] = ET.SubElement(property_tag["listing"], "num_baths")
        property_tag["area_size"] = ET.SubElement(property_tag["listing"], "area_size")
        property_tag["area_unit"] = ET.SubElement(property_tag["listing"], "area_unit")
        property_tag["price"] = ET.SubElement(property_tag["listing"], "price")
        property_tag["property_type"] = ET.SubElement(
            property_tag["listing"], "property_type"
        )
        property_tag["url"] = ET.SubElement(property_tag["listing"], "url")
        property_tag["year_built"] = ET.SubElement(
            property_tag["listing"], "year_built"
        )
        property_tag["furnish_type"] = ET.SubElement(
            property_tag["listing"], "furnish_type"
        )
        property_tag["heating_type"] = ET.SubElement(
            property_tag["listing"], "heating_type"
        )
        property_tag["parking_type"] = ET.SubElement(
            property_tag["listing"], "parking_type"
        )
        property_tag["custom_label_0"] = ET.SubElement(
            property_tag["listing"], "custom_label_0"
        )

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        property_images = sorted(property.images, key=lambda x: x.order)[:50]
        feature_ids: list[int] = [feature.id for feature in property.features]

        # Wrap text assignments with set_clean_text(...)
        set_clean_text(property_tag["home_listing_id"], f"{property.reference or ''}")
        set_clean_text(
            property_tag["name"],
            next(
                (
                    remove_some_ASCII_control_characters(desc.tagline)
                    for desc in property.descriptions
                    if desc.language == Language.ENGLISH
                    and desc.type == DescriptionType.FULL
                ),
                "",
            ),
        )
        set_clean_text(property_tag["availability"], "for_sale")
        set_clean_text(
            property_tag["description"],
            next(
                (
                    remove_some_ASCII_control_characters(desc.description)
                    for desc in property.descriptions
                    if desc.language == Language.ENGLISH
                    and desc.type == DescriptionType.FULL
                ),
                "",
            ),
        )

        # address sub-elements
        addr1_tag = ET.SubElement(property_tag["address"], "component", name="addr1")
        set_clean_text(addr1_tag, property.private_info["location"].get("address", ""))

        city_tag = ET.SubElement(property_tag["address"], "component", name="city")
        set_clean_text(
            city_tag, property._area_level_1.name if property._area_level_1 else ""
        )

        region_tag = ET.SubElement(property_tag["address"], "component", name="region")
        set_clean_text(
            region_tag,
            property._area_level_1.province if property._area_level_1 else "",
        )

        country_tag = ET.SubElement(
            property_tag["address"], "component", name="country"
        )
        set_clean_text(country_tag, property.country)

        postal_code_tag = ET.SubElement(
            property_tag["address"], "component", name="postal_code"
        )
        set_clean_text(
            postal_code_tag, property.private_info["location"].get("postCode", "")
        )

        set_clean_text(property_tag["latitude"], f"{property.latitude or ''}")
        set_clean_text(property_tag["longitude"], f"{property.longitude or ''}")

        for image in property_images:
            img_tag = ET.SubElement(property_tag["listing"], "image")
            url_tag = ET.SubElement(img_tag, "url")
            set_clean_text(url_tag, image.url)

        set_clean_text(property_tag["listing_type"], "for_sale_by_agent")
        set_clean_text(property_tag["num_beds"], f"{property.bedrooms or ''}")
        set_clean_text(property_tag["num_baths"], f"{property.bathrooms or ''}")
        set_clean_text(property_tag["area_size"], f"{property.built_area or ''}")
        set_clean_text(property_tag["area_unit"], "sq_m")
        set_clean_text(property_tag["price"], f"{property.price_sale or ''}")
        set_clean_text(property_tag["property_type"], map_property_type(property))
        set_clean_text(
            property_tag["url"],
            f"https://strandproperties.com/listings/single-listing/?reference={property.reference}",
        )

        if property.built_year and property.built_year <= datetime.now().year:
            set_clean_text(property_tag["year_built"], f"{property.built_year}")

        set_clean_text(property_tag["furnish_type"], map_furnish_type(feature_ids))
        set_clean_text(property_tag["heating_type"], map_heating_type(feature_ids))
        set_clean_text(property_tag["parking_type"], map_parking_type(property))
        set_clean_text(property_tag["custom_label_0"], map_custom_label(property))

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        # If you were doing the manual "__amp__" replace, consider removing it
        # or do it carefully:
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w", encoding="utf-8") as xfile:
            xfile.write(part1 + f'encoding="{self.encoding}"?>\n' + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "application/xml")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/facebook",
            portal=PortalNames.FACEBOOK,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)


if __name__ == "__main__":
    builder = FacebookXMLBuilder()
    builder.run()
