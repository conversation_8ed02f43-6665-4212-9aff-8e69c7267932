from datetime import datetime
from typing import List, Literal, Optional

from boto3.session import Session
from botocore.exceptions import ClientError
from botocore.response import StreamingBody
from pydantic import BaseModel

from strandproperties.config import AppConfig


class BaseAwsService:
    def __init__(self, settings: AppConfig):
        self.session = Session()
        self.region_name = settings.aws_region_name
        self.is_local_env = settings.is_local_development()

    def _create_client(self, client_type: str):
        endpoint_url = "http://localhost:4566" if self.is_local_env else None
        return self.session.client(
            client_type,
            region_name=self.region_name,
            endpoint_url=endpoint_url,
        )


class S3Service(BaseAwsService):
    def __init__(self, settings: AppConfig, bucket_name: str):
        super().__init__(settings)
        self.client = self._create_client("s3")
        self.bucket_name = bucket_name

    def generate_upload_url(
        self,
        path: str,
        expires_in: int = 60 * 60,  # 1 hour
    ):
        return self._generate_presign_url("put_object", path, expires_in)

    def generate_download_url(
        self,
        path: str,
        expires_in: int = 60 * 60,  # 1 hour
    ):
        return self._generate_presign_url("get_object", path, expires_in)

    def _generate_presign_url(
        self,
        client_method_name: Literal["put_object"] | Literal["get_object"],
        path: str,
        expires_in: int,
    ):
        return self.client.generate_presigned_url(
            client_method_name,
            Params={
                "Bucket": self.bucket_name,
                "Key": path,
            },
            ExpiresIn=expires_in,
        )

    def upload(self, path: str, file, content_type: str):
        return self.client.put_object(
            Bucket=self.bucket_name, Body=file, Key=path, ContentType=content_type
        )

    def delete_objects(self, keys: List[str]):
        return self.client.delete_objects(
            Bucket=self.bucket_name,
            Delete={
                "Objects": [{"Key": key} for key in keys],
                "Quiet": True,
            },
        )

    def rename_object(self, old_key: str, new_key: str):
        return self.client.copy_object(
            Bucket=self.bucket_name,
            CopySource=f"{self.bucket_name}/{old_key}",
            Key=new_key,
        )

    def exists(self, key: str) -> bool:
        try:
            self.client.head_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            return False

    def get_last_modified(self, key: str) -> Optional[datetime]:
        try:
            res = self.client.head_object(Bucket=self.bucket_name, Key=key)
            return res["LastModified"]
        except:
            return

    def get_content(self, key: str) -> Optional[StreamingBody]:
        try:
            return self.client.get_object(Bucket=self.bucket_name, Key=key)["Body"]
        except ClientError as e:
            return


class SQSReceiveMessageResponseMessage(BaseModel):
    MessageId: str
    ReceiptHandle: str
    MD5OfBody: str
    Body: str


class SQSReceiveMessageResponse(BaseModel):
    Messages: Optional[List[SQSReceiveMessageResponseMessage]] = []
