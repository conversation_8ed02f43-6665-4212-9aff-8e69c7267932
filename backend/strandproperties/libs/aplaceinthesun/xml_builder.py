import copy
from datetime import datetime
from xml.dom.minidom import Document, Element

from strandproperties.config import app_cfg
from strandproperties.constants import (
    Currency,
    DescriptionType,
    Language,
    ListingTypeEnum,
)
from strandproperties.libs.aplaceinthesun.mapper import (
    get_location_info,
    map_listing_type_to_aplaceinthesun_price_and_property_type,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import (
    allowed_streaming_video,
    remove_some_ASCII_control_characters,
)
from strandproperties.logger import logger
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


class APlaceInTheSunXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"
        doc = Document()
        root = doc.createElement("root")
        doc.appendChild(root)
        properties = doc.createElement("Properties")
        root.appendChild(properties)

        self.doc = doc
        self.encoding = encoding
        self.properties = properties

    def set_property_tags(self) -> dict[str, Element]:
        property_tag: dict[str, Element] = {}
        ### All mandatory
        property = self.doc.createElement("Property")
        self.properties.appendChild(property)
        property_tag["Property"] = property

        property_tag["UniquePropertyID"] = property.appendChild(
            self.doc.createElement("UniquePropertyID")
        )  # Between 1 and 500 characters

        property_tag["LastUpdateDate"] = property.appendChild(
            self.doc.createElement("LastUpdateDate")
        )  # Date formats should always be YYYY-MM-DD HH:mm:ss

        property_tag["Country"] = property.appendChild(
            self.doc.createElement("Country")
        )
        property_tag["Region"] = property.appendChild(self.doc.createElement("Region"))
        property_tag["SalePrice"] = property.appendChild(
            self.doc.createElement("SalePrice")
        )  # Numeric values between 5,000 and 15,000,000
        property_tag["Description"] = property.appendChild(
            self.doc.createElement("Description")
        )  # Full description
        property_tag["Photos"] = property.appendChild(
            self.doc.createElement("Photos")
        )  # There must be at least one photo. Max 30
        property_tag["PropertyType"] = property.appendChild(
            self.doc.createElement("PropertyType")
        )

        ### Optional (but the tags must exist the same, even if they are empty)
        property_tag["Subregion"] = property.appendChild(
            self.doc.createElement("Subregion")
        )
        property_tag["Town"] = property.appendChild(self.doc.createElement("Town"))
        property_tag["Postcode"] = property.appendChild(
            self.doc.createElement("Postcode")
        )
        property_tag["Address"] = property.appendChild(
            self.doc.createElement("Address")
        )
        property_tag["GeoLocation"] = property.appendChild(
            self.doc.createElement("GeoLocation")
        )

        property_tag["Latitude"] = property_tag["GeoLocation"].appendChild(
            self.doc.createElement("Latitude")
        )
        property_tag["Longitude"] = property_tag["GeoLocation"].appendChild(
            self.doc.createElement("Longitude")
        )
        property_tag["Currency"] = property.appendChild(
            self.doc.createElement("Currency")
        )  # The default is GBP. Possible values: GBP, EUR or USD.
        property_tag["NumBedrooms"] = property.appendChild(
            self.doc.createElement("NumBedrooms")
        )
        property_tag["NumBathrooms"] = property.appendChild(
            self.doc.createElement("NumBathrooms")
        )
        property_tag["NewBuild"] = property.appendChild(
            self.doc.createElement("NewBuild")
        )  # 0, 1, yes or no
        property_tag["Pool"] = property.appendChild(
            self.doc.createElement("Pool")
        )  # 0, 1, yes or no
        property_tag["VirtualTour"] = property.appendChild(
            self.doc.createElement("VirtualTour")
        )  # A valid YouTube, Vimeo or Matterport url
        property_tag["SurfaceArea"] = property.appendChild(
            self.doc.createElement("SurfaceArea")
        )

        property_tag["Built"] = property_tag["SurfaceArea"].appendChild(
            self.doc.createElement("Built")
        )
        property_tag["Plot"] = property_tag["SurfaceArea"].appendChild(
            self.doc.createElement("Plot")
        )

        property_tag["EnergyRating"] = property.appendChild(
            self.doc.createElement("EnergyRating")
        )
        property_tag["Consumption"] = property.appendChild(
            self.doc.createElement("Consumption")
        )  # A, B, C, D, E, F or G
        property_tag["Emissions"] = property.appendChild(
            self.doc.createElement("Emissions")
        )  # A, B, C, D, E, F or G
        property_tag["PropertyName"] = property.appendChild(
            self.doc.createElement("PropertyName")
        )  # Default: (An automatic title will be generated by default). Min 1 and max of 150 characters
        property_tag["PropertyName.en"] = property_tag["PropertyName"].appendChild(
            self.doc.createElement("en")
        )
        property_tag["PropertyName.fr"] = property_tag["PropertyName"].appendChild(
            self.doc.createElement("fr")
        )

        property_tag["Introduction"] = property.appendChild(
            self.doc.createElement("Introduction")
        )  # Short description. Min 1 and max of 1000 characters
        property_tag["Introduction.en"] = property_tag["Introduction"].appendChild(
            self.doc.createElement("en")
        )
        property_tag["Introduction.fr"] = property_tag["Introduction"].appendChild(
            self.doc.createElement("fr")
        )

        property_tag["Features"] = property.appendChild(
            self.doc.createElement("Features")
        )
        property_tag["Floorplans"] = property.appendChild(
            self.doc.createElement("Floorplans")
        )  # If there are floorplans, there must be at least one and max of 30

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        # ALL REQUIRED
        property_tag["UniquePropertyID"].appendChild(
            self.doc.createTextNode(property.reference)
        )
        property_tag["LastUpdateDate"].appendChild(
            self.doc.createTextNode(
                property.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                if property.updated_at
                else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
        )
        property_tag["Country"].appendChild(
            self.doc.createTextNode(property._area_level_1.country)
        )
        property_tag["Region"].appendChild(
            self.doc.createTextNode(property._area_level_1.province)
        )

        (price, property_type) = (
            map_listing_type_to_aplaceinthesun_price_and_property_type(
                property=property
            )
        )
        property_tag["SalePrice"].appendChild(self.doc.createTextNode(price[:8]))
        property_tag["PropertyType"].appendChild(self.doc.createTextNode(property_type))

        # INFO: `next` would break if no english description is found,
        # but it won't (shouldn't) because we defined in the loop condition of
        # `generate_xml_and_upload_to_s3` function that we should only proceed
        # if it does exist.
        description_priority = {
            DescriptionType.FULL.value: 1,
            DescriptionType.EXTRA.value: 999,
        }

        english_description = next(
            (
                description
                for description in sorted(
                    property.descriptions,
                    key=lambda d: description_priority.get(d.type, 0),
                )
                if description.language == Language.ENGLISH
            )
        )
        description = remove_some_ASCII_control_characters(
            english_description.description
        )
        description_en_tag = self.doc.createElement("en")
        description_en_tag.appendChild(self.doc.createCDATASection(description))
        property_tag["Description"].appendChild(description_en_tag)
        property_tag["Description"].appendChild(self.doc.createElement("fr"))

        property_images = [image for image in property.images if not image.is_hidden]
        property_images.sort(key=lambda x: x.order)  # type: ignore
        for image in property_images[:30]:
            photo_tag = self.doc.createElement("Photo")
            property_tag["Photos"].appendChild(photo_tag)
            url_tag = self.doc.createElement("Url")
            url_tag.appendChild(self.doc.createTextNode(image.url))
            photo_tag.appendChild(url_tag)
            photo_tag.appendChild(self.doc.createElement("Caption"))

        for v in property.video_streams:
            if not v.is_hidden and allowed_streaming_video(v.url):
                property_tag["VirtualTour"].appendChild(self.doc.createTextNode(v.url))
                break

        ### ALL OPTIONAL (but we need to have all the tags, even when empty)
        location = get_location_info(property=property)
        property_tag["Town"].appendChild(
            self.doc.createTextNode(property._area_level_1.name or location.city)
        )
        if location.postcode:
            property_tag["Postcode"].appendChild(
                self.doc.createTextNode(location.postcode)
            )
        if location.address:
            property_tag["Address"].appendChild(
                self.doc.createCDATASection(location.address)
            )
        if property.latitude:
            property_tag["Latitude"].appendChild(
                self.doc.createTextNode(f"{property.latitude}")
            )
        if property.longitude:
            property_tag["Longitude"].appendChild(
                self.doc.createTextNode(f"{property.longitude}")
            )
        property_tag["Currency"].appendChild(
            self.doc.createTextNode(
                f"{property.currency.upper()}" if property.currency else Currency.EUR
            )
        )
        if property.bedrooms:
            property_tag["NumBedrooms"].appendChild(
                self.doc.createTextNode(f"{property.bedrooms}")
            )
        if property.bathrooms:
            property_tag["NumBathrooms"].appendChild(
                self.doc.createTextNode(f"{property.bathrooms}")
            )
        property_tag["Pool"].appendChild(
            self.doc.createTextNode(
                "yes" if property.pool_types and len(property.pool_types) > 0 else "no"
            )
        )

        if property.built_area:
            property_tag["Built"].appendChild(
                self.doc.createTextNode(f"{property.built_area}")
            )

        if property.plot_area:
            property_tag["Plot"].appendChild(
                self.doc.createTextNode(f"{property.plot_area}")
            )

        property_tag["PropertyName.en"].appendChild(
            self.doc.createTextNode(property.reference)
        )
        property_tag["PropertyName.fr"].appendChild(
            self.doc.createTextNode(property.reference)
        )

        introduction = english_description.tagline[:1000]

        property_tag["Introduction.en"].appendChild(
            self.doc.createCDATASection(introduction or "")
        )

        for feature in property.features:
            feature_tag = self.doc.createElement("Feature")
            feature_tag_en = self.doc.createElement("en")
            feature_tag.appendChild(feature_tag_en)
            feature_tag_en.appendChild(self.doc.createTextNode(feature.name))
            feature_tag.appendChild(self.doc.createElement("fr"))
            property_tag["Features"].appendChild(feature_tag)

        if len(property.features) == 0:
            feature_tag = self.doc.createElement("Feature")
            property_tag["Features"].appendChild(feature_tag)
            feature_tag.appendChild(self.doc.createElement("en"))
            feature_tag.appendChild(self.doc.createElement("fr"))

        floor_plan_tag = self.doc.createElement("Floorplan")
        property_tag["Floorplans"].appendChild(floor_plan_tag)
        floor_plan_tag.appendChild(self.doc.createElement("Url"))
        floor_plan_tag.appendChild(self.doc.createElement("Caption"))

        return property_tag

    def write_to_file(self, file_location: str):
        with open(file_location, "w") as xfile:
            self.doc.writexml(
                xfile, indent="\t", newl="\n", addindent="\t", encoding=self.encoding
            )

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        ### Check all required fields first
        # images are REQUIRED
        if not property.images:
            logger.info(
                f"> Skipping property [{property.reference}][A Place in the Sun]: images are required",
                extra={"requestId": request_id},
            )
            return False
        images = [image for image in property.images if not image.is_hidden]
        if not images:
            logger.info(
                f"> Skipping property [{property.reference}][A Place in the Sun]: non-hidden images are required",
                extra={"requestId": request_id},
            )
            return False

        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][A Place in the Sun]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # description is REQUIRED
        non_empty_english_descriptions = [
            desc.description
            for desc in property.descriptions
            if desc.description and desc.language == Language.ENGLISH
        ]
        if not non_empty_english_descriptions:
            logger.info(
                f"> Skipping property [{property.reference}][A Place in the Sun]: english description is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][A Place in the Sun]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][A Place in the Sun]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][A Place in the Sun]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][A Place in the Sun]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/aplaceinthesun",
            portal=PortalNames.APLACEINTHESUN,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
