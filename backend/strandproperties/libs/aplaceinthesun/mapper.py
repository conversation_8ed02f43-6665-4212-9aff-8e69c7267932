from enum import Enum
from typing import Optional, cast

from pydantic import BaseModel

from strandproperties.constants import ListingTypeEnum
from strandproperties.models.property import Property


class APlaceInTheSunPropertyTypeEnum(str, Enum):
    APARTMENT = "Apartment"
    BAR = "Bar"
    BARN = "Barn"
    BUNGALOW = "Bungalow"
    CHATEAUX = "Chateaux"
    COMMERCIAL = "Commercial"
    CONDO = "Condo"
    CORTIJO = "Cortijo"
    COTTAGE = "Cottage"
    COUNTRY_HOUSE = "Country House"
    FARMHOUSE = "Farmhouse"
    FINCA = "Finca"
    FLAT = "Flat"
    HOTEL = "Hotel"
    HOUSE = "House"
    KEY_READY = "Key Ready"
    LAND = "Land"
    NEW_HOMES = "New Homes"
    PENTHOUSE = "Penthouse"
    PLOT = "Plot"
    PROPERTY = "Property"
    QUAD = "Quad"
    RESTAURANT = "Restaurant"
    STUDIO = "Studio"
    TOWNHOUSE = "Townhouse"
    VILLA = "Villa"
    VINEYARD = "Vineyard"


"""
Maps from Property Type (Strand) to APlaceInTheSun property types:
"""
_property_type_mapper = {
    "Apartment": APlaceInTheSunPropertyTypeEnum.APARTMENT,
    "Bar": APlaceInTheSunPropertyTypeEnum.BAR,
    "Boat": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Building": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Bungalow": APlaceInTheSunPropertyTypeEnum.BUNGALOW,
    "Business": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Castle": APlaceInTheSunPropertyTypeEnum.CHATEAUX,
    "Chalet": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Commercial Other": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Commercial Premises": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Cortijo": APlaceInTheSunPropertyTypeEnum.CORTIJO,
    "Country House": APlaceInTheSunPropertyTypeEnum.COUNTRY_HOUSE,
    "Development Land": APlaceInTheSunPropertyTypeEnum.LAND,
    "Discotheque": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Duplex": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Duplex Penthouse": APlaceInTheSunPropertyTypeEnum.PENTHOUSE,
    "Estate": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Finca": APlaceInTheSunPropertyTypeEnum.FINCA,
    "Flat": APlaceInTheSunPropertyTypeEnum.FLAT,
    "Golf Course": APlaceInTheSunPropertyTypeEnum.LAND,
    "Golf Plot": APlaceInTheSunPropertyTypeEnum.PLOT,
    "Ground Floor Apartment": APlaceInTheSunPropertyTypeEnum.APARTMENT,
    "Ground Floor Duplex": APlaceInTheSunPropertyTypeEnum.APARTMENT,
    "Hotel": APlaceInTheSunPropertyTypeEnum.HOTEL,
    "Hotel Plot": APlaceInTheSunPropertyTypeEnum.PLOT,
    "House": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Industrial Land": APlaceInTheSunPropertyTypeEnum.LAND,
    "Industrial Premises": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Investment": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Loft": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Mansion": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Mooring": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Office": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Office Units": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Palace": APlaceInTheSunPropertyTypeEnum.CHATEAUX,
    "Parking": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Penthouse": APlaceInTheSunPropertyTypeEnum.PENTHOUSE,
    "Plot": APlaceInTheSunPropertyTypeEnum.PLOT,
    "Residential Plot": APlaceInTheSunPropertyTypeEnum.PLOT,
    "Restaurant": APlaceInTheSunPropertyTypeEnum.RESTAURANT,
    "Riad": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Rustic Plot": APlaceInTheSunPropertyTypeEnum.PLOT,
    "Semi Detached House": APlaceInTheSunPropertyTypeEnum.HOUSE,
    "Semi Detached Villa": APlaceInTheSunPropertyTypeEnum.VILLA,
    "Shop": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Shopping Centre": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Store Room": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Studio": APlaceInTheSunPropertyTypeEnum.STUDIO,
    "Supermarket": APlaceInTheSunPropertyTypeEnum.COMMERCIAL,
    "Town House": APlaceInTheSunPropertyTypeEnum.TOWNHOUSE,
    "Triplex": APlaceInTheSunPropertyTypeEnum.APARTMENT,
    "Unique Building": APlaceInTheSunPropertyTypeEnum.PROPERTY,
    "Villa": APlaceInTheSunPropertyTypeEnum.VILLA,
}


def map_listing_type_to_aplaceinthesun_price_and_property_type(
    *,
    property: Property,
) -> tuple[str, str]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]
    property_type = _property_type_mapper[property._property_type.name]

    if ListingTypeEnum.SALE in listing_types_names:
        return (f"{property.price_sale}", property_type.value)
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        return (
            f"{property.price_rent_long_term}",
            property_type.value,
        )
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        return (
            f"{property.price_rent_short_term}",
            property_type.value,
        )
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


class LocationInfo(BaseModel):
    address: Optional[str]
    postcode: Optional[str]
    city: str
    country: str


def get_location_info(*, property: Property) -> LocationInfo:
    private_info = property.private_info

    if not (private_info and "location" in private_info):
        return LocationInfo(
            address="",
            postcode="",
            city="",
            country="",
        )

    location = private_info["location"]

    location_address: str = location.get("address", "")  # it is an optional field
    location_postcode = location.get("postCode", "")  # it is an optional field
    location_city = location.get("city", "")  # it is an optional field
    location_country = cast(str, property.country) or "Spain"

    return LocationInfo(
        address=location_address,
        postcode=location_postcode,
        city=location_city,
        country=location_country,
    )
