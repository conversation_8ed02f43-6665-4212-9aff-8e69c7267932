import copy
import re
import xml.dom.minidom
import xml.etree.ElementTree as ET
from xml.sax.saxutils import escape

from strandproperties import logger
from strandproperties.config import app_cfg
from strandproperties.constants import (
    CommissionTypeEnum,
    DescriptionType,
    Language,
    ListingTypeEnum,
    PropertyTypeOrigin,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.kyero.mapper import replace_description_characters
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import remove_some_ASCII_control_characters
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


def clean_text_for_xml(text: str) -> str:
    if not text:
        return ""
    text = text.replace("__amp__#13;", " ")
    text = re.sub(r"[\x00-\x08\x0B-\x1F\x7F]", " ", text)
    text = escape(text)
    return text


def set_clean_text(element: ET.Element, value: str):
    element.text = clean_text_for_xml(value)


FEATURE_NAME_MAPPING = {
    "Mountain Side": "Mountainside",
    "Recently Renovated/Refurbished": "Recently Renovated / Refurbished",
    "24h Security Service": "Security service 24h",
    "Close to Sea/Beach": "Close to sea / beach",
    "Near Transport": "Transport near",
    "Paddle Tennis": "Paddle court",
    "Fiber Optic": "Internet - Fibre optic",
    "Dolby StereoSurround System": "Dolby Stereo Surround system",
    "Fiber Optic": "Internet - Fibre optic",
    "Guest House": "Guest room",
    "Games Room": "Game Room",
    "Tennis Court": "Tennis / paddle court",
    "Wall To Wall Carpet": "Wall-to-wall carpet",
}

IGNORED_FEATURE_NAMES = [
    "Alarm System",  # We already have "Alarm" , we can create duplicated options
    "Pool",  # There are pool type and other values, like : Heated pool, Indoor Pool, etc
    "Toilets",  # There is nr_toilet field in xml
    "Parking Spaces",  # There is parking_spaces field in xml
    "Community Pool",  # There is tr_pool field in xml
    "Ensuite Bathroom",  # There is nr_onsuite_bathrooms field in xml
    "Bar",  # There is 'Bars' in inmobalia_ids.xml but this info is not clear.
    "Terrace",  # There is "Covered terrace", "Private terrace" etc
    "Disabled Access",  # not found in ids.xml
    "Fitted Kitchen",  # there is "fully fitted kitchen"
    "Aerothermics",  # not found in ids.xml
    "Climate Control",  # not found in ids.xml
    "Near Church",  # not found in ids.xml
    "Staff Accommodation",  # not found in ids.xml
    "Floor Heating",  # There is "Underfloor heating (throughout)"
    "Gasoil heating",  # not found in ids.xml
    "Restaurant On Site",  # not found in ids.xml
]


class InmobaliaXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        properties = ET.Element("properties")
        self.properties = properties
        self.encoding = encoding

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}

        property_tag["property"] = ET.SubElement(self.properties, "property")
        property_tag["id"] = ET.SubElement(property_tag["property"], "id")
        property_tag["reference"] = ET.SubElement(property_tag["property"], "reference")
        property_tag["status"] = ET.SubElement(property_tag["property"], "status")
        property_tag["publish_web"] = ET.SubElement(
            property_tag["property"], "publish_web"
        )
        property_tag["promotion_id"] = ET.SubElement(
            property_tag["property"], "promotion_id"
        )
        property_tag["dt_create_date"] = ET.SubElement(
            property_tag["property"], "dt_create_date"
        )  # Ex: 2019-09-24
        property_tag["dt_last_updated"] = ET.SubElement(
            property_tag["property"], "dt_last_updated"
        )  # Ex: 2019-09-24 16:29:31
        property_tag["dt_added_date"] = ET.SubElement(
            property_tag["property"], "dt_added_date"
        )
        property_tag["dt_review_date"] = ET.SubElement(
            property_tag["property"], "dt_review_date"
        )
        property_tag["is_new_property"] = ET.SubElement(
            property_tag["property"], "is_new_property"
        )
        property_tag["is_exclusive"] = ET.SubElement(
            property_tag["property"], "is_exclusive"
        )
        property_tag["is_featured"] = ET.SubElement(
            property_tag["property"], "is_featured"
        )
        property_tag["is_special"] = ET.SubElement(
            property_tag["property"], "is_special"
        )
        property_tag["is_hot"] = ET.SubElement(property_tag["property"], "is_hot")
        property_tag["is_luxury"] = ET.SubElement(property_tag["property"], "is_luxury")
        property_tag["is_for_sale"] = ET.SubElement(
            property_tag["property"], "is_for_sale"
        )
        property_tag["is_sold"] = ET.SubElement(property_tag["property"], "is_sold")
        property_tag["is_under_offer"] = ET.SubElement(
            property_tag["property"], "is_under_offer"
        )
        property_tag["is_for_rent"] = ET.SubElement(
            property_tag["property"], "is_for_rent"
        )
        property_tag["is_for_rent_long"] = ET.SubElement(
            property_tag["property"], "is_for_rent_long"
        )
        property_tag["is_for_rent_short"] = ET.SubElement(
            property_tag["property"], "is_for_rent_short"
        )
        property_tag["is_rented"] = ET.SubElement(property_tag["property"], "is_rented")
        property_tag["propertytype"] = ET.SubElement(
            property_tag["property"], "propertytype"
        )
        property_tag["propertycategory"] = ET.SubElement(
            property_tag["property"], "propertycategory"
        )
        property_tag["country"] = ET.SubElement(property_tag["property"], "country")
        property_tag["province"] = ET.SubElement(property_tag["property"], "province")
        property_tag["city"] = ET.SubElement(property_tag["property"], "city")
        property_tag["area"] = ET.SubElement(property_tag["property"], "area")
        property_tag["subarea"] = ET.SubElement(property_tag["property"], "subarea")
        property_tag["postcode"] = ET.SubElement(property_tag["property"], "postcode")
        property_tag["addess"] = ET.SubElement(property_tag["property"], "addess")
        property_tag["community"] = ET.SubElement(property_tag["property"], "community")
        property_tag["ibi"] = ET.SubElement(property_tag["property"], "ibi")
        property_tag["garbage"] = ET.SubElement(property_tag["property"], "garbage")
        property_tag["tr_orientation"] = ET.SubElement(
            property_tag["property"], "tr_orientation"
        )
        property_tag["mts_build"] = ET.SubElement(property_tag["property"], "mts_build")
        property_tag["mts_plot"] = ET.SubElement(property_tag["property"], "mts_plot")
        property_tag["mts_interior"] = ET.SubElement(
            property_tag["property"], "mts_interior"
        )
        property_tag["mts_terrace"] = ET.SubElement(
            property_tag["property"], "mts_terrace"
        )
        property_tag["nr_floors"] = ET.SubElement(property_tag["property"], "nr_floors")
        property_tag["nr_level"] = ET.SubElement(property_tag["property"], "nr_level")
        property_tag["nr_bedrooms"] = ET.SubElement(
            property_tag["property"], "nr_bedrooms"
        )
        property_tag["nr_bathrooms"] = ET.SubElement(
            property_tag["property"], "nr_bathrooms"
        )
        property_tag["nr_onsuite_bathrooms"] = ET.SubElement(
            property_tag["property"], "nr_onsuite_bathrooms"
        )
        property_tag["nr_toilet"] = ET.SubElement(property_tag["property"], "nr_toilet")
        property_tag["tr_garage"] = ET.SubElement(property_tag["property"], "tr_garage")

        property_tag["tr_garden"] = ET.SubElement(property_tag["property"], "tr_garden")
        property_tag["parking_spaces"] = ET.SubElement(
            property_tag["property"], "parking_spaces"
        )
        property_tag["tr_pool"] = ET.SubElement(property_tag["property"], "tr_pool")
        property_tag["dt_construction_year"] = ET.SubElement(
            property_tag["property"], "dt_construction_year"
        )
        property_tag["selling_price"] = ET.SubElement(
            property_tag["property"], "selling_price", currency="euro"
        )
        property_tag["rental_price"] = ET.SubElement(
            property_tag["property"], "rental_price", currency="euro"
        )
        property_tag["rental_price_long"] = ET.SubElement(
            property_tag["property"], "rental_price_long", currency="euro"
        )
        property_tag["rental_price_comment"] = ET.SubElement(
            property_tag["property"], "rental_price_comment"
        )
        property_tag["rental_price_comment_long"] = ET.SubElement(
            property_tag["property"], "rental_price_comment_long"
        )
        property_tag["sale_commission"] = ET.SubElement(
            property_tag["property"], "sale_commission"
        )
        property_tag["tags"] = ET.SubElement(property_tag["property"], "tags")
        property_tag["images"] = ET.SubElement(property_tag["property"], "images")
        property_tag["gps"] = ET.SubElement(property_tag["property"], "gps")
        property_tag["cadastral_reference"] = ET.SubElement(
            property_tag["property"], "cadastral_reference"
        )
        property_tag["energy"] = ET.SubElement(property_tag["property"], "energy")

        property_tag["agency"] = ET.SubElement(property_tag["property"], "agency")
        property_tag["listed_by"] = ET.SubElement(property_tag["property"], "listed_by")
        property_tag["listed_by2"] = ET.SubElement(
            property_tag["property"], "listed_by2"
        )
        property_tag["seller"] = ET.SubElement(property_tag["property"], "seller")
        property_tag["options"] = ET.SubElement(property_tag["property"], "options")
        property_tag["descriptions"] = ET.SubElement(
            property_tag["property"], "descriptions"
        )
        property_tag["internal_notes"] = ET.SubElement(
            property_tag["property"], "internal_notes"
        )
        property_tag["media"] = ET.SubElement(property_tag["property"], "media")

        return property_tag

    def fill_property_tags(
        self,
        original_property_tag: dict[str, ET.Element],
        property: Property,
    ):
        property_tag = copy.copy(original_property_tag)
        try:
            inmobalia_ids = ET.parse(
                "strandproperties/libs/inmobalia/inmobalia_ids.xml"
            ).getroot()
            inmobalia_pool = (
                inmobalia_ids[0] if inmobalia_ids[0].tag == "pool" else None
            )
            inmobalia_garage = (
                inmobalia_ids[1] if inmobalia_ids[1].tag == "garage" else None
            )
            inmobalia_garden = (
                inmobalia_ids[2] if inmobalia_ids[2].tag == "garden" else None
            )
            inmobalia_propertyoptions = (
                inmobalia_ids[3] if inmobalia_ids[3].tag == "propertyoptions" else None
            )
            inmobalia_promotionoptions = (
                inmobalia_ids[4] if inmobalia_ids[4].tag == "promotionoptions" else None
            )
            inmobalia_categories = (
                inmobalia_ids[5] if inmobalia_ids[5].tag == "categories" else None
            )
            inmobalia_types = (
                inmobalia_ids[6] if inmobalia_ids[6].tag == "types" else None
            )
        except Exception as e:
            logger.info(
                f"> fill_property_tags() failed: cannot parse inmobalia_ids.xml",
                extra={},
            )
            return

        property_images: list[Image] = property.sorted_visible_images[:50]

        # CHANGE .text = ... TO set_clean_text(...)
        set_clean_text(property_tag["id"], f"{property.id}")
        set_clean_text(property_tag["reference"], f"{property.reference}")
        set_clean_text(property_tag["status"], "available")  # Always 'available'
        set_clean_text(property_tag["publish_web"], "1")
        set_clean_text(property_tag["promotion_id"], f"{property.id}")
        set_clean_text(
            property_tag["dt_create_date"], property.created_at.strftime("%Y-%m-%d")
        )
        set_clean_text(
            property_tag["dt_last_updated"],
            property.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
        )
        set_clean_text(
            property_tag["dt_added_date"], property.created_at.strftime("%Y-%m-%d")
        )
        set_clean_text(
            property_tag["dt_review_date"], property.updated_at.strftime("%Y-%m-%d")
        )

        set_clean_text(
            property_tag["is_new_property"],
            (
                "1"
                if property.property_type_origin == PropertyTypeOrigin.NEW_DEVELOPMENT
                else "0"
            ),
        )
        set_clean_text(property_tag["is_exclusive"], "1")

        listing_types = [listing_type.name for listing_type in property.listing_types]
        set_clean_text(
            property_tag["is_for_sale"],
            "1" if ListingTypeEnum.SALE in listing_types else "0",
        )
        set_clean_text(property_tag["is_sold"], "0")
        set_clean_text(property_tag["is_under_offer"], "0")
        set_clean_text(
            property_tag["is_for_rent"],
            (
                "1"
                if ListingTypeEnum.RENT_LONG in listing_types
                or ListingTypeEnum.RENT_SHORT in listing_types
                else "0"
            ),
        )
        set_clean_text(
            property_tag["is_for_rent_long"],
            "1" if ListingTypeEnum.RENT_LONG in listing_types else "0",
        )
        set_clean_text(
            property_tag["is_for_rent_short"],
            "1" if ListingTypeEnum.RENT_SHORT in listing_types else "0",
        )
        set_clean_text(property_tag["is_rented"], "0")

        set_clean_text(
            property_tag["selling_price"],
            (
                f"{property.price_sale}"
                if property.price_sale and ListingTypeEnum.SALE in listing_types
                else ""
            ),
        )
        set_clean_text(
            property_tag["rental_price"],
            (
                f"{property.price_rent_long_term}"
                if property.price_rent_long_term
                and ListingTypeEnum.RENT_LONG in listing_types
                else ""
            ),
        )
        set_clean_text(
            property_tag["rental_price_long"],
            (
                f"{property.price_rent_long_term}"
                if property.price_rent_long_term
                and ListingTypeEnum.RENT_LONG in listing_types
                else ""
            ),
        )
        set_clean_text(property_tag["rental_price_comment"], "per month")
        set_clean_text(property_tag["rental_price_comment_long"], "per month")

        for type_element in inmobalia_types:
            text = type_element.text.replace("\n", "").replace("\t", "")
            if text == property.property_type:
                set_clean_text(property_tag["propertytype"], f"{text}")
                property_tag["propertytype"].attrib = {"id": type_element.attrib["id"]}
                cat_id = type_element.attrib["cat_id"]

                for category_element in inmobalia_categories:
                    if category_element.attrib["id"] == cat_id:
                        category_text = category_element.text.replace("\n", "").replace(
                            "\t", ""
                        )
                        set_clean_text(property_tag["propertycategory"], category_text)
                        property_tag["propertycategory"].attrib = {
                            "id": category_element.attrib["id"]
                        }
                        break
                break

        set_clean_text(property_tag["country"], property.country)
        set_clean_text(
            property_tag["province"],
            (
                property._area_level_1.province
                if property._area_level_1 is not None
                else ""
            ),
        )
        set_clean_text(property_tag["city"], property.city)
        set_clean_text(
            property_tag["area"],
            property._area_level_2.name if property._area_level_2 is not None else "",
        )
        set_clean_text(
            property_tag["subarea"],
            property._area_level_3.name if property._area_level_3 is not None else "",
        )

        set_clean_text(property_tag["community"], f"{property.communal_fees}")
        set_clean_text(property_tag["ibi"], f"{property.ibi}")
        set_clean_text(property_tag["garbage"], f"{property.garbage_tax}")

        orientation = (
            property.orientations[0].name
            if property.orientations is not None and len(property.orientations) > 0
            else ""
        )
        set_clean_text(
            property_tag["tr_orientation"], f"{'/'.join(orientation.split(' '))}"
        )

        set_clean_text(
            property_tag["mts_build"],
            f"{property.built_area}" if property.built_area is not None else "",
        )
        set_clean_text(
            property_tag["mts_plot"],
            f"{property.plot_area}" if property.plot_area is not None else "",
        )
        set_clean_text(
            property_tag["mts_interior"],
            f"{property.interior_area}" if property.interior_area is not None else "",
        )
        set_clean_text(
            property_tag["mts_terrace"],
            f"{property.terrace_area}" if property.terrace_area is not None else "",
        )
        set_clean_text(
            property_tag["nr_floors"],
            f"{property.total_floors}" if property.total_floors is not None else "",
        )
        set_clean_text(
            property_tag["nr_level"],
            f"{property.floor}" if property.floor is not None else "",
        )
        set_clean_text(
            property_tag["nr_bedrooms"],
            f"{property.bedrooms}" if property.bedrooms is not None else "",
        )
        set_clean_text(
            property_tag["nr_bathrooms"],
            f"{property.bathrooms}" if property.bathrooms is not None else "",
        )
        set_clean_text(
            property_tag["nr_onsuite_bathrooms"],
            f"{property.suite_baths}" if property.suite_baths is not None else "",
        )
        set_clean_text(
            property_tag["nr_toilet"],
            f"{property.toilets}" if property.toilets is not None else "",
        )

        garage = (
            property.garage_types[0].name
            if property.garage_types is not None and len(property.garage_types) > 0
            else ""
        )
        for garage_element in inmobalia_garage:
            if garage_element.text == garage:
                set_clean_text(property_tag["tr_garage"], garage)
                property_tag["tr_garage"].attrib = {"id": garage_element.attrib["id"]}
                break

        garden = (
            property.garden_types[0].name
            if property.garden_types is not None and len(property.garden_types) > 0
            else ""
        )
        for garden_element in inmobalia_garden:
            if garden_element.text == garden:
                set_clean_text(property_tag["tr_garden"], garden)
                property_tag["tr_garden"].attrib = {"id": garden_element.attrib["id"]}
                break

        set_clean_text(
            property_tag["parking_spaces"],
            f"{property.parking_spaces}" if property.parking_spaces is not None else "",
        )

        pool = (
            property.pool_types[0].name
            if property.pool_types is not None and len(property.pool_types) > 0
            else ""
        )
        for pool_element in inmobalia_pool:
            if pool_element.text == pool:
                set_clean_text(property_tag["tr_pool"], pool)
                property_tag["tr_pool"].attrib = {"id": pool_element.attrib["id"]}
                break

        set_clean_text(
            property_tag["dt_construction_year"],
            f"{property.built_year}" if property.built_year is not None else "",
        )
        set_clean_text(
            property_tag["sale_commission"],
            (
                f"{property.commission}"
                if property.commission
                and property.commission_type == CommissionTypeEnum.PERCENT
                else ""
            ),
        )

        for image in property_images:
            img_tag = ET.SubElement(property_tag["images"], "image")
            # filename in .text
            set_clean_text(img_tag, image.url.split("/").pop())
            # attribute remains as-is
            img_tag.attrib = {"url": f"{image.url}"}

        gps_publish_element = ET.SubElement(property_tag["gps"], "publish")
        set_clean_text(
            gps_publish_element, "1" if property.is_public_coordinates is True else "0"
        )
        gps_longitude_element = ET.SubElement(property_tag["gps"], "longitude")
        set_clean_text(
            gps_longitude_element, f"{property.longitude}" if property.longitude else ""
        )
        gps_latitude_element = ET.SubElement(property_tag["gps"], "latitude")
        set_clean_text(
            gps_latitude_element, f"{property.latitude}" if property.latitude else ""
        )

        set_clean_text(
            property_tag["cadastral_reference"],
            f"{property.cadastral_reference}" if property.cadastral_reference else "",
        )
        inProcess = "1" if property.property_has_certificate is True else "0"
        property_tag["energy"].attrib = {"inProcess": inProcess}
        kw = ET.SubElement(property_tag["energy"], "kw")
        kw_level = ET.SubElement(kw, "level")
        kw_value = ET.SubElement(kw, "value")
        set_clean_text(
            kw_level,
            (
                f"{property.certificate_consumption_rating}"
                if property.certificate_consumption_rating
                else ""
            ),
        )
        set_clean_text(
            kw_value,
            (
                f"{property.certificate_consumption_value}"
                if property.certificate_consumption_value
                else ""
            ),
        )

        co2 = ET.SubElement(property_tag["energy"], "co2")
        co2_level = ET.SubElement(co2, "level")
        co2_value = ET.SubElement(co2, "value")
        set_clean_text(
            co2_level,
            (
                f"{property.certificate_emission_rating}"
                if property.certificate_emission_rating
                else ""
            ),
        )
        set_clean_text(
            co2_value,
            (
                f"{property.certificate_emission_value}"
                if property.certificate_emission_value
                else ""
            ),
        )

        agency_name = ET.SubElement(property_tag["agency"], "name")
        set_clean_text(agency_name, "Strand Properties")
        agency_email = ET.SubElement(property_tag["agency"], "email")
        set_clean_text(agency_email, "<EMAIL>")
        agency_addr = ET.SubElement(property_tag["agency"], "Address")
        set_clean_text(agency_addr, "Av. Playas del Duque, Málaga 1 C Puerto Banús")
        agency_web = ET.SubElement(property_tag["agency"], "Web")
        set_clean_text(agency_web, "https://strandproperties.com")

        if property.realtor_users and len(property.realtor_users) > 0:
            realtor = property.realtor_users[0]
            el_name = ET.SubElement(property_tag["listed_by"], "name")
            set_clean_text(
                el_name, f"{realtor.first_name}" if realtor.first_name else ""
            )
            el_surname = ET.SubElement(property_tag["listed_by"], "surname")
            set_clean_text(
                el_surname, f"{realtor.last_name}" if realtor.last_name else ""
            )
            el_phone = ET.SubElement(property_tag["listed_by"], "phone")
            set_clean_text(el_phone, "+34676901519")
            el_mobile = ET.SubElement(property_tag["listed_by"], "mobile")
            set_clean_text(el_mobile, "+34676901519")
            el_email = ET.SubElement(property_tag["listed_by"], "email")
            set_clean_text(el_email, "<EMAIL>")
        if property.realtor_users and len(property.realtor_users) > 1:
            realtor = property.realtor_users[1]
            el2_name = ET.SubElement(property_tag["listed_by2"], "name")
            set_clean_text(
                el2_name, f"{realtor.first_name}" if realtor.first_name else ""
            )
            el2_surname = ET.SubElement(property_tag["listed_by2"], "surname")
            set_clean_text(
                el2_surname, f"{realtor.last_name}" if realtor.last_name else ""
            )
            el2_phone = ET.SubElement(property_tag["listed_by2"], "phone")
            set_clean_text(el2_phone, "+34676901519")
            el2_mobile = ET.SubElement(property_tag["listed_by2"], "mobile")
            set_clean_text(el2_mobile, "+34676901519")
            el2_email = ET.SubElement(property_tag["listed_by2"], "email")
            set_clean_text(el2_email, "<EMAIL>")

        # Filling <options> data.
        for feature in property.features:
            if feature.name in IGNORED_FEATURE_NAMES:
                continue

            feature_name = FEATURE_NAME_MAPPING.get(feature.name, feature.name)
            text = f"{feature_name}".lower()
            new_element = None
            for option in inmobalia_propertyoptions:
                if option.text.lower() == text:
                    new_element = ET.SubElement(property_tag["options"], "option")
                    set_clean_text(new_element, option.text)
                    new_element.attrib = {"type": option.attrib["id"]}
                    break
            if new_element is None:
                print(
                    f"Property reference: {property.reference}. Not found feature name: {feature.name}, groupname: {feature.feature_group_name}"
                )

        # Fill in descriptions
        en_short_desc = ET.SubElement(
            property_tag["descriptions"], "short_description", lang="en"
        )
        set_clean_text(
            en_short_desc,
            next(
                (
                    remove_some_ASCII_control_characters(description.tagline)
                    for description in property.descriptions
                    if description.language == Language.ENGLISH
                    and description.type == DescriptionType.FULL
                ),
                "",
            ),
        )
        en_long_desc = ET.SubElement(
            property_tag["descriptions"], "long_description", lang="en"
        )
        set_clean_text(
            en_long_desc,
            next(
                (
                    remove_some_ASCII_control_characters(description.description)
                    for description in property.descriptions
                    if description.language == Language.ENGLISH
                    and description.type == DescriptionType.FULL
                ),
                "",
            ),
        )
        en_price_desc = ET.SubElement(
            property_tag["descriptions"], "price_description", lang="en"
        )
        set_clean_text(en_price_desc, "")
        en_extra_desc = ET.SubElement(
            property_tag["descriptions"], "extra_description", lang="en"
        )
        set_clean_text(en_extra_desc, "")

        es_short_desc = ET.SubElement(
            property_tag["descriptions"], "short_description", lang="es"
        )
        set_clean_text(
            es_short_desc,
            next(
                (
                    replace_description_characters(description.tagline)
                    for description in property.descriptions
                    if description.language == Language.SPANISH
                    and description.type == DescriptionType.FULL
                ),
                "",
            ),
        )
        es_long_desc = ET.SubElement(
            property_tag["descriptions"], "long_description", lang="es"
        )
        set_clean_text(
            es_long_desc,
            next(
                (
                    replace_description_characters(description.description)
                    for description in property.descriptions
                    if description.language == Language.SPANISH
                    and description.type == DescriptionType.FULL
                ),
                "",
            ),
        )
        es_price_desc = ET.SubElement(
            property_tag["descriptions"], "price_description", lang="es"
        )
        set_clean_text(es_price_desc, "")
        es_extra_desc = ET.SubElement(
            property_tag["descriptions"], "extra_description", lang="es"
        )
        set_clean_text(es_extra_desc, "")

        # Fill in <media>
        files_el = ET.SubElement(property_tag["media"], "files")
        set_clean_text(files_el, "")
        media_links = ET.SubElement(property_tag["media"], "links")
        visible_video_tours = list(property.visible_video_tours)
        if len(visible_video_tours) > 0:
            video_tour = visible_video_tours[0]
            link_el = ET.SubElement(
                media_links,
                "link",
                lang="en",
                publish="1",
                type="onlinebrochure",
                url=video_tour.url,
            )
            set_clean_text(link_el, "Video")

        htmls_el = ET.SubElement(property_tag["media"], "htmls")
        set_clean_text(htmls_el, "")

        return property_tag

    def write_to_file(self, file_location: str):

        dom = xml.dom.minidom.parseString(ET.tostring(self.properties))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/inmobalia",
            portal=PortalNames.INMOBALIA,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
