import sentry_sdk
from pyramid.httpexceptions import HTTPNotFound, HTTPUnauthorized
from sentry_sdk.integrations.pyramid import PyramidIntegration

from strandproperties.config import app_cfg

IGNORE_PATHS = []


def get_sentry_sampler(sampling_context: dict) -> float:
    path = sampling_context.get("wsgi_environ", {}).get("PATH_INFO", "")
    method = sampling_context.get("wsgi_environ", {}).get("REQUEST_METHOD", "")

    # drop transaction for some paths
    if any(
        [
            path.startswith("/-/"),
            path in IGNORE_PATHS,
            method == "OPTIONS",
        ]
    ):
        return 0

    # otherwise default to 10%
    return 0.1


def get_sentry_before_send(event, hint):
    if "exc_info" not in hint:
        return event

    exception = hint["exc_info"][1]

    # these are too noisy for Sentry
    if isinstance(exception, (HTTPUnauthorized, HTTPNotFound)):
        return None

    # set event status code if available
    if not event.get("tags", {}).get("status_code") and event.get("extra", {}).get(
        "statusCode"
    ):
        if "tags" not in event:
            event["tags"] = {}
        event["tags"]["status_code"] = event["extra"]["statusCode"]
    return event


def includeme(config):
    if app_cfg.sentry_dsn:
        sentry_sdk.init(
            dsn=app_cfg.sentry_dsn,
            integrations=[PyramidIntegration()],
            traces_sampler=get_sentry_sampler,
            profiles_sampler=get_sentry_sampler,
            before_send=get_sentry_before_send,
            release=app_cfg.api_revision,
            environment=app_cfg.env,
        )
