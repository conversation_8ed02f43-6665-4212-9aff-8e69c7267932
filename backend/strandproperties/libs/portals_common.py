import enum
from datetime import datetime
from typing import Any, Callable, List, Optional

from botocore.exceptions import ClientError
from sqlalchemy import and_, select
from sqlalchemy.orm import Session, joinedload

from strandproperties.config import app_cfg
from strandproperties.constants import DataSource, Status
from strandproperties.logger import logger
from strandproperties.models.property import Property
from strandproperties.schemas.base import BaseSchema


class FileType(enum.StrEnum):
    XML = "xml"
    CSV = "csv"


class PortalNames(enum.StrEnum):
    IDEALISTA = "idealista"
    LEADINGRE = "leadingre"
    ETUOVI = "etuovi"
    STRANDPROPERTIES = "strandproperties"
    SPAINFORSALE = "spainforsale"
    RIGHTMOVE = "rightmove"
    KYERO = "kyero_v3"
    THINKSPAIN = "thinkspain"
    RESALESONLINE = "resalesonline"
    FACEBOOK = "facebook"
    APLACEINTHESUN = "aplaceinthesun"
    LUXURYESTATE = "luxuryestate"
    PISOS = "pisos"
    JAMESEDITION = "jamesedition"
    INMOBILIENSCOUT24 = "inmobilienscout24"
    FOTOCASA = "fotocasa"
    GOOGLE = "google"
    INMOBALIA = "inmobalia"


portals_db_key_names: dict[PortalNames, str] = {
    PortalNames.IDEALISTA: "is_idealista_enabled",
    PortalNames.LEADINGRE: "is_leadingre_enabled",
    PortalNames.ETUOVI: "is_etuovi_enabled",
    PortalNames.STRANDPROPERTIES: "is_strandproperties_enabled",
    PortalNames.SPAINFORSALE: "is_spainforsale_enabled",
    PortalNames.RIGHTMOVE: "is_rightmove_enabled",
    PortalNames.KYERO: "is_kyero_enabled",
    PortalNames.THINKSPAIN: "is_thinkspain_enabled",
    PortalNames.RESALESONLINE: "is_resalesonline_enabled",
    PortalNames.FACEBOOK: "is_facebook_enabled",
    PortalNames.APLACEINTHESUN: "is_aplaceinthesun_enabled",
    PortalNames.LUXURYESTATE: "is_luxuryestate_enabled",
    PortalNames.PISOS: "is_pisos_enabled",
    PortalNames.JAMESEDITION: "is_jamesedition_enabled",
    PortalNames.INMOBILIENSCOUT24: "is_inmobilienscout24_enabled",
    PortalNames.FOTOCASA: "is_fotocasa_enabled",
    PortalNames.GOOGLE: "is_google_enabled",
    PortalNames.INMOBALIA: "is_inmobalia_enabled",
}


class PortalsCommonProps(BaseSchema):
    file_location_base: str
    portal: PortalNames
    set_headers: Optional[Callable[[], None]] = (
        None  # Not needed for CSV but required for XML
    )
    validate_fields: Callable[[Property, str], bool]
    set_property_tags: Optional[Callable[[], dict[str, Any]]] = (
        None  # Not needed for CSV but required for XML
    )
    fill_property_tags: Optional[
        Callable[[dict[str, Any], Property], dict[str, Any]]
    ] = None  # Not needed for CSV but required for XML
    write_to_file: Callable[[str], None]
    upload_to_s3: Callable[[str, str], None]
    file_type: Optional[FileType] = FileType.XML
    get_dict_property: Optional[Callable[[Property], dict[str, Any]]] = None


class PortalsCommon:
    def execute(self, *, db_session: Session, props: PortalsCommonProps):
        ### Sets portal information
        with db_session.begin():
            filename_suffix = app_cfg.get_filename_environment_suffix()
            file_location = f"{props.file_location_base}/properties{filename_suffix}.{props.file_type}"
            file_name = f"{props.portal}{filename_suffix}.{props.file_type}"

            # print("Starting generating", file_name)

            ### Creates request_id to log the process
            date_now = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
            request_id = f"[{date_now}]_integrations_{props.file_type}_{props.portal}"
            logger.info(
                f"Started generating {file_name}",
                extra={
                    "requestId": request_id,
                },
            )

            ### XML headers
            if props.file_type == FileType.XML:
                if (
                    not props.set_headers
                    or not props.set_property_tags
                    or not props.fill_property_tags
                ):
                    logger.info(
                        f"Error generating {file_name}. Props set_headers, set_property_tags or fill_property_tags missing",
                        extra={
                            "requestId": request_id,
                        },
                    )
                    return
                props.set_headers()

            ### Get all Properties
            portal_key_name = portals_db_key_names[props.portal]
            dict_properties: List[dict] = []
            offset = 0
            limit = 100
            count = 0
            while True:
                stmt = (
                    select(Property)
                    .options(joinedload(Property._property_type))
                    .where(
                        and_(
                            Property.status == Status.PUBLISHED,
                            getattr(Property, "portals", {portal_key_name: False})[
                                portal_key_name
                            ]
                            == True,
                            Property.data_source != DataSource.RESALES_ONLINE,
                        )
                    )
                    .offset(offset)
                    .limit(limit)
                )
                offset = offset + limit

                properties = db_session.scalars(stmt).unique().all()
                if len(properties) == 0:
                    break
                count += len(properties)

                ### Fill in the data to the <Property> tags
                for property in properties:
                    # validate required fields
                    is_valid = props.validate_fields(property, request_id)
                    if not is_valid:
                        continue

                    if props.file_type == FileType.XML:
                        ### If all required fields are there, let it do its job
                        property_tag = props.set_property_tags()
                        props.fill_property_tags(property_tag, property)
                    elif (
                        props.file_type == FileType.CSV
                        and props.get_dict_property is not None
                    ):
                        dict_property = props.get_dict_property(property)
                        dict_properties.append(dict_property)

            ### Save it to a file
            try:
                if props.file_type == FileType.XML:
                    props.write_to_file(file_location)
                elif props.file_type == FileType.CSV:
                    props.write_to_file(file_location, dict_properties)

            except Exception as e:
                logger.error(e, extra={"requestId": request_id})
                return

            ### Upload it to S3
            try:
                props.upload_to_s3(file_location, file_name)
            except ClientError as e:
                logger.error(e, extra={"requestId": request_id})
                return

            # print("Finished generating", file_name, "- total", count, "properties.")

            logger.info(
                f"Finished generating {file_name}. Total {count} properties.",
                extra={
                    "requestId": request_id,
                },
            )
