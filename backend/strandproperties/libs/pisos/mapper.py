from enum import Enum
from typing import List, Optional

from pydantic import BaseModel

from strandproperties.constants import ListingTypeEnum
from strandproperties.models.image import Image
from strandproperties.models.property import Property


class AgentInfo(BaseModel):
    agent_email: str
    agent_phone: Optional[str]


def get_agent_info(*, property: Property) -> AgentInfo:
    return AgentInfo(
        agent_email="<EMAIL>",
        agent_phone="+34676901519",
    )


class PisosPropertyTypeEnum(Enum):
    # Apartment, but usually it is bigger than apartment (see https://www.idealista.com/en/news/property-for-sale-in-spain/2023/11/01/153254-what-is-the-difference-between-piso-and-apartamento)
    PISO = 1
    # Tower
    TORRE = 2
    # Parking
    PARKING = 3
    # Commercial premises
    LOCAL_COMERCIAL = 4
    # Industrial warehouse
    NAVE_INDUSTRIAL = 5
    # Warehouse
    ALMACEN = 6
    # Office
    DESPACHO = 7
    # House
    CASA = 8
    # Plot
    SOLAR = 9
    # Duplex
    DUPLEX = 10
    # Villa
    CHALET = 11
    # Office
    OFICINA = 12
    # Apartment
    APARTAMENTO = 13
    # Building
    EDIFICIO = 14
    # Penthouse
    ATICO = 15
    # Rustic estate
    FINCA_RUSTICA = 22
    # Farmhouse
    CORTIJO = 23
    # Garage
    GARAJE = 24
    # Country house
    CASA_DE_CAMPO = 26
    # Land
    TERRENO = 28
    # Plot
    PARCELA = 29
    # Detached house
    CASA_UNIFAMILIAR = 30
    # Semi-detached house
    CASA_ADOSADA = 31
    # Loft
    LOFT = 32
    # Storage room
    TRASTERO = 34
    # Industrial land
    TERRENO_INDUSTRIAL = 35
    # Industrial plot
    PARCELA_INDUSTRIAL = 36
    # Studio
    ESTUDIO = 39
    # Room
    HABITACION = 40
    # Semi-detached house
    CASA_PAREADA = 41
    # Commercial warehouse
    NAVE_COMERCIAL = 42


"""
Maps from Property Type (Strand) to Pisos property types:
"""
_property_type_mapper = {
    "Apartment": PisosPropertyTypeEnum.APARTAMENTO,
    "Bar": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Boat": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Building": PisosPropertyTypeEnum.EDIFICIO,
    "Bungalow": PisosPropertyTypeEnum.CHALET,
    "Business": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Castle": PisosPropertyTypeEnum.TORRE,
    "Chalet": PisosPropertyTypeEnum.CHALET,
    "Commercial Other": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Commercial Premises": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Cortijo": PisosPropertyTypeEnum.CORTIJO,
    "Country House": PisosPropertyTypeEnum.CASA_DE_CAMPO,
    "Development Land": PisosPropertyTypeEnum.TERRENO,
    "Discotheque": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Duplex": PisosPropertyTypeEnum.DUPLEX,
    "Duplex Penthouse": PisosPropertyTypeEnum.ATICO,
    "Estate": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Finca": PisosPropertyTypeEnum.FINCA_RUSTICA,
    "Flat": PisosPropertyTypeEnum.APARTAMENTO,
    "Golf Course": PisosPropertyTypeEnum.TERRENO,
    "Golf Plot": PisosPropertyTypeEnum.PARCELA,
    "Ground Floor Apartment": PisosPropertyTypeEnum.APARTAMENTO,
    "Ground Floor Duplex": PisosPropertyTypeEnum.DUPLEX,
    "Hotel": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Hotel Plot": PisosPropertyTypeEnum.PARCELA,
    "House": PisosPropertyTypeEnum.CASA,
    "Industrial Land": PisosPropertyTypeEnum.TERRENO_INDUSTRIAL,
    "Industrial Premises": PisosPropertyTypeEnum.TERRENO_INDUSTRIAL,
    "Investment": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Loft": PisosPropertyTypeEnum.LOFT,
    "Mansion": PisosPropertyTypeEnum.CASA,
    "Mooring": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Office": PisosPropertyTypeEnum.OFICINA,
    "Office Units": PisosPropertyTypeEnum.OFICINA,
    "Palace": PisosPropertyTypeEnum.CASA,
    "Parking": PisosPropertyTypeEnum.PARKING,
    "Penthouse": PisosPropertyTypeEnum.ATICO,
    "Plot": PisosPropertyTypeEnum.PARCELA,
    "Residential Plot": PisosPropertyTypeEnum.PARCELA,
    "Restaurant": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Riad": PisosPropertyTypeEnum.CASA,
    "Rustic Plot": PisosPropertyTypeEnum.FINCA_RUSTICA,
    "Semi Detached House": PisosPropertyTypeEnum.CASA_PAREADA,
    "Semi Detached Villa": PisosPropertyTypeEnum.CHALET,
    "Shop": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Shopping Centre": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Store Room": PisosPropertyTypeEnum.TRASTERO,
    "Studio": PisosPropertyTypeEnum.ESTUDIO,
    "Supermarket": PisosPropertyTypeEnum.LOCAL_COMERCIAL,
    "Town House": PisosPropertyTypeEnum.CASA,
    "Triplex": PisosPropertyTypeEnum.APARTAMENTO,
    "Unique Building": PisosPropertyTypeEnum.EDIFICIO,
    "Villa": PisosPropertyTypeEnum.CHALET,
}


class PisosListingTypeEnum(Enum):
    # RENT
    ALQUILER = 3
    # SALE
    VENTA = 4


def map_listing_type_to_pisos_price_property_type_and_listing_type(
    *,
    property: Property,
) -> tuple[str, str, PisosListingTypeEnum]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]
    property_type = _property_type_mapper[property._property_type.name]

    if ListingTypeEnum.SALE in listing_types_names:
        return (
            f"{property.price_sale}",
            f"{property_type.value}",
            PisosListingTypeEnum.VENTA,
        )
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        return (
            f"{property.price_rent_long_term}",
            f"{property_type.value}",
            PisosListingTypeEnum.ALQUILER,
        )
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        return (
            f"{property.price_rent_short_term}",
            f"{property_type.value}",
            PisosListingTypeEnum.ALQUILER,
        )
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


# Checks if it is to show the street or not
class PisosMostrarCalleEnum(Enum):
    # NO
    NO = 0
    # Show the complete direction
    MOSTRAR_DIRECCIÓN_COMPLETA = 1
    # Show only street
    MOSTRAR_SÓLO_CALLE = 2


class LocationInfo(BaseModel):
    city: str
    address: str
    postcode: Optional[str]
    show_address_data: str
    latitude: Optional[float]
    longitude: Optional[float]


def get_location_info(*, property: Property) -> LocationInfo:
    location_city: str = property._area_level_1.name  # required

    private_info = property.private_info
    location = private_info["location"]

    location_address = location.get("address", None)  # required
    location_postcode = location.get("postCode", None)  # required

    show_address_data = PisosMostrarCalleEnum.NO.value
    if property.is_public_coordinates:
        show_address_data = PisosMostrarCalleEnum.MOSTRAR_DIRECCIÓN_COMPLETA.value

    latitude = property.latitude
    longitude = property.longitude

    return LocationInfo(
        address=location_address,
        postcode=location_postcode,
        city=location_city,
        show_address_data=f"{show_address_data}",
        latitude=latitude,
        longitude=longitude,
    )


def property_has_descriptions(*, property: Property) -> bool:
    descriptions = property.descriptions

    descriptions = [desc.description for desc in property.descriptions]
    non_empty_descriptions = [desc for desc in descriptions if desc]
    if non_empty_descriptions:
        return True

    return False


# Photo captions
class PisosFotosEtiquetaEnum(Enum):
    # Not informed
    NO_INFORMADO = 0
    # Cleaning
    ASEO = 1
    # Kitchen
    COCINA = 2
    # Living room
    SALON = 3
    # Hall
    HALL = 4
    # Terrace
    TERRAZA = 5
    # Facade
    FACHADA = 6
    # Floor plan
    PLANO = 7
    # Bathroom
    BANO = 8
    # Bedroom
    DORMITORIO = 9
    # Hallway
    PASILLO = 10
    # Patio
    PATIO = 11
    # Dining room
    COMEDOR = 12
    # Attic
    BUHARDILLA = 13
    # Office
    DESPACHO = 14
    # Room
    SALA = 15
    # Reception
    RECEPCION = 16
    # Archive
    ARCHIVO = 17
    # Entrance/Exit
    ENTRADA_SALIDA = 18
    # Pool
    PISCINA = 19
    # Common areas
    ZONAS_COMUNES = 20
    # Garage/Parking
    GARAJE_PARKING = 21
    # Storage room
    TRASTERO = 22
    # Staircase
    ESCALERA = 23
    # Garden
    JARDIN = 24
    # Views
    VISTAS = 25
    # Surroundings
    ENTORNO = 26
    # Concierge
    PORTERIA = 27
    # Details
    DETALLES = 28
    # Energy Certificate
    CERTIFICADO_ENERGETICO = 29


def property_has_images(*, property: Property) -> bool:
    if not property.images:
        return False

    images = [image for image in property.images if not image.is_hidden]
    if not images:
        return False

    return True


def get_sorted_non_hidden_images(*, property: Property) -> List[Image]:
    non_hidden_images = [image for image in property.images if not image.is_hidden]
    non_hidden_images.sort(key=lambda x: x.order)
    return non_hidden_images


### -------------------------------------- FEATURES --------------------------------------
def property_has_features(*, property: Property, features: List[str]) -> bool:
    has_feature = next(
        (feature for feature in property.features if feature.name in features),
        None,
    )

    return True if has_feature else False


# <Balcon_tiene/>
BALCONY_FEATURES = ["Balcony"]

# <Terraza_tiene/>
TERRACE_FEATURES = [
    "Terrace",
    "Uncovered terrace",
    "Covered Terrace",
    "Private Terrace",
]

# <AireAcondicionado_tiene/>
AIR_CONDITIONING_FEATURES = ["Air Conditioning", "Individual A/C Units"]

# <Calefaccion_tiene/>
HEATING_FEATURES = [
    "Central Heating",
    "Floor Heating",
    "Gas Heating",
    "Ceiling Heating System",
    "Underfloor heating (throughout)",
    "Underfloor heating (bathrooms)",
    "Central heating by radiators",
    "Underfloor heating (partial)",
    "Gasoil heating",
]


# <Cocina_tiene/>
ALL_KITCHEN_FEATURES = [
    "Fitted Kitchen",
    "Kitchenette",
    "Open Plan Kitchen",
    "Fully fitted kitchen",
    "Kitchen equipped",
]


# <Comedor_tiene/>
ALL_DINING_ROOM_FEATURES = ["Separate dining room", "Dining Room"]


# <Lavadero_tiene/>
ALL_LAUNDRY_FEATURES = ["Laundry Room"]


# <Trastero_tiene/>
ALL_STORAGE_FEATURES = ["Storage Room"]


# <ArmariosEmpotrados_tiene/>
ALL_WARDROBES_FEATURES = ["Fitted Wardrobes"]


# <Amueblado_tiene/>
ALL_FURNISHED_FEATURES = ["Partly Furnished", "Fully Furnished"]

# <PuertaBlindada_tiene/>
ALL_ARMORED_DOOR_FEATURES = ["Armored Door"]

# <SistemaSeguridad_tiene/>
ALL_SECURITY_SYSTEM_FEATURES = [
    "24h Security Service",
    "Security Entrance",
    "Security Shutters",
    "Surveillance Cameras",
    "Alarm System",
    "Alarm",
]

# <VidriosDobles_tiene/>
ALL_DOUBLE_GLASS_FEATURES = ["Double Glazing"]

# <Subterraneo_tiene/>
ALL_BASEMENT_FEATURES = ["Basement"]

#  <PlantaBaja_tiene/>
ALL_GROUND_FLOOR_FEATURES = ["Ground Floor Patio"]

FEATURES_MAP = {
    "Balcon_tiene": BALCONY_FEATURES,
    "Terraza_tiene": TERRACE_FEATURES,
    "AireAcondicionado_tiene": AIR_CONDITIONING_FEATURES,
    "Calefaccion_tiene": HEATING_FEATURES,
    "Cocina_tiene": ALL_KITCHEN_FEATURES,
    "Comedor_tiene": ALL_DINING_ROOM_FEATURES,
    "Lavadero_tiene": ALL_LAUNDRY_FEATURES,
    "Trastero_tiene": ALL_STORAGE_FEATURES,
    "ArmariosEmpotrados_tiene": ALL_WARDROBES_FEATURES,
    "Amueblado_tiene": ALL_FURNISHED_FEATURES,
    "PuertaBlindada_tiene": ALL_ARMORED_DOOR_FEATURES,
    "SistemaSeguridad_tiene": ALL_SECURITY_SYSTEM_FEATURES,
    "VidriosDobles_tiene": ALL_DOUBLE_GLASS_FEATURES,
    "Subterraneo_tiene": ALL_BASEMENT_FEATURES,
    "PlantaBaja_tiene": ALL_GROUND_FLOOR_FEATURES,
}


# <Garaje_tiene/>
def property_has_garage(*, property: Property) -> bool:
    garage_types = property.garage_types

    if len(garage_types) > 0:
        return True

    return False


# <Jardin_tiene/>
def property_has_garden(*, property: Property) -> tuple[bool, Optional[str]]:
    garden_types = property.garden_types

    if len(garden_types) > 0:
        garden_type_names = [gt.name for gt in garden_types]
        if "Private" in garden_type_names:
            return True, "Propio"
        else:
            return True, "Comunitario"

    return False, None


# <Piscina_tiene/>
def property_has_pool(*, property: Property) -> tuple[bool, Optional[str]]:
    pool_types = property.pool_types

    if len(pool_types) > 0:
        pool_type_names = [pt.name for pt in pool_types]
        if "Private" in pool_type_names:
            return True, "Propia"
        else:
            return True, "Comunitaria"

    return False, None


ALL_ELEVATOR_FEATURES = ["Lift"]


# <Ascensor_tiene/>
def property_has_elevator(*, property: Property) -> bool:
    has_elevator = property.building_has_elevator
    has_lift = property_has_features(property=property, features=ALL_ELEVATOR_FEATURES)

    if has_elevator or has_lift:
        return True

    return False


# <Orientacion_tiene/>
def property_has_orientations(*, property: Property) -> tuple[bool, Optional[str]]:
    orientations = property.orientations

    if len(orientations) > 0:
        orientation_names = [orientation.name for orientation in orientations]
        return True, ", ".join(orientation_names)

    return False, None


# <AnoConstruccion_tiene/>
def property_has_built_year(*, property: Property) -> tuple[bool, Optional[str]]:
    built_year = property.built_year

    if built_year:
        return True, f"{built_year}"

    return False, None


# <GastosComunidad_tiene/>
def property_has_community_fees(*, property: Property) -> tuple[bool, Optional[str]]:
    community_fees = property.communal_fees

    if community_fees:
        return True, f"{community_fees}"

    return False, None


# <Plazas_tiene/>
def property_has_parking_spaces(*, property: Property) -> tuple[bool, Optional[str]]:
    parking_spaces = property.parking_spaces

    if parking_spaces:
        return True, f"{parking_spaces}"

    return False, None


ALL_ACCESSIBILITY_FEATURES = ["Disabled Access", "Handicap Accessible"]


# <Accesibilidad_tiene/>
def property_has_accessibility(*, property: Property) -> tuple[bool, Optional[str]]:
    current_accessibilities = [
        feature.name
        for feature in property.features
        if feature.name in ALL_ACCESSIBILITY_FEATURES
    ]

    if len(current_accessibilities) > 0:
        current_accessibilities = ", ".join(current_accessibilities)
        return True, f"{current_accessibilities}"

    return False, None


PISOS_TAGS = [
    "IdInmobiliariaExterna",
    "IdPisoExterno",
    "Expediente",
    "TipoInmueble",
    "TipoOperacion",
    "NombrePoblacion",
    "TipoCalle",
    "TipoNumeroCalle",
    "NombreCalle",
    "NumeroCalle",
    "CodigoPostal",
    "AlturaPiso",
    "MostrarCalle",
    "Situacion1",
    "Latitud",
    "Longitud",
    "HabitacionesDobles",
    "HabitacionesSimples",
    "BanosCompletos",
    "BanosAuxiliares",
    "SuperficieConstruida",
    "SuperficieUtil",
    "SuperficieSolar",
    "Descripcion",
    "EstadoConservacion",
    "EnergiaConsumoCategoria",
    "EnergiaConsumoValor",
    "EnergiaEmisionCategoria",
    "EnergiaEmisionValor",
    "CertificadoNumRegistro",
    "CodigoViviendaTuristica",
    "EstadoCodigoViviendaTuristica",
    "PrecioEur",
    "AlquilerVacacional",
    "PrecioAlquilerVacacionalMes",
    "PrecioAlquilerVacacionalSemana",
    "PrecioAlquilerVacacionalDia",
    "OpcionACompra",
    "PrecioVentaOpcionCompra",
    "IndiceReferenciaPreciosVivienda",
    "PrecioAlquilerAnterior",
    "Email",
    "Telefono",
    "NumeroVecinos",
    "Destacado",
    "Exclusivo",
    "EtiquetaExclusivo",
    "Cocina_tiene",
    "Cocina_comentario",
    "Comedor_tiene",
    "Comedor_comentario",
    "Lavadero_tiene",
    "Lavadero_comentario",
    "Trastero_tiene",
    "Trastero_comentario",
    "Garaje_tiene",
    "Garaje_comentario",
    "Ascensor_tiene",
    "Ascensor_comentario",
    "Balcon_tiene",
    "Balcon_comentario",
    "Terraza_tiene",
    "Terraza_comentario",
    "Jardin_tiene",
    "Jardin_comentario",
    "Piscina_tiene",
    "Piscina_comentario",
    "ArmariosEmpotrados_tiene",
    "ArmariosEmpotrados_comentario",
    "Calefaccion_tiene",
    "Calefaccion_comentario",
    "AireAcondicionado_tiene",
    "AireAcondicionado_comentario",
    "Amueblado_tiene",
    "Amueblado_comentario",
    "PuertaBlindada_tiene",
    "PuertaBlindada_comentario",
    "PorteroAutomatico_tiene",
    "PorteroAutomatico_comentario",
    "SistemaSeguridad_tiene",
    "SistemaSeguridad_comentario",
    "VidriosDobles_tiene",
    "VidriosDobles_comentario",
    "Chimenea_tiene",
    "Chimenea_comentario",
    "Suelo_tiene",
    "Suelo_comentario",
    "CarpinteriaInterior_tiene",
    "CarpinteriaInterior_comentario",
    "CarpinteriaExterior_tiene",
    "CarpinteriaExterior_comentario",
    "Exterior_tiene",
    "Exterior_comentario",
    "Interior_tiene",
    "Interior_comentario",
    "Orientacion_tiene",
    "Orientacion_comentario",
    "Soleado_tiene",
    "Soleado_comentario",
    "AnoConstruccion_tiene",
    "AnoConstruccion_comentario",
    "GastosComunidad_tiene",
    "GastosComunidad_comentario",
    "SuministroAgua_tiene",
    "SuministroAgua_comentario",
    "SuministroElectrico_tiene",
    "SuministroElectrico_comentario",
    "SuministroGas_tiene",
    "SuministroGas_comentario",
    "SuministroTelefono_tiene",
    "SuministroTelefono_comentario",
    "Plazas_tiene",
    "Plazas_comentario",
    "Subterraneo_tiene",
    "Subterraneo_comentario",
    "Divisiones_tiene",
    "Divisiones_comentario",
    "Cerrado_tiene",
    "Cerrado_comentario",
    "Columnas_tiene",
    "Columnas_comentario",
    "Vallado_tiene",
    "Vallado_comentario",
    "Cultivable_tiene",
    "Cultivable_comentario",
    "Arboles_tiene",
    "Arboles_comentario",
    "Edificaciones_tiene",
    "Edificaciones_comentario",
    "Edificable_tiene",
    "Edificable_comentario",
    "Alcantarillado_tiene",
    "Alcantarillado_comentario",
    "Urbanizado_tiene",
    "Urbanizado_comentario",
    "Asfaltado_tiene",
    "Asfaltado_comentario",
    "Alumbrado_tiene",
    "Alumbrado_comentario",
    "Accesibilidad_tiene",
    "Accesibilidad_comentario",
    "MuelleDeCarga_tiene",
    "MuelleDeCarga_comentario",
    "Grua_tiene",
    "Grua_comentario",
    "Aislantes_tiene",
    "Aislantes_comentario",
    "Oficina_tiene",
    "Oficina_comentario",
    "SalidaHumos_tiene",
    "SalidaHumos_comentario",
    "Cubierta_tiene",
    "Cubierta_comentario",
    "SalidaAntincendios_tiene",
    "SalidaAntincendios_comentario",
    "InstalacionRed_tiene",
    "InstalacionRed_comentario",
    "PuertasAcceso_tiene",
    "PuertasAcceso_comentario",
    "Escaparate_tiene",
    "Escaparate_comentario",
    "PlantaBaja_tiene",
    "PlantaBaja_comentario",
    "CajaFuerte_tiene",
    "CajaFuerte_comentario",
    "Vestuarios_tiene",
    "Vestuarios_comentario",
    "Altillo_tiene",
    "Altillo_comentario",
    "Fotos",
    "VideosExternos",
    "ToursVirtuales",
    "Documentos",
    "Descripciones",
]
