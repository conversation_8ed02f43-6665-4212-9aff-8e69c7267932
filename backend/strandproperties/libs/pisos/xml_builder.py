import copy
import xml.dom.minidom
import xml.etree.ElementTree as ET

from strandproperties.config import app_cfg
from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.libs.aws import S3Service
from strandproperties.libs.kyero.mapper import replace_description_characters
from strandproperties.libs.pisos.mapper import (
    FEATURES_MAP,
    PISOS_TAGS,
    PisosFotosEtiquetaEnum,
    get_agent_info,
    get_location_info,
    get_sorted_non_hidden_images,
    map_listing_type_to_pisos_price_property_type_and_listing_type,
    property_has_accessibility,
    property_has_built_year,
    property_has_community_fees,
    property_has_descriptions,
    property_has_elevator,
    property_has_features,
    property_has_garage,
    property_has_garden,
    property_has_images,
    property_has_orientations,
    property_has_parking_spaces,
    property_has_pool,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import allowed_streaming_video
from strandproperties.logger import logger
from strandproperties.models.property import Property, PropertyDescription
from strandproperties.scripts.base import BaseScript


class PisosXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        publication = ET.Element("Publicacion")
        properties = ET.SubElement(publication, "Table", attrib={"Name": "Inmuebles"})

        self.encoding = encoding
        self.publication = publication
        self.properties = properties

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}

        ### Inmueble tag
        property_tag["inmueble"] = ET.SubElement(self.properties, "Inmueble")

        for tag in PISOS_TAGS:
            property_tag[tag] = ET.SubElement(property_tag["inmueble"], tag)

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        ### Inmueble/IdInmobiliariaExterna
        # TODO: this is the client identifier. Not sure if when creating an account there
        # TODO: they will provide it
        property_tag["IdInmobiliariaExterna"].text = "strand_properties"
        ### Inmueble/IdPisoExterno
        property_tag["IdPisoExterno"].text = f"{property.id}"
        ### Inmueble/Expediente
        property_tag["Expediente"].text = f"{property.reference}"

        (price, property_type, listing_type) = (
            map_listing_type_to_pisos_price_property_type_and_listing_type(
                property=property
            )
        )
        ### Inmueble/TipoInmueble
        property_tag["TipoInmueble"].text = property_type
        ### Inmueble/TipoOperacion
        property_tag["TipoOperacion"].text = f"{listing_type.value}"
        ### Inmueble/PrecioEur
        property_tag["PrecioEur"].text = price
        property_tag["AlquilerVacacional"].text = (
            "0"  # If it is a seasonal rent. 0 = No / 1 = Si
        )

        location_info = get_location_info(property=property)
        ### Inmueble/NombrePoblacion
        property_tag["NombrePoblacion"].text = location_info.city
        ### Inmueble/NombreCalle
        property_tag["NombreCalle"].text = location_info.address
        ### Inmueble/CodigoPostal
        property_tag["CodigoPostal"].text = location_info.postcode
        ### Inmueble/MostrarCalle
        property_tag["MostrarCalle"].text = location_info.show_address_data
        if location_info.latitude:
            ### Inmueble/Latitud
            property_tag["Latitud"].text = f"{location_info.latitude}"
        if location_info.longitude:
            ### Inmueble/Longitud
            property_tag["Longitud"].text = f"{location_info.longitude}"

        if property.bedrooms:
            ### Inmueble/HabitacionesDobles
            property_tag["HabitacionesDobles"].text = f"{property.bedrooms}"

        if property.bathrooms:
            ### Inmueble/BanosCompletos
            property_tag["BanosCompletos"].text = f"{property.bathrooms}"

        ### Inmueble/SuperficieConstruida
        if property.built_area:
            property_tag["SuperficieConstruida"].text = f"{property.built_area}"
        if property.plot_area:
            property_tag["SuperficieSolar"].text = f"{property.plot_area}"

        if property.interior_area:
            ### Inmueble/SuperficieUtil
            property_tag["SuperficieUtil"].text = f"{property.interior_area}"

        agent_info = get_agent_info(property=property)
        ### Inmueble/Email
        property_tag["Email"].text = agent_info.agent_email
        if agent_info.agent_phone:
            ### Inmueble/Telefono
            property_tag["Telefono"].text = agent_info.agent_phone

        #### ----- Features -----
        for feature_tag, feature_values in FEATURES_MAP.items():
            if property_has_features(property=property, features=feature_values):
                property_tag[feature_tag].text = "1"

        ### Inmueble/Garaje_tiene
        if property_has_garage(property=property):
            property_tag["Garaje_tiene"].text = "1"

        ### Inmueble/Jardin_tiene
        (has_garden, garden_comment) = property_has_garden(property=property)
        if has_garden:
            property_tag["Jardin_tiene"].text = "1"
            property_tag["Jardin_comentario"].text = garden_comment

        ### Inmueble/Piscina_tiene
        (has_pool, pool_comment) = property_has_pool(property=property)
        if has_pool:
            property_tag["Piscina_tiene"].text = "1"
            property_tag["Piscina_comentario"].text = pool_comment

        ### Inmueble/Ascensor_tiene
        if property_has_elevator(property=property):
            property_tag["Ascensor_tiene"].text = "1"

        ### Inmueble/Orientacion_tiene
        (has_orientations, orientations_comment) = property_has_orientations(
            property=property
        )
        if has_orientations:
            property_tag["Orientacion_tiene"].text = "1"
            property_tag["Orientacion_comentario"].text = orientations_comment

        ### Inmueble/AnoConstruccion_tiene
        (has_built_year, built_year_comment) = property_has_built_year(
            property=property
        )
        if has_built_year:
            property_tag["AnoConstruccion_tiene"].text = "1"
            property_tag["AnoConstruccion_comentario"].text = built_year_comment

        ### Inmueble/GastosComunidad_tiene
        (has_community_fees, community_fees_comment) = property_has_community_fees(
            property=property
        )
        if has_community_fees:
            property_tag["GastosComunidad_tiene"].text = "1"
            property_tag["GastosComunidad_comentario"].text = community_fees_comment

        ### Inmueble/Plazas_tiene
        (has_parking_spaces, parking_spaces_comment) = property_has_parking_spaces(
            property=property
        )
        if has_parking_spaces:
            property_tag["Plazas_tiene"].text = "1"
            property_tag["Plazas_comentario"].text = parking_spaces_comment

        ### Inmueble/Accesibilidad_tiene
        (has_accessibility, accessibility_comment) = property_has_accessibility(
            property=property
        )
        if has_accessibility:
            property_tag["Accesibilidad_tiene"].text = "1"
            property_tag["Accesibilidad_comentario"].text = accessibility_comment

        #### --------------------

        ### Fotos tag
        if property_has_images(property=property):
            pictures = get_sorted_non_hidden_images(property=property)
            for index, picture in enumerate(pictures):
                property_tag[f"Fotos{index}"] = ET.SubElement(
                    property_tag["Fotos"],
                    "Foto",
                )
                ET.SubElement(
                    property_tag[f"Fotos{index}"],
                    "Url",
                ).text = picture.url
                ET.SubElement(
                    property_tag[f"Fotos{index}"],
                    "Etiqueta",
                ).text = f"{PisosFotosEtiquetaEnum.NO_INFORMADO.value}"

        for idx, v in enumerate(property.video_streams):
            if not v.is_hidden and allowed_streaming_video(v.url):
                video = ET.SubElement(property_tag["VideosExternos"], f"Video{idx+1}")
                video.text = v.url
                break

        for idx, v in enumerate(property.video_tours):
            if not v.is_hidden:
                tour = ET.SubElement(
                    property_tag["ToursVirtuales"], f"TourVirtual{idx+1}"
                )
                tour.text = v.url
                break

        ### descriptions tag
        def get_descriptions_by_language() -> dict[str, list[PropertyDescription]]:
            descriptions_by_language: dict[str, list[PropertyDescription]] = {}
            for language in Language:
                descriptions_by_language[language.value] = []

            if property_has_descriptions(property=property):
                for description in property.descriptions:
                    if not description.description:
                        continue
                    descriptions_by_language[description.language].append(description)
            return descriptions_by_language

        def get_best_description_by_language(
            descriptions_by_language: dict[str, list[PropertyDescription]]
        ) -> dict[str, str]:
            best_descriptions_by_language: dict[str, str] = {}
            for language in Language:
                current_description = descriptions_by_language[language.value]
                full_desc = ""
                extra_desc = ""
                for desc in current_description:
                    if desc.type == DescriptionType.FULL:
                        full_desc = desc.description
                    elif desc.type == DescriptionType.EXTRA:
                        extra_desc = desc.description
                if full_desc:
                    best_descriptions_by_language[language.value] = full_desc
                elif extra_desc:
                    best_descriptions_by_language[language.value] = extra_desc
                    continue

            return best_descriptions_by_language

        best_descriptions_by_language = get_best_description_by_language(
            descriptions_by_language=get_descriptions_by_language()
        )

        for language, description in best_descriptions_by_language.items():
            ET.SubElement(
                property_tag["Descripciones"],
                f"{language}",
            ).text = f"<![CDATA[ {replace_description_characters(description)} ]]>"

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.publication))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][Pisos]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # we need info from location which is REQUIRED
        if not (property.private_info and "location" in property.private_info):  # type: ignore
            logger.info(
                f"> Skipping property [{property.reference}][Pisos]: private_info.location is required",
                extra={"requestId": request_id},
            )
            return False

        location = property.private_info["location"]
        if location.get("address") is None:
            logger.info(
                f"> Skipping property [{property.reference}][Pisos]: private_info.location.address is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][Pisos]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][Pisos]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][Pisos]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][Pisos]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        # Agent is REQUIRED
        if not property.realtor_users:
            logger.info(
                f"> Skipping property [{property.reference}][Pisos]: realtor_users is required",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/pisos",
            portal=PortalNames.PISOS,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
