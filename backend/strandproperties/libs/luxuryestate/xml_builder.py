import copy
import xml.dom.minidom
import xml.etree.ElementTree as ET
from datetime import datetime

from strandproperties.config import app_cfg
from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.libs.aws import S3Service
from strandproperties.libs.kyero.mapper import replace_description_characters
from strandproperties.libs.luxuryestate.mapper import (
    LuxuryEstateMapOptionEnum,
    get_agent_info,
    get_cooling_types,
    get_kitchen_types,
    get_location_info,
    get_location_map_option,
    get_property_currency,
    get_sorted_non_hidden_images,
    get_view_types,
    map_condition_to_luxuryestate_buildingstatus,
    map_listing_type_to_luxuryestate_price_property_type_and_transaction,
    property_has_balcony,
    property_has_cooling,
    property_has_descriptions,
    property_has_images,
    property_has_kitchen,
    property_has_terrace,
    property_has_views,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import allowed_streaming_video
from strandproperties.logger import logger
from strandproperties.models.property import Property, PropertyDescription
from strandproperties.scripts.base import BaseScript


class LuxuryEstateXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element("root")

        metadata_header = ET.SubElement(root, "metadata")
        metadata_build_date = ET.SubElement(metadata_header, "build-date")
        metadata_build_date.text = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        metadata_tech_contact = ET.SubElement(metadata_header, "tech-contact")
        metadata_tech_contact.text = "<EMAIL>"

        properties = ET.SubElement(root, "properties")

        self.root = root
        self.encoding = encoding
        self.properties = properties

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        property_dates = sorted(
            filter(
                None,
                [
                    property.updated_at,
                    *list(map(lambda v: v.updated_at, property.images)),
                    *list(map(lambda v: v.updated_at, property.video_streams)),
                ],
            ),
            reverse=True,
        )

        property_last_modified = next(
            iter(property_dates),
            datetime.now(),
        )

        ### Property tag
        property_tag["property"] = ET.SubElement(
            self.properties,
            "property",
            attrib={
                "last-modified": (property_last_modified.strftime("%Y-%m-%dT%H:%M:%S")),
                "external-id": f"{property.id}",
                "ref": property.reference,
            },
        )

        # ------

        ### Agent tag
        agent_info = get_agent_info(property=property)
        property_tag["agent"] = ET.SubElement(
            property_tag["property"],
            "agent",
            attrib={
                "last-modified": agent_info.last_modified_at,
                "external-id": agent_info.agent_id,
                "ref": agent_info.agent_reference,
            },
        )
        # agent/user-name
        ET.SubElement(property_tag["agent"], "user-name").text = agent_info.agent_email
        # agent/location
        property_tag["location"] = ET.SubElement(
            property_tag["agent"],
            "location",
            attrib={
                "map": LuxuryEstateMapOptionEnum.NO.value,
            },
        )
        ET.SubElement(property_tag["location"], "country-code").text = (
            agent_info.country_code
        )
        ET.SubElement(property_tag["location"], "city").text = agent_info.city
        # agent/contact-data
        property_tag["contact_data"] = ET.SubElement(
            property_tag["agent"], "contact-data"
        )
        ET.SubElement(property_tag["contact_data"], "email").text = "<EMAIL>"
        ET.SubElement(property_tag["contact_data"], "mobile").text = "+34676901519"
        # agent/picture
        ET.SubElement(property_tag["agent"], "picture").text = agent_info.picture
        # agent/currency
        ET.SubElement(property_tag["agent"], "currency").text = agent_info.currency
        # agent/first-name
        ET.SubElement(property_tag["agent"], "first-name").text = (
            agent_info.agent_firstname
        )
        # agent/last-name
        ET.SubElement(property_tag["agent"], "last-name").text = (
            agent_info.agent_lastname
        )
        # agent/displayed-name
        ET.SubElement(property_tag["agent"], "displayed-name").text = (
            agent_info.agent_fullname
        )

        # agent/company
        property_tag["company"] = ET.SubElement(
            property_tag["agent"],
            "company",
            attrib={"external-id": "strand-properties"},
        )
        # agent/company/user-name
        ET.SubElement(property_tag["company"], "user-name").text = (
            "<EMAIL>"
        )
        # agent/company/address-data
        property_tag["company-address-data"] = ET.SubElement(
            property_tag["company"], "address-data", attrib={"show": "true"}
        )
        # agent/company/address-data/formatted-address
        ET.SubElement(
            property_tag["company-address-data"], "formatted-address"
        ).text = "Av. Playas del Duque, Málaga 1 C, Puerto Banús"
        # agent/company/address-data/postal-code
        ET.SubElement(property_tag["company-address-data"], "postal-code").text = (
            "29660"
        )
        # agent/company/location
        property_tag["company-location"] = ET.SubElement(
            property_tag["company"], "location", attrib={"map": "hide"}
        )
        # agent/company/location/country-code
        ET.SubElement(property_tag["company-location"], "country-code").text = "ES"
        # agent/company/location/city
        ET.SubElement(property_tag["company-location"], "city").text = "Marbella"
        # agent/company/contact-data
        property_tag["company-contact-data"] = ET.SubElement(
            property_tag["company"], "contact-data"
        )
        # agent/company/contact-data/email
        ET.SubElement(property_tag["company-contact-data"], "email").text = (
            "<EMAIL>"
        )
        # agent/company/contact-data/phone
        ET.SubElement(property_tag["company-contact-data"], "phone").text = (
            "+34 676 90 15 19"
        )
        # agent/company/picture
        ET.SubElement(property_tag["company"], "picture").text = (
            "https://d3gi2trcn3ggeh.cloudfront.net/images/strandlogo.jpg"
        )
        # agent/company/corporate-name
        ET.SubElement(property_tag["company"], "corporate-name").text = (
            "Strand Properties"
        )

        # ------

        location_info = get_location_info(property=property)

        ### address-data tag
        property_tag["address_data"] = ET.SubElement(
            property_tag["property"],
            "address-data",
            attrib={"show": location_info.show_address_data},
        )
        # address-data/postal-code
        ET.SubElement(property_tag["address_data"], "postal-code").text = (
            location_info.postcode
        )
        # address-data/street-name
        ET.SubElement(property_tag["address_data"], "street-name").text = (
            location_info.address
        )

        # ------

        ### location tag
        property_tag["location"] = ET.SubElement(
            property_tag["property"],
            "location",
            attrib={"map": get_location_map_option(property=property)},
        )
        # location/country-code
        ET.SubElement(property_tag["location"], "country-code").text = (
            location_info.country_code
        )
        # location/administrative-area-level1
        ET.SubElement(property_tag["location"], "administrative-area-level1").text = (
            location_info.administrative_area_level1
        )
        # location/administrative-area-level2
        ET.SubElement(property_tag["location"], "administrative-area-level2").text = (
            location_info.administrative_area_level2
        )
        # location/postal-code
        ET.SubElement(property_tag["location"], "postal-code").text = (
            location_info.postcode
        )
        # location/city
        ET.SubElement(property_tag["location"], "city").text = location_info.city
        # location/latitude
        ET.SubElement(property_tag["location"], "latitude").text = (
            f"{location_info.latitude}" if location_info.latitude else ""
        )
        # location/longitude
        ET.SubElement(property_tag["location"], "longitude").text = (
            f"{location_info.longitude}" if location_info.longitude else ""
        )

        # ------

        (price, property_type, transaction_type) = (
            map_listing_type_to_luxuryestate_price_property_type_and_transaction(
                property=property
            )
        )
        ### features tag
        property_tag["features"] = ET.SubElement(
            property_tag["property"],
            "features",
        )
        # features/type
        ET.SubElement(property_tag["features"], "type").text = property_type
        # features/price
        ET.SubElement(
            property_tag["features"],
            "price",
            attrib={"currency": get_property_currency(property=property)},
        ).text = price
        # features/transaction
        ET.SubElement(property_tag["features"], "transaction").text = transaction_type
        # features/internal-size
        ET.SubElement(
            property_tag["features"],
            "internal-size",
            attrib={"unit": "m²"},
        ).text = (
            f"{property.interior_area}" if property.interior_area else ""
        )
        # features/external-size
        ET.SubElement(
            property_tag["features"],
            "external-size",
            attrib={"unit": "m²"},
        ).text = (
            f"{property.plot_area}" if property.plot_area else ""
        )
        # features/bedrooms
        ET.SubElement(
            property_tag["features"],
            "bedrooms",
        ).text = (
            f"{property.bedrooms}" if property.bedrooms else ""
        )
        # features/bathrooms
        ET.SubElement(
            property_tag["features"],
            "bathrooms",
        ).text = (
            f"{property.bathrooms}" if property.bathrooms else ""
        )
        # features/balcony
        balcony = property_has_balcony(property=property)
        ET.SubElement(
            property_tag["features"],
            "balcony",
            attrib={"count": "1" if balcony else "0"},
        ).text = (
            "true" if balcony else "false"
        )
        # features/terrace
        terrace = property_has_terrace(property=property)
        ET.SubElement(
            property_tag["features"],
            "terrace",
        ).text = (
            "true" if terrace else "false"
        )
        # features/cooling-type
        if property_has_cooling(property=property):
            property_tag["cooling_type"] = ET.SubElement(
                property_tag["features"],
                "cooling-type",
            )
            cooling_types = get_cooling_types(property=property)
            for cooling_type in cooling_types:
                ET.SubElement(
                    property_tag["cooling_type"],
                    "type",
                ).text = cooling_type
        # features/kitchen
        if property_has_kitchen(property=property):
            kitchen_types = get_kitchen_types(property=property)
            property_tag["kitchen"] = ET.SubElement(
                property_tag["features"],
                "kitchen",
                attrib={
                    "count": f"{len(kitchen_types) if len(kitchen_types) > 0 else 1}"
                },
            )
            for index, kitchen_type in enumerate(kitchen_types):
                property_tag[f"kitchen_type_{index}"] = ET.SubElement(
                    property_tag["kitchen"],
                    "kitchen-type",
                )
                ET.SubElement(
                    property_tag[f"kitchen_type_{index}"],
                    "type",
                ).text = kitchen_type
        # features/view-type
        if property_has_views(property=property):
            property_tag["view_type"] = ET.SubElement(
                property_tag["features"],
                "view-type",
            )
            view_types = get_view_types(property=property)
            for view_type in view_types:
                ET.SubElement(
                    property_tag["view_type"],
                    "type",
                ).text = view_type
        # features/building-status
        ET.SubElement(
            property_tag["features"],
            "building-status",
        ).text = map_condition_to_luxuryestate_buildingstatus(property=property)

        # ------

        ### descriptions tag
        def get_descriptions_by_language() -> dict[str, list[PropertyDescription]]:
            descriptions_by_language: dict[str, list[PropertyDescription]] = {}
            for language in Language:
                descriptions_by_language[language.value] = []

            if property_has_descriptions(property=property):
                property_tag["descriptions"] = ET.SubElement(
                    property_tag["property"],
                    "descriptions",
                )
                for description in property.descriptions:
                    if not description.description:
                        continue
                    descriptions_by_language[description.language].append(description)
            return descriptions_by_language

        def get_best_description_by_language(
            descriptions_by_language: dict[str, list[PropertyDescription]]
        ) -> dict[str, str]:
            best_descriptions_by_language: dict[str, str] = {}
            for language in Language:
                current_description = descriptions_by_language[language.value]
                full_desc = ""
                extra_desc = ""
                for desc in current_description:
                    if desc.type == DescriptionType.FULL:
                        full_desc = desc.description
                    elif desc.type == DescriptionType.EXTRA:
                        extra_desc = desc.description
                if full_desc:
                    best_descriptions_by_language[language.value] = full_desc
                elif extra_desc:
                    best_descriptions_by_language[language.value] = extra_desc
                    continue

            return best_descriptions_by_language

        best_descriptions_by_language = get_best_description_by_language(
            descriptions_by_language=get_descriptions_by_language()
        )

        for language, description in best_descriptions_by_language.items():
            ET.SubElement(
                property_tag["descriptions"],
                "description",
                attrib={"language": f"{language}"},
            ).text = f"<![CDATA[ {replace_description_characters(description)} ]]>"

        # ------

        ### pictures tag
        if property_has_images(property=property):
            property_tag["pictures"] = ET.SubElement(
                property_tag["property"],
                "pictures",
            )
            pictures = get_sorted_non_hidden_images(property=property)
            for index, picture in enumerate(pictures):
                ET.SubElement(
                    property_tag["pictures"],
                    "picture",
                    attrib={
                        "order": f"{index +1}",
                        "modification-timestamp": (
                            picture.updated_at.strftime("%Y-%m-%dT%H:%M:%S")
                            if picture.updated_at
                            else datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
                        ),
                    },
                ).text = picture.url

        # extra tag
        property_tag["extra"] = ET.SubElement(property_tag["property"], "extra")
        videos_tag = ET.SubElement(property_tag["extra"], "videos")
        for v in property.video_streams:
            if not v.is_hidden and allowed_streaming_video(v.url):
                video = ET.SubElement(
                    videos_tag,
                    "video",
                )
                video.text = v.url

        tours_tag = ET.SubElement(property_tag["extra"], "virtual-tours")
        for v in property.video_tours:
            if not v.is_hidden:
                tour = ET.SubElement(
                    tours_tag,
                    "virtual-tour",
                )
                tour.text = v.url

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        xml_string = xml_string.replace("__amp__", "&")
        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding) + part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][LuxuryEstate]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][LuxuryEstate]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][LuxuryEstate]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][LuxuryEstate]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][LuxuryEstate]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        # Agent is REQUIRED
        if not property.realtor_users:
            logger.info(
                f"> Skipping property [{property.reference}][LuxuryEstate]: realtor_users is required",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/luxuryestate",
            portal=PortalNames.LUXURYESTATE,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
