from enum import Enum
from typing import List, Optional, cast

from pydantic import BaseModel
from strandproperties.constants import ConditionType, Currency, ListingTypeEnum
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.config import app_cfg


def get_property_currency(*, property: Property) -> str:
    return property.currency.upper() if property.currency else str(Currency.EUR).upper()


class CountryCodeISO3166(str, Enum):
    SPAIN = "ES"
    FINLAND = "FI"


class AgentInfo(BaseModel):
    agent_firstname: str
    agent_lastname: str
    agent_fullname: str
    agent_email: str
    agent_phone: str
    agent_id: str
    agent_reference: str
    last_modified_at: str
    city: str
    picture: str
    currency: str
    country_code: str


STRAND_LOGO_IMAGE = f"{app_cfg.aws_cloudfront_url}/images/strandlogo.jpg"


def get_agent_info(*, property: Property) -> AgentInfo:
    realtors = property.realtor_users
    agent = realtors[0]
    return AgentInfo(
        agent_firstname=agent.first_name,
        agent_lastname=agent.last_name,
        agent_fullname=f"{agent.first_name} {agent.last_name}",
        agent_email=agent.email,
        agent_phone=agent.phone_number if agent.phone_number else "Not defined",
        agent_id=f"{agent.id}",
        agent_reference=f"AGENT_{agent.id}",
        last_modified_at=agent.updated_at.strftime("%Y-%m-%dT%H:%M:%S"),
        city=property._area_level_1.name,
        picture=agent.photo_url or STRAND_LOGO_IMAGE,
        currency=get_property_currency(property=property),
        country_code=CountryCodeISO3166.SPAIN,
    )


class LocationInfo(BaseModel):
    address: Optional[str]
    postcode: Optional[str]
    city: str
    country_code: str
    administrative_area_level1: Optional[str]
    administrative_area_level2: Optional[str]
    show_address_data: str
    latitude: Optional[float]
    longitude: Optional[float]


class LuxuryEstateMapOptionEnum(str, Enum):
    SHOW_MARKER = "show_marker"
    EXACT = "exact"
    NEAR = "near"
    NO = "no"


def get_location_map_option(*, property: Property) -> str:
    latitude = property.latitude
    longitude = property.longitude
    coordinates_public = property.is_public_coordinates

    if coordinates_public and latitude and longitude:
        return LuxuryEstateMapOptionEnum.EXACT.value

    return LuxuryEstateMapOptionEnum.NO.value


def get_location_info(*, property: Property) -> LocationInfo:
    location_city: str = property._area_level_1.name  # required
    location_country = property._area_level_1.country  # required

    location_administrative_area_level_1: Optional[str] = (  # optional
        property._area_level_2.name if property._area_level_2 else None
    )
    location_administrative_area_level_2: Optional[str] = (  # optional
        property._area_level_3.name if property._area_level_3 else None
    )

    private_info = property.private_info
    location = (
        private_info["location"]
        if private_info and "location" in private_info
        else None
    )

    location_address: Optional[str] = None
    location_postcode: Optional[str] = None

    if location:
        location_address = location.get("address", None)  # optional
        location_postcode = location.get("postCode", None)  # optional

    # INFO: this listing is not exhaustive.
    if location_country == "Spain":
        location_country = CountryCodeISO3166.SPAIN
    elif location_country == "Finland":
        location_country = CountryCodeISO3166.SPAIN

    show_address_data = "false"
    if property.is_public_coordinates:
        show_address_data = "true"

    latitude = property.latitude
    longitude = property.longitude

    return LocationInfo(
        address=location_address,
        postcode=location_postcode,
        city=location_city,
        country_code=location_country,
        administrative_area_level1=location_administrative_area_level_1,
        administrative_area_level2=location_administrative_area_level_2,
        show_address_data=show_address_data,
        latitude=latitude,
        longitude=longitude,
    )


class LuxuryEstatePropertyTypeEnum(str, Enum):
    APARTMENT = "Apartment"
    BAR_RESTAURANT = "Bar / Restaurant"
    BUNGALOW = "Bungalow"
    CASTLE = "Castle"
    COMMERCIAL = "Commercial"
    COUNTRY_HOUSE = "Country House"
    DETACHED_HOUSE = "Detached House"
    DUPLEX = "Duplex"
    HOTEL = "Hotel"
    HOUSE = "House"
    LAND = "Land"
    LOFT = "Loft"
    MANSION = "Mansion"
    OFFICE = "Office"
    OTHER = "Other"
    PALACE = "Palace"
    PENTHOUSE = "Penthouse"
    RURAL_FARMHOUSE = "Rural / Farmhouse"
    SEMIDETACHED_HOUSE = "Semidetached House"
    SHOP = "Shop"
    STUDIO = "Studio"
    TOWNHOUSE = "Townhouse"
    VILLA = "Villa"


"""
Maps from Property Type (Strand) to LuxuryEstate property types:
"""
_property_type_mapper = {
    "Apartment": LuxuryEstatePropertyTypeEnum.APARTMENT,
    "Bar": LuxuryEstatePropertyTypeEnum.BAR_RESTAURANT,
    "Boat": LuxuryEstatePropertyTypeEnum.OTHER,
    "Building": LuxuryEstatePropertyTypeEnum.OTHER,
    "Bungalow": LuxuryEstatePropertyTypeEnum.BUNGALOW,
    "Business": LuxuryEstatePropertyTypeEnum.COMMERCIAL,
    "Castle": LuxuryEstatePropertyTypeEnum.CASTLE,
    "Chalet": LuxuryEstatePropertyTypeEnum.OTHER,
    "Commercial Other": LuxuryEstatePropertyTypeEnum.COMMERCIAL,
    "Commercial Premises": LuxuryEstatePropertyTypeEnum.COMMERCIAL,
    "Cortijo": LuxuryEstatePropertyTypeEnum.OTHER,
    "Country House": LuxuryEstatePropertyTypeEnum.COUNTRY_HOUSE,
    "Development Land": LuxuryEstatePropertyTypeEnum.LAND,
    "Discotheque": LuxuryEstatePropertyTypeEnum.OTHER,
    "Duplex": LuxuryEstatePropertyTypeEnum.DUPLEX,
    "Duplex Penthouse": LuxuryEstatePropertyTypeEnum.PENTHOUSE,
    "Estate": LuxuryEstatePropertyTypeEnum.OTHER,
    "Finca": LuxuryEstatePropertyTypeEnum.LAND,
    "Flat": LuxuryEstatePropertyTypeEnum.APARTMENT,
    "Golf Course": LuxuryEstatePropertyTypeEnum.LAND,
    "Golf Plot": LuxuryEstatePropertyTypeEnum.LAND,
    "Ground Floor Apartment": LuxuryEstatePropertyTypeEnum.APARTMENT,
    "Ground Floor Duplex": LuxuryEstatePropertyTypeEnum.DUPLEX,
    "Hotel": LuxuryEstatePropertyTypeEnum.HOTEL,
    "Hotel Plot": LuxuryEstatePropertyTypeEnum.LAND,
    "House": LuxuryEstatePropertyTypeEnum.HOUSE,
    "Industrial Land": LuxuryEstatePropertyTypeEnum.LAND,
    "Industrial Premises": LuxuryEstatePropertyTypeEnum.COMMERCIAL,
    "Investment": LuxuryEstatePropertyTypeEnum.OTHER,
    "Loft": LuxuryEstatePropertyTypeEnum.LOFT,
    "Mansion": LuxuryEstatePropertyTypeEnum.MANSION,
    "Mooring": LuxuryEstatePropertyTypeEnum.OTHER,
    "Office": LuxuryEstatePropertyTypeEnum.OFFICE,
    "Office Units": LuxuryEstatePropertyTypeEnum.OFFICE,
    "Palace": LuxuryEstatePropertyTypeEnum.PALACE,
    "Parking": LuxuryEstatePropertyTypeEnum.OTHER,
    "Penthouse": LuxuryEstatePropertyTypeEnum.PENTHOUSE,
    "Plot": LuxuryEstatePropertyTypeEnum.LAND,
    "Residential Plot": LuxuryEstatePropertyTypeEnum.LAND,
    "Restaurant": LuxuryEstatePropertyTypeEnum.BAR_RESTAURANT,
    "Riad": LuxuryEstatePropertyTypeEnum.OTHER,
    "Rustic Plot": LuxuryEstatePropertyTypeEnum.LAND,
    "Semi Detached House": LuxuryEstatePropertyTypeEnum.SEMIDETACHED_HOUSE,
    "Semi Detached Villa": LuxuryEstatePropertyTypeEnum.VILLA,
    "Shop": LuxuryEstatePropertyTypeEnum.SHOP,
    "Shopping Centre": LuxuryEstatePropertyTypeEnum.OTHER,
    "Store Room": LuxuryEstatePropertyTypeEnum.OTHER,
    "Studio": LuxuryEstatePropertyTypeEnum.STUDIO,
    "Supermarket": LuxuryEstatePropertyTypeEnum.OTHER,
    "Town House": LuxuryEstatePropertyTypeEnum.TOWNHOUSE,
    "Triplex": LuxuryEstatePropertyTypeEnum.OTHER,
    "Unique Building": LuxuryEstatePropertyTypeEnum.OTHER,
    "Villa": LuxuryEstatePropertyTypeEnum.VILLA,
}


class LuxuryEstateTransactionEnum(str, Enum):
    SALE = "sale"
    RENT = "rent"
    RENT_WEEK = "rent/week"
    RENT_MONTH = "rent/month"
    RENT_YEAR = "rent/year"
    RENT_QUARTER = "rent/quarter"
    RENT_DAILY = "rent/daily"
    RENT_BIWEEKLY = "rent/bi-weekly"
    RENT_SEASON = "rent/season"
    RENT_HALF_YEARLY = "rent/half-yearly"
    RENT_ONE_TIME = "rent/one-time"
    RENT_OTHER = "rent/other"


def map_listing_type_to_luxuryestate_price_property_type_and_transaction(
    *,
    property: Property,
) -> tuple[str, str, str]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]
    property_type = _property_type_mapper[property._property_type.name]

    if ListingTypeEnum.SALE in listing_types_names:
        return (
            f"{property.price_sale}",
            property_type,
            LuxuryEstateTransactionEnum.SALE,
        )
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        return (
            f"{property.price_rent_long_term}",
            property_type,
            LuxuryEstateTransactionEnum.RENT_MONTH,
        )
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        return (
            f"{property.price_rent_short_term}",
            property_type,
            LuxuryEstateTransactionEnum.RENT_MONTH,
        )
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


BALCONY_FEATURES = ["Balcony"]


def property_has_balcony(*, property: Property) -> bool:
    features = property.features

    has_balcony = next(
        (feature for feature in features if feature.name in BALCONY_FEATURES),
        None,
    )

    return True if has_balcony else False


TERRACE_FEATURES = [
    "Terrace",
    "Uncovered terrace",
    "Covered Terrace",
    "Private Terrace",
]


def property_has_terrace(*, property: Property) -> bool:
    features = property.features

    has_terrace = next(
        (feature for feature in features if feature.name in TERRACE_FEATURES),
        None,
    )

    return True if has_terrace else False


class LuxuryEstateCoolingTypeEnum(str, Enum):
    AIR_CONDITIONING = "Air Conditioning"
    CEILING_FAN = "Ceiling Fan"


ALL_COOLING_TYPE_FEATURES = [
    "Air Conditioning",
    "Individual A/C Units",
    "Ceiling Cooling System",
]
AIR_CONDITIONING_FEATURES = ["Air Conditioning", "Individual A/C Units"]
CEILING_FAN_FEATURES = ["Ceiling Cooling System"]


def property_has_cooling(*, property: Property) -> bool:
    features = property.features

    has_cooling = next(
        (feature for feature in features if feature.name in ALL_COOLING_TYPE_FEATURES),
        None,
    )

    return True if has_cooling else False


def get_cooling_types(*, property: Property) -> List[str]:
    cooling_types = []

    features = property.features
    has_air_conditioning = next(
        (feature for feature in features if feature.name in AIR_CONDITIONING_FEATURES),
        None,
    )

    has_ceiling_fan = next(
        (feature for feature in features if feature.name in CEILING_FAN_FEATURES), None
    )

    if has_air_conditioning:
        cooling_types.append(LuxuryEstateCoolingTypeEnum.AIR_CONDITIONING)
    if has_ceiling_fan:
        cooling_types.append(LuxuryEstateCoolingTypeEnum.CEILING_FAN)

    return cooling_types


class LuxuryEstateKitchenTypeEnum(str, Enum):
    KITCHENETTE = "Kitchenette"
    KITCHEN_DINER = "Kitchen Diner"
    COOKING_CORNER = "Cooking Corner"
    KITCHENNOOK = "Kitchennook"


ALL_KITCHEN_FEATURES = [
    "Fitted Kitchen",
    "Kitchenette",
    "Open Plan Kitchen",
    "Fully fitted kitchen",
    "Kitchen equipped",
]
KITCHENETTE_FEATURES = ["Kitchenette"]


def property_has_kitchen(*, property: Property) -> bool:
    features = property.features

    has_kitchen = next(
        (feature for feature in features if feature.name in ALL_KITCHEN_FEATURES),
        None,
    )

    return True if has_kitchen else False


def get_kitchen_types(*, property: Property) -> List[str]:
    kitchen_types = []

    features = property.features

    has_kitchenette = next(
        (feature for feature in features if feature.name in KITCHENETTE_FEATURES),
        None,
    )

    if has_kitchenette:
        kitchen_types.append(LuxuryEstateKitchenTypeEnum.KITCHENETTE)

    return kitchen_types


class LuxuryEstateViewTypeEnum(str, Enum):
    GREENBELT = "GreenBelt"
    SEA = "Sea"
    MOUNTAINS = "Mountains"
    CITY = "City"
    GOLFCLUB = "GolfClub"
    LAKE = "Lake"
    BAY = "Bay"
    RIVER = "River"


ALL_VIEWS_FEATURES = [
    "Sea View",
    "Country View",
    "Mountain View",
    "Golf View",
    "Street View",
    "Lake View",
    "Urban View",
    "Marina View",
    "Partial Sea Views",
]

GREENBELT_VIEW_FEATURES = ["Country View"]
SEA_VIEW_FEATURES = ["Sea View", "Partial Sea Views"]
MOUNTAIN_VIEW_FEATURES = ["Mountain View"]
CITY_VIEW_FEATURES = ["Street View", "Urban View"]
GOLFCLUB_VIEW_FEATURES = ["Golf View"]
LAKE_VIEW_FEATURES = ["Lake View"]
BAY_VIEW_FEATURES = ["Marina View"]


def property_has_views(*, property: Property) -> bool:
    features = property.features

    has_views = next(
        (feature for feature in features if feature.name in ALL_VIEWS_FEATURES),
        None,
    )

    return True if has_views else False


def get_view_types(*, property: Property) -> List[str]:
    view_types = []

    features = property.features

    has_greenbelt = next(
        (feature for feature in features if feature.name in GREENBELT_VIEW_FEATURES),
        None,
    )
    has_sea = next(
        (feature for feature in features if feature.name in SEA_VIEW_FEATURES),
        None,
    )
    has_mountains = next(
        (feature for feature in features if feature.name in MOUNTAIN_VIEW_FEATURES),
        None,
    )
    has_city = next(
        (feature for feature in features if feature.name in CITY_VIEW_FEATURES),
        None,
    )
    has_golfclub = next(
        (feature for feature in features if feature.name in GOLFCLUB_VIEW_FEATURES),
        None,
    )
    has_lake = next(
        (feature for feature in features if feature.name in LAKE_VIEW_FEATURES),
        None,
    )
    has_bay = next(
        (feature for feature in features if feature.name in BAY_VIEW_FEATURES),
        None,
    )

    if has_greenbelt:
        view_types.append(LuxuryEstateViewTypeEnum.GREENBELT)
    if has_sea:
        view_types.append(LuxuryEstateViewTypeEnum.SEA)
    if has_mountains:
        view_types.append(LuxuryEstateViewTypeEnum.MOUNTAINS)
    if has_city:
        view_types.append(LuxuryEstateViewTypeEnum.CITY)
    if has_golfclub:
        view_types.append(LuxuryEstateViewTypeEnum.GOLFCLUB)
    if has_lake:
        view_types.append(LuxuryEstateViewTypeEnum.LAKE)
    if has_bay:
        view_types.append(LuxuryEstateViewTypeEnum.BAY)

    return view_types


class LuxuryEstateBuildingStatusEnum(str, Enum):
    UNDER_CONSTRUCTION = "under construction"
    NEW = "new"
    HABITABLE = "habitable"
    EXCELLENT = "excellent"
    GOOD = "good"
    RENOVATED = "renovated"
    TO_BE_RENOVATED = "to be renovated"
    FAIR = "fair"
    ND = "nd"


_condition_building_status_mapper = {
    ConditionType.EXCELLENT: LuxuryEstateBuildingStatusEnum.EXCELLENT,
    ConditionType.GOOD: LuxuryEstateBuildingStatusEnum.GOOD,
    ConditionType.FAIR: LuxuryEstateBuildingStatusEnum.FAIR,
    ConditionType.RENOVATION_REQUIRED: LuxuryEstateBuildingStatusEnum.TO_BE_RENOVATED,
    ConditionType.RESTORATION_REQUIRED: LuxuryEstateBuildingStatusEnum.ND,
}


def map_condition_to_luxuryestate_buildingstatus(
    *, property: Property
) -> Optional[str]:
    condition = property.condition

    if not condition:
        return None

    return _condition_building_status_mapper[cast(ConditionType, condition)]


def property_has_descriptions(*, property: Property) -> bool:
    descriptions = property.descriptions

    descriptions = [desc.description for desc in property.descriptions]
    non_empty_descriptions = [desc for desc in descriptions if desc]
    if non_empty_descriptions:
        return True

    return False


def property_has_images(*, property: Property) -> bool:
    if not property.images:
        return False

    images = [image for image in property.images if not image.is_hidden]
    if not images:
        return False

    return True


def get_sorted_non_hidden_images(*, property: Property) -> List[Image]:
    non_hidden_images = [image for image in property.images if not image.is_hidden]
    non_hidden_images.sort(key=lambda x: x.order)
    return non_hidden_images
