import json
import time
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import datetime

import requests
from sqlalchemy import select

from strandproperties.config import app_cfg
from strandproperties.constants import FB_GRAPH_API_BASE_URL, Captures
from strandproperties.libs.lead_process import LeadProcess
from strandproperties.models.processing_history import LeadsProcessingHistory
from strandproperties.scripts.base import BaseScript

LEADS_FIELDS_SELECT = "id,retailer_item_id,campaign_name,field_data"


class FacebookCampaignLeadCapture(BaseScript):
    accounts = []
    all_leads = []
    current_date = datetime.now()
    last_run_time = None

    def call_facebook_api(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return {}

    def get_accounts(
        self,
        url=f"{FB_GRAPH_API_BASE_URL}/me/accounts?access_token={app_cfg.facebook_leads_access_code}",
    ):
        """
        get all account with default url to get first page of results
        if has next page call recursively

        :param url: get accounts url
        :return: list of accounts
        """
        result = self.call_facebook_api(url)
        if result:
            if "data" in result and len(result["data"]) > 0:
                self.accounts.extend(result["data"])

            if "paging" in result and "next" in result.get("paging"):
                url_after = result["paging"]["next"]
                self.get_accounts(url_after)

    def get_leadgen_forms(self, account=None, url=None):
        """
        get leadgen from account and specific url to get (for next page)
        if has next page call recursively

        :param account: account to get leadgen
        :param url: get leadgen url
        :return: list of leadgen
        """
        if not url:
            account_id = account["id"]
            request_url = f"{FB_GRAPH_API_BASE_URL}/{account_id}/leadgen_forms?access_token={account['access_token']}"
        else:
            request_url = url
        result = self.call_facebook_api(request_url)
        if result:
            if "data" in result:
                leadgen_forms = result.get("data")

                # call get lead with thread
                with ThreadPoolExecutor(max_workers=10) as executor:
                    future = [
                        executor.submit(self.get_leads, form["id"], None)
                        for form in leadgen_forms
                    ]
                    for future_ in as_completed(future):
                        future_.result()
            if "paging" in result and "next" in result.get("paging"):
                next_url = result["paging"]["next"]
                self.get_leadgen_forms(url=next_url)

    def get_leads(self, form_id=None, url=None):
        """
        get lead from leadgen and specific url to get (for next page)
        if has next page call recursively

        :param form_id: id of form to get leads
        :param url: get lead url
        :return: list of leads
        """
        if not url:

            # filter result from last previous run time to current
            filter_lead = [
                {
                    "field": "time_created",
                    "operator": "LESS_THAN",
                    "value": time.mktime(self.current_date.timetuple()),
                }
            ]
            if self.last_run_time:
                filter_lead.append(
                    {
                        "field": "time_created",
                        "operator": "GREATER_THAN",
                        "value": time.mktime(self.last_run_time.timetuple()),
                    }
                )

            filter_params = json.dumps(filter_lead)
            request_url = f"{FB_GRAPH_API_BASE_URL}/{form_id}/leads?access_token={app_cfg.facebook_leads_access_code}&filtering={filter_params}&fields={LEADS_FIELDS_SELECT}"
        else:
            request_url = url

        result = self.call_facebook_api(request_url)
        if result:
            if "data" in result:
                self.all_leads.extend(result["data"])
            if "paging" in result and "next" in result.get("paging"):
                next_url = result["paging"]["next"]
                self.get_leads(url=next_url)

    def extract_raw_lead_data(self, data: dict):
        """
        Extract the info lead data to process lead

        :param data: lead data
        :return: raw lead data
        """
        source_lead_id = data.get("id")
        retailer_item_id = data.get("retailer_item_id")
        campaign_name = data.get("campaign_name")

        fields = {}
        for field in data.get("field_data", []):
            field_value = field.get("values")

            if len(field_value) >= 1:
                fields[field["name"]] = field_value[0]
            else:
                fields[field["name"]] = ""

        email = fields.pop("email", None)
        name = fields.pop("full_name", None)
        phone = fields.pop("phone_number", None)

        content = json.dumps(fields)
        return (
            source_lead_id,
            retailer_item_id,
            email,
            name,
            phone,
            campaign_name,
            content,
        )

    def run(self):

        # get facebook campaign lead capture process history
        process_history = self.db_session.scalars(
            select(LeadsProcessingHistory).where(
                LeadsProcessingHistory.process_type == Captures.FACEBOOK_CAPTURE.value
            )
        ).one_or_none()

        # get last time run
        if process_history:
            self.last_run_time = process_history.last_time_run

        self.get_accounts()

        # get lead with multithread
        with ThreadPoolExecutor(max_workers=10) as executor2:
            future2 = [
                executor2.submit(self.get_leadgen_forms, account, None)
                for account in self.accounts
            ]
            for future2_ in as_completed(future2):
                future2_.result()

        # lead process
        for lead in self.all_leads:
            (
                source_lead_id,
                retailer_item_id,
                email,
                name,
                phone,
                campaign_name,
                content,
            ) = self.extract_raw_lead_data(data=lead)

            process = LeadProcess(
                db_session=self.db_session,
                id=source_lead_id,
                reference_code=retailer_item_id,
                email=email,
                name=name,
                phone=phone,
                content=content,
                source_name="Facebook",
                campaign_name=campaign_name,
            )
            process.execute(mode="process")

        # save current processing running history
        if process_history:
            process_history.last_time_run = self.current_date
        else:
            self.db_session.add(
                LeadsProcessingHistory(
                    process_type=Captures.FACEBOOK_CAPTURE.value,
                    last_time_run=self.current_date,
                )
            )
        self.db_session.commit()
