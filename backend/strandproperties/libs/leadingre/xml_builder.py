import copy
import xml.dom.minidom
import xml.etree.ElementTree as ET

from strandproperties.config import app_cfg
from strandproperties.constants import (
    Currency,
    DescriptionType,
    Language,
    ListingTypeEnum,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.leadingre.mapper import (
    convert_square_meter_to_square_feet,
    get_agent_info,
    get_location_info,
    map_listing_type_to_leadingre_price_and_property_type,
)
from strandproperties.libs.portals_common import (
    PortalNames,
    PortalsCommon,
    PortalsCommonProps,
)
from strandproperties.libs.utils import (
    get_strandproperties_property_slug,
    remove_some_ASCII_control_characters,
)
from strandproperties.logger import logger
from strandproperties.models.property import Property
from strandproperties.scripts.base import BaseScript


class LeadingREXMLBuilder(BaseScript):
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element("listings", attrib={"xmlns": "listings-schema"})

        self.root = root
        self.encoding = encoding

    def set_property_tags(self) -> dict[str, ET.Element]:
        property_tag: dict[str, ET.Element] = {}
        ### All mandatory
        property_tag["property"] = ET.SubElement(self.root, "property")
        property_tag["street_address"] = ET.SubElement(
            property_tag["property"], "street_address"
        )  # Max 100 letters
        property_tag["city"] = ET.SubElement(
            property_tag["property"], "city"
        )  # Max 100 letters
        property_tag["country_code"] = ET.SubElement(
            property_tag["property"], "country_code"
        )  # Max 3 letters - Spain is ESP
        property_tag["apartment_number"] = ET.SubElement(
            property_tag["property"], "apartment_number"
        )  # Max 10 characters
        property_tag["price"] = ET.SubElement(property_tag["property"], "price")
        property_tag["currency_code"] = ET.SubElement(
            property_tag["property"], "currency_code"
        )
        property_tag["property_type"] = ET.SubElement(
            property_tag["property"], "property_type"
        )  # Max 15 characters - Options: Single Family, Multi Family, Condo, Land, Rental
        property_tag["qty_bedrooms"] = ET.SubElement(
            property_tag["property"], "qty_bedrooms"
        )  # Max 5 characters
        property_tag["qty_full_bathrooms"] = ET.SubElement(
            property_tag["property"], "qty_full_bathrooms"
        )  # Max 5 characters
        property_tag["qty_half_bathrooms"] = ET.SubElement(
            property_tag["property"], "qty_half_bathrooms"
        )  # Max 5 characters
        property_tag["description"] = ET.SubElement(
            property_tag["property"], "description"
        )  # Max 1000 characters
        property_tag["pictures"] = ET.SubElement(
            property_tag["property"], "pictures"
        )  # Sub node of one or more property photos

        # INFO: according to Drew McCall from LeadingRE, if a virtual tour is not present
        # it is best just to leave it null or remove from the feed.
        property_tag["virtual_tour"] = ET.SubElement(
            property_tag["property"], "virtual_tour"
        )  # Max 512 characters - URL with query string parameters needed to retrieve and display the property listing virtual tour for the property.

        property_tag["mls_id"] = ET.SubElement(
            property_tag["property"], "mls_id"
        )  # Max 20 characters - Not sure, seems like some association that you need to be member. It is mostly used in the USA
        property_tag["status"] = ET.SubElement(
            property_tag["property"], "status"
        )  # Max 50 characters - MLS Listing Status: Available, Active, Under Contract, Closed, Off-Market
        property_tag["picture_url"] = ET.SubElement(
            property_tag["property"], "picture_url"
        )  # Max 200 characters. URL of primary photo property image file.
        property_tag["agent_name"] = ET.SubElement(
            property_tag["property"], "agent_name"
        )  # Max 50 characters. Listing's agent first and last name
        property_tag["agent_email"] = ET.SubElement(
            property_tag["property"], "agent_email"
        )  # Max 100 characters. Listing agent’s primary email address
        property_tag["agent_phone"] = ET.SubElement(
            property_tag["property"], "agent_phone"
        )  # Max 50 characters. Listing agent’s primary telephone number
        property_tag["agent_id"] = ET.SubElement(
            property_tag["property"], "agent_id"
        )  # Max 50 characters. Listing agent number or identifier from MLS
        property_tag["office_mls_id"] = ET.SubElement(
            property_tag["property"], "office_mls_id"
        )  # Max 50 characters. Listing agent’s primary office mls identifier
        property_tag["office_name"] = ET.SubElement(
            property_tag["property"], "office_name"
        )  # Max 100 characters. Listing agent’s primary office name

        return property_tag

    def fill_property_tags(
        self, original_property_tag: dict[str, ET.Element], property: Property
    ):
        property_tag = copy.copy(original_property_tag)

        property_images = [image for image in property.images if not image.is_hidden]
        property_images.sort(key=lambda x: x.order)  # type: ignore

        feature_names: str = ", ".join([feature.name for feature in property.features])

        description_priority = {
            DescriptionType.FULL.value: 1,
            DescriptionType.EXTRA.value: 999,
        }
        english_description = next(
            (
                description
                for description in sorted(
                    property.descriptions,
                    key=lambda d: description_priority.get(d.type, 0),
                )
                if description.language == Language.ENGLISH
            )
        )
        description = remove_some_ASCII_control_characters(
            english_description.description
        )

        location = get_location_info(property=property)
        property_tag["street_address"].text = location.address
        property_tag["city"].text = location.city
        property_tag["country_code"].text = location.country
        if location.number:
            property_tag["apartment_number"].text = location.number

        (price, property_type) = map_listing_type_to_leadingre_price_and_property_type(
            property=property
        )
        property_tag["price"].text = price[:8]
        property_tag["currency_code"].text = Currency.EUR
        property_tag["property_type"].text = property_type

        if property.bedrooms is not None:
            property_tag["qty_bedrooms"].text = f"{property.bedrooms}"
        if property.bathrooms is not None:
            property_tag["qty_full_bathrooms"].text = f"{property.bathrooms}"
        property_tag["description"].text = f"<![CDATA[ {description[:1000]} ]]>"

        for index, image in enumerate(property_images):
            picture_tag = ET.SubElement(property_tag["pictures"], "picture")
            ET.SubElement(picture_tag, "url").text = image.url
            ET.SubElement(picture_tag, "picture_caption").text = "No caption provided"
            ET.SubElement(picture_tag, "display_order").text = (
                f"{(image.order + 1) if image.order else (index + 1)}"
            )

        for v in property.video_streams + property.video_tours:
            if not v.is_hidden:
                property_tag["virtual_tour"].text = v.url
                break

        external_url_to_property = get_strandproperties_property_slug(
            reference=property.reference, portals=property.portals
        )
        if external_url_to_property:
            ET.SubElement(property_tag["property"], "member_page_url").text = (
                external_url_to_property
            )

        # INFO: According to Drew McCall from LeadingRE, they need this for their website,
        # but can be populated with our reference number
        property_tag["mls_id"].text = property.reference
        # INFO: Listing agent’s primary office mls identifier
        property_tag["office_mls_id"].text = f"strandproperties_{property.reference}"

        property_tag["status"].text = "Available"
        property_tag["picture_url"].text = property.main_img

        agent_info = get_agent_info(property=property)
        property_tag["agent_name"].text = agent_info.agent_name
        property_tag["agent_email"].text = agent_info.agent_email
        property_tag["agent_phone"].text = agent_info.agent_phone
        property_tag["agent_id"].text = agent_info.agent_id
        property_tag["office_name"].text = agent_info.office_name

        ### Optional tags

        if location.postcode:
            # Max 15 letters - only required for USA feed
            property_tag["zip_code"] = ET.SubElement(
                property_tag["property"], "zip_code"
            )
            property_tag["zip_code"].text = location.postcode[:15]

        if property.latitude:
            property_tag["latitude"] = ET.SubElement(
                property_tag["property"], "latitude"
            )
            property_tag["latitude"].text = f"{property.latitude}"

        if property.longitude:
            property_tag["longitude"] = ET.SubElement(
                property_tag["property"], "longitude"
            )
            property_tag["longitude"].text = f"{property.longitude}"

        if property.built_area:
            property_tag["square_feet"] = ET.SubElement(
                property_tag["property"], "square_feet"
            )
            square_feet = convert_square_meter_to_square_feet(
                square_meter=property.built_area
            )
            property_tag["square_feet"].text = f"{square_feet}"

        if len(feature_names) > 0:
            property_tag["features"] = ET.SubElement(
                property_tag["property"], "features"
            )  # Max 2000 characters. Example: irrigation system, dog run, golf property, fully fenced, deck

            property_tag["features"].text = feature_names[:2000]

        return property_tag

    def write_to_file(self, file_location: str):
        # parse it
        dom = xml.dom.minidom.parseString(ET.tostring(self.root))
        xml_string = dom.toprettyxml()
        xml_string = (
            xml_string.replace("__amp__", "&").replace("&lt;", "<").replace("&gt;", ">")
        )

        part1, part2 = xml_string.split("?>")

        with open(file_location, "w") as xfile:
            xfile.write(part1 + 'encoding="{}"?>\n'.format(self.encoding))
            xfile.write(part2)
            xfile.close()

    def upload_to_s3(self, file_location: str, file_name: str):
        s3 = S3Service(app_cfg, app_cfg.aws_s3_integration_bucket_name)
        with open(file_location, "rb") as file:
            s3.upload(file_name, file, "binary/octet-stream")

    def _validate_fields(self, property: Property, request_id: str) -> bool:
        ### Check all required fields first
        # images are REQUIRED
        if not property.images:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: images are required",
                extra={"requestId": request_id},
            )
            return False
        images = [image for image in property.images if not image.is_hidden]
        if not images:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: non-hidden images are required",
                extra={"requestId": request_id},
            )
            return False

        # area_level_1 (country, province) is REQUIRED
        if not property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # we need info from location which is REQUIRED
        if not (property.private_info and "location" in property.private_info):  # type: ignore
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: private_info.location is required",
                extra={"requestId": request_id},
            )
            return False

        location = property.private_info["location"]
        if not location.get("address"):
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: private_info.location.address is required",
                extra={"requestId": request_id},
            )
            return False

        # description is REQUIRED
        non_empty_english_descriptions = [
            desc.description
            for desc in property.descriptions
            if desc.description and desc.language == Language.ENGLISH
        ]
        if not non_empty_english_descriptions:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: description is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][LeadingRE]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][LeadingRE]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][LeadingRE]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        # Bedrooms and bathrooms are REQUIRED
        # if not property.bedrooms or not property.bathrooms:
        #     logger.error(
        #         f"> Skipping property [{property.reference}][LeadingRE]: bedrooms and bathrooms are required",
        #         extra={"requestId": request_id},
        #     )
        #     return False

        # Agent is REQUIRED
        if not property.realtor_users:
            logger.info(
                f"> Skipping property [{property.reference}][LeadingRE]: realtor_users is required",
                extra={"requestId": request_id},
            )
            return False

        return True

    def run(self):
        props = PortalsCommonProps(
            file_location_base="strandproperties/libs/leadingre",
            portal=PortalNames.LEADINGRE,
            set_headers=self.set_headers,
            validate_fields=self._validate_fields,
            set_property_tags=self.set_property_tags,
            fill_property_tags=self.fill_property_tags,
            write_to_file=self.write_to_file,
            upload_to_s3=self.upload_to_s3,
        )
        PortalsCommon().execute(db_session=self.db_session, props=props)
