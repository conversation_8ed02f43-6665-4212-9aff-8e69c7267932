from enum import Enum
from typing import cast

from pydantic import BaseModel

from strandproperties.constants import ListingTypeEnum
from strandproperties.models.property import Property


class LocationInfo(BaseModel):
    address: str
    postcode: str | None
    city: str
    country: str
    number: str | None


def get_location_info(*, property: Property) -> LocationInfo:
    private_info = property.private_info

    location = private_info["location"]

    location_address: str = location.get("address")
    location_postcode = location.get("postCode")  # it is an optional field
    location_city = property._area_level_1.name
    location_country = cast(str, property.country) or "ESP"

    # TODO: change once we have the concept of numbering for a location
    location_number = None

    # INFO: this listing is not exhaustive.
    if location_country == "Spain":
        location_country = "ESP"
    elif location_country == "Finland":
        location_country = "FIN"

    return LocationInfo(
        address=f"<![CDATA[ {location_address} ]]>",
        postcode=location_postcode,
        city=location_city,
        country=location_country,
        number=location_number,
    )


class LeadingREPropertyTypeEnum(str, Enum):
    SINGLE_FAMILY = "Single Family"
    MULTI_FAMILY = "Multi Family"
    CONDO = "Condo"
    LAND = "Land"
    RENTAL = "Rental"


"""
Maps from Property Type (Strand) to LeadingRE property types:
"""
_property_type_mapper = {
    "Apartment": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Bar": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Boat": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Building": LeadingREPropertyTypeEnum.MULTI_FAMILY,
    "Bungalow": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Business": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Castle": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Chalet": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Commercial Other": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Commercial Premises": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Cortijo": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Country House": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Development Land": LeadingREPropertyTypeEnum.LAND,
    "Discotheque": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Duplex": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Duplex Penthouse": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Estate": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Finca": LeadingREPropertyTypeEnum.LAND,
    "Flat": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Golf Course": LeadingREPropertyTypeEnum.LAND,
    "Golf Plot": LeadingREPropertyTypeEnum.LAND,
    "Ground Floor Apartment": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Ground Floor Duplex": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Hotel": LeadingREPropertyTypeEnum.MULTI_FAMILY,
    "Hotel Plot": LeadingREPropertyTypeEnum.LAND,
    "House": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Industrial Land": LeadingREPropertyTypeEnum.LAND,
    "Industrial Premises": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Investment": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Loft": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Mansion": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Mooring": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Office": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Office Units": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Palace": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Parking": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Penthouse": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Plot": LeadingREPropertyTypeEnum.LAND,
    "Residential Plot": LeadingREPropertyTypeEnum.LAND,
    "Restaurant": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Riad": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Rustic Plot": LeadingREPropertyTypeEnum.LAND,
    "Semi Detached House": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Semi Detached Villa": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Shop": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Shopping Centre": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Store Room": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Studio": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Supermarket": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Town House": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Triplex": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
    "Unique Building": LeadingREPropertyTypeEnum.MULTI_FAMILY,
    "Villa": LeadingREPropertyTypeEnum.SINGLE_FAMILY,
}


def map_listing_type_to_leadingre_price_and_property_type(
    *,
    property: Property,
) -> tuple[str, str]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]
    property_type = _property_type_mapper[property._property_type.name]

    if ListingTypeEnum.SALE in listing_types_names:
        return (f"{property.price_sale}", property_type)
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        return (f"{property.price_rent_long_term}", LeadingREPropertyTypeEnum.RENTAL)
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        return (f"{property.price_rent_short_term}", LeadingREPropertyTypeEnum.RENTAL)
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


# Square Meter is equal to 10.76391042 Square Feet
def convert_square_meter_to_square_feet(*, square_meter: int) -> int:
    return int(square_meter * 10.76391042)


class AgentInfo(BaseModel):
    agent_name: str
    agent_email: str
    agent_phone: str
    agent_id: str
    office_name: str


def get_agent_info(*, property: Property) -> AgentInfo:
    realtors = property.realtor_users
    agent = realtors[0]
    return AgentInfo(
        agent_name=f"{agent.first_name} {agent.last_name}",
        agent_email="<EMAIL>",
        agent_phone="+34676901519",
        agent_id=f"STRAND_{agent.id}",
        office_name="Strand Spain",  # TODO: change this when we have the office information in the User
    )
