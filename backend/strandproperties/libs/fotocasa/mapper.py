from enum import Enum
from typing import List, cast

from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.logger import logger
from strandproperties.models.image import Image
from strandproperties.models.property import Property
from strandproperties.models.video import Stream, Tour

"""
Maps from Property Type (Strand) to TypeId and SubTypeId (Fotocasa):
  - if it is "New Development" => True; otherwise, False
"""

FOTOCASA_FEATURE_ID_SURFACE_AREA = 1
FOTOCASA_TYPE_ID_LAND = 6  # Type ID for "Land" in Fotocasa

_property_type_mapper = {
    "Apartment": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 10,  # Fotocasa SubType: Apartment
    },
    "Bar": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 37,  # Fotocasa SubType: Store
    },
    "Boat": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Building": {
        "TypeId": 5,  # Fotocasa Type: Building
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Bungalow": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 27,  # Fotocasa SubType: Bungalow
    },
    "Business": {
        "TypeId": 4,  # Fotocasa Type: Office
        "SubTypeId": 44,  # Fotocasa SubType: Other
    },
    "Castle": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 28,  # Fotocasa SubType: Mansion
    },
    "Chalet": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 20,  # Fotocasa SubType: Chalet/Tower
    },
    "Commercial Other": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Commercial Premises": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 37,  # Fotocasa SubType: Store
    },
    "Cortijo": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 24,  # Fotocasa SubType: Rustic house
    },
    "Country House": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 24,  # Fotocasa SubType: Rustic house
    },
    "Development Land": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 54,  # Fotocasa SubType: Urbanizable
    },
    "Discotheque": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Duplex": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 3,  # Fotocasa SubType: Duplex
    },
    "Duplex Penthouse": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 5,  # Fotocasa SubType: Penthouse
    },
    "Estate": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 28,  # Fotocasa SubType: Mansion
    },
    "Finca": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 59,  # Fotocasa SubType: Agricultural
    },
    "Flat": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 10,  # Fotocasa SubType: Apartment
    },
    "Golf Course": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 56,  # Fotocasa SubType: Residential
    },
    "Golf Plot": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 56,  # Fotocasa SubType: Residential
    },
    "Ground Floor Apartment": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 10,  # Fotocasa SubType: Apartment
    },
    "Ground Floor Duplex": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 3,  # Fotocasa SubType: Duplex
    },
    "Hotel": {
        "TypeId": 10,  # Fotocasa Type: Hotel
        "SubTypeId": 75,  # Fotocasa SubType: Hotel
    },
    "Hotel Plot": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 56,  # Fotocasa SubType: Residential
    },
    "House": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 13,  # Fotocasa SubType: House
    },
    "Industrial Land": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 60,  # Fotocasa SubType: Industrial
    },
    "Industrial Premises": {
        "TypeId": 7,  # Fotocasa Type: Industrial
        "SubTypeId": 64,  # Fotocasa SubType: Industrial unit
    },
    "Investment": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Loft": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 7,  # Fotocasa SubType: Loft
    },
    "Mansion": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 28,  # Fotocasa SubType: Mansion
    },
    "Mooring": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 53,  # Fotocasa SubType: Others
    },
    "Office": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 88,  # Fotocasa SubType: Office
    },
    "Office Units": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Palace": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 28,  # Fotocasa SubType: Mansion
    },
    "Parking": {
        "TypeId": 8,  # Fotocasa Type: Parking
        "SubTypeId": 70,  # Fotocasa SubType: Individual
    },
    "Penthouse": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 5,  # Fotocasa SubType: Penthouse
    },
    "Plot": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 56,  # Fotocasa SubType: Residential
    },
    "Residential Plot": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 56,  # Fotocasa SubType: Residential
    },
    "Restaurant": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Riad": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 24,  # Fotocasa SubType: Rustic house
    },
    "Rustic Plot": {
        "TypeId": 6,  # Fotocasa Type: Land
        "SubTypeId": 54,  # Fotocasa SubType: Rustic house
    },
    "Semi Detached House": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 17,  # Fotocasa SubType: Semi-detached
    },
    "Semi Detached Villa": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 17,  # Fotocasa SubType: Semi-detached
    },
    "Shop": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Shopping Centre": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 43,  # Fotocasa SubType: In shopping center
    },
    "Store Room": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 38,  # Fotocasa SubType: Basement
    },
    "Studio": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 6,  # Fotocasa SubType: Study
    },
    "Supermarket": {
        "TypeId": 3,  # Fotocasa Type: Local
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Town House": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 17,  # Fotocasa SubType: Town house
    },
    "Triplex": {
        "TypeId": 1,  # Fotocasa Type: Floor
        "SubTypeId": 2,  # Fotocasa SubType: Triplex
    },
    "Unique Building": {
        "TypeId": 5,  # Fotocasa Type: Building
        "SubTypeId": 8,  # Fotocasa SubType: Other
    },
    "Villa": {
        "TypeId": 2,  # Fotocasa Type: Home
        "SubTypeId": 20,  # Fotocasa SubType: Chalet/Tower
    },
}


class PropertyDocumentTypeEnum(Enum):
    PHOTO = 1
    QUALITY_MEMORIES = 2
    WRITING = 3
    WORK_LICENSE = 4
    DOSSIER = 5
    OTHERS = 6
    VIRTUAL_TOUR = 7
    VIDEO = 8
    MICROSITE = 9
    OTHER_LINK = 10
    AERIAL_VIEW = 11
    LOGO_TEMPLATES = 12
    SIGNATURE = 13
    DNI_CIF = 15
    CONTRACT = 16
    GENERAL_DOCUMENTATION = 17
    FLAT = 23
    REPORT = 25
    EXTERNAL_VIDEO_LINK = 31
    LINK_VIRTUAL_TOUR = 32
    CADASTRE_DOCUMENT = 45
    ENERGY_CERTIFICATE = 47
    PERSONALIZED_VISITING_SHEET = 48


class PropertyDocumentFileTypeEnum(Enum):
    GIF_DOCUMENT = 1
    JPG_DOCUMENT = 2
    WORD_DOCUMENT = 3
    WORD_TEMPLATE = 4
    EXCEL_TEMPLATE = 5
    HTML_TEMPLATE = 6
    QT_MOVIE_DOCUMENT = 7
    HTML_DOCUMENT = 8
    RTF_DOCUMENT = 9
    PDF_DOCUMENT = 10
    SIMPLE_TEXT_DOCUMENT = 11
    EXCEL_DOCUMENT = 12
    EMAIL_TEMPLATE_HTML = 13
    POWER_POINT_DOCUMENT = 14
    SELF_ATTACHABLE_TEMPLATES_HTML = 15
    COMMUNICATION_TEMPLATES = 16
    LINK = 17
    WORD_2007_DOCUMENT = 19
    EXCEL_2007_DOCUMENT = 20
    POWER_POINT_DOCUMENT_2007 = 21
    BMP_DOCUMENT = 22
    TIF_DOCUMENT = 23
    DWG_DOCUMENT = 24
    MPEG_DOCUMENT = 25
    AVI_DOCUMENT = 26
    WMA_DOCUMENT = 27
    FLV_DOCUMENT = 28
    MPG_DOCUMENT = 29
    DIVX_DOCUMENT = 31
    HTML_DOCUMENT_2 = 32
    WMV_DOCUMENT = 33
    JPEG_DOCUMENT = 34
    PNG_DOCUMENT = 35
    MP4_DOCUMENT = 36


"""
Maps from file extension to FileTypeId (Fotocasa):
"""
_image_extension_mapper = {
    "jpg": PropertyDocumentFileTypeEnum.JPG_DOCUMENT.value,
    "jpeg": PropertyDocumentFileTypeEnum.JPEG_DOCUMENT.value,
    "png": PropertyDocumentFileTypeEnum.PNG_DOCUMENT.value,
}

"""
Maps from energy certificate rating to Consumption efficiency scale (Fotocasa):
"""
_certificate_rating_mapper = {
    "A": 1,
    "B": 2,
    "C": 3,
    "D": 4,
    "E": 5,
    "F": 6,
    "G": 7,
}


def is_new_construction(property: Property) -> bool:
    return property.property_type_origin == "New development"


def map_property_type(property_type: str):
    return _property_type_mapper[property_type]["TypeId"]


def map_property_subtype(property_type: str):
    return _property_type_mapper[property_type]["SubTypeId"]


def get_location_from_private_info(property: Property):
    return property.private_info.get("location")


def get_file_type_from_url(url: str) -> int:
    parts = url.split("/")
    file_name = parts[-1]
    file_name_parts = file_name.split(".")
    extension = file_name_parts[-1]

    return _image_extension_mapper.get(extension, _image_extension_mapper["jpg"])


BALCONY_FEATURES = ["Balcony"]
TERRACE_FEATURES = [
    "Terrace",
    "Uncovered terrace",
    "Covered Terrace",
    "Private Terrace",
]


class PropertyContactTypeEnum(Enum):
    AGENCY = 1
    AGENT = 2
    SPECIFIED = 3


class PropertyContactInfoTypeEnum(Enum):
    EMAIL = 1
    PHONE = 2
    MOBILE = 3
    FAX = 4
    WEB = 5
    AIM_AOL = 6
    YAHOO = 7
    MESSENGER = 8
    WINDOWS_LIVE_MESSENGER = 9
    ICQ = 10
    JABBER_GOOGLE_TALK = 11
    SKYPE = 12
    FACEBOOK = 13
    TWITTER = 14
    SLIDESHARE = 15
    BLOG = 16
    OTHERS = 17


class PropertyContactInfoValueTypeEnum(Enum):
    MY_AGENCY_DETAILS = 1
    REAL_ESTATE_AGENT = 2
    OTHER_DATA = 3


def property_has_features(*, property: Property, features: List[str]) -> bool:
    has_feature = next(
        (feature for feature in property.features if feature.name in features),
        None,
    )

    return True if has_feature else False


def add_features_data(json: dict, property: Property):
    if property_has_features(property=property, features=TERRACE_FEATURES):
        json["PropertyFeature"].append(
            {
                "FeatureId": 27,
                "LanguageId": 4,
                "BoolValue": True,
            },
        )

    if property_has_features(property=property, features=BALCONY_FEATURES):
        json["PropertyFeature"].append(
            {
                "FeatureId": 297,
                "LanguageId": 4,
                "BoolValue": True,
            },
        )

    if property.bedrooms is not None:
        json["PropertyFeature"].append(
            {
                "FeatureId": 11,
                "LanguageId": 4,
                "DecimalValue": property.bedrooms,
            },
        )
    if property.bathrooms is not None:
        json["PropertyFeature"].append(
            {
                "FeatureId": 12,
                "LanguageId": 4,
                "DecimalValue": property.bathrooms,
            },
        )


def add_descriptions_data(json: dict, property: Property):
    for description in property.descriptions:
        if (
            description.language == Language.ENGLISH
            and description.type == DescriptionType.FULL
        ):
            json["PropertyFeature"].append(
                {
                    "FeatureId": 3,
                    "LanguageId": 2,
                    "TextValue": description.description,
                },
            )

            continue

        if (
            description.language == Language.SPANISH
            and description.type == DescriptionType.FULL
        ):
            json["PropertyFeature"].append(
                {
                    "FeatureId": 3,
                    "LanguageId": 4,
                    "TextValue": description.description,
                },
            )

        if (
            description.language == Language.GERMAN
            and description.type == DescriptionType.FULL
        ):
            json["PropertyFeature"].append(
                {
                    "FeatureId": 3,
                    "LanguageId": 6,
                    "TextValue": description.description,
                },
            )

            continue


def add_energy_certificate_data(json: dict, property: Property):
    property_has_valid_energy_consumption_fields = (
        property.property_has_certificate
        and property.certificate_consumption_rating is not None
        and property.certificate_consumption_value is not None
        and property.certificate_consumption_rating in _certificate_rating_mapper
    )

    property_has_valid_energy_emission_fields = (
        property.property_has_certificate
        and property.certificate_emission_rating is not None
        and property.certificate_emission_value is not None
        and property.certificate_emission_rating in _certificate_rating_mapper
    )

    if (
        property_has_valid_energy_consumption_fields
        and property_has_valid_energy_emission_fields
    ):
        json["PropertyFeature"].append(
            {
                "FeatureId": 323,
                "LanguageId": 4,
                "DecimalValue": _certificate_rating_mapper.get(
                    cast(str, property.certificate_consumption_rating), 1
                ),
            },
        )
        json["PropertyFeature"].append(
            {
                "FeatureId": 325,
                "LanguageId": 4,
                "DecimalValue": property.certificate_consumption_value,
            },
        )
        json["PropertyFeature"].append(
            {
                "FeatureId": 324,
                "LanguageId": 4,
                "DecimalValue": _certificate_rating_mapper.get(
                    cast(str, property.certificate_emission_rating), 1
                ),
            },
        )
        json["PropertyFeature"].append(
            {
                "FeatureId": 326,
                "LanguageId": 4,
                "DecimalValue": property.certificate_emission_value,
            },
        )
        json["PropertyFeature"].append(
            {
                "FeatureId": 327,  # Energy Certificate
                "LanguageId": 4,
                "DecimalValue": 1,  # Yes
            },
        )
    else:
        json["PropertyFeature"].append(
            {
                "FeatureId": 327,  # Energy Certificate
                "LanguageId": 4,
                "DecimalValue": 2,  # In Process
            },
        )


def map_listing_type_to_fotocasa_transactiontype_price_and_pricesquaremeter(
    *,
    property: Property,
) -> tuple[int, int, float]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]

    transaction_type = 0
    price: int = 0
    price_square_meter = 0.00
    type_id = map_property_type(cast(str, property.property_type))

    if ListingTypeEnum.SALE in listing_types_names:
        price = cast(int, property.price_sale)
        transaction_type = 1
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        price = cast(int, property.price_rent_long_term)
        transaction_type = 3
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        price = cast(int, property.price_rent_short_term)
        transaction_type = 3
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )

    surface_area = (
        property.plot_area if type_id == FOTOCASA_TYPE_ID_LAND else property.built_area
    )
    price_square_meter = round(cast(int, price) / cast(int, surface_area), 2)

    return (transaction_type, price, price_square_meter)


def _validate_fields(property: Property, request_id: str) -> bool:
    # Latitude and longitude
    if not (property.latitude and property.longitude):
        logger.info(
            f"[{property.reference}] Latitude and Longitude are required for Fotocasa",
            extra={"requestId": request_id},
        )
        return False

    # Built area
    type_id = map_property_type(cast(str, property.property_type))
    is_plot = type_id == FOTOCASA_TYPE_ID_LAND

    if is_plot and property.plot_area is None:
        logger.info(
            f"[{property.reference}] Plot area is required for Fotocasa for plot type",
            extra={"requestId": request_id},
        )
        return False

    if not is_plot and property.built_area is None:
        logger.info(
            f"[{property.reference}] Built area is required for Fotocasa for non-plot type",
            extra={"requestId": request_id},
        )
        return False

    # Check price based on listing type
    listing_types = property.listing_types
    if not listing_types:
        logger.info(
            f"[{property.reference}] does not have listing type",
            extra={"requestId": request_id},
        )
        return False
    if ListingTypeEnum.SALE in listing_types:
        if not property.price_sale:
            logger.info(
                f"[{property.reference}] is for sale but has not price sale",
                extra={"requestId": request_id},
            )
            return False
    elif ListingTypeEnum.RENT_SHORT in listing_types:
        if not property.price_rent_short_term:
            logger.info(
                f"[{property.reference}] is for rent short term but has not price short term",
                extra={"requestId": request_id},
            )
            return False
    elif ListingTypeEnum.RENT_LONG in listing_types:
        if not property.price_rent_long_term:
            logger.info(
                f"[{property.reference}] is for rent long term but has not price long term",
                extra={"requestId": request_id},
            )
            return False

    return True


def get_property_document_images(images: list[Image]):
    return [
        {
            "TypeId": PropertyDocumentTypeEnum.PHOTO.value,
            "Url": f"{image.url}?width=2000",
            "FileTypeId": get_file_type_from_url(image.url),
            "Visible": True,
            "SortingId": (image.order + 1) if image.order else (idx + 1),
        }
        for idx, image in enumerate(images)
    ]


def get_property_document_streams(streams: list[Stream]):
    return [
        {
            "TypeId": PropertyDocumentTypeEnum.EXTERNAL_VIDEO_LINK.value,
            "Url": video.url,
            "FileTypeId": PropertyDocumentFileTypeEnum.LINK.value,
            "Visible": not video.is_hidden,
            "SortingId": idx + 1,
        }
        for idx, video in enumerate(streams)
    ]


def get_property_document_tours(tours: list[Tour]):
    return [
        {
            "TypeId": PropertyDocumentTypeEnum.LINK_VIRTUAL_TOUR.value,
            "Url": tour.url,
            "FileTypeId": PropertyDocumentFileTypeEnum.LINK.value,
            "Visible": not tour.is_hidden,
            "SortingId": idx + 1,
        }
        for idx, tour in enumerate(tours)
    ]


def map_property_to_fotocasa_property(property: Property, request_id: str):
    valid = _validate_fields(property=property, request_id=request_id)
    if not valid:
        raise

    (transaction_type, price, price_m2) = (
        map_listing_type_to_fotocasa_transactiontype_price_and_pricesquaremeter(
            property=property
        )
    )

    # Get sorted non hidden images
    images = [x for x in property.images if not x.is_hidden]
    images.sort(key=lambda x: x.order)  # type: ignore

    # Get non hidden videos
    streams = [x for x in property.video_streams if not x.is_hidden]
    tours = [x for x in property.video_tours if not x.is_hidden]

    # property contact info
    property_contact_info = [
        {
            "TypeId": PropertyContactInfoTypeEnum.EMAIL.value,
            "Value": "<EMAIL>",
            "ValueTypeId": PropertyContactInfoValueTypeEnum.MY_AGENCY_DETAILS.value,
        },
        {
            "TypeId": PropertyContactInfoTypeEnum.MOBILE.value,
            "Value": "0034676901519",
            "ValueTypeId": PropertyContactInfoValueTypeEnum.MY_AGENCY_DETAILS.value,
        },
    ]

    images_as_documents = get_property_document_images(images)
    videos_as_documents = get_property_document_streams(streams)
    tours_as_documents = get_property_document_tours(tours)

    property_documents = images_as_documents + videos_as_documents + tours_as_documents
    type_id = map_property_type(cast(str, property.property_type))
    surface_area = (
        property.plot_area if type_id == FOTOCASA_TYPE_ID_LAND else property.built_area
    )

    mapped_json = {
        "ExternalId": property.reference,
        "AgencyReference": property.reference,
        "TypeId": type_id,
        "SubTypeId": map_property_subtype(cast(str, property.property_type)),
        "PropertyStatusId": 1,
        "ShowSurface": True,
        "ContactTypeId": PropertyContactTypeEnum.SPECIFIED.value,
        "ContactName": "Strand Properties",
        "IsPromotion": False,
        "IsNewConstruction": is_new_construction(property),
        "PropertyAddress": [
            {
                "CountryId": 724,
                "StreetTypeId": 1,
                "VisibilityModeId": 3,
                "Street": get_location_from_private_info(property).get("address"),
                "x": property.longitude,
                "y": property.latitude,
                "ZipCode": get_location_from_private_info(property).get("postCode"),
            }
        ],
        "PropertyFeature": [
            {
                "FeatureId": FOTOCASA_FEATURE_ID_SURFACE_AREA,
                "DecimalValue": surface_area,
                "LanguageId": 2,
            },  # featureId = 1 -> surface area
        ],
        "PropertyContactInfo": property_contact_info,
        "PropertyTransaction": [
            {
                "TransactionTypeId": transaction_type,
                "Price": price,
                "PriceM2": price_m2,
                "CurrencyId": 1,
                "ShowPrice": True,
            }
        ],
        "PropertyPublications": [{"PublicationId": 1, "PublicationTypeId": 2}],
        "PropertyDocument": property_documents,
    }

    add_features_data(mapped_json, property)
    add_descriptions_data(mapped_json, property)
    add_energy_certificate_data(mapped_json, property)

    return mapped_json
