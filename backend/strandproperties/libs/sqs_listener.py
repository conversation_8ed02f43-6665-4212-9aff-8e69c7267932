#!/usr/bin/env python

import json
from datetime import datetime
from email.mime.text import MIMEText
from enum import StrEnum
from smtplib import SMTP
from string import Template
from typing import List

from boto3.session import Session
from botocore.exceptions import ClientError

from strandproperties.config import app_cfg
from strandproperties.constants import Captures, PortalIntegration
from strandproperties.libs.aplaceinthesun.xml_builder import APlaceInTheSunXMLBuilder
from strandproperties.libs.aws import (
    SQSReceiveMessageResponse,
    SQSReceiveMessageResponseMessage,
)
from strandproperties.libs.facebook.xml_builder import FacebookXMLBuilder
from strandproperties.libs.google.csv_builder import GoogleCSVBuilder
from strandproperties.libs.idealista.json_builder import IdealistaJSONBuilder
from strandproperties.libs.inmobalia.xml_builder import InmobaliaXMLBuilder
from strandproperties.libs.integration_captures.facebook_campaign_lead_capture import (
    FacebookCampaignLeadCapture,
)
from strandproperties.libs.integration_captures.serviceform_capture import (
    ServiceformCapture,
)
from strandproperties.libs.jamesedition.xml_builder import JamesEditionXMLBuilder
from strandproperties.libs.kyero.xml_builder import KyeroV3XMLBuilder
from strandproperties.libs.leadingre.xml_builder import LeadingREXMLBuilder
from strandproperties.libs.luxuryestate.xml_builder import LuxuryEstateXMLBuilder
from strandproperties.libs.matchmaking.properties_match_making_update import (
    PropertiesMatchMakingUpdate,
)
from strandproperties.libs.matchmaking.properties_newsletter import PropertiesNewsletter
from strandproperties.libs.pisos.xml_builder import PisosXMLBuilder
from strandproperties.libs.resalesonline.pull_info import PullResalesOnlineProperties
from strandproperties.libs.resalesonline.xml_builder import ResalesOnlineXMLBuilder
from strandproperties.libs.thinkspain.xml_builder import ThinkSpainXMLBuilder
from strandproperties.logger import logger
from strandproperties.schemas.base import BaseSchema
from strandproperties.scripts.base import BaseScript
from strandproperties.services.smartly import SmartlyStatsService
from strandproperties.worker.import_fi_property_from_kivi_worker import (
    ImportFIPropertyFromKiviWorker,
)


class SQSListener(BaseScript):
    def _run_script_based_on_message_received(self, body: str):
        data = json.loads(body)

        if data.get("portal") == "smartly_update_ads" and data.get("ad_update"):
            smartly_stats_service = SmartlyStatsService(
                view_id=app_cfg.smartly_ad_view_id
            )
            updated = smartly_stats_service.update_advertisements(self.db_session)
            logger.info("Smartly SQS update: %s ads updated", updated)
            return

        else:
            logger.error("Unknown type of message received: %s", body)
            raise RuntimeError(f"Unknown type of message {body}")

    def receive_messages(self) -> List[SQSReceiveMessageResponseMessage]:
        """
        Receive a batch of messages in a single request from an SQS queue.
        :return: The list of Message objects received. These each contain the Body
                of the message and metadata and custom attributes.
        """
        try:
            response = self.sqs.receive_message(
                QueueUrl=self.queue_url,
                MessageAttributeNames=["All"],
                MaxNumberOfMessages=1,
                WaitTimeSeconds=20,
                VisibilityTimeout=3600,
            )
            parsed_response = SQSReceiveMessageResponse(**response)
            return parsed_response.Messages or []
        except ClientError as error:
            logger.exception("Couldn't receive messages from queue: %s", self.queue_url)
            raise error

    def process_messages(self, messages: List[SQSReceiveMessageResponseMessage]):
        for msg in messages:
            body = msg.Body
            logger.info("Received message: %s: %s", msg.MessageId, body)
            self._run_script_based_on_message_received(body)

    def delete_message(self, message: SQSReceiveMessageResponseMessage):
        """
        Delete a message from a queue. Clients must delete messages after they
        are received and processed to remove them from the queue.

        :param message: The message to delete. The message's queue URL is contained in
                        the message's metadata.
        :return: None
        """
        try:
            self.sqs.delete_message(
                QueueUrl=self.queue_url, ReceiptHandle=message.ReceiptHandle
            )
            logger.info("Deleted message: %s", message.MessageId)
        except ClientError as error:
            logger.exception("Couldn't delete message: %s", message.MessageId)
            raise error

    def run(self):
        self.queue_url = app_cfg.integrations_queue_url

        session = Session()
        # Create SQS client
        self.sqs = session.client(
            service_name="sqs", region_name=app_cfg.aws_region_name
        )

        logger.info(f"Started listening to SQS queue {self.queue_url}")

        while True:
            # explicitly set reraise_exception to False
            # so that notification email is sent and the loop will continue
            self._run(reraise_exception=False)

    def _run(self, reraise_exception: bool = True):
        """
        Break down to its own function so it's easier to test

        :param reraise_exception:
            If True, reraise the exception if caught. This behavior is used only in testing.
            If False, log exception and send an notification email to dev team. This is behavior in production.
        """
        messages = []
        try:
            messages = self.receive_messages()
            self.process_messages(messages)
        except Exception as e:
            if reraise_exception:
                raise e
            else:
                logger.error(e)
                _email_notify(e, self.queue_url)
        finally:
            # Delete received message from queue
            for m in messages:
                self.delete_message(m)


def _email_notify(e: Exception, queue: str):
    mail_template = Template(
        """
Environment: $environment
Timestamp: $timestamp
Queue: $queue
Error: $error
"""
    )

    msgBody = mail_template.substitute(
        {
            "environment": app_cfg.env,
            "error": e,
            "timestamp": datetime.now(),
            "queue": queue,
        }
    )

    msg = MIMEText(msgBody, "plain")
    msg["Subject"] = "StrandProperties ALERT"

    with SMTP(host=app_cfg.smtp_host, port=app_cfg.smtp_port) as smtp:
        smtp.starttls()
        smtp.login(app_cfg.smtp_user, app_cfg.smtp_password)
        smtp.sendmail(
            app_cfg.smtp_user,
            app_cfg.alert_emails,
            msg.as_string(),
        )


if __name__ == "__main__":
    SQSListener().run()
