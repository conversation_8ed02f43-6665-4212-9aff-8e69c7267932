import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Optional, cast

from strandproperties.constants import CommissionType<PERSON>num, Currency, ListingTypeEnum
from strandproperties.libs.inmobilienscout24.mapper import (
    AIR_CONDITIONING_FEATURES,
    ALL_HANDICAP_FEATURES,
    ALL_KITCHEN_FEATURES,
    ALL_RAMP_FEATURES,
    BALCONY_FEATURES,
    COMMON_TAGS,
    INMOBILIEN_SCOUT_PROPERTIES_THAT_ALLOW_INTERNATIONAL_COUNTRY_REGION,
    TERRACE_FEATURES,
    CommercializationTypeEnum,
    InmobilienScout24PropertyTypeEnum,
    InmobilienScoutHasCourtage,
    UtilizationTradeSiteEnum,
    commercialization_type_based_on_listing_type,
    get_built_year,
    get_description_with_priority_to_german,
    get_location_info,
    map_listing_type_to_inmobilienscout24_price_propertytype_specifictype_and_listing_type,
    marketing_type_based_on_commercialization_type,
    property_has_courtage,
    property_has_elevator,
    property_has_features,
    tags_by_property_type,
)
from strandproperties.libs.utils import (
    get_formatted_commission_based_on_commission_type,
)
from strandproperties.logger import logger
from strandproperties.models.property import Property

#! One really important thing about this portal is that
#! it requires that the xml tags are in the right ORDER
#! (see docs for the order of them https://api.immobilienscout24.de/api-docs/import-export/introduction).
#! otherwise it won't work (the API will fail).


class InmobilienScout24XMLBuilder:
    def set_headers(self):
        encoding = "UTF-8"

        root = ET.Element(
            f"realestates:{self.inmobilien_scout_property_type.value}",
            attrib={
                "xmlns:realestates": "http://rest.immobilienscout24.de/schema/offer/realestates/1.0",
                "xmlns:xlink": "http://www.w3.org/1999/xlink",
            },
        )

        self.root = root
        self.encoding = encoding

    def set_common_property_tags(self):
        property_supports_internationalcountryregion = (
            self._does_property_support_internationalcountryregion_tag()
        )
        tags = (
            COMMON_TAGS["common_tags"]
            if property_supports_internationalcountryregion
            else COMMON_TAGS["common_tags_without_internationalcountryregion"]
        )

        self._set_tags(tags=tags)

        # This for 3rd level internationalCountryRegion
        if property_supports_internationalcountryregion:
            self.property_tag["country"] = ET.SubElement(
                self.property_tag["internationalCountryRegion"], "country"
            )
            self.property_tag["region"] = ET.SubElement(
                self.property_tag["internationalCountryRegion"], "region"
            )

    def set_specific_property_tags(
        self,
    ):
        tags = tags_by_property_type[self.inmobilien_scout_property_type]
        self._set_tags(tags=tags)

    def _set_tags(self, *, tags: list[dict]):
        for tag_dict in tags:
            self.property_tag[tag_dict["first_level"]] = ET.SubElement(
                self.root, tag_dict["first_level"]
            )
            for sub_tag in tag_dict["second_level"]:
                self.property_tag[sub_tag] = ET.SubElement(
                    self.property_tag[tag_dict["first_level"]], sub_tag
                )

    def fill_common_property_tags(self):
        description = get_description_with_priority_to_german(
            self.property.descriptions
        )

        # Features
        feature_names_list: list[str] = [
            feature.name for feature in self.property.features
        ]
        feature_names: str = ", ".join(feature_names_list)

        # Location
        location = get_location_info(property=self.property)

        property_supports_internationalcountryregion = (
            self._does_property_support_internationalcountryregion_tag()
        )

        data = {
            "externalId": self.property.reference[:50],
            "title": description.tagline[:100],
            "street": location.address[:100] if location.address else None,
            "postcode": location.postcode[:5] if location.postcode else None,
            "city": location.city[:50],
            "descriptionNote": description.description,
            "otherNote": feature_names,
            "showAddress": "true" if self.property.is_public_coordinates else "false",
        }

        # Property supports internationalCountryRegion tag
        if property_supports_internationalcountryregion:
            data.update({"country": location.countryISO3166})
            data.update({"region": location.region})

        # Property does NOT support internationalCountryRegion tag,
        # therefore houseNumber is REQUIRED
        if not property_supports_internationalcountryregion:
            # TODO: change once we have the number info in the system
            data.update({"houseNumber": f"1"})

        if self.property.latitude is not None and self.property.longitude is not None:
            self.property_tag["wgs84Coordinate"] = ET.SubElement(
                self.property_tag["address"], "wgs84Coordinate"
            )
            self.property_tag["latitude"] = ET.SubElement(
                self.property_tag["wgs84Coordinate"], "latitude"
            )
            self.property_tag["longitude"] = ET.SubElement(
                self.property_tag["wgs84Coordinate"], "longitude"
            )
            data.update({"latitude": f"{self.property.latitude}"})
            data.update({"longitude": f"{self.property.longitude}"})

        self._fill_tags(data_to_fill=data)

    def fill_required_property_tags(self):
        data: dict[str, str] = {}

        match self.inmobilien_scout_property_type:
            case InmobilienScout24PropertyTypeEnum.APARTMENT_BUY:
                data = {
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "livingSpace": f"{self.property.built_area}",
                    "numberOfRooms": f"{self.property.rooms_total or self.property.bedrooms}",
                }

            case InmobilienScout24PropertyTypeEnum.APARTMENT_RENT:
                data = {
                    "baseRent": self.price,
                    "livingSpace": f"{self.property.built_area}",
                    "numberOfRooms": f"{self.property.rooms_total or self.property.bedrooms}",
                }

            case InmobilienScout24PropertyTypeEnum.HOUSE_BUY:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "buildingType": self.specific_type,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "livingSpace": f"{self.property.built_area}",
                    "plotArea": f"{self.property.plot_area}",
                    "numberOfRooms": f"{self.property.rooms_total or self.property.bedrooms}",
                }

            case InmobilienScout24PropertyTypeEnum.HOUSE_RENT:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "livingSpace": f"{self.property.built_area}",
                    "plotArea": f"{self.property.plot_area}",
                    "numberOfRooms": f"{self.property.rooms_total or self.property.bedrooms}",
                    "buildingType": self.specific_type,
                    "baseRent": self.price,
                }

            case InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE:
                data = {
                    "commercializationType": CommercializationTypeEnum.BUY.value,  # it is not using the commercialization_type because LIVING SITE accepts BUY or LEASEHOLD (and not RENT)
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "plotArea": f"{self.property.plot_area}",
                }

            case InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE:
                data = {
                    "commercializationType": CommercializationTypeEnum.LEASEHOLD.value,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "plotArea": f"{self.property.plot_area}",
                }

            case InmobilienScout24PropertyTypeEnum.TRADE_SITE:
                data = {
                    "commercializationType": CommercializationTypeEnum.BUY.value,
                    "utilizationTradeSite": UtilizationTradeSiteEnum.TRADE,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "plotArea": f"{self.property.plot_area}",
                    "marketingType": self.marketing_type,
                }

            case InmobilienScout24PropertyTypeEnum.GARAGE_RENT:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "garageType": self.specific_type,
                }

            case InmobilienScout24PropertyTypeEnum.GARAGE_BUY:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "garageType": self.specific_type,
                }

            case InmobilienScout24PropertyTypeEnum.GASTRONOMY:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "gastronomyType": self.specific_type,
                    "commercializationType": self.commercialization_type.value,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "marketingType": self.marketing_type,
                    "totalFloorSpace": f"{self.property.plot_area}",
                }

            case InmobilienScout24PropertyTypeEnum.STORE:
                data = {
                    "commercializationType": self.commercialization_type.value,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "marketingType": self.marketing_type,
                    "totalFloorSpace": f"{self.property.plot_area}",
                    "netFloorSpace": f"{self.property.built_area}",
                }

            case InmobilienScout24PropertyTypeEnum.OFFICE:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "officeType": self.specific_type,
                    "commercializationType": self.commercialization_type.value,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "marketingType": self.marketing_type,
                    "totalFloorSpace": f"{self.property.plot_area}",
                    "netFloorSpace": f"{self.property.built_area}",
                }

            case InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "specialPurposePropertyType": self.specific_type,
                    "commercializationType": self.commercialization_type.value,
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "marketingType": self.marketing_type,
                    "totalFloorSpace": f"{self.property.plot_area}",
                    "netFloorSpace": f"{self.property.built_area}",
                }

            case InmobilienScout24PropertyTypeEnum.INVESTMENT:
                if not self.specific_type:
                    raise Exception("Specific type is required")

                data = {
                    "value": self.price,
                    "currency": Currency.EUR.value.upper(),
                    "netFloorSpace": f"{self.property.built_area}",
                    "investmentType": self.specific_type,
                    "constructionYear": f"{get_built_year(property=self.property)}",
                }

            case _:
                raise Exception(
                    f"Property {self.property.reference} hasn't a valid property type."
                )

        # As requested from portal InmobilienScout24 value is always Not_Applicable
        # commented out is the previous code
        has_courtage = (
            InmobilienScoutHasCourtage.NOT_APPLICABLE
        )  # property_has_courtage(property=self.property)
        data.update({"hasCourtage": has_courtage.value})

        # NOTE: This is turned off as request from portal InmobilienScout24 so the
        # courtage = ""
        # if has_courtage == InmobilienScoutHasCourtage.YES:
        #     courtage = get_formatted_commission_based_on_commission_type(
        #         commission=self.property.commission,
        #         commission_type=cast(
        #             Optional[CommissionTypeEnum], self.property.commission_type
        #         ),
        #         currency=self.property.currency,
        #     )
        # # data.update({"courtage": courtage})

        self._fill_tags(data_to_fill=data)

    def fill_optional_property_tags(self):
        data: dict[str, str] = {}

        match self.inmobilien_scout_property_type:
            case (
                InmobilienScout24PropertyTypeEnum.APARTMENT_BUY
                | InmobilienScout24PropertyTypeEnum.APARTMENT_RENT
            ):
                if self.property.floor is not None and not (
                    self.property.floor < 0 or self.property.floor > 999
                ):
                    data.update({"floor": f"{self.property.floor}"})
                else:
                    self._remove_element(tag="floor")

                (
                    data.update({"lift": f"true"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"false"})
                )

                (
                    data.update({"handicappedAccessible": f"YES"})
                    if property_has_features(
                        property=self.property, features=ALL_HANDICAP_FEATURES
                    )
                    else data.update({"handicappedAccessible": f"NOT_APPLICABLE"})
                )

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

                if self.property.bedrooms is not None and not (
                    self.property.bedrooms < 0 or self.property.bedrooms > 99
                ):
                    data.update({"numberOfBedRooms": f"{self.property.bedrooms}"})
                else:
                    self._remove_element(tag="numberOfBedRooms")

                if self.property.bathrooms is not None and not (
                    self.property.bathrooms < 0 or self.property.bathrooms > 99
                ):
                    data.update({"numberOfBathRooms": f"{self.property.bathrooms}"})
                else:
                    self._remove_element(tag="numberOfBathRooms")

                (
                    data.update({"builtInKitchen": f"true"})
                    if property_has_features(
                        property=self.property, features=ALL_KITCHEN_FEATURES
                    )
                    else data.update({"builtInKitchen": f"false"})
                )

                (
                    data.update({"balcony": f"true"})
                    if property_has_features(
                        property=self.property, features=BALCONY_FEATURES
                    )
                    else data.update({"balcony": f"false"})
                )

                (
                    data.update({"garden": f"true"})
                    if len(self.property.garden_types) > 0
                    else data.update({"garden": f"false"})
                )

            case (
                InmobilienScout24PropertyTypeEnum.HOUSE_BUY
                | InmobilienScout24PropertyTypeEnum.HOUSE_RENT
            ):

                (
                    data.update({"handicappedAccessible": f"YES"})
                    if property_has_features(
                        property=self.property, features=ALL_HANDICAP_FEATURES
                    )
                    else data.update({"handicappedAccessible": f"NOT_APPLICABLE"})
                )

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

                if self.property.bedrooms is not None and not (
                    self.property.bedrooms < 0 or self.property.bedrooms > 99
                ):
                    data.update({"numberOfBedRooms": f"{self.property.bedrooms}"})
                else:
                    self._remove_element(tag="numberOfBedRooms")

                if self.property.bathrooms is not None and not (
                    self.property.bathrooms < 0 or self.property.bathrooms > 99
                ):
                    data.update({"numberOfBathRooms": f"{self.property.bathrooms}"})
                else:
                    self._remove_element(tag="numberOfBathRooms")

            case (
                InmobilienScout24PropertyTypeEnum.GARAGE_BUY
                | InmobilienScout24PropertyTypeEnum.GARAGE_RENT
            ):
                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

            case InmobilienScout24PropertyTypeEnum.GASTRONOMY:
                (
                    data.update({"lift": f"YES"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"NOT_APPLICABLE"})
                )

                (
                    data.update({"terrace": f"YES"})
                    if property_has_features(
                        property=self.property, features=TERRACE_FEATURES
                    )
                    else data.update({"terrace": f"NOT_APPLICABLE"})
                )

                if self.property.bedrooms is not None and not (
                    self.property.bedrooms < 0 or self.property.bedrooms > 99_999
                ):
                    data.update({"numberBeds": f"{self.property.bedrooms}"})
                else:
                    self._remove_element(tag="numberBeds")

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

            case InmobilienScout24PropertyTypeEnum.STORE:
                (
                    data.update({"lift": f"YES"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"NOT_APPLICABLE"})
                )

                (
                    data.update({"ramp": f"YES"})
                    if property_has_features(
                        property=self.property, features=ALL_RAMP_FEATURES
                    )
                    else data.update({"ramp": f"NOT_APPLICABLE"})
                )

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

            case InmobilienScout24PropertyTypeEnum.OFFICE:
                (
                    data.update({"lift": f"YES"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"NOT_APPLICABLE"})
                )

                (
                    data.update({"handicappedAccessible": f"YES"})
                    if property_has_features(
                        property=self.property, features=ALL_HANDICAP_FEATURES
                    )
                    else data.update({"handicappedAccessible": f"NOT_APPLICABLE"})
                )

                (
                    data.update({"kitchenComplete": f"YES"})
                    if property_has_features(
                        property=self.property, features=ALL_KITCHEN_FEATURES
                    )
                    else data.update({"kitchenComplete": f"NOT_APPLICABLE"})
                )

                (
                    data.update({"airConditioning": f"YES"})
                    if property_has_features(
                        property=self.property, features=AIR_CONDITIONING_FEATURES
                    )
                    else data.update({"airConditioning": f"NOT_APPLICABLE"})
                )

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

            case InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE:
                (
                    data.update({"lift": f"YES"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"NOT_APPLICABLE"})
                )

                if self.property.built_year is not None:
                    data.update(
                        {
                            "constructionYear": f"{get_built_year(property=self.property)}"
                        }
                    )
                else:
                    self._remove_element(tag="constructionYear")

                if self.property.parking_spaces is not None and not (
                    self.property.parking_spaces < 0
                    or self.property.parking_spaces > 99_999
                ):
                    data.update(
                        {"numberOfParkingSpaces": f"{self.property.parking_spaces}"}
                    )
                else:
                    self._remove_element(tag="numberOfParkingSpaces")

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

            case InmobilienScout24PropertyTypeEnum.INVESTMENT:
                if self.property.plot_area is not None and not (
                    self.property.plot_area < 0
                    or self.property.plot_area > 99_999_999.99
                ):
                    data.update({"plotArea": f"{self.property.plot_area}"})
                else:
                    self._remove_element(tag="plotArea")

                (
                    data.update({"lift": f"YES"})
                    if property_has_elevator(property=self.property)
                    else data.update({"lift": f"NOT_APPLICABLE"})
                )

                if self.property.total_floors is not None and not (
                    self.property.total_floors < 0 or self.property.total_floors > 999
                ):
                    data.update({"numberOfFloors": f"{self.property.total_floors}"})
                else:
                    self._remove_element(tag="numberOfFloors")

            case (
                InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE
                | InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE
                | InmobilienScout24PropertyTypeEnum.TRADE_SITE
            ):
                pass
            case _:
                raise Exception(
                    f"Property {self.property.reference} hasn't a valid property type."
                )

        self._fill_tags(data_to_fill=data)

    def _fill_tags(self, *, data_to_fill: dict[str, str]):
        for key, value in data_to_fill.items():
            self.property_tag[key].text = value

    def _does_property_support_internationalcountryregion_tag(self) -> bool:
        return (
            self.inmobilien_scout_property_type
            in INMOBILIEN_SCOUT_PROPERTIES_THAT_ALLOW_INTERNATIONAL_COUNTRY_REGION
        )

    def _remove_element(self, *, tag: str):
        self.root.remove(self.property_tag[tag])

    def get_xml(self) -> str:
        return ET.tostring(self.root, encoding=self.encoding)

    def generate_xml(self) -> str:
        # Get data beforehand
        (price, inmobilien_scout_property_type, specific_type, listing_type) = (
            map_listing_type_to_inmobilienscout24_price_propertytype_specifictype_and_listing_type(
                property=self.property
            )
        )

        ### Check required fields
        property_has_all_required_fields = self._does_property_has_all_required_fields(
            inmobilien_property_type=inmobilien_scout_property_type,
            property=self.property,
            request_id=self.request_id,
        )

        if not property_has_all_required_fields:
            logger.info(
                "Propery does not have all required fields",
                extra={
                    "requestId": self.request_id,
                },
            )
            raise Exception("Propery does not have all required fields")

        commercialization_type = commercialization_type_based_on_listing_type[
            listing_type
        ]
        marketing_type = marketing_type_based_on_commercialization_type[
            commercialization_type
        ]

        self.property_tag: dict[str, ET.Element] = {}
        self.price: str = price
        self.inmobilien_scout_property_type: InmobilienScout24PropertyTypeEnum = (
            inmobilien_scout_property_type
        )
        self.specific_type: Optional[str] = specific_type
        self.listing_type: ListingTypeEnum = listing_type
        self.commercialization_type = commercialization_type
        self.marketing_type = marketing_type

        ### XML headers
        self.set_headers()
        ### Set common tags
        self.set_common_property_tags()
        ### Set specific tags
        self.set_specific_property_tags()

        # Fill tags
        self.fill_common_property_tags()
        self.fill_required_property_tags()
        self.fill_optional_property_tags()

        try:
            return self.get_xml()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request_id})
            raise Exception(f"Could not generate xml: {e}")

    def _does_property_has_all_required_fields(
        self,
        inmobilien_property_type: InmobilienScout24PropertyTypeEnum,
        property: Property,
        request_id: str,
    ) -> bool:
        ### COMMON
        # title, address (street, postcode, city),
        # isPublicCoordinates

        # area_level_1 (city) is REQUIRED
        if not self.property.area_level_1_id:
            logger.info(
                f"> Skipping property [{property.reference}][InmobilienScout24]: area level 1 is required",
                extra={"requestId": request_id},
            )
            return False

        # we need info from location which is REQUIRED
        if not (
            self.property.private_info and "location" in self.property.private_info  # type: ignore
        ):  # type: ignore
            logger.info(
                f"> Skipping property [{property.reference}][InmobilienScout24]: private_info.location is required",
                extra={"requestId": request_id},
            )
            return False

        # Check price based on listing type
        listing_types = self.property.listing_types
        if not listing_types:
            logger.info(
                f"> Skipping property [{property.reference}][InmobilienScout24]: listing type is required",
                extra={"requestId": request_id},
            )
            return False
        if ListingTypeEnum.SALE in listing_types:
            if not self.property.price_sale:
                logger.info(
                    f"> Skipping property [{property.reference}][InmobilienScout24]: listing_type is SALE but price_sale does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_SHORT in listing_types:
            if not self.property.price_rent_short_term:
                logger.info(
                    f"> Skipping property [{property.reference}][InmobilienScout24]: listing_type is RENT_SHORT but price_rent_short_term does not exist",
                    extra={"requestId": request_id},
                )
                return False
        elif ListingTypeEnum.RENT_LONG in listing_types:
            if not self.property.price_rent_long_term:
                logger.info(
                    f"> Skipping property [{property.reference}][InmobilienScout24]: listing_type is RENT_LONG but price_rent_long_term does not exist",
                    extra={"requestId": request_id},
                )
                return False

        # tagline (title) is REQUIRED
        non_empty_tagline = [
            desc.tagline for desc in self.property.descriptions if desc.tagline
        ]
        if not non_empty_tagline:
            logger.info(
                f"> Skipping property [{property.reference}][InmobilienScout24]: non-empty tagline is required",
                extra={"requestId": request_id},
            )
            return False

        match inmobilien_property_type:
            case (
                InmobilienScout24PropertyTypeEnum.APARTMENT_BUY
                | InmobilienScout24PropertyTypeEnum.APARTMENT_RENT
            ):
                # built_area is REQUIRED
                if not self.property.built_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: built_area is required for APARTMENT",
                        extra={"requestId": request_id},
                    )
                    return False
                # rooms_total is REQUIRED
                if self.property.rooms_total is None and self.property.bedrooms is None:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: rooms_total/bedrooms is required for APARTMENT",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for APARTMENT",
                        extra={"requestId": request_id},
                    )
                    return False

            case (
                InmobilienScout24PropertyTypeEnum.HOUSE_BUY
                | InmobilienScout24PropertyTypeEnum.HOUSE_RENT
            ):
                # built_area is REQUIRED
                if not self.property.built_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: built_area is required for HOUSE",
                        extra={"requestId": request_id},
                    )
                    return False
                # plot_area is REQUIRED
                if not self.property.plot_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: plot_area is required for HOUSE",
                        extra={"requestId": request_id},
                    )
                    return False
                # rooms_total is REQUIRED
                if self.property.rooms_total is None and self.property.bedrooms is None:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: rooms_total/bedrooms is required for APARTMENT",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for HOUSE",
                        extra={"requestId": request_id},
                    )
                    return False

            case (
                InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE
                | InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE
                | InmobilienScout24PropertyTypeEnum.GASTRONOMY
            ):
                # plot_area is REQUIRED
                if not self.property.plot_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: plot_area is required for LIVING_SITE/GASTRONOMY",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for LIVING_SITE/GASTRONOMY",
                        extra={"requestId": request_id},
                    )
                    return False

            case (
                InmobilienScout24PropertyTypeEnum.STORE
                | InmobilienScout24PropertyTypeEnum.OFFICE
                | InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE
            ):
                # plot_area is REQUIRED
                if not self.property.plot_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: plot_area is required for STORE/OFFICE/SPECIAL_PURPOSE",
                        extra={"requestId": request_id},
                    )
                    return False
                # built_area is REQUIRED
                if not self.property.built_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: built_area is required for STORE/OFFICE/SPECIAL_PURPOSE",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for STORE/OFFICE/SPECIAL_PURPOSE",
                        extra={"requestId": request_id},
                    )
                    return False

            case InmobilienScout24PropertyTypeEnum.INVESTMENT:
                # built_area is REQUIRED
                if not self.property.built_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: built_area is required for INVESTMENT",
                        extra={"requestId": request_id},
                    )
                    return False
                # built_year is REQUIRED
                if not self.property.built_year:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: built_year is required for INVESTMENT",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for INVESTMENT",
                        extra={"requestId": request_id},
                    )
                    return False

            case InmobilienScout24PropertyTypeEnum.TRADE_SITE:
                # plot_area is REQUIRED
                if not self.property.plot_area:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: plot_area is required for TRADE_SITE",
                        extra={"requestId": request_id},
                    )
                    return False
                # commission is REQUIRED
                if not self.property.commission:
                    logger.info(
                        f"> Skipping property [{property.reference}][InmobilienScout24]: commission is required for TRADE_SITE",
                        extra={"requestId": request_id},
                    )
                    return False

        return True

    def run(self, property: Property) -> str:
        ### Creates request_id to log the process
        date_now = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        self.request_id = f"[{date_now}]_integrations_xml_inmobilienscout24"
        logger.info(
            f"Started generating inmobilienscout24 xml for property {property.reference}",
            extra={
                "requestId": self.request_id,
            },
        )

        ### Generates XML
        self.property = property
        try:
            generated_xml = self.generate_xml()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request_id})
            raise Exception(e)

        logger.info(
            f"Finished generating inmobilienscout24 xml for property {property.reference}",
            extra={
                "requestId": self.request_id,
            },
        )

        return generated_xml
