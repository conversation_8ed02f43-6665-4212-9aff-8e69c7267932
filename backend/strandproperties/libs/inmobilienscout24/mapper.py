from enum import Enum
from typing import List, Optional, cast

from pydantic import BaseModel

from strandproperties.constants import DescriptionType, Language, ListingTypeEnum
from strandproperties.models.property import Property, PropertyDescription

"""
Every non-German real estate has to have at least these inside the address tag:
From (https://api.immobilienscout24.de/api-docs/import-export/real-estate/international-real-estates/)

- street, house number and zip code are optional fields, but highly recommended input for resolving the real estate's geocodes.
- The wgs84Coordinate element is optional. If you don't pass the element, ImmoScout24 will try to resolve the geocodes from the given address.
  If this geocoding is successful, the resulting coordinates are added to the real estate object. Otherwise the real estate will be stored
  with the given address and the given country and region only.

#! OBS: only supports the following real estate types:
        apartment buy (Wohnung Kauf),
        house buy (Haus Kauf),
        apartment rent (Wohnung Miete),
        house rent (Haus Miete),
        living buy site (Grundstück Kauf),
        trade site (Grundstück Gewerbe)
        
Example:
...
<address>
    <street>Kalverstraat</street>
    <houseNumber>1</houseNumber>
    <postcode>5912 HG</postcode>
    <city>Amsterdam</city>
    <internationalCountryRegion>
        <country>NLD</country>
        <region>Nord-Holland</region>
    </internationalCountryRegion>
    <wgs84Coordinate>
        <latitude>52.51245</latitude>
        <longitude>13.43134</longitude>
    </wgs84Coordinate>
</address>
...

"""

"""
Tags that are common to all property types.
externalId,descriptionNote, otherNote are OPTIONAL.
"""
COMMON_TAGS = {
    "common_tags": [
        {"first_level": "externalId", "second_level": []},
        {"first_level": "title", "second_level": []},
        {
            "first_level": "address",
            "second_level": [
                "street",
                "postcode",
                "city",
                "internationalCountryRegion",
            ],
        },
        {"first_level": "descriptionNote", "second_level": []},
        {"first_level": "otherNote", "second_level": []},
        {"first_level": "showAddress", "second_level": []},
    ],
    "common_tags_without_internationalcountryregion": [
        {"first_level": "externalId", "second_level": []},
        {"first_level": "title", "second_level": []},
        {
            "first_level": "address",
            "second_level": [
                "street",
                "houseNumber",
                "postcode",
                "city",
            ],
        },
        {"first_level": "descriptionNote", "second_level": []},
        {"first_level": "otherNote", "second_level": []},
        {"first_level": "showAddress", "second_level": []},
    ],
}


# From https://api.immobilienscout24.de/api-docs/tutorials/international-regions/
# Obs.: these are only for Spain
class InmobilienScout24Region(str, Enum):
    ANDALUSIA = "Andalusien"
    ARAGON = "Aragonien"
    ASTURIAS = "Asturien"
    BASQUE_COUNTRY = "Pais Vasco"
    CANTABRIA = "Kantabrien"
    CASTILLA_LA_MANCHA = "Kastilien-La Mancha"
    CASTILLA_LEON = "Kastilien Leon"
    CATALONIA = "Katalonien"
    EL_HIERRO = "Hierro"
    EXTREMADURA = "Extremadura"
    FORMENTERA = "Formentera"
    FUERTEVENTURA = "Fuerteventura"
    GALICIA = "Galicien"
    GRAN_CANARIA = "Gran Canaria"
    IBIZA = "Ibiza"
    LA_GOMERA = "Gomera"
    LA_PALMA = "La Palma"
    LA_RIOJA = "La Rioja"
    LANZAROTE = "Lanzarote"
    MADRID = "Madrid"
    MALLORCA = "Mallorca"
    MENORCA = "Menorca"
    MURCIA = "Murcia"
    NAVARRA = "Navarra"
    TENERIFE = "Teneriffa"
    VALENCIA = "Valencia"


class PublishChannelsEnum(str, Enum):
    INMOBILIEN_SCOUT_24_DE = "10000"
    CUSTOMER_HOMEPAGE = "10001"


class HouseBuildingTypeEnum(str, Enum):
    NO_INFORMATION = "NO_INFORMATION"
    SINGLE_FAMILY_HOUSE = "SINGLE_FAMILY_HOUSE"
    MID_TERRACE_HOUSE = "MID_TERRACE_HOUSE"
    END_TERRACE_HOUSE = "END_TERRACE_HOUSE"
    MULTI_FAMILY_HOUSE = "MULTI_FAMILY_HOUSE"
    BUNGALOW = "BUNGALOW"
    SEMIDETACHED_HOUSE = "SEMIDETACHED_HOUSE"
    VILLA = "VILLA"
    CASTLE_MANOR_HOUSE = "CASTLE_MANOR_HOUSE"
    SPECIAL_REAL_ESTATE = "SPECIAL_REAL_ESTATE"
    OTHER = "OTHER"


class CommercializationTypeEnum(str, Enum):
    BUY = "BUY"
    LEASEHOLD = "LEASEHOLD"
    RENT = "RENT"
    LEASE = "LEASE"


class UtilizationTradeSiteEnum(str, Enum):
    LEISURE = "LEISURE"
    AGRICULTURE_FORESTRY = "AGRICULTURE_FORESTRY"
    TRADE = "TRADE"


class GarageTypeEnum(str, Enum):
    NO_INFORMATION = "NO_INFORMATION"
    GARAGE = "GARAGE"
    STREET_PARKING = "STREET_PARKING"
    CARPORT = "CARPORT"
    DUPLEX = "DUPLEX"
    CAR_PARK = "CAR_PARK"
    UNDERGROUND_GARAGE = "UNDERGROUND_GARAGE"


class GastronomyTypeEnum(str, Enum):
    BAR_LOUNGE = "BAR_LOUNGE"
    CAFE = "CAFE"
    CLUB_DISCO = "CLUB_DISCO"
    GUESTS_HOUSE = "GUESTS_HOUSE"
    TAVERN = "TAVERN"
    HOTEL = "HOTEL"
    HOTEL_RESIDENCE = "HOTEL_RESIDENCE"
    HOTEL_GARNI = "HOTEL_GARNI"
    PENSION = "PENSION"
    RESTAURANT = "RESTAURANT"
    BUNGALOW = "BUNGALOW"


class OfficeTypeEnum(str, Enum):
    LOFT = "LOFT"
    STUDIO = "STUDIO"
    OFFICE = "OFFICE"
    OFFICE_FLOOR = "OFFICE_FLOOR"
    OFFICE_BUILDING = "OFFICE_BUILDING"
    OFFICE_CENTRE = "OFFICE_CENTRE"
    OFFICE_STORAGE_BUILDING = "OFFICE_STORAGE_BUILDING"
    SURGERY = "SURGERY"
    SURGERY_FLOOR = "SURGERY_FLOOR"
    SURGERY_BUILDING = "SURGERY_BUILDING"
    COMMERCIAL_CENTRE = "COMMERCIAL_CENTRE"
    LIVING_AND_COMMERCIAL_BUILDING = "LIVING_AND_COMMERCIAL_BUILDING"
    OFFICE_AND_COMMERCIAL_BUILDING = "OFFICE_AND_COMMERCIAL_BUILDING"


class InvestmentTypeEnum(str, Enum):
    SINGLE_FAMILY_HOUSE = "SINGLE_FAMILY_HOUSE"
    MULTI_FAMILY_HOUSE = "MULTI_FAMILY_HOUSE"
    FREEHOLD_FLAT = "FREEHOLD_FLAT"
    SHOPPING_CENTRE = "SHOPPING_CENTRE"
    RESTAURANT = "RESTAURANT"
    HOTEL = "HOTEL"
    LEISURE_FACILITY = "LEISURE_FACILITY"
    COMMERCIAL_UNIT = "COMMERCIAL_UNIT"
    OFFICE_BUILDING = "OFFICE_BUILDING"
    COMMERCIAL_BUILDING = "COMMERCIAL_BUILDING"
    COMMERCIAL_PROPERTY = "COMMERCIAL_PROPERTY"
    HALL_STORAGE = "HALL_STORAGE"
    INDUSTRIAL_PROPERTY = "INDUSTRIAL_PROPERTY"
    SHOP_SALES_FLOOR = "SHOP_SALES_FLOOR"
    SERVICE_CENTRE = "SERVICE_CENTRE"
    OTHER = "OTHER"
    SUPERMARKET = "SUPERMARKET"
    LIVING_BUSINESS_HOUSE = "LIVING_BUSINESS_HOUSE"
    HOUSING_ESTATE = "HOUSING_ESTATE"


class SpecialPurposeTypeEnum(str, Enum):
    RESIDENCE = "RESIDENCE"
    FARM = "FARM"
    HORSE_FARM = "HORSE_FARM"
    VINEYARD = "VINEYARD"
    REPAIR_SHOP = "REPAIR_SHOP"
    LEISURE_FACILITY = "LEISURE_FACILITY"
    INDUSTRIAL_AREA = "INDUSTRIAL_AREA"
    SPECIAL_ESTATE = "SPECIAL_ESTATE"
    COMMERCIAL_CENTRE = "COMMERCIAL_CENTRE"


class InmobilienScoutHasCourtage(str, Enum):
    YES = "YES"
    NO = "NO"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class InmobilienScout24PropertyTypeEnum(str, Enum):
    APARTMENT_BUY = "apartmentBuy"
    APARTMENT_RENT = "apartmentRent"
    HOUSE_BUY = "houseBuy"
    HOUSE_RENT = "houseRent"
    LIVING_RENT_SITE = "livingRentSite"
    LIVING_BUY_SITE = "livingBuySite"
    GARAGE_RENT = "garageRent"
    GARAGE_BUY = "garageBuy"
    OFFICE = "office"
    STORE = "store"
    GASTRONOMY = "gastronomy"
    INDUSTRY = "industry"
    TRADE_SITE = "tradeSite"
    SPECIAL_PURPOSE = "specialPurpose"
    INVESTMENT = "investment"
    FLAT_SHARE_ROOM = "flatShareRoom"
    HOUSE_TYPE = "houseType"
    COMPULSORY_AUCTION = "compulsoryAuction"
    ASSISTED_LIVING = "assistedLiving"
    SENIOR_CARE = "seniorCare"
    SHORT_TERM_ACCOMMODATION = "shortTermAccommodation"


# Check (https://api.immobilienscout24.de/api-docs/import-export/real-estate/international-real-estates/)
INMOBILIEN_SCOUT_PROPERTIES_THAT_ALLOW_INTERNATIONAL_COUNTRY_REGION = [
    InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE,
    InmobilienScout24PropertyTypeEnum.TRADE_SITE,
]

"""
Get tags that each inmobilienscout24 property type
can have (besides the common tags).

NOTE: the optional fields are here because the portal require tags
to have the same order as displayed in their API documentation
"""
tags_by_property_type = {
    InmobilienScout24PropertyTypeEnum.APARTMENT_BUY: [
        # OPTIONAL property.floor
        {"first_level": "floor", "second_level": []},
        # OPTIONAL true/false (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_HANDICAP_FEATURES))
        {"first_level": "handicappedAccessible", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
        # OPTIONAL property.bedrooms
        {"first_level": "numberOfBedRooms", "second_level": []},
        # OPTIONAL property.bathrooms
        {"first_level": "numberOfBathRooms", "second_level": []},
        #! REQUIRED Currency must be EUR. If price is 0, it will be shown as "price on application"
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED (use property.built_area)
        {"first_level": "livingSpace", "second_level": []},
        #! REQUIRED (use property.rooms_total)
        {"first_level": "numberOfRooms", "second_level": []},
        # OPTIONAL true/false (use property_has_features(property, ALL_KITCHEN_FEATURES))
        {"first_level": "builtInKitchen", "second_level": []},
        # OPTIONAL true/false (use property_has_features(property, BALCONY_FEATURES))
        {"first_level": "balcony", "second_level": []},
        # OPTIONAL self.property.garden_types
        {"first_level": "garden", "second_level": []},
        #! REQUIRED (this is commission) hasCourtage: YES, NO, NOT_APPLICABLE; # courtage string max 100 chars, only mandatory if `hasCourtage=YES`
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.APARTMENT_RENT: [
        # OPTIONAL property.floor
        {"first_level": "floor", "second_level": []},
        # OPTIONAL true/false (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_HANDICAP_FEATURES))
        {"first_level": "handicappedAccessible", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
        # OPTIONAL property.bedrooms
        {"first_level": "numberOfBedRooms", "second_level": []},
        # OPTIONAL property.bathrooms
        {"first_level": "numberOfBathRooms", "second_level": []},
        #! REQUIRED If baseRent is 0, it will be shown as "price on application"
        {"first_level": "baseRent", "second_level": []},
        #! REQUIRED
        {"first_level": "livingSpace", "second_level": []},
        #! REQUIRED  (use property.rooms_total)
        {"first_level": "numberOfRooms", "second_level": []},
        # OPTIONAL true/false (use property_has_features(property, ALL_KITCHEN_FEATURES))
        {"first_level": "builtInKitchen", "second_level": []},
        # OPTIONAL true/false (use property_has_features(property, BALCONY_FEATURES))
        {"first_level": "balcony", "second_level": []},
        # OPTIONAL self.property.garden_types
        {"first_level": "garden", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.HOUSE_BUY: [
        #! REQUIRED One of HouseBuildingTypeEnum
        {"first_level": "buildingType", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_HANDICAP_FEATURES))
        {"first_level": "handicappedAccessible", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
        # OPTIONAL property.bedrooms
        {"first_level": "numberOfBedRooms", "second_level": []},
        # OPTIONAL property.bathrooms
        {"first_level": "numberOfBathRooms", "second_level": []},
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED
        {"first_level": "livingSpace", "second_level": []},
        #! REQUIRED use property.plot_area
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED (use property.rooms_total)
        {"first_level": "numberOfRooms", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.HOUSE_RENT: [
        #! REQUIRED
        {"first_level": "livingSpace", "second_level": []},
        #! REQUIRED
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED (use property.rooms_total)
        {"first_level": "numberOfRooms", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        #! REQUIRED
        {"first_level": "buildingType", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_HANDICAP_FEATURES))
        {"first_level": "handicappedAccessible", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
        # OPTIONAL property.bedrooms
        {"first_level": "numberOfBedRooms", "second_level": []},
        # OPTIONAL property.bathrooms
        {"first_level": "numberOfBathRooms", "second_level": []},
        #! REQUIRED
        {"first_level": "baseRent", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE: [
        #! REQUIRED One of CommercializationTypeEnum (BUY or LEASEHOLD)
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE: [
        #! REQUIRED
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.GARAGE_RENT: [
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        #! REQUIRED One of GarageTypeEnum
        {"first_level": "garageType", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.GARAGE_BUY: [
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        #! REQUIRED
        {"first_level": "garageType", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.GASTRONOMY: [
        #! REQUIRED One of GastronomyTypeEnum
        {"first_level": "gastronomyType", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, TERRACE_FEATURES))
        {"first_level": "terrace", "second_level": []},
        # OPTIONAL property.bedrooms
        {"first_level": "numberBeds", "second_level": []},
        #! REQUIRED One of CommercializationTypeEnum (BUY or RENT)
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {
            "first_level": "price",
            # Has to be compatible to commercializationType! If commercializationType = "RENT", than "RENT". If "BUY", than "PURCHASE".
            # Use _marketing_type_based_on_commercialization_type
            "second_level": ["value", "currency", "marketingType"],
        },
        #! REQUIRED use the property.plot_area
        {"first_level": "totalFloorSpace", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.STORE: [
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_RAMP_FEATURES))
        {"first_level": "ramp", "second_level": []},
        #! REQUIRED One of CommercializationTypeEnum (BUY or RENT)
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {
            "first_level": "price",
            # Has to be compatible to commercializationType! If commercializationType = "RENT", than RENT or RENT_PER_SQM. If "BUY", than "PURCHASE".
            "second_level": ["value", "currency", "marketingType"],
        },
        #! REQUIRED use the property.plot_area
        {"first_level": "totalFloorSpace", "second_level": []},
        #! REQUIRED use the property.built_area
        {"first_level": "netFloorSpace", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.OFFICE: [
        #! REQUIRED One of OfficeTypeEnum
        {"first_level": "officeType", "second_level": []},
        # OPTIONAL YES/NO/NOT_APPLICABLE (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_HANDICAP_FEATURES))
        {"first_level": "handicappedAccessible", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_features(property, ALL_KITCHEN_FEATURES))
        {"first_level": "kitchenComplete", "second_level": []},
        # OPTIONAL NO_INFORMATION/YES/NO/BY_APPOINTMENT (use property_has_features(property, AIR_CONDITIONING_FEATURES))
        {"first_level": "airConditioning", "second_level": []},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
        #! REQUIRED
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {
            "first_level": "price",
            # Has to be compatible to commercializationType! If commercializationType = "RENT", than RENT or RENT_PER_SQM. If "BUY", than "PURCHASE".
            # Use _marketing_type_based_on_commercialization_type
            "second_level": ["value", "currency", "marketingType"],
        },
        #! REQUIRED use the property.built_area
        {"first_level": "netFloorSpace", "second_level": []},
        #! REQUIRED use the property.plot_area
        {"first_level": "totalFloorSpace", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
    InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE: [
        #! REQUIRED One of SpecialPurposeTypeEnum
        {"first_level": "specialPurposePropertyType", "second_level": []},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        #! REQUIRED One of CommercializationTypeEnum (RENT, BUY, LEASE, LEASEHOLD)
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED
        {
            "first_level": "price",
            # Has to be compatible to commercializationType! If commercializationType = "RENT", than RENT or RENT_PER_SQM. If "BUY", than "PURCHASE".
            # Use _marketing_type_based_on_commercialization_type
            "second_level": ["value", "currency", "marketingType"],
        },
        #! REQUIRED use the property.plot_area
        {"first_level": "totalFloorSpace", "second_level": []},
        #! REQUIRED use the property.built_area
        {"first_level": "netFloorSpace", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        # OPTIONAL property.built_year (use get_built_year)
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.parking_spaces
        {"first_level": "numberOfParkingSpaces", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.INVESTMENT: [
        #! REQUIRED
        {"first_level": "price", "second_level": ["value", "currency"]},
        # OPTIONAL property.plot_area (use get_built_year)
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED use the property.built_area
        {"first_level": "netFloorSpace", "second_level": []},
        #! REQUIRED One of InvestmentTypeEnum
        {"first_level": "investmentType", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
        # OPTIONAL YES/NOT_APPLICABLE (use property_has_elevator)
        {"first_level": "lift", "second_level": []},
        #! REQUIRED
        {"first_level": "constructionYear", "second_level": []},
        # OPTIONAL property.total_floors
        {"first_level": "numberOfFloors", "second_level": []},
    ],
    InmobilienScout24PropertyTypeEnum.TRADE_SITE: [
        #! REQUIRED One of CommercializationTypeEnum (RENT, BUY, LEASE, LEASEHOLD)
        {"first_level": "commercializationType", "second_level": []},
        #! REQUIRED: utilizationTradeSite = LEISURE, AGRICULTURE_FORESTRY, TRADE
        {"first_level": "utilizationTradeSite", "second_level": []},
        #! REQUIRED
        {
            "first_level": "price",
            "second_level": ["value", "currency", "marketingType"],
        },
        #! REQUIRED
        {"first_level": "plotArea", "second_level": []},
        #! REQUIRED
        {"first_level": "courtage", "second_level": ["hasCourtage", "courtage"]},
    ],
}

marketing_type_based_on_commercialization_type = {
    CommercializationTypeEnum.BUY: "PURCHASE",
    CommercializationTypeEnum.RENT: "RENT",
}

commercialization_type_based_on_listing_type = {
    ListingTypeEnum.SALE: CommercializationTypeEnum.BUY,
    ListingTypeEnum.RENT_LONG: CommercializationTypeEnum.RENT,
    ListingTypeEnum.RENT_SHORT: CommercializationTypeEnum.RENT,
}


_strand_property_type_to_specifictype_mapper = {
    # House building types
    "Bungalow": HouseBuildingTypeEnum.BUNGALOW.value,
    "Castle": HouseBuildingTypeEnum.CASTLE_MANOR_HOUSE.value,
    "Chalet": HouseBuildingTypeEnum.OTHER.value,
    "Cortijo": HouseBuildingTypeEnum.OTHER.value,
    "Country House": HouseBuildingTypeEnum.OTHER.value,
    "Finca": HouseBuildingTypeEnum.OTHER.value,
    "House_Houses": HouseBuildingTypeEnum.SINGLE_FAMILY_HOUSE.value,
    "Mansion": HouseBuildingTypeEnum.SPECIAL_REAL_ESTATE.value,
    "Palace": HouseBuildingTypeEnum.SPECIAL_REAL_ESTATE.value,
    "Riad": HouseBuildingTypeEnum.OTHER.value,
    "Semi Detached House": HouseBuildingTypeEnum.SEMIDETACHED_HOUSE.value,
    "Semi Detached Villa": HouseBuildingTypeEnum.VILLA.value,
    "Villa": HouseBuildingTypeEnum.VILLA.value,
    # Gastronomy types
    "Bar": GastronomyTypeEnum.BAR_LOUNGE.value,
    "Discotheque": GastronomyTypeEnum.CLUB_DISCO.value,
    "Hotel": GastronomyTypeEnum.HOTEL.value,
    "Restaurant": GastronomyTypeEnum.RESTAURANT.value,
    # Investment types
    "Boat": InvestmentTypeEnum.OTHER.value,
    "Building": InvestmentTypeEnum.COMMERCIAL_BUILDING.value,
    "Business": InvestmentTypeEnum.OTHER.value,
    "Estate": InvestmentTypeEnum.HOUSING_ESTATE.value,
    "Golf Course": InvestmentTypeEnum.OTHER.value,
    "Golf Plot": InvestmentTypeEnum.OTHER.value,
    "Hotel Plot": InvestmentTypeEnum.HOTEL.value,
    "House_Plots and Lands": InvestmentTypeEnum.HOUSING_ESTATE.value,
    "Investment": InvestmentTypeEnum.OTHER.value,
    "Mooring": InvestmentTypeEnum.OTHER.value,
    "Shopping Centre": InvestmentTypeEnum.SHOPPING_CENTRE.value,
    # Office types
    "Loft": OfficeTypeEnum.LOFT.value,
    "Office": OfficeTypeEnum.OFFICE.value,
    "Office Units": OfficeTypeEnum.OFFICE.value,
    # Special purpose types
    "Unique Building": SpecialPurposeTypeEnum.SPECIAL_ESTATE.value,
    # Garage types
    "Parking": GarageTypeEnum.GARAGE,
}


"""
Maps from Property Type (Strand) to InmobilienScout24 property types
"""
_property_type_mapper = {
    "Apartment/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Apartment/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Bar/Sale": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Bar/Rent": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Boat/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Boat/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Building/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Building/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Bungalow/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Bungalow/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Business/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Business/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Castle/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Castle/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Chalet/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Chalet/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Commercial Other/Sale": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Commercial Other/Rent": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Commercial Premises/Sale": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Commercial Premises/Rent": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Cortijo/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Cortijo/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Country House/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Country House/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Development Land/Sale": InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE,
    "Development Land/Rent": InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE,
    "Discotheque/Sale": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Discotheque/Rent": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Duplex/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Duplex/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Duplex Penthouse/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Duplex Penthouse/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Estate/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Estate/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Finca/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Finca/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Flat/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Flat/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Golf Course/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Golf Course/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Golf Plot/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Golf Plot/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Ground Floor Apartment/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Ground Floor Apartment/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Ground Floor Duplex/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Ground Floor Duplex/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Hotel/Sale": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Hotel/Rent": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Hotel Plot/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Hotel Plot/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "House_Houses/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "House_Houses/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "House_Plots and Lands/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "House_Plots and Lands/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Industrial Land/Sale": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Industrial Land/Rent": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Industrial Premises/Sale": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Industrial Premises/Rent": InmobilienScout24PropertyTypeEnum.TRADE_SITE,
    "Investment/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Investment/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Loft/Sale": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Loft/Rent": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Mansion/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Mansion/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Mooring/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Mooring/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Office/Sale": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Office/Rent": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Office Units/Sale": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Office Units/Rent": InmobilienScout24PropertyTypeEnum.OFFICE,
    "Palace/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Palace/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Parking/Sale": InmobilienScout24PropertyTypeEnum.GARAGE_BUY,
    "Parking/Rent": InmobilienScout24PropertyTypeEnum.GARAGE_RENT,
    "Penthouse/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Penthouse/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Plot/Sale": InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE,
    "Plot/Rent": InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE,
    "Residential Plot/Sale": InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE,
    "Residential Plot/Rent": InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE,
    "Restaurant/Sale": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Restaurant/Rent": InmobilienScout24PropertyTypeEnum.GASTRONOMY,
    "Riad/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Riad/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Rustic Plot/Sale": InmobilienScout24PropertyTypeEnum.LIVING_BUY_SITE,
    "Rustic Plot/Rent": InmobilienScout24PropertyTypeEnum.LIVING_RENT_SITE,
    "Semi Detached House/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Semi Detached House/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Semi Detached Villa/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Semi Detached Villa/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
    "Shop/Sale": InmobilienScout24PropertyTypeEnum.STORE,
    "Shop/Rent": InmobilienScout24PropertyTypeEnum.STORE,
    "Shopping Centre/Sale": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Shopping Centre/Rent": InmobilienScout24PropertyTypeEnum.INVESTMENT,
    "Store Room/Sale": InmobilienScout24PropertyTypeEnum.STORE,
    "Store Room/Rent": InmobilienScout24PropertyTypeEnum.STORE,
    "Studio/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Studio/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Supermarket/Sale": InmobilienScout24PropertyTypeEnum.STORE,
    "Supermarket/Rent": InmobilienScout24PropertyTypeEnum.STORE,
    "Town House/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Town House/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Triplex/Sale": InmobilienScout24PropertyTypeEnum.APARTMENT_BUY,
    "Triplex/Rent": InmobilienScout24PropertyTypeEnum.APARTMENT_RENT,
    "Unique Building/Sale": InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE,
    "Unique Building/Rent": InmobilienScout24PropertyTypeEnum.SPECIAL_PURPOSE,
    "Villa/Sale": InmobilienScout24PropertyTypeEnum.HOUSE_BUY,
    "Villa/Rent": InmobilienScout24PropertyTypeEnum.HOUSE_RENT,
}


def map_listing_type_to_inmobilienscout24_price_propertytype_specifictype_and_listing_type(
    *,
    property: Property,
) -> tuple[str, InmobilienScout24PropertyTypeEnum, Optional[str], ListingTypeEnum]:
    listing_types_names = [listing_type.name for listing_type in property.listing_types]

    strand_property_type_name = property._property_type.name
    # This is needed because we have two property types with the same name ("House")
    # but from different categories ("Houses" and "Plots and Lands")
    if strand_property_type_name == "House":
        strand_property_type_name = (
            f"{strand_property_type_name}_{property.property_type_category}"
        )

    specific_type: Optional[str] = _strand_property_type_to_specifictype_mapper.get(
        strand_property_type_name, None
    )

    if ListingTypeEnum.SALE in listing_types_names:
        inmobilienscout_property_type = _property_type_mapper[
            f"{strand_property_type_name}/Sale"
        ]
        return (
            f"{property.price_sale}",
            inmobilienscout_property_type,
            specific_type,
            ListingTypeEnum.SALE,
        )
    elif ListingTypeEnum.RENT_LONG in listing_types_names:
        inmobilienscout_property_type = _property_type_mapper[
            f"{strand_property_type_name}/Rent"
        ]
        return (
            f"{property.price_rent_long_term}",
            inmobilienscout_property_type,
            specific_type,
            ListingTypeEnum.RENT_LONG,
        )
    elif ListingTypeEnum.RENT_SHORT in listing_types_names:
        inmobilienscout_property_type = _property_type_mapper[
            f"{strand_property_type_name}/Rent"
        ]
        return (
            f"{property.price_rent_short_term}",
            inmobilienscout_property_type,
            specific_type,
            ListingTypeEnum.RENT_SHORT,
        )
    else:
        raise Exception(
            f"Property {property.reference} hasn't any type of price. This should not happen."
        )


def _property_has_commission(*, property: Property) -> bool:
    return True if property.commission else False


def property_has_courtage(*, property: Property) -> InmobilienScoutHasCourtage:
    has_commission = _property_has_commission(property=property)

    return (
        InmobilienScoutHasCourtage.YES
        if has_commission
        else InmobilienScoutHasCourtage.NO
    )


# https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3#Officially_assigned_code_elements
_iso3166_country_code_mapper = {"Spain": "ESP", "Finland": "FIN,"}


class LocationInfo(BaseModel):
    address: Optional[str]
    postcode: Optional[str]
    city: str
    country: str
    countryISO3166: str
    region: str


def get_location_info(*, property: Property) -> LocationInfo:
    private_info = property.private_info

    location = private_info["location"]
    city = property._area_level_1.name
    province = property._area_level_1.province

    location_address: Optional[str] = location.get("address", None)
    location_postcode: Optional[str] = location.get("postCode", None)
    location_city = city
    location_country = cast(str, property.country) or "Spain"
    country_iso3166_code = _iso3166_country_code_mapper[location_country]
    region = _get_inmobilienscout_region_based_on_province(province=province, city=city)

    return LocationInfo(
        address=location_address[:100] if location_address else None,
        postcode=location_postcode,
        city=location_city,
        country=location_country,
        countryISO3166=country_iso3166_code,
        region=region,
    )


AIR_CONDITIONING_FEATURES = ["Air Conditioning", "Individual A/C Units"]
ALL_HANDICAP_FEATURES = ["Handicap Accessible"]
ALL_RAMP_FEATURES = ["Disabled Access"]
ALL_ELEVATOR_FEATURES = ["Lift"]
ALL_KITCHEN_FEATURES = [
    "Fitted Kitchen",
    "Kitchenette",
    "Open Plan Kitchen",
    "Fully fitted kitchen",
    "Kitchen equipped",
]
BALCONY_FEATURES = ["Balcony"]
TERRACE_FEATURES = [
    "Terrace",
    "Uncovered terrace",
    "Covered Terrace",
    "Private Terrace",
]


def property_has_features(*, property: Property, features: List[str]) -> bool:
    has_feature = next(
        (feature for feature in property.features if feature.name in features),
        None,
    )

    return True if has_feature else False


def property_has_elevator(*, property: Property) -> bool:
    has_elevator = property.building_has_elevator
    has_lift = property_has_features(property=property, features=ALL_ELEVATOR_FEATURES)

    if has_elevator or has_lift:
        return True

    return False


def get_built_year(*, property: Property) -> int:
    original_value = property.built_year
    if not original_value:
        raise Exception("Built year is empty")
    string_value = str(property.built_year)
    if len(string_value) != 4:
        raise Exception("Built year must have 4 digits")

    return original_value


def get_inmobilien_scout24_type(
    property: Property,
) -> InmobilienScout24PropertyTypeEnum:
    (_, inmobilien_scout_property_type, _, _) = (
        map_listing_type_to_inmobilienscout24_price_propertytype_specifictype_and_listing_type(
            property=property
        )
    )
    return inmobilien_scout_property_type


def get_inmobilien_publish_channel_id() -> str:
    return PublishChannelsEnum.INMOBILIEN_SCOUT_24_DE.value


def get_customer_homepage_publish_channel_id() -> str:
    return PublishChannelsEnum.CUSTOMER_HOMEPAGE.value


def _get_inmobilienscout_region_based_on_province(
    province: str, city: str
) -> InmobilienScout24Region:
    match city:
        case "Mallorca":
            return InmobilienScout24Region.MALLORCA
        case "Madrid":
            return InmobilienScout24Region.MADRID
        case "Menorca":
            return InmobilienScout24Region.MENORCA
        case "Valencia":
            return InmobilienScout24Region.VALENCIA

    # Got from https://www.travelrepublic.co.uk/blog/spain-the-regions
    match province:
        case (
            "Cadiz"
            | "Ceuta"
            | "Cordoba"
            | "Granada"
            | "Malaga"
            | "Sevilla"
            | "Huelva"
            | "Jaen"
            | "Almeria"
            | "Melilla"
        ):
            return InmobilienScout24Region.ANDALUSIA
        case "Huesca" | "Teruel" | "Zaragoza":
            return InmobilienScout24Region.ARAGON
        case "Asturias":
            return InmobilienScout24Region.ASTURIAS
        case "Islas Baleares":
            return InmobilienScout24Region.MALLORCA
        case "Alava" | "Guipuzcoa" | "Vizcaya":
            return InmobilienScout24Region.BASQUE_COUNTRY
        case "Las Palmas" | "Santa Cruz de Tenerife":
            return InmobilienScout24Region.GRAN_CANARIA
        case "Cantabria":
            return InmobilienScout24Region.CANTABRIA
        case "Guadalajara" | "Toledo" | "Cuenca" | "Ciudad Real" | "Albacete":
            return InmobilienScout24Region.CASTILLA_LA_MANCHA
        case (
            "Leon"
            | "Palencia"
            | "Burgos"
            | "Zamora"
            | "Valladolid"
            | "Segovia"
            | "Soria"
            | "Salamanca"
            | "Avila"
        ):
            return InmobilienScout24Region.CASTILLA_LEON
        case "Barcelona" | "Girona" | "Lleida" | "Tarragona":
            return InmobilienScout24Region.CATALONIA
        case "Caceres" | "Badajoz":
            return InmobilienScout24Region.EXTREMADURA
        case "La Coruna" | "Pontevedra" | "Lugo" | "Orense":
            return InmobilienScout24Region.GALICIA
        case "La Rioja":
            return InmobilienScout24Region.LA_RIOJA
        case "Murcia":
            return InmobilienScout24Region.MURCIA
        case "Navarra":
            return InmobilienScout24Region.NAVARRA
        case "Valencia" | "Castellon" | "Alicante":
            return InmobilienScout24Region.VALENCIA
        case _:
            raise Exception("Could not get region")


def get_description_with_priority_to_german(
    descriptions: List[PropertyDescription],
) -> PropertyDescription:
    german_description = []
    english_description = []
    other_description = []
    for desc in descriptions:
        if not desc.tagline:
            continue

        if desc.language == Language.GERMAN.value:
            if desc.type != DescriptionType.FULL:
                german_description.append(desc)
            else:
                german_description = [desc]
        elif desc.language == Language.ENGLISH.value:
            if desc.type != DescriptionType.FULL:
                english_description.append(desc)
            else:
                english_description = [desc]
        elif desc.type != DescriptionType.FULL:
            other_description.append(desc)
        else:
            other_description = [desc]

    description = None
    if german_description:
        description = german_description[0]
    elif english_description:
        description = english_description[0]
    elif other_description:
        description = other_description[0]

    return description
