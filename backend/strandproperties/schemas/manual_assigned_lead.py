from datetime import datetime
from enum import Enum
from typing import Any, List, Optional

from pydantic import Field, validator

from strandproperties.constants import SortEnum
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactIdName
from strandproperties.schemas.param import TempPaginationParam
from strandproperties.schemas.sale_activity import SaleActivity
from strandproperties.schemas.user import UserBasicInfo


class ManualLeadSortColumn(str, Enum):
    SOURCE = "source"
    STATUS = "status"
    CREATED = "created"
    FULL_NAME = "full_name"


class ManualAssignedLeadFilterParam(TempPaginationParam):
    status: list[str] | None = None
    organizations: list[str] | None = None
    sort_column: Optional[ManualLeadSortColumn] = None
    sort_direction: Optional[SortEnum] = None

    @validator("sort_column", "sort_direction", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class ManualAssignedLeadRead(BaseSchema):
    id: Optional[int]
    campaign_name: Optional[str]
    status: Optional[str]
    source: Optional[str]
    contact: Optional[ContactIdName] = None
    property_reference: Optional[str]
    selections: Optional[List[str]] = []
    sale_activity: Optional[SaleActivity] = None
    content: Optional[Any] = None
    assigned_to_users: Optional[list[UserBasicInfo]] = []
    organization: str | None
    created_at: datetime
    updated_at: datetime


class ManualAssignedLeadAllocate(BaseSchema):
    assigned_to: List[int]


class ManualAssignedLeadParam(BaseSchema):
    id: int = Field(..., json_schema_extra={"param_in": "path"})
