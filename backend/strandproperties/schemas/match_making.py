from datetime import datetime
from enum import StrEnum
from typing import List, Optional

from pydantic import Field

from strandproperties.constants import (
    CountryCode,
    LeadRelevance,
    LeadStatus,
    TitleValidation,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactRead
from strandproperties.schemas.param import PropertyFilterParam
from strandproperties.schemas.user import UserListRead, UserRead
from strandproperties.schemas.utils import EmptyStrToNone


class EmailTemplateRead(BaseSchema):
    id: int
    name: Optional[str] = None


class MatchMakingBase(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime
    title: str
    notes: Optional[str]
    params: str
    assigned_to_users: List[UserRead]
    contacts: List[ContactRead]


class MatchMakingRead(MatchMakingBase):
    lead_id: Optional[int]
    email_template: Optional[EmailTemplateRead]
    is_auto_sent: Optional[bool]
    new_user_created_id: Optional[int] = None


class MatchMakingListRead(MatchMakingBase):
    class LeadMatchMakingRead(BaseSchema):
        id: int
        title: str
        created_at: datetime
        relevance: Optional[LeadRelevance]
        assigned_to_users: List[UserListRead]
        contacts: List[ContactRead]
        status: LeadStatus

    lead: Optional[LeadMatchMakingRead] = None


class MatchMakingReadDetail(MatchMakingRead):
    filters: Optional[PropertyFilterParam] = None


class MatchMakingCreateEdit(BaseSchema):
    title: str = TitleValidation
    notes: Optional[str] = None
    params: str = Field(
        ...,
        description="Match making parameters",
        min_length=1,
    )
    lead_id: Optional[int] = None
    email_template_id: Optional[int] = None
    assigned_users: Optional[List[int]] = []
    contact_ids: Optional[List[int]] = []
    property_ids: Optional[List[int]] = []


class MatchMakingSubscriptionCreate(BaseSchema):
    email: str
    name: str
    location: CountryCode
    filters: Optional[dict] = {}


class MatchMakingPropertyCreateEdit(BaseSchema):
    is_favorite: Optional[bool] = None


class MatchMakingEmailEdit(BaseSchema):
    is_auto_sent: bool


class MatchMakingTrackCreateEdit(BaseSchema):
    match_making_id: int
    realtor_id: int
    property_reference: str


class MatchMakingTrackRead(BaseSchema):
    match_making_id: int
    realtor_id: int
    property_id: int
    click_count: int


class EmailCreate(BaseSchema):
    subject: Optional[EmptyStrToNone]
    recipient_ids: Optional[List[int]] = []
    group_ids: Optional[List[int]] = []
    body: str
    email_template_name: str
    is_all_contact: bool = False


class EmailBody(BaseSchema):
    content_type: str
    content: str


class EmailAddress(BaseSchema):
    name: str
    address: str


class EmailSender(BaseSchema):
    email_address: EmailAddress


class EmailRecipient(BaseSchema):
    email_address: EmailAddress


class EmailRead(BaseSchema):
    subject: Optional[EmptyStrToNone]
    body: EmailBody
    body_preview: str
    sender: EmailSender
    to_recipients: list[EmailRecipient]
    sent_date_time: str


class MailgunEmailTemplate(BaseSchema):
    name: str
    description: str
    created_at: str
    created_by: str
    id: str


class MailgunEmailTemplateVersion(BaseSchema):
    id: str
    version_tag: str
    subject: str


class EmailTemplateCreateEdit(BaseSchema):
    uuid: str
    version_tag: Optional[str] = None
    subject: Optional[str] = None
    name: Optional[str] = None


class EmailStatus(StrEnum):
    OPEN = "opened"
    DELIVERED = "delivered"
    ACCEPTED = "accepted"
    CLICKED = "clicked"


class MatchMakingEmailInsights(BaseSchema):
    email_id: str
    time: str
    status: EmailStatus
