from datetime import datetime

from strandproperties.constants import ActivityObjectType
from strandproperties.schemas.base import BaseSchema


class Description(BaseSchema):
    description: str


class LogActivityCreateEdit(BaseSchema):
    description: Description
    object_id: int
    object_type: ActivityObjectType


class LogActivityRead(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime
    description: Description
    object_id: int
    object_type: ActivityObjectType
    actor_id: int
