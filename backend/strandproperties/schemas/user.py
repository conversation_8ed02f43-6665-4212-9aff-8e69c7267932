from datetime import date, datetime
from enum import StrEnum
from typing import List, Literal, Optional, Union

from pydantic import EmailStr, model_validator, root_validator
from typing_extensions import Self

from strandproperties.constants import (
    ROLE_TAG_KEYWORDS,
    CompanyType,
    CountryCode,
    Currency,
    Language,
    MostSpokenLanguages,
    RoleType,
    TranslationsLanguages,
)
from strandproperties.schemas.base import BaseSchema, PaginatedList
from strandproperties.schemas.company import CompanyRead
from strandproperties.schemas.office import OfficeRead
from strandproperties.schemas.organization import OrganizationRead
from strandproperties.schemas.param import TempPaginationParam
from strandproperties.schemas.tag import TagRead
from strandproperties.schemas.utils import EmptyStrToNone, StripNotEmptyStr


class RoleRead(BaseSchema):
    role: RoleType
    organization: OrganizationRead | None


class UserSocialDetails(BaseSchema):
    tiktok: Optional[str] = None
    facebook: Optional[str] = None
    instagram: Optional[str] = None
    linkedin: Optional[str] = None


class CompanyDetail(BaseSchema):

    company_name: str
    company_id: str
    street_address: str
    city: str
    country: str
    postal_code: str
    iban: str
    bic: str
    company_email: str
    company_phone: str


class UserRoleEditItem(BaseSchema):
    organization_id: int
    role: Optional[RoleType] = None


class UserCreate(BaseSchema):
    first_name: str
    last_name: str
    email: str
    phone_number: Optional[str] = None
    social_security_number: Optional[str] = None
    roles: List[UserRoleEditItem]


class UserReadWithSSO(BaseSchema):
    email: str
    cognito_sub: Optional[str] = None


class PropertyViewMode(StrEnum):
    LIST = "LIST"
    GRID = "GRID"


class PropertyOpenMode(StrEnum):
    FULL_VIEW = "FULL_VIEW"
    PEEK_MODE = "PEEK_MODE"


class ChangePreviewModePayload(BaseSchema):
    property_view_mode: Optional[PropertyViewMode] = None
    property_open_mode: Optional[PropertyOpenMode] = None


class UserDetails(BaseSchema):
    company: CompanyDetail | None = None
    company_type: Union[CompanyType, Literal[""]] = ""

    description: Optional[str] = None
    position: Optional[str] = None
    languages: Optional[List[MostSpokenLanguages]] = None
    social: Optional[UserSocialDetails] = None
    preferred_language: Optional[TranslationsLanguages] = None
    serviceform_booking_calendar_id: Optional[str] = None
    videobot_id: Optional[str] = None
    company_reference: Optional[str] = None
    roaiib: Optional[str] = None
    liability_insurance: Optional[str] = None
    property_view_mode: Optional[PropertyViewMode] = None
    property_open_mode: Optional[PropertyOpenMode] = None

    @model_validator(mode="after")
    def validate_data_by_company_type(self) -> Self:
        if not self.company_type:
            self.company = None
        elif not self.company:
            raise ValueError("Missing company detail data")
        return self


class UserPropertiesRead(BaseSchema):
    id: int
    reference: str


class UserOrganizationsRead(BaseSchema):
    id: int
    name: str
    country_code: CountryCode
    currency: Currency
    language: Language


class UserListRead(BaseSchema):
    id: int
    first_name: str
    last_name: str
    email: str
    phone_number: Optional[str] = None
    social_security_number: Optional[str] = None
    is_superadmin: bool
    is_active: bool
    is_verified: bool
    photo_url: Optional[str] = None
    details: UserDetails
    tags: List[TagRead]
    offices: List[OfficeRead]
    roles: list[RoleRead]
    team_name: Optional[str] = None
    reference_number: Optional[str] = None
    company: Optional[CompanyRead] = None
    has_dias_api_key: bool
    roaiib: Optional[str] = None
    liability_insurance: Optional[str] = None
    stripe_customer_id: Optional[str] = None


class UserBasicInfo(BaseSchema):
    id: int
    first_name: str
    last_name: str
    photo_url: Optional[str] = None


class UserRead(UserListRead):
    properties: List[UserPropertiesRead]
    organizations: List[UserOrganizationsRead]
    created_at: datetime
    updated_at: datetime


class UserFilterParam(TempPaginationParam):
    keyword: Optional[str] = None
    tag_ids: Optional[str] = None
    only_active: Optional[bool] = None
    organization_id: Optional[int] = None


class UserEdit(BaseSchema):
    first_name: Optional[StripNotEmptyStr] = None
    last_name: Optional[StripNotEmptyStr] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[EmptyStrToNone] = None
    social_security_number: Optional[EmptyStrToNone] = None
    photo_url: Optional[EmptyStrToNone] = None
    details: Optional[UserDetails] = None
    new_tags: Optional[List[str]] = None
    existing_tags: Optional[List[int]] = None
    new_offices: Optional[List[str]] = None
    existing_offices: Optional[List[int]] = None
    team_name: Optional[str] = None
    company_id: Optional[int] = None
    dias_api_key: Optional[str] = None
    roaiib: Optional[str] = None
    liability_insurance: Optional[str] = None


class UserRoleEdit(BaseSchema):
    roles: List[UserRoleEditItem]


class RealtorListRead(BaseSchema):
    id: int
    first_name: str
    last_name: str
    email: str
    start_date: date
    end_date: date | None
    status: str
    office: str
    role: str

    @root_validator(pre=True)
    def pre_process_data(cls, values):
        values.office = ", ".join(
            [val for val in values.offices_name if "office" in val.lower()]
        )
        values.role = ", ".join(
            [
                val
                for val in values.offices_name
                if any(keyword in val.lower() for keyword in ROLE_TAG_KEYWORDS)
            ]
        )
        values.start_date = values.created_at.date()
        if values.is_active:
            values.end_date = None
            values.status = "active"
        else:
            values.end_date = values.updated_at.date()
            values.status = "deactivated"

        return values


# alias, required for schema discovery
UserList = PaginatedList[UserListRead]
UserList = PaginatedList[UserListRead]
