from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import Field, validator

from strandproperties.constants import SortEnum, TitleValidation
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactListRead
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.tag import TagRead
from strandproperties.schemas.user import UserBasicInfo
from strandproperties.schemas.utils import EmptyStrToNone


class GroupListRead(BaseSchema):
    # fix me later with less repeaded definition
    class GroupListReadContact(BaseSchema):
        id: int
        name: str
        email: Optional[str]
        company: Optional[str]
        website: Optional[str]
        address: Optional[str]
        city: Optional[str]
        post_code: Optional[str]
        country: Optional[str]
        preferred_language: Optional[str]
        passport_number: Optional[str]
        nationality: Optional[str]
        country_specific: Optional[dict] = {}
        notes: Optional[str]

    id: int
    created_at: datetime
    updated_at: datetime
    assigned_to_users: List[UserBasicInfo]
    contacts: List[GroupListReadContact]
    name: str = TitleValidation
    description: Optional[EmptyStrToNone]
    language: Optional[EmptyStrToNone]
    tags: list[TagRead]


class GroupRead(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime
    assigned_to_users: List[UserBasicInfo]
    contacts: List[ContactListRead]
    name: str = TitleValidation
    description: Optional[EmptyStrToNone]
    language: Optional[EmptyStrToNone]
    tags: list[TagRead]


class GroupCreateEdit(BaseSchema):
    assigned_to_users: Optional[List[int]] = []
    contact_ids: Optional[List[int]] = []
    name: str = TitleValidation
    description: Optional[EmptyStrToNone] = None
    language: Optional[EmptyStrToNone] = None
    new_tags: Optional[List[str]] = []
    existing_tags: Optional[List[int]] = []


class GroupSortColumn(str, Enum):
    NAME = "name"


class GroupFilterParam(PaginationParam):
    keyword: Optional[str] = None
    tag_ids: Optional[str] = None
    contact_id: Optional[int] = None
    sort_column: Optional[GroupSortColumn] = None
    sort_direction: Optional[SortEnum] = None

    @validator("sort_column", "sort_direction", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v
