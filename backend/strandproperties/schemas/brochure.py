from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import Field

from strandproperties.models.brochure import BrochureLanguage, CoverTheme, ImagePosition
from strandproperties.schemas.base import BaseSchema


class CoverPageType(int, Enum):
    LOGO_ONLY = 1
    IMAGE_WITH_LOGO = 2
    IMAGE_ONLY = 3


class ImageOrientation(str, Enum):
    VERTICAL = "vertical"
    HORIZONTAL = "horizontal"
    SQUARE = "square"


POSITION_ORIENTATION_CONSTRAINTS = {
    ImagePosition.FULL_PAGE: ImageOrientation.VERTICAL,
    ImagePosition.TOP: ImageOrientation.HORIZONTAL,
    ImagePosition.BOTTOM: ImageOrientation.HORIZONTAL,
    ImagePosition.LEFT: ImageOrientation.VERTICAL,
    ImagePosition.RIGHT: ImageOrientation.VERTICAL,
    ImagePosition.TOP_LEFT: ImageOrientation.VERTICAL,
    ImagePosition.TOP_RIGHT: ImageOrientation.VERTICAL,
    ImagePosition.BOTTOM_LEFT: ImageOrientation.VERTICAL,
    ImagePosition.BOTTOM_RIGHT: ImageOrientation.VERTICAL,
}


class BrochureRead(BaseSchema):
    id: int
    property_id: int
    realtor_id: int
    theme: Optional[CoverTheme] = None
    language: Optional[BrochureLanguage] = None
    version_number: int
    is_complete: bool
    property_information_version_id: Optional[int] = None
    property_images_version_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime


class BrochureInformationRead(BaseSchema):
    id: int
    property_description: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class BrochureInformationCreate(BaseSchema):
    property_description: Optional[str] = None


class ImageWithPosition(BaseSchema):
    image_url: str
    position: ImagePosition


class PropertyImagePage(BaseSchema):
    page_number: int = Field(ge=1)
    images: List[ImageWithPosition]


class BrochureImagesCreate(BaseSchema):
    images_data: List[PropertyImagePage]


class GenerateBrochureRequest(BaseSchema):
    property_id: int
    realtor_id: int
    is_complete: bool = False

    theme: Optional[CoverTheme] = CoverTheme.WHITE
    language: Optional[BrochureLanguage] = BrochureLanguage.FI

    include_cover: bool = True
    cover_page_type: Optional[CoverPageType] = CoverPageType.LOGO_ONLY

    property_blueprint_image: Optional[str] = None
    property_images_data: Optional[BrochureImagesCreate] = None
    property_information_data: Optional[BrochureInformationCreate] = None
