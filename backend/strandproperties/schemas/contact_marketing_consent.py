from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from strandproperties.schemas.base import BaseSchema, PaginatedList


class NewsletterType(str, Enum):
    FI_NEWSLETTER = "fi_newsletter"
    ES_NEWSLETTER = "es_newsletter"
    ES_MARBELLA_NEWSLETTER = "es_marbella_newsletter"


class ContactMarketingConsentStatusBase(BaseSchema):
    contact_id: int
    newsletter_type: NewsletterType
    is_subscribed: bool


class ContactMarketingConsentStatusCreate(ContactMarketingConsentStatusBase):
    pass


class ContactMarketingConsentStatusUpdate(BaseSchema):
    is_subscribed: bool


class ContactMarketingConsentStatusResponse(ContactMarketingConsentStatusBase):
    id: int
    updated_at: datetime
    created_at: datetime


class ContactMarketingConsentStatusList(PaginatedList):
    data: List[ContactMarketingConsentStatusResponse]


class TokenPayload(BaseSchema):
    email: str
    user_id: int
    exp: datetime


class ConsentUpdateRequest(BaseSchema):
    consents: Dict[str, bool]
