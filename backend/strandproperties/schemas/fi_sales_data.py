from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_purchase_offer import FIPurchaseOfferRead
from strandproperties.schemas.fi_sales_agreement import FISalesAgreementRead
from strandproperties.schemas.fi_details_of_sale import FIDetailsOfSaleRead


class FISalesDataRead(BaseSchema):
    property_id: int
    purchase_offers: list[FIPurchaseOfferRead]
    sales_agreements: list[FISalesAgreementRead]
    details_of_sales: list[FIDetailsOfSaleRead]
