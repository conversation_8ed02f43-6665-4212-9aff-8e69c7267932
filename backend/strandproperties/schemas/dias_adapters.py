from typing import List, Optional
from strandproperties.schemas.dias_common import (
    BuyerOrganization,
    BuyerPerson,
    DiasBuyer,
    DiasSeller,
    InternalBuyer,
    InternalSeller,
    InvoiceVerificationType,
    ParticipantType,
    SellerOrganization,
    SellerPerson,
    SellerRealtorPayment,
    TransferTax,
)
from strandproperties.schemas.dias_property_trade import (
    StrandConfigRequest,
    DiasConfigRequest,
    StrandConfigResponse,
    DiasConfigResponse,
    DownPayment,
    Kasiraha,
    LuovutuksenEhdot,
    TermsOfTransfer,
)
from strandproperties.schemas.dias_shared_trade import (
    DiasPaperTrade,
    StrandSharedTrade,
)


class DownPaymentAdapter:
    @staticmethod
    def dias_to_strand(kasiraha: Optional[Kasiraha]) -> Optional[DownPayment]:
        if kasiraha is None:
            return None
        return DownPayment(
            amount_in_cents=kasiraha.amountInCents,
            payment_date=kasiraha.maksupaivamaara,
        )

    @staticmethod
    def strand_to_dias(down_payment: Optional[DownPayment]) -> Optional[Kasiraha]:
        if down_payment is None:
            return None
        return Kasiraha(
            amountInCents=down_payment.amount_in_cents,
            maksupaivamaara=down_payment.payment_date,
        )


# TODO: Add tests
class ConfigRequestAdapter:
    @staticmethod
    def dias_to_strand(dias_config: DiasConfigRequest) -> StrandConfigRequest:
        return StrandConfigRequest(
            pledge_certificate_codes=dias_config.panttikirjojenKoodit,
            non_transferable_pledge_certificate_codes=dias_config.eiSiirrettavatPanttikirjojenKoodit,
            buyer_bank_business_id=dias_config.buyerBankBusinessId,
            penalty_for_contract_termination=dias_config.sopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana,
            institution_code=dias_config.laitostunnus,
            down_payment=DownPaymentAdapter.dias_to_strand(dias_config.kasiraha),
            transfer_target=dias_config.luovutuksenKohde,
            seller_bank_business_id=dias_config.sellerBankBusinessId,
            purchase_price=dias_config.kauppahinta,
        )

    @staticmethod
    def strand_to_dias(strand_config: StrandConfigRequest) -> DiasConfigRequest:
        return DiasConfigRequest(
            panttikirjojenKoodit=strand_config.pledge_certificate_codes,
            eiSiirrettavatPanttikirjojenKoodit=strand_config.non_transferable_pledge_certificate_codes,
            buyerBankBusinessId=strand_config.buyer_bank_business_id,
            sopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana=strand_config.penalty_for_contract_termination,
            laitostunnus=strand_config.institution_code,
            kasiraha=DownPaymentAdapter.strand_to_dias(strand_config.down_payment),
            luovutuksenKohde=strand_config.transfer_target,
            sellerBankBusinessId=strand_config.seller_bank_business_id,
            kauppahinta=strand_config.purchase_price,
        )


# TODO: Add tests
class ConfigResponseAdapter:
    @staticmethod
    def dias_to_strand(dias_response: DiasConfigResponse) -> StrandConfigResponse:
        mandatory_deed_terms = [
            TermsOfTransfer(title=term.title, description=term.description)
            for term in dias_response.luovutuskirjanPakollisetEhdot
        ]

        options = dias_response.options

        return StrandConfigResponse(
            mandatory_deed_terms=mandatory_deed_terms, options=options
        )

    @staticmethod
    def strand_to_dias(strand_response: StrandConfigResponse) -> DiasConfigResponse:
        luovutuskirjanPakollisetEhdot = [
            LuovutuksenEhdot(title=term.title, description=term.description)
            for term in strand_response.mandatory_deed_terms
        ]
        options = strand_response.options
        return DiasConfigResponse(
            luovutuskirjanPakollisetEhdot=luovutuskirjanPakollisetEhdot, options=options
        )


# TODO: Add tests
class SharedTradeDraftAdapter:
    @staticmethod
    def dias_to_strand(dias_draft: DiasPaperTrade) -> StrandSharedTrade:
        return StrandSharedTrade(
            attachments=dias_draft.attachments,
            require_initiator_confirmation=dias_draft.requireInitiatorConfirmation,
            bill_of_sale=dias_draft.billOfSale,
            realtor_bank_account=dias_draft.realtorBankAccount,
            sellers=dias_draft.sellers,
            buyers=dias_draft.buyers,
            initiator_contact_info=dias_draft.initiatorContactInfo,
            initiator_person_id=dias_draft.initiatorPersonId,
            apartment=dias_draft.apartment,
            trade_initiated_timestamp=dias_draft.tradeInitiatedTimestamp,
            # Optional Fields
            initiator_trade_reference_id=dias_draft.initiatorTradeReferenceId,
            deadline_for_signing_bill_of_sale=dias_draft.deadlineForSigningBillOfSale,
            seller_share_certificate_status=dias_draft.sellerShareCertificateStatus,
            # buyers_mortgage_status=dias_draft.buyersMortgageStatus,  # TODO: Enable this after DIAS releases mortgage-free buyers
        )

    @staticmethod
    def strand_to_dias(strand_draft: StrandSharedTrade) -> DiasPaperTrade:
        sellers: List[DiasSeller] = (
            SharedTradeDraftAdapter.internal_sellers_to_dias_sellers(
                strand_draft.internal_sellers, strand_draft
            )
        )
        buyers: List[DiasBuyer] = (
            SharedTradeDraftAdapter.internal_buyers_to_dias_buyers(
                strand_draft.internal_buyers
            )
        )

        return DiasPaperTrade(
            attachments=strand_draft.attachments,
            requireInitiatorConfirmation=strand_draft.require_initiator_confirmation,
            billOfSale=strand_draft.bill_of_sale,
            realtorBankAccount=strand_draft.realtor_bank_account,
            sellers=sellers,
            buyers=buyers,
            initiatorContactInfo=strand_draft.initiator_contact_info,
            initiatorPersonId=strand_draft.initiator_person_id,
            apartment=strand_draft.apartment,
            tradeInitiatedTimestamp=strand_draft.trade_initiated_timestamp,
            # Optional Fields
            initiatorTradeReferenceId=strand_draft.initiator_trade_reference_id,
            deadlineForSigningBillOfSale=strand_draft.deadline_for_signing_bill_of_sale,
            sellerShareCertificateStatus=strand_draft.seller_share_certificate_status,
            # buyersMortgageStatus=strand_draft.buyers_mortgage_status,  # TODO: Enable this after DIAS releases mortgage-free buyers
        )

    def internal_sellers_to_dias_sellers(
        internal_sellers: List[InternalSeller], shared_trade: StrandSharedTrade
    ) -> List[DiasSeller]:
        sellers: List[DiasSeller] = []
        if not isinstance(internal_sellers, list):
            raise ValueError("Internal sellers are required")

        for internal_seller in shared_trade.internal_sellers:
            realtor_payment = None
            if internal_seller.hasRealtorPayment:
                if internal_seller.realtorPaymentAmountInEuros is None:
                    raise ValueError("Realtor payment amount is required")
                realtor_payment = SellerRealtorPayment(
                    amountInCents=round(
                        internal_seller.realtorPaymentAmountInEuros * 100
                    ),
                )
                if (
                    shared_trade.invoice_verification_type
                    == InvoiceVerificationType.INVOICE_REFERENCE_NUMBER
                ):
                    realtor_payment.referenceNumber = (
                        shared_trade.invoice_verification_value
                    )
                elif (
                    shared_trade.invoice_verification_type
                    == InvoiceVerificationType.INVOICE_MESSAGE
                ):
                    realtor_payment.message = shared_trade.invoice_verification_value
            if internal_seller.sellerType == ParticipantType.INDIVIDUAL:
                seller = DiasSeller(
                    person=SellerPerson(
                        firstName=internal_seller.firstName,
                        lastName=internal_seller.lastName,
                        email=internal_seller.email,
                        socialSecurityNumber=internal_seller.socialSecurityNumber,
                    ),
                    realtorPayment=realtor_payment,
                    bankBusinessId=internal_seller.bankBusinessId,
                )
                sellers.append(seller)
            elif internal_seller.sellerType == ParticipantType.ORGANIZATION:
                person = None
                if internal_seller.firstName and internal_seller.lastName:
                    person = SellerPerson(
                        firstName=internal_seller.firstName,
                        lastName=internal_seller.lastName,
                        email=internal_seller.email,
                        socialSecurityNumber=internal_seller.socialSecurityNumber,
                    )
                seller = DiasSeller(
                    organization=SellerOrganization(
                        name=internal_seller.organizationName,
                        businessId=internal_seller.organizationBusinessId,
                    ),
                    roles=internal_seller.roles,
                    bankBusinessId=internal_seller.bankBusinessId,
                    businessIds=internal_seller.businessIds,
                    realtorPayment=realtor_payment,
                    person=person,
                )
                sellers.append(seller)
        return sellers

    def internal_buyers_to_dias_buyers(
        internal_buyers: List[InternalBuyer],
    ) -> List[DiasBuyer]:
        buyers: List[DiasBuyer] = []
        if not isinstance(internal_buyers, list):
            raise ValueError("Internal buyers are required")

        for internal_buyer in internal_buyers:
            buyer = DiasBuyer(
                bankBusinessId=internal_buyer.bankBusinessId,
            )
            if (
                isinstance(internal_buyer.transferTaxAmountInEuros, float)
                and internal_buyer.transferTaxAmountInEuros > 0
            ):
                buyer.transferTax = TransferTax(
                    amountInCents=round(internal_buyer.transferTaxAmountInEuros * 100),
                )
            if internal_buyer.buyerType == ParticipantType.INDIVIDUAL:
                buyer.person = BuyerPerson(
                    firstName=internal_buyer.firstName,
                    lastName=internal_buyer.lastName,
                    email=internal_buyer.email,
                    socialSecurityNumber=internal_buyer.socialSecurityNumber,
                )
            elif internal_buyer.buyerType == ParticipantType.ORGANIZATION:
                person = None
                if internal_buyer.firstName and internal_buyer.lastName:
                    person = BuyerPerson(
                        firstName=internal_buyer.firstName,
                        lastName=internal_buyer.lastName,
                        email=internal_buyer.email,
                        socialSecurityNumber=internal_buyer.socialSecurityNumber,
                    )
                buyer.organization = BuyerOrganization(
                    name=internal_buyer.organizationName,
                    businessId=internal_buyer.organizationBusinessId,
                )
                buyer.person = person
                buyer.roles = internal_buyer.roles
                buyer.businessIds = internal_buyer.businessIds
            buyers.append(buyer)
        return buyers
