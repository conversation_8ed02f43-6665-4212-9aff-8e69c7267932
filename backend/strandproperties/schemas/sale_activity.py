from datetime import datetime
from typing import List, Optional
from strandproperties.constants import LeadStatus, LeadType, TitleValidation
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.user import UserBasicInfo
from strandproperties.schemas.utils import EmptyStrToNone


class SaleActivity(BaseSchema):
    id: int
    status: LeadStatus
    title: str = TitleValidation
    description: Optional[EmptyStrToNone]
    type: Optional[LeadType]
    source: Optional[str]
    assigned_to_users: Optional[List[UserBasicInfo]]
    created_at: datetime
