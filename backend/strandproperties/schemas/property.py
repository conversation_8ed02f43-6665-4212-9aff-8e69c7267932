from datetime import datetime
from typing import List, Optional

from pydantic import Field

from strandproperties.constants import (
    DEFAULT_CURRENCY,
    CommissionTypeEnum,
    EnergyCertificateRating,
    ImagesQuality,
    IVATaxEnum,
    SoldBy,
    Status,
)
from strandproperties.schemas.base import BaseSchema, PaginatedList
from strandproperties.schemas.contact import ContactIdName, ContactRead
from strandproperties.schemas.document import DocumentListRead
from strandproperties.schemas.fi_property.fi_realty import FIRealtyListRead
from strandproperties.schemas.file import FileRead
from strandproperties.schemas.mapping import (
    FeatureRead,
    FIPropertyTypeRead,
    GarageTypeRead,
    GardenTypeRead,
    ListingTypeRead,
    OrientationRead,
    PoolTypeRead,
    SettingRead,
    ViewRead,
)
from strandproperties.schemas.user import UserBasicInfo, UserRead


class Portals(BaseSchema):
    is_idealista_enabled: bool = False
    is_leadingre_enabled: bool = False
    is_etuovi_enabled: bool = False
    is_strandproperties_enabled: bool = False
    is_spainforsale_enabled: bool = False
    is_rightmove_enabled: bool = False
    is_kyero_enabled: bool = False
    is_thinkspain_enabled: bool = False
    is_resalesonline_enabled: bool = False
    is_facebook_enabled: bool = False
    is_aplaceinthesun_enabled: bool = False
    is_luxuryestate_enabled: bool = False
    is_pisos_enabled: bool = False
    is_jamesedition_enabled: bool = False
    is_inmobilienscout24_enabled: bool = False
    is_fotocasa_enabled: bool = False
    is_google_enabled: bool = False
    is_inmobalia_enabled: bool = False


class TelecommunicationSystems(BaseSchema):
    telephone_network: bool = False
    general_cabling: bool = False
    fiber_cable: bool = False
    unknown: bool = False


class KeysAndHandoff(BaseSchema):
    protected_keys_total: Optional[int] = None
    protected_keys_delivered: Optional[int] = None
    protected_keys_existing: Optional[int] = None
    unprotected_keys_total: Optional[int] = None
    unprotected_keys_delivered: Optional[int] = None
    unprotected_keys_existing: Optional[int] = None
    other_keys_info: bool = False
    other_keys_info_phone_number: Optional[str] = None
    other_keys_info_description: Optional[str] = None
    strand_properties_keys_info: bool = False
    strand_properties_keys_info_office: Optional[str] = None
    strand_properties_keys_info_notes: Optional[str] = None
    has_electricity: bool = False
    has_lights: bool = False


class Renovations(BaseSchema):
    major_renovations_performed: bool = False
    describe_performed_renovations: Optional[str] = None
    planned_renovations: bool = False
    describe_planned_renovations: Optional[str] = None
    renovations_performed_before_seller_ownership: Optional[str] = None
    describe_previous_performed_renovations: Optional[str] = None


class DamagesAndDefects(BaseSchema):
    defects_damages_repair_observed: bool = False
    describe_defects_damages_repairs: Optional[str] = None
    official_permits_acquired: bool = False
    final_inspection_of_changes: bool = False
    describe_repair_work: Optional[str] = None


class OtherDamages(BaseSchema):
    water_damage: bool = False
    moisture_damage: bool = False
    time_of_damage: Optional[str] = None
    scope_of_damage: Optional[str] = None
    mold_or_fungal_problems: bool = False
    other_special_damages: bool = False
    cause_of_damage: Optional[str] = None
    repair_method: Optional[str] = None


class PropertyBase(BaseSchema):
    reference: str
    area_level_1_id: Optional[int] = Field(default=None, alias="areaLevel1Id")
    area_level_2_id: Optional[int] = Field(default=None, alias="areaLevel2Id")
    area_level_3_id: Optional[int] = Field(default=None, alias="areaLevel3Id")
    area_level_4_id: Optional[int] = Field(default=None, alias="areaLevel4Id")
    area_level_5_id: Optional[int] = Field(default=None, alias="areaLevel5Id")
    is_exclusive: bool
    is_strandified: bool
    data_source: Optional[str] = None


class PropertyDescription(BaseSchema):
    tagline: Optional[str] = None
    description: str
    type: Optional[str] = None
    language: str


class PropertyDetail(PropertyBase):
    title: Optional[str] = None

    bathrooms: Optional[int] = None
    bedrooms: Optional[int] = None

    location_from_areas: Optional[str] = Field(
        None,
        serialization_alias="location",
    )

    pax: Optional[int] = None

    price_sale_old: Optional[int] = None
    price_rent_short_term_old: Optional[int] = None  # per month
    price_rent_long_term_old: Optional[int] = None  # per month
    price_sale: Optional[int] = None
    price_rent_short_term: Optional[int] = None
    price_rent_long_term: Optional[int] = None

    price_square_meter: Optional[float] = None
    currency: Optional[str] = DEFAULT_CURRENCY

    built_area: Optional[int] = None
    built_year: Optional[int] = None

    interior_area: Optional[int] = None
    plot_area: Optional[int] = None
    terrace_area: Optional[int] = None

    levels: Optional[int] = None
    floor: Optional[int] = None

    organization_id: int

    commission: Optional[float] = None
    commission_notes: Optional[str] = None
    commission_type: Optional[CommissionTypeEnum] = None
    iva_tax: Optional[IVATaxEnum] = None

    internal_notes: Optional[str] = None

    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_public_coordinates: Optional[bool] = None
    development_name: Optional[str] = None


class PropertyLegacyData(BaseSchema):
    ext_property_id: Optional[str] = None
    agencyref: Optional[str] = None
    province: Optional[str] = None
    area: Optional[str] = None
    subarea_id: Optional[int] = None
    city_id: Optional[int] = None
    city_name: Optional[str] = None
    property_subtype: Optional[int] = None
    property_subtype_name: Optional[str] = None
    commercial_type: Optional[int] = None
    plot_type: Optional[int] = None
    sale_status: Optional[int] = 1
    price_rent_long: Optional[int] = None
    communityfee: Optional[int] = None
    garbagetax: Optional[int] = None
    ibi: Optional[int] = None
    new_development: Optional[int] = None
    listedby_name: Optional[str] = None
    listedby_email: Optional[str] = None
    listedby2_name: Optional[str] = None
    listedby2_email: Optional[str] = None
    priority: Optional[int] = None
    main_img_resized: Optional[str] = None
    development: Optional[str] = None
    new_row: Optional[int] = None
    viewed: Optional[int] = None
    saved: Optional[int] = None
    is_public: Optional[bool] = None
    is_hidden: Optional[bool] = None
    etuovi_public: Optional[int] = None
    etuovi_title: Optional[str] = None
    etuovi_description_fi: Optional[str] = None
    etuovi_flatstructure: Optional[str] = None


class VideoRead(BaseSchema):
    id: int
    url: str
    is_hidden: bool


class DetailOfSale(BaseSchema):
    id: int


class PropertyReadWithoutContacts(PropertyDetail):
    id: int
    slug: Optional[str] = None

    created_at: datetime
    updated_at: datetime

    status: str

    main_img: Optional[str] = None

    property_type: Optional[str] = None
    property_type_origin: Optional[str] = None
    property_type_category: Optional[str] = None

    listing_types: list[ListingTypeRead] = []
    orientations: Optional[list[OrientationRead]] = []
    features: Optional[list[FeatureRead]] = []
    settings: Optional[list[SettingRead]] = []
    views: Optional[list[ViewRead]] = []
    documents: Optional[list[DocumentListRead]] = []
    files: Optional[list[FileRead]] = []
    video_streams: Optional[list[VideoRead]] = []
    video_tours: Optional[list[VideoRead]] = []
    garage_types: list[GarageTypeRead] = []
    pool_types: list[PoolTypeRead] = []
    garden_types: list[GardenTypeRead] = []

    descriptions: Optional[list[PropertyDescription]] = []

    condition: Optional[str] = None

    realtor_users: Optional[list[UserRead]] = []

    legacy_data: PropertyLegacyData

    private_info: Optional[dict] = None

    legal_representative: Optional[dict] = None

    portals: Portals

    property_type_id: int
    country: Optional[str] = None
    city: Optional[str] = None

    communal_fees: Optional[float] = None
    ibi: Optional[float] = None
    garbage_tax: Optional[float] = None
    water_fee: Optional[float] = None
    electricity: Optional[float] = None

    cadastral_reference: Optional[str] = None
    hostaway_property_id: Optional[str] = None

    building_constructor: Optional[str] = None
    total_floors: Optional[int] = None
    building_has_elevator: Optional[bool] = False

    foundation_and_structure: Optional[str] = None
    roof: Optional[str] = None
    exterior_walls: Optional[str] = None

    property_has_certificate: Optional[bool] = False
    certificate_consumption_rating: Optional[EnergyCertificateRating] = None
    certificate_consumption_value: Optional[float] = None
    certificate_emission_rating: Optional[EnergyCertificateRating] = None
    certificate_emission_value: Optional[float] = None

    rooms_total: Optional[int] = None
    toilets: Optional[int] = None
    suite_baths: Optional[int] = None

    parking_spaces: Optional[int] = None

    telecommunication_systems: Optional[TelecommunicationSystems] = None

    keys_and_handoff: Optional[KeysAndHandoff] = None

    renovations: Optional[Renovations] = None

    damages_and_defects: Optional[DamagesAndDefects] = None

    other_damages: Optional[OtherDamages] = None

    sold_by: Optional[SoldBy] = None

    details_of_sales: Optional[list[DetailOfSale]] = []


class PropertyRead(PropertyReadWithoutContacts):
    contacts: Optional[list[ContactRead]] = []


class PropertyMetadata(BaseSchema):
    is_favorite: Optional[bool] = None


class PropertyReadWithMetadata(PropertyRead):
    metadata: Optional[PropertyMetadata] = None


class PropertyReadExtend(PropertyRead):
    is_favorite: Optional[bool] = None
    click_count: Optional[int] = None


class PropertyListRead(PropertyBase):
    id: int
    location_from_areas: Optional[str] = Field(
        None,
        serialization_alias="location",
    )

    data_source: str

    property_type: Optional[str] = None
    property_type_origin: Optional[str] = None
    property_type_category: Optional[str] = None

    status: Optional[str] = None

    realtor_users: Optional[list[UserBasicInfo]] = []
    contacts: list[ContactIdName] = []
    listing_types: list[ListingTypeRead] = []
    garage_types: list[GarageTypeRead] = []
    pool_types: list[PoolTypeRead] = []
    garden_types: list[GardenTypeRead] = []

    commission: Optional[float] = None
    commission_type: Optional[CommissionTypeEnum] = None
    price_sale_old: Optional[int] = None
    price_rent_short_term_old: Optional[int] = None  # per month
    price_rent_long_term_old: Optional[int] = None  # per month
    price_sale: Optional[int] = None
    price_rent_short_term: Optional[int] = None
    price_rent_long_term: Optional[int] = None
    main_img: Optional[str] = None
    fi_property_type: Optional[FIPropertyTypeRead] = None
    fi_realty: Optional[FIRealtyListRead] = None

    currency: str = DEFAULT_CURRENCY

    plot_area: Optional[int] = None
    built_area: Optional[int] = None

    bathrooms: Optional[int] = None
    bedrooms: Optional[int] = None

    main_img: Optional[str] = None

    sold_by: Optional[SoldBy] = None
    development_name: Optional[str] = None

    created_at: datetime = Field(..., alias="createdAt")
    updated_at: datetime = Field(..., alias="updatedAt")


class PropertyEdit(PropertyDetail):
    feature_ids: Optional[list[int]] = None
    orientation_ids: Optional[list[int]] = None
    setting_ids: Optional[list[int]] = None
    view_ids: Optional[list[int]] = None

    realtor_user_ids: Optional[list[int]] = None
    listing_type_ids: Optional[list[int]] = None
    garage_type_ids: Optional[list[int]] = None
    pool_type_ids: Optional[list[int]] = None
    garden_type_ids: Optional[list[int]] = None

    condition: Optional[str] = None
    private_info: Optional[dict] = None
    legal_representative: Optional[dict] = None
    descriptions: Optional[list[PropertyDescription]] = None

    legacy_data: Optional[PropertyLegacyData] = None
    property_type_id: Optional[int] = None
    contact_ids: Optional[list[int]] = None

    communal_fees: Optional[float] = None
    ibi: Optional[float] = None
    garbage_tax: Optional[float] = None
    water_fee: Optional[float] = None
    electricity: Optional[float] = None

    cadastral_reference: Optional[str] = None
    hostaway_property_id: Optional[str] = None

    building_constructor: Optional[str] = None
    total_floors: Optional[int] = None
    building_has_elevator: Optional[bool] = None

    foundation_and_structure: Optional[str] = None
    roof: Optional[str] = None
    exterior_walls: Optional[str] = None

    property_has_certificate: Optional[bool] = None
    certificate_consumption_rating: Optional[EnergyCertificateRating] = None
    certificate_consumption_value: Optional[float] = None
    certificate_emission_rating: Optional[EnergyCertificateRating] = None
    certificate_emission_value: Optional[float] = None

    rooms_total: Optional[int] = None
    toilets: Optional[int] = None
    suite_baths: Optional[int] = None

    parking_spaces: Optional[int] = None

    telecommunication_systems: Optional[TelecommunicationSystems] = None

    keys_and_handoff: Optional[KeysAndHandoff] = None

    renovations: Optional[Renovations] = None

    damages_and_defects: Optional[DamagesAndDefects] = None

    other_damages: Optional[OtherDamages] = None

    video_streams: Optional[List[str]] = None
    video_tours: Optional[List[str]] = None

    is_exclusive: Optional[bool] = None
    is_strandified: Optional[bool] = None
    organization_id: Optional[int] = None


class PropertyCreate(PropertyBase):
    reference: None = None
    listing_type_ids: list[int]
    realtor_user_ids: list[int]
    property_type_id: int
    price_sale_old: Optional[int] = None
    price_rent_short_term_old: Optional[int] = None  # per month
    price_rent_long_term_old: Optional[int] = None  # per month
    price_sale: Optional[int] = None
    price_rent_short_term: Optional[int] = None
    price_rent_long_term: Optional[int] = None
    commission: float
    commission_type: CommissionTypeEnum
    private_info: dict
    cadastral_reference: Optional[str] = None
    hostaway_property_id: Optional[str] = None
    contact_ids: Optional[list[int]] = None
    development_name: Optional[str] = None


class PropertyChangeStatusBody(BaseSchema):
    action: Status
    portals: Optional[Portals] = None
    sold_by: Optional[SoldBy] = None


class PropertyValidation(BaseSchema):
    sales_agreement_valid: bool
    images_quality: ImagesQuality
    missing_fields: List[str]


class FIPropertyValidation(BaseSchema):
    images_quality: ImagesQuality
    missing_fields: List[str]


# alias, required for schema discovery
PropertyList = PaginatedList[PropertyListRead]
