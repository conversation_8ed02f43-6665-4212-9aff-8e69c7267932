from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FICommonArea,
    FIEnergyCertificate,
    FIRedemption,
    FIShareCertificate,
    FITranslatedText,
)


# ############## Commercial ##################
class FICommercialPropertyTypeCodeEnum(StrEnum):
    """
    Enum: "RETAIL_SPACE" "PRODUCTION_FACILITY" "WAREHOUSE" "OFFICE_SPACE" "COWORKING" "CARE_FACILITY" "OTHER"
          "COMMERCIAL_PLOT" "INDUSTRIAL_PLOT" "STORAGE_PLOT" "SOLAR_FARM"

    Describes the real estate type of the property. Possible values for commercial estate are:

    - RETAIL_SPACE: Commercial retail space.
    - PRODUCTION_FACILITY: Production facility.
    - WAREHOUSE: Warehouse or storage space.
    - OFFICE_SPACE: Office space.
    - COWORKING: Office space for coworking.
    - CARE_FACILITY: Care facility, nursing home, retirement home, or similar facility.
    - OTHER: Another commercial space.

    Possible values for commercial plot are:

    - COMMERCIAL_PLOT: Commercial or office plot.
    - INDUSTRIAL_PLOT: Industrial land.
    - STORAGE_PLOT: Plot for storage.
    - SOLAR_FARM: Solar farm.
    """

    RETAIL_SPACE = "RETAIL_SPACE"
    PRODUCTION_FACILITY = "PRODUCTION_FACILITY"
    WAREHOUSE = "WAREHOUSE"
    OFFICE_SPACE = "OFFICE_SPACE"
    COWORKING = "COWORKING"
    CARE_FACILITY = "CARE_FACILITY"
    OTHER = "OTHER"
    COMMERCIAL_PLOT = "COMMERCIAL_PLOT"
    INDUSTRIAL_PLOT = "INDUSTRIAL_PLOT"
    STORAGE_PLOT = "STORAGE_PLOT"
    SOLAR_FARM = "SOLAR_FARM"


class FITypicalUsesEnum(StrEnum):
    RESTAURANT = "RESTAURANT"
    CAFE = "CAFE"
    HAIRDRESSER_BARBER = "HAIRDRESSER_BARBER"
    TRAINING_FACILITY = "TRAINING_FACILITY"
    SHOWROOM = "SHOWROOM"
    STUDIO = "STUDIO"
    WORKSHOP = "WORKSHOP"
    ACCOMMODATION = "ACCOMMODATION"
    KINDERGARTEN = "KINDERGARTEN"
    POP_UP_SPACE = "POP_UP_SPACE"
    BAND_REHEARSAL_SPACE = "BAND_REHEARSAL_SPACE"
    EXERCISE_SPACE = "EXERCISE_SPACE"
    SHOP = "SHOP"


class FIEnvironmentalCertificateTypeCodeEnum(StrEnum):
    """
    Enum: "BREEAM" "JOUTSENMERKKI" "LEED" "RTS" "OTHER"

    Details about the environmental certificate that the realty has been certified with. Possible values are:

    - BREEAM: The realty is certified with the BREEAM environmental certification.
    - JOUTSENMERKKI: The realty is certified with the Joutsenmerkki (Nordic Swan) environmental certification.
    - LEED: The realty is certified with the LEED (Leadership in Energy and Environmental Design) certification.
    - RTS: The realty is certified with the RTS environmental certification.
    - OTHER: The realty is certified with another type of environmental certification.
    """

    BREEAM = "BREEAM"
    JOUTSENMERKKI = "JOUTSENMERKKI"
    LEED = "LEED"
    RTS = "RTS"
    OTHER = "OTHER"


class FIAdditionalServicesEnum(StrEnum):
    PARKING_FOR_CUSTOMERS = "PARKING_FOR_CUSTOMERS"
    LOBBY_SERVICES = "LOBBY_SERVICES"
    OFFICIAL_SPACE = "OFFICIAL_SPACE"
    KITCHEN = "KITCHEN"
    PROPERTY_SERVICES = "PROPERTY_SERVICES"
    MEETING_ROOMS = "MEETING_ROOMS"
    GYM = "GYM"
    LOADING_DOCK = "LOADING_DOCK"
    LOUNGE = "LOUNGE"
    SERVER_ROOM = "SERVER_ROOM"
    BALCONY = "BALCONY"
    LAUNDRY_SERVICES = "LAUNDRY_SERVICES"
    YARD = "YARD"
    POSTAL_SERVICES = "POSTAL_SERVICES"
    DRESSING_ROOM = "DRESSING_ROOM"
    PARKING_GARAGE = "PARKING_GARAGE"
    BIKE_PARK = "BIKE_PARK"
    CARGO_PORT = "CARGO_PORT"
    RESTAURANT = "RESTAURANT"
    SAUNA = "SAUNA"
    CLEANING_SERVICES = "CLEANING_SERVICES"
    SHOWER_FACILITIES = "SHOWER_FACILITIES"
    FREIGHT_ELEVATOR = "FREIGHT_ELEVATOR"
    TERRACE = "TERRACE"
    SECURITY_SERVICES = "SECURITY_SERVICES"


class FISpaceTypeCodeEnum(StrEnum):
    """
    Describes the purpose for an individual space. Possible values are:

    - RETAIL_SPACE: Retail space.
    - PRODUCTION_FACILITY: Production facility.
    - WAREHOUSE: Warehouse.
    - OFFICE_SPACE: Office space.
    - COWORKING: Coworking space.
    - CARE_FACILITY: Care facility.
    - OTHER: Other purposes.
    """

    RETAIL_SPACE = "RETAIL_SPACE"
    PRODUCTION_FACILITY = "PRODUCTION_FACILITY"
    WAREHOUSE = "WAREHOUSE"
    OFFICE_SPACE = "OFFICE_SPACE"
    COWORKING = "COWORKING"
    CARE_FACILITY = "CARE_FACILITY"
    OTHER = "OTHER"


class FIEnvironmentalCertificate(BaseSchema):
    type_code: list[FIEnvironmentalCertificateTypeCodeEnum]
    description: Optional[FITranslatedText] = None


class FISustainableDevelopment(BaseSchema):
    energy_certificate: Optional[FIEnergyCertificate] = None
    environmental_certificate: Optional[FIEnvironmentalCertificate] = None


class FICommercialSpace(BaseSchema):
    space_type_code: FISpaceTypeCodeEnum
    description: Optional[FITranslatedText] = None
    area: Optional[FICommonArea] = None
    minimum_area: Optional[FICommonArea] = None
    maximum_area: Optional[FICommonArea] = None
    height: Optional[str] = None
    # Identifies the floor. Note that the floor might be indicated also as "Kellarikerros", "Ullakko" or similar.
    floor: Optional[str] = None
    # Net price (excluding VAT) per square meter.
    price_amount: Optional[int] = None
    # Currency code as specified in ISO 4217 with alphabetic code.
    currency_code: Optional[str] = None


class _FICommercialPropertyOverviewBasicDetails(BaseSchema):
    debt_free_price: Optional[int] = None
    currency_code: Optional[str] = None


class _FICommercialPropertyOverviewFullDetails(
    _FICommercialPropertyOverviewBasicDetails
):
    commercial_property_type_code: FICommercialPropertyTypeCodeEnum
    typical_uses: list[FITypicalUsesEnum]
    administration: Optional[FIAdministration] = None
    floor_area: Optional[FICommonArea] = None
    total_area: Optional[FICommonArea] = None
    debt_share_amount: Optional[int] = None
    starting_debt_free_price: Optional[int] = None
    share_certificate: Optional[FIShareCertificate] = None
    redemption: Optional[FIRedemption] = None
    sustainable_development: Optional[FISustainableDevelopment] = None
    additional_services: list[FIAdditionalServicesEnum]
    spaces: list[FICommercialSpace]


class FICommercialPropertyOverviewCreateEdit(_FICommercialPropertyOverviewFullDetails):
    pass


class FICommercialPropertyOverviewListRead(_FICommercialPropertyOverviewBasicDetails):
    pass


class FICommercialPropertyOverviewRead(_FICommercialPropertyOverviewFullDetails):
    pass
