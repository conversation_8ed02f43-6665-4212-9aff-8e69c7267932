from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FIApartmentArea,
    FIApartmentRoom,
    FIApartmentRoomCountCodeEnum,
    FIAsbestosMapping,
    FIChoiceEnum,
    FICompassPointEnum,
    FIHearth,
    FILivingFloorCountCodeEnum,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIParkingSpaceTypeCodeEnum as ParkingSpaceTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIPropertyStorageTypeCodeEnum,
    FIRealtyInspection,
    FIRedemption,
    FIRenovationShare,
    FIResidentialPropertyBalconyTypeCodeEnum,
    FIResidentialTypeCodeEnum,
    FIShareCertificate,
    FITranslatedText,
)


class FIOwnershipTypeCodeEnum(StrEnum):
    """
        OWN. Direct ownership.
    PARTIAL_OWNERSHIP. Co-ownership housing is a form of housing in which the
    resident redeems parts of the apartment as his or her own. When moving into
    an apartment, a 10-15% share of the price of the apartment is paid, in which
    case the resident owns a minority share of the apartment. After this, the
    resident lives in his or her co-owned apartment with a fixed-term lease, but
    can redeem the apartment as his or her own within the agreed time.
    Co-ownership dwellings are both state-subsidized and non-subsidized
    dwellings, the form of which is partly owned by the developer.

    LIVING_RIGHT. In a living right apartment the residence pays 15% of the
    purchase price of the apartment, after which the resident pays a monthly
    housing allowance and manages his or her apartment as its owner. The
    living-right apartment always remains a living-right apartment, ie. you can
    live in the apartment for as long as you want. Housing is carefree, as the
    housing company takes care of the maintenance and renovations of the
    property. The housing company will take care of the resale of the apartment
    if needed. The right-of-occupancy apartment can also be left as a legacy.
    """

    OWN = ("OWN", "OMA")
    PARTIAL_OWNERSHIP = ("PARTIAL_OWNERSHIP", "")
    LIVING_RIGHT = ("LIVING_RIGHT", "")

    def __new__(cls, english_name, finnish_name):
        obj = str.__new__(cls, english_name)
        obj._value_ = english_name
        obj.finnish_name = finnish_name
        return obj

    @classmethod
    def from_finnish_name(cls, finnish_name: str):
        if not finnish_name:
            return finnish_name
        for item in cls:
            if item.finnish_name == finnish_name.upper():
                return item
        return None

    @classmethod
    def from_english_name(cls, english_name: str):
        if not english_name:
            return english_name
        for item in cls:
            if item.value == english_name.upper():
                return item
        return None


class FIFloorLevelCodeEnum(StrEnum):
    """
    Identifies the floor level of the apartment. This value can be set only if
    the building in which the apartment is located has multiple floors.
    """

    ON_BOTTOM_FLOOR = "ON_BOTTOM_FLOOR"
    ON_MIDDLE_FLOOR = "ON_MIDDLE_FLOOR"
    ON_TOP_FLOOR = "ON_TOP_FLOOR"
    NOT_KNOWN = "NOT_KNOWN"


class FIBasisForPossessionCodeYardEnum(StrEnum):
    ARTICLES_OF_ASSOCIATION = "ARTICLES_OF_ASSOCIATION"
    NOT_KNOWN = "NOT_KNOWN"
    OTHER = "OTHER"


class FITerraceGlassMaintenanceResponsibilityCodeEnum(StrEnum):
    """
    Describes who is responsible of maintaining the glass of the terrace. The
    value of this attribute must be set if the value of the isGlazedTerrace
    attribute is: YES and the realty is a share of a housing company.
    """

    HOUSING_COMPANY = "HOUSING_COMPANY"
    MAINTENANCE_COMPANY = "MAINTENANCE_COMPANY"
    RESIDENTS = "RESIDENTS"


class FIBalconyGlassMaintenanceResponsibilityCodeEnum(StrEnum):
    """
    Describes who is responsible of maintaining the glass of the balcony. The
    value of this attribute must be set if the value of the `typeCode` attribute
    is `GLAZED` and the realty is a share of a housing company.
    """

    HOUSING_COMPANY = "HOUSING_COMPANY"
    MAINTENANCE_COMPANY = "MAINTENANCE_COMPANY"
    RESIDENTS = "RESIDENTS"


class FIBasisForPossessionCodesEnum(StrEnum):
    BELONGS_TO_APARTMENT_ACCORDING_TO_THE_ARTICLES_OF_ASSOCIATION = (
        "BELONGS_TO_APARTMENT_ACCORDING_TO_THE_ARTICLES_OF_ASSOCIATION"
    )
    RENTED_FROM_THE_COMPANY = "RENTED_FROM_THE_COMPANY"
    WITH_A_DIFFERENT_GROUP_OF_SHARES = "WITH_A_DIFFERENT_GROUP_OF_SHARES"


class FITransferCodeEnum(StrEnum):
    TRANSFERABLE = "TRANSFERABLE"
    NON_TRANSFERABLE = "NON_TRANSFERABLE"
    NO_SELECTION = "NO_SELECTION"


class FIParkingSpaceTypeCodeEnum(StrEnum):
    GARAGE = "GARAGE"
    SPACE_IN_PARKING_HALL = "SPACE_IN_PARKING_HALL"
    SPACE_IN_CAR_PORT = "SPACE_IN_CAR_PORT"
    SPACE_IN_CAR_PORT_WITH_ELECTRICAL_PLUG = "SPACE_IN_CAR_PORT_WITH_ELECTRICAL_PLUG"
    OUTDOOR_PARKING_SPACE = "OUTDOOR_PARKING_SPACE"
    OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG = (
        "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG"
    )
    ELECTRIC_CAR_CHARGING_POINT = "ELECTRIC_CAR_CHARGING_POINT"
    FOR_PURCHASE_OR_RENT_IN_SEPARATE_PARKING_HALL = (
        "FOR_PURCHASE_OR_RENT_IN_SEPARATE_PARKING_HALL"
    )


class FIApartmentFloorOverview(BaseSchema):
    floor_level: Optional[int] = None
    floor_level_code: Optional[FIFloorLevelCodeEnum] = None
    living_floor_count: Optional[str] = None
    living_floor_count_code: Optional[FILivingFloorCountCodeEnum] = None
    # Identifies the number of floor found from the building in which the
    # apartment is located. This value can be set only if the building in which
    # the apartment is located has multiple floors.
    total_floor_count: Optional[int] = None


class FIResidentialShareYard(BaseSchema):
    has_private_yard: Optional[FIChoiceEnum] = None
    basis_for_possession_code: Optional[FIBasisForPossessionCodeYardEnum] = None
    basis_for_possession_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FITerrace(BaseSchema):
    is_glazed_terrace: Optional[FIChoiceEnum] = None
    description: Optional[list[FITranslatedText]] = None
    compass_points: Optional[list[FICompassPointEnum]] = None
    terrace_glass_maintenance_responsibility_code: Optional[
        FITerraceGlassMaintenanceResponsibilityCodeEnum
    ] = None


class FIBalcony(BaseSchema):
    balcony_type_code: Optional[FIResidentialPropertyBalconyTypeCodeEnum] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None
    compass_points: Optional[list[FICompassPointEnum]] = None
    balcony_glass_maintenance_responsibility_code: Optional[
        FIBalconyGlassMaintenanceResponsibilityCodeEnum
    ] = None


class FIApartmentOverview(BaseSchema):
    official_room_structure: Optional[str] = None
    room_count_code: Optional[FIApartmentRoomCountCodeEnum] = None
    area: Optional[FIApartmentArea] = None
    floors: Optional[FIApartmentFloorOverview] = None
    asbestos_mapping: Optional[FIAsbestosMapping] = None
    hearth: Optional[FIHearth] = None
    has_balcony: Optional[FIChoiceEnum] = None
    balcony: Optional[list[FIBalcony]] = None
    has_terrace: Optional[FIChoiceEnum] = None
    terrace: Optional[list[FITerrace]] = None
    yard: Optional[list[FIResidentialShareYard]] = None
    rooms: Optional[list[FIApartmentRoom]] = None
    number_of_toilets: Optional[int] = None
    number_of_bedrooms: Optional[int] = None
    boiler_room_description: Optional[list[FITranslatedText]] = None
    living_quarters_description: Optional[list[FITranslatedText]] = None
    interior_material_description: Optional[list[FITranslatedText]] = None
    is_fit_for_winter_habitation: Optional[FIChoiceEnum] = None
    has_own_sauna: Optional[FIChoiceEnum] = None
    heating_description: Optional[list[FITranslatedText]] = None
    inspections: Optional[list[FIRealtyInspection]] = None
    inspections_description: Optional[list[FITranslatedText]] = None
    modification_documents: Optional[list[FITranslatedText]] = None
    patio_description: Optional[list[FITranslatedText]] = None
    refrigerated_cellar_description: Optional[list[FITranslatedText]] = None
    renovations: Optional[list[FIRenovationShare]] = None
    renovations_description: Optional[list[FITranslatedText]] = None
    shareholder_installation_description: Optional[list[FITranslatedText]] = None
    storage_description: Optional[list[FITranslatedText]] = None
    underfloor_heating_description: Optional[list[FITranslatedText]] = None
    utility_services_room_description: Optional[list[FITranslatedText]] = None
    things_effecting_housing_comfort: Optional[list[FITranslatedText]] = None
    view_description: Optional[list[FITranslatedText]] = None
    smoking_allowed: Optional[FIChoiceEnum] = None
    pets_allowed: Optional[FIChoiceEnum] = None
    suitable_for_disabled: Optional[FIChoiceEnum] = None
    furnished: Optional[FIChoiceEnum] = None


class FIResidentialShareStorage(BaseSchema):
    type_code: Optional[FIPropertyStorageTypeCodeEnum] = None
    basis_for_possession_code: Optional[FIBasisForPossessionCodesEnum] = None
    transfer_code: Optional[FITransferCodeEnum] = None
    basis_for_allocation_description: Optional[list[FITranslatedText]] = None


class FIResidentialShareOverviewParkingSpaces(BaseSchema):
    type_code: Optional[ParkingSpaceTypeCodeEnum] = None
    basis_for_possession_codes: Optional[list[FIBasisForPossessionCodesEnum]] = None
    transfer_code: Optional[FITransferCodeEnum] = None
    description: Optional[list[FITranslatedText]] = None
    parking_space_descriptions: Optional[list[FITranslatedText]] = None


class FIResidentialPropertyOverviewParkingSpaces(BaseSchema):
    count: Optional[int] = None
    parking_space_type_code: Optional[ParkingSpaceTypeCodeEnum] = None
    amount: Optional[int] = None
    description: Optional[list[FITranslatedText]] = None
    parking_space_descriptions: Optional[list[FITranslatedText]] = None


class FIResidentialShareParkingSpace(BaseSchema):
    type_code: FIParkingSpaceTypeCodeEnum
    basis_for_possession_codes: Optional[list[FIBasisForPossessionCodesEnum]] = None
    transfer_code: Optional[FITransferCodeEnum] = None
    basis_for_allocation_description: Optional[list[FITranslatedText]] = None


class _FIResidentialShareOverviewBasicDetails(BaseSchema):
    apartment: Optional[FIApartmentOverview] = None
    debt_free_price: Optional[int] = None


class _FIResidentialShareOverviewFullDetails(_FIResidentialShareOverviewBasicDetails):
    residential_type_code: Optional[FIResidentialTypeCodeEnum] = None
    ownership_type_code: Optional[FIOwnershipTypeCodeEnum] = None
    administration: Optional[FIAdministration] = None
    debt_share_amount: Optional[int] = None
    debt_share_additional_info: Optional[list[FITranslatedText]] = None
    currency_code: Optional[str] = None
    starting_debt_free_price: Optional[int] = None
    storages: Optional[list[FIResidentialShareStorage]] = None
    limits_of_storage_usage: Optional[list[FITranslatedText]] = None
    parking_spaces: Optional[list[FIResidentialShareOverviewParkingSpaces]] = None
    parking_space_description: Optional[list[FITranslatedText]] = None
    share_certificate: Optional[FIShareCertificate] = None
    redemption: Optional[FIRedemption] = None
    more_information_about_the_premises: Optional[list[FITranslatedText]] = None
    more_information_about_the_materials: Optional[list[FITranslatedText]] = None


class FIResidentialShareOverviewCreateEdit(_FIResidentialShareOverviewFullDetails):
    pass


class FIResidentialShareOverviewListRead(_FIResidentialShareOverviewBasicDetails):
    id: int


class FIResidentialShareOverviewRead(
    _FIResidentialShareOverviewFullDetails, FIResidentialShareOverviewListRead
):
    pass
