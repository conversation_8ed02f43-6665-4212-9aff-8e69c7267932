from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FICommonArea,
    FITranslatedText,
)


class FIPlotPropertyTypeCodeEnum(StrEnum):
    """
    Describes the type of a plot. This attribute must be set if the propertyTypeCode is PLOT. Possible values are:
        APARTMENT_HOUSE_PLOT is suitable for building an apartment house.
        HOLIDAY_PLOT is suitable for building a cottage which is meant for vacationing.
        HOUSE_PLOT is suitable for building a detached house.
        ROW_HOUSE_PLOT is suitable for building a row house.
        COMMERCIAL_OR_INDUSTRIAL_PLOT is suitable for commercial or industrial use.
        OTHER is a plot which doesn't fit into any of the above categories.
    """

    APARTMENT_HOUSE_PLOT = "APARTMENT_HOUSE_PLOT"
    HOLIDAY_PLOT = "HOLIDAY_PLOT"
    HOUSE_PLOT = "HOUSE_PLOT"
    ROW_HOUSE_PLOT = "ROW_HOUSE_PLOT"
    COMMERCIAL_OR_INDUSTRIAL_PLOT = "COMMERCIAL_OR_INDUSTRIAL_PLOT"
    OTHER = "OTHER"


class FIHoldingTypeCodeEnum(StrEnum):
    """
    Describes the holding type of the plot. Possible values are:

        OWN is an owned piece of land.
        LEASEHOLD is a rented piece of land.
    """

    OWN = "OWN"
    LEASEHOLD = "LEASEHOLD"


class FIZoningTypeCodeEnum(StrEnum):
    """
    Items Enum: "CITY_PLAN" "RURAL_AREA" "UNZONED" "COMMISSIONING_INSPECTION" "FINAL_INSPECTION"
                "COMPONENT_MASTER_PLAN" "DEVIATION_DECISION" "DETAILED_PLAN" "BUILDING_FORBID"
                "BUILDING_PERMIT" "DETAILED_SHORE_PLAN" "SHORE_PLAN_AREA" "AREA_REQUIRING_PLANNING"
                "ACTION_BAN" "MASTER_PLAN"

    Describes zoning types concerning the plot.
    """

    CITY_PLAN = "CITY_PLAN"
    RURAL_AREA = "RURAL_AREA"
    UNZONED = "UNZONED"
    COMMISSIONING_INSPECTION = "COMMISSIONING_INSPECTION"
    FINAL_INSPECTION = "FINAL_INSPECTION"
    COMPONENT_MASTER_PLAN = "COMPONENT_MASTER_PLAN"
    DEVIATION_DECISION = "DEVIATION_DECISION"
    DETAILED_PLAN = "DETAILED_PLAN"
    BUILDING_FORBID = "BUILDING_FORBID"
    BUILDING_PERMIT = "BUILDING_PERMIT"
    DETAILED_SHORE_PLAN = "DETAILED_SHORE_PLAN"
    SHORE_PLAN_AREA = "SHORE_PLAN_AREA"
    AREA_REQUIRING_PLANNING = "AREA_REQUIRING_PLANNING"
    ACTION_BAN = "ACTION_BAN"
    MASTER_PLAN = "MASTER_PLAN"


class FIBeachCodeEnum(StrEnum):
    """
    Enum: "NO_BEACH" "SHARED_BEACH" "OWN_BEACH" "BEACH_RIGHT" "RIGHT_TO_WATER_AREA" "NEXT_TO_RELICTION_AREA"

    Describes what kind of beach access the plot possibly has. Possible values are:

    - NO_BEACH: No beach access.
    - SHARED_BEACH: Access to a shared beach.
    - OWN_BEACH: Own beach.
    - BEACH_RIGHT: Right to use a beach.
    - RIGHT_TO_WATER_AREA: Right to use an area of water.
    - NEXT_TO_RELICTION_AREA: Location next to reliction area.
    """

    NO_BEACH = "NO_BEACH"
    SHARED_BEACH = "SHARED_BEACH"
    OWN_BEACH = "OWN_BEACH"
    BEACH_RIGHT = "BEACH_RIGHT"
    RIGHT_TO_WATER_AREA = "RIGHT_TO_WATER_AREA"
    NEXT_TO_RELICTION_AREA = "NEXT_TO_RELICTION_AREA"


class FIBeachTypeCodeEnum(StrEnum):
    """
    Enum: "RIVER" "LAKE" "POND" "SEA" "OTHER"

    Describes the type of beach the plot has. Possible values are:

    - RIVER: Beach by a river.
    - LAKE: Beach by a lake.
    - POND: Beach by a pond.
    - SEA: Beach by the sea.
    - OTHER: Some other type of beach.
    """

    RIVER = "RIVER"
    LAKE = "LAKE"
    POND = "POND"
    SEA = "SEA"
    OTHER = "OTHER"


class FIPlotZoning(BaseSchema):
    type_codes: Optional[list[FIZoningTypeCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FIPlotBeach(BaseSchema):
    code: Optional[FIBeachCodeEnum] = None
    description: Optional[list[FITranslatedText]] = None
    type_code: Optional[FIBeachTypeCodeEnum] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    # Specifies the name of the body of water when the value of the beachCode attribute is other than NO_BEACH.
    water_body_name: Optional[str] = None


class FIPlotConstructionRight(BaseSchema):
    density_rate: Optional[float] = None
    floor_area: Optional[FICommonArea] = None
    description: Optional[list[FITranslatedText]] = None


class _FIPlotOverviewBasicDetails(BaseSchema):
    area: Optional[FICommonArea] = None


class _FIPlotOverviewFullDetails(_FIPlotOverviewBasicDetails):
    plot_property_type_code: Optional[FIPlotPropertyTypeCodeEnum] = None
    holding_type_code: Optional[FIHoldingTypeCodeEnum] = None
    holding_type_description: Optional[list[FITranslatedText]] = None
    # Name of the plot landlord when the value of the holdingTypeCode attribute is "LEASEHOLD".
    landlord: Optional[str] = None
    # Specifies the amount of plot's yearly rent when the value of the holdingTypeCode attribute is "LEASEHOLD".
    yearly_rent: Optional[int] = None
    currency_code: Optional[str] = None
    is_redeemable: Optional[FIChoiceEnum] = None
    redeemable_description: Optional[list[FITranslatedText]] = None
    # Specifies the amount of redemption portion in the price when the value of the isRedeemable attribute is "YES".
    redemption_portion: Optional[int] = None
    optional_rental_plot: Optional[FIChoiceEnum] = None
    optional_rental_plot_description: Optional[list[FITranslatedText]] = None
    # Specifies the plot's lease end date when the value of the holdingTypeCode attribute is "LEASEHOLD".
    lease_end_date: Optional[str] = None
    lease_period_description: Optional[list[FITranslatedText]] = None
    # Specifies the official plot registration number
    plot_number: Optional[str] = None
    # Identifier for a right to lease if the plot is rented when the value of
    # the holdingTypeCode attribute is "LEASEHOLD"
    identification_number_of_land_charge: Optional[str] = None
    # Describes the plot leaseholder when the value of the holdingTypeCode attribute is "LEASEHOLD".
    lease_holder: Optional[str] = None
    lease_hold_transfer_limitation: Optional[FIChoiceEnum] = None
    lease_hold_transfer_limitation_description: Optional[list[FITranslatedText]] = None
    legal_confirmation_of_title_to_real_property: Optional[list[FITranslatedText]] = (
        None
    )
    size_of_plot: Optional[FICommonArea] = None
    area: Optional[FICommonArea] = None
    zonings: Optional[FIPlotZoning] = None
    beaches: Optional[list[FIPlotBeach]] = None
    construction_right: Optional[FIPlotConstructionRight] = None
    unbuilt_plot: Optional[bool] = None
    plot_redemption_info: Optional[list[FITranslatedText]] = None
    plot_redemption_info_link: Optional[str] = None
    plot_rental_agreement: Optional[list[FITranslatedText]] = None
    plot_rental_agreement_link: Optional[str] = None
    fi_housing_company_id: Optional[int] = None


class FIPlotOverviewCreateEdit(_FIPlotOverviewFullDetails):
    pass


class FIPlotOverviewListRead(_FIPlotOverviewBasicDetails):
    id: Optional[int] = None


class FIPlotOverviewRead(_FIPlotOverviewFullDetails, FIPlotOverviewListRead):
    pass
