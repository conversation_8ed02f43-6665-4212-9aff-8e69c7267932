from enum import Enum, StrEnum

from strandproperties.schemas.base import BaseSchema


class FIListingTypeEnum(StrEnum):
    SALE = "Sale"
    RENTAL = "Rental"


class FIOwnershipTypeEnum(StrEnum):
    """
    Describes if the realty is a share of the limited liability housing company or a property.
    """

    PROPERTY = "Property"
    SHARE = "Share"


class FIPropertyTypeGroupEnum(StrEnum):
    COMMERCIAL_PROPERTY = "Commercial property"
    ESTATE = "Estate"
    LEISURE = "Leisure"
    OTHER = "Other"
    PLOT = "Plot"
    RESIDENTIAL = "Residential"
    SHARED_APARTMENT = "Shared apartment"
    SUBLEASE = "Sublease"


class FIPropertyTypeEnum(str, Enum):
    APARTMENT_HOUSE = ("Apartment house", "kerrostalo")
    APARTMENT_PLOT = ("Apartment plot", "")
    ARABLE_FARM = ("Arable farm", "")
    BALCONY_ACCESS_BLOCK = ("Balcony access block", "")
    BOAT = ("Boat", "")
    BUSINESS_OR_INDUSTRIAL_PLOT = ("Business or industrial plot", "liiketila")
    CAR_SHED = ("Car shed", "")
    CAR_SHELTER = ("Car shelter", "")
    CARE_FACILITY = ("Care facility", "")
    COMMERCIAL_PLOT = ("Commercial plot", "")
    COTTAGE_OR_VILLA = ("Cottage or villa", "mökki tai huvila")
    COWORKING = ("Coworking", "")
    DETACHED_HOUSE = ("Detached house", "omakotitalo")
    FARM = ("Farm", "")
    FOREST = ("Forest", "")
    GARAGE = ("Garage", "autohallipaikka")
    HOLIDAY_PLOT = ("Holiday plot", "vapaa-ajan tontti")
    HOUSE_PLOT = ("House plot", "omakotitalotontti")
    INDUSTRIAL_PLOT = ("Industrial plot", "")
    LEISURE_APARTMENT = ("Leisure apartment", "")
    OFFICE_SPACE = ("Office space", "")
    OTHER = ("Other", "muu")
    PARCEL_OF_LAND = ("Parcel of land", "")
    PARKING_SLOT = ("Parking slot", "autopaikka")
    PRODUCTION_FACILITY = ("Production facility", "")
    RETAIL_SPACE = ("Retail space", "")
    ROW_HOUSE = ("Row house", "rivitalo", "rivitalotontti")
    ROW_HOUSE_PLOT = ("Row house plot", "")
    SEMI_DETACHED_HOUSE = ("Semi detached house", "paritalo")
    SEPARATE_HOUSE = ("Separate house", "erillistalo")
    SOLAR_FARM = ("Solar farm", "")
    STORAGE = ("Storage", "")
    STORAGE_PLOT = ("Storage plot", "")
    TIME_SHARE_APARTMENT = ("Time share apartment", "")
    WAREHOUSE = ("Warehouse", "")
    WILDERNESS = ("Wilderness", "")
    WOODEN_HOUSE_APARTMENT = ("Wooden house apartment", "puutalo-osake")

    def __new__(cls, english_name, *finnish_names):
        obj = str.__new__(cls, english_name)
        obj._value_ = english_name
        obj.finnish_names = set(filter(None, finnish_names))
        return obj

    @classmethod
    def from_finnish_name(cls, finnish_name: str):
        if not finnish_name:
            return finnish_name
        for item in cls:
            if finnish_name in item.finnish_names:
                return item
        return None

    @classmethod
    def from_english_name(cls, english_name: str):
        if not english_name:
            return english_name
        for item in cls:
            if item.value == english_name:
                return item
        return None


class FIPropertyTypeListRead(BaseSchema):
    id: int
    listing_type: FIListingTypeEnum
    ownership_type: FIOwnershipTypeEnum
    property_type_group: FIPropertyTypeGroupEnum
    property_type: FIPropertyTypeEnum
