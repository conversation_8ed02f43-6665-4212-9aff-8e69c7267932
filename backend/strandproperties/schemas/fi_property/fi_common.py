from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema


class FIAreaUnitCodeEnum(StrEnum):
    SQUARE_METERS = "M2"
    HECTARES = "HA"


class FIEnergyCertificateTypeCodeEnum(StrEnum):
    A_2007 = "A_2007"
    B_2007 = "B_2007"
    C_2007 = "C_2007"
    D_2007 = "D_2007"
    E_2007 = "E_2007"
    F_2007 = "F_2007"
    G_2007 = "G_2007"
    A_2013 = "A_2013"
    B_2013 = "B_2013"
    C_2013 = "C_2013"
    D_2013 = "D_2013"
    E_2013 = "E_2013"
    F_2013 = "F_2013"
    G_2013 = "G_2013"
    A_2018 = "A_2018"
    B_2018 = "B_2018"
    C_2018 = "C_2018"
    D_2018 = "D_2018"
    E_2018 = "E_2018"
    F_2018 = "F_2018"
    G_2018 = "G_2018"
    H = "H"
    NOT_AVAILABLE = "NOT_AVAILABLE"
    NOT_REQUIRED = "NOT_REQUIRED"


class FIChoiceEnum(StrEnum):
    """
    This enum is used to replace the boolean data type. Possible values are:

        - YES means true.
        - NO means false.
    """

    YES = "YES"
    NO = "NO"


class FITranslatedText(BaseSchema):
    language_code: str
    text: str | None


class FICommonArea(BaseSchema):
    value: int | float | None
    area_unit_code: Optional[FIAreaUnitCodeEnum] = None


class FIEnergyCertificate(BaseSchema):
    type_code: Optional[FIEnergyCertificateTypeCodeEnum] = None
    description: Optional[list[FITranslatedText]] = None


class FICoordinateAccuracyEnum(StrEnum):
    """
    Identifies accuracy of coordinates Possible values are:

    STREET means that latitude and longitude were set by geo coding, but it may not be in exactly correct location.
    ADDRESS means that latitude and longitude were set by geo coding with good accuracy or were selected manually.
    UNKNOWN means that latitude and longitude may not be accurate
    """

    STREET = "STREET"
    ADDRESS = "ADDRESS"
    UNKNOWN = "UNKNOWN"


class FICoordinateSourceEnum(StrEnum):
    """
    Identifies the method which was used to set the latitude and longitude of the address. Possible values are:

    GEO_CODING means that the latitude and longitude were set by geo coding.
    MANUAL means that the latitude and longitude were selected manually.
    """

    GEO_CODING = "GEO_CODING"
    MANUAL = "MANUAL"


class Location(BaseSchema):
    latitude: float
    longitude: float
    coordinate_accuracy: Optional[FICoordinateAccuracyEnum] = None
    coordinate_source: Optional[FICoordinateSourceEnum] = None


class FIAsbestosMapping(BaseSchema):
    is_asbestos_mapping_done: Optional[FIChoiceEnum] = None
    is_asbestos_mapping_report_available: Optional[FIChoiceEnum] = None
    construction_materials_might_have_asbestos: Optional[FIChoiceEnum] = None
    description: Optional[list[FITranslatedText]] = None


class FIConstructionMaterialCodeEnum(StrEnum):
    """
    Identifies the used construction material.
    """

    BRICK = "BRICK"
    CONCRETE = "CONCRETE"
    ELEMENT = "ELEMENT"
    STEEL = "STEEL"
    STONE = "STONE"
    TIMBER = "TIMBER"
    WOOD = "WOOD"
    OTHER = "OTHER"


class FIConstructionMaterials(BaseSchema):
    type_code: Optional[list[FIConstructionMaterialCodeEnum]] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIOuterRoofTypeCodeEnum(StrEnum):
    """
    Describes the outer roof type.
    """

    GABLED = "GABLED"
    HIPPED = "HIPPED"
    PENT = "PENT"
    FLAT = "FLAT"
    GAMBREL = "GAMBREL"
    MANSARD = "MANSARD"
    SATERI = "SATERI"
    OTHER = "OTHER"


class FIOuterRoofMaterialCodeEnum(StrEnum):
    """
    Describes the outer roof material.
    """

    BRICK = "BRICK"
    SHEET_METAL = "SHEET_METAL"
    FELT = "FELT"
    BITUMEN_FELT = "BITUMEN_FELT"
    REINFORCED_CONCRETE = "REINFORCED_CONCRETE"
    PVC = "PVC"
    STONE_COATED_METAL = "STONE_COATED_METAL"
    COPPER = "COPPER"
    GREEN_ROOF = "GREEN_ROOF"
    OTHER = "OTHER"


class FIOuterRoof(BaseSchema):
    type_code: Optional[FIOuterRoofTypeCodeEnum] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    material_code: Optional[FIOuterRoofMaterialCodeEnum] = None
    other_material_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIHeatingSystemCodeEnum(StrEnum):
    """
    Identifies the used heating systems.
    """

    DISTRICT_HEATING = "DISTRICT_HEATING"
    ELECTRIC = "ELECTRIC"
    GAS = "GAS"
    GEOTHERMAL_HEATING = "GEOTHERMAL_HEATING"
    EXHAUST_AIR_HEAT_PUMP = "EXHAUST_AIR_HEAT_PUMP"
    OIL = "OIL"
    SUN = "SUN"
    WATER_HEAT_PUMP = "WATER_HEAT_PUMP"
    WOOD = "WOOD"
    OTHER = "OTHER"


class FIHeatDistributionSystemCodeEnum(StrEnum):
    """
    Identifies the used heat distribution systems.
    """

    AIR_HEAT_PUMP = "AIR_HEAT_PUMP"
    ELECTRIC_CEILING_HEATING = "ELECTRIC_CEILING_HEATING"
    ELECTRIC_RADIATOR = "ELECTRIC_RADIATOR"
    ELECTRIC_UNDERFLOOR_HEATING = "ELECTRIC_UNDERFLOOR_HEATING"
    WATER_RADIATOR = "WATER_RADIATOR"
    WATER_UNDERFLOOR_HEATING = "WATER_UNDERFLOOR_HEATING"
    OTHER = "OTHER"


class FIHeating(BaseSchema):
    system_codes: Optional[list[FIHeatingSystemCodeEnum]] = None
    system_other_description: Optional[list[FITranslatedText]] = None
    distribution_system_codes: Optional[list[FIHeatDistributionSystemCodeEnum]] = None
    distribution_system_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIResidentialPropertyOilTank(BaseSchema):
    location_description: Optional[list[FITranslatedText]] = None
    inspection_description: Optional[list[FITranslatedText]] = None
    manufacturing_year: Optional[int] = None


class FIVentilationSystemCodeEnum(StrEnum):
    """
    Identifies the used ventilation systems.
    """

    FORCED_EXHAUST = "FORCED_EXHAUST"
    FORCED_INTAKE = "FORCED_INTAKE"
    GRAVITY_BASED = "GRAVITY_BASED"
    HEAT_RECOVERY = "HEAT_RECOVERY"
    HEATING_AND_COOLING = "HEATING_AND_COOLING"


class FIVentilation(BaseSchema):
    system_codes: Optional[list[FIVentilationSystemCodeEnum]] = None
    systems_description: Optional[list[FITranslatedText]] = None


class FITelevisionTypeCodeEnum(StrEnum):
    ANTENNA = "ANTENNA"
    CABLE = "CABLE"
    IPTV = "IPTV"
    SATELLITE = "SATELLITE"


class FITelevision(BaseSchema):
    type_codes: Optional[list[FITelevisionTypeCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FIParkingSpaceTypeCodeEnum(StrEnum):
    """
    Identifies the type of the parking space. Possible values are:

    - CARPORT_SPACE: A carport space.
    - CARPORT_SPACE_WITH_ELECTRICAL_PLUG: A carport space with an electrical plug.
    - CHARGING_POINT_FOR_ELECTRICAL_CARS: A charging point for electrical cars.
    - GARAGE: A garage.
    - OUTDOOR_PARKING_SPACE: An outdoor parking space.
    - OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG: An outdoor parking space with an electrical plug.
    - PARKING_HALL_SPACE: A parking hall space.
    - PARKING_SPACE_SHARE: A parking space share.
    """

    CARPORT_SPACE = "CARPORT_SPACE"
    CARPORT_SPACE_WITH_ELECTRICAL_PLUG = "CARPORT_SPACE_WITH_ELECTRICAL_PLUG"
    CHARGING_POINT_FOR_ELECTRICAL_CARS = "CHARGING_POINT_FOR_ELECTRICAL_CARS"
    GARAGE = "GARAGE"
    OUTDOOR_PARKING_SPACE = "OUTDOOR_PARKING_SPACE"
    OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG = (
        "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG"
    )
    PARKING_HALL_SPACE = "PARKING_HALL_SPACE"
    PARKING_SPACE_SHARE = "PARKING_SPACE_SHARE"


class FIRenovationStatusCodeEnum(StrEnum):
    """
    Identifies the status of the renovation.
    Possible values:
    - PLANNED. The renovation has been planned. The actual work will be done in the future.
    - IN_PROGRESS. The renovation is in progress.
    - FINISHED. The renovation is finished.
    """

    PLANNED = "PLANNED"
    IN_PROGRESS = "IN_PROGRESS"
    FINISHED = "FINISHED"


class FIRenovationTypeCodeEnum(StrEnum):
    """
    Identifies the type of the renovation.
    Possible values:
    - KITCHEN is kitchen renovation.
    - BATHROOM is bathroom renovation.
    - FLOOR is floor renovation.
    - PLUMBING is plumbing renovation.
    - FACADE is facade renovation.
    - ROOF is roof renovation.
    - BALCONY is balcony renovation.
    - WINDOW is window renovation.
    - LIFT is lift renovation.
    - ELECTRICAL is electrical renovation.
    - SUBDRAINAGE is subdrainage renovation.
    - OTHER is other specified renovation.
    """

    KITCHEN = "KITCHEN"
    BATHROOM = "BATHROOM"
    FLOOR = "FLOOR"
    PLUMBING = "PLUMBING"
    FACADE = "FACADE"
    ROOF = "ROOF"
    BALCONY = "BALCONY"
    WINDOW = "WINDOW"
    LIFT = "LIFT"
    ELECTRICAL = "ELECTRICAL"
    SUBDRAINAGE = "SUBDRAINAGE"
    OTHER = "OTHER"


class FIResidentialTypeCodeEnum(StrEnum):
    """
    Enum: "APARTMENT_HOUSE" "DETACHED_HOUSE" "ROW_HOUSE" "SEMI_DETACHED_HOUSE" "SEPARATE_HOUSE"
          "WOODEN_HOUSE_APARTMENT" "BALCONY_ACCESS_BLOCK" "COTTAGE" "TIME_SHARE_APARTMENT"
          "LEISURE_APARTMENT" "OTHER"

    Describes the type of a leisure or residential realty. The value of this attribute must be set if
    the propertyTypeCode is LEISURE or RESIDENTIAL. Possible values are:

    - APARTMENT_HOUSE: A multi-storey building which has multiple apartments.
    - DETACHED_HOUSE: A house which is suitable for one family.
    - ROW_HOUSE: A terraced house which has apartments with a shared wall in between the apartments.
    - SEMI_DETACHED_HOUSE: A combination of detached house and row house. It has two apartments in one house
      with a shared wall in between the apartments.
    - SEPARATE_HOUSE: A house which is suitable for one family. The land is owned by a housing company.
    - WOODEN_HOUSE_APARTMENT: A wooden building which has multiple apartments. The apartment building is
      owned by a housing company.
    - BALCONY_ACCESS_BLOCK: A building where all apartments have their own front door access from the outside.
      The building is owned by a housing company.
    - COTTAGE: A leisure house. Not intended for permanent residence.
    - TIME_SHARE_APARTMENT: A share of a leisure apartment with a pre-defined time allocation.
    - LEISURE_APARTMENT: A leisure apartment. Not intended for permanent residence.
    - OTHER: None of the above.
    """

    APARTMENT_HOUSE = "APARTMENT_HOUSE"
    DETACHED_HOUSE = "DETACHED_HOUSE"
    ROW_HOUSE = "ROW_HOUSE"
    SEMI_DETACHED_HOUSE = "SEMI_DETACHED_HOUSE"
    SEPARATE_HOUSE = "SEPARATE_HOUSE"
    WOODEN_HOUSE_APARTMENT = "WOODEN_HOUSE_APARTMENT"
    BALCONY_ACCESS_BLOCK = "BALCONY_ACCESS_BLOCK"
    COTTAGE = "COTTAGE"
    TIME_SHARE_APARTMENT = "TIME_SHARE_APARTMENT"
    LEISURE_APARTMENT = "LEISURE_APARTMENT"
    OTHER = "OTHER"


class FIAdministrationTypeCodeEnum(StrEnum):
    """
    Enum: "APARTMENT_HOUSING_COMPANY" "REAL_ESTATE_COMPANY" "PART_OWNERSHIP" "OTHER"

    - APARTMENT_HOUSING_COMPANY: One housing company consists of apartments in one or several buildings.
      Parties who own apartments in the buildings form the shareholders of the company.
    - REAL_ESTATE_COMPANY: One real estate company consists of several real estates. Parties who own
      real-estates in the company form the shareholders of the company.
    - PART_OWNERSHIP: Co-ownership housing is a form of housing in which the resident redeems parts of
      the apartment as his or her own. When moving into an apartment, a 10-15% share of the price of the
      apartment is paid, in which case the resident owns a minority share of the apartment. After this,
      the resident lives in his or her co-owned apartment with a fixed-term lease, but can redeem the
      apartment as his or her own within the agreed time. Co-ownership dwellings are both state-subsidized
      and non-subsidized dwellings, the form of which is partly owned by the developer.
    - OTHER: The administration type is none of the above. Note that if you use this value, you must describe
      the administration type in the value of the administrationTypeOtherDescription attribute.
    """

    APARTMENT_HOUSING_COMPANY = "APARTMENT_HOUSING_COMPANY"
    REAL_ESTATE_COMPANY = "REAL_ESTATE_COMPANY"
    PART_OWNERSHIP = "PART_OWNERSHIP"
    OTHER = "OTHER"


class FIAdministration(BaseSchema):
    type_code: Optional[FIAdministrationTypeCodeEnum] = None
    type_other_description: Optional[list[FITranslatedText]] = None


class FIRealtyInspectionTypeCodeEnum(StrEnum):
    """
    Identifies the type of the inspection.
    Possible values:
    - CONDITION_INSPECTION
    - HUMIDITY_INSPECTION
    """

    CONDITION_INSPECTION = "CONDITION_INSPECTION"
    HUMIDITY_INSPECTION = "HUMIDITY_INSPECTION"


class FIRealtyInspection(BaseSchema):
    date: Optional[str] = None
    description: Optional[list[FITranslatedText]] = None
    type_code: FIRealtyInspectionTypeCodeEnum


class FIApartmentRoomCountCodeEnum(StrEnum):
    """
    Identifies the number of rooms of the apartment. Possible values are:

    - 1H: The apartment has one room.
    - 2H: The apartment has two rooms.
    - 3H: The apartment has three rooms.
    - 4H: The apartment has four rooms.
    - 5H: The apartment has five rooms.
    - 5H+: The apartment has more than five rooms.
    """

    _1H = "1H"
    _2H = "2H"
    _3H = "3H"
    _4H = "4H"
    _5H = "5H"
    _5H_PLUS = "5H+"


class FIFloorSurfaceMaterialCodeEnum(StrEnum):
    """
    Describes the room's floor surface materials.
    """

    TILED = ("TILED", "laatta")
    LAMINATE = ("LAMINATE", "laminaatti")
    PARQUET = ("PARQUET", "parketti")
    PLASTIC = ("PLASTIC", "muovi")
    BOARD = ("BOARD", "lauta")
    STONE = ("STONE", "kivi")
    CONCRETE = ("CONCRETE", "betoni")
    MICROCEMENT = ("MICROCEMENT", "")
    VINYL = ("VINYL", "vinyyli")
    VINYL_CORK = ("VINYL_CORK", "vinyylikorkki")
    CORK = ("CORK", "korkki")
    ACRYLIC_MASS = ("ACRYLIC_MASS", "")
    WALL_TO_WALL_CARPET = ("WALL_TO_WALL_CARPET", "")
    OTHER = ("OTHER", "muu")

    def __new__(cls, english_name, finnish_name):
        obj = str.__new__(cls, english_name)
        obj._value_ = english_name
        obj.finnish_name = finnish_name
        return obj

    @classmethod
    def from_finnish_name(cls, finnish_name: str):
        if not finnish_name:
            return finnish_name
        for item in cls:
            if item.finnish_name == finnish_name:
                return item
        return None


class FIWallSurfaceMaterialCodeEnum(StrEnum):
    """
    Describes the room's wall surface materials.
    """

    CERAMIC_TILE = ("CERAMIC_TILE", "kaakeli")
    WOOD = ("WOOD", "puu")
    LOG = ("LOG", "")
    PANEL = ("PANEL", "")
    WAINSCOT = ("WAINSCOT", "puolipaneeli")
    WALLPAPER = ("WALLPAPER", "tapetti")
    GLASS_FIBRE_TEXTILE_COVERED = ("GLASS_FIBRE_TEXTILE_COVERED", "lasikuitutapetti")
    GLASS = ("GLASS", "")
    PARTIALLY_TILED = ("PARTIALLY_TILED", "osalaatoitus")
    PLASTIC = ("PLASTIC", "muovi")
    STONE = ("STONE", "kivi")
    CONCRETE = ("CONCRETE", "betoni")
    MICROCEMENT = ("MICROCEMENT", "")
    PAINT = ("PAINT", "maalattu")
    OTHER = ("OTHER", "muu")

    def __new__(cls, english_name, finnish_name):
        obj = str.__new__(cls, english_name)
        obj._value_ = english_name
        obj.finnish_name = finnish_name
        return obj

    @classmethod
    def from_finnish_name(cls, finnish_name: str):
        if not finnish_name:
            return finnish_name
        for item in cls:
            if item.finnish_name == finnish_name:
                return item
        return None


class FICeilingSurfaceMaterialCodeEnum(StrEnum):
    """
    Describes the room's ceiling surface materials.
    """

    PLASTER = "PLASTER"
    PANEL = "PANEL"
    STONE = "STONE"
    PAINTED = "PAINTED"
    WOOD = "WOOD"
    OTHER = "OTHER"


class FITypeCodeEnum(StrEnum):
    """
    Identifies the type of the room.
    """

    BATH_ROOM = "BATH_ROOM"
    BEDROOM = "BEDROOM"
    DINING_ROOM = "DINING_ROOM"
    DRAUGHT_LOBBY = "DRAUGHT_LOBBY"
    HALL = "HALL"
    HALLWAY = "HALLWAY"
    KITCHEN = "KITCHEN"
    LIBRARY = "LIBRARY"
    LIVING_ROOM = "LIVING_ROOM"
    LOFT = "LOFT"
    SAUNA = "SAUNA"
    STUDY = "STUDY"
    TOILET = "TOILET"
    UTILITY_ROOM = "UTILITY_ROOM"
    WALK_IN_CLOSET = "WALK_IN_CLOSET"
    LAUNDRY = "LAUNDRY"
    OTHER = "OTHER"


class FeatureCodeEnum(StrEnum):
    WC = "WC"
    SHOWER = "SHOWER"
    TWO_SHOWERS = "TWO_SHOWERS"
    SHOWER_WALL = "SHOWER_WALL"
    WALK_IN_SHOWER = "WALK_IN_SHOWER"
    WASHING_MACHINE_CONNECTION = "WASHING_MACHINE_CONNECTION"
    FIXED_LAMPS = "FIXED_LAMPS"
    WASHING_MACHINE = "WASHING_MACHINE"
    TUMBLE_DRYER = "TUMBLE_DRYER"
    DRYING_CABINET = "DRYING_CABINET"
    MIRROR = "MIRROR"
    MIRROR_CABINET = "MIRROR_CABINET"
    BASIN_CABINET = "BASIN_CABINET"
    JACUZZI = "JACUZZI"
    BATHTUB = "BATHTUB"
    BATHROOM_CABINETS = "BATHROOM_CABINETS"
    REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT = (
        "REFRIGERATOR_WITH_SMALL_FREEZER_COMPARTMENT"
    )
    REFRIGERATOR_FREEZER = "REFRIGERATOR_FREEZER"
    REFRIGERATOR = "REFRIGERATOR"
    REFRIGERATED_CABINET = "REFRIGERATED_CABINET"
    COLD_ROOM = "COLD_ROOM"
    FREEZER = "FREEZER"
    REFRIGERATOR_CHILLER = "REFRIGERATOR_CHILLER"
    DISHWASHER = "DISHWASHER"
    DISHWASHER_CONNECTION = "DISHWASHER_CONNECTION"
    RESERVED_LOCATION_FOR_DISHWASHER = "RESERVED_LOCATION_FOR_DISHWASHER"
    INTEGRATED_DISHWASHER = "INTEGRATED_DISHWASHER"
    FREE_STANDING_ISLANDS = "FREE_STANDING_ISLANDS"
    FREE_STANDING_CABINETS = "FREE_STANDING_CABINETS"
    WINE_CABINET = "WINE_CABINET"
    INTEGRATED_HOUSEHOLD_APPLIANCES = "INTEGRATED_HOUSEHOLD_APPLIANCES"
    CERAMIC_STOVE = "CERAMIC_STOVE"
    HOB = "HOB"
    BAKING_OVEN = "BAKING_OVEN"
    COOKTOP = "COOKTOP"
    INTEGRATED_STOVE = "INTEGRATED_STOVE"
    EXTRACTOR_HOOD = "EXTRACTOR_HOOD"
    WOOD_BURNING_STOVE = "WOOD_BURNING_STOVE"
    ELECTRIC_STOVE = "ELECTRIC_STOVE"
    INDUCTION_STOVE = "INDUCTION_STOVE"
    GAS_STOVE = "GAS_STOVE"
    MICROWAVE_OVEN = "MICROWAVE_OVEN"
    SEPARATE_OVEN = "SEPARATE_OVEN"
    EXTRACTOR_HOOD_WITH_FLUE = "EXTRACTOR_HOOD_WITH_FLUE"
    COOKER_HOOD = "COOKER_HOOD"
    CONCRETE = "CONCRETE"
    STONE = "STONE"
    WOOD = "WOOD"
    LAMINATE = "LAMINATE"
    COMPOSITE = "COMPOSITE"
    METAL = "METAL"
    ROOM_WITH_FIREPLACE = "ROOM_WITH_FIREPLACE"
    ELECTRIC_HEATER = "ELECTRIC_HEATER"
    WOOD_HEATED_SAUNA_STOVE = "WOOD_HEATED_SAUNA_STOVE"
    ALWAYS_READY_HEATER = "ALWAYS_READY_HEATER"
    READY_FOR_ELECTRIC_HEATER = "READY_FOR_ELECTRIC_HEATER"
    INTEGRATED_BUCKET = "INTEGRATED_BUCKET"
    WATER_FAUCET = "WATER_FAUCET"
    OPTICAL_FIBRE_LIGHTING = "OPTICAL_FIBRE_LIGHTING"
    LED_LIGHTING = "LED_LIGHTING"
    WINDOW_OUT = "WINDOW_OUT"
    FLOOR_MOUNTED_WC = "FLOOR_MOUNTED_WC"
    WALL_HUNG_WC = "WALL_HUNG_WC"
    BIDET = "BIDET"
    UNDERFLOOR_HEATING = "UNDERFLOOR_HEATING"
    FLOOR_DRAIN = "FLOOR_DRAIN"
    LAUNDRY_CABINETS = "LAUNDRY_CABINETS"
    TABLE_TOP = "TABLE_TOP"
    IRONING_TABLE_BOARD = "IRONING_TABLE_BOARD"
    BABY_CHANGING_TABLE = "BABY_CHANGING_TABLE"
    SINK = "SINK"
    SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR = "SHOWER_AND_DRAIN_BY_EXTERIOR_DOOR"
    CENTRAL_VACUUM_UNIT = "CENTRAL_VACUUM_UNIT"
    EXIT = "EXIT"


class FIApartmentRoomFeature(BaseSchema):
    feature_codes: Optional[list[FeatureCodeEnum]] = None
    description: list[FITranslatedText] = []


class FIApartmentRoom(BaseSchema):
    type_code: Optional[FITypeCodeEnum] = None
    description: list[FITranslatedText] = []
    features: Optional[list[FIApartmentRoomFeature]] = None
    floor_material_codes: list[FIFloorSurfaceMaterialCodeEnum] = []
    wall_material_codes: list[FIWallSurfaceMaterialCodeEnum] = []
    ceiling_material_codes: list[FICeilingSurfaceMaterialCodeEnum] = []


class FICompassPointEnum(StrEnum):
    """
    Identifies the compass points in which the terrace is facing.
    """

    NORTH = "NORTH"
    EAST = "EAST"
    SOUTH = "SOUTH"
    WEST = "WEST"
    NORTHEAST = "NORTHEAST"
    SOUTHEAST = "SOUTHEAST"
    SOUTHWEST = "SOUTHWEST"
    NORTHWEST = "NORTHWEST"


class FIAreaBasisCodeEnum(StrEnum):
    """
    Identifies the methods used to determine the area of the apartment. Possible values are:

    - ARTICLES_OF_ASSOCIATION: The area is determined based on the articles of association.
    - MANAGERS_CERTIFICATE: The area is determined based on the manager's certificate.
    - VERIFYING_MEASUREMENT: The area is determined based on verifying measurement.
    """

    ARTICLES_OF_ASSOCIATION = "ARTICLES_OF_ASSOCIATION"
    MANAGERS_CERTIFICATE = "MANAGERS_CERTIFICATE"
    VERIFYING_MEASUREMENT = "VERIFYING_MEASUREMENT"
    CLIENT_REPORTED = "CLIENT_REPORTED"


class FIApartmentArea(BaseSchema):
    area_basis_codes: Optional[list[FIAreaBasisCodeEnum]] = None
    living_area: Optional[FICommonArea] = None
    other_space_area: Optional[FICommonArea] = None
    total_area: Optional[FICommonArea] = None


class FIResidentialPropertyBalconyTypeCodeEnum(StrEnum):
    """
    Identifies the types of the balconies.
    """

    FRENCH_WINDOW = "FRENCH_WINDOW"
    GLAZED = "GLAZED"
    PROTRUDING = "PROTRUDING"
    RETRACTED = "RETRACTED"
    OTHER = "OTHER"


class FIHearthTypeCodeEnum(StrEnum):
    """
    Identifies the types of hearths found from an apartment.
    """

    CONVECTION_FIREPLACE = "CONVECTION_FIREPLACE"
    FIREPLACE = "FIREPLACE"
    FLUE_IN_PLACE = "FLUE_IN_PLACE"
    HEAT_RETAINING_FIREPLACE = "HEAT_RETAINING_FIREPLACE"
    IRON_STOVE = "IRON_STOVE"
    OPEN_FIREPLACE = "OPEN_FIREPLACE"
    PLACE_ALLOCATED_FOR_FIREPLACE = "PLACE_ALLOCATED_FOR_FIREPLACE"
    BAKING_OVEN = "BAKING_OVEN"
    OTHER = "OTHER"


class FIHearth(BaseSchema):
    type_codes: Optional[list[FIHearthTypeCodeEnum]] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FILivingFloorCountCodeEnum(StrEnum):
    """
    Identifies the number of apartment's floors which can be used for living.
    """

    SINGLE_FLOOR = "SINGLE_FLOOR"
    TWO_FLOORS = "TWO_FLOORS"
    FLOOR_AND_A_HALF = "FLOOR_AND_A_HALF"
    MORE_THAN_TWO_FLOORS = "MORE_THAN_TWO_FLOORS"
    NOT_KNOWN = "NOT_KNOWN"


class FIPropertyStorageTypeCodeEnum(StrEnum):
    ATTIC = "ATTIC"
    CELLAR = "CELLAR"
    OUTDOOR = "OUTDOOR"
    REFRIGERATED_CELLAR = "REFRIGERATED_CELLAR"
    OTHER = "OTHER"


class FIRedemption(BaseSchema):
    redeemable_by_housing_company: Optional[FIChoiceEnum] = None
    redeemable_by_existing_shareholders: Optional[FIChoiceEnum] = None
    redemption_right_applies_to_all_shares: Optional[FIChoiceEnum] = None
    other_restrictions: Optional[list[FITranslatedText]] = []


class FIShareCertificateFormEnum(StrEnum):
    """
    Enum: "DIGITAL" "PAPER" "OTHER"

    Defines the form for the share certificate. Possible values are:

    - DIGITAL: The share certificate is in digital form.
    - PAPER: The share certificate is in paper form.
    - OTHER: The form of the share certificate is other than digital or paper.
    """

    DIGITAL = "DIGITAL"
    PAPER = "PAPER"
    OTHER = "OTHER"


class FIShareCertificate(BaseSchema):
    share_certificate_available: Optional[FIChoiceEnum] = None
    mortgage_declared_separately: Optional[FIChoiceEnum] = None
    share_certificate_used_as_mortgage: Optional[FIChoiceEnum] = None
    # Name and contact details for the mortgage holder. This is mandatory if the asset is mortgaged.
    mortgage_holder: Optional[str] = None
    # Mortgage amount.
    liability_amount: Optional[int] = None
    currency_code: Optional[str] = None
    share_certificate_location: Optional[str] = None
    # The share numbers of the shares in accordance with the agreement or articles of association.
    share_group_identifiers: Optional[list[str]] = None
    quantity_of_shares: Optional[int] = None
    share_certificate_form_code: Optional[FIShareCertificateFormEnum] = None
    # Permanent dwelling identifier (VTJ-PHT) is a 10-character code given to
    # every apartment. There must not be any leading or trailing spaces.
    permanent_dwelling_identifier: Optional[str] = None


class FIHousingCompanyParkingSpace(BaseSchema):
    parking_space_type_code: Optional[FIParkingSpaceTypeCodeEnum] = None
    count: Optional[int] = None
    parking_space_descriptions: Optional[list[FITranslatedText]] = []


class FIRenovationShare(BaseSchema):
    date: Optional[str] = None
    decided_by_general_meeting: Optional[FIChoiceEnum] = None
    description: Optional[list[FITranslatedText]] = None
    is_housing_company_notified: Optional[FIChoiceEnum] = None
    status_code: Optional[FIRenovationStatusCodeEnum] = None
    type_code: Optional[FIRenovationTypeCodeEnum] = None
    type_other_description: Optional[list[FITranslatedText]] = None


class FiPortals(BaseSchema):
    is_oikotie_enabled: bool = False
