from datetime import datetime
from enum import StrEnum
from typing import Optional
from ..base import BaseSchema


class FIAreaKindEnum(StrEnum):
    MAJOR_REGION = "MAJOR_REGION"
    REGION = "REGION"
    SUBREGION = "SUBREGION"
    MUNICIPALITY = "MUNICIPALITY"
    DISTRICT = "DISTRICT"


class FIAreaRead(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime

    kind: str
    area_code: str
    area_name_en: str
    area_name_fi: str
    area_name_sv: str
    parent_id: Optional[int] = None


class FIAreaFilterParams(BaseSchema):
    withDistricts: bool = False
