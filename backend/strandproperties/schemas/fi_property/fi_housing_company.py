from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_address import (
    FIAddressCreate,
    FIAddressRead,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIAsbestosMapping,
    FIChoiceEnum,
    FICommonArea,
    FIConstructionMaterials,
    FIEnergyCertificate,
    FIHeating,
    FIHousingCompanyParkingSpace,
    FIOuterRoof,
    FIRenovationShare,
    FITelevision,
    FITranslatedText,
    FIVentilation,
)
from strandproperties.schemas.fi_property.fi_housing_company_charges import (
    FIFinancingCharge,
    FIPlotCharges,
)
from strandproperties.schemas.fi_property.fi_plot import (
    F<PERSON>lotOverviewListRead,
    FIPlotOverviewRead,
)


class FISchemeIdEnum(StrEnum):
    """
    Specifies the format of the business ID. Possible values are:

    - FI:ORGNR The business ID uses the Finnish proprietary format. It consists of seven digits,
      a dash and a control mark, for example 0247238-2.
    - VAT The business ID is a VAT number. You can generate VAT number from the Finnish business ID
      by adding a two-letter country code FI as a prefix and by omitting the dash. In this example,
      the VAT number would be FI02472382.
    """

    FI_ORGNR = "FI:ORGNR"
    VAT = "VAT"


class FIHousingCompanyMaintenanceTypeCodeEnum(StrEnum):
    """
    Describes who is responsible of maintaining the housing company.
    """

    JANITOR = "JANITOR"
    PROPERTY_MAINTENANCE_COMPANY = "PROPERTY_MAINTENANCE_COMPANY"
    BY_RESIDENTS = "BY_RESIDENTS"


class FIDevelopmentPhaseCodeEnum(StrEnum):
    """
    Identifies the development phase of the housing company. The value of this attribute must be set only
    if the realty is a new building. Possible values are:

    PRE_MARKETING. Construction work hasn't started yet and the developer is premarketing the realty.
    IN_CONSTRUCTION. The housing company is under construction.
    MOVE_IN_READY. The construction work has been finished and people can move in to the apartments.
    """

    PRE_MARKETING = "PRE_MARKETING"
    IN_CONSTRUCTION = "IN_CONSTRUCTION"
    MOVE_IN_READY = "MOVE_IN_READY"


class FIHousingCompanyPremiseTypeCodeEnum(StrEnum):
    APARTMENT_SPECIFIC_STORAGE = "APARTMENT_SPECIFIC_STORAGE"
    ATTIC_STORAGE = "ATTIC_STORAGE"
    BALCONY = "BALCONY"
    CELLAR_STORAGE = "CELLAR_STORAGE"
    CRAFT_ROOM = "CRAFT_ROOM"
    DRYING_ROOM = "DRYING_ROOM"
    EMERGENCY_SHELTER = "EMERGENCY_SHELTER"
    LAUNDRY_ROOM = "LAUNDRY_ROOM"
    MANGLE_ROOM = "MANGLE_ROOM"
    MOVABLE_PROPERTY_STORAGE = "MOVABLE_PROPERTY_STORAGE"
    REFRIGERATED_CELLAR = "REFRIGERATED_CELLAR"
    SPORTS_EQUIPMENT_STORAGE = "SPORTS_EQUIPMENT_STORAGE"
    SWIMMING_POOL = "SWIMMING_POOL"
    OTHER = "OTHER"


class FIPremiseTypeCodeEnum(StrEnum):
    APARTMENT = "APARTMENT"
    BUSINESS_PREMISE = "BUSINESS_PREMISE"
    OTHER_PREMISE = "OTHER_PREMISE"


class FIHousingCompanyInspectionTypeCodeEnum(StrEnum):
    """
    Identifies the type of the inspection. Possible values are:

    CONDITION_ASSESSMENT. The condition of the housing company has been assessed by doing a sensory inspection.
    CONDITION_INSPECTION. The condition of the housing company has been inspected by taking a closer look at
    the structures of the buildings of the housing company.
    CONDITION_SURVEY. The housing company has ordered an inspection which documents the condition of an "critical part"
    of its buildings. For example, the condition survey can be done to figure out the condition of water and sewage
    pipes, electric wires, balconies, outer roof and ventilation systems.
    HUMIDITY_MEASUREMENT. Humidity measurement has been done to the housing company.
    MAINTENANCE_NEED_STATEMENT. The housing company has identified and documented the renovations which has to be done
    during the next five years.
    LONG_TERM_PLAN. The housing company has identified and documented the renovations which has to be done during the
    next ten years. Also, the housing company has estimated the budgets of these renovations.
    """

    CONDITION_ASSESSMENT = "CONDITION_ASSESSMENT"
    CONDITION_INSPECTION = "CONDITION_INSPECTION"
    CONDITION_SURVEY = "CONDITION_SURVEY"
    HUMIDITY_MEASUREMENT = "HUMIDITY_MEASUREMENT"
    MAINTENANCE_NEED_STATEMENT = "MAINTENANCE_NEED_STATEMENT"
    LONG_TERM_PLAN = "LONG_TERM_PLAN"


class FIBusinessIdentification(BaseSchema):
    business_id: str
    scheme_id: Optional[FISchemeIdEnum] = None


class FIHousingCompanyMaintenance(BaseSchema):
    type_codes: Optional[list[FIHousingCompanyMaintenanceTypeCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FIHousingCompanyConstruction(BaseSchema):
    builder: Optional[str] = None
    construction_year: Optional[int] = None
    development_phase_code: Optional[FIDevelopmentPhaseCodeEnum] = None
    usage_start_year: Optional[int] = None
    construction_and_usage_year_description: Optional[list[FITranslatedText]] = None


class FIHousingCompanyPremise(BaseSchema):
    type_codes: Optional[list[FIHousingCompanyPremiseTypeCodeEnum]] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIHousingCompanyBuildings(BaseSchema):
    construction_material: Optional[FIConstructionMaterials] = None
    heating: Optional[FIHeating] = None
    ventilation: Optional[FIVentilation] = None
    outer_roof: Optional[FIOuterRoof] = None
    has_elevator: Optional[FIChoiceEnum] = None
    elevator_takes_to_apartment: Optional[FIChoiceEnum] = None
    has_sauna: Optional[FIChoiceEnum] = None
    premises: Optional[FIHousingCompanyPremise] = None


class FIHousingCompanyInternetConnections(BaseSchema):
    broadband_available: Optional[FIChoiceEnum] = None
    fiber_optic_internet: Optional[FIChoiceEnum] = None
    housing_company_broadband: Optional[FIChoiceEnum] = None
    broadband_operator: Optional[str] = None
    internal_network_cabling: Optional[FIChoiceEnum] = None
    network_description: Optional[list[FITranslatedText]] = None


class FIHousingCompanyFinances(BaseSchema):
    loan_amount: Optional[float] = None
    mortgage_amount: Optional[float] = None
    # The yearly rent revenue of the housing company.
    rent_revenue: Optional[int] = None
    # If the housing company has a bank account with credit limit, the value of this attribute identifies
    # the credit limit of that bank account.
    bank_account_credit_limit: Optional[int] = None
    currency_code: Optional[str] = None
    financing_fee_interest_only_period: Optional[list[FITranslatedText]] = None
    financing_fee_interest_only_startdate: Optional[str] = None
    financing_fee_interest_only_enddate: Optional[str] = None
    financing_fee_after_interest_only: Optional[int] = None
    management_charge_after_interest_only: Optional[int] = None
    management_charges_info_link: Optional[str] = None


class FIHousingCompanyPremiseStatistic(BaseSchema):
    premise_type_code: Optional[FIPremiseTypeCodeEnum] = None
    count: Optional[int] = None
    area: Optional[FICommonArea] = None
    managed_by_housing_company_count: Optional[int] = None
    area_managed_by_housing_company: Optional[FICommonArea] = None
    housing_company_premise_statistics_descriptions: Optional[
        list[FITranslatedText]
    ] = []


class FIHousingCompanyInspection(BaseSchema):
    date: Optional[str] = None
    description: Optional[list[FITranslatedText]] = None
    type_code: FIHousingCompanyInspectionTypeCodeEnum | None


class _FIHousingCompanyBasicDetails(BaseSchema):
    pass


class _FIHousingCompanyFullDetails(_FIHousingCompanyBasicDetails):
    business_id: Optional[str] = None
    name: Optional[str] = None
    street_address: Optional[str] = None
    postal_area: Optional[str] = None
    manager_name: Optional[str] = None
    manager_contact_details: Optional[str] = None
    maintenance: Optional[FIHousingCompanyMaintenance] = None
    # The property identifier is a four part code which uses the format: xxx-xxx-xxxx-xxxx.
    # You can find the the property identifier from the manager's certificate or you can use
    # the website of the National Land Survey:
    # https://www.maanmittauslaitos.fi/kiinteistot/palvelut/selvita-kiinteistotunnus
    property_identifier: Optional[str] = None
    construction: Optional[FIHousingCompanyConstruction] = None
    buildings: Optional[FIHousingCompanyBuildings] = None
    yard_description: Optional[list[FITranslatedText]] = None
    internet_connections: Optional[FIHousingCompanyInternetConnections] = None
    television: Optional[FITelevision] = None
    last_annual_general_meeting_date: Optional[str] = None
    next_annual_general_meeting_date: Optional[str] = None
    identified_deficiencies: Optional[list[FITranslatedText]] = None
    cost_incurring_liabilities: Optional[list[FITranslatedText]] = None
    repairs_and_maintenance_agreements: Optional[list[FITranslatedText]] = None
    house_manager_certificate: Optional[str] = None
    finances: Optional[FIHousingCompanyFinances] = None
    energy_certificate: Optional[FIEnergyCertificate] = None
    asbestos_mapping: Optional[FIAsbestosMapping] = None
    # Identifies the number of parking spaces available to the shareholders of the housing company.
    parking_spaces: Optional[list[FIHousingCompanyParkingSpace]] = None
    parking_spaces_description: Optional[list[FITranslatedText]] = None
    housing_company_premise_statistics: Optional[
        list[FIHousingCompanyPremiseStatistic]
    ] = None
    housing_company_premise_statistics_description: Optional[list[FITranslatedText]] = (
        None
    )
    # Charges
    management_charge: Optional[float] = None
    financing_charge: Optional[FIFinancingCharge] = None
    plot_charges: Optional[FIPlotCharges] = None
    special_charge: Optional[float] = None
    maintenance_charge: Optional[float] = None
    charges_description: Optional[list[FITranslatedText]] = None

    renovations: Optional[list[FIRenovationShare]] = None
    renovations_description: Optional[list[FITranslatedText]] = None
    renovations_planned_description: Optional[list[FITranslatedText]] = None
    inspections: Optional[list[FIHousingCompanyInspection]] = None
    inspections_description: Optional[list[FITranslatedText]] = None
    additional_description: Optional[list[FITranslatedText]] = None
    list_of_shares_transferred: Optional[FIChoiceEnum] = None
    digital_share_group_identifier: Optional[str] = None


class FIHousingCompanyCreateEdit(_FIHousingCompanyFullDetails):
    fi_address: Optional[FIAddressCreate] = None
    fi_plot_overview_id: Optional[int] = None
    fi_plot_overviews: Optional[list[FIPlotOverviewRead]] = None


class FIHousingCompanyListRead(_FIHousingCompanyBasicDetails):
    id: int
    fi_address: Optional[FIAddressRead] = None
    business_id: Optional[str] = None
    name: Optional[str] = None
    property_identifier: Optional[str] = None
    scheme_id: Optional[str] = None


class FIHousingCompanyRead(_FIHousingCompanyFullDetails, FIHousingCompanyListRead):
    fi_plot_overview_id: Optional[int] = None


class FIHousingCompanyFilterParam(BaseSchema):
    keyword: Optional[str] = None
