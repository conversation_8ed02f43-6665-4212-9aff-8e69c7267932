from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIApartmentArea,
    FIApartmentRoom,
    FIApartmentRoomCountCodeEnum,
    FIAsbestosMapping,
    FIChoiceEnum,
    FICompassPointEnum,
    FIConstructionMaterials,
    FIEnergyCertificate,
    FIHearth,
    FIHeating,
    FIHousingCompanyParkingSpace,
    FILivingFloorCountCodeEnum,
    FIOuterRoof,
    FIRealtyInspection,
    FIRenovationStatusCodeEnum,
    FIRenovationTypeCodeEnum,
    FIResidentialPropertyBalconyTypeCodeEnum,
    FIResidentialPropertyOilTank,
    FIResidentialTypeCodeEnum,
    FITranslatedText,
    FIVentilation,
)
from strandproperties.schemas.fi_property.fi_residential_share import (
    FIOwnershipTypeCodeEnum,
)


class FIDevelopmentPhaseCodeEnum(StrEnum):
    """
    Enum: "PRE_MARKETING" "IN_CONSTRUCTION" "MOVE_IN_READY"

    Describes the development phase the property is in. Possible values are:

    - PRE_MARKETING: Construction has not started but marketing is underway.
    - IN_CONSTRUCTION: The property is being built.
    - MOVE_IN_READY: The property construction has completed and it is ready for moving in.
    """

    PRE_MARKETING = "PRE_MARKETING"
    IN_CONSTRUCTION = "IN_CONSTRUCTION"
    MOVE_IN_READY = "MOVE_IN_READY"


class FIFoundationTypeEnum(StrEnum):
    """
    Describes the property's foundation type. Possible values are:

    - PAD_AND_PLINTH_FOUNDATION: A foundation construction with pad and plinth.
    - PILE_FOUNDATION: A foundation construction with piles.
    - PIER_FOUNDATION: A foundation construction with piers.
    - EDGE_REINFORCED_SLAB: A foundation construction with edge reinforced slab.
    - VENTILATED_FOUNDATION: A foundation construction with ventilation.
    - FALSE_STEM_WALL: A slab based foundation construction with false stem wall.
    """

    PAD_AND_PLINTH_FOUNDATION = "PAD_AND_PLINTH_FOUNDATION"
    PILE_FOUNDATION = "PILE_FOUNDATION"
    PIER_FOUNDATION = "PIER_FOUNDATION"
    EDGE_REINFORCED_SLAB = "EDGE_REINFORCED_SLAB"
    VENTILATED_FOUNDATION = "VENTILATED_FOUNDATION"
    FALSE_STEM_WALL = "FALSE_STEM_WALL"


class FIPropertyInspections(BaseSchema):
    commissioning_inspection_done: Optional[FIChoiceEnum] = None
    # Specifies the year the commissioning inspection has been done when the
    # value of the commissioningInspectionDone attribute is "YES".
    commissioning_inspection_year: Optional[int] = None
    are_commissioning_inspection_fixes_done: Optional[FIChoiceEnum] = None
    final_inspection_done: Optional[FIChoiceEnum] = None
    final_inspection_year: Optional[int] = None
    are_final_inspection_fixes_done: Optional[FIChoiceEnum] = None
    inspections_description: Optional[list[FITranslatedText]] = None


class FIResidentialPropertyFoundation(BaseSchema):
    type_code: Optional[FIFoundationTypeEnum] = None
    description: Optional[list[FITranslatedText]] = None


class FIResidentialPropertyTerrace(BaseSchema):
    is_glazed_terrace: Optional[FIChoiceEnum] = None
    description: Optional[list[FITranslatedText]] = None
    compass_points: Optional[list[FICompassPointEnum]] = None


class FIResidentialPropertyBalcony(BaseSchema):
    type_codes: Optional[list[FIResidentialPropertyBalconyTypeCodeEnum]] = None
    type_other_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None
    compass_points: Optional[list[FICompassPointEnum]] = None


class FIResidentialPropertyFloors(BaseSchema):
    living_floor_count: Optional[str] = None
    living_floor_count_code: Optional[FILivingFloorCountCodeEnum] = None


class FIRenovationProperty(BaseSchema):
    date: str
    description: list[FITranslatedText]
    status_code: FIRenovationStatusCodeEnum
    type_code: FIRenovationTypeCodeEnum
    type_other_description: Optional[list[FITranslatedText]] = None


class FIResidentialPropertyApartment(BaseSchema):
    room_count_code: Optional[FIApartmentRoomCountCodeEnum] = None
    area: Optional[FIApartmentArea] = None
    rooms: Optional[list[FIApartmentRoom]] = None
    number_of_toilets: Optional[int] = None
    number_of_bedrooms: Optional[int] = None
    foundation: Optional[FIResidentialPropertyFoundation] = None
    construction_materials: Optional[FIConstructionMaterials] = None
    outer_roof: Optional[FIOuterRoof] = None
    heating: Optional[FIHeating] = None
    oil_tank: Optional[FIResidentialPropertyOilTank] = None
    ventilation: Optional[FIVentilation] = None
    hearth: Optional[FIHearth] = None
    energy_certificate: Optional[FIEnergyCertificate] = None
    has_terrace: Optional[FIChoiceEnum] = None
    terrace: Optional[FIResidentialPropertyTerrace] = None
    patio_description: Optional[list[FITranslatedText]] = None
    has_balcony: Optional[FIChoiceEnum] = None
    balcony: Optional[FIResidentialPropertyBalcony] = None
    floors: Optional[FIResidentialPropertyFloors] = None
    renovations: Optional[list[FIRenovationProperty]] = None
    renovations_description: Optional[list[FITranslatedText]] = None
    realty_inspections: Optional[list[FIRealtyInspection]] = None
    asbestos_mapping: Optional[FIAsbestosMapping] = None
    living_quarters_description: Optional[list[FITranslatedText]] = None
    interior_material_description: Optional[list[FITranslatedText]] = None
    refrigerated_cellar_description: Optional[list[FITranslatedText]] = None
    boiler_room_description: Optional[list[FITranslatedText]] = None
    underfloor_heating_description: Optional[list[FITranslatedText]] = None
    utility_services_room_description: Optional[list[FITranslatedText]] = None
    storage_description: Optional[list[FITranslatedText]] = None
    things_effecting_housing_comfort: Optional[list[FITranslatedText]] = None
    view_description: Optional[list[FITranslatedText]] = None
    has_own_sauna: Optional[FIChoiceEnum] = None
    is_fit_for_winter_habitation: Optional[FIChoiceEnum] = None
    modification_documents: Optional[list[FITranslatedText]] = None
    smoking_allowed: Optional[FIChoiceEnum] = None
    pets_allowed: Optional[FIChoiceEnum] = None
    suitable_for_disabled: Optional[FIChoiceEnum] = None
    furnished: Optional[FIChoiceEnum] = None


class _FIResidentialPropertyOverviewBasicDetails(BaseSchema):
    apartment: Optional[FIResidentialPropertyApartment] = None


class _FIResidentialPropertyOverviewFullDetails(
    _FIResidentialPropertyOverviewBasicDetails
):
    residential_type_code: Optional[FIResidentialTypeCodeEnum] = None
    ownership_type_code: Optional[FIOwnershipTypeCodeEnum] = None
    # The year construction permit was granted.
    construction_permit_grant_year: Optional[int] = None
    construction_start_year: Optional[int] = None
    construction_end_year: Optional[int] = None
    usage_start_year: Optional[int] = None
    development_phase_code: Optional[FIDevelopmentPhaseCodeEnum] = None
    property_inspections: Optional[FIPropertyInspections] = None
    # construction_and_usage_start_year_description: Optional[list[FITranslatedText]] = (
    #     None
    # )
    construction_permit_usage_purpose: Optional[list[FITranslatedText]] = None
    parking_spaces: Optional[list[FIHousingCompanyParkingSpace]] = None


class FIResidentialPropertyOverviewCreateEdit(
    _FIResidentialPropertyOverviewFullDetails
):
    pass


class FIResidentialPropertyOverviewListRead(_FIResidentialPropertyOverviewBasicDetails):
    id: int


class FIResidentialPropertyOverviewRead(
    _FIResidentialPropertyOverviewFullDetails, FIResidentialPropertyOverviewListRead
):
    pass
