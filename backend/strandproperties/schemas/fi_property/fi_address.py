from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_area import FIAreaRead
from strandproperties.schemas.fi_property.fi_common import FIChoiceEnum, Location


class _FIAddressBase(BaseSchema):
    """
    The street address of a realty. This can contain the "full" street address or just the name of the street.
    """

    id: Optional[int] = None
    street_address: str
    stairwell: Optional[str] = None
    apartment_number: Optional[str] = None
    district: Optional[str] = None
    municipality: Optional[str] = None
    postal_code: str
    location: Optional[Location] = None
    show_map_on_announcement: Optional[FIChoiceEnum] = None


class FIAddressCreate(_FIAddressBase):
    pass


class FIAddressRead(_FIAddressBase):
    area: FIAreaRead
