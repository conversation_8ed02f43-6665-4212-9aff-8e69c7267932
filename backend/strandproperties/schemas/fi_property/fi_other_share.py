from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FIAreaUnitCodeEnum,
    FICommonArea,
    FIRedemption,
    FIShareCertificate,
)


class FIOtherShareTypeCodeEnum(StrEnum):
    BOAT_HOLD = "BOAT_HOLD"
    CAR_SHED = "CAR_SHED"
    CAR_SHELTER = "CAR_SHELTER"
    GARAGE = "GARAGE"
    PARKING_SLOT = "PARKING_SLOT"
    STORAGE = "STORAGE"
    OTHER = "OTHER"


class _FIOtherShareOverviewBasicDetails(BaseSchema):
    debt_free_price: Optional[int] = None
    currency_code: Optional[str] = None


class _FIOtherShareOverviewFullDetails(_FIOtherShareOverviewBasicDetails):
    is_auction_listing: Optional[bool] = None
    other_share_type_code: Optional[FIOtherShareTypeCodeEnum] = None
    administration: Optional[FIAdministration] = None
    area_basis_codes: Optional[list[FIAreaUnitCodeEnum]] = None
    floor_area: Optional[FICommonArea] = None
    total_area: Optional[FICommonArea] = None
    debt_share_amount: Optional[int] = None
    starting_debt_free_price: Optional[int] = None
    share_certificate: Optional[FIShareCertificate] = None
    redemption: Optional[FIRedemption] = None


class FIOtherShareOverviewCreateEdit(_FIOtherShareOverviewFullDetails):
    pass


class FIOtherShareOverviewListRead(_FIOtherShareOverviewBasicDetails):
    pass


class FIOtherShareOverviewRead(
    _FIOtherShareOverviewFullDetails, FIOtherShareOverviewListRead
):
    pass
