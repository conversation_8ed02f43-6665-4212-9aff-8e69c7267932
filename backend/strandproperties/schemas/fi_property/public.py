from typing import Literal

from pydantic import Field

from strandproperties.constants import Language
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.image import ImageRead


class FIPropertyParameters(BaseSchema):
    reference_code: str
    language: Literal[Language.FINNISH, Language.ENGLISH, Language.SWEDISH]


class FIPropertyCoordinates(BaseSchema):
    latitude: float = 0
    longitude: float = 0


class FIPropertyEvent(BaseSchema):
    pass


class FIPropertyRealtor(BaseSchema):
    pass


class FIPropertyMetadata(BaseSchema):
    pass


class TranslatedField(BaseSchema):
    title: str
    # TODO: remove dict from this later then formatting is done
    value: str | dict | int | list | None


class FIPropertyBodySubSection(BaseSchema):
    title: str
    content: dict[str, TranslatedField]


class FIPropertyBodySection(BaseSchema):
    title: str
    content: dict[str, TranslatedField | FIPropertyBodySubSection]


class FIPropertyBody(BaseSchema):
    description: FIPropertyBodySection
    basic_info: FIPropertyBodySection
    price_and_cost: FIPropertyBodySection
    additional_information: FIPropertyBodySection
    housing_company: FIPropertyBodySection
    spaces_and_materials: FIPropertyBodySection
    services_and_transportation: FIPropertyBodySection


class FIPropertyPublicBase(BaseSchema):
    parameters: FIPropertyParameters
    images: list[str]
    coordinates: FIPropertyCoordinates
    events: list[FIPropertyEvent]
    realtor: list[int]
    metadata: FIPropertyMetadata
    body: FIPropertyBody


class FIPropertyPublicRead(FIPropertyPublicBase):
    pass


class FIPropertyPublicReadParam(BaseSchema):
    reference_code: str = Field(..., json_schema_extra={"param_in": "path"})
