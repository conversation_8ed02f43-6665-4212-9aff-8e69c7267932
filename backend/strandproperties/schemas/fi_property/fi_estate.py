from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FICommonArea,
    FITranslatedText,
)


############ Estate #######################
class FIEstatePropertyTypeCodeEnum(StrEnum):
    """
    Enum: "ARABLE" "FARM" "FOREST" "PARCEL_OF_LAND" "WILDERNESS" "OTHER"

    Describes the type of an estate. This attribute must be set if the propertyTypeCode is ESTATE. Possible values are:

    - ARABLE: Estate which consists of arable fields.
    - FARM: Estate for farming.
    - FOREST: Estate that consists mainly of forest.
    - PARCEL_OF_LAND: Part of a plot that can be later separated into an own plot.
    - WILDERNESS: Estate that's mainly wilderness such as forests, rivers, lakes, swamps, and other uninhabited areas.
    - OTHER: Estate which doesn't fit into any of the above categories.
    """

    ARABLE = "ARABLE"
    FARM = "FARM"
    FOREST = "FOREST"
    PARCEL_OF_LAND = "PARCEL_OF_LAND"
    WILDERNESS = "WILDERNESS"
    OTHER = "OTHER"


class FIEstateAreaTypeCodeEnum(StrEnum):
    """
    Enum: "TOTAL" "FOREST" "ARABLE"

    Specifies an estate area type. Possible values are:

    - TOTAL: Estate's total area.
    - FOREST: Estate's forest land area.
    - ARABLE: Estate's arable land area.
    """

    TOTAL = "TOTAL"
    FOREST = "FOREST"
    ARABLE = "ARABLE"


class FIPartitionTypeCodeEnum(StrEnum):
    """
    Enum: "CAN_BE_PARTITIONED" "SOLD_AS_A_WHOLE" "UNKNOWN"

    Describes the possibility of partitioning the estate. Possible values are:

    - CAN_BE_PARTITIONED: The estate can be partitioned.
    - SOLD_AS_A_WHOLE: The estate is sold as a whole and cannot be partitioned.
    - UNKNOWN: The type is not known.
    """

    CAN_BE_PARTITIONED = "CAN_BE_PARTITIONED"
    SOLD_AS_A_WHOLE = "SOLD_AS_A_WHOLE"
    UNKNOWN = "UNKNOWN"


class FITreeTypeCodeEnum(StrEnum):
    """
    Enum: "PINE" "SPRUCE" "BIRCH" "OTHER"

    Specifies the tree species. Possible values are:

    - PINE: Pine trees.
    - SPRUCE: Spruce trees.
    - BIRCH: Birch trees.
    - OTHER: Other tree species.
    """

    PINE = "PINE"
    SPRUCE = "SPRUCE"
    BIRCH = "BIRCH"
    OTHER = "OTHER"


class FIEstateLandAreaTypeCodeEnum(StrEnum):
    """
    Enum: "HERB-RICH_HEATH_FOREST" "MESIC_HEATH_FOREST" "SUB-XERIC_HEATH_FOREST"
          "XERIC_HEATH_FOREST" "BARREN_HEATH_FOREST" "MIRE" "NON-PRODUCTIVE_LAND"

    Specifies an estate land type. Possible values are:

    - HERB-RICH_HEATH_FOREST: Estate's herb-rich heath forest land type.
    - MESIC_HEATH_FOREST: Estate's mesic heath forest land type.
    - SUB-XERIC_HEATH_FOREST: Estate's sub-xeric heath forest land type.
    - XERIC_HEATH_FOREST: Estate's xeric heath forest land type.
    - BARREN_HEATH_FOREST: Estate's barren heath forest land type.
    - MIRE: Estate's mire land type.
    - NON-PRODUCTIVE_LAND: Estate's non-productive land type.
    """

    HERB_RICH_HEATH_FOREST = "HERB-RICH_HEATH_FOREST"
    MESIC_HEATH_FOREST = "MESIC_HEATH_FOREST"
    SUB_XERIC_HEATH_FOREST = "SUB-XERIC_HEATH_FOREST"
    XERIC_HEATH_FOREST = "XERIC_HEATH_FOREST"
    BARREN_HEATH_FOREST = "BARREN_HEATH_FOREST"
    MIRE = "MIRE"
    NON_PRODUCTIVE_LAND = "NON-PRODUCTIVE_LAND"


class FIEstateArea(BaseSchema):
    type_code: Optional[FIEstateAreaTypeCodeEnum] = None
    area: Optional[FICommonArea] = None


class FIForestStand(BaseSchema):
    type_code: Optional[FITreeTypeCodeEnum] = None
    # Specifies the size of the estate's timber logging possibilities for the tree species in cubic meters.
    timber_amount_in_cubic_meters: Optional[int] = None
    # Specifies the size of the estate's pulp logging possibilities for the tree species in cubic meters.
    pulp_amount_in_cubic_meters: Optional[int] = None


class FIEstateLandArea(BaseSchema):
    type_code: Optional[FIEstateAreaTypeCodeEnum] = None
    area: Optional[FICommonArea] = None


class _FIEstateOverviewBasicDetails(BaseSchema):
    pass


class _FIEstateOverviewFullDetails(_FIEstateOverviewBasicDetails):
    estate_property_type_code: FIEstatePropertyTypeCodeEnum
    areas: Optional[list[FIEstateArea]] = None
    partition_type_code: Optional[FIPartitionTypeCodeEnum] = None
    partition_min_size: Optional[FICommonArea] = None
    forest_land_description: Optional[list[FITranslatedText]] = None
    # Specifies the size of the estate's immediate forest logging possibilities in cubic meters.
    immediate_forest_logging_possibilities_in_cubic_meters: Optional[int] = None
    forest_logging_potential_description: Optional[list[FITranslatedText]] = None
    arable_land_description: Optional[list[FITranslatedText]] = None
    forest_stand: Optional[list[FIForestStand]] = None
    land_areas: Optional[FIEstateLandArea] = None


class FIEstateOverviewCreateEdit(_FIEstateOverviewFullDetails):
    pass


class FIEstateOverviewListRead(_FIEstateOverviewBasicDetails):
    pass


class FIEstateOverviewRead(_FIEstateOverviewFullDetails, FIEstateOverviewListRead):
    pass
