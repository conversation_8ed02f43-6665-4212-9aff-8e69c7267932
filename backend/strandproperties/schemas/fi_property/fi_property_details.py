from enum import StrEnum
from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FIPropertyStorageTypeCodeEnum,
    FITelevision,
    FITranslatedText,
)


class FIRealEstateTypeCodeEnum(StrEnum):
    """
    Describes the real estate type of the property. Possible values are:

    FARM is an estate for farming.
    PLOT is a piece of land without habitable buildings.
    PARCEL_OF_LAND is a part of a plot that can be later separated into an own plot.
    DESIGNATED_SHARE is a share of a plot.
    LEASEHOLD is a right to lease a property.
    """

    FARM = "FARM"
    PLOT = "PLOT"
    PARCEL_OF_LAND = "PARCEL_OF_LAND"
    DESIGNATED_SHARE = "DESIGNATED_SHARE"
    LEASEHOLD = "LEASEHOLD"


class FIWaterSupplyTypeCodeEnum(StrEnum):
    DISTRICT_HEATING_CONNECTION = "DISTRICT_HEATING_CONNECTION"
    ELECTRICAL_CONNECTION = "ELECTRICAL_CONNECTION"
    MAINS_WATER_SUPPLY = "MAINS_WATER_SUPPLY"
    SEWAGE_CONNECTION = "SEWAGE_CONNECTION"


class FISewageDisposalSystemCodeEnum(StrEnum):
    NO_SEWER = "NO_SEWER"
    NO_SEWER_CORRESPONDING_TO_CURRENT_REGULATIONS = (
        "NO_SEWER_CORRESPONDING_TO_CURRENT_REGULATIONS"
    )
    MUNICIPAL = "MUNICIPAL"
    OWNED = "OWNED"
    ABSORPTION_FIELD = "ABSORPTION_FIELD"
    LAND_FILTRATION = "LAND_FILTRATION"
    SMALL_SEWAGE_TREATMENT_PLANT = "SMALL_SEWAGE_TREATMENT_PLANT"
    SEPTIC_TANK = "SEPTIC_TANK"
    CLOSED_TANK = "CLOSED_TANK"
    WATER_COOPERATIVE = "WATER_COOPERATIVE"


class FISewageDisposalDocumentCodeEnum(StrEnum):
    REPORT = "REPORT"
    PLAN = "PLAN"
    OPERATING_AND_MAINTENANCE_INSTRUCTIONS = "OPERATING_AND_MAINTENANCE_INSTRUCTIONS"
    OTHER = "OTHER"


class FIPropertyConnectionCodeEnum(StrEnum):
    DISTRICT_HEATING_CONNECTION = "DISTRICT_HEATING_CONNECTION"
    ELECTRICAL_CONNECTION = "ELECTRICAL_CONNECTION"
    MAINS_WATER_SUPPLY = "MAINS_WATER_SUPPLY"
    SEWAGE_CONNECTION = "SEWAGE_CONNECTION"
    CABLE_TV_OR_TELECOM_CONNECTION = "CABLE_TV_OR_TELECOM_CONNECTION"


class FIGroundAreaTypeCodeEnum(StrEnum):
    HILLSIDE = "HILLSIDE"
    FLAT = "FLAT"
    WATERFRONT = "WATERFRONT"
    UNKNOWN = "UNKNOWN"


class FIPropertyBuildingTypeCodeEnum(StrEnum):
    GRANARY = "GRANARY"
    SHELTERED_PARKING = "SHELTERED_PARKING"
    GARAGE = "GARAGE"
    PLAYHOUSE = "PLAYHOUSE"
    WOODSHED = "WOODSHED"
    SAUNA = "SAUNA"
    SERVICE_BUILDING = "SERVICE_BUILDING"
    SHED = "SHED"
    WAREHOUSE = "WAREHOUSE"
    STABLE = "STABLE"
    SUMMER_KITCHEN = "SUMMER_KITCHEN"
    OTHER = "OTHER"


class FIPropertyInternetConnections(BaseSchema):
    broadband_available: Optional[FIChoiceEnum] = None
    fiber_optic_internet: Optional[FIChoiceEnum] = None
    broadband_operator: Optional[str] = None
    internal_network_cabling: Optional[FIChoiceEnum] = None
    network_description: Optional[list[FITranslatedText]] = None


class FIWaterSupply(BaseSchema):
    type_codes: Optional[list[FIWaterSupplyTypeCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FISewageDisposal(BaseSchema):
    system_codes: Optional[list[FISewageDisposalSystemCodeEnum]] = None
    document_codes: Optional[list[FISewageDisposalDocumentCodeEnum]] = None
    other_document_description: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIRenovationGrant(BaseSchema):
    received: Optional[FIChoiceEnum] = None
    description: Optional[list[FITranslatedText]] = None


class FIIslandProperty(BaseSchema):
    berth_on_mainland: Optional[bool] = None
    berth_description: Optional[list[FITranslatedText]] = None
    parking_space_on_mainland: Optional[bool] = None
    parking_space_description: Optional[list[FITranslatedText]] = None


class FIPropertyConnections(BaseSchema):
    codes: Optional[list[FIPropertyConnectionCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FIGroundArea(BaseSchema):
    type_code: Optional[list[FIGroundAreaTypeCodeEnum]] = None
    description: Optional[list[FITranslatedText]] = None


class FIPropertyBuilding(BaseSchema):
    type_code: Optional[list[FIPropertyBuildingTypeCodeEnum]] = None
    other_type: Optional[list[FITranslatedText]] = None
    name: Optional[list[FITranslatedText]] = None
    description: Optional[list[FITranslatedText]] = None


class FIPropertyStorage(BaseSchema):
    type_code: Optional[list[FIPropertyStorageTypeCodeEnum]] = None


class FIPropertyEncumbrance(BaseSchema):
    mortgage_amount: Optional[float] = None
    mortgage_free_amount: Optional[int] = None
    deeds: Optional[FIChoiceEnum] = None
    deeds_count: Optional[int] = None
    deeds_amount: Optional[int] = None
    deeds_safe_holding: Optional[list[FITranslatedText]] = None
    separate_pledge_statement: Optional[FIChoiceEnum] = None
    is_property_pledged: Optional[FIChoiceEnum] = None
    pledge_holder: Optional[list[FITranslatedText]] = None
    unpaid_fees: Optional[list[FITranslatedText]] = None
    no_unreported_public_encumbrances: Optional[FIChoiceEnum] = None
    no_public_encumbrances: Optional[FIChoiceEnum] = None
    encumbrances: Optional[list[FITranslatedText]] = None
    descriptions: Optional[list[FITranslatedText]] = None
    currency_code: Optional[str] = None


class _FIPropertyOverviewBasicDetails(BaseSchema):
    pass


class _FIPropertyOverviewFullDetails(_FIPropertyOverviewBasicDetails):
    real_estate_type_code: Optional[FIRealEstateTypeCodeEnum] = None
    property_name: Optional[str] = None
    property_id: Optional[str] = None
    floor_plan: Optional[str] = None
    has_possession_or_divide_agreement: Optional[bool] = None
    # Indicates whether the property has a possession or divide agreement when the value of
    # the realEstateTypeCode attribute is DESIGNATED_SHARE.
    estate_has_residence: Optional[bool] = None
    electricity_consumption_description: Optional[list[FITranslatedText]] = None
    internet_connection: Optional[FIPropertyInternetConnections] = None
    television: Optional[FITelevision] = None
    restriction_for_use_and_assignment: Optional[FIChoiceEnum] = None
    restriction_for_use_and_assignment_description: Optional[list[FITranslatedText]] = (
        None
    )
    water_supply: Optional[FIWaterSupply] = None
    sewage_disposal: Optional[FISewageDisposal] = None
    renovation_grant: Optional[FIRenovationGrant] = None
    road_availability_till_property: Optional[FIChoiceEnum] = None
    road_access_rights_and_restrictions: Optional[list[FITranslatedText]] = None
    property_on_island: Optional[FIChoiceEnum] = None
    island_property: Optional[FIIslandProperty] = None
    property_connections: Optional[FIPropertyConnections] = None
    yard_description: Optional[list[FITranslatedText]] = None
    view_description: Optional[list[FITranslatedText]] = None
    ground_area: Optional[FIGroundArea] = None
    buildings: Optional[list[FIPropertyBuilding]] = None
    storages: Optional[list[FIPropertyStorage]] = None
    encumbrance: Optional[FIPropertyEncumbrance] = None


class FIPropertyOverviewCreateEdit(_FIPropertyOverviewFullDetails):
    pass


class FIPropertyOverviewListRead(_FIPropertyOverviewBasicDetails):
    pass


class FIPropertyOverviewRead(
    _FIPropertyOverviewFullDetails, FIPropertyOverviewListRead
):
    pass
