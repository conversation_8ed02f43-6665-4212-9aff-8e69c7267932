from datetime import datetime
from enum import Enum
from typing import Dict, List, Literal, Optional

from pydantic import validator

from strandproperties.constants import (
    CommissionTypeEnum,
    OrderBy,
    SoldBy,
    SortEnum,
    Status,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactListRead, ContactRead
from strandproperties.schemas.fi_property.fi_commercial import (
    FICommercialPropertyOverviewRead,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIAdministration,
    FIApartmentRoom,
    FIChoiceEnum,
    FICommonArea,
    FiPortals,
    FIRedemption,
    FIResidentialTypeCodeEnum,
    FIShareCertificate,
    FITranslatedText,
)
from strandproperties.schemas.fi_property.fi_estate import (
    FIEstateOverviewListRead,
    FIEstateOverviewRead,
)
from strandproperties.schemas.fi_property.fi_housing_company import (
    FIHousingCompanyCreateEdit,
    FIHousingCompanyListRead,
    FIHousingCompanyRead,
)
from strandproperties.schemas.fi_property.fi_other_share import (
    FIOtherShareOverviewListRead,
    FIOtherShareOverviewRead,
    FIOtherShareTypeCodeEnum,
)
from strandproperties.schemas.fi_property.fi_plot import (
    FIPlotOverviewListRead,
    FIPlotOverviewRead,
)
from strandproperties.schemas.fi_property.fi_property_details import (
    FIPropertyOverviewListRead,
    FIPropertyOverviewRead,
)
from strandproperties.schemas.fi_property.fi_property_type import (
    FIListingTypeEnum,
    FIOwnershipTypeEnum,
    FIPropertyTypeEnum,
    FIPropertyTypeGroupEnum,
)
from strandproperties.schemas.fi_property.fi_realty import (
    FIConditionCodeEnum,
    FIConsentToChange,
    FICost,
    FIDamages,
    FILeaseDetails,
    FILivingFormTypeCodeEnum,
    FIRealtyAdditionalInformation,
    FIRealtyAvailability,
    FIRealtyCondition,
    FIRealtyCreateEdit,
    FIRealtyListRead,
    FIRealtyRead,
    FIRealtyShare,
)
from strandproperties.schemas.fi_property.fi_residential_property import (
    FIResidentialPropertyOverviewCreateEdit,
    FIResidentialPropertyOverviewListRead,
    FIResidentialPropertyOverviewRead,
)
from strandproperties.schemas.fi_property.fi_residential_share import (
    FIApartmentOverview,
    FIBalcony,
    FIOwnershipTypeCodeEnum,
    FIResidentialPropertyOverviewParkingSpaces,
    FIResidentialShareOverviewListRead,
    FIResidentialShareOverviewParkingSpaces,
    FIResidentialShareOverviewRead,
    FIResidentialShareStorage,
    FIResidentialShareYard,
    FITerrace,
)
from strandproperties.schemas.mapping import FIPropertyTypeRead
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.property import PropertyDescription, VideoRead
from strandproperties.schemas.user import UserListRead, UserRead


class _FIPropertyBase(BaseSchema):
    is_exclusive: bool
    data_source: Optional[str] = None
    is_strandified: Optional[bool] = False
    commission: Optional[float] = None
    commission_type: Optional[CommissionTypeEnum] = None
    commission_note: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    internal_note: Optional[str] = None
    video_streams: Optional[list[VideoRead]] = []
    video_tours: Optional[list[VideoRead]] = []
    portals: FiPortals = {}


class FIPropertyCreate(_FIPropertyBase):
    fi_property_type_id: int
    fi_realty: FIRealtyCreateEdit
    realtor_user_ids: list[int]
    contact_ids: list[int]


class FIPropertyMinimalRead(BaseSchema):
    id: int
    reference: Optional[str] = None


class FIPropertyKiviImporter(BaseSchema):
    reference_code: str | None = None


class FIPropertyListRead(_FIPropertyBase):
    id: int
    created_at: datetime
    updated_at: datetime
    reference: str
    main_img: Optional[str] = None
    realtor_users: list[UserListRead]
    contacts: list[ContactListRead]

    descriptions: Optional[list[PropertyDescription]] = None
    fi_property_type: FIPropertyTypeRead
    fi_realty: FIRealtyListRead
    fi_commercial_property_overview: Optional[FICommercialPropertyOverviewRead] = None
    fi_estate_overview: Optional[FIEstateOverviewListRead] = None
    fi_housing_company: Optional[FIHousingCompanyListRead] = None
    fi_other_share_overview: Optional[FIOtherShareOverviewListRead] = None
    fi_plot_overview: Optional[FIPlotOverviewListRead] = None
    fi_property_overview: Optional[FIPropertyOverviewListRead] = None
    fi_residential_property_overview: Optional[
        FIResidentialPropertyOverviewListRead
    ] = None
    fi_residential_share_overview: Optional[FIResidentialShareOverviewListRead] = None


class FIPropertyRead(FIPropertyListRead):
    descriptions: Optional[list[PropertyDescription]] = None
    realtor_users: list[UserRead]
    contacts: list[ContactRead]
    fi_realty: FIRealtyRead
    fi_commercial_property_overview: Optional[FICommercialPropertyOverviewRead] = None
    fi_estate_overview: Optional[FIEstateOverviewRead] = None
    fi_housing_company: Optional[FIHousingCompanyRead] = None
    fi_other_share_overview: Optional[FIOtherShareOverviewRead] = None
    fi_plot_overview: Optional[FIPlotOverviewRead] = None
    fi_property_overview: Optional[FIPropertyOverviewRead] = None
    fi_residential_property_overview: Optional[FIResidentialPropertyOverviewRead] = None
    fi_residential_share_overview: Optional[FIResidentialShareOverviewRead] = None


class FIPropertySortColumn(str, Enum):
    SELLING_PRICE = "selling_price"
    STATUS = "status"


class FIPropertyFilterParam(PaginationParam):
    areas: Optional[list[int]] = []
    assigned_to: Optional[list[int]] = []
    data_source: Optional[Literal["strand", "network"]] = None
    from_created_at: Optional[datetime] = None
    from_updated_at: Optional[datetime] = None
    is_exclusive: Optional[bool] = None
    is_strandified: Optional[bool] = None
    keyword: Optional[str] = None
    offices: Optional[List[int]] = None
    order_by: OrderBy = OrderBy.LATEST
    sellers: Optional[List[int]] = None
    sold_by: Optional[SoldBy] = None
    status: Optional[list[Status]] = None
    tags: Optional[List[int]] = None
    portals: Optional[List[str]] = []
    until_created_at: Optional[datetime] = None
    until_updated_at: Optional[datetime] = None
    price_min: Optional[List[int]] = None
    price_max: Optional[List[int]] = None
    types: Optional[List[int]] = []
    conditions: Optional[List[FIConditionCodeEnum]] = None
    size_min: Optional[List[int]] = None
    size_max: Optional[List[int]] = None
    room_counts: Optional[List[str]] = None
    sort_column: Optional[FIPropertySortColumn] = None
    sort_direction: Optional[SortEnum] = None

    @validator("sort_column", "sort_direction", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class PropertyOverviewEdit(BaseSchema):
    property_id: Optional[str] = None
    property_name: Optional[str] = None
    floor_plan: Optional[str] = None


class AddressEdit(BaseSchema):
    street_address: Optional[str] = None
    stairwell: Optional[str] = None
    apartment_number: Optional[str] = None
    postal_code: Optional[str] = None
    district: Optional[str] = None
    municipality: Optional[str] = None


class RealtyEdit(BaseSchema):
    status: Optional[str] = None
    monthly_rent: Optional[int] = None
    selling_price: Optional[int] = None
    currency_code: Optional[str] = None
    fi_address: Optional[AddressEdit] = None
    new_building: Optional[FIChoiceEnum] = None
    consent_to_change: Optional[FIConsentToChange] = None
    costs: Optional[list[FICost]] = []
    condition: Optional[FIRealtyCondition] = None
    costs_description: Optional[list[FITranslatedText]] = None
    is_rented: Optional[FIChoiceEnum] = None
    living_form_type_code: Optional[FILivingFormTypeCodeEnum] = None
    additional_area_measurement_information: Optional[list[FITranslatedText]] = []
    starting_price_amount: Optional[int] = None
    availability: Optional[FIRealtyAvailability] = None
    lease_details: Optional[FILeaseDetails] = None
    damages: Optional[list[FIDamages]] = []
    additional_information: Optional[FIRealtyAdditionalInformation] = None
    share: Optional[FIRealtyShare] = None


class PropertyTypeEdit(BaseSchema):
    id: int
    property_type: Optional[FIPropertyTypeEnum] = None
    ownership_type: Optional[FIOwnershipTypeEnum] = None
    property_type_group: Optional[FIPropertyTypeGroupEnum] = None
    listing_type: Optional[FIListingTypeEnum] = None


class FIResidentialShareOverviewEdit(BaseSchema):
    administration: Optional[FIAdministration] = None
    residential_type_code: Optional[FIResidentialTypeCodeEnum] = None
    ownership_type_code: Optional[FIOwnershipTypeCodeEnum] = None
    apartment: Optional[FIApartmentOverview] = None
    starting_debt_free_price: Optional[int] = None
    debt_free_price: Optional[int] = None
    debt_share_amount: Optional[int] = None
    debt_share_additional_info: Optional[list[FITranslatedText]] = None
    share_certificate: Optional[FIShareCertificate] = None
    redemption: Optional[FIRedemption] = None
    parking_spaces: Optional[list[FIResidentialShareOverviewParkingSpaces]] = None
    storages: Optional[list[FIResidentialShareStorage]] = None
    limits_of_storage_usage: Optional[list[FITranslatedText]] = None
    parking_space_description: Optional[list[FITranslatedText]] = None


class PlotOverviewEdit(BaseSchema):
    area: Optional[FICommonArea] = None


class OtherShareOverviewEdit(BaseSchema):
    is_auction_listing: Optional[bool] = None
    starting_debt_free_price: Optional[int] = None
    debt_free_price: Optional[int] = None
    debt_share_amount: Optional[int] = None
    currency_code: Optional[str] = None
    other_share_type_code: Optional[FIOtherShareTypeCodeEnum] = None
    administration: Optional[FIAdministration] = None
    area_basis_code: Optional[List[str]] = None
    floor_area: Optional[FICommonArea] = None
    total_area: Optional[FICommonArea] = None
    share_certificate: Optional[FIShareCertificate] = None
    redemption: Optional[FIRedemption] = None


class CommercialPropertyOverviewEdit(BaseSchema):
    debt_free_price: Optional[int] = None
    debt_share_amount: Optional[int] = None


class FIBasicInformation(_FIPropertyBase):
    fi_property_overview: Optional[PropertyOverviewEdit] = None
    fi_realty: Optional[RealtyEdit] = None
    fi_property_type: Optional[PropertyTypeEdit] = None
    fi_residential_share_overview: Optional[FIResidentialShareOverviewEdit] = None
    fi_residential_property_overview: Optional[
        FIResidentialPropertyOverviewCreateEdit
    ] = None
    fi_plot_overview: Optional[FIPlotOverviewRead] = None
    fi_other_share_overview: Optional[OtherShareOverviewEdit] = None
    fi_commercial_property_overview: Optional[CommercialPropertyOverviewEdit] = None
    fi_housing_company: Optional[FIHousingCompanyCreateEdit] = None


class SpacesAndMaterialsCreateEdit(BaseSchema):
    kitchen: list[FIApartmentRoom] = []
    dining_room: list[FIApartmentRoom] = []
    bathroom: list[FIApartmentRoom] = []
    bedroom: list[FIApartmentRoom] = []
    sauna: list[FIApartmentRoom] = []
    toilet: list[FIApartmentRoom] = []
    utility_room: list[FIApartmentRoom] = []
    utility_room_description: list[FITranslatedText] | None = None
    living_room: list[FIApartmentRoom] = []
    closet: list[FIApartmentRoom] = []
    loft: list[FIApartmentRoom] = []
    library: list[FIApartmentRoom] = []
    study: list[FIApartmentRoom] = []
    hall: list[FIApartmentRoom] = []
    hallway: list[FIApartmentRoom] = []
    draught_lobby: list[FIApartmentRoom] = []
    other: list[FIApartmentRoom] = []
    terrace: list[FITerrace] = []
    balcony: list[FIBalcony] = []
    patio: list[FITranslatedText] | None = None
    yard: list[FIResidentialShareYard] = []
    storages: list[FIResidentialShareStorage] | None = None
    storage_usage_limitations: list[FITranslatedText] | None = None
    storage_description: list[FITranslatedText] | None = None
    refrigerated_cellar: list[FITranslatedText] | None = None
    boiler_room: list[FITranslatedText] | None = None
    residential_share_overview_parking_spaces: (
        list[FIResidentialShareOverviewParkingSpaces] | None
    ) = None
    residential_property_overview_parking_spaces: (
        list[FIResidentialPropertyOverviewParkingSpaces] | None
    ) = None
    parking_description: list[FITranslatedText] | None = None
    parking_space_description: list[FITranslatedText] = []
    more_information_about_the_premises: list[FITranslatedText] | None = None
    more_information_about_the_materials: list[FITranslatedText] | None = None
    transaction: list[FITranslatedText] | None = None
    transaction_includes: list[FITranslatedText] | None = None
    transaction_does_not_include: list[FITranslatedText] | None = None
    furnished: FIChoiceEnum | None = None


class FIPropertyEdit(FIBasicInformation):
    realtor_user_ids: Optional[list[int]] = None
    contact_ids: Optional[list[int]] = None
    descriptions: Optional[list[PropertyDescription]] = None
    spaces: Optional[dict] = None
    spaces_and_materials: Optional[SpacesAndMaterialsCreateEdit] = None


class FiPropertyChangeStatusBody(BaseSchema):
    action: Status
    portals: Optional[FiPortals] = None
    sold_by: Optional[SoldBy] = None
