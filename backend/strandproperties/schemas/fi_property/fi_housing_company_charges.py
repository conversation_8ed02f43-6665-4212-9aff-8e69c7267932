from typing import Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FITranslatedText,
)


class FIFinancingCharge(BaseSchema):
    # The amount of the monthly financing charge.
    amount: Optional[float] = None
    includes_only_interest: Optional[FIChoiceEnum] = None
    # Specifies the amount of the monthly financing charge after the share holder has started to repay the actual loan. The value of this attribute must be set only if the value of the includesOnlyInterest attribute is YES.
    full_amount: Optional[float] = None
    repayment_start_date: Optional[str] = None


class FIPlotCharges(BaseSchema):
    # The amount of the monthly plot rent charge that must be paid by the share holder.
    rent_charge: Optional[float] = None
    is_plot_redeemable: Optional[FIChoiceEnum] = None
    # The amount that must be paid if the share holder wants to redeem his/her share of the plot.
    redemption_share: Optional[float] = None


class FIHousingCompanyCharges(BaseSchema):
    management_charge: Optional[int] = None
    financing_charge: Optional[FIFinancingCharge] = None
    plot: Optional[FIPlotCharges] = None
    special_charge: Optional[int] = None
    maintenance_charge: Optional[int] = None
    description: Optional[list[FITranslatedText]] = None
    currency_code: Optional[str] = None
