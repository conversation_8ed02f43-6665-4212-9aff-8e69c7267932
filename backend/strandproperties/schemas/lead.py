import json
from datetime import datetime
from typing import List, Optional, Union

from pydantic import Field, field_validator

from strandproperties.constants import (
    LeadRelevance,
    LeadSource,
    LeadStatus,
    LeadType,
    TitleValidation,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactListRead
from strandproperties.schemas.match_making import MatchMakingRead
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.user import UserBasicInfo
from strandproperties.schemas.utils import EmptyStrToNone


class LeadFilterParam(PaginationParam):
    keyword: Optional[str] = None
    assigned_users: Optional[str] = None


class LeadListParams(BaseSchema):
    limit: int = Field(
        10,
        description="Number of leads to return",
        ge=1,
    )
    after_lead_id: Optional[int] = Field(
        None,
        description="Return leads after this id",
    )
    status: Optional[LeadStatus] = None
    relevance: Optional[str] = None
    assigned_to: Optional[str] = None
    assigned_to_group: Optional[str] = None


class LeadListRead(BaseSchema):
    # fix me later with less repeaded definition
    class LeadListReadContact(BaseSchema):
        id: int
        name: str
        email: Optional[str]
        company: Optional[str]
        website: Optional[str]
        address: Optional[str]
        city: Optional[str]
        post_code: Optional[str]
        country: Optional[str]
        preferred_language: Optional[str]
        passport_number: Optional[str]
        nationality: Optional[str]
        country_specific: Optional[dict] = {}
        notes: Optional[str]

    id: int
    created_at: datetime
    updated_at: datetime
    status: LeadStatus
    relevance: Optional[LeadRelevance]
    assigned_to_users: List[UserBasicInfo]
    property_reference: Optional[str]
    contacts: List[LeadListReadContact]
    title: str = TitleValidation
    description: Optional[EmptyStrToNone]
    type: Optional[LeadType]
    source: Optional[Union[LeadSource, str]]


class LeadRead(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime
    status: LeadStatus
    relevance: Optional[LeadRelevance]
    assigned_to_users: List[UserBasicInfo]
    property_reference: Optional[str]
    contacts: List[ContactListRead]
    title: str = TitleValidation
    description: Optional[EmptyStrToNone]
    type: Optional[LeadType]
    source: Optional[Union[LeadSource, str]]

    @field_validator("description", mode="before")
    def format_description(cls, value):
        if isinstance(value, str):
            try:
                parsed_value = json.loads(value)
                if isinstance(parsed_value, dict):
                    formatted_str = "\n".join(
                        f"{key}={val}" for key, val in parsed_value.items()
                    )
                    return formatted_str
                elif isinstance(parsed_value, str):
                    try:
                        parsed_inner_value = json.loads(parsed_value)

                        if isinstance(parsed_inner_value, dict):
                            formatted_str = "\n".join(
                                f"{key}={val}"
                                for key, val in parsed_inner_value.items()
                            )
                            return formatted_str
                        else:
                            return parsed_value
                    except (json.JSONDecodeError, TypeError):
                        return parsed_value

            except (json.JSONDecodeError, TypeError):
                return value
        return value


class LeadReadMatchMaking(LeadRead):
    match_making: Optional[List[MatchMakingRead]]


class LeadCreateEdit(BaseSchema):
    status: LeadStatus = LeadStatus.NEW
    relevance: Optional[LeadRelevance] = None
    assigned_to: List[int] = Field(
        ...,
        description="List of user ids to assign the lead to",
        min_items=1,
    )
    property_reference: Optional[str] = None
    contact_ids: Optional[List[int]] = []
    title: str = TitleValidation
    description: Optional[EmptyStrToNone] = None
    type: Optional[LeadType] = None
    source: Optional[Union[LeadSource, str]] = None
