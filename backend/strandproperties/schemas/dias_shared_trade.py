from datetime import datetime
from enum import StrEnum, IntEnum
from typing import List, Optional

from strandproperties.constants import OrderBy
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.dias_common import (
    At<PERSON>chment,
    BankAccount,
    DiasBuyer,
    DiasSeller,
    DisplayText,
    InitiatorContactInfo,
    DiasAttachmentRead,
    DiasWebhookEvent,
    InternalBuyer,
    InternalRealtor,
    InternalSeller,
    InvoiceVerificationType,
)
from strandproperties.schemas.fi_property.fi_property import (
    FIPropertyListRead,
    FIPropertyMinimalRead,
)
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.user import UserListRead, UserRead


class BuyersMortgageStatus(StrEnum):
    MORTGAGE = "MORTGAGE"
    NO_MORTGAGE = "NO_MORTGAGE"


class Step(IntEnum):
    # These steps are based on the FrontEnd UI design
    PREPARATION = 1
    IN_REVIEW = 2
    SIGNING = 3
    COMPLETED = 4


class TradePhase(StrEnum):
    INITIATED = "INITIATED"
    SELLER_BANK_APPROVED = "SELLER_BANK_APPROVED"
    BUYER_BANK_APPROVED = "BUYER_BANK_APPROVED"
    INITIATOR_SIGNING_CONFIRMATION_RECEIVED = "INITIATOR_SIGNING_CONFIRMATION_RECEIVED"
    SIGNING_STARTED = "SIGNING_STARTED"
    SIGNING_COMPLETED = "SIGNING_COMPLETED"
    BUYER_BANK_PAYMENTS_COMPLETED = "BUYER_BANK_PAYMENTS_COMPLETED"
    SELLER_BANK_PAYMENTS_COMPLETED = "SELLER_BANK_PAYMENTS_COMPLETED"
    SELLER_BANK_TRANSFERRED_SHARE = "SELLER_BANK_TRANSFERRED_SHARE"
    BUYER_BANK_RECEIVED_SHARE = "BUYER_BANK_RECEIVED_SHARE"
    COMPLETED = "COMPLETED"
    MOVED_TO_MANUAL_PROCESSING = "MOVED_TO_MANUAL_PROCESSING"
    CANCELLED = "CANCELLED"

    DRAFT = "DRAFT"  # This is a custom trade phase for Strand, mainly for filtering purposes, it is not used by DIAS


ONGOING_TRADE_PHASES = [
    TradePhase.INITIATED,
    TradePhase.SELLER_BANK_APPROVED,
    TradePhase.BUYER_BANK_APPROVED,
    TradePhase.INITIATOR_SIGNING_CONFIRMATION_RECEIVED,
    TradePhase.SIGNING_STARTED,
    TradePhase.SIGNING_COMPLETED,
    TradePhase.BUYER_BANK_PAYMENTS_COMPLETED,
    TradePhase.SELLER_BANK_PAYMENTS_COMPLETED,
    TradePhase.SELLER_BANK_TRANSFERRED_SHARE,
    TradePhase.BUYER_BANK_RECEIVED_SHARE,
]
FINAL_STEPS = [
    TradePhase.SIGNING_COMPLETED,
    TradePhase.BUYER_BANK_PAYMENTS_COMPLETED,
    TradePhase.SELLER_BANK_PAYMENTS_COMPLETED,
    TradePhase.SELLER_BANK_TRANSFERRED_SHARE,
    TradePhase.BUYER_BANK_RECEIVED_SHARE,
    TradePhase.COMPLETED,
]


class ShareCertificateStatus(StrEnum):
    UNKNOWN = "UNKNOWN"
    ONE_OF_SELLERS_HAS_SHARE_CERTIFICATE = "ONE_OF_SELLERS_HAS_SHARE_CERTIFICATE"
    SELLER_BANK_HAS_SHARE_CERTIFICATE = "SELLER_BANK_HAS_SHARE_CERTIFICATE"


class AttachmentTypeSharedTrade(StrEnum):

    TRADE_REGISTER_EXTRACT = "TRADE_REGISTER_EXTRACT"
    ESTATE_INVENTORY_DEED = "ESTATE_INVENTORY_DEED"
    ESTATE_INVENTORY_DEED_SHAREHOLDERS_CERTIFICATION = (
        "ESTATE_INVENTORY_DEED_SHAREHOLDERS_CERTIFICATION"
    )
    ESTATE_INVENTORY_DEED_ATTACHMENT = "ESTATE_INVENTORY_DEED_ATTACHMENT"
    MINUTES_EXTRACT = "MINUTES_EXTRACT"
    UNKNOWN = "UNKNOWN"

    HOUSE_MANAGERS_CERTIFICATE = "HOUSE_MANAGERS_CERTIFICATE"
    SPOUSES_CONSENT = "SPOUSES_CONSENT"

    # DIAS Docs: The share certificate is required to be attached in the trade when sellerShareCertificateStatus is set to ONE_OF_SELLERS_HAS_SHARE_CERTIFICATE
    SHARE_CERTIFICATE = "SHARE_CERTIFICATE"

    # This attachment is required for trades with digital shares.
    # If the shares are digital, there must be exactly as many attachments with this type as there are elements in the apartment.osakeryhmatunnukset array.
    OWNER_APARTMENT_PRINTOUT = "OWNER_APARTMENT_PRINTOUT"


class AttachmentSharedTrade(Attachment):
    estateSocialSecurityNumber: Optional[str] = None
    documentType: Optional[AttachmentTypeSharedTrade] = None

    class Config:
        alias_generator = None


class ApartmentAddress(BaseSchema):
    streetAddress: str
    postalCode: str
    city: str

    class Config:
        alias_generator = None


class ApartmentHousingCompany(BaseSchema):
    name: str
    businessId: str

    class Config:
        alias_generator = None


class SharesType(StrEnum):
    UNKNOWN = "UNKNOWN"
    DIGITAL = "DIGITAL"
    PAPER = "PAPER"


class Apartment(BaseSchema):
    address: ApartmentAddress
    shares: List[str]
    sharesType: Optional[SharesType] = None
    osakeryhmatunnukset: Optional[List[str]] = None
    housingCompany: ApartmentHousingCompany

    class Config:
        alias_generator = None


class BillOfSaleSigned(BaseSchema):
    id: str
    description: str


class SigningComplete(BaseSchema):
    billOfSaleSigned: BillOfSaleSigned
    signedTimestamp: str

    class Config:
        alias_generator = None


class InitiatorConfirmedToSign(BaseSchema):
    initiatorPersonId: str
    confirmedTimestamp: str

    class Config:
        alias_generator = None


class Event(BaseSchema):
    displayText: DisplayText
    event: str
    tradePhase: TradePhase
    timestamp: str


class TradeState(BaseSchema):
    tradeReceivedTimestamp: str
    tradeInitiatedTimestamp: str
    tradePhase: TradePhase

    invitedToSignTimestamp: Optional[str] = None
    signingComplete: Optional[SigningComplete] = None
    buyerBankApprovedTimestamp: Optional[str] = None
    sellerBankConfirmedPaymentTimestamp: Optional[str] = None
    buyerBankPaymentConfirmedTimestamp: Optional[str] = None
    sellerBankRealtorPaymentConfirmedTimestamp: Optional[str] = None
    initiatorConfirmedToSign: Optional[InitiatorConfirmedToSign] = None
    sellerBankApprovedTimestamp: Optional[str] = None

    class Config:
        alias_generator = None


class DiasPaperTradeBase(BaseSchema):
    attachments: List[AttachmentSharedTrade]
    requireInitiatorConfirmation: bool
    billOfSale: Attachment
    realtorBankAccount: BankAccount
    sellers: List[DiasSeller]
    buyers: List[DiasBuyer]
    initiatorPersonId: str
    apartment: Apartment

    # Optional Fields
    initiatorTradeReferenceId: Optional[str] = None
    deadlineForSigningBillOfSale: Optional[str] = None
    sellerShareCertificateStatus: Optional[ShareCertificateStatus] = None
    buyersMortgageStatus: Optional[BuyersMortgageStatus] = None


class DiasPaperTrade(DiasPaperTradeBase):
    """
    This schema is used for sending Requests to DIAS to initiate a paper trade, so the fields are camelCase and are exactly as DIAS models.
    """

    initiatorContactInfo: InitiatorContactInfo
    tradeInitiatedTimestamp: str

    class Config:
        alias_generator = None


class DiasPaperTradeRead(DiasPaperTradeBase):
    """
    This schema is used for validating Response from DIAS
    """

    initiatorContactInfo: Optional[InitiatorContactInfo] = None
    tradeInitiatedTimestamp: Optional[str] = None

    # Optional Extra Fields, some fields only exist in /paper-trades/{tradeId}
    events: Optional[List[Event]] = None
    tradeState: Optional[TradeState] = None
    ownershipTransferPowerOfAttorney: Optional[Attachment] = None

    class Config:
        alias_generator = None


class StrandSharedTrade(BaseSchema):

    attachments: List[AttachmentSharedTrade]
    require_initiator_confirmation: bool
    bill_of_sale: Attachment
    realtor_bank_account: BankAccount
    # sellers: List[DiasSeller]
    # buyers: List[DiasBuyer]
    internal_sellers: List[InternalSeller]
    internal_buyers: List[InternalBuyer]
    initiator_contact_info: InitiatorContactInfo
    initiator_person_id: str
    apartment: Apartment
    trade_initiated_timestamp: str

    internal_realtor: Optional[InternalRealtor] = None
    invoice_verification_type: Optional[InvoiceVerificationType] = None
    invoice_verification_value: Optional[str] = None

    # Optional Fields
    initiator_trade_reference_id: Optional[str] = None
    deadline_for_signing_bill_of_sale: Optional[str] = None
    seller_share_certificate_status: Optional[ShareCertificateStatus] = None
    buyers_mortgage_status: Optional[BuyersMortgageStatus] = None


class StrandSharedTradeEdit(BaseSchema):

    attachments: Optional[List[AttachmentSharedTrade]] = None
    require_initiator_confirmation: Optional[bool] = None
    bill_of_sale: Optional[Attachment] = None
    realtor_bank_account: Optional[BankAccount] = None
    # sellers: Optional[List[DiasSeller]] = None
    # buyers: Optional[List[DiasBuyer]] = None
    internal_sellers: Optional[List[InternalSeller]] = []
    internal_buyers: Optional[List[InternalBuyer]] = []
    internal_realtor: Optional[InternalRealtor] = None
    include_commission: Optional[bool] = None
    realtor_sum_commission: Optional[float] = None
    invoice_verification_type: Optional[InvoiceVerificationType] = None
    invoice_verification_value: Optional[str] = None
    initiator_contact_info: Optional[InitiatorContactInfo] = None
    initiator_person_id: Optional[str] = None
    initiator_id: Optional[int] = None
    apartment: Optional[Apartment] = None
    trade_initiated_timestamp: Optional[str] = None
    buyers_mortgage_status: Optional[BuyersMortgageStatus] = None

    # Optional Fields
    initiator_trade_reference_id: Optional[str] = None
    deadline_for_signing_bill_of_sale: Optional[str] = None
    seller_share_certificate_status: Optional[ShareCertificateStatus] = None

    dias_attachment_ids: Optional[List[int]] = []


class StrandSharedTradeCreate(StrandSharedTradeEdit):
    created_by_id: int
    fi_property_id: Optional[int] = None


class StrandSharedTradeInitiate(BaseSchema):
    id: int
    created_by_id: int
    updated_at: datetime


class StrandSharedTradeRead(StrandSharedTradeEdit):
    id: int
    created_by_id: int
    created_by: UserRead
    fi_property_id: Optional[int] = None
    fi_property: Optional[FIPropertyListRead] = None
    initiator_id: Optional[int] = None
    initiator: Optional[UserRead] = None
    trade_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    current_step: Step

    # Optional Extra Fields, some fields only exist in /paper-trades/{tradeId}
    # dias_updated_at: Optional[datetime] = None
    # events: Optional[List[Event]] = None
    # trade_state: Optional[TradeState] = None
    # ownership_transfer_power_of_attorney: Optional[Attachment] = None

    dias_attachments: List[DiasAttachmentRead]
    # latest_webhook_event: Optional[DiasWebhookEvent] = None


class StrandInitiatedSharedTradeRead(StrandSharedTradeRead):
    sellers: Optional[List[DiasSeller]] = None
    buyers: Optional[List[DiasBuyer]] = None

    dias_updated_at: Optional[datetime] = None
    events: Optional[List[Event]] = None
    trade_state: Optional[TradeState] = None
    ownership_transfer_power_of_attorney: Optional[Attachment] = None

    latest_webhook_event: Optional[DiasWebhookEvent] = None


class StrandSharedTradeFilterParam(PaginationParam):
    order_by: OrderBy = OrderBy.LATEST
    reference: Optional[str] = None
    trade_phase: Optional[TradePhase] = None


class StrandSharedTradePreviewListRead(BaseSchema):
    id: int
    fi_property_id: int
    fi_property: FIPropertyMinimalRead
    initiator_id: Optional[int]
    initiator_contact_info: Optional[InitiatorContactInfo] = None
    internal_sellers: Optional[List[InternalSeller]] = []
    internal_buyers: Optional[List[InternalBuyer]] = []
    internal_realtor: Optional[InternalRealtor] = None
    trade_state: Optional[TradeState] = None
    initiator_trade_reference_id: Optional[str] = None
    created_by: UserListRead
    created_at: datetime
    updated_at: datetime


class CancellationSchema(BaseSchema):
    cancellationReason: str
