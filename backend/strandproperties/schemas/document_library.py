from datetime import datetime
from typing import List, Literal, Optional

from apispec_pydantic_plugin import ApiRootModel
from pydantic import Field

from strandproperties.constants import EventLogActorType
from strandproperties.models.document_library import DocumentType, OwnerType
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.document_signing import (
    CreateDocumentSigning,
    DocumentSigningRead,
)


class DocumentLibraryItemOwnerRead(BaseSchema):
    owner_type: OwnerType
    owner_id: int


class DocumentLibraryItemUpload(BaseSchema):
    filename: str
    mime_type: Literal[
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
    ] = Field(
        ...,
        description="Allowed file types: application/pdf, image/jpeg, image/jpg, image/png",
    )
    file_size: int
    document_type: DocumentType
    description: Optional[str] = Field(default=None, description="Optional description")


class DocumentLibraryItemRead(BaseSchema):
    id: int
    owner_type: OwnerType
    owner_id: int
    filename: str
    mime_type: str
    document_type: DocumentType
    description: Optional[str]
    file_size: int
    created_at: datetime | None
    created_by: str
    signings: list[DocumentSigningRead] = []
    download_url: str


class DocumentLibraryItemUploadRead(BaseSchema):
    id: int
    filename: str
    upload_url: str


class DocumentLibraryItemUploadConfirmation(BaseSchema):
    id: int


class DocumentLibraryItemUploadCancellation(BaseSchema):
    id: int


class DocumentLibraryOwnerParam(BaseSchema):
    owner_type: OwnerType = Field(..., json_schema_extra={"param_in": "path"})
    owner_id: int = Field(..., json_schema_extra={"param_in": "path"})


class DocumentLibraryItemParam(DocumentLibraryOwnerParam):
    item_id: int = Field(..., json_schema_extra={"param_in": "path"})


class GetDocumentLibraryItemsResponse(BaseSchema):
    items: List[DocumentLibraryItemRead]


class SendDocumentLibraryItemForSigning(BaseSchema):
    signing: CreateDocumentSigning


class StartDocumentLibraryItemUploadsRequest(BaseSchema):
    items: List[DocumentLibraryItemUpload]


class StartDocumentLibraryItemUploadsResponse(BaseSchema):
    items: List[DocumentLibraryItemUploadRead]


class ConfirmDocumentLibraryItemUploadsRequest(BaseSchema):
    items: List[DocumentLibraryItemUploadConfirmation]


class CancelDocumentLibraryItemUploadsRequest(BaseSchema):
    items: List[DocumentLibraryItemUploadCancellation]


class UpdateDocumentLibraryItemRequest(BaseSchema):
    document_type: DocumentType
    description: Optional[str] = None


class GetDocumentTypesResponse(BaseSchema):
    document_types: List[DocumentType]


class TokenParam(BaseSchema):
    token: str = Field(..., json_schema_extra={"param_in": "path"})


class DownloadDocumentLibraryItemTokenPayload(BaseSchema):
    item_id: int
    owner_id: int
    owner_type: OwnerType
    user_id: int
    organization_id: int
    is_admin: bool
    actor_type: EventLogActorType = EventLogActorType.USER
    actor_id: int


class DownloadDocumentLibraryItemsRequest(ApiRootModel[List[int]]):
    """Request body is an array of document library item IDs"""


class ShareDocumentLibraryItemsRequest(BaseSchema):
    contact_ids: List[int]
    item_ids: List[int]
    message: Optional[str] = None


class SharedDocumentLibraryItemsTokenPayload(BaseSchema):
    contact_id: int
    item_ids: List[int]
    owner_id: int
    owner_type: OwnerType
    shared_by: int
    organization_id: int
    is_admin: bool


class SharedDocumentLibraryItem(BaseSchema):
    id: int
    filename: str
    mime_type: str
    document_type: DocumentType
    file_size: int
    download_url: str


class SharedDocumentLibraryItemsResponse(BaseSchema):
    items: List[SharedDocumentLibraryItem]
