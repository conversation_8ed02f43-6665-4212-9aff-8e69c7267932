from datetime import datetime
from typing import List, Literal, Optional

from pydantic import field_serializer, validator

from strandproperties.constants import AttachmentDocumentType, SowiseStatus
from strandproperties.schemas.base import BaseSchema, PaginatedList
from strandproperties.schemas.contact import ContactRead
from strandproperties.schemas.param import PaginationParam


class DocumentListRead(BaseSchema):
    id: int
    name: str
    type: str
    visibility: str
    sowise_id: str
    status: str
    language: str
    contacts: Optional[list[ContactRead]] = []
    expires_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class DocumentFilterParam(PaginationParam):
    property_id: Optional[int] = None
    visibility: Optional[str] = None


class DocumentBasicInfo(BaseSchema):
    sowise_id: str
    status: Literal[
        SowiseStatus.DRAFT,
        SowiseStatus.PENDING,
        SowiseStatus.SIGNED,
        SowiseStatus.ARCHIVED,
        SowiseStatus.GENERIC,
        SowiseStatus.APPROVED,
        SowiseStatus.REQUESTING_CHANGES,
        SowiseStatus.NOTARIZED,
        SowiseStatus.IN_REVIEW,
    ]
    name: str
    type: str
    updated_at: datetime

    @validator("status")
    def lowercase_status(cls, value: str) -> str:
        return value.lower() if value else value


class DocumentAttachmentBase(BaseSchema):
    name: str
    type: str
    sowise_id: str
    updated_at: datetime | None = None
    attachment_type: str


class DocumentWithAttachments(DocumentBasicInfo):
    document_attachments: List[DocumentAttachmentBase]

    @field_serializer("document_attachments")
    def exclude_document_attachments(self, document_attachments: list, _info):
        return [
            attachment
            for attachment in document_attachments
            if attachment.attachment_type != AttachmentDocumentType.AGENT_INVOICES
        ]


# alias, required for schema discovery
DocumentList = PaginatedList[DocumentListRead]
