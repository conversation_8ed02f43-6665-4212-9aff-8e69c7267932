from typing import List, Optional

from pydantic import Field

from strandproperties.schemas.base import BaseSchema


class BrokerageOfferRequest(BaseSchema):
    realtor_id: int
    realtor_description: str

    address: str
    price_inquiry: float = Field(
        ge=0, description="Price inquiry must be greater than or equal to 0"
    )
    commission: float = Field(
        ge=0, description="Commission must be greater than or equal to 0"
    )

    notes: Optional[str] = None

    contact_ids: List[int] = Field(
        min_items=1, description="List of contact IDs (required, at least one)"
    )
