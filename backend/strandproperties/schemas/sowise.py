from datetime import datetime
from typing import List, Optional

from pydantic import EmailStr

from strandproperties.constants import Language, SigningMethod
from strandproperties.schemas.base import BaseSchema


class DocumentBase(BaseSchema):
    name: str
    type: str
    visibility: str
    sowise_id: str
    status: str
    language: str
    expires_at: datetime | None = None


class DocumentSowiseRead(BaseSchema):
    type: str
    platform: str
    drive_id: str
    name: str
    owner: str
    children: List[dict] = []
    parent: dict = {}
    state: int
    creator: str
    id: str
    created: str
    updated: str


class DocumentConvert(BaseSchema):
    name: str
    include_children: Optional[bool] = None
    parent: Optional[str]


class DocumentRead(BaseSchema):
    document_id: str
    status: str


class DocumentSignedPayload(BaseSchema):
    document_id: str


class AddSignee(BaseSchema):
    given_name: str
    family_name: str
    email: EmailStr
    level: int


class Signee(BaseSchema):
    owner: str
    document_id: str
    given_name: str
    family_name: str
    email: str
    signed: Optional[bool] = None
    level: int
    state: int
    signing_document_id: Optional[str] = None
    signing_url: Optional[str] = None
    id: str
    created: str
    updated: str
    full_name: str


class AddSigneeRead(BaseSchema):
    signee: Signee
    status: str


class SalesAgreementDocuments(BaseSchema):
    public_deed_or_nota_simple: Optional[bool] = None
    first_occupancy_license: Optional[bool] = None
    identity_document: Optional[bool] = None
    urban_contribution_tax_bill: Optional[bool] = None
    rubbish_collection_fees_receipt: Optional[bool] = None
    community_fees_receipt: Optional[bool] = None
    energy_performance_certificate: Optional[bool] = None
    certificate_of_community_property_owners: Optional[bool] = None
    certificate_of_tax_residence: Optional[bool] = None
    legal_representative_and_power_of_attorney: Optional[bool] = None
    the_powers_in_case_of_society: Optional[bool] = None
    separation_ruling: Optional[bool] = None
    prenuptial_agreement: Optional[bool] = None
    certificate_of_inheritance: Optional[bool] = None
    certificate_of_outstanding_debt: Optional[bool] = None
    utility_bill: Optional[bool] = None


class CreateSalesAgreement(BaseSchema):
    property_id: int
    realtor_id: int
    first_seller_id: int
    second_seller_id: Optional[int] = None
    validity_in_months: int
    reference_number: Optional[str] = None
    documents: Optional[SalesAgreementDocuments] = None
    document_language: Language
    level_of_signature: Optional[str] = SigningMethod.PEN_AND_PAPER.value
    company_reference: Optional[str] = None
    roaiib: Optional[str] = None
    liability_insurance: Optional[str] = None


class SalesAgreementContactBase(BaseSchema):
    name: str
    mobile: str
    email: str


class Seller(SalesAgreementContactBase):
    address: str
    passport_number: str
    nationality: str
    nie_number: str
    resident_of_spain: str


class FillSalesAgreementProperty(BaseSchema):
    reference: str
    address: str
    price: int
    commission: int
    legal_representative: Optional[SalesAgreementContactBase] = None


class FillSalesAgreement(BaseSchema):
    property: FillSalesAgreementProperty
    agent: SalesAgreementContactBase
    first_seller: Seller
    second_seller: Optional[Seller] = None
    validity_in_months: int
    documents: Optional[SalesAgreementDocuments] = None
    date: str
