from datetime import datetime
from enum import Enum, StrEnum
from typing import List, Optional
from strandproperties.constants import OrderBy
from strandproperties.schemas.base import BaseSchema, PaginatedList
from pydantic import BaseModel

from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.user import UserBasicInfo


class ParticipantType(StrEnum):
    INDIVIDUAL = "Individual"
    ORGANIZATION = "Organization"


class ParticipantRole(StrEnum):
    ORGANIZATION = "ORGANIZATION"
    ORGANIZATION_SIGNER = "ORGANIZATION_SIGNER"
    SIGNER = "SIGNER"


class InvoiceVerificationType(StrEnum):
    INVOICE_REFERENCE_NUMBER = "INVOICE_REFERENCE_NUMBER"
    INVOICE_MESSAGE = "INVOICE_MESSAGE"


class AttachmentDiasField(StrEnum):
    ATTACHMENTS = "attachments"
    BILL_OF_SALE = "billOfSale"
    TRANSFER_TAX_RECEIPT = "transferTaxReceipt"
    SHARE_CERTIFICATE = "shareCertificate"
    OWNERSHIP_TRANSFER_POWER_OF_ATTORNEY = "ownershipTransferPowerOfAttorney"
    BILL_OF_SALE_SIGNED = "billOfSaleSigned"


class AttachmentStatus(StrEnum):
    ACTIVE = "active"
    ARCHIVED = "archived"


class DisplayText(BaseSchema):
    fin: str


class Attachment(BaseSchema):
    id: str
    description: str
    businessId: Optional[str] = None

    class Config:
        alias_generator = None


class TransferTax(BaseSchema):
    amountInCents: int

    confirmedTimestamp: Optional[str] = None
    receipt: Optional[Attachment] = None

    class Config:
        alias_generator = None


class BankAccount(BaseSchema):
    name: str
    IBAN: str
    BIC: str

    class Config:
        alias_generator = None


class DiasBank(BaseSchema):
    name: str
    businessId: str
    groupName: str
    groupBusinessId: str

    class Config:
        alias_generator = None


class DiasBankListRead(BaseSchema):
    banks: List[DiasBank]

    class Config:
        alias_generator = None


class SigningTimestamp(BaseSchema):
    type: str
    timestamp: str

    class Config:
        alias_generator = None


class ParticipantIndividual(BaseSchema):
    firstName: str
    lastName: str
    socialSecurityNumber: str
    email: Optional[str] = None

    class Config:
        alias_generator = None


class ParticipantOrganization(BaseSchema):
    name: str
    businessId: str

    class Config:
        alias_generator = None


class SellerPerson(ParticipantIndividual):
    pass


class SellerOrganization(ParticipantOrganization):
    pass


class SellerRealtorPayment(BaseSchema):
    amountInCents: int
    referenceNumber: Optional[str] = None
    message: Optional[str] = None

    class Config:
        alias_generator = None


class BuyerPerson(ParticipantIndividual):
    pass


class BuyerOrganization(ParticipantOrganization):
    pass


class DiasSeller(BaseSchema):
    person: Optional[SellerPerson] = None
    organization: Optional[SellerOrganization] = None
    bankBusinessId: Optional[str] = None
    realtorPayment: Optional[SellerRealtorPayment] = None
    roles: Optional[List[str]] = None
    businessIds: Optional[List[str]] = None

    signingTimestamps: Optional[List[SigningTimestamp]] = None

    class Config:
        alias_generator = None


class DiasBuyer(BaseSchema):
    person: Optional[BuyerPerson] = None
    organization: Optional[BuyerOrganization] = None

    # Not in SharedTrade.Buyers
    # osuus: Optional[str] = None

    bankBusinessId: Optional[str] = None
    roles: Optional[List[str]] = None
    # kenenLukuunLainhuutoaHaetaan: Optional[KenenLukuunLainhuutoaHaetaan] = None  // Not exist in ShareTrade
    businessIds: Optional[List[str]] = None
    transferTax: Optional[TransferTax] = None

    signingTimestamps: Optional[List[SigningTimestamp]] = None

    class Config:
        alias_generator = None


class StrandSellerFI(BaseSchema):
    id: int
    realtorPayment: Optional[SellerRealtorPayment] = None
    roles: List[str]


class StrandSellerEN(BaseSchema):
    id: int
    realtor_payment: Optional[SellerRealtorPayment] = None
    roles: List[str]


class StrandBuyerFI(BaseSchema):  #  Currently not used
    id: int
    osuus: str
    roles: Optional[List[str]] = None
    # kenenLukuunLainhuutoaHaetaan: Optional[KenenLukuunLainhuutoaHaetaan] = None


class StrandBuyerEN(BaseSchema):
    id: int
    share: str
    roles: Optional[List[str]] = None
    # title_claim_type: Optional[TitleClaimType] = None


class InternalParticipant(BaseSchema):
    bankBusinessId: Optional[str] = None
    bankName: Optional[str] = None

    # As Individual
    id: Optional[int] = None  # This associates with Contact.id
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    socialSecurityNumber: Optional[str] = None
    email: Optional[str] = None

    # As Organization
    organizationName: Optional[str] = None
    organizationBusinessId: Optional[str] = None
    businessIds: Optional[List[str]] = None
    roles: Optional[List[ParticipantRole]] = None

    class Config:
        alias_generator = None


class InternalSeller(InternalParticipant):
    sellerType: ParticipantType
    hasRealtorPayment: bool
    realtorPaymentAmountInEuros: Optional[float] = None

    class Config:
        alias_generator = None


class InternalBuyer(InternalParticipant):
    buyerType: ParticipantType
    transferTaxAmountInEuros: Optional[float] = None

    class Config:
        alias_generator = None


class InternalRealtor(BaseSchema):
    id: Optional[int] = None  # This associates with User.id
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    email: Optional[str] = None
    phoneNumber: Optional[str] = None
    companyName: Optional[str] = None
    companyId: Optional[str] = None

    class Config:
        alias_generator = None


class InitiatorContactInfo(BaseSchema):
    email: str
    phoneNumber: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None

    class Config:
        alias_generator = None


class DiasAttachmentBase(BaseSchema):
    dias_field: str
    property_reference: str
    dias_id: Optional[str] = None
    description: Optional[str] = None
    document_type: Optional[str] = None


class DiasAttachmentCreate(DiasAttachmentBase):
    file_name: str


class DiasAttachmentRead(DiasAttachmentBase):
    id: int
    status: Optional[str] = None
    file_name: Optional[str] = None
    created_at: datetime
    created_by: Optional[UserBasicInfo] = None


class DiasAttachmentPresignedUrlRead(BaseSchema):
    url: str
    original_filename: str
    message: Optional[str] = None


class NotificationType(str, Enum):
    SHARED_TRADE = "PaperTradeEventV1"
    PROPERTY_TRADE = "KiinteistokauppaEventV1"


class DiasWebhookEvent(BaseSchema):
    notificationType: NotificationType
    tradeId: str
    initiatorPersonId: str
    event: dict


class DiasAttachmentsFilterParam(PaginationParam):
    order_by: OrderBy = OrderBy.LATEST
    property_reference: Optional[str] = None
    dias_field: Optional[str] = None


PaginatedListDiasAttachmentRead = PaginatedList[DiasAttachmentRead]
