from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Literal, Optional, Union

from annotated_types import Len
from pydantic import EmailStr, field_validator, model_validator
from typing_extensions import Annotated

from strandproperties.constants import (
    ContactRelatedPartyType,
    ContactStatus,
    ContactType,
    CountryCode,
    LeadAssignmentStatus,
    OrderBy,
    SortEnum,
)
from strandproperties.schemas.base import BaseSchema, PaginatedList
from strandproperties.schemas.contact_marketing_consent import (
    ContactMarketingConsentStatusResponse,
    NewsletterType,
)
from strandproperties.schemas.mapping import BankRead
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.tag import TagRead
from strandproperties.schemas.user import UserListRead
from strandproperties.schemas.utils import EmptyStrToNone, StripNotEmptyStr
from strandproperties.views.exceptions import CustomValidationError


class ContactRelatedPartyCreate(BaseSchema):
    id: int
    type: ContactRelatedPartyType


class ContactMarketingConsentItem(BaseSchema):
    newsletter_type: NewsletterType
    is_subscribed: bool


class ContactBase(BaseSchema):
    name: Optional[StripNotEmptyStr] = None
    email: Optional[Union[EmailStr, Literal[""]]] = None
    phone_numbers: Optional[List[EmptyStrToNone]] = None
    company: Optional[EmptyStrToNone] = None
    website: Optional[EmptyStrToNone] = None
    address: Optional[EmptyStrToNone] = None
    city: Optional[EmptyStrToNone] = None
    post_code: Optional[EmptyStrToNone] = None
    country: Optional[EmptyStrToNone] = None
    preferred_language: Optional[EmptyStrToNone] = None
    passport_number: Optional[EmptyStrToNone] = None
    nationality: Optional[EmptyStrToNone] = None
    country_specific: Optional[dict] = {}
    social_security_number: Optional[EmptyStrToNone] = None
    type: Optional[ContactType] = None
    business_id: Optional[EmptyStrToNone] = None
    vat_number: Optional[EmptyStrToNone] = None
    iban: Optional[EmptyStrToNone] = None
    bic: Optional[EmptyStrToNone] = None
    bank_id: Optional[int] = None
    notes: Optional[EmptyStrToNone] = None
    source: Optional[EmptyStrToNone] = None
    assigned_to: Optional[List[int]] = None
    new_tags: Optional[List[str]] = None
    existing_tags: Optional[List[int]] = None
    groups: Optional[List[int]] = None
    areas: Optional[List[str]] = None
    property_types: Optional[List[int]] = None
    min_bedrooms: Optional[int] = None
    min_price: Optional[int] = None
    max_price: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[str] = None
    confidential_customer: Optional[bool] = None
    consent_email_marketing: Optional[bool] = None
    consent_newsletter_marketing: Optional[bool] = None
    marketing_consents: Optional[List[ContactMarketingConsentItem]] = None


class ContactCreate(ContactBase):
    name: StripNotEmptyStr
    tags: Optional[List[Dict[str, Union[int, str]]]] = None
    phone_numbers: List[EmptyStrToNone] = []
    assigned_to: Annotated[List[int], Len(min_length=1)]
    new_tags: Optional[List[str]] = []
    existing_tags: Optional[List[int]] = []
    groups: Optional[List[int]] = []
    areas: Optional[List[str]] = []
    property_types: Optional[List[int]] = []

    @model_validator(mode="after")
    def validate_contact_info(self) -> "ContactCreate":
        is_collaborator = any(
            tag.get("label") == "Collaborator" for tag in self.tags or []
        )
        if not is_collaborator and not self.phone_numbers and not self.email:
            raise ValueError("Either email or at least one phone number is required")
        return self

    @field_validator("email")
    @classmethod
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v


class ContactEdit(ContactBase):
    status: Optional[ContactStatus] = None
    marketing_consents: Optional[List[ContactMarketingConsentItem]] = None


class ContactCreateV2(ContactBase):
    related_parties: Optional[List[ContactRelatedPartyCreate]] = []

    @field_validator("phone_numbers")
    @classmethod
    def validate_contact_info(cls, v, values):
        if not v and not values.get("email"):
            raise ValueError("Either email or at least one phone number is required")
        return v

    @field_validator("email")
    @classmethod
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v


class ContactEditV2(ContactBase):
    status: Optional[ContactStatus] = None
    related_parties: Optional[List[ContactRelatedPartyCreate]] = []


class ContactIdName(BaseSchema):
    id: int
    name: str


class ContactRelatedPartyRead(BaseSchema):
    id: int
    name: str
    email: Optional[str]
    phone_numbers: List[str] = []
    contact_type: Optional[ContactType]
    type: ContactRelatedPartyType


class ContactListRead(BaseSchema):
    class ContactGroupRead(BaseSchema):
        id: int
        name: str
        description: Optional[EmptyStrToNone]
        language: Optional[EmptyStrToNone]
        organization_id: int
        created_at: datetime
        updated_at: datetime

    id: int
    name: str
    email: Optional[str]
    phone_numbers: List[str] = []
    company: Optional[str]
    website: Optional[str]
    address: Optional[str]
    city: Optional[str]
    post_code: Optional[str]
    country: Optional[str]
    preferred_language: Optional[str]
    passport_number: Optional[str]
    nationality: Optional[str]
    country_specific: Optional[dict] = {}
    social_security_number: Optional[str]
    type: Optional[ContactType]
    business_id: Optional[str]
    vat_number: Optional[str]
    bank: Optional[BankRead]
    iban: Optional[str]
    bic: Optional[str]
    notes: Optional[str]
    source: Optional[str]
    status: Optional[ContactStatus] = None
    assigned_to_users: list[UserListRead]
    related_parties: list[ContactRelatedPartyRead]
    tags: list[TagRead]
    created_at: datetime
    updated_at: datetime
    groups: list[ContactGroupRead]
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[str] = None
    confidential_customer: Optional[bool] = None
    consent_email_marketing: Optional[bool] = None
    consent_newsletter_marketing: Optional[bool] = None


class ContactPreviewListRead(BaseSchema):
    id: int
    name: str
    email: Optional[str]
    assigned_to_users: list[UserListRead]
    created_at: datetime
    updated_at: datetime


class ContactPropertiesRead(BaseSchema):
    id: int
    reference: str


class ContactRead(ContactListRead):
    class BuyerPropertyType(BaseSchema):
        id: int
        name: str
        category: str
        origin: str

    bank_id: Optional[int]
    bank: Optional[BankRead]
    properties: list[ContactPropertiesRead]
    areas: Optional[List[str]]
    property_types: list[BuyerPropertyType]
    min_bedrooms: int | None
    min_price: int | None
    max_price: int | None
    related_parties: list[ContactRelatedPartyRead]
    marketing_consents: list[ContactMarketingConsentStatusResponse] = []


class ContactSortColumn(str, Enum):
    NAME = "name"
    EMAIL = "email"


class ContactFilterParam(PaginationParam):
    order_by: OrderBy = OrderBy.ALPHABETICAL_ASC
    nationalities: Optional[list[str]] = None
    keyword: Optional[str] = None
    assigned_to: Optional[str] = None
    tag_ids: Optional[str] = None
    group_ids: Optional[str] = None
    assignment_status: Optional[LeadAssignmentStatus] = None
    sort_column: Optional[ContactSortColumn] = None
    sort_direction: Optional[SortEnum] = None
    organization_id: Optional[int] = None

    @field_validator("nationalities", mode="before")
    @classmethod
    def parse_nationalities(cls, v):
        if isinstance(v, str):
            return [n.strip() for n in v.split(",") if n.strip()]
        return v

    @field_validator("sort_column", "sort_direction", mode="before")
    @classmethod
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class ContactPreviewFilterParam(PaginationParam):
    order_by: OrderBy = OrderBy.ALPHABETICAL_ASC
    email: Optional[str] = None
    name: Optional[str] = None
    phone_number: Optional[str] = None
    sort_column: Optional[ContactSortColumn] = None
    sort_direction: Optional[SortEnum] = None

    @field_validator("sort_column", "sort_direction", mode="before")
    @classmethod
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class ContactMultipleAssign(BaseSchema):
    contact_ids: list[int]
    agent_ids: list[int]


class ContactNewsletterCreate(BaseSchema):
    email: Union[EmailStr, Literal[""]]
    org: str


def validate_complete_contact_schema(
    data: ContactCreate | ContactEdit, org_country_code: CountryCode
):
    """
    Validates the contact schema based on the contact type.

    Args:
        data (Union[ContactCreate, ContactEdit]): The input model instance.
        org_country_code (CountryCode): The country code of the organization.
    Returns:
        Union[ContactCreate, ContactEdit]: The validated instance.

    Raises:
        CustomValidationError: If validation fails, raises a custom error with formatted messages.
    """

    errors = {}

    if org_country_code == CountryCode.FINLAND:
        errors = _validate_schema_fi(data)
    else:
        errors = _validate_schema_sp(data)

    if errors:
        raise CustomValidationError(errors)

    return data


def _validate_schema_fi(data: ContactCreate | ContactEdit):
    errors = {}
    common_required_fields = {
        "first_name": {"field_name": "First name"},
        "last_name": {"field_name": "Last name"},
        "phones": {"field_name": "Phone numbers"},
        "email": {"field_name": "Email"},
        "source": {"field_name": "Source"},
    }
    # Finnish required fields
    person_required_fields = {
        **common_required_fields,
    }

    org_required_fields = {
        **common_required_fields,
        "name": {"field_name": "Name"},
        "business_id": {
            "field_name": "Business ID",
            "validationFn": _validate_fi_business_id,
        },
    }

    estate_required_fields = {
        **common_required_fields,
        "name": {"field_name": "Name"},
    }

    match (data.type):
        case ContactType.PERSON:
            errors = _validate_fields(data, person_required_fields)
        case ContactType.ORGANIZATION:
            errors = _validate_fields(data, org_required_fields)
        case ContactType.ESTATE:
            errors = _validate_fields(data, estate_required_fields)

    return errors


def _validate_schema_sp(data: ContactCreate | ContactEdit):
    errors = {}

    if not data.email and not data.phone_numbers:
        errors["contact_info"] = {
            "message": "Either email or at least one phone number is required.",
            "code": "missing",
        }
    return errors


def _validate_fields(
    data: ContactCreate | ContactEdit,
    required_fields: dict[str, dict[str, str | Callable[[Any], str]]],
):
    errors = {}
    for field, validation in required_fields.items():
        field_value = getattr(data, field, None)
        if field_value is None:
            errors[field] = {
                "message": f"{validation.get('field_name')} is required.",
                "code": "missing",
            }
        elif validation.get("validationFn"):
            validation_fn = validation["validationFn"]
            if not callable(validation_fn):
                continue

            error_message = validation_fn(field_value)
            if error_message:
                errors[field] = {
                    "message": error_message,
                    "code": "invalid",
                }
    return errors


def _validate_fi_business_id(v):
    if len(v) != 9:
        return "Business ID must be 9 characters long"
    return None


# alias, required for schema discovery
ContactList = PaginatedList[ContactListRead]
