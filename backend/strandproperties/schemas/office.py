from pydantic import Field, StringConstraints, field_serializer
from typing_extensions import Annotated

from strandproperties.constants import RoleType
from strandproperties.schemas.base import BaseSchema

StripNotEmptyStr = Annotated[
    str,
    StringConstraints(strip_whitespace=True, min_length=1),
]


class RoleSchema(BaseSchema):
    role: str


class AdminBasicInfo(BaseSchema):
    id: int
    first_name: str
    last_name: str
    roles: list[RoleSchema]


class OfficeBase(BaseSchema):
    name: StripNotEmptyStr


class OfficeRead(OfficeBase):
    id: int
    users: list[AdminBasicInfo] = Field(
        alias="admins",
    )

    @field_serializer("users")
    def serializer_users(self, users: list, _info):
        return [
            user
            for user in users
            if any(RoleType.ADMIN in role.role for role in user.roles)
        ]
