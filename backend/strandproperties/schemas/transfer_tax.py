"""
Transfer Tax API Types

This module contains Pydantic models for the Finnish Tax Administration's Transfer Tax API.
Based on the OpenAPI specification for the SendReturn/v1 endpoint.
"""

from datetime import date, datetime
from enum import IntEnum
from typing import List, Optional, Union

from pydantic import Field, field_validator, model_validator

from strandproperties.schemas.base import BaseSchema


class SenderType(IntEnum):
    REAL_ESTATE_AGENT = 1
    STOCK_TRADER = 2
    INDIVIDUAL_BUYER = 3


class TransferType(IntEnum):
    SALE = 1
    EXCHANGE_OF_PROPERTY_DEAL = 2
    OTHER = 3


class AssetType(IntEnum):
    RESIDENTIAL_PROPERTY = 1
    PARKING_SPACE_STORAGE_UNIT = 2
    CORPORATE_SHARE = 3
    TIME_SHARE = 4
    SHARE_IN_TELEPHONE_COMPANY = 5
    OTHER_SECURITY_THAN_SHARE = 6


class SellerIndividualOrCorporate(IntEnum):
    INDIVIDUAL = 1
    CORPORATE = 2


class FirstTimeHomebuyerConditions(IntEnum):
    YES_CONDITIONS_FULFILLED = 1
    YES_FIRST_TIME_HOMEBUYER_ASSURES = 2
    CONDITIONS_NOT_FULFILLED = 3


class AdditionalSellingPrice(BaseSchema):
    add_selling_price_when_clarified: bool = Field(
        alias="AddSellingPriceWhenClarified",
        description="Was the amount of additional selling price or the estimated additional selling price known when the deed of sale was signed?",
    )
    buyer_share_add_selling_price: Optional[float] = Field(
        None,
        alias="BuyerShareAddSellingPrice",
        description="Buyer's share of the additional selling price",
    )
    add_selling_price_clarified_date: Optional[date] = Field(
        None,
        alias="AddSellingPriceClarifiedDate",
        description="Date when the amount of the additional selling price is clarified",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        add_selling_price_when_clarified: bool,
        buyer_share_add_selling_price: Optional[float] = None,
        add_selling_price_clarified_date: Optional[date] = None,
    ) -> "AdditionalSellingPrice":
        """Create an AdditionalSellingPrice instance with more Pythonic snake_case parameters."""
        return cls(
            AddSellingPriceWhenClarified=add_selling_price_when_clarified,
            BuyerShareAddSellingPrice=buyer_share_add_selling_price,
            AddSellingPriceClarifiedDate=add_selling_price_clarified_date,
        )


class SellerDetails(BaseSchema):
    seller_individual_or_corporate: SellerIndividualOrCorporate = Field(
        alias="SellerIndividualOrCorporate",
        description="Is the seller individual or corporate?",
    )
    seller_id: Optional[str] = Field(
        None, alias="SellerID", description="Seller's personal ID or business ID"
    )
    seller_date_of_birth: Optional[date] = Field(
        None, alias="SellerDateOfBirth", description="Seller's date of birth"
    )
    seller_individual_foreign_id: Optional[str] = Field(
        None,
        alias="SellerIndividualForeignID",
        max_length=20,
        description="Seller's foreign personal ID",
    )
    seller_corporate_foreign_id: Optional[str] = Field(
        None,
        alias="SellerCorporateForeignID",
        max_length=35,
        description="Seller's foreign business ID",
    )
    seller_name: Optional[str] = Field(
        None, alias="SellerName", max_length=200, description="Seller's name"
    )
    work_effort: Optional[bool] = Field(
        None,
        alias="WorkEffort",
        description="Is the transfer of corporate share or other securities based on work effort, either completely or in part?",
    )
    interest_corporate_buyer_individual_seller: Optional[bool] = Field(
        None,
        alias="InterestCorporateBuyerIndividualSeller",
        description="Is the seller a shareholder or partner in the buyer company or a parent, a child or spouse of a shareholder or partner?",
    )
    interest_individual_buyer_corporate_seller: Optional[bool] = Field(
        None,
        alias="InterestIndividualBuyerCorporateSeller",
        description="Was the buyer a shareholder or a partner in the seller company before this transfer or is the buyer a parent, a child or spouse of a shareholder or partner?",
    )
    interest_corporate_buyer_seller: Optional[bool] = Field(
        None,
        alias="InterestCorporateBuyerSeller",
        description="Is this transfer between a company and company's shareholder or partner or is this an intra-group transfer?",
    )
    seller_ownership_share_fractions: Optional[str] = Field(
        None,
        alias="SellerOwnershipShareFractions",
        max_length=11,
        description="Share of ownership in fractions which seller sold to the buyer of the entire residential property or other space",
    )
    seller_ownership_share_percentage: Optional[float] = Field(
        None,
        alias="SellerOwnershipSharePercentage",
        ge=0.0,
        le=100.0,
        description="Share of ownership in percentage which seller sold to the buyer of the entire residential property or other space",
    )
    quantity_of_shares_seller_sold: Optional[int] = Field(
        None,
        alias="QuantityOfSharesSellerSold",
        description="Quantity of shares which the seller sold to the buyer",
    )
    nonresident_seller_buyer: Optional[bool] = Field(
        None,
        alias="NonresidentSellerBuyer",
        description="Seller and buyer are nonresidents (foreign citizens)",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        seller_individual_or_corporate: SellerIndividualOrCorporate,
        seller_id: Optional[str] = None,
        seller_date_of_birth: Optional[date] = None,
        seller_individual_foreign_id: Optional[str] = None,
        seller_corporate_foreign_id: Optional[str] = None,
        seller_name: Optional[str] = None,
        work_effort: Optional[bool] = None,
        interest_corporate_buyer_individual_seller: Optional[bool] = None,
        interest_individual_buyer_corporate_seller: Optional[bool] = None,
        interest_corporate_buyer_seller: Optional[bool] = None,
        seller_ownership_share_fractions: Optional[str] = None,
        seller_ownership_share_percentage: Optional[float] = None,
        quantity_of_shares_seller_sold: Optional[int] = None,
        nonresident_seller_buyer: Optional[bool] = None,
    ) -> "SellerDetails":
        """Create a SellerDetails instance with more Pythonic snake_case parameters."""
        return cls(
            SellerIndividualOrCorporate=seller_individual_or_corporate,
            SellerID=seller_id,
            SellerDateOfBirth=seller_date_of_birth,
            SellerIndividualForeignID=seller_individual_foreign_id,
            SellerCorporateForeignID=seller_corporate_foreign_id,
            SellerName=seller_name,
            WorkEffort=work_effort,
            InterestCorporateBuyerIndividualSeller=interest_corporate_buyer_individual_seller,
            InterestIndividualBuyerCorporateSeller=interest_individual_buyer_corporate_seller,
            InterestCorporateBuyerSeller=interest_corporate_buyer_seller,
            SellerOwnershipShareFractions=seller_ownership_share_fractions,
            SellerOwnershipSharePercentage=seller_ownership_share_percentage,
            QuantityOfSharesSellerSold=quantity_of_shares_seller_sold,
            NonresidentSellerBuyer=nonresident_seller_buyer,
        )


class FirstTimeHomebuyer(BaseSchema):
    conditions: FirstTimeHomebuyerConditions = Field(
        alias="Conditions", description="Conditions fulfilled"
    )
    portion_of_selling_price: Optional[float] = Field(
        None,
        alias="PortionOfSellingPrice",
        description="If the acquired real estate or residential is only partly exempt from tax, enter here the portion of the selling price and other compensation that buyer pays the transfer tax",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        conditions: FirstTimeHomebuyerConditions,
        portion_of_selling_price: Optional[float] = None,
    ) -> "FirstTimeHomebuyer":
        """Create a FirstTimeHomebuyer instance with more Pythonic snake_case parameters."""
        return cls(
            Conditions=conditions,
            PortionOfSellingPrice=portion_of_selling_price,
        )


class AssetDetails(BaseSchema):
    asset: AssetType = Field(alias="Asset", description="Asset type")
    acquired_portion_fractions: Optional[str] = Field(
        None,
        alias="AcquiredPortionFractions",
        max_length=11,
        description="Acquired portion in fractions",
    )
    acquired_portion_percentage: Optional[float] = Field(
        None,
        alias="AcquiredPortionPercentage",
        ge=0.0,
        le=100.0,
        description="Acquired portion in percentage",
    )
    company_id: Optional[str] = Field(
        None, alias="CompanyID", description="Target company's business ID"
    )
    company_name: Optional[str] = Field(
        None, alias="CompanyName", max_length=200, description="Target company's name"
    )
    share_numbers: Optional[str] = Field(
        None, alias="ShareNumbers", max_length=30, description="Share numbers"
    )
    area_m2: Optional[float] = Field(None, alias="AreaM2", description="Area (m²)")
    quantity_of_shares: Optional[int] = Field(
        None, alias="QuantityOfShares", description="Quantity of shares"
    )
    residential_id: Optional[str] = Field(
        None,
        alias="ResidentialID",
        max_length=200,
        description="Residential or other space identifier (letter/number/week)",
    )
    new_construction: Optional[bool] = Field(
        None,
        alias="NewConstruction",
        description="Is the acquired asset a new construction?",
    )
    ownership_transfer_date: Optional[date] = Field(
        None,
        alias="OwnershipTransferDate",
        description="Date of transfer of ownership rights",
    )
    buyer_share_selling_price: Optional[float] = Field(
        None,
        alias="BuyerShareSellingPrice",
        description="Buyer's share of the selling price",
    )
    buyer_share_loan: Optional[float] = Field(
        None,
        alias="BuyerShareLoan",
        description="Buyer's share of housing company loan",
    )
    buyer_share_seller_house_loan: Optional[float] = Field(
        None,
        alias="BuyerShareSellerHouseLoan",
        description="Buyer's share of the seller's house loan or other debt that buyer has taken over",
    )
    buyer_share_seller_debt: Optional[float] = Field(
        None,
        alias="BuyerShareSellerDebt",
        description="Buyer's share of the seller's debt that buyer has taken over",
    )
    buyer_share_other_compensation: Optional[float] = Field(
        None,
        alias="BuyerShareOtherCompensation",
        description="Buyer's share of other compensation which is a benefit to the seller",
    )
    add_selling_price: Optional[bool] = Field(
        None,
        alias="AddSellingPrice",
        description="Does the deed of sale include a condition for an additional selling price?",
    )
    additional_selling_price: Optional[List["AdditionalSellingPrice"]] = Field(
        None, alias="AdditionalSellingPrice", description="Additional selling price"
    )
    buyer_share_total_selling_price: Optional[float] = Field(
        None,
        alias="BuyerShareTotalSellingPrice",
        description="Buyer's share of the total sum of selling price and other compensation",
    )
    amount_transfer_tax: Optional[float] = Field(
        None, alias="AmountTransferTax", description="Amount of transfer tax"
    )
    seller_details: List["SellerDetails"] = Field(
        alias="SellerDetails", description="Details of seller(s)"
    )
    first_time_homebuyer: Optional[List["FirstTimeHomebuyer"]] = Field(
        None,
        alias="FirstTimeHomebuyer",
        description="First-time home buyer information",
    )

    @field_validator("additional_selling_price")
    @classmethod
    def validate_additional_selling_price_max_items(cls, v):
        if v and len(v) > 10:
            raise ValueError("AdditionalSellingPrice can have maximum 10 items")
        return v

    @field_validator("first_time_homebuyer")
    @classmethod
    def validate_first_time_homebuyer_max_items(cls, v):
        if v and len(v) > 1:
            raise ValueError("FirstTimeHomebuyer can have maximum 1 item")
        return v

    @field_validator("seller_details")
    @classmethod
    def validate_seller_details_min_items(cls, v):
        if not v:
            raise ValueError("SellerDetails must contain at least one item")
        return v

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        asset: AssetType,
        seller_details: List["SellerDetails"],
        acquired_portion_fractions: Optional[str] = None,
        acquired_portion_percentage: Optional[float] = None,
        company_id: Optional[str] = None,
        company_name: Optional[str] = None,
        share_numbers: Optional[str] = None,
        area_m2: Optional[float] = None,
        quantity_of_shares: Optional[int] = None,
        residential_id: Optional[str] = None,
        new_construction: Optional[bool] = None,
        ownership_transfer_date: Optional[date] = None,
        buyer_share_selling_price: Optional[float] = None,
        buyer_share_loan: Optional[float] = None,
        buyer_share_seller_house_loan: Optional[float] = None,
        buyer_share_seller_debt: Optional[float] = None,
        buyer_share_other_compensation: Optional[float] = None,
        add_selling_price: Optional[bool] = None,
        additional_selling_price: Optional[List["AdditionalSellingPrice"]] = None,
        buyer_share_total_selling_price: Optional[float] = None,
        amount_transfer_tax: Optional[float] = None,
        first_time_homebuyer: Optional[List["FirstTimeHomebuyer"]] = None,
    ) -> "AssetDetails":
        """Create an AssetDetails instance with more Pythonic snake_case parameters."""
        return cls(
            Asset=asset,
            SellerDetails=seller_details,
            AcquiredPortionFractions=acquired_portion_fractions,
            AcquiredPortionPercentage=acquired_portion_percentage,
            CompanyID=company_id,
            CompanyName=company_name,
            ShareNumbers=share_numbers,
            AreaM2=area_m2,
            QuantityOfShares=quantity_of_shares,
            ResidentialID=residential_id,
            NewConstruction=new_construction,
            OwnershipTransferDate=ownership_transfer_date,
            BuyerShareSellingPrice=buyer_share_selling_price,
            BuyerShareLoan=buyer_share_loan,
            BuyerShareSellerHouseLoan=buyer_share_seller_house_loan,
            BuyerShareSellerDebt=buyer_share_seller_debt,
            BuyerShareOtherCompensation=buyer_share_other_compensation,
            AddSellingPrice=add_selling_price,
            AdditionalSellingPrice=additional_selling_price,
            BuyerShareTotalSellingPrice=buyer_share_total_selling_price,
            AmountTransferTax=amount_transfer_tax,
            FirstTimeHomebuyer=first_time_homebuyer,
        )


class SendReturnRequest(BaseSchema):
    replacement_return: Optional[bool] = Field(
        None,
        alias="ReplacementReturn",
        description="Set this flag value to True if the return that is being sent is a replacement return to previously sent and accepted return.",
    )
    previous_return_key: Optional[str] = Field(
        None,
        alias="PreviousReturnKey",
        min_length=4,
        max_length=20,
        description="When correcting previously sent and accepted return, the original response contained a unique identifier number. Set that unique return identifier to this field when sending the correction. Also the ReplacementReturn must be true when using PreviousReturnKey.",
    )
    sender: SenderType = Field(alias="Sender", description="Sender type")
    buyer_id: Optional[str] = Field(
        None, alias="BuyerID", description="Buyer's personal ID or business ID"
    )
    buyer_date_of_birth: Optional[date] = Field(
        None, alias="BuyerDateOfBirth", description="Buyer's date of birth"
    )
    buyer_individual_foreign_id: Optional[str] = Field(
        None,
        alias="BuyerIndividualForeignID",
        max_length=20,
        description="Buyer's foreign personal ID",
    )
    buyer_corporate_foreign_id: Optional[str] = Field(
        None,
        alias="BuyerCorporateForeignID",
        max_length=30,
        description="Buyer's foreign business ID",
    )
    buyer_name: Optional[str] = Field(
        None, alias="BuyerName", max_length=200, description="Buyer's name"
    )
    real_estate_agency_id: Optional[str] = Field(
        None, alias="RealEstateAgencyID", description="Real estate agency's business ID"
    )
    real_estate_agency_name: Optional[str] = Field(
        None,
        alias="RealEstateAgencyName",
        max_length=200,
        description="Real estate agency's name",
    )
    agency_contact_person_name: Optional[str] = Field(
        None,
        alias="AgencyContactPersonName",
        max_length=200,
        description="Real estate agency's contact person's name",
    )
    real_estate_agency_phone: Optional[str] = Field(
        None,
        alias="RealEstateAgencyPhone",
        max_length=35,
        description="Real estate agency's phone number",
    )
    real_estate_agency_address: Optional[str] = Field(
        None,
        alias="RealEstateAgencyAddress",
        max_length=70,
        description="Real estate agency's post address",
    )
    real_estate_agency_post_code: Optional[str] = Field(
        None,
        alias="RealEstateAgencyPostCode",
        max_length=9,
        description="Real estate agency's post code",
    )
    stock_trader_id: Optional[str] = Field(
        None, alias="StockTraderID", description="Stock trader's business ID"
    )
    stock_trader_name: Optional[str] = Field(
        None, alias="StockTraderName", max_length=200, description="Stock trader's name"
    )
    signing_date: date = Field(
        alias="SigningDate",
        description="Signing date of the deed of sale or other contract",
    )
    transfer_type: TransferType = Field(
        alias="TransferType", description="Transfer type"
    )
    advance_ruling: Optional[bool] = Field(
        None,
        alias="AdvanceRuling",
        description="Has Verohallinto issued an advance ruling regarding this contract that the buyer is requesting to be applied?",
    )
    asset_details: List["AssetDetails"] = Field(
        alias="AssetDetails", description="Details of asset(s)"
    )

    @field_validator("asset_details")
    @classmethod
    def validate_asset_details(cls, v):
        if not v:
            raise ValueError("AssetDetails must contain at least one item")
        return v

    @field_validator("previous_return_key")
    @classmethod
    def validate_previous_return_key(cls, v, info):
        """Validate that PreviousReturnKey is used with ReplacementReturn=True"""
        if v and not info.data.get("replacement_return"):
            raise ValueError(
                "PreviousReturnKey can only be used when ReplacementReturn is True"
            )
        return v

    @model_validator(mode="after")
    def validate_first_time_homebuyer_date_restriction(self):
        """Validate that first-time homebuyer info cannot be given for sales from 2024 onwards"""
        if self.signing_date and self.signing_date >= date(2024, 1, 1):
            for asset_detail in self.asset_details:
                if asset_detail.first_time_homebuyer:
                    raise ValueError(
                        "First time home buyer information cannot be given for sales starting from 1.1.2024"
                    )
        return self

    @model_validator(mode="after")
    def validate_sender_asset_type_restrictions(self):
        """Validate sender type restrictions on asset types"""
        for asset_detail in self.asset_details:
            asset_type = asset_detail.asset

            if self.sender == SenderType.REAL_ESTATE_AGENT:
                if asset_type not in [
                    AssetType.RESIDENTIAL_PROPERTY,
                    AssetType.PARKING_SPACE_STORAGE_UNIT,
                    AssetType.TIME_SHARE,
                ]:
                    raise ValueError(
                        "Real Estate Agent can only submit: Residential property, Parking space/storage unit, or Time share"
                    )

            elif self.sender == SenderType.STOCK_TRADER:
                if asset_type not in [
                    AssetType.CORPORATE_SHARE,
                    AssetType.SHARE_IN_TELEPHONE_COMPANY,
                    AssetType.OTHER_SECURITY_THAN_SHARE,
                ]:
                    raise ValueError(
                        "Stock Trader can only submit: Corporate share, Share in telephone company, or Other security than share"
                    )

            elif self.sender == SenderType.INDIVIDUAL_BUYER:
                if asset_type not in [
                    AssetType.RESIDENTIAL_PROPERTY,
                    AssetType.PARKING_SPACE_STORAGE_UNIT,
                ]:
                    raise ValueError(
                        "Individual Buyer can only submit: Residential property or Parking space/storage unit"
                    )

        return self

    @model_validator(mode="after")
    def validate_mutually_exclusive_fields(self):
        """Validate mutually exclusive fields and field dependencies"""
        for asset_detail in self.asset_details:
            # Fractions vs percentage - only one can be given for certain asset types
            if asset_detail.asset in [
                AssetType.RESIDENTIAL_PROPERTY,
                AssetType.PARKING_SPACE_STORAGE_UNIT,
                AssetType.TIME_SHARE,
            ]:
                has_fractions = asset_detail.acquired_portion_fractions is not None
                has_percentage = asset_detail.acquired_portion_percentage is not None
                if has_fractions and has_percentage:
                    raise ValueError(
                        "Either AcquiredPortionFractions or AcquiredPortionPercentage must be given, but not both"
                    )

            # AddSellingPrice dependency
            if (
                asset_detail.add_selling_price
                and not asset_detail.additional_selling_price
            ):
                raise ValueError(
                    "AdditionalSellingPrice must be provided when AddSellingPrice is True"
                )

            # NewConstruction dependency
            if (
                asset_detail.new_construction
                and not asset_detail.ownership_transfer_date
            ):
                raise ValueError(
                    "OwnershipTransferDate must be provided when NewConstruction is True"
                )

        return self

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        sender: SenderType,
        signing_date: date,
        transfer_type: TransferType,
        asset_details: List["AssetDetails"],
        replacement_return: Optional[bool] = None,
        previous_return_key: Optional[str] = None,
        buyer_id: Optional[str] = None,
        buyer_date_of_birth: Optional[date] = None,
        buyer_individual_foreign_id: Optional[str] = None,
        buyer_corporate_foreign_id: Optional[str] = None,
        buyer_name: Optional[str] = None,
        real_estate_agency_id: Optional[str] = None,
        real_estate_agency_name: Optional[str] = None,
        agency_contact_person_name: Optional[str] = None,
        real_estate_agency_phone: Optional[str] = None,
        real_estate_agency_address: Optional[str] = None,
        real_estate_agency_post_code: Optional[str] = None,
        stock_trader_id: Optional[str] = None,
        stock_trader_name: Optional[str] = None,
        advance_ruling: Optional[bool] = None,
    ) -> "SendReturnRequest":
        """Create a SendReturnRequest instance with more Pythonic snake_case parameters."""
        return cls(
            Sender=sender,
            SigningDate=signing_date,
            TransferType=transfer_type,
            AssetDetails=asset_details,
            ReplacementReturn=replacement_return,
            PreviousReturnKey=previous_return_key,
            BuyerID=buyer_id,
            BuyerDateOfBirth=buyer_date_of_birth,
            BuyerIndividualForeignID=buyer_individual_foreign_id,
            BuyerCorporateForeignID=buyer_corporate_foreign_id,
            BuyerName=buyer_name,
            RealEstateAgencyID=real_estate_agency_id,
            RealEstateAgencyName=real_estate_agency_name,
            AgencyContactPersonName=agency_contact_person_name,
            RealEstateAgencyPhone=real_estate_agency_phone,
            RealEstateAgencyAddress=real_estate_agency_address,
            RealEstateAgencyPostCode=real_estate_agency_post_code,
            StockTraderID=stock_trader_id,
            StockTraderName=stock_trader_name,
            AdvanceRuling=advance_ruling,
        )


class SendReturnResponse200(BaseSchema):
    unique_identifier: str = Field(
        alias="UniqueIdentifier",
        description="Unique key value that identifies the accepted transfer tax return in Verohallinto",
    )
    accepted_timestamp: datetime = Field(
        alias="AcceptedTimestamp",
        description="Date and time that transfer tax return filing is accepted by Verohallinto",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls, unique_identifier: str, accepted_timestamp: str
    ) -> "SendReturnResponse200":
        """Create a SendReturnResponse200 instance with more Pythonic snake_case parameters."""
        return cls(
            UniqueIdentifier=unique_identifier,
            AcceptedTimestamp=accepted_timestamp,
        )


class ErrorDetail(BaseSchema):
    error_code: str = Field(
        alias="ErrorCode", description="Error Code for Business Rule"
    )
    error_reference: Optional[str] = Field(
        None, alias="ErrorReference", description="Reference to Parameter in Error"
    )
    error_description_fi: Optional[str] = Field(
        None,
        alias="ErrorDescriptionFI",
        description="Description of Business Rule in Error (FI)",
    )
    error_description_se: Optional[str] = Field(
        None,
        alias="ErrorDescriptionSE",
        description="Description of Business Rule in Error (SE)",
    )
    error_description_en: Optional[str] = Field(
        None,
        alias="ErrorDescriptionEN",
        description="Description of Business Rule in Error (EN)",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        error_code: str,
        error_reference: Optional[str] = None,
        error_description_fi: Optional[str] = None,
        error_description_se: Optional[str] = None,
        error_description_en: Optional[str] = None,
    ) -> "ErrorDetail":
        """Create an ErrorDetail instance with more Pythonic snake_case parameters."""
        return cls(
            ErrorCode=error_code,
            ErrorReference=error_reference,
            ErrorDescriptionFI=error_description_fi,
            ErrorDescriptionSE=error_description_se,
            ErrorDescriptionEN=error_description_en,
        )


class SendReturnResponse400(BaseSchema):
    error_code: Optional[str] = Field(
        None, alias="ErrorCode", description="Error Code for Processing Request Data"
    )
    error_text: str = Field(
        alias="ErrorText", description="Error Message for Processing Request Data"
    )
    error_details: Optional[List[ErrorDetail]] = Field(
        None,
        alias="ErrorDetails",
        description="Expanded Details for Business Error Rules",
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls,
        error_text: str,
        error_code: Optional[str] = None,
        error_details: Optional[List[ErrorDetail]] = None,
    ) -> "SendReturnResponse400":
        """Create a SendReturnResponse400 instance with more Pythonic snake_case parameters."""
        return cls(
            ErrorText=error_text,
            ErrorCode=error_code,
            ErrorDetails=error_details,
        )


class SendReturnResponse500(BaseSchema):
    error_code: Optional[str] = Field(
        None, alias="ErrorCode", description="Error Code for Processing Request Data"
    )
    error_text: str = Field(
        alias="ErrorText", description="Error Message for Processing Request Data"
    )

    model_config = {"populate_by_name": True, "use_enum_values": True}

    @classmethod
    def create(
        cls, error_text: str, error_code: Optional[str] = None
    ) -> "SendReturnResponse500":
        """Create a SendReturnResponse500 instance with more Pythonic snake_case parameters."""
        return cls(ErrorText=error_text, ErrorCode=error_code)


# Union type for all possible responses
SendReturnResponse = Union[
    SendReturnResponse200, SendReturnResponse400, SendReturnResponse500
]
