from enum import Enum
from typing import Optional

from strandproperties.schemas.base import BaseSchema


class MessageResponseSchema(BaseSchema):
    message: str


class <PERSON>rror<PERSON>abel(str, Enum):
    CREATE_DOCUMENT_SIGNING_USER_MISSING_NAME = (
        "create-document-signing-user-missing-name"
    )
    CREATE_DOCUMENT_SIGNING_CONTACT_MISSING_NAME = (
        "create-document-signing-contact-missing-name"
    )
    CREATE_DOCUMENT_SIGNING_AT_LEAST_ONE_SIGNER_REQUIRED = (
        "create-document-signing-at-least-one-signer-required"
    )
    CREATE_DOCUMENT_SIGNING_SSN_INVALID_OR_MISSING = (
        "create-document-signing-ssn-invalid-or-missing"
    )
    ADD_SIGNER_DOCUMENT_SIGNING_NOT_FOUND = "add-signer-document-signing-not-found"
    ADD_SIGNER_DOCUMENT_SIGNING_NOT_ALLOWED_STATUS = (
        "add-signer-document-signing-not-allowed-status"
    )
    ADD_SIGNER_SIGNER_ALREADY_ADDED = "add-signer-signer-already-added"
    DELETE_DOCUMENT_SIGNER_NOT_ALLOWED_STATUS = (
        "delete-document-signer-not-allowed-status"
    )
    DELETE_DOCUMENT_SIGNER_NOT_FOUND = "delete-document-signer-not-found"
    CREATE_DOCUMENT_SIGNING_ONLY_PDF_FILES_SUPPORTED = (
        "create-document-signing-only-pdf-files-supported"
    )
