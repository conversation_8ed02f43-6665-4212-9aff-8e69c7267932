from datetime import datetime
from typing import List, Optional

from pydantic import Field
from strandproperties.constants import EventFormat, EventType
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactListRead
from strandproperties.schemas.property import PropertyListRead
from strandproperties.schemas.user import UserBasicInfo
from strandproperties.schemas.lead import LeadListRead


class EventListParams(BaseSchema):
    limit: int = Field(
        10,
        description="Number of events to return",
        ge=1,
    )
    after_event_id: Optional[int] = Field(
        None,
        description="Return events after this id",
    )
    event_types: Optional[str] = None  # Comma-separated values
    event_formats: Optional[str] = None  # Comma-separated values
    is_cancelled: Optional[bool] = None
    user_ids: Optional[str] = None  # Comma-separated values
    property_ids: Optional[str] = None  # Comma-separated values
    contact_ids: Optional[str] = None  # Comma-separated values
    min_start_time: Optional[datetime] = None
    max_start_time: Optional[datetime] = None


class EventListRead(BaseSchema):
    class EventListReadContact(BaseSchema):
        id: int
        name: str
        email: Optional[str]
        company: Optional[str]
        website: Optional[str]
        address: Optional[str]
        city: Optional[str]
        post_code: Optional[str]
        country: Optional[str]
        preferred_language: Optional[str]
        passport_number: Optional[str]
        nationality: Optional[str]
        country_specific: Optional[dict] = {}
        notes: Optional[str]

    id: int
    created_at: datetime
    updated_at: datetime
    event_type: EventType
    event_format: EventFormat
    title: Optional[str]
    descriptions: Optional[dict]
    internal_notes: Optional[str]
    start_time: datetime
    end_time: datetime
    location: Optional[str]
    url: Optional[str]
    additional_details: Optional[dict]
    is_cancelled: bool
    contacts: List[EventListReadContact]
    properties: List[PropertyListRead]
    users: List[UserBasicInfo]
    leads: List[LeadListRead]


class EventRead(BaseSchema):
    id: int
    created_at: datetime
    updated_at: datetime
    event_type: EventType
    event_format: EventFormat
    title: Optional[str]
    descriptions: Optional[dict]
    internal_notes: Optional[str]
    start_time: datetime
    end_time: datetime
    location: Optional[str]
    url: Optional[str]
    additional_details: Optional[dict]
    is_cancelled: bool
    contacts: List[ContactListRead]
    properties: List[PropertyListRead]
    users: List[UserBasicInfo]
    leads: List[LeadListRead]


class EventCreateEdit(BaseSchema):
    event_type: EventType
    event_format: EventFormat
    title: Optional[str] = None
    descriptions: Optional[dict] = None
    internal_notes: Optional[str] = None
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    url: Optional[str] = None
    additional_details: Optional[dict] = None
    is_cancelled: Optional[bool] = False
    contact_ids: Optional[List[int]] = Field(
        [],
        description="List of contact ids to associate with the event",
    )
    property_ids: List[int] = Field(
        ...,
        description="List of property ids to associate with the event",
        min_items=1,
    )
    user_ids: List[int] = Field(
        ...,
        description="List of user ids to associate with the event",
        min_items=1,
    )
    lead_ids: Optional[List[int]] = Field(
        [],
        description="List of lead ids to associate with the event",
    )


class EventDelete(BaseSchema):
    is_cancelled: bool = True
