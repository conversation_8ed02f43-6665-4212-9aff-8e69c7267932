from datetime import datetime
from enum import Enum
from typing import List, Literal, Optional

from pydantic import EmailStr, Field, validator

from strandproperties.constants import (
    DOSTransactionOrderBy,
    EventLogAction,
    Language,
    OrderBy,
    SoldBy,
    SortEnum,
)
from strandproperties.schemas.base import BaseSchema


class LoginParam(BaseSchema):
    email: EmailStr
    password: str


class SwitchOrganizationParam(BaseSchema):
    organization_id: int


class PaginationParam(BaseSchema):
    page: int = 1
    page_size: int = 10

    @validator("page")
    def validate_page(cls, v):
        assert v > 0
        return v

    @validator("page_size")
    def validate_page_size(cls, v):
        assert 0 < v <= 100, "Max page_size is 100"
        return v


# TODO Remove when dropdowns issue is resolved
class TempPaginationParam(BaseSchema):
    page: int = 1
    page_size: int = 10

    @validator("page")
    def validate_page(cls, v):
        assert v > 0
        return v

    @validator("page_size")
    def validate_page_size(cls, v):
        assert 0 < v <= 300, "Max page_size is 300"
        return v


class PropertySortColumn(str, Enum):
    DATA_SOURCE = "data_source"
    PRICE_SALE = "price_sale"
    STATUS = "status"


class EventLogParam(PaginationParam):
    language: Optional[Language] = None
    actions: Optional[List[EventLogAction]] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    portals: Optional[List[str]] = None

    @validator("actions", pre=True)
    def validate_actions(cls, v):
        if not v:
            return None
        if isinstance(v, list):
            return v
        if isinstance(v, str):
            return [v]
        return None

    @validator("portals", pre=True)
    def validate_portals(cls, v):
        if not v:
            return None
        if isinstance(v, list):
            return v
        if isinstance(v, str):
            return [v]
        return None


class PropertyFilterParam(PaginationParam):
    order_by: OrderBy = OrderBy.LATEST_STRAND
    data_source: Optional[Literal["strand", "network"]] = None
    price_min: Optional[int] = None
    price_max: Optional[int] = None
    size_min: Optional[int] = None
    size_max: Optional[int] = None
    keyword: Optional[str] = None
    types: Optional[list[int]] = []
    min_bedrooms: Optional[int] = None
    min_bathrooms: Optional[int] = None
    settings: Optional[list[int]] = []
    orientations: Optional[list[int]] = []
    views: Optional[list[int]] = []
    features: Optional[list[int]] = []
    conditions: Optional[str] = None
    has_pool: Optional[bool] = None
    has_air_conditioning: Optional[bool] = None
    assigned_to: Optional[list[int]] = []
    areas: Optional[list[str]] = []
    listing_types: Optional[list[int]] = []
    garage_types: Optional[list[int]] = []
    garden_types: Optional[list[int]] = []
    pool_types: Optional[list[int]] = []
    status: Optional[list[str]] = None
    is_strandified: Optional[bool] = None
    from_created_at: Optional[str] = None
    until_created_at: Optional[str] = None
    from_updated_at: Optional[str] = None
    until_updated_at: Optional[str] = None
    is_exclusive: Optional[bool] = None
    portals: Optional[List[str]] = []
    sold_by: Optional[SoldBy] = None
    tags: Optional[List[int]] = None
    offices: Optional[List[int]] = None
    sellers: Optional[List[int]] = None
    match_making_id: Optional[int] = None
    is_disliked: Optional[bool] = None
    is_shortlist: Optional[bool] = None
    is_match_making_created: Optional[bool] = None
    sort_column: Optional[PropertySortColumn] = None
    sort_direction: Optional[SortEnum] = None
    has_email_id: Optional[bool] = None

    @validator("sort_column", "sort_direction", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class MatchMakingFilterParam(PaginationParam):
    keyword: Optional[str] = None
    assigned_users: Optional[list[int]] = []


class ExternalFilterParam(PaginationParam):
    agent_id: Optional[int] = None
    order_by: OrderBy = OrderBy.LATEST


class StrandWebsiteParam(PaginationParam):
    keyword: Optional[str] = None
    types: Optional[list[int]] = []
    bedrooms: Optional[list[int]] = []
    bathrooms: Optional[list[int]] = []
    areas: Optional[list[str]] = []
    area_groups: list[int] = []
    price_min: Optional[int] = None
    price_max: Optional[int] = None
    size_min: Optional[int] = None
    size_max: Optional[int] = None
    settings: Optional[list[int]] = []
    orientations: Optional[list[int]] = []
    conditions: Optional[str] = None
    has_pool: Optional[bool] = None
    is_strandified: Optional[bool] = None
    has_climate_control: Optional[bool] = None
    views: Optional[list[int]] = []
    features: Optional[list[int]] = []
    order_by: OrderBy = OrderBy.LATEST
    listing_types: Optional[list[int]] = []
    assigned_to: Optional[list[int]] = []
    garage_types: Optional[list[int]] = []
    garden_types: Optional[list[int]] = []
    pool_types: Optional[list[int]] = []
    status: Optional[list[str]] = None
    is_exclusive: Optional[bool] = None
    sold_by: Optional[SoldBy] = None
    agent_id: Optional[int] = None


class SowiseWebhookPayload(BaseSchema):
    email: str
    apiKey: str


class ContactReadParam(BaseSchema):
    id: int = Field(..., json_schema_extra={"param_in": "path"})


class OfferListParam(BaseSchema):
    reference_code: str = Field(..., json_schema_extra={"param_in": "path"})


class DetailOfSaleListParam(PaginationParam):
    reference_code: Optional[str] = None
    status: Optional[list[str]] = None
    created_by: Optional[list[int]] = None


class ListTransactionParams(PaginationParam):
    from_time: Optional[str] = None
    to_time: Optional[str] = None
    offices: Optional[List[int]] = None
    agents: Optional[List[int]] = None
    order_by: Optional[DOSTransactionOrderBy] = None
    sort: Optional[SortEnum] = SortEnum.ASC
    sort_column: Optional[DOSTransactionOrderBy] = None
    sort_direction: Optional[SortEnum] = None


class ReportKpiParams(BaseSchema):
    from_time: Optional[str] = None
    to_time: Optional[str] = None
    offices: Optional[List[int]] = None
    agents: Optional[List[int]] = None
    by_month: Optional[bool] = False
    compare: Optional[bool] = False


class IdParam(BaseSchema):
    id: int = Field(..., json_schema_extra={"param_in": "path"})


class PropertyIdParam(BaseSchema):
    property_id: int = Field(..., json_schema_extra={"param_in": "path"})


class ValidateParam(BaseSchema):
    should_validate: Optional[bool] = False
