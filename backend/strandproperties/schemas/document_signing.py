from datetime import datetime
from enum import Enum
from typing import List, Optional
from pydantic import Field


from strandproperties.constants import (
    DocumentEventAction,
    DocumentEventStatus,
    DocumentSignatureStatus,
    DocumentSignatureUserType,
    DocumentSigningEntityType,
    DocumentStatus,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactListRead
from strandproperties.schemas.user import UserListRead


class DocumentSigner(BaseSchema):
    id: int
    document_signing_id: int
    user_id: int
    user_type: DocumentSignatureUserType
    signing_external_id: Optional[str] = None
    status: DocumentSignatureStatus
    user: Optional[UserListRead] = None
    contact: Optional[ContactListRead] = None


class DocumentEvent(BaseSchema):
    id: int
    document_signing_id: int
    action: DocumentEventAction
    status: DocumentEventStatus
    raw_payload: dict


class DocumentSigningEntity(BaseSchema):
    id: int
    document_signing_id: int
    entity_type: DocumentSigningEntityType
    entity_id: int


class _DocumentSigningBase(BaseSchema):
    entities: List[DocumentSigningEntity]
    document_external_id: str
    signing_url: Optional[str] = None
    status: DocumentStatus
    deadline: Optional[datetime] = None
    created_at: Optional[datetime] = None


class DocumentSigningRead(_DocumentSigningBase):
    id: int
    signers: List[DocumentSigner]
    events: List[DocumentEvent]


class SignerRole(str, Enum):
    SIGNER = "signer"
    VIEWER = "viewer"


class Signer(BaseSchema):
    id: int
    type: str
    role: SignerRole


class DocumentSigningEntityCreate(BaseSchema):
    entity_type: DocumentSigningEntityType
    entity_id: int


class CreateDocumentSigning(BaseSchema):
    entities: List[DocumentSigningEntityCreate]
    signers: List[Signer]
    comment: Optional[str] = None
    last_signing_date: str
    language: Optional[str] = "fi"
    require_ssn: bool = False


class DocumentSigningAddSigner(BaseSchema):
    signer: Signer
    language: Optional[str] = "fi"


class DocumentSigningDeleteSignerParam(BaseSchema):
    id: int = Field(..., json_schema_extra={"param_in": "path"})
    signer_id: int = Field(..., json_schema_extra={"param_in": "path"})
