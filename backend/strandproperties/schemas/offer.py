from datetime import date, datetime
from typing import Literal, Optional

from pydantic import <PERSON><PERSON><PERSON><PERSON>, Field, field_serializer

from strandproperties.constants import Language, OfferDepositePaymentPaidTo
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactEdit
from strandproperties.schemas.details_of_sale import DetailsOfSaleListRead
from strandproperties.schemas.user import UserBasicInfo


class BuyerCreateOrUpdateInOffer(ContactEdit):

    id: int


class SellerInOffer(BaseSchema):

    id: int


class OfferBasicData(BaseSchema):
    sale_price: int
    special_condition: Optional[str] = None
    deposit_amount: int
    deposit_payee: Optional[str] = None
    deposit_payment_paid_to: Literal[
        OfferDepositePaymentPaidTo.STRAND_PROPERTIES_SL,
        OfferDepositePaymentPaidTo.OTHER,
    ]
    price_settled_by: date
    private_purchase_contract_due_date: Optional[date] = None


class OfferCreate(OfferBasicData):
    reference_number: Optional[str] = None
    property_reference: str
    language: Literal[
        Language.ENGLISH.value, Language.FINNISH.value, Language.SPANISH.value
    ]
    buyers: list[int]
    sellers: list[int]
    company_reference: Optional[str] = None
    roaiib: Optional[str] = None
    liability_insurance: Optional[str] = None


class BuyerBasicInfo(BaseSchema):
    name: str


class BuyerSellerBasicInfo(BuyerBasicInfo):
    id: Optional[int] = None
    name: str


class OfferList(BaseSchema):
    id: int
    property_id: int
    status: str = Field(
        validation_alias=AliasPath("document", "status"),
    )
    updated_at: datetime
    created_user_info: Optional[UserBasicInfo] = Field(
        None,
        alias="createdUser",
        validation_alias=AliasPath("created_user"),
    )
    deposit_amount: int
    sale_price: int
    realtor_users: list[UserBasicInfo] = Field(
        alias="realtorUsers",
        validation_alias=AliasPath("property", "realtor_users"),
    )
    buyers: list[BuyerSellerBasicInfo]
    sellers: list[BuyerSellerBasicInfo]
    document_id: str = Field(
        validation_alias=AliasPath("document", "sowise_id"),
    )
    special_condition: Optional[str] = None
    details_of_sale: Optional[DetailsOfSaleListRead] = Field(
        alias="detailsOfSale",
        validation_alias=AliasPath("property", "details_of_sale"),
    )

    @field_serializer("updated_at")
    def serialize_updated_at(updated_at: datetime) -> str:
        return updated_at.strftime("%d %b, %Y")


class OfferDocumentParam(BaseSchema):
    reference_code: str


class OfferDocumentRead(BaseSchema):
    name: str
    content: str
    document_id: str
    status: str
    datetime: datetime
