from datetime import datetime
from typing import List, Optional

from strandproperties.constants import FIPurchaseOfferStatusEnum
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.contact import ContactRead
from strandproperties.schemas.document_signing import DocumentSigningRead


class FICounterOfferBase(BaseSchema):
    unencumbered_price: float
    valid_until: datetime
    additional_details: Optional[str] = None
    status: Optional[FIPurchaseOfferStatusEnum] = None


class FICounterOfferCreate(FICounterOfferBase):
    purchase_offer_id: int
    created_by: int
    offeror_ids: List[int]
    offeree_ids: List[int]
    previous_counter_offer_id: Optional[int] = None


class FICounterOfferEdit(FICounterOfferBase):
    pass


class FICounterOfferRead(FICounterOfferBase):
    id: int
    created_at: datetime
    purchase_offer_id: int
    offerees: List["ContactRead"]
    offerors: List["ContactRead"]
    previous_counter_offer: Optional["FICounterOfferRead"] = None
    signings: Optional[List["DocumentSigningRead"]] = None
