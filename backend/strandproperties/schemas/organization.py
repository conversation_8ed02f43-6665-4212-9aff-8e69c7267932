from typing import List, Optional

from strandproperties.constants import CountryC<PERSON>, Currency, Language
from strandproperties.schemas.base import BaseSchema


class AdTemplateRead(BaseSchema):
    id: int
    image_url: str
    smartly_id: str


class AdTemplateCreateEdit(BaseSchema):
    image_url: str
    smartly_id: str


class OrganizationRead(BaseSchema):
    id: int
    name: str
    currency: Currency
    language: Language
    country_code: CountryCode
    stripe_account_id: Optional[str]
    ad_templates: Optional[List[AdTemplateRead]] = None


class AdTemplateReadList(BaseSchema):
    templates: List[AdTemplateRead]
