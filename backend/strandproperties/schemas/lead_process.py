from typing import Optional

from strandproperties.schemas.base import BaseSchema


class LeadProcessCreate(BaseSchema):
    id: str
    email: Optional[str]
    name: Optional[str] = None
    phone: Optional[str] = None
    content: Optional[str] = None
    source_name: str
    reference_code: Optional[str] = None
    campaign_name: Optional[str] = None
    realtor_email: Optional[str] = None
    organization: Optional[str] = None
