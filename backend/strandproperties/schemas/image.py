from enum import Enum
from typing import Annotated, Optional

from pydantic import AfterValidator, AnyHttpUrl, HttpUrl

from strandproperties.schemas.base import BaseSchema

HttpUrlString = Annotated[AnyHttpUrl, AfterValidator(lambda v: str(v))]


class ImageBase(BaseSchema):
    url: HttpUrlString
    is_hidden: Optional[bool] = None


class ImageRead(ImageBase):
    id: int


class ImageCreate(ImageBase):
    pass


class ImageUpdateActionType(str, Enum):
    REORDER = "reorder"
    CHANGE_VISIBILITY = "change_visibility"


class ImageUpdate(BaseSchema):
    id: int
    is_hidden: Optional[bool] = None


class ImageUpdateBody(BaseSchema):
    action: ImageUpdateActionType
    images: list[ImageUpdate]


class ImagePresignedUrlRead(BaseSchema):
    original_filename: str
    url: str
