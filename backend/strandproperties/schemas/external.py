from datetime import datetime
from typing import List, Optional

from pydantic import Field

from strandproperties.constants import (
    DEFAULT_CURRENCY,
    EnergyCertificateRating,
    IVATaxEnum,
    SoldBy,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.image import ImageRead
from strandproperties.schemas.mapping import (
    FeatureRead,
    GarageTypeRead,
    GardenTypeRead,
    ListingTypeRead,
    OrientationRead,
    PoolTypeRead,
    SettingRead,
    ViewRead,
)
from strandproperties.schemas.property import (
    DamagesAndDefects,
    KeysAndHandoff,
    OtherDamages,
    PropertyDescription,
    Renovations,
    TelecommunicationSystems,
)
from strandproperties.schemas.tag import TagRead
from strandproperties.schemas.user import UserDetails


class ExternalRealtorRead(BaseSchema):
    first_name: str
    last_name: str
    email: str
    phone_number: Optional[str] = None
    photo_url: Optional[str] = None
    details: UserDetails
    offices: List[TagRead]


class ExternalVideoRead(BaseSchema):
    id: int
    url: str


class ExternalListRead(BaseSchema):
    reference: str
    location_from_areas: Optional[str] = Field(
        None,
        serialization_alias="location",
    )

    property_type: Optional[str] = None
    property_type_origin: Optional[str] = None
    property_type_category: Optional[str] = None

    price_sale_old: Optional[int] = None
    price_rent_short_term_old: Optional[int] = None  # per month
    price_rent_long_term_old: Optional[int] = None  # per month
    price_sale: Optional[int] = None
    price_rent_short_term: Optional[int] = None
    price_rent_long_term: Optional[int] = None

    listing_types: list[ListingTypeRead] = []

    price_square_meter: Optional[float] = None
    currency: str = DEFAULT_CURRENCY
    plot_area: Optional[int] = None
    built_area: Optional[int] = None
    bathrooms: Optional[int] = None
    bedrooms: Optional[int] = None
    descriptions: Optional[list[PropertyDescription]] = []
    title: Optional[str] = None
    slug: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_public_coordinates: Optional[bool] = None
    main_img: Optional[str] = None
    status: str
    created_at: datetime = Field(..., alias="createdAt")
    updated_at: datetime = Field(..., alias="updatedAt")


class ExternalRead(BaseSchema):
    # Property Base
    reference: str
    area_level_1_id: Optional[int] = Field(default=None, alias="areaLevel1Id")
    area_level_2_id: Optional[int] = Field(default=None, alias="areaLevel2Id")
    area_level_3_id: Optional[int] = Field(default=None, alias="areaLevel3Id")
    area_level_4_id: Optional[int] = Field(default=None, alias="areaLevel4Id")
    area_level_5_id: Optional[int] = Field(default=None, alias="areaLevel5Id")

    # Property Detail
    title: Optional[str] = None
    bathrooms: Optional[int] = None
    bedrooms: Optional[int] = None
    location_from_areas: Optional[str] = Field(
        None,
        serialization_alias="location",
    )

    pax: Optional[int] = None
    price_sale_old: Optional[int] = None
    price_rent_short_term_old: Optional[int] = None  # per month
    price_rent_long_term_old: Optional[int] = None  # per month
    price_sale: Optional[int] = None
    price_rent_short_term: Optional[int] = None
    price_rent_long_term: Optional[int] = None
    price_square_meter: Optional[float] = None
    currency: Optional[str] = DEFAULT_CURRENCY
    built_area: Optional[int] = None
    built_year: Optional[int] = None
    interior_area: Optional[int] = None
    plot_area: Optional[int] = None
    terrace_area: Optional[int] = None
    levels: Optional[int] = None
    floor: Optional[int] = None
    iva_tax: Optional[IVATaxEnum] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_public_coordinates: Optional[bool] = None
    main_img: Optional[str] = None

    # PropertyReadWithoutContacts
    id: int
    slug: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    status: str
    property_type: Optional[str] = None
    property_type_origin: Optional[str] = None
    property_type_category: Optional[str] = None
    listing_types: list[ListingTypeRead] = []
    orientations: Optional[list[OrientationRead]] = []
    features: Optional[list[FeatureRead]] = []
    settings: Optional[list[SettingRead]] = []
    views: Optional[list[ViewRead]] = []
    # documents: Optional[list[DocumentListRead]] = []  # ❌ removed
    # files: Optional[list[FileRead]] = []  # ❌ removed
    visible_video_streams: Optional[list[ExternalVideoRead]] = Field(
        [], alias="videoStreams"
    )
    visible_video_tours: Optional[list[ExternalVideoRead]] = Field(
        [], alias="videoTours"
    )
    garage_types: list[GarageTypeRead] = []
    pool_types: list[PoolTypeRead] = []
    garden_types: list[GardenTypeRead] = []
    descriptions: Optional[list[PropertyDescription]] = []
    condition: Optional[str] = None
    realtor_users: Optional[list[ExternalRealtorRead]] = []
    # legacy_data: PropertyLegacyData  # ❌ removed
    # private_info: Optional[dict] = None # ❌ removed
    legal_representative: Optional[dict] = None
    # portals: Portals  # ❌ removed
    # property_type_id: int  # ❌ removed
    country: Optional[str] = None
    city: Optional[str] = None
    communal_fees: Optional[float] = None
    ibi: Optional[float] = None
    garbage_tax: Optional[float] = None
    water_fee: Optional[float] = None
    electricity: Optional[float] = None
    cadastral_reference: Optional[str] = None
    building_constructor: Optional[str] = None
    total_floors: Optional[int] = None
    building_has_elevator: Optional[bool] = False
    foundation_and_structure: Optional[str] = None
    roof: Optional[str] = None
    exterior_walls: Optional[str] = None
    property_has_certificate: Optional[bool] = False
    certificate_consumption_rating: Optional[EnergyCertificateRating] = None
    certificate_consumption_value: Optional[float] = None
    certificate_emission_rating: Optional[EnergyCertificateRating] = None
    certificate_emission_value: Optional[float] = None
    rooms_total: Optional[int] = None
    toilets: Optional[int] = None
    suite_baths: Optional[int] = None
    parking_spaces: Optional[int] = None
    telecommunication_systems: Optional[TelecommunicationSystems] = None
    keys_and_handoff: Optional[KeysAndHandoff] = None
    renovations: Optional[Renovations] = None
    damages_and_defects: Optional[DamagesAndDefects] = None
    other_damages: Optional[OtherDamages] = None
    sorted_visible_images: Optional[list[ImageRead]] = Field(
        serialization_alias="images",
    )
    sold_by: Optional[SoldBy] = None
    total_floors: Optional[int] = None
    building_has_elevator: Optional[bool] = False
    foundation_and_structure: Optional[str] = None
    roof: Optional[str] = None
    exterior_walls: Optional[str] = None
    property_has_certificate: Optional[bool] = False
    certificate_consumption_rating: Optional[EnergyCertificateRating] = None
    certificate_consumption_value: Optional[float] = None
    certificate_emission_rating: Optional[EnergyCertificateRating] = None
    certificate_emission_value: Optional[float] = None
    rooms_total: Optional[int] = None
    toilets: Optional[int] = None
    suite_baths: Optional[int] = None
    parking_spaces: Optional[int] = None
    telecommunication_systems: Optional[TelecommunicationSystems] = None
    keys_and_handoff: Optional[KeysAndHandoff] = None
    renovations: Optional[Renovations] = None
    damages_and_defects: Optional[DamagesAndDefects] = None
    other_damages: Optional[OtherDamages] = None
    sorted_visible_images: Optional[list[ImageRead]] = Field(
        serialization_alias="images",
    )
    sold_by: Optional[SoldBy] = None
