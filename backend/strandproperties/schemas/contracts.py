from datetime import datetime
from enum import Enum
from typing import List, Literal, Optional

from pydantic import validator

from strandproperties.constants import ContractTypes, SortEnum
from strandproperties.schemas.document import DocumentBasicInfo
from strandproperties.schemas.office import OfficeRead
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.property import PropertyBase
from strandproperties.schemas.user import UserBasicInfo


class ContractSortColumn(str, Enum):
    STATUS = "status"
    UPDATED_AT = "updated_at"
    OFFICE = "office"


class ContractsListParam(PaginationParam):
    reference_code: Optional[str] = None
    status: Optional[List[str]] = None
    contract_types: Optional[
        List[
            Literal[
                ContractTypes.OFFER,
                ContractTypes.SALE_AGREEMENT,
                ContractTypes.DETAILS_OF_SALE,
            ]
        ]
    ] = None
    office_ids: Optional[List[int]] = None
    created_by: Optional[List[int]] = None
    sort_column: Optional[ContractSortColumn] = None
    sort_direction: Optional[SortEnum] = None

    @validator("sort_column", "sort_direction", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v


class ContractsListData(DocumentBasicInfo):
    id: Optional[int] = None
    contract_type: Optional[str] = None
    property: Optional[PropertyBase] = None
    any_offer_signed: Optional[bool] = None
    any_dos_created: Optional[bool] = None
    custom_reference_property: Optional[str] = None
    office: Optional[OfficeRead] = None
    created_user: Optional[UserBasicInfo] = None
    updated_at: Optional[datetime] = None
