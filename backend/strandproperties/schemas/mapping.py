from typing import Literal, Optional

from pydantic import Field

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.param import PaginationParam


class FeatureRead(BaseSchema):
    id: int
    features_name: str = Field(..., alias="name")
    feature_group_name: str


class FeatureGroupRead(BaseSchema):
    id: int
    name: str
    features: list[FeatureRead]


class CityRead(BaseSchema):
    id: int
    city: str = Field(..., alias="name")


class SubAreaRead(BaseSchema):
    id: int
    city: str = Field(..., alias="name")


class ConditionRead(BaseSchema):
    id: int
    condition_name: str = Field(..., alias="name")


class SettingRead(BaseSchema):
    id: int
    settings_name: str = Field(..., alias="name")


class ViewRead(BaseSchema):
    id: int
    views_name: str = Field(..., alias="name")


class OrientationRead(BaseSchema):
    id: int
    orientation_name: str = Field(..., alias="name")


class ListingTypeRead(BaseSchema):
    id: int
    listing_type: str = Field(..., alias="name")


class PropertyTypeRead(BaseSchema):
    id: int
    origin: str
    category: str
    property_type: str = Field(..., alias="name")


class SubAreaNestedRead(BaseSchema):
    id: int
    name: str
    level: Literal[1, 2, 3, 4, 5]


class AreaNestedRead(SubAreaNestedRead):
    subareas: list["AreaNestedRead"] | None = None


class AreaNestedFilterParam(PaginationParam):
    keyword: Optional[str] = None
    hide_areas_without_properties: Optional[bool] = Field(
        None, alias="hideAreasWithoutProperties"
    )


class PublicAreaFilterParam(BaseSchema):
    hide_areas_without_properties: Optional[bool] = Field(
        None, alias="hideAreasWithoutProperties"
    )


class GarageTypeRead(BaseSchema):
    id: int
    garage_type: str = Field(..., alias="name")


class PoolTypeRead(BaseSchema):
    id: int
    pool_type: str = Field(..., alias="name")


class GardenTypeRead(BaseSchema):
    id: int
    garden_type: str = Field(..., alias="name")


class FIPropertyTypeRead(BaseSchema):
    id: int
    listing_type: str
    ownership_type: str
    property_type_group: str
    property_type: str


class BankRead(BaseSchema):
    id: int
    name: str
    business_id: Optional[str]
    group_name: Optional[str]
    group_business_id: Optional[str]
