from datetime import datetime, timezone
from enum import StrEnum
from typing import Any, Dict, List, Optional

from pydantic import Field, computed_field, field_validator, model_validator

from strandproperties.logger import logger
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.event import EventRead
from strandproperties.schemas.fi_property.fi_property import FIPropertyRead
from strandproperties.schemas.organization import OrganizationRead
from strandproperties.schemas.property import PropertyReadExtend
from strandproperties.schemas.user import UserRead


class AdStatus(StrEnum):
    DRAFT = "draft"
    IN_REVIEW = "in_review"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class AdType(StrEnum):
    LISTING_PROPERTY = "listing_property"
    PROPERTY_SOLD = "property_sold"
    AGENT = "agent"
    EVENT = "event"
    CUSTOM = "custom"


class AdLanguage(StrEnum):
    ENGLISH = "en"
    SPANISH = "es"
    FINNISH = "fi"


class AdvertisementImageBase(BaseSchema):
    url: str
    order: Optional[int] = None
    is_hidden: bool


class AdvertisementImageRead(AdvertisementImageBase):
    id: int


class AdvertisementPreviewSettingsBase(BaseSchema):
    display_metrics: bool = False
    display_details: bool = False


class AdvertisementPreviewSettingsRead(AdvertisementPreviewSettingsBase):
    advertisement_id: int


class AdvertisementPreviewSettingsUpdate(BaseSchema):
    display_metrics: Optional[bool] = None
    display_details: Optional[bool] = None


class AdvertisementBase(BaseSchema):
    title: str = Field(..., min_length=3, max_length=255)
    primary_text: Optional[str] = None
    description: Optional[str] = None
    budget_total: float = Field(..., ge=0.0)
    budget_daily: Optional[float] = Field(None, ge=0.0)
    status: AdStatus = AdStatus.DRAFT
    type: Optional[AdType] = Field(default=AdType.CUSTOM)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    target_area_radius_km: float = 50.0
    conversion_location_url: Optional[str] = None
    entry_point: Optional[str] = None
    country: Optional[str] = None
    municipality: Optional[str] = None
    property_id: Optional[int] = None
    fi_property_id: Optional[int] = None
    event_id: Optional[int] = None
    agent_id: Optional[int] = None
    advertisement_images: List[AdvertisementImageBase] = Field(default_factory=list)
    language: Optional[str] = AdLanguage.ENGLISH
    ad_template_id: Optional[int] = None


class AdvertisementCreate(AdvertisementBase):
    owner_id: int
    organization_id: int


class AdvertisementUpdate(AdvertisementBase):
    title: Optional[str] = Field(None, min_length=3, max_length=255)
    description: Optional[str] = None
    budget_total: Optional[float] = Field(None, ge=0.0)
    budget_daily: Optional[float] = Field(None, ge=0.0)
    status: Optional[AdStatus] = None
    type: Optional[AdType] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class AdvertisementRead(AdvertisementBase):
    id: int
    owner_id: int
    status_reason: Optional[str] = None
    metrics: Optional[Dict[Any, Any]] = None
    ad_content: Optional[Dict[Any, Any]] = None
    preview_url: Optional[str] = None
    is_smartly_ad: bool
    created_at: datetime
    updated_at: datetime
    link_clicks: Optional[int] = Field(0, alias="linkClicks")
    impressions: int = Field(0, alias="impressions")
    ctr: Optional[float] = Field(
        0.0,
        alias="ctr",
        description="Click-through rate – percentage of clicks per impression",
    )
    cpc: Optional[float] = Field(
        0.0, alias="cpc", description="Cost per click, stored in cents"
    )
    cpm: Optional[float] = Field(
        0.0,
        alias="cpm",
        description="Cost per mille – cost per thousand impressions, stored in cents",
    )
    preview_settings: Optional[AdvertisementPreviewSettingsRead] = None
    owner: UserRead
    organization: Optional[OrganizationRead] = None
    fi_property: Optional[FIPropertyRead] = None
    agent: Optional[UserRead] = None
    event: Optional[EventRead] = None
    property: Optional[PropertyReadExtend] = None
    budget_total: float = Field(alias="budgetTotal")

    @computed_field
    def publishable(self) -> bool:
        """Determine if the advertisement is publishable."""

        if self.status != AdStatus.DRAFT:
            return False

        required_fields = [
            self.title,
            self.type,
            self.language,
            self.country,
            self.municipality,
            self.budget_total,
            self.start_date,
            self.end_date,
            self.primary_text,
            self.description,
            self.status,
            self.target_area_radius_km,
        ]
        if not all(field is not None for field in required_fields):
            return False

        now = datetime.now(timezone.utc)

        # Ensure start_date has timezone info
        start_date = (
            self.start_date.replace(tzinfo=timezone.utc)
            if self.start_date and self.start_date.tzinfo is None
            else self.start_date
        )
        end_date = (
            self.end_date.replace(tzinfo=timezone.utc)
            if self.end_date and self.end_date.tzinfo is None
            else self.end_date
        )

        if not (start_date and end_date and start_date > now and end_date > start_date):
            return False

        if self.type == AdType.LISTING_PROPERTY:
            if not (self.property_id or self.fi_property_id):
                return False
        elif self.type == AdType.AGENT:
            if not self.agent_id:
                return False
        elif self.type == AdType.EVENT:
            if not self.event_id:
                return False

        return True
