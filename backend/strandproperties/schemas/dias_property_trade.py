from enum import StrEnum
from typing import List, Optional

from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.dias_common import (
    DisplayText,
    Attachment,
    BankAccount,
    DiasBuyer,
    DiasSeller,
    InitiatorContactInfo,
    StrandBuyerEN,
    StrandSellerEN,
)


class Kayttotarkoitus(StrEnum):
    MK = "MK"
    MR = "MR"
    MM = "MM"
    AR = "AR"
    LR = "LR"
    RM = "RM"


class UsagePurpose(StrEnum):
    MK = Kayttotarkoitus.MK
    MR = Kayttotarkoitus.MR
    MM = Kayttotarkoitus.MM
    AR = Kayttotarkoitus.AR
    LR = Kayttotarkoitus.LR
    RM = Kayttotarkoitus.RM


class Rakennustiedot(StrEnum):
    OL = "OL"
    RL = "RL"
    EL = "EL"
    AR = "AR"
    ER = "ER"


class BuildingInformation(StrEnum):
    OL = Rakennustiedot.OL
    RL = Rakennustiedot.RL
    EL = Rakennustiedot.EL
    AR = Rakennustiedot.AR
    ER = Rakennustiedot.ER


class Kaavatiedot(StrEnum):
    AK = "AK"
    YK = "YK"
    EK = "EK"
    RK = "RK"


class ZoningInformation(StrEnum):
    AK = Kaavatiedot.AK
    YK = Kaavatiedot.YK
    EK = Kaavatiedot.EK
    RK = Kaavatiedot.RK


class LuovutuskirjanTyyppi(StrEnum):
    LA = "LA"
    KA = "KA"
    ES = "ES"


class DeedType(StrEnum):
    LA = LuovutuskirjanTyyppi.LA
    KA = LuovutuskirjanTyyppi.KA
    ES = LuovutuskirjanTyyppi.ES


class SuostujanTyyppi(StrEnum):
    LE = "LE"
    PU = "PU"
    KH = "KH"


class ConsenterType(StrEnum):
    LE = SuostujanTyyppi.LE
    PU = SuostujanTyyppi.PU
    KH = SuostujanTyyppi.KH


class KenenLukuunLainhuutoaHaetaan(StrEnum):
    OL = "OL"
    ML = "ML"
    YL = "YL"
    AL = "AL"


class TitleClaimType(StrEnum):
    OL = KenenLukuunLainhuutoaHaetaan.OL
    ML = KenenLukuunLainhuutoaHaetaan.ML
    YL = KenenLukuunLainhuutoaHaetaan.YL
    AL = KenenLukuunLainhuutoaHaetaan.AL


class LuovutuksenKohde(StrEnum):
    # Allowed
    KK = "KK"
    MO = "MO"

    # Currently not allowed
    MP = "MP"
    MA = "MA"


class TransferObject(StrEnum):
    # Allowed
    KK = LuovutuksenKohde.KK
    MO = LuovutuksenKohde.MO

    # CurLuovutuksenKohde.enly not allowed
    MP = LuovutuksenKohde.MP
    MA = LuovutuksenKohde.MA


class AttachmentTypeKiinteistokauppa(StrEnum):
    # required
    # There must be exactly one RASITUSTODISTUS attachment
    RASITUSTODISTUS = "RASITUSTODISTUS"
    # There must be exactly one LAINHUUTOTODISTUS attachment
    LAINHUUTOTODISTUS = "LAINHUUTOTODISTUS"

    # required if luovutuksenKohde is MO
    HALLINNANJAKOSOPIMUS = "HALLINNANJAKOSOPIMUS"

    # required if orgnization trade
    MINUTES_EXTRACT = "MINUTES_EXTRACT"
    TRADE_REGISTER_EXTRACT = "TRADE_REGISTER_EXTRACT"

    UNKNOWN = "UNKNOWN"


class AttachmentTypePropertyTrade(StrEnum):
    # Required
    BURDEN_CERTIFICATE = AttachmentTypeKiinteistokauppa.RASITUSTODISTUS
    TITLE_CERTIFICATE = AttachmentTypeKiinteistokauppa.LAINHUUTOTODISTUS

    # required if luovutuksenKohde (TransferObject) is MO
    MANAGEMENT_AGREEMENT = AttachmentTypeKiinteistokauppa.HALLINNANJAKOSOPIMUS

    # required if orgnization trade
    MINUTES_EXTRACT = AttachmentTypeKiinteistokauppa.MINUTES_EXTRACT
    TRADE_REGISTER_EXTRACT = AttachmentTypeKiinteistokauppa.TRADE_REGISTER_EXTRACT

    UNKNOWN = AttachmentTypeKiinteistokauppa.UNKNOWN


class AttachmentTypeVuokraoikeudenSiirto(StrEnum):
    # Required
    # For a vuokraoikeuden siirto, There must be exactly one of each of the following
    LAINHUUTOTODISTUS = "LAINHUUTOTODISTUS"
    RASITUSTODISTUS = "RASITUSTODISTUS"
    KANTAKIINTEISTON_RASITUSTODISTUS = "KANTAKIINTEISTON_RASITUSTODISTUS"
    VUOKRASOPIMUKSEN_KOPIO = "VUOKRASOPIMUKSEN_KOPIO"
    VUOKRAOIKEUSTODISTUS = "VUOKRAOIKEUSTODISTUS"

    # required if luovutuksenKohde is MO
    HALLINNANJAKOSOPIMUS = "HALLINNANJAKOSOPIMUS"

    # required if orgnization trade
    MINUTES_EXTRACT = "MINUTES_EXTRACT"
    TRADE_REGISTER_EXTRACT = "TRADE_REGISTER_EXTRACT"

    UNKNOWN = "UNKNOWN"


class AttachmentTypeLeaseTransfer(StrEnum):
    # Required
    # For a vuokraoikeuden siirto, There must be exactly one of each of the following
    BURDEN_CERTIFICATE = AttachmentTypeVuokraoikeudenSiirto.RASITUSTODISTUS
    TITLE_CERTIFICATE = AttachmentTypeVuokraoikeudenSiirto.LAINHUUTOTODISTUS
    LEASE_AGREEMENT_COPY = AttachmentTypeVuokraoikeudenSiirto.VUOKRASOPIMUKSEN_KOPIO
    LEASEHOLD_CERTIFICATE = AttachmentTypeVuokraoikeudenSiirto.VUOKRAOIKEUSTODISTUS
    MAIN_PROPERTY_BURDEN_CERTIFICATE = (
        AttachmentTypeVuokraoikeudenSiirto.KANTAKIINTEISTON_RASITUSTODISTUS
    )

    # required if luovutuksenKohde is MO
    MANAGEMENT_AGREEMENT = AttachmentTypeVuokraoikeudenSiirto.HALLINNANJAKOSOPIMUS

    # required if orgnization trade
    MINUTES_EXTRACT = AttachmentTypeVuokraoikeudenSiirto.MINUTES_EXTRACT
    TRADE_REGISTER_EXTRACT = AttachmentTypeVuokraoikeudenSiirto.TRADE_REGISTER_EXTRACT

    UNKNOWN = AttachmentTypeVuokraoikeudenSiirto.UNKNOWN


class SuostujatPerson(BaseSchema):
    firstName: str
    lastName: str
    socialSecurityNumber: str
    email: str


class ConsentingPerson(BaseSchema):
    first_name: str
    last_name: str
    social_security_number: str
    email: str


class AttachmentKiinteistokauppa(Attachment):
    documentType: AttachmentTypeKiinteistokauppa


class AttachmentPropertyTrade(Attachment):
    document_type: AttachmentTypePropertyTrade


class AttachmentVuokraoikeudenSiirto(Attachment):
    documentType: Optional[AttachmentTypeVuokraoikeudenSiirto] = None


class AttachmentLeaseTransfer(Attachment):
    document_type: Optional[AttachmentTypeLeaseTransfer] = None


class AmountObject(BaseSchema):
    amountInCents: int


class AmountObjectWithDate(BaseSchema):
    amountInCents: int
    maksupaivamaara: Optional[str] = None


class ConfigTerm(BaseSchema):
    title: str
    description: str


class OptionValue(BaseSchema):
    value: str
    type: str
    supported: bool
    displayText: DisplayText


class ConfigOption(BaseSchema):
    values: List[OptionValue]


# class SellerPerson(BaseSchema):
#     firstName: str
#     lastName: str
#     socialSecurityNumber: str
#     email: str

#     class Config:
#         alias_generator = None


# class SellerOrganization(BaseSchema):
#     name: str
#     businessId: str

#     class Config:
#         alias_generator = None


# class SellerRealtorPayment(BaseSchema):
#     amountInCents: int
#     referenceNumber: Optional[str] = None
#     message: Optional[str] = None

#     class Config:
#         alias_generator = None


# class BuyerPerson(BaseSchema):
#     firstName: str
#     lastName: str
#     socialSecurityNumber: str
#     email: str

#     class Config:
#         alias_generator = None


# class BuyerOrganization(BaseSchema):
#     name: str
#     businessId: str

#     class Config:
#         alias_generator = None


class Luovutusilmoitus(BaseSchema):
    kayttotarkoitus: Kayttotarkoitus
    sukulaisluovutus: bool
    rantatontti: bool
    rakennustiedot: Rakennustiedot
    kaavatiedot: Kaavatiedot


# Luovutusilmoitus = TransferNotification
class TransferNotification(BaseSchema):
    usage_purpose: UsagePurpose
    is_relative_transfer: bool
    is_waterfront: bool
    building_information: BuildingInformation
    zoning_information: ZoningInformation


class LuovutettavatOsuudet(BaseSchema):
    osuus: str
    sellerSocialSecurityNumbers: Optional[List[str]] = None
    sellerBusinessIds: Optional[List[str]] = None


class TransferredShares(BaseSchema):
    share: str
    seller_social_security_numbers: Optional[List[str]] = None
    seller_business_ids: Optional[List[str]] = None


class Suostujat(BaseSchema):
    person: SuostujatPerson
    suostujanTyyppi: SuostujanTyyppi
    sellerSocialSecurityNumber: str


class Consenter(BaseSchema):
    person: ConsentingPerson
    consenter_type: ConsenterType
    seller_social_security_number: str


class AmountInCents(BaseSchema):
    amountInCents: int


class Kasiraha(BaseSchema):
    amountInCents: int
    maksupaivamaara: str


# DownPayment = Kasiraha
class DownPayment(BaseSchema):
    amount_in_cents: int
    payment_date: str


class LuovutuksenEhdot(BaseSchema):
    title: str
    description: str


# TermsOfTransfer = LuovutuksenEhdot
class TermsOfTransfer(LuovutuksenEhdot):
    pass


class DiasConfigRequest(BaseSchema):
    panttikirjojenKoodit: Optional[List[str]] = None
    eiSiirrettavatPanttikirjojenKoodit: Optional[List[str]] = None
    buyerBankBusinessId: Optional[str] = None
    sopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana: Optional[
        AmountInCents
    ] = None
    laitostunnus: Optional[str] = None
    kasiraha: Optional[Kasiraha] = None
    luovutuksenKohde: Optional[LuovutuksenKohde] = None
    sellerBankBusinessId: Optional[str] = None
    kauppahinta: Optional[AmountInCents] = None


class StrandConfigRequest(BaseSchema):
    pledge_certificate_codes: Optional[List[str]] = None
    non_transferable_pledge_certificate_codes: Optional[List[str]] = None
    buyer_bank_business_id: Optional[str] = None
    # Translation: SopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana = penalty_for_contract_termination_if_purchase_price_not_paid_on_due_date
    penalty_for_contract_termination: Optional[AmountInCents] = None
    institution_code: Optional[str] = None
    down_payment: Optional[DownPayment] = None
    transfer_target: Optional[TransferObject] = None
    seller_bank_business_id: Optional[str] = None
    purchase_price: Optional[AmountInCents] = None


class DiasConfigResponse(BaseSchema):
    luovutuskirjanPakollisetEhdot: List[LuovutuksenEhdot]
    options: dict[str, ConfigOption]


class StrandConfigResponse(BaseSchema):
    mandatory_deed_terms: List[TermsOfTransfer]
    options: dict[str, ConfigOption]


# CreateKiinteistökauppaDraft
class DiasCreatePropertyTradeDraftBase(BaseSchema):
    # Required Fields
    luovutettavatOsuudet: List[LuovutettavatOsuudet]
    initiatorContactInfo: InitiatorContactInfo
    suostujat: List[Suostujat]
    luovutuskirjanTyyppi: LuovutuskirjanTyyppi
    luovutuksenEhdot: List[LuovutuksenEhdot]
    luovutuksenKohde: LuovutuksenKohde
    sellers: List[DiasSeller]
    buyers: List[DiasBuyer]
    kauppahinta: AmountInCents
    initiatorPersonId: str
    tradeInitiatedTimestamp: str
    initiatorTradeReferenceId: str

    # Optional Fields
    panttikirjojenKoodit: Optional[List[str]] = None
    realtorBankAccount: Optional[BankAccount] = None
    kasiraha: Optional[Kasiraha] = None

    # Required when submitting for bank approval
    sopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana: Optional[
        AmountInCents
    ] = None


class StrandCreatePropertyTradeDraftBase(BaseSchema):
    # Required Fields
    transferred_shares: List[TransferredShares]
    initiator_contact_info: InitiatorContactInfo
    consenters: List[Consenter]
    deed_type: DeedType
    terms_of_transfer: List[TermsOfTransfer]
    transfer_object: TransferObject
    sellers: List[DiasSeller]
    buyers: List[DiasBuyer]
    purchase_price: AmountInCents
    initiator_person_id: str
    trade_initiated_timestamp: str
    initiator_trade_reference_id: str

    # Optional Fields
    pledge_certificate_codes: Optional[List[str]] = None
    realtor_bank_account: Optional[BankAccount] = None
    down_payment: Optional[DownPayment] = None

    # Required when submitting for bank approval
    # Translation: SopimussakkoPurkamisestaJosKauppahintaaEiMaksetaErapaivana = penalty_for_contract_termination_if_purchase_price_not_paid_on_due_date
    penalty_for_contract_termination: Optional[AmountInCents] = None


class DiasCreateKiinteistokauppaDraft(DiasCreatePropertyTradeDraftBase):
    kiinteistotunnus: str
    attachments: List[AttachmentKiinteistokauppa]
    luovutusilmoitus: Luovutusilmoitus


class StrandCreatePropertyTradeDraft(StrandCreatePropertyTradeDraftBase):
    real_estate_code: str
    attachments: List[AttachmentPropertyTrade]
    transfer_notification: TransferNotification


class DiasCreateVuokraoikeudenSiirto(DiasCreatePropertyTradeDraftBase):
    laitostunnus: str
    attachments: List[AttachmentVuokraoikeudenSiirto]
    eiSiirrettavatPanttikirjojenKoodit: Optional[List[str]] = None


class StrandCreateLeaseTransfer(StrandCreatePropertyTradeDraftBase):
    institution_code: str
    attachments: List[AttachmentLeaseTransfer]
    non_transferable_pledge_codes: Optional[List[str]] = None


class StrandPropertyTradeDraftBase(BaseSchema):
    property_id: int
    transferred_shares: List[TransferredShares]
    consenters: List[Consenter]
    deed_type: DeedType
    terms_of_transfer: List[TermsOfTransfer]
    transfer_object: TransferObject
    sellers: List[StrandSellerEN]
    buyers: List[StrandBuyerEN]
    pledge_certificate_codes: Optional[List[str]] = None
    down_payment: Optional[DownPayment] = None
    penalty_for_contract_termination: Optional[AmountInCents] = None

    # Not added to the user model yet (ASK JOHANNES)
    realtor_bank_account: Optional[BankAccount] = None


class DiasPropertyTradeDraftRead(DiasCreatePropertyTradeDraftBase):
    id: int


class StrandPropertyTradeDraftRead(StrandPropertyTradeDraftBase):
    id: int
