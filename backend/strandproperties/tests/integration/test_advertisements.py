# strandproperties/tests/integration/test_advertisements.py
from datetime import datetime, timezone

import pytest
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.constants import CountryCode
from strandproperties.models.advertisement import AdStatus, AdType, Advertisement
from strandproperties.models.property import Property
from strandproperties.schemas.advertisement import AdvertisementCreate
from strandproperties.views.expense import Expenses


def _iso_now() -> str:
    return datetime.now(timezone.utc).isoformat()


def test_create_advertisement_non_finland(
    route_url, api, admin1, organization1, db_session
):
    organization1.country_code = CountryCode.SPAIN
    db_session.flush()

    payload = {
        "title": "Test ad",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 500,
        "startDate": _iso_now(),
        "municipality": "Madrid",
    }

    resp = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )

    assert resp["title"] == "Test ad"
    assert resp["budgetTotal"] == 500 * 100
    assert resp["ownerId"] == admin1.id
    assert resp.get("fiPropertyId") is None
    assert resp.get("fiAgentId") is None
    assert resp["status"] == AdStatus.DRAFT.value


def test_create_advertisement_finland_fields(
    route_url, api, admin1, organization1, db_session
):
    organization1.country_code = CountryCode.FINLAND
    db_session.flush()

    payload = {
        "title": "Nordic Offer",
        "type": AdType.EVENT.value,
        "status": AdStatus.DRAFT.value,
        "language": "fi",
        "country": "Finland",
        "municipality": "Helsinki",
        "budgetTotal": 800,
        "startDate": _iso_now(),
    }

    resp = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )

    assert resp.get("fiAgentId") is None
    assert resp.get("propertyId") is None
    assert resp["status"] == AdStatus.DRAFT.value


def test_create_validation_error(route_url, api, admin1):
    # Test invalid title (too short)
    payload = {
        "title": "ab",  # Less than min_length=3
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 100,
        "startDate": _iso_now(),
    }
    api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=400,
        role=admin1._roles[0],
    )

    # Test invalid budget (negative)
    payload = {
        "title": "Valid Title",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": -100,  # Less than ge=0.0
        "startDate": _iso_now(),
    }
    api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=400,
        role=admin1._roles[0],
    )


def test_create_with_expense(route_url, api, admin1, organization1, mocker):
    organization1.country_code = CountryCode.SPAIN
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    payload = {
        "title": "Paid Ad",
        "type": AdType.CUSTOM.value,
        "language": "en",
        "country": "Spain",
        "budgetTotal": 300,
        "status": AdStatus.ACTIVE.value,
        "startDate": _iso_now(),
        "municipality": "Madrid",
    }

    resp = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )

    assert (
        mock_expense.call_count == 2
    ), "Both expense and deposit should be created for non‑draft ads"
    assert resp["status"] == AdStatus.ACTIVE.value


def test_create_expense_failure_bubbles_up(
    route_url, api, admin1, organization1, mocker
):
    organization1.country_code = CountryCode.SPAIN
    mocker.patch.object(
        Expenses, "create_expense_transaction", side_effect=Exception("oops")
    )

    payload = {
        "title": "Broken Ad",
        "type": AdType.CUSTOM.value,
        "language": "en",
        "country": "Spain",
        "budgetTotal": 400,
        "status": AdStatus.ACTIVE.value,
        "startDate": _iso_now(),
        "municipality": "Madrid",
    }

    api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=500,
        role=admin1._roles[0],
    )


def test_list_advertisements_filters(
    route_url, api, admin1, realtor1, organization1, db_session
):

    ad1 = Advertisement(
        owner_id=realtor1.id,
        organization_id=organization1.id,
        type=AdType.LISTING_PROPERTY.value,
        language="en",
        country="Spain",
        title="Foo",
        budget_total=1000 * 100,
        property_id=None,
        status=AdStatus.DRAFT.value,
        start_date=datetime.now(timezone.utc),
        municipality="Madrid",
    )
    ad2 = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        type=AdType.PROPERTY_SOLD.value,
        language="fi",
        country="Finland",
        title="Bar",
        budget_total=2000 * 100,
        property_id=None,
        status=AdStatus.ACTIVE.value,
        start_date=datetime.now(timezone.utc),
        municipality="Helsinki",
    )

    db_session.add_all([ad1, ad2])
    db_session.flush()

    # Test admin view - should see all ads in their organization
    admin_view = api(
        "get",
        route_url("advertisement.list_create"),
        status=200,
        role=admin1._roles[0],
    )
    assert admin_view["metadata"]["totalCount"] == 2, "Admin should see all ads"
    assert any(
        ad["ownerId"] == realtor1.id for ad in admin_view["records"]
    ), "Admin should see realtor's ad"
    assert any(
        ad["ownerId"] == admin1.id for ad in admin_view["records"]
    ), "Admin should see own ad"

    # Test realtor view - should only see their own ads
    realtor_view = api(
        "get",
        route_url("advertisement.list_create"),
        status=200,
        role=realtor1._roles[0],
    )
    assert (
        realtor_view["metadata"]["totalCount"] == 1
    ), "Realtor should only see their own ads"
    assert (
        realtor_view["records"][0]["ownerId"] == realtor1.id
    ), "Realtor should only see their own ads"

    # Test filtering by type (as admin)
    filtered = api(
        "get",
        route_url("advertisement.list_create"),
        params={"types": [AdType.PROPERTY_SOLD.value]},
        status=200,
        role=admin1._roles[0],
    )
    assert len(filtered["records"]) == 1, "Type filter should work"
    assert (
        filtered["records"][0]["type"] == AdType.PROPERTY_SOLD.value
    ), "Should get correct type"

    # Test filtering by type (as realtor)
    filtered_realtor = api(
        "get",
        route_url("advertisement.list_create"),
        params={"types": [AdType.LISTING_PROPERTY.value]},
        status=200,
        role=realtor1._roles[0],
    )
    assert len(filtered_realtor["records"]) == 1, "Type filter should work for realtor"
    assert (
        filtered_realtor["records"][0]["type"] == AdType.LISTING_PROPERTY.value
    ), "Should get correct type"
    assert (
        filtered_realtor["records"][0]["ownerId"] == realtor1.id
    ), "Should still be filtered by owner"


def test_read_update_delete_publish_duplicate_end(
    route_url, api, admin1, organization1, db_session, mocker
):
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    ad = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        type=AdType.LISTING_PROPERTY.value,
        language="en",
        country="Spain",
        title="Original",
        budget_total=1500,
        property_id=None,
        status=AdStatus.DRAFT.value,
        start_date=datetime.now(timezone.utc),
        municipality="Madrid",
    )
    db_session.add(ad)
    db_session.flush()
    ad_id = ad.id

    # read
    got = api(
        "get",
        route_url("advertisement.read_edit_delete", id=ad_id),
        status=200,
        role=admin1._roles[0],
    )
    assert got["title"] == "Original"

    # update
    patched = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad_id),
        {"title": "Updated"},
        status=200,
        role=admin1._roles[0],
    )
    assert patched["title"] == "Updated"

    # publish
    published = api(
        "patch",
        route_url("advertisement.publish", id=ad_id),
        status=200,
        role=admin1._roles[0],
    )
    assert published["status"] == AdStatus.IN_REVIEW.value

    # duplicate
    dup = api(
        "post",
        route_url("advertisement.duplicate", id=ad_id),
        status=201,
        role=admin1._roles[0],
    )
    assert dup["id"] != ad_id
    assert dup["status"] == AdStatus.DRAFT.value

    # end original
    ended = api(
        "patch",
        route_url("advertisement.end", id=ad_id),
        status=200,
        role=admin1._roles[0],
    )
    assert ended.get("endDate") is not None

    # delete the duplicated draft
    api(
        "delete",
        route_url("advertisement.read_edit_delete", id=dup["id"]),
        status=200,
        role=admin1._roles[0],
    )


def test_advertisement_status_transitions(
    route_url, api, admin1, organization1, db_session, mocker
):
    """Test valid advertisement status transitions"""
    # Mock expense creation since we're testing status transitions
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    # Create initial draft ad
    payload = {
        "title": "Status Test Ad",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 1000,
        "startDate": _iso_now(),
        "municipality": "Barcelona",
        "status": AdStatus.DRAFT.value,
    }

    # Create initial draft
    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["status"] == AdStatus.DRAFT.value

    # Test transition to IN_REVIEW
    published = api(
        "patch",
        route_url("advertisement.publish", id=ad["id"]),
        status=200,
        role=admin1._roles[0],
    )
    assert published["status"] == AdStatus.IN_REVIEW.value

    # Test transition to ACTIVE - this should create an expense
    activated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"status": AdStatus.ACTIVE.value, "startDate": _iso_now()},
        status=200,
        role=admin1._roles[0],
    )
    assert activated["status"] == AdStatus.ACTIVE.value

    # Test transition to COMPLETED
    ended = api(
        "patch",
        route_url("advertisement.end", id=ad["id"]),
        status=200,
        role=admin1._roles[0],
    )
    assert ended["status"] == AdStatus.COMPLETED.value
    assert ended.get("endDate") is not None


def test_create_non_draft_creates_expense(
    route_url, api, admin1, organization1, db_session, mocker
):
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    payload = {
        "title": "Non-draft Ad Creates Expense",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 1500,
        "startDate": _iso_now(),
        "municipality": "Barcelona",
        "status": AdStatus.ACTIVE.value,
    }

    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["status"] == AdStatus.ACTIVE.value

    assert mock_expense.call_count == 2, "Both expense and deposit should be created"

    expense_call_args = mock_expense.call_args_list[0][0][0]
    assert expense_call_args["amount"] == 150000
    assert expense_call_args["type"] == "expense"
    assert expense_call_args["status"] == "on_hold"
    assert (
        expense_call_args["description"]
        == "Expense transaction for advertisement: Non-draft Ad Creates Expense"
    )

    deposit_call_args = mock_expense.call_args_list[1][0][0]
    assert deposit_call_args["amount"] == 150000
    assert deposit_call_args["type"] == "deposit"
    assert deposit_call_args["status"] == "succeeded"
    assert (
        deposit_call_args["description"]
        == "Deposit transaction for advertisement: Non-draft Ad Creates Expense"
    )


def test_draft_creation_and_update_behavior(
    route_url, api, admin1, organization1, db_session, mocker
):
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    draft_payload = {
        "title": "Draft Ad - No Expenses",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 1000,
        "startDate": _iso_now(),
        "municipality": "Barcelona",
        "status": AdStatus.DRAFT.value,
    }

    draft_ad = api(
        "post",
        route_url("advertisement.list_create"),
        draft_payload,
        status=201,
        role=admin1._roles[0],
    )
    assert draft_ad["status"] == AdStatus.DRAFT.value

    assert mock_expense.call_count == 0

    mock_expense.reset_mock()

    in_review_ad = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=draft_ad["id"]),
        {"status": AdStatus.IN_REVIEW.value},
        status=200,
        role=admin1._roles[0],
    )
    assert in_review_ad["status"] == AdStatus.IN_REVIEW.value

    assert (
        mock_expense.call_count == 2
    ), "Moving from DRAFT to IN_REVIEW should create expense transactions"

    mock_expense.reset_mock()

    active_ad = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=draft_ad["id"]),
        {"status": AdStatus.ACTIVE.value},
        status=200,
        role=admin1._roles[0],
    )
    assert active_ad["status"] == AdStatus.ACTIVE.value

    assert (
        mock_expense.call_count == 0
    ), "Further status changes should not create new expense transactions"


def test_update_to_in_review_from_in_review_no_duplicate_expense(
    route_url, api, admin1, organization1, db_session, mocker
):
    """Test that updating advertisement status to IN_REVIEW from IN_REVIEW doesn't create duplicate expense"""
    # Mock expense creation to verify it doesn't get called
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    # Create initial advertisement in IN_REVIEW status
    payload = {
        "title": "Already IN_REVIEW Test Ad",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 1200,
        "startDate": _iso_now(),
        "municipality": "Madrid",
        "status": AdStatus.IN_REVIEW.value,
    }

    # Create in_review ad
    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["status"] == AdStatus.IN_REVIEW.value

    # Reset mock to track calls from the update
    mock_expense.reset_mock()

    # Update some other field while keeping status as IN_REVIEW
    updated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"status": AdStatus.IN_REVIEW.value, "title": "Updated Title"},
        status=200,
        role=admin1._roles[0],
    )
    assert updated["status"] == AdStatus.IN_REVIEW.value
    assert updated["title"] == "Updated Title"

    # Verify expense creation was NOT called (no status change)
    mock_expense.assert_not_called()


def test_status_transitions_without_required_fields(route_url, api, admin1, mocker):
    """Test advertisement status transitions without required fields"""
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )
    # Create initial draft ad
    payload = {
        "title": "Status Test Ad",
        "type": AdType.EVENT.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 500,
        "municipality": "Madrid",
    }

    # Create draft ad
    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["status"] == AdStatus.DRAFT.value
    assert ad["budgetTotal"] == 50000  # Budget is converted to cents on create

    # Test can activate without startDate
    activated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"status": AdStatus.ACTIVE.value},
        status=200,
        role=admin1._roles[0],
    )
    assert activated["status"] == AdStatus.ACTIVE.value

    # Test can decrease budget
    updated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"budgetTotal": 300},
        status=200,
        role=admin1._roles[0],
    )
    assert updated["budgetTotal"] == 30000  # Budget is converted to cents on update


def test_budget_validation_and_updates(
    route_url, api, admin1, organization1, db_session, mocker
):
    """Test budget validation and updates for advertisements"""
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    # Test minimum budget validation
    payload = {
        "title": "Budget Test Ad",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": -100,  # Invalid negative budget
        "startDate": _iso_now(),
        "municipality": "Valencia",
    }

    api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=400,
        role=admin1._roles[0],
    )

    # Create valid ad
    payload["budgetTotal"] = 1000
    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["budgetTotal"] == 100000  # Budget is converted to cents on create

    # Test budget update while in draft
    updated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"budgetTotal": 1500},
        status=200,
        role=admin1._roles[0],
    )
    assert updated["budgetTotal"] == 150000  # Budget is converted to cents on update

    # Publish the ad
    published = api(
        "patch",
        route_url("advertisement.publish", id=ad["id"]),
        status=200,
        role=admin1._roles[0],
    )

    # Can decrease budget after publishing
    decreased = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"budgetTotal": 1000},
        status=200,
        role=admin1._roles[0],
    )
    assert decreased["budgetTotal"] == 100000  # Budget is converted to cents on update

    # Can increase budget after publishing
    final_update = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"budgetTotal": 2000},
        status=200,
        role=admin1._roles[0],
    )
    assert (
        final_update["budgetTotal"] == 200000
    )  # Budget is converted to cents on update


def test_status_transitions(route_url, api, admin1, mocker):
    """Test advertisement status transitions"""
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )
    # Create initial draft ad
    payload = {
        "title": "Status Test Ad",
        "type": AdType.EVENT.value,
        "language": "en",
        "country": "ES",
        "budgetTotal": 500,
        "startDate": _iso_now(),
        "municipality": "Madrid",
    }

    # Create draft ad
    ad = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )
    assert ad["status"] == AdStatus.DRAFT.value

    # Test can activate directly
    activated = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"status": AdStatus.ACTIVE.value},
        status=200,
        role=admin1._roles[0],
    )
    assert activated["status"] == AdStatus.ACTIVE.value

    # Test can complete directly
    completed = api(
        "patch",
        route_url("advertisement.read_edit_delete", id=ad["id"]),
        {"status": AdStatus.COMPLETED.value},
        status=200,
        role=admin1._roles[0],
    )
    assert completed["status"] == AdStatus.COMPLETED.value


def test_advertisement_with_property_creates_connected_expense(
    route_url, api, admin1, organization1, db_session, mocker, property1
):
    organization1.country_code = CountryCode.SPAIN
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    payload = {
        "title": "Ad with Property Connection",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "en",
        "country": "Spain",
        "budgetTotal": 500,
        "status": AdStatus.ACTIVE.value,
        "startDate": _iso_now(),
        "municipality": "Madrid",
        "propertyId": property1.id,
    }

    resp = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )

    assert mock_expense.call_count == 2, "Both expense and deposit should be created"

    expense_call_args = mock_expense.call_args_list[0][0][0]
    expense_targets = expense_call_args["targets"]

    assert (
        len(expense_targets) == 2
    ), "Should have both advertisement and property targets"

    target_types = [target["target_type"] for target in expense_targets]
    assert "advertisement" in target_types
    assert "property" in target_types

    advertisement_target = next(
        t for t in expense_targets if t["target_type"] == "advertisement"
    )
    assert advertisement_target["reference"] is None

    property_target = next(t for t in expense_targets if t["target_type"] == "property")
    assert property_target["target_id"] == property1.id
    assert property_target["reference"] == property1.reference

    deposit_call_args = mock_expense.call_args_list[1][0][0]
    deposit_targets = deposit_call_args["targets"]

    assert len(deposit_targets) == 2, "Deposit should also have both targets"
    assert (
        deposit_targets == expense_targets
    ), "Both transactions should have same targets"


def test_advertisement_with_fi_property_creates_connected_expense(
    route_url, api, admin1, organization1, db_session, mocker, fi_property1
):
    organization1.country_code = CountryCode.FINLAND
    mock_expense = mocker.patch.object(
        Expenses, "create_expense_transaction", return_value={}
    )

    payload = {
        "title": "Ad with FI Property Connection",
        "type": AdType.LISTING_PROPERTY.value,
        "language": "fi",
        "country": "Finland",
        "budgetTotal": 800,
        "status": AdStatus.ACTIVE.value,
        "startDate": _iso_now(),
        "municipality": "Helsinki",
        "propertyId": fi_property1.id,
    }

    resp = api(
        "post",
        route_url("advertisement.list_create"),
        payload,
        status=201,
        role=admin1._roles[0],
    )

    assert mock_expense.call_count == 2, "Both expense and deposit should be created"

    expense_call_args = mock_expense.call_args_list[0][0][0]
    expense_targets = expense_call_args["targets"]

    assert (
        len(expense_targets) == 2
    ), "Should have both advertisement and fi_property targets"

    target_types = [target["target_type"] for target in expense_targets]
    assert "advertisement" in target_types
    assert "fi_property" in target_types

    advertisement_target = next(
        t for t in expense_targets if t["target_type"] == "advertisement"
    )
    assert advertisement_target["reference"] is None

    fi_property_target = next(
        t for t in expense_targets if t["target_type"] == "fi_property"
    )
    assert fi_property_target["target_id"] == fi_property1.id
    assert fi_property_target["reference"] == fi_property1.reference

    deposit_call_args = mock_expense.call_args_list[1][0][0]
    deposit_targets = deposit_call_args["targets"]

    assert len(deposit_targets) == 2, "Deposit should also have both targets"
    assert (
        deposit_targets == expense_targets
    ), "Both transactions should have same targets"
