import os
from contextlib import contextmanager
from io import BytesIO
from unittest.mock import Mock, patch

import pytest
import requests
from pypdf import PdfReader

from strandproperties.libs.aws import S3Service
from strandproperties.models.brochure import (
    Brochure,
    BrochureImages,
    BrochureLanguage,
    CoverTheme,
)
from strandproperties.services.brochure.service import PropertyNotFoundError
from strandproperties.services.documents.config import TemplatesEnum
from strandproperties.services.documents.document_renderer import DocumentRenderer
from strandproperties.services.documents.image_processor import ImageProcessor


@pytest.fixture(autouse=True)
def mock_s3_service(mocker):
    mocker.patch.object(S3Service, "upload")


def test_generate_brochure_pdf_creates_brochure_record(
    brochure_service, fi_property1, realtor1, brochure_images_data, db_session
):
    with (success_response_from_image_fetching(),):
        result = brochure_service.generate_brochure_pdf(
            property_id=fi_property1.id,
            realtor_id=realtor1.id,
            is_complete=False,
            theme=CoverTheme.WHITE,
            language=BrochureLanguage.FI,
            include_cover=False,
            cover_page_type=1,
            property_blueprint_image=None,
            property_images_data=brochure_images_data,
            property_information_data=None,
        )

        assert isinstance(result, bytes)
        assert (
            db_session.query(Brochure)
            .where(Brochure.property_id == fi_property1.id)
            .one_or_none()
            is not None
        )


def test_generate_brochure_pdf_fails_for_non_existing_property(
    brochure_service, non_existing_property_id, realtor1
):
    with pytest.raises(PropertyNotFoundError):
        brochure_service.generate_brochure_pdf(
            property_id=non_existing_property_id,
            realtor_id=realtor1.id,
            is_complete=False,
            theme=CoverTheme.WHITE,
            language=BrochureLanguage.FI,
            include_cover=False,
            cover_page_type=1,
            property_blueprint_image=None,
            property_images_data=None,
            property_information_data=None,
        )


def test_generate_brochure_pdf_with_cover_page(
    brochure_service, fi_property1, realtor1, brochure_images_data
):
    expected_text = "FRONT COVER PAGE"
    with (
        success_response_from_image_fetching(),
        mock_template(TemplatesEnum.FRONT_COVER, expected_text),
    ):
        result = brochure_service.generate_brochure_pdf(
            property_id=fi_property1.id,
            realtor_id=realtor1.id,
            is_complete=True,
            theme=CoverTheme.FLOWER,
            language=BrochureLanguage.FI,
            include_cover=True,
            cover_page_type=2,
            property_blueprint_image=None,
            property_images_data=brochure_images_data,
            property_information_data=None,
        )

        assert_pdf_contains_text(pdf_bytes=result, page=0, text=expected_text)


def test_generate_brochure_pdf_with_blueprint_page(
    brochure_service, fi_property1, realtor1, brochure_images_data
):
    blueprint_image_url = "https://example.com/blueprint.jpg"
    with (
        success_response_from_image_fetching(),
        mock_template(
            TemplatesEnum.BROCHURE_PROPERTY_BLUEPRINT_IMAGE, blueprint_image_url
        ),
    ):
        result = brochure_service.generate_brochure_pdf(
            property_id=fi_property1.id,
            realtor_id=realtor1.id,
            is_complete=True,
            theme=CoverTheme.WHITE,
            language=BrochureLanguage.FI,
            include_cover=False,
            cover_page_type=1,
            property_blueprint_image=blueprint_image_url,
            property_images_data=brochure_images_data,
            property_information_data=None,
        )

        assert isinstance(result, bytes)
        assert_pdf_contains_text(pdf_bytes=result, page=0, text=blueprint_image_url)


def test_save_brochure_draft_creates_brochure_record(
    brochure_service, fi_property1, realtor1, db_session
):
    result = brochure_service.save_brochure_draft(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
    )

    assert isinstance(result, Brochure)
    assert (
        db_session.query(Brochure)
        .where(Brochure.property_id == fi_property1.id)
        .one_or_none()
        is not None
    )


def test_generate_property_images_pdf_creates_db_record(
    brochure_service, brochure_images_data, db_session
):
    with (
        patch.object(DocumentRenderer, "render_template", return_value="<html></html>"),
        patch.object(DocumentRenderer, "generate_pdf", return_value=b"images_pdf"),
        patch.object(
            ImageProcessor, "convert_image_to_base64", return_value="base64_image_data"
        ),
    ):
        result = brochure_service._generate_property_images_pdf(brochure_images_data)

        assert result.pdf == b"images_pdf"
        assert isinstance(result.id, int)
        assert (
            db_session.query(BrochureImages)
            .where(BrochureImages.id == result.id)
            .one_or_none()
            is not None
        )


def test_generate_property_images_pdf_processes_multiple_pages(
    brochure_service, brochure_images_data
):
    with (
        patch.object(DocumentRenderer, "render_template", return_value="<html></html>"),
        patch.object(DocumentRenderer, "generate_pdf", return_value=b"images_pdf"),
        patch.object(
            ImageProcessor, "convert_image_to_base64", return_value="base64_image_data"
        ) as mock_convert,
    ):
        brochure_service._generate_property_images_pdf(brochure_images_data)

        assert mock_convert.call_count == 3


def assert_pdf_contains_text(pdf_bytes: bytes, page: int, text: str):
    reader = PdfReader(BytesIO(pdf_bytes))
    page_text = reader.get_page(page).extract_text()
    assert text in page_text


@contextmanager
def success_response_from_image_fetching():
    def response(url: str, *args, **kwargs):
        result = Mock()
        result.status_code = 200
        result.content = open(
            os.path.join(os.path.dirname(__file__), "test_brochure_image.jpeg"), "rb"
        ).read()
        result.headers = {"content-type": "image/jpeg"}
        return result

    with patch.object(requests, "get", side_effect=response):
        yield


@contextmanager
def mock_template(template: TemplatesEnum, expected_text: str):
    def side_effect(template_name, *args, **kwargs):
        if template_name == template:
            return f"<html><h1>{expected_text}</h1></html>"
        return "<html></html>"

    with patch.object(DocumentRenderer, "render_template", side_effect=side_effect):
        yield
