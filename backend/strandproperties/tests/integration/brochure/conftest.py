from typing import Callable, Optional, Type<PERSON>lias

import pytest
from sqlalchemy.orm import Session

from strandproperties.constants import DataSource
from strandproperties.models.brochure import (
    Brochure,
    BrochureImagePage,
    BrochureImages,
    BrochureImageWithPosition,
    BrochureInformation,
    BrochureLanguage,
    CoverTheme,
    ImagePosition,
)
from strandproperties.models.fi_property import FIProperty
from strandproperties.models.fi_residential_property_overview import (
    FIResidentialPropertyOverview,
)
from strandproperties.schemas.brochure import (
    BrochureImagesCreate,
    BrochureInformationCreate,
    ImageWithPosition,
    PropertyImagePage,
)
from strandproperties.schemas.fi_property.fi_common import FIParkingSpaceTypeCodeEnum
from strandproperties.services.brochure.service import BrochureService
from strandproperties.tests.integration.conftest import _create_api_fixture

BrochureServiceFactory: TypeAlias = Callable[
    [int | None, int | None, bool | None], BrochureService
]


@pytest.fixture
def fi_residential_property_overview1(db_session):

    fi_residential_property_overview = FIResidentialPropertyOverview(
        parking_spaces=[
            {
                "parking_space_type_code": FIParkingSpaceTypeCodeEnum.GARAGE,
                "count": 10,
            }
        ]
    )
    db_session.add(fi_residential_property_overview)
    db_session.flush()
    return fi_residential_property_overview


@pytest.fixture
def fi_property1(
    db_session,
    fi_property_type1,
    fi_realty1,
    fi_property_overview1,
    fi_housing_company1,
    fi_residential_share_overview1,
    fi_plot_overview1,
    fi_residential_property_overview1,
    realtor1,
) -> FIProperty:
    fi_housing_company1.construction = {"construction_year": 2020}
    fi_property = FIProperty(
        organization_id=1,
        is_exclusive=True,
        data_source=DataSource.STRAND,
        reference="SPCRM1001",
        fi_property_type_id=fi_property_type1.id,
        fi_realty_id=fi_realty1.id,
        fi_property_overview_id=fi_property_overview1.id,
        fi_housing_company_id=fi_housing_company1.id,
        fi_residential_share_overview_id=fi_residential_share_overview1.id,
        fi_plot_overview_id=fi_plot_overview1.id,
        portals={"is_oikotie_enabled": True, "is_etuovi_enabled": False},
        fi_residential_property_overview_id=fi_residential_property_overview1.id,
    )
    fi_property.realtor_users = [realtor1]

    db_session.add(fi_property)
    db_session.commit()
    return fi_property


@pytest.fixture
def api(app, mocker, realtor1, pyramid_request) -> Callable:
    return _create_api_fixture(app, mocker, realtor1, pyramid_request, use_json=True)


@pytest.fixture
def pdf_api(app, mocker, realtor1, pyramid_request) -> Callable:
    return _create_api_fixture(app, mocker, realtor1, pyramid_request, use_json=False)


@pytest.fixture
def brochure_route_url(route_url):
    def url_builder(name: str):
        return route_url(name)

    return url_builder


@pytest.fixture
def brochure_service(create_brochure_service, realtor1) -> BrochureService:
    return create_brochure_service(
        user_id=realtor1.id,
        organization_id=1,
    )


@pytest.fixture
def create_brochure_service(db_session: Session, realtor1) -> BrochureServiceFactory:
    def factory(
        user_id: Optional[int] = None,
        organization_id: Optional[int] = None,
        is_admin: Optional[bool] = None,
    ) -> BrochureService:
        return BrochureService(
            db_session=db_session,
            user_id=user_id or realtor1.id,
            organization_id=organization_id or 1,
            is_admin=is_admin or False,
        )

    return factory


@pytest.fixture
def brochure_images_data() -> BrochureImagesCreate:
    return BrochureImagesCreate(
        images_data=[
            PropertyImagePage(
                page_number=1,
                images=[
                    ImageWithPosition(
                        image_url="https://example.com/image1.jpg",
                        position=ImagePosition.FULL_PAGE,
                    ),
                    ImageWithPosition(
                        image_url="https://example.com/image2.jpg",
                        position=ImagePosition.TOP,
                    ),
                ],
            ),
            PropertyImagePage(
                page_number=2,
                images=[
                    ImageWithPosition(
                        image_url="https://example.com/image3.jpg",
                        position=ImagePosition.LEFT,
                    ),
                ],
            ),
        ]
    )


@pytest.fixture
def brochure_information_data() -> BrochureInformationCreate:
    return BrochureInformationCreate(
        property_description="Test property description for brochure"
    )


@pytest.fixture
def existing_brochure(db_session: Session, realtor1, fi_property1) -> Brochure:
    brochure = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        theme=CoverTheme.WHITE,
        language=BrochureLanguage.FI,
        version_number=1,
        is_complete=True,
    )
    db_session.add(brochure)
    db_session.flush()
    return brochure


@pytest.fixture
def existing_brochure_images(
    db_session: Session,
) -> BrochureImages:
    brochure_images = BrochureImages()
    db_session.add(brochure_images)
    db_session.flush()

    page = BrochureImagePage(brochure_images_id=brochure_images.id, page_number=1)
    db_session.add(page)
    db_session.flush()

    image = BrochureImageWithPosition(
        page_id=page.id,
        image_url="https://example.com/test.jpg",
        position=ImagePosition.FULL_PAGE,
    )
    db_session.add(image)
    db_session.flush()

    return brochure_images


@pytest.fixture
def existing_brochure_information(
    db_session: Session,
) -> BrochureInformation:
    info = BrochureInformation(property_description="Existing property description")
    db_session.add(info)
    db_session.flush()
    return info


@pytest.fixture
def non_existing_property_id() -> int:
    return -100


@pytest.fixture
def non_existing_realtor_id() -> int:
    return -200
