import pytest

from strandproperties.models.brochure import (
    Bro<PERSON><PERSON>,
    BrochureImagePage,
    BrochureImages,
    BrochureImageWithPosition,
    BrochureInformation,
    BrochureLanguage,
    CoverTheme,
    ImagePosition,
)


def test_brochure_model_creation(db_session, realtor1, fi_property1):
    brochure = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        theme=CoverTheme.WHITE,
        language=BrochureLanguage.FI,
        version_number=1,
        is_complete=False,
    )
    db_session.add(brochure)
    db_session.flush()

    assert brochure.id is not None
    assert brochure.property_id == fi_property1.id
    assert brochure.realtor_id == realtor1.id
    assert brochure.theme == CoverTheme.WHITE
    assert brochure.language == BrochureLanguage.FI
    assert brochure.version_number == 1
    assert brochure.is_complete == False


def test_brochure_model_default_values(db_session, realtor1, fi_property1):
    brochure = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        version_number=1,
    )
    db_session.add(brochure)
    db_session.flush()

    assert brochure.theme == CoverTheme.WHITE
    assert brochure.language == BrochureLanguage.FI
    assert brochure.is_complete == False


def test_brochure_images_creation(db_session):
    brochure_images = BrochureImages()
    db_session.add(brochure_images)
    db_session.flush()

    assert brochure_images.id is not None
    assert brochure_images.pages == []


def test_brochure_image_page_creation(db_session, existing_brochure_images):
    page = BrochureImagePage(
        brochure_images_id=existing_brochure_images.id,
        page_number=1,
    )
    db_session.add(page)
    db_session.flush()

    assert page.id is not None
    assert page.brochure_images_id == existing_brochure_images.id
    assert page.page_number == 1


def test_brochure_image_with_position_creation(db_session):
    brochure_images = BrochureImages()
    db_session.add(brochure_images)
    db_session.flush()

    page = BrochureImagePage(
        brochure_images_id=brochure_images.id,
        page_number=1,
    )
    db_session.add(page)
    db_session.flush()

    image = BrochureImageWithPosition(
        page_id=page.id,
        image_url="https://example.com/image.jpg",
        position=ImagePosition.FULL_PAGE,
    )
    db_session.add(image)
    db_session.flush()

    assert image.id is not None
    assert image.page_id == page.id
    assert image.image_url == "https://example.com/image.jpg"
    assert image.position == ImagePosition.FULL_PAGE


def test_brochure_images_relationship_cascade(db_session):
    brochure_images = BrochureImages()
    db_session.add(brochure_images)
    db_session.flush()

    page = BrochureImagePage(
        brochure_images_id=brochure_images.id,
        page_number=1,
    )
    db_session.add(page)
    db_session.flush()

    image = BrochureImageWithPosition(
        page_id=page.id,
        image_url="https://example.com/image.jpg",
        position=ImagePosition.TOP_LEFT,
    )
    db_session.add(image)
    db_session.flush()

    page_id = page.id
    image_id = image.id

    db_session.delete(brochure_images)
    db_session.flush()

    assert db_session.get(BrochureImagePage, page_id) is None
    assert db_session.get(BrochureImageWithPosition, image_id) is None


def test_brochure_information_creation(db_session):
    info = BrochureInformation(property_description="Test property description")
    db_session.add(info)
    db_session.flush()

    assert info.id is not None
    assert info.property_description == "Test property description"


def test_brochure_information_nullable_fields(db_session):
    info = BrochureInformation()
    db_session.add(info)
    db_session.flush()

    assert info.id is not None
    assert info.property_description is None


def test_brochure_language_enum_values():
    assert BrochureLanguage.FI == "fi"
    assert BrochureLanguage.EN == "en"


def test_cover_theme_enum_values():
    assert CoverTheme.WHITE == 1
    assert CoverTheme.FLOWER == 2
    assert CoverTheme.LIVINGROOM == 3
    assert CoverTheme.LIVINGROOM2 == 4
    assert CoverTheme.OLIVE == 5


def test_image_position_enum_values():
    assert ImagePosition.FULL_PAGE == "full_page"
    assert ImagePosition.TOP == "top"
    assert ImagePosition.BOTTOM == "bottom"
    assert ImagePosition.LEFT == "left"
    assert ImagePosition.RIGHT == "right"
    assert ImagePosition.CENTER == "center"
    assert ImagePosition.TOP_LEFT == "top_left"
    assert ImagePosition.TOP_RIGHT == "top_right"
    assert ImagePosition.BOTTOM_LEFT == "bottom_left"
    assert ImagePosition.BOTTOM_RIGHT == "bottom_right"


def test_brochure_unique_constraint_on_property_version(
    db_session, realtor1, fi_property1
):
    brochure1 = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        version_number=1,
        theme=CoverTheme.WHITE,
    )
    db_session.add(brochure1)
    db_session.flush()

    brochure2 = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        version_number=1,
        theme=CoverTheme.WHITE,
    )
    db_session.add(brochure2)

    with pytest.raises(Exception):
        db_session.flush()


def test_brochure_allows_different_versions_for_same_property(
    db_session, realtor1, fi_property1
):
    brochure1 = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        version_number=1,
        theme=CoverTheme.WHITE,
    )
    db_session.add(brochure1)
    db_session.flush()

    brochure2 = Brochure(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
        version_number=2,
        theme=CoverTheme.WHITE,
    )
    db_session.add(brochure2)
    db_session.flush()

    assert brochure1.id != brochure2.id
    assert brochure1.version_number != brochure2.version_number
