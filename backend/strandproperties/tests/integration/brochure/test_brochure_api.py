from unittest.mock import patch

from strandproperties.schemas.brochure import GenerateBrochureRequest


def test_generate_brochure_pdf_with_invalid_property_id(brochure_route_url, pdf_api):
    request_data = GenerateBrochureRequest(
        property_id=999999,
        realtor_id=1,
    ).model_dump()

    pdf_api(
        "post",
        brochure_route_url("brochure.generate_pdf"),
        request_data,
        status=400,
    )


def test_generate_brochure_pdf_success_minimal_request(
    brochure_route_url, pdf_api, fi_property1, realtor1
):
    request_data = GenerateBrochureRequest(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
    ).model_dump()

    with patch(
        "strandproperties.services.brochure.service.BrochureService.generate_brochure_pdf",
        return_value=b"fake_pdf",
    ):
        response = pdf_api(
            "post",
            brochure_route_url("brochure.generate_pdf"),
            request_data,
            status=200,
        )

        assert response.content_type == "application/pdf"
        assert "brochure_" in response.headers["Content-Disposition"]


def test_generate_brochure_pdf_fails_for_invalid_request_data(
    brochure_route_url, pdf_api
):
    invalid_request = {
        "property_id": "not_an_integer",
        "realtor_id": None,
    }

    pdf_api(
        "post",
        brochure_route_url("brochure.generate_pdf"),
        invalid_request,
        status=400,
    )


def test_save_brochure_draft_success_minimal_request(
    brochure_route_url, api, fi_property1, realtor1
):
    request_data = GenerateBrochureRequest(
        property_id=fi_property1.id,
        realtor_id=realtor1.id,
    ).model_dump()

    with patch("strandproperties.services.brochure.service.create_event_log"):
        response = api(
            "post",
            brochure_route_url("brochure.save_brochure_draft"),
            request_data,
            status=200,
        )

        assert response["message"] == "Brochure draft saved"
        assert response["property_id"] == fi_property1.id
        assert response["version_number"] == 1


def test_save_brochure_draft_fails_for_invalid_request_data(brochure_route_url, api):
    invalid_request = {
        "property_id": "not_an_integer",
        "realtor_id": None,
    }

    api(
        "post",
        brochure_route_url("brochure.save_brochure_draft"),
        invalid_request,
        status=400,
    )
