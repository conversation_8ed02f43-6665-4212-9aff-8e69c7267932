import json
from datetime import datetime, timedelta, timezone
from typing import Callable
from unittest.mock import patch

import pytest
from sqlalchemy import select
from sqlalchemy.orm import Session

from strandproperties.config import app_cfg
from strandproperties.constants import (
    AttachmentDocumentType,
    CountryCode,
    Currency,
    DataSource,
    DocumentType,
    DoSCommissionType,
    DoSDepositAccountType,
    EventFormat,
    EventType,
    Language,
    ListingTypeEnum,
    RoleType,
    SigningMethod,
    SowiseStatus,
    StatusResetCode,
    TemplateType,
    VisibilityType,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.sqs_listener import SQSListener
from strandproperties.models.activity import Activity
from strandproperties.models.auth import ResetCode
from strandproperties.models.company import Company
from strandproperties.models.contact import Contact, ContactPhone, ContactRealtor
from strandproperties.models.details_of_sale import (
    DetailsOfSale,
    DetailsOfSaleInvoice,
    RealtorDetailsOfSale,
    SellerDetailsOfSale,
)
from strandproperties.models.dias import DiasAttachment, SharedTrade
from strandproperties.models.document import (
    Document,
    DocumentAttachments,
    PropertyDocument,
)
from strandproperties.models.event import Event
from strandproperties.models.group import Group
from strandproperties.models.image import Image
from strandproperties.models.lead import Lead, LeadUser
from strandproperties.models.listing_type import ListingType
from strandproperties.models.match_making import MatchMaking
from strandproperties.models.offer import BuyerOffer, Offer
from strandproperties.models.office import Office
from strandproperties.models.organization import Organization
from strandproperties.models.property import Property, PropertyRealtor, PropertyType
from strandproperties.models.raw_lead_data import RawLeadData
from strandproperties.models.sales_agreement import SalesAgreement
from strandproperties.models.tag import Tag
from strandproperties.models.template import Template
from strandproperties.models.user import Role, User
from strandproperties.models.video import Stream, Tour
from strandproperties.schemas.dias_shared_trade import (
    AttachmentTypeSharedTrade,
    SharesType,
    TradePhase,
)
from strandproperties.services.cognito_service import CognitoService
from strandproperties.tests.integration.fi_property.conftest import *
from strandproperties.views.user import UserApi

DIAS_DEMO_API_KEY = "jdfZ9iVFTq}UNWeC3R7x7zFEPiEhka"


@pytest.fixture
def mock_send_set_password_email(mocker):
    mocker.patch.object(
        UserApi,
        "send_set_password_email",
        return_value=None,
    )


@pytest.fixture
def mock_fernet_key_string(mocker):
    app_cfg.fernet_key_string = "nYJ2zpKkPChgCrHiWyRzlQRrZhJYSaQEV6o3cFGJjk8="


@pytest.fixture
def mock_get_s3_file_content(mocker):

    mocker.patch.object(
        S3Service,
        "get_content",
        return_value=b"mock file content",
    )
    mocker.patch.object(
        S3Service,
        "exists",
        return_value=True,
    )


@pytest.fixture
def mock_upload_s3_file(mocker):

    mocker.patch.object(
        S3Service,
        "upload",
        return_value=None,
    )


@pytest.fixture
def mock_get_s3_file_url(mocker):
    url = "https://strandproperties-documents-dev.s3.amazonaws.com/mock_url"

    mocker.patch.object(
        S3Service,
        "generate_download_url",
        return_value=url,
    )
    return url


@pytest.fixture
def api(app, mocker, admin1, pyramid_request) -> Callable:
    return _create_api_fixture(app, mocker, admin1, pyramid_request, use_json=True)


@pytest.fixture
def api_without_admin(app, mocker, realtor2, pyramid_request) -> Callable:
    return _create_api_fixture(app, mocker, realtor2, pyramid_request, use_json=True)


@pytest.fixture
def api_fi(app, mocker, admin1_organization2, pyramid_request) -> Callable:
    return _create_api_fixture(
        app, mocker, admin1_organization2, pyramid_request, use_json=True
    )


@pytest.fixture
def api_without_json(app, mocker, admin1, pyramid_request) -> Callable:
    return _create_api_fixture(app, mocker, admin1, pyramid_request, use_json=False)


def _create_api_fixture(
    app, mocker, admin1, pyramid_request, use_json: bool
) -> Callable:
    """Creates a reusable API fixture for making requests with or without JSON responses."""

    def _request(method, *args, **kwargs):
        role = _get_role(kwargs, admin1)
        _setup_authentication(mocker, role)

        headers = _setup_headers(kwargs, role)
        method_func = _get_method_func(app, method)

        response = method_func(*args, **kwargs, headers=headers)
        return (
            response.json if use_json and response.status_code != 204 else response
        )  # Apply `.json` only when needed

    return _request


def _get_role(kwargs, admin1):
    """Determines the role to use for the request."""
    if "role" in kwargs:
        return kwargs.pop("role")
    return admin1._roles[0]


def _setup_authentication(mocker, role):
    """Mocks authentication mechanisms for API requests."""
    mocker.patch(
        "strandproperties.services.cognito_service.CognitoService.verify_id_token",
        return_value={"sub": role.user.cognito_sub},
    )

    mocker.patch(
        "strandproperties.security.cognito_authentication_policy.CognitoAuthenticationPolicy.effective_principals",
        return_value=[
            "system.Everyone",
            "system.Authenticated",
            f"role:{role.role}",
            f"user:{str(role.user.cognito_sub)}",
        ],
    )

    mocker.patch(
        "strandproperties.security.cognito_authentication_policy.CognitoAuthenticationPolicy.unauthenticated_userid",
        return_value=str(role.user.cognito_sub),
    )


def _setup_headers(kwargs, role):
    """Sets up headers for the API request."""
    headers = {
        "Authorization": f"Bearer mock-data-bearer-token",
        "X-Organization-Id": str(role.organization_id),
    }
    if "headers" in kwargs:
        headers.update(kwargs.pop("headers"))
    return headers


def _get_method_func(app, method):
    """Maps method string to the correct function in the test app."""
    method = method.lower()
    method_map = {
        "get": "get",
        "post_not_json": "post",
        "post": "post_json",
        "put": "put_json",
        "patch": "patch_json",
        "delete": "delete_json",
    }
    if method not in method_map:
        raise ValueError("Invalid method")
    return getattr(app, method_map[method])


@pytest.fixture
def unauthenticated_api_request(app):
    def _request(method, *args, **kwargs):
        headers = {}
        if "headers" in kwargs:
            headers = {**kwargs.pop("headers")}

        method = method.lower()
        method_map = {
            "get": "get",
            "post_not_json": "post",
            "post": "post_json",
            "put": "put_json",
            "patch": "patch_json",
            "delete": "delete_json",
        }
        if method not in method_map.keys():
            raise ValueError("Invalid method")

        return getattr(app, method_map[method])(
            *args,
            **kwargs,
            headers=headers,
        ).json

    return _request


@pytest.fixture
def user_factory(db_session: Session):
    def _user(email, **kwargs):
        user = User(
            email=email,
            cognito_sub=email,  # We use the email as a dummy for the cognito_sub
            first_name=kwargs.get("first_name", ""),
            last_name=kwargs.get("last_name", ""),
            is_active=kwargs.get("is_active", True),
            is_verified=kwargs.get("is_verified", True),
            is_superadmin=kwargs.get("is_superadmin", False),
            offices=kwargs.get("offices", []),
            tags=kwargs.get("tags", []),
            photo_url=kwargs.get("photo_url"),
            slug=kwargs.get("slug"),
            details=kwargs.get("details", {}),
            company=kwargs.get("company", None),
            company_id=kwargs.get("company_id", None),
            social_security_number=kwargs.get("social_security_number", "010101-123A"),
        )
        user.set_password(email)

        db_session.add(user)
        db_session.flush()

        if kwargs.get("role") and kwargs.get("organization"):
            role = Role(
                user_id=user.id,
                organization_id=kwargs.get("organization").id,
                role=kwargs.get("role"),
            )
            db_session.add(role)
            db_session.flush()

        return db_session.scalars(select(User).where(User.email == email)).one()

    return _user


@pytest.fixture
def group_factory(db_session: Session):
    def _group(name, **kwargs):
        group = Group(
            name=name,
            description=kwargs.get("description", ""),
            language=kwargs.get("language", Language.ENGLISH),
            organization_id=kwargs.get("organization").id,
            tags=kwargs.get("tags", []),
            assigned_to_users=kwargs.get("realtors", []),
        )

        db_session.add(group)
        db_session.flush()

        return db_session.scalars(select(Group).where(Group.name == name)).one()

    return _group


@pytest.fixture
def mock_sqs_factory(mocker):
    """
    SQSListener mock fixture factory

    Example:

        body = {"capture": Captures.FACEBOOK_CAPTURE}
        sqs_listener = mock_sqs_factory(body)
    """

    def _sqs(message_body: dict):
        mock_sqs = mocker.Mock()
        mock_sqs.receive_message = mocker.Mock(
            return_value={
                "Messages": [
                    {
                        "MessageId": "mock_message_id",
                        "ReceiptHandle": "mock_receipt_handle",
                        "MD5OfBody": "mock_md5",
                        "Body": json.dumps(message_body),
                    }
                ],
            }
        )

        listener = SQSListener()
        listener.queue_url = "mock_queue_url"
        listener.sqs = mock_sqs

        return listener

    return _sqs


@pytest.fixture
def admin1(user_factory, organization1) -> User:
    return user_factory(
        "<EMAIL>",
        role=RoleType.ADMIN.value,
        organization=organization1,
    )


@pytest.fixture
def admin_es_1(user_factory, organization1) -> User:
    return user_factory(
        "<EMAIL>",
        role=RoleType.ADMIN.value,
        organization=organization1,
    )


@pytest.fixture
def admin1_organization2(user_factory, organization2):
    return user_factory(
        "<EMAIL>",
        role=RoleType.ADMIN.value,
        organization=organization2,
    )


@pytest.fixture
def realtor1(user_factory, organization1) -> User:
    return user_factory(
        "<EMAIL>",
        first_name="Bill",
        last_name="Nguyen",
        role=RoleType.REALTOR,
        organization=organization1,
        photo_url="https://www.example.com/photo_url",
        roaiib="roaiib",
        liability_insurance="liability_insurance",
    )


@pytest.fixture
def realtor_es_1(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        first_name="Bill",
        last_name="Nguyen",
        role=RoleType.REALTOR,
        organization=organization1,
        photo_url="https://www.example.com/photo_url",
    )


@pytest.fixture
def group1(group_factory, organization1, realtor1) -> Group:
    return group_factory(
        "Group 1",
        description="Description of group 1",
        language=Language.ENGLISH,
        organization=organization1,
        realtors=[realtor1],
    )


@pytest.fixture
def group2(group_factory, organization1) -> Group:
    return group_factory(
        "Group 2",
        description="Description of group 2",
        language=Language.ENGLISH,
        organization=organization1,
        tags=[Tag(name="Tag 1"), Tag(name="Tag 2")],
    )


@pytest.fixture
def group3(group_factory, organization1, realtor3) -> Group:
    return group_factory(
        "Group 3",
        description="Description of group 3",
        language=Language.ENGLISH,
        organization=organization1,
        realtors=[realtor3],
    )


@pytest.fixture
def group4(group_factory, organization1, realtor3) -> Group:
    return group_factory(
        "Group 4",
        description="Description of group 4",
        language=Language.ENGLISH,
        organization=organization1,
        realtors=[realtor3],
    )


@pytest.fixture
def realtor1_organization2(user_factory, organization2, company_1):
    user = user_factory(
        "<EMAIL>",
        first_name="Bill Org 2",
        last_name="Nguyen",
        role=RoleType.REALTOR,
        organization=organization2,
        company=company_1,
        company_id=company_1.id,
        photo_url="https://www.example.com/photo_url",
    )
    user.set_dias_api_key(DIAS_DEMO_API_KEY)
    return user


@pytest.fixture
def realtor2_organization2(user_factory, organization2):
    user = user_factory(
        "<EMAIL>",
        first_name="John Org 2",
        last_name="Nguyen",
        role=RoleType.REALTOR,
        organization=organization2,
        photo_url="https://www.example.com/photo_url",
    )
    user.set_dias_api_key(DIAS_DEMO_API_KEY)
    return user


@pytest.fixture
def realtor2(user_factory, organization1, office1) -> User:
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[office1],
        slug="realtor_2",
    )


@pytest.fixture
def realtor3(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[Office(name="Sample Office")],
    )


@pytest.fixture
def realtor7(user_factory, organization1, office1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.ADMIN,
        organization=organization1,
        offices=[office1],
        slug="realtor_7",
    )


@pytest.fixture(scope="function")
def contact1(db_session):
    contact = Contact(
        name="Jane Doe",
        organization_id=1,
        email="<EMAIL>",
        phones=[],
        company=None,
        website=None,
        address=None,
        city=None,
        post_code=None,
        country=None,
        preferred_language=None,
        passport_number=None,
        nationality=None,
        country_specific={},
        notes=None,
        source="Hubspot",
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
        status=None,
    )
    db_session.add(contact)
    db_session.commit()
    return contact


@pytest.fixture(scope="function")
def contact1_organization2(db_session):
    contact = Contact(
        name="Jane Doe",
        organization_id=2,
        email="<EMAIL>",
        phones=[],
        company=None,
        website=None,
        address=None,
        city=None,
        post_code=None,
        country=None,
        preferred_language=None,
        passport_number=None,
        nationality=None,
        country_specific={},
        notes=None,
        source="Hubspot",
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
        status=None,
    )
    db_session.add(contact)
    db_session.commit()
    return contact


@pytest.fixture(scope="function")
def contact1_organization2_group4(db_session, group4):
    contact = Contact(
        name="Jane Doe",
        organization_id=1,
        email="<EMAIL>",
        phones=[],
        company=None,
        website=None,
        address=None,
        city=None,
        post_code=None,
        country=None,
        preferred_language=None,
        passport_number=None,
        nationality=None,
        country_specific={},
        notes=None,
        source="Hubspot",
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
        status=None,
        groups=[group4],
    )
    db_session.add(contact)
    db_session.commit()
    return contact


@pytest.fixture
def realtor4(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[Office(name="Potato Office")],
        details={"serviceform_booking_calendar_id": "1", "videobot_id": "1"},
    )


@pytest.fixture(scope="function")
def contact_realtor(db_session, contact1, realtor4):
    contact_realtor = ContactRealtor(
        contact_id=contact1.id,
        realtor_id=realtor4.id,
    )
    db_session.add(contact_realtor)
    db_session.commit()
    return contact_realtor


@pytest.fixture
def realtor5(user_factory, organization2):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization2,
        details={"serviceform_booking_calendar_id": "1", "videobot_id": "1"},
    )


@pytest.fixture
def unactive_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[Office(name="Potato Office")],
        is_active=False,
    )


@pytest.fixture
def realtor4(db_session, user_factory, organization1):
    palma_office = db_session.query(Office).filter_by(id=3).first()
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[palma_office],
    )


@pytest.fixture
def px8_fi_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        is_active=False,
    )


@pytest.fixture
def reaktor_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        is_active=False,
    )


@pytest.fixture
def loisto_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        is_active=False,
    )


@pytest.fixture
def reactron_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        is_active=False,
    )


@pytest.fixture
def normal_active_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[
            Office(name="Tomato Office"),
            Office(name="ES Office"),
            Office(name="Support Customer"),
            Office(name="Manager"),
        ],
        tags=[
            Tag(name="Tomato Office"),
            Tag(name="ES Office"),
            Tag(name="Support Customer"),
            Tag(name="Manager"),
        ],
        photo_url="https://www.example.com/photo_url",
    )


@pytest.fixture
def normal_unactive_realtor(user_factory, organization1):
    return user_factory(
        "<EMAIL>",
        role=RoleType.REALTOR,
        organization=organization1,
        offices=[
            Office(name="Potato Office"),
            Office(name="Madrid Office"),
            Office(name="Devilery Manager"),
            Office(name="Support"),
        ],
        tags=[
            Tag(name="Potato Office"),
            Tag(name="Madrid Office"),
            Tag(name="Devilery Manager"),
            Tag(name="Support"),
        ],
        is_active=False,
        photo_url="https://www.example.com/photo_url",
    )


@pytest.fixture
def photographer1(user_factory, organization1: Organization) -> User:
    return user_factory(
        "<EMAIL>",
        role=RoleType.PHOTOGRAPHER,
        organization=organization1,
    )


@pytest.fixture
def photographer1_organization2(user_factory, organization2: Organization) -> User:
    return user_factory(
        "<EMAIL>",
        role=RoleType.PHOTOGRAPHER,
        organization=organization2,
    )


@pytest.fixture
def property_type1(db_session: Session) -> PropertyType:
    property_type = PropertyType(category="House", name="Town House", origin="Resales")

    db_session.add(property_type)
    db_session.flush()

    return property_type


@pytest.fixture
def property1(
    db_session: Session, organization1: Organization, admin1: User, realtor1: User
) -> Property:
    property = Property(
        reference="Property1",
        title="Property 1",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=1,
        longitude=10.0,
        latitude=20.0,
    )

    property.realtor_users = [admin1, realtor1]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property2(
    db_session: Session, organization1: Organization, admin1: User, realtor2: User
) -> Property:
    property = Property(
        reference="Property2",
        title="Property 2",
        organization_id=organization1.id,
        property_type_id=2,  # Assuming a different property type ID
        area_level_1_id=2,  # Assuming a different area level ID
        is_exclusive=False,
        status="Draft",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.RENT_LONG)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {
                "address": "Another Street",
                "postCode": "54321-765",
                "city": "Madrid",
            }
        },
        built_area=150,
        bedrooms=4,
        bathrooms=3,
        price_sale=2,
        longitude=30.0,
        latitude=40.0,
    )

    property.realtor_users = [admin1, realtor2]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property3(db_session, organization1, admin1, realtor2):
    property = Property(
        reference="SPTEST2",
        title="Property 2",
        organization_id=organization1.id,
        property_type_id=2,  # Assuming a different property type ID
        area_level_1_id=2,  # Assuming a different area level ID
        is_exclusive=False,
        status="Draft",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.RENT_LONG)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {
                "address": "Another Street",
                "postCode": "54321-765",
                "city": "Madrid",
            }
        },
        built_area=150,
        bedrooms=4,
        bathrooms=3,
        price_sale=2,
        longitude=30.0,
        latitude=40.0,
    )

    property.realtor_users = []

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property4(
    db_session: Session, organization1: Organization, admin1: User, realtor2: User
) -> Property:
    property = Property(
        reference="Property4",
        title="Property 4",
        organization_id=organization1.id,
        property_type_id=2,  # Assuming a different property type ID
        area_level_1_id=2,  # Assuming a different area level ID
        area_level_2_id=3,
        is_exclusive=False,
        status="Draft",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.RENT_LONG)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {
                "address": "Another Street",
                "postCode": "54321-765",
                "city": "Madrid",
            }
        },
        built_area=150,
        bedrooms=4,
        bathrooms=3,
        price_sale=2,
        longitude=30.0,
        latitude=40.0,
    )

    property.realtor_users = [admin1, realtor2]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property_es_1(
    db_session: Session,
    organization1: Organization,
    admin_es_1: User,
    realtor_es_1: User,
) -> Property:
    property = Property(
        reference="PropertyES1",
        title="Property ES 1",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=100000,
        longitude=10.0,
        latitude=20.0,
    )

    property.realtor_users = [admin_es_1, realtor_es_1]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property_es_1_old(
    db_session: Session,
    organization1: Organization,
    admin_es_1: User,
    realtor_es_1: User,
) -> Property:
    property = Property(
        reference="PropertyES1Old",
        title="Property ES 1 Old",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=100000,
        longitude=10.0,
        latitude=20.0,
        created_at=datetime.now(timezone.utc) - timedelta(days=1),
    )

    property.realtor_users = [admin_es_1, realtor_es_1]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property1_resales(db_session, organization1, admin1, realtor1):
    property = Property(
        reference="Property1-resales",
        title="Property 1",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        data_source=DataSource.RESALES_ONLINE,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=1,
        longitude=10.0,
        latitude=20.0,
    )

    property.realtor_users = []

    db_session.add(property)
    db_session.flush()
    return property


@pytest.fixture
def property1_not_for_sale(db_session, organization1):
    property = Property(
        reference="Property1",
        title="Property 1",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.RENT_LONG)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=1,
        longitude=10.0,
        latitude=20.0,
    )

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property_published_to_rightmove(db_session, organization1, admin1, realtor1):
    property = Property(
        reference="PropertyRightMove",
        title="Property Right Move",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        data_source=DataSource.STRAND,
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
        built_area=100,
        bedrooms=3,
        bathrooms=2,
        price_sale=1,
        longitude=10.0,
        latitude=20.0,
        portals={"is_rightmove_enabled": True},
    )

    property.realtor_users = [admin1, realtor1]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property1_strandwebsite_integration(db_session, organization1, realtor1):
    property = Property(
        reference="Prop1Strand",
        title="Property 1 StrandWebsite Integration",
        organization_id=organization1.id,
        property_type_id=1,
        area_level_1_id=1,
        is_exclusive=True,
        status="Published",
        portals={"is_strandproperties_enabled": True},
        listing_types=[
            db_session.scalars(
                select(ListingType).where(ListingType.name == ListingTypeEnum.SALE)
            ).one()
        ],
        private_info={
            "location": {"address": "Street", "postCode": "12345-567", "city": "Málaga"}
        },
    )

    property.realtor_users = [realtor1]

    db_session.add(property)
    db_session.flush()

    return property


@pytest.fixture
def property1_realtor2(db_session, property1, realtor2):
    property_realtor = PropertyRealtor(property_id=property1.id, user_id=realtor2.id)

    db_session.add(property_realtor)
    db_session.flush()

    return property_realtor


@pytest.fixture
def image1(db_session, property1):
    image = Image(
        property_id=property1.id,
        order=1,
        url="http://localhost/test.png",
        is_hidden=False,
    )

    db_session.add(image)
    db_session.flush()

    return image


@pytest.fixture
def image2(db_session, property1):
    image = Image(
        property_id=property1.id,
        order=2,
        url="http://localhost/test2.png",
        is_hidden=False,
    )

    db_session.add(image)
    db_session.flush()

    return image


@pytest.fixture
def images1(db_session, property1):
    img1 = Image(
        property_id=property1.id,
        order=0,
        url="http://localhost/test1.png",
        is_hidden=True,
    )
    img2 = Image(
        property_id=property1.id,
        order=1,
        url="http://localhost/test2.png",
        is_hidden=False,
    )
    img3 = Image(
        property_id=property1.id,
        order=2,
        url="http://localhost/test3.png",
        is_hidden=False,
    )

    images = [img1, img2, img3]

    db_session.add_all(images)
    db_session.flush()

    return images


@pytest.fixture
def streams1(db_session, property1):
    stream1 = Stream(
        property_id=property1.id,
        url="http://youtube/test1",
        is_hidden=True,
    )
    stream2 = Stream(
        property_id=property1.id,
        url="http://youtube/test2",
        is_hidden=False,
    )
    stream3 = Stream(
        property_id=property1.id,
        url="http://youtube/test3",
        is_hidden=False,
    )

    streams = [stream1, stream2, stream3]

    db_session.add_all(streams)
    db_session.flush()

    return streams


@pytest.fixture
def tours1(db_session, property1):
    tour1 = Tour(
        property_id=property1.id,
        url="https://floorfy.com/tour/test1",
        is_hidden=True,
    )
    tour2 = Tour(
        property_id=property1.id,
        url="https://floorfy.com/tour/test2",
        is_hidden=False,
    )
    tour3 = Tour(
        property_id=property1.id,
        url="https://floorfy.com/tour/test3",
        is_hidden=False,
    )

    tours = [tour1, tour2, tour3]

    db_session.add_all(tours)
    db_session.flush()

    return tours


@pytest.fixture(scope="function")
def expired_reset_code(admin1, db_session):
    expired_code = ResetCode(
        user_id=admin1.id,
        code="expiredcode123",
        created_at=datetime.utcnow() - timedelta(hours=3),
        expired_at=datetime.utcnow() - timedelta(hours=1),
        status=StatusResetCode.expired,
    )
    db_session.add(expired_code)
    db_session.flush()
    return expired_code


@pytest.fixture
def organization1_sowise_owner() -> str:
    return "<EMAIL>"


@pytest.fixture
def organization1(db_session, organization1_sowise_owner) -> Organization:
    """
    Get the first organization in the database, which should be populated
    by seed script
    """
    org = db_session.scalar(select(Organization).where(Organization.id == 1))
    org.details["sowise_owner"] = organization1_sowise_owner

    db_session.add(org)
    db_session.flush()

    return org


@pytest.fixture
def organization2(db_session, organization1_sowise_owner) -> Organization:
    org2 = Organization(
        name="Test Organization 2",
        currency=Currency.EUR,
        language="fi",
        country_code=CountryCode.FINLAND,
        details={},
    )
    org2.details["sowise_owner"] = organization1_sowise_owner

    db_session.add(org2)
    db_session.flush()
    db_session.refresh(org2)

    return org2


@pytest.fixture
def contact1(db_session, realtor1, organization1):
    contact = Contact(
        name="John Doe",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor1],
        organization_id=organization1.id,
        subscription_id="123",
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact_es_1(db_session, realtor_es_1, organization1):
    contact = Contact(
        name="John Doe",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor_es_1],
        organization_id=organization1.id,
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact_dev_test(db_session, realtor_es_1, organization1):
    contact = Contact(
        name="Dev Test",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor_es_1],
        organization_id=organization1.id,
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact3(db_session, realtor1, organization2):
    contact = Contact(
        name="lead process",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor1],
        organization_id=organization2.id,
        nationality="FI",
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact4(db_session, realtor1, organization2):
    contact = Contact(
        name="lead process",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        organization_id=organization2.id,
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact5(db_session, realtor1, realtor2, organization2):
    contact = Contact(
        name="lead process",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor1, realtor2],
        organization_id=organization2.id,
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact1_organization2(db_session, realtor1_organization2, organization2):
    contact = Contact(
        name="John Doe",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123")],
        assigned_to_users=[realtor1_organization2],
        organization_id=organization2.id,
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def contact2(db_session: Session, organization1):
    contact = Contact(
        name="Huy",
        email="<EMAIL>",
        phones=[ContactPhone(phone_number="+123 555")],
        organization_id=organization1.id,
        nationality="IS",
    )

    db_session.add(contact)
    db_session.flush()
    db_session.refresh(contact)

    return contact


@pytest.fixture
def lead1(db_session, organization1, realtor1):
    lead = Lead(
        status="new",
        relevance="cold",
        title="Lead 1",
        description="Description of lead 1",
        type="buying",
        source="email",
        organization_id=organization1.id,
        assigned_to_users=[realtor1],
    )
    db_session.add(lead)
    db_session.flush()
    db_session.refresh(lead)

    return lead


@pytest.fixture
def activity_1(db_session, realtor1, lead1):
    activity_1 = Activity(
        description={
            "description": "Description of activity 1",
        },
        object_type="SALES_ACTIVITY",
        object_id=lead1.id,
        actor_id=realtor1.id,
    )
    db_session.add(activity_1)
    db_session.flush()
    db_session.refresh(activity_1)

    return activity_1


@pytest.fixture
def activity_2(db_session, realtor1, lead2):
    activity_2 = Activity(
        description={
            "description": "Description of activity 1",
        },
        object_type="SALES_ACTIVITY",
        object_id=lead2.id,
        actor_id=realtor1.id,
    )
    db_session.add(activity_2)
    db_session.flush()
    db_session.refresh(activity_2)

    return activity_2


@pytest.fixture
def raw_lead_data1(db_session, contact1, lead1, property1):
    raw_lead_data = RawLeadData(
        source_lead_id="1",
        contact_id=contact1.id,
        sale_activity_id=lead1.id,
        full_name="John Doe",
        phone="+34631689787",
        email="<EMAIL>",
        status="unprocessed",
        source="Facebook",
        is_manual_assign=True,
        property_reference=property1.reference,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
        organization="fi",
        campaign_name="test campaign",
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


@pytest.fixture
def raw_lead_data2(db_session, contact1, lead1):
    raw_lead_data = RawLeadData(
        source_lead_id="2",
        contact_id=contact1.id,
        sale_activity_id=lead1.id,
        full_name="John Doe 2",
        phone="+34631689789",
        email="<EMAIL>",
        status="unprocessed",
        source="Facebook",
        property_reference="SPTEST2",
        is_manual_assign=True,
        is_new_contact=True,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
        organization=None,
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


# lead without the reference
@pytest.fixture
def raw_lead_data_without_reference(db_session, contact1, lead1):
    raw_lead_data = RawLeadData(
        source_lead_id="no-reference",
        contact_id=contact1.id,
        sale_activity_id=lead1.id,
        full_name="John Doe 2",
        phone="+34631689789",
        email="<EMAIL>",
        status="unprocessed",
        source="Facebook",
        property_reference=None,
        is_manual_assign=True,
        is_new_contact=True,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


# lead without the reference
@pytest.fixture
def raw_lead_data_with_404_reference(db_session, contact1, lead1):
    raw_lead_data = RawLeadData(
        source_lead_id="outdate-reference",
        contact_id=contact1.id,
        sale_activity_id=lead1.id,
        full_name="John Doe 2",
        phone="+34631689789",
        email="<EMAIL>",
        status="unprocessed",
        source="Facebook",
        property_reference="CannotFindReference",
        is_manual_assign=True,
        is_new_contact=True,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


@pytest.fixture
def raw_lead_data3(db_session, contact1, lead1, lead2, lead3, property1):
    raw_lead_data = RawLeadData(
        source_lead_id="1",
        contact_id=contact1.id,
        sale_activity_id=lead3.id,
        full_name="John Doe",
        phone="+34631689787",
        email="<EMAIL>",
        status="processed",
        source="Facebook",
        is_new_contact=True,
        property_reference=property1.reference,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


@pytest.fixture
def raw_lead_data4(db_session, contact1, lead1, lead2, lead3, property1):
    raw_lead_data = RawLeadData(
        source_lead_id="4",
        contact_id=contact1.id,
        sale_activity_id=lead3.id,
        full_name="John Doe",
        phone="+34631689787",
        status="unprocessed",
        source="Facebook",
        property_reference=property1.reference,
        is_new_contact=False,
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


@pytest.fixture
def raw_lead_data5(db_session, contact1, lead1, lead2, lead3):
    raw_lead_data = RawLeadData(
        source_lead_id="4",
        contact_id=contact1.id,
        sale_activity_id=lead3.id,
        full_name="John Doe",
        phone="+34631689787",
        status="unprocessed",
        source="Facebook",
        content={
            "budget_range": "500k-1m_€",
            "area_interest": "Almería",
            "kind_of_property": "apartment",
        },
    )
    db_session.add(raw_lead_data)
    db_session.flush()
    db_session.refresh(raw_lead_data)

    return raw_lead_data


@pytest.fixture
def lead3(db_session, organization1, property1, realtor3):
    lead = Lead(
        status="contacted",
        relevance="warm",
        title="Facebook John Doe",
        property_reference=property1.reference,
        description="Description of lead 2",
        type="selling",
        source="phone_sms",
        organization_id=organization1.id,
        assigned_to_users=[realtor3],
    )
    db_session.add(lead)
    db_session.flush()
    db_session.refresh(lead)

    return lead


@pytest.fixture
def lead3_user(db_session, lead3, property1_realtor2):
    lead_user = LeadUser(user_id=property1_realtor2.user_id, lead_id=lead3.id)
    db_session.add(lead_user)
    db_session.flush()
    db_session.refresh(lead_user)

    return lead_user


@pytest.fixture
def lead2(db_session, organization1):
    lead = Lead(
        status="contacted",
        relevance="warm",
        title="Lead 2",
        description="Description of lead 2",
        type="selling",
        source="phone_sms",
        organization_id=organization1.id,
    )
    db_session.add(lead)
    db_session.flush()
    db_session.refresh(lead)

    return lead


@pytest.fixture
def lead1_organization2(db_session, organization2, realtor1_organization2):
    lead = Lead(
        status="new",
        relevance="cold",
        title="Lead 1 Org 2",
        description="Description of lead 1 org2",
        type="buying",
        source="email",
        organization_id=organization2.id,
        assigned_to_users=[realtor1_organization2],
    )
    db_session.add(lead)
    db_session.flush()
    db_session.refresh(lead)

    return lead


@pytest.fixture
def event1(
    db_session: Session,
    organization1: Organization,
    realtor1: User,
    property1: Property,
):
    event = Event(
        event_type=EventType.PRIVATE_VIEWING,
        event_format=EventFormat.VIRTUAL,
        title="Event 1",
        descriptions={"en": "Description of event 1"},
        internal_notes="Internal note",
        start_time=datetime.now(),
        end_time=datetime.now() + timedelta(hours=1),
        location="Location 1",
        url="http://localhost/test",
        additional_details={"key": "value"},
        organization_id=organization1.id,
    )
    event.users.append(realtor1)
    event.properties.append(property1)
    event.validate_associations()
    db_session.add(event)
    db_session.commit()

    return event


@pytest.fixture
def event2(db_session, organization1, realtor1, property1):
    event = Event(
        event_type="client_meeting",
        event_format="virtual",
        title="Event 2",
        descriptions={"en": "Description of event 2"},
        internal_notes="Internal note",
        start_time=datetime.now() + timedelta(days=1),
        end_time=datetime.now() + timedelta(days=1, hours=1),
        location="Location 2",
        url="http://localhost/test2",
        additional_details={"key": "value"},
        organization_id=organization1.id,
    )
    event.users.append(realtor1)
    event.properties.append(property1)
    event.validate_associations()
    db_session.add(event)
    db_session.commit()
    return event


@pytest.fixture
def event1_organization2(
    db_session: Session,
    organization2: Organization,
    realtor1_organization2: User,
    property1: Property,
):
    event = Event(
        event_type="private_viewing",
        event_format="virtual",
        title="Event 1 Org 2",
        descriptions={"en": "Description of event 1 org 2"},
        internal_notes="Internal note",
        start_time=datetime.now(),
        end_time=datetime.now() + timedelta(hours=1),
        location="Location 2",
        url="http://localhost/test3",
        additional_details={"key": "value"},
        organization_id=organization2.id,
    )
    event.users.append(realtor1_organization2)
    event.properties.append(property1)
    event.validate_associations()
    db_session.add(event)
    db_session.commit()
    return event


@pytest.fixture
def document1(db_session):
    document = Document(
        name="document1",
        type=DocumentType.SALES_AGREEMENT,
        visibility=VisibilityType.PUBLIC,
        sowise_id="abcdef0123456789",
        status=SowiseStatus.DRAFT,
        language=Language.ENGLISH,
    )

    db_session.add(document)
    db_session.flush()
    db_session.refresh(document)

    return document


@pytest.fixture
def signed_offer_document(db_session):
    document = Document(
        name="signed_offer_document",
        type=DocumentType.OFFER,
        visibility=VisibilityType.PUBLIC,
        sowise_id="signed_offer_document",
        status=SowiseStatus.SIGNED,
        language=Language.ENGLISH,
    )

    db_session.add(document)
    db_session.flush()
    db_session.refresh(document)

    return document


@pytest.fixture
def property_signed_offer_document(db_session, property2, signed_offer_document):
    property_document = PropertyDocument(
        property_id=property2.id,
        document_id=signed_offer_document.id,
    )

    db_session.add(property_document)
    db_session.flush()
    db_session.refresh(property_document)

    return property_document


@pytest.fixture
def signed_sales_agreement_document(db_session):
    document = Document(
        name="signed_sales_agreement_document",
        type=DocumentType.SALES_AGREEMENT,
        visibility=VisibilityType.PUBLIC,
        sowise_id="signed_sales_agreement_document",
        status=SowiseStatus.SIGNED,
        language=Language.ENGLISH,
    )

    db_session.add(document)
    db_session.flush()
    db_session.refresh(document)

    return document


@pytest.fixture
def property_signed_sales_agreement_document(
    db_session, property2, signed_sales_agreement_document
):
    property_document = PropertyDocument(
        property_id=property2.id,
        document_id=signed_sales_agreement_document.id,
    )

    db_session.add(property_document)
    db_session.flush()
    db_session.refresh(property_document)

    return property_document


@pytest.fixture
def sales_agreement1(db_session, contact1, property_signed_sales_agreement_document):
    sales_agreement = SalesAgreement(
        property_document_id=property_signed_sales_agreement_document.id,
        first_seller_id=contact1.id,
        validity_in_months=4,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
    )

    db_session.add(sales_agreement)
    db_session.flush()
    db_session.refresh(sales_agreement)

    return sales_agreement


@pytest.fixture
def signed_details_of_sale_document(db_session):
    document = Document(
        name="document1",
        type=DocumentType.DETAILS_OF_SALE,
        visibility=VisibilityType.PUBLIC,
        sowise_id="abcdef0123456789",
        status=SowiseStatus.SIGNED,
        language=Language.ENGLISH,
    )

    db_session.add(document)
    db_session.flush()
    db_session.refresh(document)

    return document


@pytest.fixture
def template1(db_session):
    template = Template(
        name="template1",
        type=TemplateType.SALES_AGREEMENT_EXCLUSIVE,
        sowise_id="abcdef0123456789",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def template_offer(db_session: Session):
    template = Template(
        name="template_offer",
        type=TemplateType.OFFER,
        sowise_id="abcdef0123456999",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def template_details_of_sale(db_session: Session):
    template = Template(
        name="template_details_of_sale",
        type=TemplateType.DETAILS_OF_SALE,
        sowise_id="abcdef0123456999",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def template_palma_office(db_session):
    template = Template(
        name="template_palma_office",
        type=TemplateType.SALES_AGREEMENT_EXCLUSIVE_PALMA_OFFICE,
        sowise_id="abcdef0123456789",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def template_dos_invoice(db_session):
    template = Template(
        name="template_dos_invoice",
        type=TemplateType.DOS_INVOICE,
        sowise_id="abcdef0123456999",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def template_dos_invoice_proforma(db_session):
    template = Template(
        name="template_dos_invoice",
        type=TemplateType.DOS_PROFORMA,
        sowise_id="abcdef0123456999",
        language=Language.ENGLISH,
    )

    db_session.add(template)
    db_session.flush()
    db_session.refresh(template)

    return template


@pytest.fixture
def office1(db_session: Session):
    office = Office(name="Potato Office")

    db_session.add(office)
    db_session.flush()
    db_session.refresh(office)

    return office


@pytest.fixture
def details_of_sale_custom_reference_property(
    db_session: Session,
    document1: Document,
    realtor1: User,
    office1: Office,
    contact1: Contact,
    property1: Property,
):
    details_of_sale = DetailsOfSale(
        document_id=document1.id,
        custom_reference_property="custom_reference_property",
        offer_agreed_date="2024-07-19",
        sale_price=0,
        property_id=property1.id,
        deposit_percentage=0,
        deposit_amount=10,
        deposit_account_type=DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT,
        deposit_paid_date="2024-07-19",
        ppc_date="2024-07-19",
        completion_notary_deadline="2024-07-19",
        notary_day_booked="2024-07-19",
        total_commission_amount=0,
        total_commission_type=DoSCommissionType.AMOUNT_PLUS_VAT,
        strand_commission_amount=0,
        strand_commission_type=DoSCommissionType.PERCENT_PLUS_VAT,
        other_agency_commission_amount=0,
        other_agency_commission_type=DoSCommissionType.AMOUNT_VAT_INCLUDED,
        separate_invoice_for_each_seller=True,
        created_by=realtor1.id,
        reviewer_office_id=office1.id,
    )
    db_session.add(details_of_sale)
    db_session.flush()
    realtor_details_of_sale = RealtorDetailsOfSale(
        user_id=realtor1.id,
        commission_percentage=0.8,
        details_of_sale_id=details_of_sale.id,
    )
    details_of_sale.realtors.append(realtor_details_of_sale)
    db_session.refresh(details_of_sale)

    return details_of_sale


@pytest.fixture
def seller_details_of_sale1(
    db_session, details_of_sale_custom_reference_property, contact1, dos_invoice1
):
    seller = SellerDetailsOfSale(
        details_of_sale_id=details_of_sale_custom_reference_property.id,
        seller_id=contact1.id,
        details_of_sale_invoice_id=dos_invoice1.id,
    )
    db_session.add(seller)
    db_session.flush()
    return seller


@pytest.fixture
def seller_details_of_sale2(
    db_session, details_of_sale_custom_reference_property, contact2, dos_invoice1
):
    seller = SellerDetailsOfSale(
        details_of_sale_id=details_of_sale_custom_reference_property.id,
        seller_id=contact2.id,
        details_of_sale_invoice_id=dos_invoice1.id,
    )
    db_session.add(seller)
    db_session.flush()
    return seller


@pytest.fixture
def dos_invoice1(db_session, details_of_sale_custom_reference_property):
    invoice = DetailsOfSaleInvoice(
        details_of_sale_id=details_of_sale_custom_reference_property.id,
        invoice_date=datetime(2024, 8, 1, 0, 0, 0),
        invoice_due_date=datetime(2024, 8, 1, 0, 0, 0),
    )

    db_session.add(invoice)
    db_session.flush()

    return invoice


@pytest.fixture
def details_of_sale(
    db_session: Session,
    document1: Document,
    realtor1: User,
    property1: Property,
    office1: Office,
):
    details_of_sale = DetailsOfSale(
        document_id=document1.id,
        offer_agreed_date="2024-07-19",
        property_id=property1.id,
        sale_price=0,
        deposit_percentage=0,
        deposit_amount=10,
        deposit_account_type=DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT,
        deposit_paid_date="2024-07-19",
        ppc_date="2024-07-19",
        completion_notary_deadline="2024-07-19",
        notary_day_booked="2024-07-19",
        total_commission_amount=0,
        total_commission_type=DoSCommissionType.AMOUNT_PLUS_VAT,
        strand_commission_amount=0,
        strand_commission_type=DoSCommissionType.PERCENT_PLUS_VAT,
        other_agency_commission_amount=0,
        other_agency_commission_type=DoSCommissionType.AMOUNT_VAT_INCLUDED,
        separate_invoice_for_each_seller=True,
        created_by=realtor1.id,
        reviewer_office_id=office1.id,
    )
    db_session.add(details_of_sale)
    db_session.flush()
    realtor_details_of_sale = RealtorDetailsOfSale(
        user_id=realtor1.id,
        commission_percentage=0.8,
        details_of_sale_id=details_of_sale.id,
    )
    details_of_sale.realtors.append(realtor_details_of_sale)
    db_session.refresh(details_of_sale)

    return details_of_sale


@pytest.fixture
def details_of_sale_for_editting(
    db_session: Session,
    document1: Document,
    realtor1: User,
    property1: Property,
    office1: Office,
):
    details_of_sale = DetailsOfSale(
        document_id=document1.id,
        offer_agreed_date="2024-07-19",
        property_id=property1.id,
        sale_price=0,
        deposit_percentage=0,
        deposit_amount=10,
        deposit_account_type=DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT,
        deposit_paid_date="2024-07-19",
        ppc_date="2024-07-19",
        completion_notary_deadline="2024-07-19",
        notary_day_booked="2024-07-19",
        total_commission_amount=0,
        total_commission_type=DoSCommissionType.AMOUNT_PLUS_VAT,
        strand_commission_amount=0,
        strand_commission_type=DoSCommissionType.PERCENT_PLUS_VAT,
        other_agency_commission_amount=0,
        other_agency_commission_type=DoSCommissionType.AMOUNT_VAT_INCLUDED,
        separate_invoice_for_each_seller=True,
        created_by=realtor1.id,
        reviewer_office_id=office1.id,
    )
    db_session.add(details_of_sale)
    db_session.flush()
    realtor_details_of_sale = RealtorDetailsOfSale(
        user_id=realtor1.id,
        commission_percentage=0.8,
        details_of_sale_id=details_of_sale.id,
    )
    details_of_sale.realtors.append(realtor_details_of_sale)
    db_session.refresh(details_of_sale)

    return details_of_sale


@pytest.fixture
def dos_invoice2(db_session, details_of_sale):
    invoice = DetailsOfSaleInvoice(
        details_of_sale_id=details_of_sale.id,
        invoice_date=datetime(2024, 8, 1, 0, 0, 0),
        invoice_due_date=datetime(2024, 8, 1, 0, 0, 0),
    )

    db_session.add(invoice)
    db_session.flush()

    return invoice


@pytest.fixture
def signed_details_of_sale1(
    db_session: Session,
    signed_details_of_sale_document: Document,
    realtor1: User,
    property1: Property,
    office1: Office,
):
    signed_details_of_sale = DetailsOfSale(
        document_id=signed_details_of_sale_document.id,
        offer_agreed_date="2024-07-19",
        property_id=property1.id,
        sale_price=0,
        deposit_percentage=0,
        deposit_amount=10,
        deposit_account_type=DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT,
        deposit_paid_date="2024-07-19",
        ppc_date="2024-07-19",
        completion_notary_deadline="2024-07-19",
        notary_day_booked="2024-07-19",
        total_commission_amount=0,
        total_commission_type=DoSCommissionType.AMOUNT_PLUS_VAT,
        strand_commission_amount=0,
        strand_commission_type=DoSCommissionType.PERCENT_PLUS_VAT,
        other_agency_commission_amount=0,
        other_agency_commission_type=DoSCommissionType.AMOUNT_VAT_INCLUDED,
        separate_invoice_for_each_seller=True,
        created_by=realtor1.id,
        reviewer_office_id=office1.id,
    )
    db_session.add(signed_details_of_sale)
    db_session.flush()
    realtor_details_of_sale = RealtorDetailsOfSale(
        user_id=realtor1.id,
        commission_percentage=0.8,
        details_of_sale_id=signed_details_of_sale.id,
    )
    signed_details_of_sale.realtors.append(realtor_details_of_sale)
    db_session.refresh(signed_details_of_sale)

    return signed_details_of_sale


@pytest.fixture
def offer_with_property2(
    db_session: Session,
    signed_offer_document: Document,
    realtor1: User,
    property2: Property,
):
    offer = Offer(
        property_id=property2.id,
        sale_price=10,
        deposit_amount=100,
        deposit_payment_paid_to="1",
        price_settled_by="2024-05-23",
        private_purchase_contract_due_date="2024-05-23",
        created_by=realtor1.id,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
        document_id=signed_offer_document.id,
    )

    db_session.add(offer)
    db_session.flush()
    db_session.refresh(offer)

    return offer


@pytest.fixture
def offer_with_property1(
    db_session: Session,
    signed_offer_document: Document,
    realtor1: User,
    property1: Property,
):
    offer = Offer(
        property_id=property1.id,
        sale_price=10,
        deposit_amount=100,
        deposit_payment_paid_to="1",
        price_settled_by="2024-05-23",
        private_purchase_contract_due_date="2024-05-23",
        created_by=realtor1.id,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
        document_id=signed_offer_document.id,
    )

    db_session.add(offer)
    db_session.flush()
    db_session.refresh(offer)

    return offer


@pytest.fixture
def offer1(
    db_session: Session,
    document1: Document,
    realtor1: User,
    property1: Property,
):
    offer = Offer(
        property_id=property1.id,
        sale_price=10,
        deposit_amount=100,
        deposit_payment_paid_to="1",
        price_settled_by="2024-05-23",
        private_purchase_contract_due_date="2024-05-23",
        created_by=realtor1.id,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
        document_id=document1.id,
    )

    db_session.add(offer)
    db_session.flush()
    db_session.refresh(offer)

    return offer


@pytest.fixture
def offer2(
    db_session: Session,
    document1: Document,
    realtor1: User,
    property1_strandwebsite_integration: Property,
    contact1: Contact,
    contact2: Contact,
):
    offer = Offer(
        property_id=property1_strandwebsite_integration.id,
        sale_price=100,
        deposit_amount=100,
        deposit_payment_paid_to="1",
        price_settled_by="2024-05-23",
        private_purchase_contract_due_date="2024-05-23",
        created_by=realtor1.id,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
        created_at=datetime(2024, 5, 5, 0, 0),
        document_id=document1.id,
    )

    offer.buyers.append(contact1)
    offer.sellers.append(contact2)
    db_session.add(offer)
    db_session.flush()
    db_session.refresh(offer)

    return offer


@pytest.fixture
def offer3(
    db_session: Session,
    document1: Document,
    realtor2: User,
    property1_strandwebsite_integration: Property,
):
    offer = Offer(
        property_id=property1_strandwebsite_integration.id,
        sale_price=1000,
        deposit_amount=100,
        deposit_payment_paid_to="1",
        price_settled_by="2024-05-23",
        private_purchase_contract_due_date="2024-05-23",
        created_by=realtor2.id,
        signing_method=SigningMethod.PEN_AND_PAPER.value,
        created_at=datetime(2024, 7, 7, 0, 0),
        document_id=document1.id,
    )

    db_session.add(offer)
    db_session.flush()
    db_session.refresh(offer)

    return offer


@pytest.fixture
def buyer_offer1(
    db_session: Session,
    offer1: Offer,
    contact1: Contact,
):
    buyer_offer = BuyerOffer(
        offer_id=offer1.id,
        buyer_id=contact1.id,
    )
    db_session.add(buyer_offer)
    db_session.flush()
    db_session.refresh(buyer_offer)
    return buyer_offer


@pytest.fixture
def shared_trade_1(
    db_session: Session,
    realtor1_organization2: User,
    fi_property1: FIProperty,
):
    shared_trade_1 = SharedTrade(
        fi_property_id=fi_property1.id,
        attachments=[
            {
                "id": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
                "description": "Kauppakirjan liite 1",
            },
            {
                "id": "465bde84f42d9ff46cb91e1fa8546eb60afd80709",
                "description": "Attachment 2",
                "documentType": AttachmentTypeSharedTrade.HOUSE_MANAGERS_CERTIFICATE,
            },
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "ce39a3ee5e6b4b0d3255bfef95601890afd80304",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Realtor LKV",
            "IBAN": "FI02 3456 7890 1234 56",
            "BIC": "DABAFIHX",
        },
        # sellers=[
        #     {
        #         "person": {
        #             "firstName": "John",
        #             "lastName": "Doe",
        #             "email": "<EMAIL>",
        #             "socialSecurityNumber": "010190-123A",
        #         },
        #         "bankBusinessId": "FI12345678",
        #     }
        # ],
        # buyers=[
        #     {
        #         "person": {
        #             "firstName": "Jane",
        #             "lastName": "Smith",
        #             "email": "<EMAIL>",
        #             "socialSecurityNumber": "020290-456B",
        #         },
        #         "bankBusinessId": "FI87654321",
        #     }
        # ],
        internal_sellers=[
            {
                "id": 12345,
                "sellerType": "Individual",
                "hasRealtorPayment": True,
                "realtorPaymentAmountInEuros": 1000,
                "firstName": "John",
                "lastName": "Doe",
                "email": "<EMAIL>",
                "socialSecurityNumber": "010190-123A",
                "bankBusinessId": "FI12345678",
            }
        ],
        internal_buyers=[
            {
                "id": 12346,
                "buyerType": "Individual",
                "transferTaxAmountInEuros": 1000,
                "firstName": "Jane",
                "lastName": "Smith",
                "email": "<EMAIL>",
                "socialSecurityNumber": "020290-456B",
                "bankBusinessId": "FI87654321",
            }
        ],
        internal_realtor={
            "id": 12347,
            "firstName": "Mike",
            "lastName": "Doe",
            "email": "<EMAIL>",
        },
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator",
            "lastName": "Test",
        },
        initiator_person_id="1235",
        apartment={
            "address": {
                "streetAddress": "Test Street 1",
                "postalCode": "00100",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.PAPER,
            "housingCompany": {
                "name": "Test Housing Company",
                "businessId": "1234567-8",
            },
        },
        trade_initiated_timestamp="2023-10-15T10:00:00Z",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2023, 10, 15, 0, 0),
    )

    db_session.add(shared_trade_1)
    db_session.commit()
    return shared_trade_1


@pytest.fixture
def shared_trade_2(
    db_session: Session,
    realtor1_organization2: User,
    fi_property2: FIProperty,
):
    shared_trade_2 = SharedTrade(
        fi_property_id=fi_property2.id,
        attachments=[
            {
                "id": "attachment2",
                "description": "Test Attachment 2",
                "documentType": AttachmentTypeSharedTrade.SPOUSES_CONSENT,
            }
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "bill_of_sale 2",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Test Bank 2",
            "IBAN": "FI1234567890",
            "BIC": "TESTBIC2",
        },
        sellers=[
            {
                "person": {
                    "firstName": "Johny",
                    "lastName": "Parker",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010180-123A",
                },
                "bankBusinessId": "FI12345678",
            }
        ],
        buyers=[
            {
                "person": {
                    "firstName": "Jenny",
                    "lastName": "Lopez",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "020296-456B",
                },
                "bankBusinessId": "FI87654321",
            }
        ],
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator2",
            "lastName": "Test2",
        },
        initiator_person_id="initiator2",
        apartment={
            "address": {
                "streetAddress": "Test Street 2",
                "postalCode": "00200",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.DIGITAL,
            "housingCompany": {
                "name": "Test Housing Company 2",
                "businessId": "1234567-8",
            },
        },
        trade_initiated_timestamp="2023-11-15T10:00:00Z",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2023, 11, 16, 0, 0),
    )

    db_session.add(shared_trade_2)
    db_session.commit()
    return shared_trade_2


@pytest.fixture
def shared_trade_3(
    db_session: Session,
    realtor2_organization2: User,
    fi_property2: FIProperty,
):
    shared_trade_3 = SharedTrade(
        fi_property_id=fi_property2.id,
        attachments=[
            {
                "id": "attachment3",
                "description": "Test Attachment 3",
                "documentType": AttachmentTypeSharedTrade.SPOUSES_CONSENT,
            }
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "bill_of_sale 3",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Test Bank 3",
            "IBAN": "FI1334567890",
            "BIC": "TESTBIC3",
        },
        sellers=[
            {
                "person": {
                    "firstName": "Johny",
                    "lastName": "Parker",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010180-133A",
                },
                "bankBusinessId": "FI12345678",
            }
        ],
        buyers=[
            {
                "person": {
                    "firstName": "Jenny",
                    "lastName": "Lopez",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "020296-456B",
                },
                "bankBusinessId": "FI87654321",
            }
        ],
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator3",
            "lastName": "Test3",
        },
        initiator_person_id="initiator3",
        apartment={
            "address": {
                "streetAddress": "Test Street 3",
                "postalCode": "00200",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.DIGITAL,
            "housingCompany": {
                "name": "Test Housing Company 3",
                "businessId": "1234567-8",
            },
        },
        trade_initiated_timestamp="2023-11-15T10:00:00Z",
        trade_id="********-0000-0000-0000-********0209",
        created_by_id=realtor2_organization2.id,
        created_at=datetime(2023, 12, 16, 0, 0),
    )

    db_session.add(shared_trade_3)
    db_session.commit()
    return shared_trade_3


@pytest.fixture
def shared_trade_4(
    db_session: Session,
    realtor1_organization2: User,
    fi_property2: FIProperty,
):
    # This shared_trade has trade_id , which means it is initiated to DIAS.
    shared_trade_4 = SharedTrade(
        fi_property_id=fi_property2.id,
        attachments=[
            {
                "id": "attachment4",
                "description": "Test Attachment 4",
                "documentType": AttachmentTypeSharedTrade.TRADE_REGISTER_EXTRACT,
            }
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "bill_of_sale 4",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Test Bank 4",
            "IBAN": "FI1234567890",
            "BIC": "TESTBIC4",
        },
        sellers=[
            {
                "person": {
                    "firstName": "Tommy",
                    "lastName": "Parker",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010180-123A",
                },
                "bankBusinessId": "FI12345678",
            }
        ],
        buyers=[
            {
                "person": {
                    "firstName": "Hanna",
                    "lastName": "Virtanen",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "020295-456B",
                },
                "bankBusinessId": "FI87654321",
            }
        ],
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator4",
            "lastName": "Test2",
        },
        initiator_person_id="initiator4",
        apartment={
            "address": {
                "streetAddress": "Test Street 4",
                "postalCode": "00200",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.DIGITAL,
            "housingCompany": {
                "name": "Test Housing Company 4",
                "businessId": "1234567-8",
            },
        },
        trade_state={
            "tradePhase": TradePhase.INITIATED,
            "tradeReceivedTimestamp": "2023-10-15T10:00:00Z",
            "tradeInitiatedTimestamp": "2023-10-15T10:00:00Z",
        },
        trade_initiated_timestamp="2023-10-15T10:00:00Z",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2023, 10, 10, 0, 0),
        trade_id="********-0000-0000-0000-********0209",
    )

    db_session.add(shared_trade_4)
    db_session.commit()
    return shared_trade_4


@pytest.fixture
def shared_trade_5(
    db_session: Session,
    realtor1_organization2: User,
    fi_property2: FIProperty,
):
    # This shared_trade has trade_id=eb973a26-0666-4843-8b20-2ba1028c45f4 . This is used for initiating signing process
    shared_trade_5 = SharedTrade(
        fi_property_id=fi_property2.id,
        attachments=[
            {
                "id": "attachment4",
                "description": "Test Attachment 5",
                "documentType": AttachmentTypeSharedTrade.TRADE_REGISTER_EXTRACT,
            }
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "bill_of_sale 5",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Test Bank 5",
            "IBAN": "FI1234567890",
            "BIC": "TESTBIC4",
        },
        sellers=[
            {
                "person": {
                    "firstName": "Tommy",
                    "lastName": "Parker",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010180-123A",
                },
                "bankBusinessId": "FI12345678",
            }
        ],
        buyers=[
            {
                "person": {
                    "firstName": "Hanna",
                    "lastName": "Virtanen",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "020295-456B",
                },
                "bankBusinessId": "FI87654321",
            }
        ],
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator4",
            "lastName": "Test2",
        },
        initiator_person_id="string",
        apartment={
            "address": {
                "streetAddress": "Test Street 5",
                "postalCode": "00200",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.DIGITAL,
            "housingCompany": {
                "name": "Test Housing Company 5",
                "businessId": "1234567-8",
            },
        },
        trade_initiated_timestamp="2023-10-15T10:00:00Z",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2023, 10, 10, 0, 0),
        trade_id="********-0000-0000-0000-********0502",
    )

    db_session.add(shared_trade_5)
    db_session.commit()
    return shared_trade_5


@pytest.fixture
def completed_shared_trade(
    db_session: Session,
    realtor1_organization2: User,
    fi_property2: FIProperty,
):
    # This is completed shared trade which has some documents in DIAS
    # Dias valid document-id for testing is 74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C
    # get /api/v1/documents/{document-id}
    completed_shared_trade = SharedTrade(
        fi_property_id=fi_property2.id,
        attachments=[
            {
                "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
                "description": "Test Attachment 5",
                "documentType": AttachmentTypeSharedTrade.TRADE_REGISTER_EXTRACT,
            }
        ],
        require_initiator_confirmation=True,
        bill_of_sale={
            "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
            "description": "Test Bill of Sale",
        },
        realtor_bank_account={
            "name": "Test Bank 5",
            "IBAN": "FI1234567890",
            "BIC": "TESTBIC4",
        },
        trade_state={
            "buyerBankShareCertificateReceivedConfirmationTimestamp": "2019-03-27T09:13:52.686Z",
            "shareCertificate": {
                "certificate": {
                    "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
                    "description": "Skannattu osakekirja",
                },
                "transferConfirmedTimestamp": "2019-03-25T14:58:19.312Z",
            },
            "invitedToSignTimestamp": "2019-03-23T08:31:19.718Z",
            "signingComplete": {
                "billOfSaleSigned": {
                    "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
                    "description": "Allekirjoitettu kauppakirja",
                },
                "signedTimestamp": "2019-03-24T11:08:42.645Z",
            },
            "buyerBankApprovedTimestamp": "2019-03-22T16:52:37.932Z",
            "sellerBankConfirmedPaymentTimestamp": "2019-03-25T14:38:43.373Z",
            "buyerBankPaymentConfirmedTimestamp": "2019-03-24T15:12:45.719Z",
            "tradeReceivedTimestamp": "2019-03-22T08:20:54.275Z",
            "tradeInitiatedTimestamp": "2019-03-22T08:20:54.275Z",
            "sellerBankRealtorPaymentConfirmedTimestamp": "2019-03-25T14:39:21.765Z",
            "initiatorConfirmedToSign": {
                "initiatorPersonId": "19283",
                "confirmedTimestamp": "2019-03-23T08:27:25.524Z",
            },
            "tradePhase": "COMPLETED",
            "sellerBankApprovedTimestamp": "2019-03-22T12:32:01.567Z",
        },
        sellers=[
            {
                "person": {
                    "firstName": "Tommy",
                    "lastName": "Parker",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010180-123A",
                },
                "bankBusinessId": "FI12345678",
            }
        ],
        buyers=[
            {
                "person": {
                    "firstName": "Ossi",
                    "lastName": "Ostaja",
                    "email": "<EMAIL>",
                    "socialSecurityNumber": "010175-7777",
                },
                "bankBusinessId": "FI12345678",
                "transferTax": {
                    "amountInCents": 16335,
                    "confirmedTimestamp": "2019-03-25T08:52:11.148Z",
                    "receipt": {
                        "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
                        "description": "Kuitti Ossin varainsiirtoveron maksamisesta",
                    },
                },
                "signingTimestamps": [
                    {"type": "DIAS", "timestamp": "2019-03-23T08:44:19.718Z"}
                ],
            }
        ],
        initiator_contact_info={
            "email": "<EMAIL>",
            "phoneNumber": "+************",
            "firstName": "Initiator4",
            "lastName": "Test2",
        },
        initiator_person_id="string",
        apartment={
            "address": {
                "streetAddress": "Test Street 5",
                "postalCode": "00200",
                "city": "Helsinki",
            },
            "shares": ["1-100"],
            "sharesType": SharesType.DIGITAL,
            "housingCompany": {
                "name": "Test Housing Company 5",
                "businessId": "1234567-8",
            },
        },
        ownership_transfer_power_of_attorney={
            "id": "74E6712ADD6C44E919AE3D7DFD3E8192D2570E0AD368542A2FD76EF448A3642C",
            "description": "Siirtomerkinnän_valtakirja.pdf",
        },
        trade_initiated_timestamp="2023-10-15T10:00:00Z",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2023, 10, 10, 0, 0),
        trade_id="********-0000-0000-0000-********0300",
    )

    db_session.add(completed_shared_trade)
    db_session.commit()
    return completed_shared_trade


@pytest.fixture
def company_1(
    db_session: Session,
    mock_fernet_key_string,
):
    company = Company(
        name="Test Company 1",
        description="Test Company 1 Description",
        address="Test Company 1 Address",
        website="https://testcompany1.com",
        business_id="1234567-1",
    )
    try:
        company.set_dias_api_key(DIAS_DEMO_API_KEY)
    except Exception as e:
        print(e)
    db_session.add(company)
    db_session.commit()
    return company


@pytest.fixture
def company_2(
    db_session: Session,
):
    company = Company(
        name="Test Company 2",
        description="Test Company 2 Description",
        address="Test Company 2 Address",
        website="https://testcompany2.com",
        business_id="1234567-2",
    )
    db_session.add(company)
    db_session.commit()
    return company


@pytest.fixture
def dias_attachment_1(
    db_session: Session,
    realtor1_organization2: User,
):
    attachment = DiasAttachment(
        property_reference="ABC1234567",
        dias_field="attachments",
        file_name="Customer letter.pdf",
        description="Test Attachment 1",
        document_type=AttachmentTypeSharedTrade.HOUSE_MANAGERS_CERTIFICATE,
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2024, 10, 15, 0, 0),
    )
    db_session.add(attachment)
    db_session.commit()
    return attachment


@pytest.fixture
def dias_attachment_2(
    db_session: Session,
    realtor1_organization2: User,
):
    attachment = DiasAttachment(
        property_reference="XYZ1234567",
        dias_field="receipts",
        file_name="Customer letter.pdf",
        description="Test DiasAttachment 2",
        created_by_id=realtor1_organization2.id,
        created_at=datetime(2024, 10, 14, 0, 0),
    )
    db_session.add(attachment)
    db_session.commit()
    return attachment


@pytest.fixture
def dias_attachment_3(
    db_session: Session,
    realtor2_organization2: User,
):
    attachment = DiasAttachment(
        property_reference="ABC1234567",
        dias_field="receipts",
        file_name="Receipt.pdf",
        description="Test DiasAttachment 3",
        created_by_id=realtor2_organization2.id,
        created_at=datetime(2024, 10, 13, 0, 0),
    )
    db_session.add(attachment)
    db_session.commit()
    return attachment


@pytest.fixture
def matchmaking(db_session, property_es_1, contact_es_1, admin_es_1):
    matchmaking_object = MatchMaking(
        title="Test Matchmaking",
        notes="Test note",
        params=json.dumps({"min_bedrooms": 3}),
        assigned_to_users=[admin_es_1],
        contacts=[contact_es_1],
        properties=[property_es_1],
    )
    db_session.add(matchmaking_object)
    db_session.flush()

    return matchmaking_object


@pytest.fixture
def matchmaking_dev_test(db_session, property_es_1, contact_dev_test, admin_es_1):
    matchmaking_object = MatchMaking(
        title="Test Matchmaking",
        notes="Test note",
        params=json.dumps({"min_bedrooms": 3}),
        assigned_to_users=[admin_es_1],
        contacts=[contact_dev_test],
        properties=[property_es_1],
        is_auto_sent=True,
    )
    db_session.add(matchmaking_object)
    db_session.flush()

    return matchmaking_object


@pytest.fixture
def document_attachment_1(db_session: Session, realtor2_organization2: User, document1):
    attachment = DocumentAttachments(
        name="ABC1234567",
        type="application/pdf",
        attachment_type=AttachmentDocumentType.EXTRA_DOS,
        sowise_id="document_attachment_1",
        document_id=document1.id,
    )
    db_session.add(attachment)
    db_session.commit()
    return attachment


@pytest.fixture
def tag1(db_session: Session):
    tag = Tag(name="Buyer")
    db_session.add(tag)
    db_session.commit()
    return tag
