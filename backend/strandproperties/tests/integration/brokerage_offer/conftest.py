from typing import Any, Dict, Optional, Tuple
from unittest.mock import MagicMock, patch

import pytest


@pytest.fixture
def realtor_with_brokerage_details(realtor1, db_session):
    realtor1.details = {
        "position": "Senior Real Estate Agent",
        "company": {
            "company_name": "ADD BUSINESS NAME",
            "company_id": "ADD BUSINESS NUMBER",
        },
    }
    realtor1.phone_number = "+358401234567"
    db_session.commit()
    return realtor1


@pytest.fixture
def realtor2_with_brokerage_details(realtor2, db_session):
    realtor2.details = {
        "position": "Junior Real Estate Agent",
        "company": {
            "company_name": "ADD BUSINESS NAME",
            "company_id": "ADD BUSINESS NUMBER",
        },
    }
    realtor2.phone_number = "+358409876543"
    db_session.commit()
    return realtor2


@pytest.fixture
def mock_document_renderer():
    """Mock DocumentRenderer for successful operations"""
    with patch(
        "strandproperties.views.brokerage_offer.DocumentRenderer"
    ) as mock_renderer:
        mock_renderer_instance = mock_renderer.return_value
        mock_renderer_instance.render_template.return_value = "<html>Mock HTML</html>"
        mock_renderer_instance.generate_pdf.return_value = b"Mock PDF content"
        yield mock_renderer_instance


@pytest.fixture
def mock_document_renderer_with_errors():
    """Mock DocumentRenderer that can be configured to raise errors"""
    with patch(
        "strandproperties.views.brokerage_offer.DocumentRenderer"
    ) as mock_renderer:
        mock_renderer_instance = mock_renderer.return_value
        yield mock_renderer_instance


@pytest.fixture
def mock_document_library():
    """Mock document library service and related functionality"""
    with patch(
        "strandproperties.views.brokerage_offer.create_document_library"
    ) as mock_create_library:
        mock_library_instance = MagicMock()
        mock_document_item = MagicMock()
        mock_document_item.id = 123
        mock_library_instance.create_item_from_content.return_value = mock_document_item
        mock_create_library.return_value = mock_library_instance
        yield mock_create_library, mock_library_instance, mock_document_item


@pytest.fixture
def realtor_with_minimal_profile(realtor1, db_session):
    """Realtor with minimal profile data - missing phone, details, etc."""
    # Reset to minimal data - only first_name and last_name are guaranteed
    # Note: email cannot be None due to database NOT NULL constraint
    realtor1.email = "<EMAIL>"  # Required by database constraint
    realtor1.phone_number = None
    realtor1.details = None
    db_session.commit()
    return realtor1


@pytest.fixture
def realtor_with_missing_company_details(realtor1, db_session):
    """Realtor with some profile data but missing company details"""
    realtor1.details = {
        "position": "Real Estate Agent",
        # company is missing
    }
    realtor1.phone_number = "+358401234567"
    # email exists from base fixture
    db_session.commit()
    return realtor1


@pytest.fixture
def realtor_with_null_company_details(realtor1, db_session):
    """Realtor with explicitly null company details"""
    realtor1.details = {
        "position": "Real Estate Agent",
        "company": None,
    }
    realtor1.phone_number = None
    realtor1.email = "<EMAIL>"  # Required by database constraint
    db_session.commit()
    return realtor1


@pytest.fixture
def realtor_with_empty_company_details(realtor1, db_session):
    """Realtor with empty company object"""
    realtor1.details = {
        "position": None,
        "company": {},
    }
    db_session.commit()
    return realtor1


def create_base_payload(realtor_id: int, **overrides) -> Dict[str, Any]:
    """Create a base brokerage offer payload with optional overrides"""
    base = {
        "realtor_id": realtor_id,
        "realtor_description": "Test description",
        "address": "Test Address 123",
        "price_inquiry": 750000,
        "commission": 3.0,
        "contact_ids": [1],
    }
    base.update(overrides)
    return base


def unpack_document_library_mocks(
    mock_document_library,
) -> Tuple[MagicMock, MagicMock, MagicMock]:
    """Unpack document library mock tuple for easier use"""
    return mock_document_library


def extract_template_data(mock_document_renderer) -> Dict[str, Any]:
    """Extract template data from document renderer mock call"""
    call_args = mock_document_renderer.render_template.call_args[0]
    return call_args[1]


def assert_user_data(user_data: Dict[str, Any], expected: Dict[str, Any]):
    """Assert user data fields match expected values"""
    for key, value in expected.items():
        assert user_data[key] == value, f"User {key} should be '{value}'"


def assert_property_data(property_data: Dict[str, Any], expected: Dict[str, Any]):
    """Assert property data fields match expected values"""
    for key, value in expected.items():
        assert property_data[key] == value, f"Property {key} should be '{value}'"


def assert_document_library_calls(
    mock_create_library: MagicMock,
    mock_library_instance: MagicMock,
    expected_contacts: int,
    expected_items: int = 1,
):
    """Assert document library was called correctly for expected number of contacts"""
    assert (
        mock_create_library.call_count == expected_contacts
    ), f"Should create document library for {expected_contacts} contacts"
    assert (
        mock_library_instance.create_item_from_content.call_count == expected_items
    ), f"Should create {expected_items} document items"


def assert_generate_response(
    response_data: Dict[str, Any], expected_contacts: int, expected_items: int = 1
):
    """Assert email sharing response has correct format and values"""
    required_fields = ["message", "item_ids", "contacts_processed"]
    for field in required_fields:
        assert field in response_data, f"Response should contain '{field}'"

    assert (
        response_data["contacts_processed"] == expected_contacts
    ), f"Should process {expected_contacts} contacts"
    assert (
        len(response_data["item_ids"]) == expected_items
    ), f"Should return {expected_items} document item IDs"


def setup_realtor_details(
    realtor,
    db_session,
    details: Optional[Dict] = None,
    phone: Optional[str] = None,
    email: Optional[str] = None,
):
    """Helper to set up realtor details for testing"""
    if details is not None:
        realtor.details = details
    if phone is not None:
        realtor.phone_number = phone
    if email is not None:
        realtor.email = email
    db_session.commit()
    return realtor


def format_price(amount: float) -> str:
    """Format price as expected in templates"""
    return f"{amount:,.2f}".replace(",", " ").replace(".", ",") + " €"


def format_commission(rate: float) -> str:
    """Format commission as expected in templates"""
    return f"{rate:.2f}".replace(".", ",") + " %"
