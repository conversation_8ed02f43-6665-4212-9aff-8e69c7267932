from unittest.mock import MagicMock, patch

from strandproperties.tests.integration.brokerage_offer.conftest import (
    assert_document_library_calls,
    assert_generate_response,
    assert_property_data,
    assert_user_data,
    create_base_payload,
    extract_template_data,
    format_commission,
    format_price,
    setup_realtor_details,
    unpack_document_library_mocks,
)


def test_post_brokerage_offer_happy_path_with_all_fields(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="Experienced real estate professional specializing in luxury properties",
        address="Kauppakatu 10, 20100 Turku, Finland",
        price_inquiry=1250000.50,
        commission=4.5,
        notes="Beautiful waterfront property with stunning sea views and private dock",
        contact_ids=[1, 2, 3],
    )

    mock_create_library, mock_library_instance, mock_document_item = (
        unpack_document_library_mocks(mock_document_library)
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 3)

    assert_document_library_calls(mock_create_library, mock_library_instance, 3)

    mock_document_renderer.render_template.assert_called_once()
    mock_document_renderer.generate_pdf.assert_called_once()

    template_name, template_data = mock_document_renderer.render_template.call_args[0]
    assert template_name == "brokerage_offer_template.html"

    expected_realtor_name = f"{realtor_with_brokerage_details.first_name} {realtor_with_brokerage_details.last_name}"
    assert_user_data(
        template_data["user"],
        {
            "name": expected_realtor_name,
            "email": realtor_with_brokerage_details.email,
            "phone": "+358401234567",
            "title": "Senior Real Estate Agent",
            "description": payload["realtor_description"],
        },
    )

    assert_property_data(
        template_data["property"],
        {
            "address": payload["address"],
            "price_inquiry": format_price(1250000.50),
            "commission": format_commission(4.5),
            "notes": payload["notes"],
        },
    )


def test_post_brokerage_offer_large_financial_values(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="Luxury property specialist",
        address="Exclusive Villa, 02100 Espoo",
        price_inquiry=15750000.99,
        commission=2.75,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_property_data(
        template_data["property"],
        {
            "price_inquiry": format_price(15750000.99),
            "commission": format_commission(2.75),
        },
    )


def test_post_brokerage_offer_special_characters_and_unicode(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="Kiinteistönvälittäjä - Asiantuntija åäö ñ € specialist",
        address="Hämeenkatu 123, 33200 Tampere (Keskusta-alue)",
        price_inquiry=850000,
        commission=3.5,
        notes="Kaunis koti • Moderni keittiö • Sauna & parvekkeet",
        contact_ids=[1, 2],
    )

    api("post", route_url("brokerage_offer.generate"), payload, status=201)

    template_data = extract_template_data(mock_document_renderer)
    assert "åäö ñ €" in template_data["user"]["description"]
    assert "Hämeenkatu" in template_data["property"]["address"]
    assert "•" in template_data["property"]["notes"]


def test_post_brokerage_offer_forbidden_for_non_owner(
    api, route_url, realtor2_with_brokerage_details, contact1
):
    payload = create_base_payload(
        realtor2_with_brokerage_details.id,
        notes="Test notes",
        contact_ids=[contact1.id],
    )

    error_response = api(
        "post",
        route_url("brokerage_offer.generate"),
        payload,
        status=403,
        role=realtor2_with_brokerage_details._roles[0],
    )

    assert "Error generating brokerage offer" in error_response["error"]


def test_post_brokerage_offer_realtor_not_found(api, route_url, mock_document_library):
    payload = create_base_payload(99999, notes="Test notes")

    error_response = api(
        "post", route_url("brokerage_offer.generate"), payload, status=404
    )

    assert "Error generating brokerage offer" in error_response["error"]


def test_post_brokerage_offer_missing_required_fields(api, route_url):
    error_response = api("post", route_url("brokerage_offer.generate"), {}, status=400)

    assert "Invalid request data" in error_response["error"]


def test_post_brokerage_offer_invalid_data_types(api, route_url):
    payload = {
        "realtor_id": "not_a_number",
        "realtor_description": 123,
        "address": None,
        "price_inquiry": "not_a_number",
        "commission": "not_a_number",
        "contact_ids": "not_a_list",
    }

    error_response = api(
        "post", route_url("brokerage_offer.generate"), payload, status=400
    )

    assert "Invalid request data" in error_response["error"]


def test_post_brokerage_offer_negative_financial_values(
    api, route_url, realtor_with_brokerage_details
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        price_inquiry=-100000,
        commission=-5,
    )

    error_response = api(
        "post", route_url("brokerage_offer.generate"), payload, status=400
    )

    assert "Invalid request data" in error_response["error"]


def test_post_brokerage_offer_zero_values_allowed(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="Free property consultation",
        address="Community Service Property",
        price_inquiry=0,
        commission=0,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_property_data(
        template_data["property"],
        {
            "price_inquiry": format_price(0),
            "commission": format_commission(0),
        },
    )


def test_post_brokerage_offer_template_data_structure_validation(
    api, route_url, realtor_with_brokerage_details, mock_document_library
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="Template data structure test",
        address="Test Address 123",
        price_inquiry=750000,
        commission=2.8,
        notes="Data structure validation test",
        contact_ids=[1, 2],
    )

    mock_create_library, mock_library_instance, mock_document_item = (
        unpack_document_library_mocks(mock_document_library)
    )

    with patch(
        "strandproperties.views.brokerage_offer.DocumentRenderer"
    ) as mock_renderer_class:
        mock_renderer_instance = mock_renderer_class.return_value
        mock_renderer_instance.render_template.return_value = "<html>Mock HTML</html>"
        mock_renderer_instance.generate_pdf.return_value = b"Mock PDF content"

        response = api("post", route_url("brokerage_offer.generate"), payload)

        assert_generate_response(response, 2)

        mock_renderer_instance.render_template.assert_called_once()
        template_path, template_data = mock_renderer_instance.render_template.call_args[
            0
        ]

        assert template_path == "brokerage_offer_template.html"

        required_keys = ["user", "property"]
        for key in required_keys:
            assert key in template_data

        expected_realtor_name = f"{realtor_with_brokerage_details.first_name} {realtor_with_brokerage_details.last_name}"
        assert_user_data(
            template_data["user"],
            {
                "name": expected_realtor_name,
                "email": realtor_with_brokerage_details.email,
                "phone": "+358401234567",
                "title": "Senior Real Estate Agent",
                "description": "Template data structure test",
                "business_name": "ADD BUSINESS NAME",
                "business_number": "ADD BUSINESS NUMBER",
            },
        )

        assert_property_data(
            template_data["property"],
            {
                "address": "Test Address 123",
                "price_inquiry": format_price(750000),
                "commission": format_commission(2.8),
                "notes": "Data structure validation test",
            },
        )

        assert_document_library_calls(mock_create_library, mock_library_instance, 2)


def test_post_brokerage_offer_document_renderer_template_error(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer_with_errors,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id, price_inquiry=1000000, commission=5
    )

    mock_document_renderer_with_errors.render_template.side_effect = Exception(
        "Template rendering failed"
    )

    error_response = api(
        "post", route_url("brokerage_offer.generate"), payload, status=500
    )

    assert "Error generating brokerage offer" in error_response["error"]


def test_post_brokerage_offer_document_renderer_pdf_error(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer_with_errors,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id, price_inquiry=1000000, commission=5
    )

    mock_document_renderer_with_errors.render_template.return_value = (
        "<html>Mock HTML</html>"
    )
    mock_document_renderer_with_errors.generate_pdf.side_effect = Exception(
        "PDF generation failed"
    )

    error_response = api(
        "post", route_url("brokerage_offer.generate"), payload, status=500
    )

    assert "Error generating brokerage offer" in error_response["error"]


def test_post_brokerage_offer_empty_string_fields(
    api, route_url, realtor_with_brokerage_details, mock_document_library
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        realtor_description="",
        address="",
        price_inquiry=1000000,
        commission=5,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)


def test_post_brokerage_offer_price_formatting_validation(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    test_cases = [
        (100000.0, "100 000,00 €"),
        (1234567.89, "1 234 567,89 €"),
        (500000.6, "500 000,60 €"),
        (0.0, "0,00 €"),
    ]

    mock_create_library, mock_library_instance, mock_document_item = (
        unpack_document_library_mocks(mock_document_library)
    )

    for price_input, expected_formatted in test_cases:
        payload = create_base_payload(
            realtor_with_brokerage_details.id,
            price_inquiry=price_input,
            commission=3.5,
        )

        response = api("post", route_url("brokerage_offer.generate"), payload)

        assert_generate_response(response, 1)

        template_data = extract_template_data(mock_document_renderer)
        assert template_data["property"]["price_inquiry"] == expected_formatted


def test_post_brokerage_offer_document_library_partial_failure(
    api, route_url, realtor_with_brokerage_details, mock_document_renderer
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        price_inquiry=1000000,
        commission=5,
        contact_ids=[1, 2, 3],
    )

    with patch(
        "strandproperties.views.brokerage_offer.create_document_library"
    ) as mock_create_library:
        mock_library_instance1 = MagicMock()
        mock_library_instance2 = MagicMock()
        mock_document_item = MagicMock()
        mock_document_item.id = 123
        mock_library_instance1.create_item_from_content.return_value = (
            mock_document_item
        )
        mock_library_instance2.create_item_from_content.return_value = (
            mock_document_item
        )

        mock_create_library.side_effect = [
            mock_library_instance1,
            PermissionError("Access denied"),
            mock_library_instance2,
        ]

        response = api(
            "post", route_url("brokerage_offer.generate"), payload, status=207
        )

        assert_generate_response(response, 2)
        assert response["failed_contacts"] == 1
        assert mock_create_library.call_count == 3


def test_post_brokerage_offer_all_contacts_fail_document_library(
    api, route_url, realtor_with_brokerage_details, mock_document_renderer
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        price_inquiry=1000000,
        commission=5,
        contact_ids=[1, 2],
    )

    with patch(
        "strandproperties.views.brokerage_offer.create_document_library"
    ) as mock_create_library:
        mock_create_library.side_effect = PermissionError(
            "Access denied to document library"
        )

        error_response = api(
            "post", route_url("brokerage_offer.generate"), payload, status=403
        )

        assert "Error generating brokerage offer" in error_response["error"]


def test_post_brokerage_offer_document_filename_generation(
    api,
    route_url,
    realtor_with_brokerage_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_brokerage_details.id,
        address="Test Address, 12345 City",
        price_inquiry=1000000,
        commission=5,
    )

    mock_create_library, mock_library_instance, mock_document_item = (
        unpack_document_library_mocks(mock_document_library)
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    mock_library_instance.create_item_from_content.assert_called_once()
    call_args = mock_library_instance.create_item_from_content.call_args

    filename = call_args.kwargs["filename"]
    assert "Test_Address" in filename
    assert filename.endswith(".pdf")

    assert call_args.kwargs["mime_type"] == "application/pdf"
    assert call_args.kwargs["document_type"].value == "FI_BROKERAGE_OFFER"
    assert "brokerage offer" in call_args.kwargs["description"].lower()


def test_post_brokerage_offer_with_minimal_profile_data(
    api,
    route_url,
    realtor_with_minimal_profile,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_minimal_profile.id,
        address="Test Address 123",
        price_inquiry=500000,
        commission=3.0,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    expected_realtor_name = f"{realtor_with_minimal_profile.first_name} {realtor_with_minimal_profile.last_name}"
    assert_user_data(
        template_data["user"],
        {
            "name": expected_realtor_name,
            "email": "<EMAIL>",
            "phone": "",
            "title": "",
            "business_name": "",
            "business_number": "",
        },
    )


def test_post_brokerage_offer_with_missing_company_details(
    api,
    route_url,
    realtor_with_missing_company_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_missing_company_details.id,
        address="Test Address 123",
        price_inquiry=750000,
        commission=4.0,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "title": "Real Estate Agent",
            "phone": "+358401234567",
            "email": "<EMAIL>",
            "business_name": "",
            "business_number": "",
        },
    )


def test_post_brokerage_offer_with_null_company_details(
    api,
    route_url,
    realtor_with_null_company_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_null_company_details.id,
        address="Test Address 123",
        price_inquiry=600000,
        commission=2.5,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "title": "Real Estate Agent",
            "email": "<EMAIL>",
            "phone": "",
            "business_name": "",
            "business_number": "",
        },
    )


def test_post_brokerage_offer_with_empty_company_details(
    api,
    route_url,
    realtor_with_empty_company_details,
    mock_document_renderer,
    mock_document_library,
):
    payload = create_base_payload(
        realtor_with_empty_company_details.id,
        address="Test Address 123",
        price_inquiry=800000,
        commission=3.5,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)

    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "title": "",
            "business_name": "",
            "business_number": "",
        },
    )


def test_post_brokerage_offer_comprehensive_missing_fields_scenarios(
    api,
    route_url,
    realtor1,
    db_session,
    mock_document_renderer,
    mock_document_library,
):
    setup_realtor_details(
        realtor1, db_session, details={}, phone=None, email="<EMAIL>"
    )

    payload = create_base_payload(
        realtor1.id,
        realtor_description="Empty details test",
        address="Empty Details Street 1",
        price_inquiry=300000,
        commission=1.5,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)
    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "email": "<EMAIL>",
            "phone": "",
            "title": "",
            "business_name": "",
            "business_number": "",
        },
    )

    setup_realtor_details(
        realtor1,
        db_session,
        details={"position": "Agent", "company": {"company_name": "Test Company"}},
        email="<EMAIL>",
    )

    payload["address"] = "Partial Details Street 2"
    response = api("post", route_url("brokerage_offer.generate"), payload)
    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "email": "<EMAIL>",
            "phone": "",
            "title": "Agent",
            "business_name": "Test Company",
            "business_number": "",
        },
    )


def test_post_brokerage_offer_template_fallback_behavior_documentation(
    api,
    route_url,
    realtor1,
    db_session,
    mock_document_renderer,
    mock_document_library,
):
    setup_realtor_details(
        realtor1,
        db_session,
        details={
            "position": None,
            "company": {
                "company_name": None,
                "company_id": "",
            },
        },
        phone=None,
        email="<EMAIL>",
    )

    payload = create_base_payload(
        realtor1.id,
        realtor_description="Fallback behavior documentation",
        address="Fallback Test Address",
        price_inquiry=400000,
        commission=2.0,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)
    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    expected_name = f"{realtor1.first_name} {realtor1.last_name}"

    assert_user_data(
        template_data["user"],
        {
            "email": "<EMAIL>",
            "phone": "",
            "title": "",
            "business_name": "",
            "business_number": "",
            "description": "Fallback behavior documentation",
            "name": expected_name,
        },
    )

    assert "?width=300&height=300" in template_data["user"]["profile_picture"]


def test_post_brokerage_offer_handles_none_details_properly(
    api,
    route_url,
    realtor1,
    db_session,
    mock_document_renderer,
    mock_document_library,
):
    setup_realtor_details(realtor1, db_session, details=None, phone=None)

    payload = create_base_payload(
        realtor1.id,
        realtor_description="Testing None details handling",
        address="None Details Test Address",
        price_inquiry=500000,
        commission=3.0,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)
    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    expected_name = f"{realtor1.first_name} {realtor1.last_name}"

    assert_user_data(
        template_data["user"],
        {
            "title": "",
            "business_name": "",
            "business_number": "",
            "phone": "",
            "email": realtor1.email,
            "name": expected_name,
        },
    )


def test_post_brokerage_offer_handles_empty_string_email(
    api,
    route_url,
    realtor1,
    db_session,
    mock_document_renderer,
    mock_document_library,
):
    setup_realtor_details(
        realtor1,
        db_session,
        details={
            "position": "",
            "company": {
                "company_name": "",
                "company_id": "",
            },
        },
        phone="",
        email="",
    )

    payload = create_base_payload(
        realtor1.id,
        realtor_description="Testing empty string handling",
        address="Empty String Test Address",
        price_inquiry=400000,
        commission=2.5,
    )

    response = api("post", route_url("brokerage_offer.generate"), payload)
    assert_generate_response(response, 1)

    template_data = extract_template_data(mock_document_renderer)
    assert_user_data(
        template_data["user"],
        {
            "email": "",
            "phone": "",
            "title": "",
            "business_name": "",
            "business_number": "",
        },
    )
