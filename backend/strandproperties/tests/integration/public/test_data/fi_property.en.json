{"parameters": {"referenceCode": "Property2", "language": "en"}, "images": ["http://localhost/test.png"], "coordinates": {"latitude": 40.0, "longitude": 30.0}, "events": [], "realtor": [355, 356], "metadata": {}, "body": {"description": {"title": "Description", "content": {"propertyTitle": {"title": "Property Title", "value": "xxx"}, "propertyDescription": {"title": "Property Description", "value": "xxx"}}}, "basicInfo": {"title": "Basic info", "content": {"reference": {"title": "Reference", "value": "Property2"}, "propertyId": {"title": "Property ID", "value": "PROP12345"}, "propertyName": {"title": "Property name", "value": "Test Farm"}, "roomStructure": {"title": "Room structure", "value": "3BHK"}, "address": {"title": "Address", "value": "123 Main St A 5B 12345, Downtown"}, "residentialType": {"title": "Residential type", "value": "Balcony access block"}, "ownershipType": {"title": "Ownership type", "value": "OWN"}, "livingType": {"title": "Living type", "value": "Senior house"}, "newBuilding": {"title": "New building", "value": "Yes"}, "rooms": {"title": "Rooms", "value": "3H"}, "builtSize": {"title": "Built size", "value": "10 m2"}, "otherSpaces": {"title": "Other spaces", "value": "50 m2"}, "plotSize": {"title": "Plot size", "value": "89 m2"}, "totalSize": {"title": "Total size", "value": "99 m2"}, "sizeVerification": {"title": "Size verification", "value": ["Managers certificate"]}, "sizeDescription": {"title": "Size description", "value": "Additional area measurement information."}, "floors": {"title": "Floors", "value": "3/4"}, "availability": {"title": "Availability", "value": "Available"}}}, "priceAndCost": {"title": "price_and_cost", "content": {"price": {"title": "Price", "content": {"startingPrice": {"title": "Starting price", "value": "222 €"}, "startingDebtFreePrice": {"title": "Starting depth free price", "value": "123 €"}, "startingDebtShareAmount": {"title": "Starting depth share amount", "value": "200000 €"}, "salePrice": {"title": "Sale price", "value": "987 €"}, "residentialShareOverviewDebtFreePrice": {"title": "Debt free price", "value": "250000 €"}, "residentialShareOverviewDebtShareAmount": {"title": "Debt share amount", "value": "50000 €"}, "residentialShareOverviewDebtShareAmountDescription": {"title": "Depth share amount description", "value": "debt_share_additional_info"}, "commercialOverviewDebtFreePrice": {"title": "Debt free price", "value": "250000 €"}, "commercialOverviewDebtShareAmount": {"title": "Debt share amount", "value": "50000 €"}, "otherShareOverviewDebtFreePrice": {"title": "Debt free price", "value": "********* €"}, "otherShareOverviewDebtShareAmount": {"title": "Debt share amount", "value": "3425325 €"}, "monthlyRent": {"title": "Monthly rent", "value": "20000 €"}}}, "costs": {"title": "Costs", "content": {"costs": {"title": "Costs", "value": ["Monthly property tax.: 100 €", "Monthly fee for garbage collection.: 50 €", "Monthly fee for TV connection.: 25 €"]}, "costsDescription": {"title": "Costs description", "value": "This fee covers maintenance of common areas and utilities."}, "managementCost": {"title": "Management cost", "value": "10.0 €"}, "financingCost": {"title": "Financing cost", "value": "1500€ (interest only) and after 2025-01-01, 2500€"}, "plotCost": {"title": "Plot cost", "value": "Monthly cost 2000€ (Land redeeemable: Yes and redeemtion share 50000€)"}, "specialCost": {"title": "Special cost", "value": "11.0 €"}, "repaymentHolidayPeriod": {"title": "Repayment holiday period", "value": "12 month"}, "startDateOfTheRepaymentHolidayPeriod": {"title": "Start date of the repayment holiday period", "value": "10.08.2024"}, "endDateOfTheRepaymentHolidayPeriod": {"title": "End date of the repayment holiday period", "value": "10.08.2024"}, "financialChargeAfterTheRepaymentHoliday": {"title": "Financial charge after the repayment holiday", "value": "1200€"}, "moreInformationAboutTheChargesLink": {"title": "More information about the charges link", "value": "https://strand.fi"}}}, "additionalDetails": {"title": "Additional details", "content": {"isRented": {"title": "Is rented", "value": "Yes"}, "leaseDetails": {"title": "Lease details", "value": {"payment_date": "2024-09-01", "deposit_description": "Security deposit for the lease agreement.", "deposit_amount": 1500, "currency_code": "EUR", "lease_type_code": "Fixed term", "fixed_lease_start_date": "2024-09-01", "fixed_lease_end_date": "2025-09-01", "lease_start_date": "2024-09-01"}}}}, "plot": {"title": "Plot", "content": {"plotYearlyRent": {"title": "Plot yearly rent", "value": "120000 €"}, "moreInformationAboutThePlotRedemption": {"title": "More information about the plot redemption", "value": "plot_redemption_info"}, "moreInformationAboutThePlotRedemptionLink": {"title": "More information about the plot redemption link", "value": "https://strand.fi"}, "informationAboutTheLandLeaseAgreement": {"title": "Information about the land lease agreement", "value": "plot_rental_agreement"}, "landLeaseAgreementLink": {"title": "Land lease agreement link", "value": "https://strand.fi"}}}}}, "additionalInformation": {"title": "Additional information", "content": {"administration": {"title": "Administration", "value": "Apartment housing company"}, "sauna": {"title": "Sauna", "value": "Yes"}, "terrace": {"title": "Terrace", "value": "Yes"}, "balcony": {"title": "Balcony", "value": "Yes"}, "toilets": {"title": "<PERSON><PERSON><PERSON>", "value": 4}, "bedrooms": {"title": "Bedrooms", "value": 7}, "viewDescription": {"title": "View description", "value": "View description."}, "installationsByShareholder": {"title": "Installations by shareholder", "value": "Shareholder installation description."}, "renovations": {"title": "Renovations", "value": ["Kitchen renovation completed.", "Bathroom plumbing renovation ongoing.", "Facade renovation planned."]}, "renovationsDescription": {"title": "Renovations description", "value": "The apartment has undergone several renovations, including kitchen, plumbing, and facade."}, "condition": {"title": "Condition", "value": {"code": "Good", "description": "The property is in good condition with minor wear and tear."}}, "damages": {"title": "Damages", "value": [{"damages_exists_code": "Yes", "damage_type_code": "Water damage", "damage_date": "2023-05-01", "cause_description": "A pipe burst causing water damage in the kitchen.", "extent_description": "Water damage affected 20 square meters.", "repair_description": "Replaced damaged flooring and drywall.", "attachment_id": "doc_123"}, {"damages_exists_code": "No", "damage_type_code": "Moisture damage", "damage_date": null, "cause_description": null, "extent_description": null, "repair_description": null, "attachment_id": null}]}, "inspections": {"title": "Inspections", "value": [{"date": "2023-06-15", "description": "A condition assessment was conducted, revealing minor issues that need to be addressed.", "type_code": "CONDITION_ASSESSMENT"}, {"date": "2022-10-10", "description": "A condition survey focused on the water and sewage pipes was conducted, and it indicated the need for replacement within the next 5 years.", "type_code": "CONDITION_SURVEY"}, {"date": "2021-08-05", "description": "A long-term plan was created, outlining the necessary renovations and estimated budgets for the next ten years.", "type_code": "LONG_TERM_PLAN"}]}, "inspectionsDescription": {"title": "Inspections description", "value": "The housing company has conducted several inspections, including condition assessments, surveys, and long-term planning. These inspections ensure the building's structural integrity and identify necessary renovations."}, "modifications": {"title": "Modifications", "value": "Modification document detailing the changes made to the living room."}, "asbestosMapping": {"title": "Asbestos mapping", "value": {"is_asbestos_mapping_done": "Yes", "is_asbestos_mapping_report_available": "Yes", "construction_materials_might_have_asbestos": "No", "description": "Asbestos mapping was conducted and a report is available."}}, "housingComfort": {"title": "Housing comfort", "value": "Nearby construction may cause noise during the day."}, "fitForWinterHabitation": {"title": "Fit for winter habitation", "value": "Yes"}, "smokingAllowed": {"title": "Smoking allowed", "value": "No"}, "petsAllowed": {"title": "Pets allowed", "value": "No"}, "accessible": {"title": "Accessible", "value": "No"}, "heating": {"title": "Heating", "value": "The apartment is heated using central heating."}, "underfloorHeatingDescription": {"title": "underfloor_heating_description", "value": "Underfloor heating is available in the bathrooms."}, "furnished": {"title": "Furnished", "value": "No"}, "hearth": {"title": "Hearth", "value": {"type_codes": ["Fireplace", "Iron stove"], "type_other_description": "An iron stove and a traditional fireplace are available.", "description": "The apartment includes both an iron stove and a fireplace, providing excellent heating options."}}, "shareCertification": {"title": "Share certification", "value": {"share_certificate_available": "Yes", "mortgage_declared_separately": "No", "share_certificate_used_as_mortgage": "Yes", "mortgage_holder": "Bank XYZ", "liability_amount": 150000, "currency_code": "EUR", "share_certificate_location": "In the possession of the bank", "share_group_identifiers": ["1001-1010"], "quantity_of_shares": 10, "share_certificate_form_code": "Digital", "permanent_dwelling_identifier": "VTJ-PHT-**********"}}, "redemption": {"title": "Redemption", "value": {"redeemable_by_housing_company": "Yes", "redeemable_by_existing_shareholders": "No", "redemption_right_applies_to_all_shares": "No", "other_restrictions": "No other restrictions apply."}}, "moreInformation": {"title": "More information", "value": "This property is well-maintained and ideal for families seeking a quiet neighborhood."}, "moreInformationLink": {"title": "More information link", "value": [{"title": "Virtual Tour", "url": "https://example.com/virtual-tour", "type_code": "Virtual presentation"}, {"title": "Property Video", "url": "https://example.com/property-video", "type_code": "Video presentation"}]}}}, "housingCompany": {"title": "Housing company", "content": {"housingCompany": {"title": "Housing company", "value": "Sample Housing Company"}, "businessId": {"title": "Business ID", "value": "BIZ1234567"}, "address": {"title": "Address", "value": "456 Elm St 67890, Uptown"}, "manager": {"title": "Manager", "value": "*********"}, "houseManagerCertificateDate": {"title": "House manager certificate date", "value": "CERT123456 - Issued by Certifying Authority on 2023-01-01"}, "maintenance": {"title": "Maintenance", "value": {"type_codes": ["<PERSON><PERSON>", "Residents"], "description": "Maintenance is handled by the janitor and residents."}}, "propertyId": {"title": "Property ID", "value": "property_identifier"}, "construction": {"title": "Construction", "value": "\n            Builder: ABC Constructions Ltd.\n            Year of construction: 2020\n            Construction phase: MOVE_IN_READY\n            Year of commissioning: 2021\n            More information: Construction completed in 2020, ready for move-in by 2021.\n        "}, "buildings": {"title": "Buildings", "value": "\n            Material: BRICK, The buildings are primarily constructed with brick.\n            Heating: DISTRICT_HEATING, District heating is used throughout the building.\n            Ventilation: FORCED_EXHAUST, The ventilation system uses forced exhaust.\n            Outer roof: GABLED The roof is a gabled structure made of sheet metal.\n            Has elevator: Yes\n            Elevator directly to the apartment: No\n            Other facilities: ATTIC_STORAGE\n        "}, "yardDescription": {"title": "Yard description", "value": "The yard is spacious and includes a playground."}, "plot": {"title": "Plot", "value": []}, "internetConnections": {"title": "Internet connections", "value": "\n            Broadband available: Yes\n            Fiber: No\n            Housing company broadband: Yes\n            Broadband operator: NetProvider Inc.\n            Internal network cabling: None\n            More information about internet connection: The building has internal network cabling.\n        "}, "television": {"title": "Television", "value": "\n            Type: CABLE IPTV\n            More information about TV connection: The building has cable and IPTV services available.\n        "}, "lastAnnualMeeting": {"title": "Last annual meeting", "value": "10.08.2024"}, "nextAnnualMeeting": {"title": "Next annual meeting", "value": "10.08.2024"}, "identifiedDeficiencies": {"title": "Identified deficiencies", "value": "The roof needs repairs."}, "incurringLiabilities": {"title": "Incurring liabilities", "value": "The building has outstanding debt for recent renovations."}, "repairAndMaintenanceAgreements": {"title": "Repair and maintenance agreements", "value": "The housing company has a maintenance agreement with ABC Maintenance Co."}, "finances": {"title": "Finances", "value": {"loan_amount": 500000, "mortgage_amount": 200000, "rent_revenue": 150000, "bank_account_credit_limit": 50000, "currency_code": "EUR", "financing_fee_interest_only_period": "12 month", "financing_fee_interest_only_startdate": "10.08.2024", "financing_fee_interest_only_enddate": "10.08.2024", "financing_fee_after_interest_only": 1200, "management_charge_after_interest_only": 2000, "management_charges_info_link": "https://strand.fi"}}, "energyCertificate": {"title": "Energy certificate", "value": {"type_code": "B_2018", "description": "Energy certificate issued in 2018, class B."}}, "asbestosMapping": {"title": "Asbestos mapping", "value": {"is_asbestos_mapping_done": "Yes", "is_asbestos_mapping_report_available": "Yes", "construction_materials_might_have_asbestos": "No", "description": "Asbestos mapping completed. Report is available."}}, "parkingSpaces": {"title": "Parking spaces", "value": [{"parking_space_type_code": "Garage", "count": 10}, {"parking_space_type_code": "Outdoor parking space with electrical plug", "count": 20}, {"parking_space_type_code": "Charging point for electrical cars", "count": 5}]}, "parkingSpacesDescription": {"title": "Parking spaces description", "value": "The building includes 10 garage spaces, 20 outdoor parking spaces with electrical plugs, and 5 charging points for electric cars."}, "housingCompanyPremiseSpecification": {"title": "Housing company premise specifications", "value": [{"premise_type_code": "APARTMENT", "count": 50, "area": {"value": 5000, "area_unit_code": "M2"}, "managed_by_housing_company_count": 45, "area_managed_by_housing_company": {"value": 4500, "area_unit_code": "M2"}}, {"premise_type_code": "BUSINESS_PREMISE", "count": 10, "area": {"value": 1000, "area_unit_code": "M2"}, "managed_by_housing_company_count": 8, "area_managed_by_housing_company": {"value": 800, "area_unit_code": "M2"}}, {"premise_type_code": "OTHER_PREMISE", "count": 5, "area": {"value": 500, "area_unit_code": "M2"}, "managed_by_housing_company_count": 3, "area_managed_by_housing_company": {"value": 300, "area_unit_code": "M2"}}]}, "housingCompanyPremiseSpecificationDescription": {"title": "Housing company premise specification description", "value": "The housing company manages 45 out of 50 apartment units, 8 out of 10 business premises, and 3 out of 5 other premises."}, "renovations": {"title": "Renovations", "value": [{"date": "2023-01-15", "decided_by_general_meeting": "Yes", "description": "The kitchen renovation was planned and will be executed in Q2 2023.", "is_housing_company_notified": "Yes", "status_code": "PLANNED", "type_code": "KITCHEN", "type_other_description": null}, {"date": "2022-09-10", "decided_by_general_meeting": "No", "description": "The roof renovation is in progress and expected to be completed by the end of the year.", "is_housing_company_notified": "Yes", "status_code": "IN_PROGRESS", "type_code": "ROOF", "type_other_description": null}, {"date": "2021-05-20", "decided_by_general_meeting": "Yes", "description": "The facade renovation was completed successfully.", "is_housing_company_notified": "Yes", "status_code": "FINISHED", "type_code": "FACADE", "type_other_description": null}]}, "renovationsDescription": {"title": "Renovations description", "value": "The housing company has undergone several renovations including kitchen, roof, and facade. These renovations were aimed at modernizing the building and ensuring its longevity."}, "plannedRenovations": {"title": "Planned renovations", "value": "The housing company has planned several renovations for the upcoming years, including upgrading the electrical systems and installing new windows."}, "inspections": {"title": "Inspections", "value": [{"date": "2023-06-15", "description": "A condition assessment was conducted, revealing minor issues that need to be addressed.", "type_code": "CONDITION_ASSESSMENT"}, {"date": "2022-10-10", "description": "A condition survey focused on the water and sewage pipes was conducted, and it indicated the need for replacement within the next 5 years.", "type_code": "CONDITION_SURVEY"}, {"date": "2021-08-05", "description": "A long-term plan was created, outlining the necessary renovations and estimated budgets for the next ten years.", "type_code": "LONG_TERM_PLAN"}]}, "inspectionsDescription": {"title": "Inspections description", "value": "The housing company has conducted several inspections, including condition assessments, surveys, and long-term planning. These inspections ensure the building's structural integrity and identify necessary renovations."}, "additionalInformation": {"title": "Additional information", "value": "The housing company also includes a gym and a communal garden, which are available to all residents. The communal areas are well-maintained and regularly cleaned."}, "listOfSharesTransferred": {"title": "List of shares transferred", "value": "Yes"}, "digitalShares": {"title": "Digital shares", "value": "SHARE_GROUP_001"}}}, "spacesAndMaterials": {"title": "Spaces & materials", "content": {"kitchen_and_dining_room": {"title": "Kitchen and dining room", "content": {"kitchen": {"title": "Kitchen", "value": [{"type_code": "KITCHEN", "description": "A modern kitchen equipped with stainless steel appliances and ample counter space.", "features": [{"feature_code": "ISLAND", "description": "Kitchen island with seating and additional storage."}], "floor_material_codes": ["Tiled"], "wall_material_codes": ["Ceramic tile"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "dining_room": {"title": "Dining room", "value": [{"type_code": "DINING_ROOM", "description": "A spacious dining room with elegant lighting and easy access to the kitchen.", "features": [{"feature_code": "CHANDLIER", "description": "Elegant chandelier as the centerpiece of the dining room."}], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["Panel"]}]}}}, "bathroom": {"title": "Bathroom", "content": {"bathroom": {"title": "Bathroom", "value": [{"type_code": "BATH_ROOM", "description": "A modern bathroom with a walk-in shower and tiled flooring.", "features": [], "floor_material_codes": ["Tiled"], "wall_material_codes": ["Ceramic tile"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "bedroom": {"title": "Bedroom", "content": {"bedroom": {"title": "Bedroom", "value": [{"type_code": "BEDROOM", "description": "A cozy bedroom with soft carpet flooring and built-in wardrobes.", "features": [{"feature_code": "BUILT_IN_WARDROBE", "description": "The bedroom includes built-in wardrobes for ample storage."}], "floor_material_codes": ["Wall to wall carpet"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "sauna": {"title": "Sauna", "content": {"sauna": {"title": "Sauna", "value": [{"type_code": "SAUNA", "description": "A traditional Finnish sauna with wood paneling and a stone stove.", "features": [], "floor_material_codes": ["Stone"], "wall_material_codes": ["<PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "toilet": {"title": "<PERSON><PERSON><PERSON>", "content": {"toilet": {"title": "<PERSON><PERSON><PERSON>", "value": [{"type_code": "TOILET", "description": "A compact toilet room with modern fixtures.", "features": [], "floor_material_codes": ["Tiled"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "utility_room": {"title": "Utility room", "content": {"utility_room": {"title": "Utility room", "value": [{"type_code": "UTILITY_ROOM", "description": "A utility room with laundry facilities and storage space.", "features": [], "floor_material_codes": ["Concrete"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "utility_room_description": {"title": "Utility room description", "value": "The utility services room houses the main electrical panel, water heater, and other essential building services."}}}, "living_room": {"title": "Living room", "content": {"living_room": {"title": "Living room", "value": [{"type_code": "LIVING_ROOM", "description": "A spacious living room with a large window and fireplace.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "closet": {"title": "Closet", "content": {"walk_in_closet": {"title": "Walk-in closet", "value": [{"type_code": "WALK_IN_CLOSET", "description": "A walk-in closet with built-in shelving and ample space.", "features": [], "floor_material_codes": ["Cork"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "loft": {"title": "Loft", "content": {"loft": {"title": "Loft", "value": [{"type_code": "LOFT", "description": "A cozy loft space suitable for a small bedroom or study.", "features": [], "floor_material_codes": ["Plastic"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "workroom_and_library": {"title": "Workroom and library", "content": {"library": {"title": "Library", "value": [{"type_code": "LIBRARY", "description": "A quiet library with built-in bookshelves and reading chairs.", "features": [], "floor_material_codes": ["Vinyl cork"], "wall_material_codes": ["<PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "study": {"title": "Study", "value": [{"type_code": "STUDY", "description": "A study room with a large desk and ample lighting.", "features": [], "floor_material_codes": ["Cork"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "area_halls": {"title": "Area halls", "content": {"hall": {"title": "Hall", "value": [{"type_code": "HALL", "description": "An elegant entrance hall with tiled flooring and a chandelier.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "hallway": {"title": "Hallway", "value": [{"type_code": "HALLWAY", "description": "A wide hallway connecting all rooms, with parquet flooring.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "draught_lobby": {"title": "Draught lobby", "value": [{"type_code": "DRAUGHT_LOBBY", "description": "A draught lobby that prevents cold air from entering the main house.", "features": [], "floor_material_codes": ["Tiled"], "wall_material_codes": ["Glass"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "balcony_terrace_and_patio": {"title": "Balcony, terrace and patio", "content": {"terrace_description": {"title": "Terrace description", "value": {"is_glazed_terrace": "Yes", "description": "A glazed terrace facing southwest, ideal for enjoying the sunset.", "compass_points": ["South-west"], "terrace_glass_maintenance_responsibility_code": "Residents"}}, "balcony_description": {"title": "Balcony description", "value": {"type_codes": ["Glazed", "Retracted"], "type_other_description": null, "description": "A glazed and retracted balcony facing southeast, perfect for morning sunlight.", "compass_points": ["South-east"], "balcony_glass_maintenance_responsibility_code": "HOUSING_COMPANY"}}, "patio": {"title": "<PERSON><PERSON>", "value": "A spacious patio with stone flooring, ideal for outdoor gatherings and barbecues."}, "yard": {"title": "Yard", "value": {"has_private_yard": "Yes", "basis_for_possession_code": "Articles of association", "basis_for_possession_other_description": null, "description": "A well-maintained private yard with a garden and patio area."}}}}, "storage_rooms_and_basement": {"title": "Storage rooms and basement", "content": {"storages": {"title": "Storages", "value": [{"type_code": "Attic", "basis_for_possession_codes": ["Belongs to apartment according to the articles of association"], "transfer_code": "Transferable", "basis_for_allocation_description": "Allocated based on ownership."}]}, "storage_usage_limitations": {"title": "Storage usage limitations", "value": "Storage can be used for personal items only."}, "storage_description": {"title": "Storage description", "value": "Spacious storage room located in the basement, suitable for storing seasonal items and tools."}, "refrigerated_cellar": {"title": "Refrigerated cellar", "value": "A refrigerated cellar designed for long-term storage of perishable goods, maintaining a consistent low temperature."}}}, "other": {"title": "Other", "content": {"other": {"title": "Other", "value": [{"type_code": "Other", "description": "A multipurpose room that can be used as an office, guest room, or hobby room.", "features": [{"feature_code": "FLEXIBLE_SPACE", "description": "The room can be easily adapted to different uses."}], "floor_material_codes": ["Vinyl"], "wall_material_codes": ["Paint"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}, {"type_code": "Other", "description": "A small soundproofed room designed for music practice or recording.", "features": [{"feature_code": "SOUNDPROOFING", "description": "The room is fully soundproofed, making it ideal for music practice."}], "floor_material_codes": ["Concrete"], "wall_material_codes": ["Panel"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "boiler_room": {"title": "Boiler room", "value": "A well-ventilated boiler room housing the central heating system and hot water tank, with easy access for maintenance."}, "residential_share_overview_parking_spaces": {"title": "Parking spaces", "value": [{"type_code": "Garage", "basis_for_possession_codes": ["Belongs to apartment according to the articles of association"], "transfer_code": "Transferable", "basis_for_allocation_description": "The garage belongs to the apartment according to the articles of association."}, {"type_code": "Space in car port with electrical plug", "basis_for_possession_codes": ["Rented from the company"], "transfer_code": "Non-transferable", "basis_for_allocation_description": "The parking space is rented from the company and includes an electrical plug."}, {"type_code": "Electric car charging point", "basis_for_possession_codes": ["With a different group of shares"], "transfer_code": "No selection", "basis_for_allocation_description": "The electric car charging point is allocated with a different group of shares."}]}, "residential_property_overview_parking_spaces": {"title": "Parking spaces", "value": [{"parking_space_type_code": "Garage", "count": 10}]}, "parking_description": {"title": "Parking description", "value": "Two parking spaces, one covered."}, "furnished": {"title": "Furnished", "value": "No"}, "more_information_about_the_premises": {"title": "More information about the premises", "value": "The living quarters are spacious and well-lit, featuring large windows that allow ample natural light and a modern open floor plan that connects the kitchen, dining, and living areas seamlessly."}, "more_information_about_the_materials": {"title": "More information about the materials", "value": "The interior features high-quality materials, including hardwood flooring, marble countertops, and custom cabinetry. The walls are finished with premium paint, and the ceilings have decorative moldings."}, "transaction": {"title": "Transaction", "value": "The transaction includes the sale of the entire property, with all attached rights and obligations. The buyer will be responsible for any outstanding maintenance fees and property taxes as of the date of transfer."}, "transaction_includes": {"title": "Transaction includes", "value": "The transaction includes the sale of the property, all existing buildings, and any fixtures attached to the property, as well as the transfer of any existing rental agreements."}, "transaction_does_not_include": {"title": "Transaction does not include", "value": "The transaction does not include personal belongings, furniture, or any movable items not permanently attached to the property. The seller will also retain ownership of any leased equipment on the premises."}}}}}, "servicesAndTransportation": {"title": "Services & transportation", "content": {"services": {"title": "Services", "value": "Nearby amenities include grocery stores, schools, and public parks."}, "hobbiesAndActivities": {"title": "Hobbies and activities", "value": "The area offers various recreational activities, including hiking trails, a community swimming pool, and sports courts."}, "transportConnections": {"title": "Transport connections", "value": "The property is well-connected by public transportation, with a bus stop just a short walk away."}, "drivingDirections": {"title": "Driving directions", "value": "Take the main highway, exit at Maple Street, and continue straight for 2 miles. The property will be on your left."}}}}}