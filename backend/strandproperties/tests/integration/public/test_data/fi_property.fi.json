{"parameters": {"referenceCode": "Property2", "language": "fi"}, "images": ["http://localhost/test.png"], "coordinates": {"latitude": 40.0, "longitude": 30.0}, "events": [], "realtor": [353, 354], "metadata": {}, "body": {"description": {"title": "<PERSON><PERSON><PERSON>", "content": {"propertyTitle": {"title": "<PERSON><PERSON><PERSON>", "value": "xxx"}, "propertyDescription": {"title": "<PERSON><PERSON><PERSON> k<PERSON>", "value": "xxx"}}}, "basicInfo": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": {"reference": {"title": "Ko<PERSON>denumero", "value": "Property2"}, "propertyId": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "PROP12345"}, "propertyName": {"title": "<PERSON><PERSON><PERSON> nimi", "value": "Test Farm"}, "roomStructure": {"title": "<PERSON><PERSON><PERSON><PERSON> koko<PERSON>o", "value": "3BHK"}, "address": {"title": "Osoite", "value": "123 Main St A 5B 12345, Downtown"}, "residentialType": {"title": "Tyyppi", "value": "Parvekkeen sisäänkäynti"}, "ownershipType": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "OMA"}, "livingType": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Seniorital<PERSON>"}, "newBuilding": {"title": "Uudiskohde", "value": "K<PERSON><PERSON>ä"}, "rooms": {"title": "<PERSON><PERSON><PERSON>", "value": "3H"}, "builtSize": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pinta-ala", "value": "10 m2"}, "otherSpaces": {"title": "<PERSON>ut tilat", "value": "50 m2"}, "plotSize": {"title": "<PERSON><PERSON> pin<PERSON>-ala", "value": "89 m2"}, "totalSize": {"title": "Kokonaispinta-ala", "value": "99 m2"}, "sizeVerification": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> peruste", "value": ["<PERSON><PERSON><PERSON>"]}, "sizeDescription": {"title": "Lisätietoja pinta-alasta", "value": "Lisätietoa al<PERSON> mitta<PERSON>."}, "floors": {"title": "<PERSON><PERSON><PERSON>", "value": "3/4"}, "availability": {"title": "V<PERSON>ut<PERSON><PERSON>", "value": "Saatavilla"}}}, "priceAndCost": {"title": "price_and_cost", "content": {"price": {"title": "<PERSON><PERSON>", "content": {"startingPrice": {"title": "Lähtöhinta", "value": "222 €"}, "startingDebtFreePrice": {"title": "<PERSON><PERSON><PERSON>", "value": "123 €"}, "startingDebtShareAmount": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "value": "200000 €"}, "salePrice": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "987 €"}, "residentialShareOverviewDebtFreePrice": {"title": "<PERSON><PERSON><PERSON> hinta", "value": "250000 €"}, "residentialShareOverviewDebtShareAmount": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "50000 €"}, "residentialShareOverviewDebtShareAmountDescription": {"title": "Lisätietoa velk<PERSON>", "value": [{"language_code": "en", "text": "debt_share_additional_info"}]}, "commercialOverviewDebtFreePrice": {"title": "<PERSON><PERSON><PERSON> hinta", "value": "250000 €"}, "commercialOverviewDebtShareAmount": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "50000 €"}, "otherShareOverviewDebtFreePrice": {"title": "<PERSON><PERSON><PERSON> hinta", "value": "124515325 €"}, "otherShareOverviewDebtShareAmount": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3425325 €"}, "monthlyRent": {"title": "Kuukausivuokra", "value": "20000 €"}}}, "costs": {"title": "Vastike", "content": {"costs": {"title": "Vastike", "value": ["Kuukausittainen kiinteistövero.: 100 €", "Kuukausimaksu jätehuollosta.: 50 €", "Kuukausimaksu TV-liittymästä.: 25 €"]}, "costsDescription": {"title": "Lisätiet<PERSON> maksuista", "value": "Tämä maksu kattaa yhteisten tilojen ja käyttöpalvelujen ylläpidon."}, "managementCost": {"title": "Hoitovastike", "value": "10.0 €"}, "financingCost": {"title": "Rahoitusvastike", "value": "1500€ (vain korko) ja jälkeen 2025-01-01, 2500€"}, "plotCost": {"title": "Tonttivastike", "value": "Kuukausikustannus 2000€ (Maa lunastettavissa: Kyllä ja lunastusosuus 50000€)"}, "specialCost": {"title": "<PERSON><PERSON>", "value": "11.0 €"}, "repaymentHolidayPeriod": {"title": "Lyhennysvapaajakso", "value": "12 month"}, "startDateOfTheRepaymentHolidayPeriod": {"title": "Lyhennysvapa<PERSON>ks<PERSON>äiv<PERSON>", "value": "10.08.2024"}, "endDateOfTheRepaymentHolidayPeriod": {"title": "Lyhennysvapaajakso päättymispäivä", "value": "10.08.2024"}, "financialChargeAfterTheRepaymentHoliday": {"title": "Rahoitusvastike lyhennysvapaan j<PERSON>lk<PERSON>", "value": "1200€"}, "moreInformationAboutTheChargesLink": {"title": "Lisää tietoa vastikkeista linkki", "value": "https://strand.fi"}}}, "additionalDetails": {"title": "Lisätietoja", "content": {"isRented": {"title": "Vuokrattu", "value": "K<PERSON><PERSON>ä"}, "leaseDetails": {"title": "Lisätietoja vuokrasta", "value": {"payment_date": "2024-09-01", "deposit_description": "Security deposit for the lease agreement.", "deposit_amount": 1500, "currency_code": "EUR", "lease_type_code": "Kiinteä aika", "fixed_lease_start_date": "2024-09-01", "fixed_lease_end_date": "2025-09-01", "lease_start_date": "2024-09-01"}}}}, "plot": {"title": "<PERSON><PERSON><PERSON>", "content": {"plotYearlyRent": {"title": "<PERSON><PERSON>", "value": "120000 €"}, "moreInformationAboutThePlotRedemption": {"title": "Lisätietoa tontin luna<PERSON>", "value": [{"language_code": "en", "text": "plot_redemption_info"}]}, "moreInformationAboutThePlotRedemptionLink": {"title": "Lisätietoa ton<PERSON>", "value": "https://strand.fi"}, "informationAboutTheLandLeaseAgreement": {"title": "<PERSON>ieto ma<PERSON><PERSON><PERSON> v<PERSON>", "value": [{"language_code": "en", "text": "plot_rental_agreement"}]}, "landLeaseAgreementLink": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "https://strand.fi"}}}}}, "additionalInformation": {"title": "Lisätietoja", "content": {"administration": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Kerrostaloyhtiö"}, "sauna": {"title": "Sauna", "value": "K<PERSON><PERSON>ä"}, "terrace": {"title": "<PERSON><PERSON><PERSON>", "value": "K<PERSON><PERSON>ä"}, "balcony": {"title": "Parveke", "value": "K<PERSON><PERSON>ä"}, "toilets": {"title": "<PERSON><PERSON><PERSON>", "value": 4}, "bedrooms": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 7}, "viewDescription": {"title": "Näkymän kuva<PERSON>", "value": "Näytä kuva<PERSON>."}, "installationsByShareholder": {"title": "Osakkaan tekemät asennukset", "value": "Osakkeenomist<PERSON><PERSON> as<PERSON>."}, "renovations": {"title": "Tehdyt remontit", "value": ["Keittiöremontti valmis.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> putkiremontti käynnissä.", "Julkisivuremontti suunnitteilla."]}, "renovationsDescription": {"title": "Lisätietoa remonteista", "value": "Asunnossa on tehty useita remontteja, kuten keit<PERSON>, putkisto ja julkisivu."}, "condition": {"title": "<PERSON><PERSON>", "value": {"code": "Hyvä", "description": "Kiinteistö on hyvässä kunnossa, pienillä kulumilla."}}, "damages": {"title": "Vahingot", "value": [{"damages_exists_code": "K<PERSON><PERSON>ä", "damage_type_code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damage_date": "2023-05-01", "cause_description": "A pipe burst causing water damage in the kitchen.", "extent_description": "Water damage affected 20 square meters.", "repair_description": "Replaced damaged flooring and drywall.", "attachment_id": "doc_123"}, {"damages_exists_code": "<PERSON>i", "damage_type_code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damage_date": null, "cause_description": null, "extent_description": null, "repair_description": null, "attachment_id": null}]}, "inspections": {"title": "Tarkastukset", "value": [{"date": "2023-06-15", "description": "Kuntotar<PERSON><PERSON> tehtiin, ja siinä havaittiin vähäisiä ongelmia, jotka on korjattava.", "type_code": "CONDITION_ASSESSMENT"}, {"date": "2022-10-10", "description": "Vesi- ja vie<PERSON><PERSON><PERSON><PERSON>kia koskeva kuntokartoitus tehtiin, ja se osoitti, ett<PERSON> putket on vaihdettava seuraavien viiden vuoden kuluessa.", "type_code": "CONDITION_SURVEY"}, {"date": "2021-08-05", "description": "<PERSON><PERSON><PERSON><PERSON> pitkän a<PERSON><PERSON><PERSON>, jossa esitetään tarvittavat remontit ja niiden arvioidut budjetit seuraaville kymmenelle vuodelle.", "type_code": "LONG_TERM_PLAN"}]}, "inspectionsDescription": {"title": "Lisätietoja tarkastuksista", "value": "Asunto-osakeyhtiö on suorittanut useita tarkastuksia, kuten kuntotarkastuksia, kartoituksia ja pitkän aikavälin suunnittelua. Nämä tarkastukset varmistavat rakennuksen rakenteellisen eheyden ja tunnistavat tarvittavat remontit."}, "modifications": {"title": "Muutokset", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa y<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>oneessa te<PERSON>dy<PERSON>ä muutoksista."}, "asbestosMapping": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": {"is_asbestos_mapping_done": "K<PERSON><PERSON>ä", "is_asbestos_mapping_report_available": "K<PERSON><PERSON>ä", "construction_materials_might_have_asbestos": "<PERSON>i", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on tehty ja rap<PERSON><PERSON> on saatavilla."}}, "housingComfort": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Lähellä oleva rakenn<PERSON>ömaa saattaa aiheuttaa melua päivän aikana."}, "fitForWinterHabitation": {"title": "Soveltuu talviasumiseen", "value": "K<PERSON><PERSON>ä"}, "smokingAllowed": {"title": "Tu<PERSON><PERSON><PERSON>i sallittu", "value": "<PERSON>i"}, "petsAllowed": {"title": "<PERSON><PERSON><PERSON><PERSON> sallittu", "value": "<PERSON>i"}, "accessible": {"title": "Esteetön", "value": "<PERSON>i"}, "heating": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asunto lämmitetään keskuslämmityksellä."}, "underfloorHeatingDescription": {"title": "underfloor_heating_description", "value": "Lattialämmitys on käytettävissä kylpyhuoneissa."}, "furnished": {"title": "Kalustettu", "value": "<PERSON>i"}, "hearth": {"title": "<PERSON><PERSON><PERSON>", "value": {"type_codes": ["Takka", "<PERSON><PERSON><PERSON> liesi"], "type_other_description": "<PERSON><PERSON><PERSON><PERSON> on raut<PERSON><PERSON>ina ja perinteinen takka.", "description": "Asunnossa on sekä rautakamiina että takka, jotka tarjoavat erinoma<PERSON>t lämmitysvaihtoehdot."}}, "shareCertification": {"title": "Osakekirja", "value": {"share_certificate_available": "K<PERSON><PERSON>ä", "mortgage_declared_separately": "<PERSON>i", "share_certificate_used_as_mortgage": "K<PERSON><PERSON>ä", "mortgage_holder": "Bank XYZ", "liability_amount": 150000, "currency_code": "EUR", "share_certificate_location": "In the possession of the bank", "share_group_identifiers": ["1001-1010"], "quantity_of_shares": 10, "share_certificate_form_code": "Digitaalinen", "permanent_dwelling_identifier": "VTJ-PHT-**********"}}, "redemption": {"title": "Lunastus", "value": {"redeemable_by_housing_company": "K<PERSON><PERSON>ä", "redeemable_by_existing_shareholders": "<PERSON>i", "redemption_right_applies_to_all_shares": "<PERSON>i", "other_restrictions": [{"language_code": "en", "text": "No other restrictions apply."}]}}, "moreInformation": {"title": "Lisätietoja", "value": "Tämä kii<PERSON> on hyvin hoidettu ja sopii erinoma<PERSON> per<PERSON>, jotka etsivät rauhallista asuinaluetta."}, "moreInformationLink": {"title": "<PERSON><PERSON>", "value": [{"title": "Virtuaali<PERSON><PERSON><PERSON>", "url": "https://example.com/virtual-tour", "type_code": "Virtuaalinen esitys"}, {"title": "<PERSON><PERSON>eistö<PERSON> video", "url": "https://example.com/property-video", "type_code": "Esittelyvideo"}]}}}, "housingCompany": {"title": "Taloyhtiö", "content": {"housingCompany": {"title": "Taloyhtiö", "value": "Sample Housing Company"}, "businessId": {"title": "Y-tunnus", "value": "BIZ1234567"}, "address": {"title": "Osoite", "value": "456 Elm St 67890, Uptown"}, "manager": {"title": "Isännöitsijä", "value": "*********"}, "houseManagerCertificateDate": {"title": "Isännöitsijäntodistuksen päivämäärä", "value": "CERT123456 - Issued by Certifying Authority on 2023-01-01"}, "maintenance": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": {"type_codes": ["<PERSON><PERSON><PERSON>", "Asukkaiden toimesta"], "description": "Ylläpidosta vastaavat talonmies ja asukka<PERSON>."}}, "propertyId": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "property_identifier"}, "construction": {"title": "<PERSON><PERSON><PERSON>", "value": "\n            Rakennuttaja: ABC Constructions Ltd.\n            Rakennusvuosi: 2020\n            Rakennusvaihe: MOVE_IN_READY\n            Käyttöönottovuosi: 2021\n            Lisätietoja: Rakennustyöt valmistuivat vuonna 2020, muutto valmis vuonna 2021.\n        "}, "buildings": {"title": "Rakennukset", "value": "\n            Materiaali: BRICK, <PERSON><PERSON><PERSON><PERSON><PERSON> on pääasiassa rakennettu tiilestä.\n            Lämmitys: DISTRICT_HEATING, Ra<PERSON><PERSON>ksessa käytetään kaukolämpöä.\n            Ilmanvaihto: FORCED_EXHAUST, Ilmanvaihtojärjestelmä käyttää koneellista poistoa.\n            Katto: GABLED <PERSON>to on harjakatto, joka on valmistettu peltistä.\n            Hissi: <PERSON><PERSON><PERSON><PERSON>\n            <PERSON> suoraan asuntoon: Ei\n            Muut tilat: ATTIC_STORAGE\n        "}, "yardDescription": {"title": "<PERSON><PERSON> k<PERSON>", "value": "<PERSON><PERSON> on tilava ja sisältää leikkipaikan."}, "plot": {"title": "<PERSON><PERSON><PERSON>", "value": []}, "internetConnections": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "\n            Laajakaista saatavilla: Kyllä\n            Kuitu: <PERSON><PERSON>ajakaista: Kyllä\n            Laajakaistaoperaattori: NetProvider Inc.\n            Sisäiset verkkokaapeloinnit: None\n            Lisätietoja nettiyhteydestä: Ra<PERSON><PERSON><PERSON><PERSON><PERSON> on sisäiset verkkokaapeloinnit.\n        "}, "television": {"title": "TV-yhteydet", "value": "\n            Tyyppi: CABLE IPTV\n            Lisätietoja TV-yhteydestä: Rakennuksessa on saatavilla kaapeli- ja IPTV-palvelut.\n        "}, "lastAnnualMeeting": {"title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "value": "10.08.2024"}, "nextAnnualMeeting": {"title": "<PERSON><PERSON><PERSON> vuosik<PERSON>", "value": "10.08.2024"}, "identifiedDeficiencies": {"title": "<PERSON><PERSON><PERSON><PERSON> puutteet", "value": "<PERSON><PERSON> k<PERSON>."}, "incurringLiabilities": {"title": "Velvoitteet", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> on velkaa viimeaikaisista remonteista."}, "repairAndMaintenanceAgreements": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja h<PERSON><PERSON><PERSON><PERSON>", "value": "Asunto-osakeyhtiöllä on ylläpitosopimus ABC Maintenance Co:n kanssa."}, "finances": {"title": "<PERSON><PERSON>", "value": {"loan_amount": 500000, "mortgage_amount": 200000, "rent_revenue": 150000, "bank_account_credit_limit": 50000, "currency_code": "EUR", "financing_fee_interest_only_period": "12 month", "financing_fee_interest_only_startdate": "10.08.2024", "financing_fee_interest_only_enddate": "10.08.2024", "financing_fee_after_interest_only": 1200, "management_charge_after_interest_only": 2000, "management_charges_info_link": "https://strand.fi"}}, "energyCertificate": {"title": "Ener<PERSON><PERSON><PERSON><PERSON>", "value": {"type_code": "B_2018", "description": "Vuonna 2018 myönnetty energiatodistus, luokka B."}}, "asbestosMapping": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": {"is_asbestos_mapping_done": "K<PERSON><PERSON>ä", "is_asbestos_mapping_report_available": "K<PERSON><PERSON>ä", "construction_materials_might_have_asbestos": "<PERSON>i", "description": "Asbestikartoitus tehty. Raportti saatavilla."}}, "parkingSpaces": {"title": "<PERSON><PERSON>", "value": [{"parking_space_type_code": "Autotalli", "count": 10}, {"parking_space_type_code": "Ulkopysäköintipaikka sähköpistokkeella", "count": 20}, {"parking_space_type_code": "Sähköauton latauspiste", "count": 5}]}, "parkingSpacesDescription": {"title": "Lisätietoa auton säilytyksestä", "value": "Rakennuksessa on 10 autotallipaikkaa, 20 ulkopysäköintipaikkaa sähköpistokkeella ja 5 latauspistettä sähköautoille."}, "housingCompanyPremiseSpecification": {"title": "Yhteiset tilat", "value": [{"premise_type_code": "APARTMENT", "count": 50, "area": {"value": 5000, "area_unit_code": "M2"}, "managed_by_housing_company_count": 45, "area_managed_by_housing_company": {"value": 4500, "area_unit_code": "M2"}}, {"premise_type_code": "BUSINESS_PREMISE", "count": 10, "area": {"value": 1000, "area_unit_code": "M2"}, "managed_by_housing_company_count": 8, "area_managed_by_housing_company": {"value": 800, "area_unit_code": "M2"}}, {"premise_type_code": "OTHER_PREMISE", "count": 5, "area": {"value": 500, "area_unit_code": "M2"}, "managed_by_housing_company_count": 3, "area_managed_by_housing_company": {"value": 300, "area_unit_code": "M2"}}]}, "housingCompanyPremiseSpecificationDescription": {"title": "<PERSON><PERSON><PERSON><PERSON> til<PERSON>n kuvaus", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 45:ttä 50:stä <PERSON><PERSON><PERSON><PERSON>istost<PERSON>, 8:aa 10:stä liiketilasta ja 3:ta 5:stä muusta tilasta."}, "renovations": {"title": "Tehdyt remontit", "value": [{"date": "2023-01-15", "decided_by_general_meeting": "K<PERSON><PERSON>ä", "description": "Keittiön remontti suunniteltiin ja se toteutetaan vuoden 2023 toisella neljänneksellä.", "is_housing_company_notified": "K<PERSON><PERSON>ä", "status_code": "PLANNED", "type_code": "KITCHEN", "type_other_description": null}, {"date": "2022-09-10", "decided_by_general_meeting": "<PERSON>i", "description": "<PERSON><PERSON><PERSON><PERSON> on käynnissä ja sen odotetaan valmistuvan vuoden loppuun mennessä.", "is_housing_company_notified": "K<PERSON><PERSON>ä", "status_code": "IN_PROGRESS", "type_code": "ROOF", "type_other_description": null}, {"date": "2021-05-20", "decided_by_general_meeting": "K<PERSON><PERSON>ä", "description": "Julkisivuremontti saatiin onnistuneesti päätökseen.", "is_housing_company_notified": "K<PERSON><PERSON>ä", "status_code": "FINISHED", "type_code": "FACADE", "type_other_description": null}]}, "renovationsDescription": {"title": "Lisätietoa remonteista", "value": "Asunto-osakeyhtiössä on tehty useita remontteja, kuten keit<PERSON>, katon ja julkisivun kunnostus. Näiden remonttien tarkoituksena oli nykyaikaistaa rakennus ja varmistaa sen pitkäikäisyys."}, "plannedRenovations": {"title": "Tulevat remontit", "value": "Asunto-osakeyhtiö on suunnitellut useita remontteja tuleville vuosille, mukaan lukien sähköjärjestelmien päivitys ja uusien ikkunoiden asennus."}, "inspections": {"title": "Tarkastukset", "value": [{"date": "2023-06-15", "description": "Kuntotar<PERSON><PERSON> tehtiin, ja siinä havaittiin vähäisiä ongelmia, jotka on korjattava.", "type_code": "CONDITION_ASSESSMENT"}, {"date": "2022-10-10", "description": "Vesi- ja vie<PERSON><PERSON><PERSON><PERSON>kia koskeva kuntokartoitus tehtiin, ja se osoitti, ett<PERSON> putket on vaihdettava seuraavien viiden vuoden kuluessa.", "type_code": "CONDITION_SURVEY"}, {"date": "2021-08-05", "description": "<PERSON><PERSON><PERSON><PERSON> pitkän a<PERSON><PERSON><PERSON>, jossa esitetään tarvittavat remontit ja niiden arvioidut budjetit seuraaville kymmenelle vuodelle.", "type_code": "LONG_TERM_PLAN"}]}, "inspectionsDescription": {"title": "Lisätietoja tarkastuksista", "value": "Asunto-osakeyhtiö on suorittanut useita tarkastuksia, kuten kuntotarkastuksia, kartoituksia ja pitkän aikavälin suunnittelua. Nämä tarkastukset varmistavat rakennuksen rakenteellisen eheyden ja tunnistavat tarvittavat remontit."}, "additionalInformation": {"title": "Lisätietoja", "value": "Asunto-o<PERSON>keyhtiössä on my<PERSON><PERSON> kuntosali ja yhteinen puutarha, jotka ovat kaikkien asukkaiden käytettävissä. Yhteiset alueet pidetään hyvin hoidettuina ja niitä siivotaan säännöllisesti."}, "listOfSharesTransferred": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lue<PERSON>", "value": "K<PERSON><PERSON>ä"}, "digitalShares": {"title": "Digitaaliset osakkeet", "value": "SHARE_GROUP_001"}}}, "spacesAndMaterials": {"title": "<PERSON>ilat ja materiaalit", "content": {"kitchen_and_dining_room": {"title": "Keittiö ja ruokailut<PERSON>", "content": {"kitchen": {"title": "Keittiö", "value": [{"type_code": "KITCHEN", "description": "Moderni keittiö, jossa on ruostumattomasta teräksestä valmistetut kodinkoneet ja runsaasti työtasotilaa.", "features": [{"feature_code": "ISLAND", "description": "Keittiösa<PERSON>ke istumapaikkoineen ja lisäsäilytystiloineen."}], "floor_material_codes": ["Tilattu"], "wall_material_codes": ["<PERSON><PERSON><PERSON><PERSON> la<PERSON>a"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "dining_room": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "DINING_ROOM", "description": "<PERSON><PERSON><PERSON>, jossa on tyylikäs valaistus ja kätevä pääsy keittiöön.", "features": [{"feature_code": "CHANDLIER", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kattok<PERSON>u ruokasalin keski<PERSON>."}], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "bathroom": {"title": "Kylpyhuone", "content": {"bathroom": {"title": "Kylpyhuone", "value": [{"type_code": "BATH_ROOM", "description": "<PERSON><PERSON>, jossa on su<PERSON><PERSON><PERSON>a ja laatt<PERSON>a.", "features": [], "floor_material_codes": ["Tilattu"], "wall_material_codes": ["<PERSON><PERSON><PERSON><PERSON> la<PERSON>a"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "bedroom": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content": {"bedroom": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "BEDROOM", "description": "<PERSON><PERSON><PERSON> ma<PERSON>, jossa on peh<PERSON><PERSON> kokolattiamatto ja kiinteät vaatekaapit.", "features": [{"feature_code": "BUILT_IN_WARDROBE", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on kiinteät vaatekaapit, j<PERSON><PERSON> on runsaasti säilytystilaa."}], "floor_material_codes": ["Korkeakylly"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "sauna": {"title": "Sauna", "content": {"sauna": {"title": "Sauna", "value": [{"type_code": "SAUNA", "description": "<PERSON><PERSON><PERSON><PERSON> suomalainen sauna, jossa on puupanelointi ja kiviki<PERSON>s.", "features": [], "floor_material_codes": ["<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "toilet": {"title": "<PERSON><PERSON><PERSON>", "content": {"toilet": {"title": "<PERSON><PERSON><PERSON>", "value": [{"type_code": "TOILET", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on modernit kalusteet.", "features": [], "floor_material_codes": ["Tilattu"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "utility_room": {"title": "<PERSON><PERSON><PERSON>itohu<PERSON>", "content": {"utility_room": {"title": "<PERSON><PERSON><PERSON>itohu<PERSON>", "value": [{"type_code": "UTILITY_ROOM", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on pesulapal<PERSON>ut ja s<PERSON><PERSON><PERSON><PERSON>.", "features": [], "floor_material_codes": ["<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "utility_room_description": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ku<PERSON>", "value": "Teknisten palveluiden huoneessa on pääsähkötaulu, lämminvesivaraaja ja muut tärkeät rakennuksen palvelut."}}}, "living_room": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": {"living_room": {"title": "<PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "LIVING_ROOM", "description": "<PERSON><PERSON><PERSON>, jossa on suuri ikkuna ja takka.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "closet": {"title": "Komero", "content": {"walk_in_closet": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "WALK_IN_CLOSET", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on sisäänrakennetut hyllyt ja runsaasti tilaa.", "features": [], "floor_material_codes": ["Ko<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "loft": {"title": "<PERSON><PERSON><PERSON>", "content": {"loft": {"title": "<PERSON><PERSON><PERSON>", "value": [{"type_code": "LOFT", "description": "<PERSON><PERSON><PERSON>, joka soveltuu pieneksi makuuhuoneeksi tai ty<PERSON>tilaksi.", "features": [], "floor_material_codes": ["<PERSON><PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "workroom_and_library": {"title": "Työhuone ja kirjasto", "content": {"library": {"title": "<PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "LIBRARY", "description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, jossa on sisäänrakennetut kirjahyllyt ja lukutuolit.", "features": [], "floor_material_codes": ["Vinylkorko"], "wall_material_codes": ["<PERSON><PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "study": {"title": "<PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "STUDY", "description": "<PERSON><PERSON><PERSON><PERSON>, jossa on suuri työp<PERSON>ytä ja runsaasti valaistusta.", "features": [], "floor_material_codes": ["Ko<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "area_halls": {"title": "Aulahallit", "content": {"hall": {"title": "<PERSON><PERSON>", "value": [{"type_code": "HALL", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on la<PERSON><PERSON><PERSON>a ja kattok<PERSON>u.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "hallway": {"title": "Käytävä", "value": [{"type_code": "HALLWAY", "description": "<PERSON><PERSON><PERSON> k<PERSON>, joka yhdist<PERSON><PERSON> kaik<PERSON> huo<PERSON>, parkettilattia.", "features": [], "floor_material_codes": ["Pa<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "draught_lobby": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": [{"type_code": "DRAUGHT_LOBBY", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, joka estää kylmän ilman pääsyn päärakennukseen.", "features": [], "floor_material_codes": ["Tilattu"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}}}, "balcony_terrace_and_patio": {"title": "<PERSON><PERSON><PERSON>, terassi ja patio", "content": {"terrace_description": {"title": "<PERSON><PERSON><PERSON>", "value": {"is_glazed_terrace": "K<PERSON><PERSON>ä", "description": "<PERSON><PERSON><PERSON><PERSON> suuntautuva lasitettu terassi, joka on ihanteellinen auringonlaskun nauttimiseen.", "compass_points": ["<PERSON><PERSON><PERSON><PERSON>"], "terrace_glass_maintenance_responsibility_code": "Residentit"}}, "balcony_description": {"title": "Parvekkeen kuvaus", "value": {"type_codes": ["Lasitettu", "Peruutettu"], "type_other_description": null, "description": "Lasitettu ja sis<PERSON><PERSON>n<PERSON>y parveke, joka suuntautuu kaakkoon ja on täydellinen aamun auringon<PERSON>olle.", "compass_points": ["<PERSON><PERSON><PERSON><PERSON>"], "balcony_glass_maintenance_responsibility_code": "HOUSING_COMPANY"}}, "patio": {"title": "<PERSON><PERSON>", "value": "Tilava patio, jossa on kiv<PERSON><PERSON>a, sopii erinoma<PERSON> ul<PERSON>iin ja g<PERSON>ukseen."}, "yard": {"title": "Pi<PERSON>", "value": {"has_private_yard": "K<PERSON><PERSON>ä", "basis_for_possession_code": "Yhtiöjärjestys", "basis_for_possession_other_description": null, "description": "<PERSON><PERSON><PERSON> ho<PERSON>u yks<PERSON> piha, jossa on puutarha ja patioalue."}}}}, "storage_rooms_and_basement": {"title": "Varasto ja kellari", "content": {"storages": {"title": "<PERSON><PERSON><PERSON>", "value": [{"type_code": "<PERSON><PERSON><PERSON><PERSON>", "basis_for_possession_codes": ["Ku<PERSON>u yhtiöjärjestyksen mukaan asuntoon"], "transfer_code": "Siirrettävä", "basis_for_allocation_description": "<PERSON><PERSON><PERSON><PERSON>."}]}, "storage_usage_limitations": {"title": "Varaston käyttöraj<PERSON>uk<PERSON>", "value": "Varastoa voidaan käyttää vain henkilökohtaisiin ta<PERSON>."}, "storage_description": {"title": "Lisätietoja varastosta", "value": "<PERSON><PERSON><PERSON> var<PERSON><PERSON> k<PERSON>, so<PERSON>i ka<PERSON> ja työkalujen säilyty<PERSON>."}, "refrigerated_cellar": {"title": "Kylmäkellari", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kella<PERSON>, joka on suunniteltu helposti pilaantuvien tuotteiden pitkäaikaiseen säilytykseen, s<PERSON><PERSON><PERSON><PERSON><PERSON> tasaisen al<PERSON>sen lä<PERSON>."}}}, "other": {"title": "<PERSON><PERSON>", "content": {"other": {"title": "<PERSON><PERSON>", "value": [{"type_code": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> huo<PERSON>, jota <PERSON>aan kä<PERSON><PERSON><PERSON> toim<PERSON>, v<PERSON><PERSON><PERSON>ena tai harrastetilana.", "features": [{"feature_code": "FLEXIBLE_SPACE", "description": "<PERSON><PERSON> voidaan helposti mukauttaa eri käyttötarkoituksiin."}], "floor_material_codes": ["Vinyl"], "wall_material_codes": ["<PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}, {"type_code": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> huo<PERSON>, joka on suunni<PERSON><PERSON> musiikin har<PERSON>luun tai äänityks<PERSON>.", "features": [{"feature_code": "SOUNDPROOFING", "description": "<PERSON><PERSON> on t<PERSON><PERSON><PERSON>, mik<PERSON> tekee siitä i<PERSON>tee<PERSON>en musiikin har<PERSON>lu<PERSON>."}], "floor_material_codes": ["<PERSON><PERSON>"], "wall_material_codes": ["<PERSON><PERSON><PERSON>"], "ceiling_material_codes": ["<PERSON><PERSON><PERSON>"]}]}, "boiler_room": {"title": "Tekninentila", "value": "<PERSON><PERSON><PERSON> tu<PERSON><PERSON> ka<PERSON>, jossa on keskuslämmitysjärjestelmä ja lämminvesivaraaja, <PERSON><PERSON> p<PERSON><PERSON> huoltoa varten."}, "residential_share_overview_parking_spaces": {"title": "<PERSON><PERSON>", "value": [{"type_code": "Autotalli", "basis_for_possession_codes": ["Ku<PERSON>u yhtiöjärjestyksen mukaan asuntoon"], "transfer_code": "Siirrettävä", "basis_for_allocation_description": "Talli kuuluu asuntoon yht<PERSON>öjärjestyksen mukaan."}, {"type_code": "Tilaa autokatos sähköpistokkeella", "basis_for_possession_codes": ["Vuokrattu yhtiöltä"], "transfer_code": "<PERSON><PERSON> si<PERSON>", "basis_for_allocation_description": "Pysäköintipaikka on vuokrattu yhtiöltä ja siihen sisältyy sähköpistoke."}, {"type_code": "Sähköauton latauspiste", "basis_for_possession_codes": ["<PERSON><PERSON>"], "transfer_code": "<PERSON>i valintaa", "basis_for_allocation_description": "Sähköauton la<PERSON> on jaettu eri osa<PERSON><PERSON>jan kanssa."}]}, "residential_property_overview_parking_spaces": {"title": "<PERSON><PERSON>", "value": [{"parking_space_type_code": "Autotalli", "count": 10}]}, "parking_description": {"title": "Lisätietoja autonsäilytyksestä", "value": "<PERSON><PERSON><PERSON>, yksi katettu."}, "furnished": {"title": "Kalustettu", "value": "<PERSON>i"}, "more_information_about_the_premises": {"title": "Lisätietoja til<PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON> ovat tilavat ja hyvin v<PERSON>, ja ni<PERSON><PERSON> on suuret ikku<PERSON>, jotka antavat runsaasti luonnonvaloa sekä moderni avoin p<PERSON>, joka yhdistä<PERSON> keit<PERSON>, ruokailutilan ja oleskeluti<PERSON> sauma<PERSON>ti."}, "more_information_about_the_materials": {"title": "Lisätietoja materiaaleista", "value": "<PERSON><PERSON><PERSON> on valmistettu korkealaatuisista materiaaleista, kuten kova<PERSON>, marmoritasoista ja mittatilauskaapistoista. Seinät on viimeistelty laadukkaalla maalilla ja kattoihin on asennettu koristeellisia listoja."}, "transaction": {"title": "Lisätietoja ka<PERSON>a", "value": "<PERSON><PERSON><PERSON> kattaa koko kiinte<PERSON>ön myynnin kaikkine siihen liittyvine oike<PERSON>een ja velvollisuuk<PERSON>een. Ostaja on vastuussa kaikista maksamattomista ylläpitomaksuista ja kiinteistöveroista siirtopäivästä lähtien."}, "transaction_includes": {"title": "<PERSON><PERSON><PERSON><PERSON> ku<PERSON>u", "value": "<PERSON><PERSON><PERSON> kattaa kiinte<PERSON>, kaikkien olemassa olevien rakennusten ja kiinteistöön kiinnitettyjen laitteiden myynnin sekä olemassa olevien vuokrasopimusten siirron."}, "transaction_does_not_include": {"title": "<PERSON><PERSON><PERSON><PERSON> ei kuulu", "value": "Ka<PERSON><PERSON> ei sisällä henkilökohtaisia tavaro<PERSON>, huonekal<PERSON><PERSON> tai irtaim<PERSON>, joka ei ole pysyvästi kiinnitetty kiinteistöön. Myyjä säilyttää omistusoikeuden tiloissa olevaan vuokralaitteistoon."}}}}}, "servicesAndTransportation": {"title": "Palvelut ja liikenneyhteydet", "content": {"services": {"title": "Palvelut", "value": "<PERSON><PERSON><PERSON><PERSON> on muun muassa ruoka<PERSON>up<PERSON>, kouluja ja puistoja."}, "hobbiesAndActivities": {"title": "Harrastukset ja aktiviteetit", "value": "<PERSON><PERSON><PERSON> on tarjolla erilaisia vapaa-a<PERSON> a<PERSON>, kuten v<PERSON>, yhteinen uima-allas ja urheilukenttiä."}, "transportConnections": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> on hyvin yhdistetty julkiseen liikenteeseen, ja <PERSON><PERSON><PERSON><PERSON><PERSON> on lyhyen kävelymatkan päässä."}, "drivingDirections": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>, p<PERSON><PERSON> <PERSON> Streetillä ja jatka suoraan 2 mailia. <PERSON><PERSON>eistö on vasemmalla puolella."}}}}}