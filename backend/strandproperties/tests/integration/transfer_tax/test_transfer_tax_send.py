from contextlib import contextmanager
from datetime import date
from typing import List, Optional, Tuple
from unittest.mock import Mock, patch

import pytest
import requests
from pydantic import ValidationError
from sqlalchemy.orm import Session

from strandproperties.constants import EventLogAction, EventLogObjectType
from strandproperties.libs.aws import S3Service
from strandproperties.models.event_log import EventLog
from strandproperties.schemas.transfer_tax import (
    ErrorDetail,
    SenderType,
    SendReturnRequest,
    SendReturnResponse,
    SendReturnResponse200,
    SendReturnResponse400,
    TransferType,
)
from strandproperties.services.document_library.service import DocumentLibrary
from strandproperties.services.transfer_tax.mapper import TransferTaxReturnsMapper
from strandproperties.services.transfer_tax.send_transfer_tax_returns import (
    BuyerTransferTaxReturnsErrorResult,
    BuyerTransferTaxReturnsSuccessResult,
    DetailsOfSaleNotFoundError,
    UnableToSendTransferTaxReturnsError,
    UserNotAuthorizedError,
)


def test_raises_details_of_sale_not_found_error_when_details_of_sale_does_not_exist(
    send_transfer_tax_returns, non_existing_details_of_sale_id, realtor1
):
    with pytest.raises(DetailsOfSaleNotFoundError):
        send_transfer_tax_returns.run(
            details_of_sale_id=non_existing_details_of_sale_id,
            user_id=realtor1.id,
        )


def test_raises_user_not_authorized_error_when_user_is_not_authorized_to_send_transfer_tax_returns(
    send_transfer_tax_returns, details_of_sale1, realtor2
):
    with pytest.raises(UserNotAuthorizedError):
        send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor2.id,
        )


def test_raises_unable_to_send_transfer_tax_returns_error_when_missing_buyers(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
):
    details_of_sale1.buyers = []

    with pytest.raises(UnableToSendTransferTaxReturnsError):
        send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )


def test_raises_unable_to_send_transfer_tax_returns_error_when_missing_sellers(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
):
    details_of_sale1.sellers = []

    with pytest.raises(UnableToSendTransferTaxReturnsError):
        send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )


def test_allows_admin_to_send_transfer_tax_returns_for_any_property(
    send_transfer_tax_returns, details_of_sale1, admin1
):
    with success_response_from_transfer_tax_api():
        send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=admin1.id,
        )


def test_uses_mapper_to_produce_transfer_tax_request(
    send_transfer_tax_returns, transfer_tax_mapper, details_of_sale1, realtor1, buyer1
):
    expected_request = Mock()
    with patch.object(
        transfer_tax_mapper,
        "map_to_request",
        return_value=expected_request,
    ) as mock_map_to_request:
        with success_response_from_transfer_tax_api():
            send_transfer_tax_returns.run(
                details_of_sale_id=details_of_sale1.id,
                user_id=realtor1.id,
            )

        assert mock_map_to_request.call_count == 1
        mock_map_to_request.assert_called_with(
            details_of_sale1,
            buyer1,
        )


def test_converts_mapping_errors_to_error_result(
    send_transfer_tax_returns, transfer_tax_mapper, details_of_sale1, realtor1, buyer1
):
    error, expected_error_message = create_validation_error()
    with patch.object(transfer_tax_mapper, "map_to_request", side_effect=error):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.success == False
        assert result.buyers[0].buyer_id == buyer1.id
        assert isinstance(result.buyers[0], BuyerTransferTaxReturnsErrorResult)
        assert result.buyers[0].error_text == "Validation error"
        assert result.buyers[0].error_description_fi == [
            # this is intentionally in english
            expected_error_message
        ]
        assert result.buyers[0].error_description_en == [expected_error_message]


def test_converts_any_other_errors_to_error_result(
    send_transfer_tax_returns, transfer_tax_mapper, details_of_sale1, realtor1, buyer1
):
    with patch.object(
        transfer_tax_mapper, "map_to_request", side_effect=Exception("Some error")
    ):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.success == False
        assert result.buyers[0].buyer_id == buyer1.id
        assert isinstance(result.buyers[0], BuyerTransferTaxReturnsErrorResult)
        assert result.buyers[0].error_text == "System error"
        assert result.buyers[0].error_description_fi == [
            # this is intentionally in english
            "Some error"
        ]
        assert result.buyers[0].error_description_en == ["Some error"]


def test_processes_error_response_from_transfer_tax_api_correctly(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
    buyer1,
):
    error_details1 = ErrorDetail.create(
        error_code="UNKNOWN",
        error_description_fi="Error 1 fi",
        error_description_en="Error 1 en",
    )
    error_details2 = ErrorDetail.create(
        error_code="UNKNOWN",
        error_description_fi="Error 2 fi",
        error_description_en="Error 2 en",
    )

    error_response = SendReturnResponse400.create(
        error_code="UNKNOWN",
        error_text="Some error",
        error_details=[error_details1, error_details2],
    )

    with responses_from_transfer_tax_api([error_response]):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.success == False
        assert result.buyers[0].buyer_id == buyer1.id
        assert isinstance(result.buyers[0], BuyerTransferTaxReturnsErrorResult)
        assert result.buyers[0].error_text == error_response.error_text
        assert result.buyers[0].error_description_fi == [
            error_details1.error_description_fi,
            error_details2.error_description_fi,
        ]
        assert result.buyers[0].error_description_en == [
            error_details1.error_description_en,
            error_details2.error_description_en,
        ]


def test_processes_successful_response_from_vero_api_correctly(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
):
    expected_unique_identifier = "expected-unique-identifier"
    with success_response_from_transfer_tax_api(expected_unique_identifier):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.buyers[0].unique_identifier == expected_unique_identifier


def test_generates_pdf_from_request_sent_to_transfer_tax_api(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
    document_library,
):
    with success_response_from_transfer_tax_api():
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )
        assert_item_exists_in_library(document_library, result.buyers[0].document_id)


def test_sends_transfer_tax_returns_for_each_buyer(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
    buyer1,
    buyer2,
):
    with success_response_from_transfer_tax_api(count=2):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.success == True
        assert result.buyers[0].buyer_id == buyer1.id
        assert result.buyers[1].buyer_id == buyer2.id


def test_returns_errors_when_sending_fails_for_buyer(
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
    buyer1,
    buyer2,
):
    expected_unique_identifier = "expected-unique-identifier"
    with responses_from_transfer_tax_api(
        [
            SendReturnResponse200.create(
                unique_identifier=expected_unique_identifier,
                accepted_timestamp="2024-01-15T10:30:00.000Z",
            ),
            SendReturnResponse400.create(
                error_code="UNKNOWN",
                error_text="Some error",
            ),
        ]
    ):
        result = send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

        assert result.success == False
        assert result.buyers[0].buyer_id == buyer1.id
        assert isinstance(result.buyers[0], BuyerTransferTaxReturnsSuccessResult)
        assert result.buyers[1].buyer_id == buyer2.id
        assert isinstance(result.buyers[1], BuyerTransferTaxReturnsErrorResult)


def test_logs_event_when_transfer_tax_returns_are_sent(
    db_session,
    send_transfer_tax_returns,
    details_of_sale1,
    realtor1,
):
    with success_response_from_transfer_tax_api():
        send_transfer_tax_returns.run(
            details_of_sale_id=details_of_sale1.id,
            user_id=realtor1.id,
        )

    assert_event_logged(
        db_session=db_session,
        user_id=realtor1.id,
        details_of_sale_id=details_of_sale1.id,
        action=EventLogAction.TRANSFER_TAX_RETURN_SENT,
    )


@pytest.fixture(autouse=True)
def mock_s3_service(mocker):
    mocker.patch.object(S3Service, "upload")
    mocker.patch.object(S3Service, "delete_objects")


@contextmanager
def success_response_from_transfer_tax_api(
    identifier: Optional[str] = None, count: int = 1
):
    responses: List[SendReturnResponse] = [
        SendReturnResponse200.create(
            unique_identifier=identifier or "TEST123456",
            accepted_timestamp="2024-01-15T10:30:00.000Z",
        )
        for _ in range(count)
    ]
    with responses_from_transfer_tax_api(responses):
        yield


@contextmanager
def error_response_from_transfer_tax_api(count: int = 1):
    responses: List[SendReturnResponse] = [
        SendReturnResponse400.create(
            error_code="UNKNOWN",
            error_text="Some error",
        )
        for _ in range(count)
    ]
    with responses_from_transfer_tax_api(responses):
        yield


@contextmanager
def responses_from_transfer_tax_api(
    responses: List[SendReturnResponse],
):
    def response(url: str, *args, **kwargs):
        if not responses:
            raise RuntimeError("No more mock responses left to return.")

        response = response_queue.pop(0)

        result = Mock()
        result.status_code = 200 if isinstance(response, SendReturnResponse200) else 400
        result.json = Mock(return_value=response.model_dump())
        return result

    response_queue = responses.copy()

    with patch.object(requests, "post", side_effect=response):
        yield


def assert_item_exists_in_library(document_library: DocumentLibrary, id: int) -> None:
    item = document_library.get_item(id)
    assert item.id == id


def assert_event_logged(
    db_session: Session,
    details_of_sale_id: int,
    action: EventLogAction,
    user_id: int,
) -> None:
    event_log = fetch_event_log(
        db_session=db_session,
        details_of_sale_id=details_of_sale_id,
        action=action,
        actor_id=user_id,
    )
    assert event_log is not None


def fetch_event_log(
    db_session: Session,
    details_of_sale_id: int,
    action: EventLogAction,
    actor_id: int,
):
    return (
        db_session.query(EventLog)
        .filter(
            EventLog.object_type == EventLogObjectType.DETAILS_OF_SALE,
            EventLog.object_id == details_of_sale_id,
            EventLog.action == action,
            EventLog.actor_id == actor_id,
        )
        .first()
    )


def create_validation_error() -> Tuple[ValidationError, str]:
    try:
        SendReturnRequest.create(
            sender=SenderType.REAL_ESTATE_AGENT,
            signing_date=date.today(),
            transfer_type=TransferType.SALE,
            asset_details=[],
        )

        raise RuntimeError("Should not happen")
    except ValidationError as e:
        return (e, "Value error, AssetDetails must contain at least one item")
