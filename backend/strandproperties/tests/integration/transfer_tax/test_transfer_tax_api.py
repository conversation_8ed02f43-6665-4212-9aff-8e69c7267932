import pytest

from strandproperties.services.transfer_tax.send_transfer_tax_returns import (
    BuyerTransferTaxReturnsErrorResult,
    BuyerTransferTaxReturnsSuccessResult,
    DetailsOfSaleNotFoundError,
    SendTransferTaxReturnsResult,
    UnableToSendTransferTaxReturnsError,
    UserNotAuthorizedError,
)


def test_api_responds_with_403_when_user_not_authorized_to_send_transfer_tax_returns(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    admin1,
    details_of_sale1,
):
    mock_send_transfer_tax_returns_run.side_effect = UserNotAuthorizedError(
        details_of_sale1.id, admin1.id
    )

    api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=403,
    )


def test_api_responds_with_404_when_details_of_sale_not_found(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    details_of_sale1,
):
    mock_send_transfer_tax_returns_run.side_effect = DetailsOfSaleNotFoundError(
        details_of_sale1.id
    )

    api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=404,
    )


def test_api_responds_with_409_when_unable_to_send_transfer_tax_returns(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    details_of_sale1,
):
    mock_send_transfer_tax_returns_run.side_effect = (
        UnableToSendTransferTaxReturnsError(details_of_sale1.id)
    )

    api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=409,
    )


def test_api_responds_with_200_when_transfer_tax_returns_are_sent(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    admin1,
    details_of_sale1,
):
    api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=200,
    )

    mock_send_transfer_tax_returns_run.assert_called_once_with(
        details_of_sale_id=details_of_sale1.id,
        user_id=admin1.id,
    )


def test_api_maps_success_result_to_response(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    buyer1,
    details_of_sale1,
):
    buyer_result = BuyerTransferTaxReturnsSuccessResult(
        buyer_id=buyer1.id,
        unique_identifier="1234567890",
        document_id=1,
    )
    mock_send_transfer_tax_returns_run.return_value = SendTransferTaxReturnsResult(
        success=True,
        buyers=[buyer_result],
    )

    response = api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=200,
    )

    assert response["success"] == True
    assert response["buyers"][0]["buyerId"] == buyer1.id
    assert response["buyers"][0]["uniqueIdentifier"] == buyer_result.unique_identifier
    assert response["buyers"][0]["documentId"] == buyer_result.document_id


def test_api_maps_error_result_to_response(
    mock_send_transfer_tax_returns_run,
    route_url,
    api,
    buyer1,
    details_of_sale1,
):
    buyer_result = BuyerTransferTaxReturnsErrorResult(
        buyer_id=buyer1.id,
        error_text="Error",
        error_description_fi=["Error fi"],
        error_description_en=["Error en"],
    )
    mock_send_transfer_tax_returns_run.return_value = SendTransferTaxReturnsResult(
        success=False,
        buyers=[buyer_result],
    )

    response = api(
        "post",
        route_url(
            "details_of_sale.send_transfer_tax_returns",
            id=details_of_sale1.id,
        ),
        status=200,
    )

    assert response["success"] == False
    assert response["buyers"][0]["buyerId"] == buyer1.id
    assert response["buyers"][0]["errorText"] == buyer_result.error_text
    assert (
        response["buyers"][0]["errorDescriptionFi"] == buyer_result.error_description_fi
    )
    assert (
        response["buyers"][0]["errorDescriptionEn"] == buyer_result.error_description_en
    )


@pytest.fixture(autouse=True)
def mock_transfer_tax_client(mocker):
    mock_client = mocker.Mock()
    mocker.patch(
        "strandproperties.config.AppConfig.get_transfer_tax_client",
        return_value=mock_client,
    )


@pytest.fixture
def mock_send_transfer_tax_returns_run(mocker):
    return mocker.patch(
        "strandproperties.services.transfer_tax.send_transfer_tax_returns.SendTransferTaxReturns.run",
    )
