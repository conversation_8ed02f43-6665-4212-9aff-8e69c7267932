import pytest
from sqlalchemy.orm import Session

from strandproperties.constants import DataSource, RoleType
from strandproperties.models.contact import Contact
from strandproperties.models.document_library import OwnerType
from strandproperties.models.fi_details_of_sale import (
    FIBuyerDetailsOfSale,
    FIDetailsOfSale,
    FISellerDetailsOfSale,
)
from strandproperties.models.fi_property import FIProperty
from strandproperties.services.document_library.service import DocumentLibrary
from strandproperties.services.transfer_tax.mapper import TransferTaxReturnsMapper
from strandproperties.services.transfer_tax.pdf import TransferTaxReturnsPdfGenerator
from strandproperties.services.transfer_tax.send_transfer_tax_returns import (
    SendTransferTaxReturns,
)
from strandproperties.services.transfer_tax.transfer_tax_client import TransferTaxClient


@pytest.fixture
def property1(
    db_session,
    organization1,
    realtor1,
    fi_property_type1,
    fi_realty1,
    fi_housing_company1,
) -> FIProperty:
    result = FIProperty(
        organization_id=organization1.id,
        is_exclusive=True,
        data_source=DataSource.STRAND,
        reference="SPCRM1001",
        fi_property_type_id=fi_property_type1.id,
        fi_realty_id=fi_realty1.id,
        fi_housing_company_id=fi_housing_company1.id,
    )
    result.realtor_users = [realtor1]
    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def document_library(db_session, realtor1, property1) -> DocumentLibrary:
    return DocumentLibrary(
        db_session=db_session,
        user_id=realtor1.id,
        owner_type=OwnerType.FI_PROPERTY,
        owner_id=property1.id,
    )


@pytest.fixture
def transfer_tax_client() -> TransferTaxClient:
    return TransferTaxClient(
        software_key="test-key", base_url="http://example.com", user_agent="agent"
    )


@pytest.fixture
def transfer_tax_pdf_generator() -> TransferTaxReturnsPdfGenerator:
    return TransferTaxReturnsPdfGenerator()


@pytest.fixture
def transfer_tax_mapper() -> TransferTaxReturnsMapper:
    return TransferTaxReturnsMapper()


@pytest.fixture
def send_transfer_tax_returns(
    db_session: Session,
    document_library,
    transfer_tax_client,
    transfer_tax_pdf_generator,
    transfer_tax_mapper,
) -> SendTransferTaxReturns:
    return SendTransferTaxReturns(
        db_session=db_session,
        document_library=document_library,
        api_client=transfer_tax_client,
        transfer_tax_pdf_generator=transfer_tax_pdf_generator,
        mapper=transfer_tax_mapper,
    )


@pytest.fixture
def non_existing_details_of_sale_id() -> int:
    return -100


@pytest.fixture
def contact_seller1(db_session, realtor1, organization1) -> Contact:
    result = Contact(
        name="Seller One",
        organization_id=organization1.id,
        email="<EMAIL>",
        assigned_to_users=[realtor1],
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
    )
    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def contact_seller2(db_session, realtor1, organization1) -> Contact:
    result = Contact(
        first_name="Seller",
        last_name="Two",
        organization_id=organization1.id,
        email="<EMAIL>",
        assigned_to_users=[realtor1],
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
    )
    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def contact_buyer1(db_session, realtor1, organization1) -> Contact:
    result = Contact(
        name="Buyer One",
        organization_id=organization1.id,
        email="<EMAIL>",
        assigned_to_users=[realtor1],
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
    )
    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def contact_buyer2(db_session, realtor1, organization1) -> Contact:
    result = Contact(
        first_name="Buyer",
        last_name="Two",
        organization_id=organization1.id,
        email="<EMAIL>",
        assigned_to_users=[realtor1],
        created_at="2023-06-02T14:34:07",
        updated_at="2023-06-02T14:58:57",
    )
    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def details_of_sale1(
    db_session, property1, contact_seller1, contact_buyer1
) -> FIDetailsOfSale:
    result = FIDetailsOfSale(
        property_id=property1.id,
        ownership_type="percentage",
        transaction_method="traditional",
        estimated_transaction_date="2025-06-01",
        sale_duration_days=180,
        offer_count=3,
        highest_rejected_offer=950000.0,
        sale_price=1000000.0,
        debt_free_price=1050000.0,
        mortgage_bank="Example Bank",
        notes="This is a test note.",
        commission_amount_total=50000.0,
        commission_percent=5.0,
        commission_vat_percent=24.0,
        commission_vat_included=True,
        commission_amount_without_vat=40000.0,
        commission_vat_amount=10000.0,
        commission_amount_with_vat=50000.0,
        sellers=[
            FISellerDetailsOfSale(
                seller_id=contact_seller1.id,
                ownership_share_percent=100.0,
            )
        ],
        buyers=[
            FIBuyerDetailsOfSale(
                buyer_id=contact_buyer1.id,
                ownership_share_percent=100.0,
            )
        ],
        recipients=[],
    )

    db_session.add(result)
    db_session.commit()
    return result


@pytest.fixture
def seller1(details_of_sale1) -> FISellerDetailsOfSale:
    return details_of_sale1.sellers[0]


@pytest.fixture
def seller2(details_of_sale1, contact_seller2) -> FISellerDetailsOfSale:
    share_percent = 100 / len(details_of_sale1.sellers)

    for seller in details_of_sale1.sellers:
        seller.ownership_share_percent = share_percent

    result = FISellerDetailsOfSale(
        seller=contact_seller2,
        ownership_share_percent=share_percent,
    )

    details_of_sale1.sellers.append(result)

    return result


@pytest.fixture
def buyer1(details_of_sale1) -> FIBuyerDetailsOfSale:
    return details_of_sale1.buyers[0]


@pytest.fixture
def buyer2(details_of_sale1, contact_buyer2) -> FIBuyerDetailsOfSale:
    share_percent = 100 / len(details_of_sale1.buyers)

    for buyer in details_of_sale1.buyers:
        buyer.ownership_share_percent = share_percent

    result = FIBuyerDetailsOfSale(
        buyer=contact_buyer2,
        ownership_share_percent=share_percent,
    )

    details_of_sale1.buyers.append(result)
    return result
