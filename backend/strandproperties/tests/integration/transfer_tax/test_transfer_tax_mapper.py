from datetime import date

import pytest

from strandproperties.schemas.transfer_tax import (
    SellerIndividualOrCorporate,
    SenderType,
    TransferType,
)
from strandproperties.services.transfer_tax.mapper import TransferTaxReturnsMapper


def test_sets_sender_type_to_real_estate_agent(mapper, details_of_sale1, buyer1):
    request = mapper.map_to_request(details_of_sale1, buyer1)
    assert request.sender == SenderType.REAL_ESTATE_AGENT


def test_sets_transfer_type_to_sale(mapper, details_of_sale1, buyer1):
    request = mapper.map_to_request(details_of_sale1, buyer1)
    assert request.transfer_type == TransferType.SALE


def test_sets_buyer_id(mapper, details_of_sale1, buyer1):
    expected_social_security_number = "010101-0101"
    buyer1.buyer.social_security_number = expected_social_security_number

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert request.buyer_id == expected_social_security_number


def test_sets_buyer_date_of_birth(mapper, details_of_sale1, buyer1):
    expected_date_of_birth = date.fromisoformat("1990-01-01")
    buyer1.buyer.date_of_birth = expected_date_of_birth.isoformat()

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert request.buyer_date_of_birth == expected_date_of_birth


def test_sets_buyer_name(mapper, details_of_sale1, buyer1):
    expected_name = "John Doe"
    buyer1.buyer.first_name = "John"
    buyer1.buyer.last_name = "Doe"

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert request.buyer_name == expected_name


def test_sets_buyer_name_from_name_when_first_and_last_name_are_not_set(
    mapper, details_of_sale1, buyer1
):
    expected_name = "John Doe"
    buyer1.buyer.name = expected_name
    buyer1.buyer.first_name = None
    buyer1.buyer.last_name = None

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert request.buyer_name == expected_name


def test_sets_signing_date(mapper, details_of_sale1, buyer1):
    expected_signing_date = "2025-01-01"
    details_of_sale1.estimated_transaction_date = expected_signing_date

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert request.signing_date == date.fromisoformat(expected_signing_date)


def test_sets_asset_details(mapper, details_of_sale1, buyer1):
    request = mapper.map_to_request(details_of_sale1, buyer1)
    assert len(request.asset_details) == 1


def test_sets_seller_details(mapper, details_of_sale1, buyer1, seller1):
    seller1.seller.social_security_number = "010101-0101"
    seller1.seller.date_of_birth = "1990-01-01"
    seller1.seller.name = "John Doe"
    seller1.ownership_share_percent = 100

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert len(request.asset_details) == 1
    assert len(request.asset_details[0].seller_details) == 1

    seller_details = request.asset_details[0].seller_details[0]
    assert (
        seller_details.seller_individual_or_corporate
        == SellerIndividualOrCorporate.INDIVIDUAL
    )
    assert seller_details.seller_id == seller1.seller.social_security_number
    assert seller_details.seller_date_of_birth == date.fromisoformat(
        seller1.seller.date_of_birth
    )
    assert seller_details.seller_name == "John Doe"
    assert (
        seller_details.seller_ownership_share_percentage
        == seller1.ownership_share_percent
    )


def test_maps_multiple_sellers(
    mapper,
    details_of_sale1,
    buyer1,
    seller1,
    seller2,
):
    seller1.seller.social_security_number = "010101-0101"
    seller1.ownership_share_percent = 75
    seller2.seller.social_security_number = "010101-0102"
    seller2.ownership_share_percent = 25

    request = mapper.map_to_request(details_of_sale1, buyer1)

    assert len(request.asset_details) == 1
    assert len(request.asset_details[0].seller_details) == 2

    seller_details1 = request.asset_details[0].seller_details[0]
    seller_details2 = request.asset_details[0].seller_details[1]

    assert seller_details1.seller_id == seller1.seller.social_security_number
    assert (
        seller_details1.seller_ownership_share_percentage
        == seller1.ownership_share_percent
    )
    assert seller_details2.seller_id == seller2.seller.social_security_number
    assert (
        seller_details2.seller_ownership_share_percentage
        == seller2.ownership_share_percent
    )


@pytest.fixture
def mapper():
    return TransferTaxReturnsMapper()
