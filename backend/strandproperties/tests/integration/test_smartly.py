import csv
from datetime import datetime, timezone
from unittest.mock import Mock, patch

import pytest

from strandproperties.models.advertisement import AdStatus, AdType, Advertisement
from strandproperties.models.user import User


@pytest.fixture
def active_advertisement(db_session, organization1, admin1, property1):
    advertisement = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        property_id=property1.id,
        type=AdType.LISTING_PROPERTY,
        title="Test Advertisement",
        description="Test description",
        primary_text="Test primary text",
        status=AdStatus.ACTIVE,
        budget_total=50000,  # 500 EUR in cents
        budget_daily=1000,  # 10 EUR in cents
        target_area_radius_km=25.0,
        start_date=datetime.now(timezone.utc),
        language="en",
        country="ES",
        municipality="Madrid",
    )
    db_session.add(advertisement)
    db_session.flush()
    return advertisement


@pytest.fixture
def expired_advertisement(db_session, organization1, admin1, property1):
    past_date = datetime(2023, 1, 1, tzinfo=timezone.utc)
    advertisement = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        property_id=property1.id,
        type=AdType.LISTING_PROPERTY,
        title="Expired Advertisement",
        description="Expired description",
        status=AdStatus.ACTIVE,
        budget_total=30000,
        start_date=past_date,
        end_date=past_date,
        language="en",
        country="ES",
        municipality="Barcelona",
    )
    db_session.add(advertisement)
    db_session.flush()
    return advertisement


def test_smartly_csv_endpoint_success(app, route_url, active_advertisement):
    response = app.get(route_url("smartly.csv"), status=200)

    assert response.content_type.startswith("text/csv"), "Response should be text/csv"
    assert "attachment; filename=smartly_export.csv" in response.headers.get(
        "Content-Disposition", ""
    ), "Content-Disposition header must contain correct filename"

    content = response.text.strip().splitlines()
    assert len(content) >= 2, "CSV should contain at least header and one data row"

    header = content[0].split(",")
    expected_headers = [
        "ad_id",
        "agent_id",
        "budget",
        "facebook_page",
        "instagram_account",
        "heading",
        "subheading",
        "description",
        "sales_argument_1",
        "sales_argument_2",
        "sales_argument_3",
        "sales_argument_4",
        "sales_argument_5",
        "date_start",
        "date_and_time_start",
        "date_end",
        "date_and_time_end",
        "ad_type",
        "agent_name",
        "agent_email",
        "agent_phone",
        "agent_office",
        "agent_photo_url",
        "agent_video_url",
        "template_id",
        "property_id",
        "landing_page_url",
        "property_image_1_square_url",
        "property_image_1_vertical_url",
        "property_image_1_landscape_url",
        "property_image_1_portrait_url",
        "property_image_2_square_url",
        "property_image_2_vertical_url",
        "property_image_2_landscape_url",
        "property_image_2_portrait_url",
        "property_image_3_square_url",
        "property_image_3_vertical_url",
        "property_image_3_landscape_url",
        "property_image_3_portrait_url",
        "property_image_4_square_url",
        "property_image_4_vertical_url",
        "property_image_4_landscape_url",
        "property_image_4_portrait_url",
        "property_image_5_square_url",
        "property_image_5_vertical_url",
        "property_image_5_landscape_url",
        "property_image_5_portrait_url",
        "language",
        "property_address",
        "property_area",
        "property_city",
        "property_country",
        "property_type",
        "property_size",
        "property_price",
        "target_postal_codes",
        "target_cities",
        "property_lat",
        "property_lng",
        "radius",
    ]

    for expected_header in expected_headers:
        assert (
            expected_header in header
        ), f"Header '{expected_header}' should be present"

    rows = list(csv.DictReader(content))
    assert len(rows) >= 1, "CSV should contain at least one data row"

    test_row = next(
        (r for r in rows if r["ad_id"] == str(active_advertisement.id)), None
    )
    assert test_row is not None, "Test advertisement should appear in CSV output"

    assert test_row["heading"] == "Test Advertisement"
    assert test_row["subheading"] == "Test description"
    assert test_row["description"] == "Test primary text"
    assert test_row["budget"] == "500.0"  # 50000 cents / 100
    assert test_row["ad_type"] == AdType.LISTING_PROPERTY.value
    assert test_row["agent_id"] == str(active_advertisement.owner_id)
    assert test_row["property_id"] == str(active_advertisement.property_id)
    assert test_row["radius"] == "25"
    assert test_row["target_cities"] == "Madrid, ES/25/kilometer"
    assert test_row["language"] == "English"


def test_smartly_csv_endpoint_with_expired_ad(
    app, route_url, expired_advertisement, db_session
):
    response = app.get(route_url("smartly.csv"), status=200)

    content = response.text.strip().splitlines()
    rows = list(csv.DictReader(content))

    expired_row = next(
        (r for r in rows if r["ad_id"] == str(expired_advertisement.id)), None
    )
    assert expired_row is None, "Expired advertisement should not appear in CSV output"

    db_session.refresh(expired_advertisement)
    assert expired_advertisement.status == AdStatus.COMPLETED


def test_smartly_csv_endpoint_empty_data(app, route_url):
    response = app.get(route_url("smartly.csv"), status=200)

    assert response.content_type.startswith("text/csv")

    content = response.text.strip().splitlines()
    assert len(content) == 1, "CSV should contain only header when no active ads"

    header = content[0].split(",")
    assert "ad_id" in header, "Header should still be present"


@patch("strandproperties.views.smartly.SmartlyStatsService")
@patch("strandproperties.config.app_cfg")
def test_smartly_update_ads_success(mock_app_cfg, mock_service_class, app, route_url):
    mock_app_cfg.smartly_ad_view_id = "test_view_id"
    mock_app_cfg.smartly_token = "test_token"
    mock_service = Mock()
    mock_service.update_advertisements.return_value = 3
    mock_service_class.return_value = mock_service

    response = app.post(route_url("smartly.update_ads"), status=200)

    assert response.json["updated"] == 3
    assert response.json["message"] == "Updated 3 advertisements"

    mock_service_class.assert_called_once()
    mock_service.update_advertisements.assert_called_once()


@patch("strandproperties.views.smartly.SmartlyStatsService")
@patch("strandproperties.config.app_cfg")
def test_smartly_update_ads_no_updates(
    mock_app_cfg, mock_service_class, app, route_url
):
    mock_app_cfg.smartly_ad_view_id = "test_view_id"
    mock_app_cfg.smartly_token = "test_token"
    mock_service = Mock()
    mock_service.update_advertisements.return_value = 0
    mock_service_class.return_value = mock_service

    response = app.post(route_url("smartly.update_ads"), status=200)

    assert response.json["updated"] == 0
    assert response.json["message"] == "Updated 0 advertisements"


@patch("strandproperties.views.smartly.SmartlyStatsService")
@patch("strandproperties.config.app_cfg")
def test_smartly_update_ads_service_error(
    mock_app_cfg, mock_service_class, app, route_url
):
    mock_app_cfg.smartly_ad_view_id = "test_view_id"
    mock_app_cfg.smartly_token = "test_token"
    mock_service = Mock()
    mock_service.update_advertisements.side_effect = Exception("API Error")
    mock_service_class.return_value = mock_service

    response = app.post(route_url("smartly.update_ads"), status=500)

    assert "An error occurred while updating advertisements" in response.json["error"]


@patch("strandproperties.views.smartly.SmartlyStatsService")
@patch("strandproperties.config.app_cfg")
def test_smartly_update_ads_config_error(
    mock_app_cfg, mock_service_class, app, route_url
):
    mock_app_cfg.smartly_ad_view_id = "test_view_id"
    mock_app_cfg.smartly_token = None
    mock_service_class.side_effect = ValueError("Smartly token must be provided")

    response = app.post(route_url("smartly.update_ads"), status=500)

    assert "An error occurred while updating advertisements" in response.json["error"]


def test_smartly_csv_with_fi_property(
    app, route_url, db_session, organization1, admin1, fi_property1
):
    advertisement = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        fi_property_id=fi_property1.id,
        type=AdType.LISTING_PROPERTY,
        title="Finnish Property Ad",
        description="Finnish property description",
        status=AdStatus.ACTIVE,
        budget_total=75000,
        language="fi",
        country="FI",
        municipality="Helsinki",
    )
    db_session.add(advertisement)
    db_session.flush()

    response = app.get(route_url("smartly.csv"), status=200)

    content = response.text.strip().splitlines()
    rows = list(csv.DictReader(content))

    fi_row = next((r for r in rows if r["ad_id"] == str(advertisement.id)), None)
    assert fi_row is not None, "FI property advertisement should appear in CSV"

    assert fi_row["heading"] == "Finnish Property Ad"
    assert fi_row["budget"] == "750.0"  # 75000 cents / 100
    assert fi_row["language"] == "Finnish"


def test_smartly_csv_with_advertisement_images(
    app, route_url, active_advertisement, db_session
):
    from strandproperties.models.advertisement import AdvertisementImage

    image1 = AdvertisementImage(
        advertisement_id=active_advertisement.id,
        url="https://example.com/image1.jpg",
        order=1,
    )
    image2 = AdvertisementImage(
        advertisement_id=active_advertisement.id,
        url="https://example.com/image2.jpg",
        order=2,
    )
    db_session.add_all([image1, image2])
    db_session.flush()

    response = app.get(route_url("smartly.csv"), status=200)

    content = response.text.strip().splitlines()
    rows = list(csv.DictReader(content))

    test_row = next(
        (r for r in rows if r["ad_id"] == str(active_advertisement.id)), None
    )
    assert test_row is not None

    assert test_row["property_image_1_square_url"] == "https://example.com/image1.jpg"
    assert test_row["property_image_1_vertical_url"] == "https://example.com/image1.jpg"
    assert test_row["property_image_2_square_url"] == "https://example.com/image2.jpg"
    assert test_row["property_image_2_vertical_url"] == "https://example.com/image2.jpg"


@patch("strandproperties.services.smartly.SmartlyExportService.generate_csv")
def test_smartly_csv_database_error(mock_generate_csv, app, route_url):
    mock_generate_csv.side_effect = Exception("Database connection failed")

    response = app.get(route_url("smartly.csv"), status=500)

    assert "An error occurred while generating the CSV" in response.json["error"]


def test_smartly_csv_spanish_language_and_multiple_images(
    app, route_url, db_session, organization1, admin1, property1
):
    """Test Spanish language mapping and multiple image handling"""
    from strandproperties.models.advertisement import AdvertisementImage

    advertisement = Advertisement(
        owner_id=admin1.id,
        organization_id=organization1.id,
        property_id=property1.id,
        type=AdType.LISTING_PROPERTY,
        title="Spanish Property Ad",
        description="Spanish property description",
        status=AdStatus.ACTIVE,
        budget_total=60000,
        target_area_radius_km=20.0,
        start_date=datetime.now(timezone.utc),
        language="es",  # Spanish
        country="ES",
        municipality="Barcelona",
    )
    db_session.add(advertisement)
    db_session.flush()

    # Add multiple images to test 5-image functionality
    for i in range(3):  # Add 3 images
        image = AdvertisementImage(
            advertisement_id=advertisement.id,
            url=f"https://example.com/image_{i+1}.jpg",
            order=i + 1,
        )
        db_session.add(image)
    db_session.flush()

    response = app.get(route_url("smartly.csv"), status=200)

    content = response.text.strip().splitlines()
    rows = list(csv.DictReader(content))

    test_row = next((r for r in rows if r["ad_id"] == str(advertisement.id)), None)
    assert test_row is not None, "Spanish advertisement should appear in CSV"

    # Verify Spanish language mapping
    assert test_row["language"] == "Spanish"

    # Verify target cities uses Spanish location
    assert test_row["target_cities"] == "Barcelona, ES/20/kilometer"

    # Verify multiple images are included
    assert test_row["property_image_1_square_url"] == "https://example.com/image_1.jpg"
    assert test_row["property_image_2_square_url"] == "https://example.com/image_2.jpg"
    assert test_row["property_image_3_square_url"] == "https://example.com/image_3.jpg"
    assert test_row["property_image_4_square_url"] == ""  # No 4th image
    assert test_row["property_image_5_square_url"] == ""  # No 5th image
