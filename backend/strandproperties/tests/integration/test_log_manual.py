def test_create_activity(route_url, api, lead1):
    payload = {
        "description": {"description": "Discussed about an opportunity to sell 2"},
        "objectId": lead1.id,
        "objectType": "SALES_ACTIVITY",
    }
    response = api("post", route_url("activity.create"), payload, status=200)

    assert response["objectType"] == "SALES_ACTIVITY"
    assert response["objectId"] == lead1.id
    assert (
        response["description"]["description"]
        == "Discussed about an opportunity to sell 2"
    )


def test_read_activity(route_url, api, activity_1):
    response = api("get", route_url("activity.read_edit", id=activity_1.id), status=200)
    assert response["id"] == activity_1.id
    assert response["description"] == activity_1.description


def test_edit_activity(route_url, api, activity_1):
    payload = {
        "description": {"description": "Discussed about an opportunity to sell 3"},
        "objectId": activity_1.object_id,
        "objectType": activity_1.object_type,
    }
    response = api(
        "patch",
        route_url("activity.read_edit", id=activity_1.id),
        payload,
        status=200,
    )

    assert response["objectType"] == activity_1.object_type
    assert response["objectId"] == activity_1.object_id
    assert (
        response["description"]["description"]
        == "Discussed about an opportunity to sell 3"
    )
