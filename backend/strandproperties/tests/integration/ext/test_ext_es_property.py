def test_list_properties(route_url, api, properties):
    # Missing X-Api-Key
    r = api("get", route_url("ext.es_property.list"), status=401)

    # Invalid X-Api-Key
    r = api(
        "get",
        route_url("ext.es_property.list"),
        status=401,
        headers={"X-Api-Key": "potato"},
    )

    # Valid X-Api-Key & Agent
    r = api(
        "get",
        route_url("ext.es_property.list"),
        status=200,
        headers={"X-Api-Key": "public_property_api_key"},
        params={"page_size": 100, "agent_id": 41}, # TODO: remove agent_id filter when no longer needed
    )
