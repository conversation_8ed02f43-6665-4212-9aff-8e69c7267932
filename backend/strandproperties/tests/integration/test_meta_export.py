import csv

import pytest


def test_meta_export_endpoint_default_tsv(
    app, route_url, property1_strandwebsite_integration
):
    """Test that the endpoint returns TSV by default for backward compatibility"""
    response = app.get(route_url("meta.export"), status=200)

    assert response.content_type.startswith("text/csv"), "Response should be text/csv"
    assert "attachment; filename=meta_export.tsv" in response.headers.get(
        "Content-Disposition", ""
    ), "Content-Disposition header must contain correct filename with .tsv extension"

    content = response.text.strip().splitlines()
    header = content[0].split("\t")
    assert (
        "home_listing_id" in header
    ), "CSV header row must include home_listing_id field"

    assert len(content) >= 2, "CSV should contain at least one data row"

    rows = list(csv.DictReader(content, delimiter="\t"))
    matching_rows = [
        r
        for r in rows
        if r["home_listing_id"] == property1_strandwebsite_integration.reference
    ]
    assert (
        len(matching_rows) == 1
    ), "Test property must appear exactly once in CSV output"

    row = matching_rows[0]
    assert (
        row["name"] == property1_strandwebsite_integration.reference
    ), "Name column should match property reference"
    assert row["listing_type"].lower() == "sale", "Listing type should be 'sale'"
    assert row["address.country"] == "Spain", "Country column should be 'Spain'"


def test_meta_export_endpoint_with_csv_format(
    app, route_url, property1_strandwebsite_integration
):
    """Test that the endpoint returns comma-separated CSV when format=csv is specified"""
    response = app.get(route_url("meta.export") + "?format=csv", status=200)

    assert response.content_type.startswith("text/csv"), "Response should be text/csv"
    assert "attachment; filename=meta_export.csv" in response.headers.get(
        "Content-Disposition", ""
    ), "Content-Disposition header must contain correct filename with .csv extension"

    content = response.text.strip().splitlines()
    # For comma-separated, we need to handle potential commas in values
    reader = csv.DictReader(content)
    headers = reader.fieldnames
    assert (
        "home_listing_id" in headers
    ), "CSV header row must include home_listing_id field"

    assert len(content) >= 2, "CSV should contain at least one data row"

    rows = list(reader)
    matching_rows = [
        r
        for r in rows
        if r["home_listing_id"] == property1_strandwebsite_integration.reference
    ]
    assert (
        len(matching_rows) == 1
    ), "Test property must appear exactly once in CSV output"

    row = matching_rows[0]
    assert (
        row["name"] == property1_strandwebsite_integration.reference
    ), "Name column should match property reference"
    assert row["listing_type"].lower() == "sale", "Listing type should be 'sale'"
    assert row["address.country"] == "Spain", "Country column should be 'Spain'"


def test_meta_export_endpoint_with_tsv_format(
    app, route_url, property1_strandwebsite_integration
):
    """Test that the endpoint returns TSV when format=tsv is explicitly specified"""
    response = app.get(route_url("meta.export") + "?format=tsv", status=200)

    assert response.content_type.startswith("text/csv"), "Response should be text/csv"
    assert "attachment; filename=meta_export.tsv" in response.headers.get(
        "Content-Disposition", ""
    ), "Content-Disposition header must contain correct filename with .tsv extension"

    content = response.text.strip().splitlines()
    header = content[0].split("\t")
    assert (
        "home_listing_id" in header
    ), "CSV header row must include home_listing_id field"

    assert len(content) >= 2, "CSV should contain at least one data row"

    rows = list(csv.DictReader(content, delimiter="\t"))
    matching_rows = [
        r
        for r in rows
        if r["home_listing_id"] == property1_strandwebsite_integration.reference
    ]
    assert (
        len(matching_rows) == 1
    ), "Test property must appear exactly once in CSV output"

    row = matching_rows[0]
    assert (
        row["name"] == property1_strandwebsite_integration.reference
    ), "Name column should match property reference"
    assert row["listing_type"].lower() == "sale", "Listing type should be 'sale'"
    assert row["address.country"] == "Spain", "Country column should be 'Spain'"
