import io
import zipfile
from unittest.mock import patch

from strandproperties.models.document_library import OwnerType
from strandproperties.services.document_library.service import DocumentLibrary


def test_download_items_returns_zip(
    api_without_json,
    owner_route_url,
    ready_document_library_item,
    pending_document_library_item,
    fi_property1,
    mock_get_s3_file_content,
):
    """When more than one ID is provided the endpoint should return a ZIP archive."""

    response = api_without_json(
        "post",
        owner_route_url(
            "document_library.download_items",
            owner_type=OwnerType.FI_PROPERTY,
            owner_id=fi_property1.id,
        ),
        [ready_document_library_item.id, pending_document_library_item.id],
        status=200,
    )

    assert response.content_type == "application/zip"
    assert response.headers["Content-Disposition"].startswith(
        'attachment; filename="strandproperties-'
    )

    assert len(response.body) > 0


def test_download_items_returns_file_content_for_single_item(
    api_without_json,
    owner_route_url,
    ready_document_library_item,
    fi_property1,
    mock_get_s3_file_content,
):
    """When exactly one ID is provided the endpoint should return the file content directly."""

    response = api_without_json(
        "post",
        owner_route_url(
            "document_library.download_items",
            owner_type=OwnerType.FI_PROPERTY,
            owner_id=fi_property1.id,
        ),
        [ready_document_library_item.id],
        status=200,
    )

    assert response.status_code == 200
    assert response.content_type == ready_document_library_item.mime_type
    assert response.headers["Content-Disposition"].startswith(
        f'attachment; filename="{ready_document_library_item.filename}"'
    )
    assert len(response.body) > 0


def test_download_items_404_for_non_existing(
    api_without_json, owner_route_url, non_existing_item_id, fi_property1
):
    api_without_json(
        "post",
        owner_route_url(
            "document_library.download_items",
            owner_type=OwnerType.FI_PROPERTY,
            owner_id=fi_property1.id,
        ),
        [non_existing_item_id],
        status=404,
    )
