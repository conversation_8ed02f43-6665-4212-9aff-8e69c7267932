import pytest
import stripe
from stripe.error import CardError

from strandproperties.models.transaction import (
    ExpenseTransaction,
    ExpenseTransactionTarget,
)


@pytest.fixture(scope="function", autouse=True)
def ensure_stripe_api_key(monkeypatch):
    monkeypatch.setattr(stripe, "api_key", "sk_test")


def test_create_expense_account_creates_new_customer_and_setup_intent(
    route_url, api, realtor1, db_session, mocker
):
    realtor1.stripe_customer_id = None
    db_session.flush()

    mocker.patch.object(
        stripe.Customer, "create", return_value=mocker.Mock(id="cus_123")
    )
    mocker.patch.object(
        stripe.SetupIntent, "create", return_value=mocker.Mock(client_secret="sec_456")
    )

    resp = api(
        "post",
        route_url("expense.create_expense_account"),
        status=200,
        role=realtor1._roles[0],
    )
    assert resp["status"] == "ok"
    assert resp["clientSecret"] == "sec_456"

    db_session.refresh(realtor1)
    assert realtor1.stripe_customer_id == "cus_123"


def test_create_expense_account_reuses_existing_customer(
    route_url, api, realtor1, db_session, mocker
):
    realtor1.stripe_customer_id = "cus_existing"
    db_session.flush()

    mocker.patch.object(
        stripe.Customer, "retrieve", return_value=mocker.Mock(id="cus_existing")
    )
    mocker.patch.object(
        stripe.SetupIntent, "create", return_value=mocker.Mock(client_secret="sec_789")
    )

    resp = api(
        "post",
        route_url("expense.create_expense_account"),
        status=200,
        role=realtor1._roles[0],
    )
    assert resp["status"] == "ok"
    assert resp["clientSecret"] == "sec_789"


# def test_create_expense_transaction_happy_path(
#     route_url, api, admin1, organization1, db_session, mocker
# ):
#     admin1.stripe_customer_id = "cus_A"
# organization1.stripe_account_id = "acct_B"
# db_session.flush()

# pm_list = mocker.Mock(data=[mocker.Mock(id="pm_1")])
# mocker.patch.object(stripe.PaymentMethod, "list", return_value=pm_list)

# charge = mocker.Mock(
#     status="succeeded",
#     receipt_url="https://example.com/receipt",
#     payment_method_details={"card": {"last4": "4242"}},
# )
# intent = mocker.Mock(
#     status="succeeded", id="pi_123", charges=mocker.Mock(data=[charge])
# )
# mocker.patch.object(stripe.PaymentIntent, "create", return_value=intent)

# payload = {
#     "user_id": admin1.id,
#     "amount": 1500,
#     "type": "payout",
#     "status": "processing",
#     "description": "Smartly refund",
#     "meta": {"payment_method_id": "pm_test_123"},
# }

# r = api(
#     "post",
#     route_url("expense.create_list"),
#     payload,
#     status=200,
#     role=admin1._roles[0],
# )

# assert r["amount"] == 1500
# assert r["description"] == "Smartly refund"
# assert r["stripeTransactionId"] == "pi_123"
# assert r["status"] == "succeeded"


# def test_create_expense_transaction_card_error_bubbles_up(
#     route_url, api, admin1, organization1, db_session, mocker
# ):
#     admin1.stripe_customer_id = "cus_A"
#     organization1.stripe_account_id = "acct_B"
#     db_session.flush()

#     mocker.patch.object(stripe.PaymentMethod, "list", return_value=mocker.Mock(data=[]))
#     payload = {
#         "user_id": admin1.id,
#         "amount": 100,
#         "type": "payout",
#         "status": "processing",
#         "description": "Ohnee problemsssss",
#     }

#     e1 = api(
#         "post",
#         route_url("expense.create_list"),
#         payload,
#         status=400,
#         role=admin1._roles[0],
#     )
#     assert "No payment method provided" in e1["error"]

#     pm_one = mocker.Mock(data=[mocker.Mock(id="pm_fail")])
#     mocker.patch.object(stripe.PaymentMethod, "list", return_value=pm_one)
#     mocker.patch.object(
#         stripe.PaymentIntent,
#         "create",
#         side_effect=CardError("Declined", param=None, code=None, http_body=None),
#     )
#     e2 = api(
#         "post",
#         route_url("expense.create_list"),
#         payload,
#         status=400,
#         role=admin1._roles[0],
#     )
#     assert "Stripe error" in e2["error"]


def test_get_card_details_and_disconnect_card(
    route_url, api, admin1, db_session, mocker
):
    admin1.stripe_customer_id = None
    db_session.flush()
    err = api(
        "get",
        route_url("expense.card_details"),
        status=400,
        role=admin1._roles[0],
    )
    assert "Stripe connection" in err["error"]

    admin1.stripe_customer_id = "cus_X"
    db_session.flush()
    pm = mocker.Mock(data=[mocker.Mock(card=mocker.Mock(last4="1111"), id="pmX")])
    mocker.patch.object(stripe.PaymentMethod, "list", return_value=pm)
    last4 = api(
        "get",
        route_url("expense.card_details"),
        status=200,
        role=admin1._roles[0],
    )
    assert last4 == "1111"

    mocker.patch.object(stripe.PaymentMethod, "list", return_value=pm)
    mocker.patch.object(
        stripe.PaymentMethod, "detach", return_value=mocker.Mock(id="pmX")
    )
    resp = api(
        "post",
        route_url("expense.disconnect_card"),
        status=200,
        role=admin1._roles[0],
    )
    assert resp["status"] == "ok"
    assert "pmX" in resp["message"]

    mocker.patch.object(stripe.PaymentMethod, "list", return_value=mocker.Mock(data=[]))
    err2 = api(
        "post",
        route_url("expense.disconnect_card"),
        status=404,
        role=admin1._roles[0],
    )
    assert "No card" in err2["error"]


def test_create_expense_transaction_creates_and_sends_invoice(
    route_url, api, admin1, organization1, db_session, mocker
):
    """Test that creating an expense transaction creates and sends a Stripe invoice"""
    admin1.stripe_customer_id = "cus_A"
    admin1.email = "<EMAIL>"
    organization1.stripe_account_id = "acct_B"
    db_session.flush()

    mocker.patch.object(type(admin1), "balance", return_value=2000)

    mock_invoice_item = mocker.patch.object(stripe.InvoiceItem, "create")
    mock_invoice = mocker.patch.object(
        stripe.Invoice, "create", return_value=mocker.Mock(id="in_456")
    )
    mock_finalize = mocker.patch.object(
        stripe.Invoice, "finalize_invoice", return_value=mocker.Mock(id="in_456")
    )
    mock_send = mocker.patch.object(stripe.Invoice, "send_invoice")

    payload = {
        "user_id": admin1.id,
        "amount": 1500,
        "type": "expense",
        "status": "succeeded",
        "description": "Test expense",
    }

    r = api(
        "post",
        route_url("expense.create_list"),
        payload,
        status=200,
        role=admin1._roles[0],
    )

    assert r["amount"] == 1500
    assert r["description"] == "Test expense"
    assert r["status"] == "succeeded"
    assert "stripe_invoice_id" in r["meta"]
    assert r["meta"]["stripe_invoice_id"] == "in_456"

    mock_invoice_item.assert_called_once_with(
        customer="cus_A",
        amount=1500,
        currency="eur",
        description="Test expense",
    )
    mock_invoice.assert_called_once()
    mock_finalize.assert_called_once_with("in_456")
    mock_send.assert_called_once_with("in_456")


def test_create_expense_transaction_with_deposit_creates_invoice(
    route_url, api, admin1, organization1, db_session, mocker
):
    admin1.stripe_customer_id = "cus_A"
    admin1.email = "<EMAIL>"
    organization1.stripe_account_id = "acct_B"
    db_session.flush()

    mocker.patch.object(type(admin1), "balance", return_value=50)

    pm_list = mocker.Mock(data=[mocker.Mock(id="pm_1")])
    mocker.patch.object(stripe.PaymentMethod, "list", return_value=pm_list)

    charge = mocker.Mock(
        status="succeeded",
        receipt_url="https://example.com/receipt",
        payment_method_details={"card": {"last4": "4242"}},
    )
    intent = mocker.Mock(
        status="succeeded", id="pi_123", charges=mocker.Mock(data=[charge])
    )
    mocker.patch.object(stripe.PaymentIntent, "create", return_value=intent)

    mock_invoice_item = mocker.patch.object(stripe.InvoiceItem, "create")
    mock_invoice = mocker.patch.object(
        stripe.Invoice, "create", return_value=mocker.Mock(id="in_456")
    )
    mock_finalize = mocker.patch.object(
        stripe.Invoice, "finalize_invoice", return_value=mocker.Mock(id="in_456")
    )
    mock_send = mocker.patch.object(stripe.Invoice, "send_invoice")

    payload = {
        "user_id": admin1.id,
        "amount": 1500,
        "type": "expense",
        "status": "processing",
        "description": "Test expense with deposit",
    }

    r = api(
        "post",
        route_url("expense.create_list"),
        payload,
        status=200,
        role=admin1._roles[0],
    )

    assert "deposit_transaction" in r
    assert "expense_transaction" in r

    expense_transaction = r["expense_transaction"]
    assert expense_transaction["amount"] == 1500
    assert expense_transaction["description"] == "Test expense with deposit"
    assert "stripe_invoice_id" in expense_transaction["meta"]
    assert expense_transaction["meta"]["stripe_invoice_id"] == "in_456"

    mock_invoice_item.assert_called_once_with(
        customer="cus_A",
        amount=1500,
        currency="eur",
        description="Test expense with deposit",
    )
    mock_invoice.assert_called_once()
    mock_finalize.assert_called_once_with("in_456")
    mock_send.assert_called_once_with("in_456")


def test_create_expense_transaction_invoice_failure_does_not_break_transaction(
    route_url, api, admin1, organization1, db_session, mocker
):
    admin1.stripe_customer_id = "cus_A"
    admin1.email = "<EMAIL>"
    organization1.stripe_account_id = "acct_B"
    db_session.flush()

    mocker.patch.object(type(admin1), "balance", return_value=2000)

    mocker.patch.object(
        stripe.InvoiceItem,
        "create",
        side_effect=stripe.error.StripeError("Invoice creation failed"),
    )

    payload = {
        "user_id": admin1.id,
        "amount": 1500,
        "type": "expense",
        "status": "succeeded",
        "description": "Test expense",
    }

    r = api(
        "post",
        route_url("expense.create_list"),
        payload,
        status=200,
        role=admin1._roles[0],
    )

    assert r["amount"] == 1500
    assert r["description"] == "Test expense"
    assert r["status"] == "succeeded"
    assert r["meta"] == {} or "stripe_invoice_id" not in r["meta"]


def test_create_expense_transaction_no_email_skips_invoice(
    route_url, api, admin1, organization1, db_session, mocker
):
    admin1.stripe_customer_id = "cus_A"
    admin1.email = ""  # Set to empty string instead of None due to database constraint
    organization1.stripe_account_id = "acct_B"
    db_session.flush()

    mocker.patch.object(type(admin1), "balance", return_value=2000)

    mock_invoice_item = mocker.patch.object(stripe.InvoiceItem, "create")

    payload = {
        "user_id": admin1.id,
        "amount": 1500,
        "type": "expense",
        "status": "succeeded",
        "description": "Test expense",
    }

    r = api(
        "post",
        route_url("expense.create_list"),
        payload,
        status=200,
        role=admin1._roles[0],
    )

    assert r["amount"] == 1500
    assert r["description"] == "Test expense"
    assert r["status"] == "succeeded"
    assert r["meta"] == {} or "stripe_invoice_id" not in r["meta"]

    mock_invoice_item.assert_not_called()


def test_list_expense_transactions_applies_filters(
    route_url, api, realtor1, admin1, db_session, mocker
):
    # Mock the _update_transaction_with_stripe method to avoid Stripe API calls and serialization issues
    def mock_update_transaction(transaction):
        return transaction

    mocker.patch(
        "strandproperties.views.expense.Expenses._update_transaction_with_stripe",
        side_effect=mock_update_transaction,
    )

    from strandproperties.schemas.base import PageMetadata

    mocker.patch(
        "strandproperties.views.expense.build_page_metadata",
        return_value=PageMetadata(page=1, page_size=10, page_count=1, total_count=2),
    )

    tx1 = ExpenseTransaction(
        user_id=realtor1.id,
        amount=100,
        type="expense",
        description="foo",
        status="succeeded",
        stripe_transaction_id="x1",
        meta={"intent_status": "succeeded"},
    )
    tx2 = ExpenseTransaction(
        user_id=admin1.id,
        amount=200,
        type="expense",
        description="bar",
        status="failed",
        stripe_transaction_id="x2",
        meta={"intent_status": "failed"},
    )
    db_session.add_all([tx1, tx2])
    db_session.flush()

    both = api(
        "get",
        route_url("expense.create_list"),
        status=200,
        role=admin1._roles[0],
    )
    assert both["metadata"]["totalCount"] == 2
    assert len(both["records"]) == 2

    failed = api(
        "get",
        route_url("expense.create_list"),
        params={"statuses": ["failed"]},
        status=200,
        role=admin1._roles[0],
    )
    assert len(failed["records"]) == 1
    assert failed["records"][0]["status"] == "failed"

    me = api(
        "get",
        route_url("expense.create_list"),
        params={"user_id": realtor1.id},
        status=200,
        role=admin1._roles[0],
    )
    assert len(me["records"]) == 1
    assert me["records"][0]["userId"] == realtor1.id


def test_list_expense_transactions_includes_target_reference(
    route_url, api, admin1, db_session, property1, mocker
):
    # Mock the _update_transaction_with_stripe method to avoid Stripe API calls and serialization issues
    def mock_update_transaction(transaction):
        return transaction

    mocker.patch(
        "strandproperties.views.expense.Expenses._update_transaction_with_stripe",
        side_effect=mock_update_transaction,
    )

    # Mock the page metadata function to prevent query issues
    from strandproperties.schemas.base import PageMetadata

    mocker.patch(
        "strandproperties.views.expense.build_page_metadata",
        return_value=PageMetadata(page=1, page_size=10, page_count=1, total_count=1),
    )

    tx = ExpenseTransaction(
        user_id=admin1.id,
        amount=100,
        type="expense",
        description="Test with target",
        status="succeeded",
        stripe_transaction_id="test",
        meta={},
    )
    db_session.add(tx)
    db_session.flush()

    target = ExpenseTransactionTarget(
        expense_transaction_id=tx.id,
        target_type="property",
        target_id=property1.id,
        reference=property1.reference,
    )
    db_session.add(target)
    db_session.flush()

    response = api(
        "get",
        route_url("expense.create_list"),
        status=200,
        role=admin1._roles[0],
    )

    assert len(response["records"]) == 1
    record = response["records"][0]
    assert len(record["targets"]) == 1

    target_data = record["targets"][0]
    assert target_data["targetType"] == "property"
    assert target_data["targetId"] == property1.id
    assert target_data["reference"] == property1.reference
