from datetime import datetime

import pytz

from strandproperties.libs.utils import (
    parse_datetime_or_date_to_utc,
    parse_fi_datetime_or_date_to_signing_dl_utc,
)


def test_parsing_fi_signing_dl_date():
    # date strings should assume time to be 23:59:59 in Europe/Helsinki
    # and be converted to utc
    date_str = "2025-04-04"
    parsed_date = parse_fi_datetime_or_date_to_signing_dl_utc(date_str)

    helsinki_tz = pytz.timezone("Europe/Helsinki")
    expected_date = helsinki_tz.localize(
        datetime(
            year=2025,
            month=4,
            day=4,
            hour=23,
            minute=59,
            second=59,
        )
    ).astimezone(pytz.UTC)

    assert parsed_date == expected_date


def test_parsing_fi_signing_dl_datetime():
    # naive datetimes are localized to the default_timezone (Europe/Helsinki)
    # and converted to utc
    date_str = "2025-04-04 12:00:00"
    parsed_datetime = parse_fi_datetime_or_date_to_signing_dl_utc(date_str)

    helsinki_tz = pytz.timezone("Europe/Helsinki")
    expected_datetime = helsinki_tz.localize(
        datetime(
            year=2025,
            month=4,
            day=4,
            hour=12,
            minute=0,
            second=0,
        )
    ).astimezone(pytz.UTC)

    assert parsed_datetime == expected_datetime


def test_parsing_iso_datetime_utc():
    # iso datetimes in utc are unchanged
    input_datetime = "2025-04-04T12:00:00Z"
    parsed_datetime = parse_datetime_or_date_to_utc(input_datetime)
    expected_datetime = datetime(
        year=2025,
        month=4,
        day=4,
        hour=12,
        minute=0,
        second=0,
        tzinfo=pytz.UTC,
    )

    assert parsed_datetime == expected_datetime


def test_parsing_iso_datetime_fi():
    # iso datetimes are converted to utc
    input_datetime = "2025-04-04T12:00:00+03:00"
    parsed_datetime = parse_datetime_or_date_to_utc(input_datetime)
    expected_datetime = datetime(
        year=2025,
        month=4,
        day=4,
        hour=9,
        minute=0,
        second=0,
        tzinfo=pytz.UTC,
    )

    assert parsed_datetime == expected_datetime


def test_parsing_datetime_ams():
    input_datetime = "2025-04-04 12:00:00"
    parsed_datetime = parse_datetime_or_date_to_utc(
        input_datetime, default_timezone="Europe/Amsterdam"
    )
    expected_datetime = datetime(
        year=2025,
        month=4,
        day=4,
        hour=10,
        minute=0,
        second=0,
        tzinfo=pytz.UTC,
    )

    assert parsed_datetime == expected_datetime
