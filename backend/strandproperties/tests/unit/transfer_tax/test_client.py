from datetime import date, datetime, timezone
from typing import Optional
from unittest.mock import MagicMock, patch

import pytest
import requests

from strandproperties.schemas.transfer_tax import (
    AdditionalSellingPrice,
    AssetDetails,
    AssetType,
    SellerDetails,
    SellerIndividualOrCorporate,
    SenderType,
    SendReturnRequest,
    SendReturnResponse,
    SendReturnResponse200,
    SendReturnResponse400,
    SendReturnResponse500,
    TransferType,
)
from strandproperties.services.transfer_tax.transfer_tax_client import TransferTaxClient


class TestTransferTaxClient:
    def test_software_key_is_required(self):
        with pytest.raises(ValueError):
            TransferTaxClient(
                software_key="", base_url="http://example.com", user_agent="agent"
            )

    def test_headers_include_software_key(self):
        expected_software_key = "software_key"
        client = TransferTaxClient(
            software_key=expected_software_key,
            base_url="http://example.com",
            user_agent="agent",
        )
        _, _, _, headers = self._send_return(client)

        assert headers["Vero-SoftwareKey"] == expected_software_key

    def test_headers_include_software_id_when_provided(self):
        expected_software_id = "software_id"
        client = TransferTaxClient(
            software_key="software_key",
            software_id=expected_software_id,
            base_url="http://example.com",
            user_agent="agent",
        )
        _, _, _, headers = self._send_return(client)

        assert headers["Vero-SoftwareId"] == expected_software_id

    def test_headers_include_subscription_key_when_provided(self):
        expected_subscription_key = "subscription_key"
        client = TransferTaxClient(
            software_key="software_key",
            subscription_key=expected_subscription_key,
            base_url="http://example.com",
            user_agent="agent",
        )
        _, _, _, headers = self._send_return(client)

        assert headers["Ocp-Apim-Subscription-Key"] == expected_subscription_key

    def test_base_url_is_required(self):
        with pytest.raises(ValueError):
            TransferTaxClient(software_key="123", base_url="", user_agent="agent")

    def test_sends_request_to_correct_url(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        _, url, _, _ = self._send_return(client)
        assert url == "http://example.com/SendReturn/v1"

    def test_base_url_is_stripped_of_trailing_slash(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com/",
            user_agent="agent",
        )
        _, url, _, _ = self._send_return(client)
        assert url == "http://example.com/SendReturn/v1"

    def test_user_agent_is_required(self):
        with pytest.raises(ValueError):
            TransferTaxClient(
                software_key="123", base_url="http://example.com", user_agent=""
            )

    def test_headers_include_user_agent(self):
        user_agent = "TestAgent/1.0"
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent=user_agent,
        )
        _, _, _, headers = self._send_return(client)

        assert headers["User-Agent"] == user_agent

    def test_headers_include_required_content_type_and_accept(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        _, _, _, headers = self._send_return(client)

        assert headers["Content-Type"] == "application/json"
        assert headers["Accept"] == "*/*"

    def test_dates_are_converted_to_iso_format(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        request = self._create_test_request()

        request.signing_date = date(2024, 1, 15)
        request.buyer_date_of_birth = date(1990, 5, 20)
        request.asset_details[0].seller_details[0].seller_date_of_birth = date(
            1980, 1, 1
        )
        request.asset_details[0].ownership_transfer_date = date(2024, 2, 1)
        request.asset_details[0].additional_selling_price = [
            AdditionalSellingPrice.create(
                add_selling_price_when_clarified=True,
                add_selling_price_clarified_date=date(2024, 3, 1),
                buyer_share_add_selling_price=100000.00,
            )
        ]

        _, _, body, _ = self._send_return(client, request=request)

        assert body["SigningDate"] == request.signing_date.isoformat()
        assert body["BuyerDateOfBirth"] == request.buyer_date_of_birth.isoformat()
        assert (
            body["AssetDetails"][0]["SellerDetails"][0]["SellerDateOfBirth"]
            == request.asset_details[0]
            .seller_details[0]
            .seller_date_of_birth.isoformat()
        )
        assert (
            body["AssetDetails"][0]["OwnershipTransferDate"]
            == request.asset_details[0].ownership_transfer_date.isoformat()
        )
        assert (
            body["AssetDetails"][0]["AdditionalSellingPrice"][0][
                "AddSellingPriceClarifiedDate"
            ]
            == request.asset_details[0]
            .additional_selling_price[0]
            .add_selling_price_clarified_date.isoformat()
        )

    def test_enums_are_converted_to_integer_values_for_api_compatibility(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        request = self._create_test_request()
        request.sender = SenderType.REAL_ESTATE_AGENT
        request.transfer_type = TransferType.SALE
        request.asset_details[0].asset = AssetType.RESIDENTIAL_PROPERTY
        request.asset_details[0].seller_details[
            0
        ].seller_individual_or_corporate = SellerIndividualOrCorporate.INDIVIDUAL

        _, _, body, _ = self._send_return(client, request=request)

        assert body["Sender"] == request.sender
        assert body["TransferType"] == request.transfer_type
        assert body["AssetDetails"][0]["Asset"] == request.asset_details[0].asset
        assert (
            body["AssetDetails"][0]["SellerDetails"][0]["SellerIndividualOrCorporate"]
            == request.asset_details[0].seller_details[0].seller_individual_or_corporate
        )

    def test_successful_transfer_tax_submission_returns_accepted_response(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        request = self._create_test_request()
        expected_response = {
            "UniqueIdentifier": "TEST123456",
            "AcceptedTimestamp": "2024-01-15T10:30:00.000Z",
        }

        response, _, _, _ = self._send_return(
            client, request=request, response=expected_response
        )

        assert isinstance(response, SendReturnResponse200)
        assert response.unique_identifier == expected_response["UniqueIdentifier"]
        assert response.accepted_timestamp == datetime(
            2024, 1, 15, 10, 30, 0, 0, tzinfo=timezone.utc
        )

    def test_validation_errors_return_detailed_error_response(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        expected_response = {
            "ErrorCode": "1005",
            "ErrorText": "Validation error",
            "ErrorDetails": [
                {
                    "ErrorCode": "1101",
                    "ErrorReference": "BuyerID",
                    "ErrorDescriptionFI": "Virheellinen henkilö- tai Y-tunnus.",
                    "ErrorDescriptionSE": "Fel i personbeteckningen eller FO-numret",
                    "ErrorDescriptionEN": "Invalid Personal or Business Id.",
                }
            ],
        }

        response, _, _, _ = self._send_return(
            client, status_code=400, response=expected_response
        )

        assert isinstance(response, SendReturnResponse400)
        assert response.error_code == expected_response["ErrorCode"]
        assert response.error_text == expected_response["ErrorText"]
        assert response.error_details is not None
        assert len(response.error_details) == len(expected_response["ErrorDetails"])
        assert (
            response.error_details[0].error_code
            == expected_response["ErrorDetails"][0]["ErrorCode"]
        )

    def test_server_errors_return_internal_error_response(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        expected_response = {
            "ErrorCode": "INTERNAL_ERROR",
            "ErrorText": "Internal server error",
        }

        response, _, _, _ = self._send_return(
            client, status_code=500, response=expected_response
        )

        assert isinstance(response, SendReturnResponse500)
        assert response.error_code == expected_response["ErrorCode"]
        assert response.error_text == expected_response["ErrorText"]

    def test_unexpected_status_codes_return_unknown_error_response(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        expected_response = {
            "ErrorCode": "UNKNOWN",
            "ErrorText": "Unexpected status code: 404",
        }

        response, _, _, _ = self._send_return(
            client, status_code=404, response=expected_response
        )

        assert isinstance(response, SendReturnResponse500)
        assert response.error_code == expected_response["ErrorCode"]
        assert response.error_text == expected_response["ErrorText"]

    def test_invalid_json_responses_are_handled_gracefully(self):
        client = TransferTaxClient(
            software_key="software_key",
            base_url="http://example.com",
            user_agent="agent",
        )
        expected_response = {
            "Invalid": "Json",
        }

        response, _, _, _ = self._send_return(
            client, status_code=200, response=expected_response
        )

        assert isinstance(response, SendReturnResponse500)
        assert response.error_code == "INVALID_JSON"

    def _send_return(
        self,
        client: TransferTaxClient,
        request: Optional[SendReturnRequest] = None,
        response: Optional[dict] = None,
        status_code: int = 200,
    ) -> tuple[SendReturnResponse, str, dict, dict]:
        captured_url: Optional[str] = None
        captured_body: Optional[dict] = None
        captured_headers: Optional[dict] = None

        def mock_post(
            url: str,
            headers: Optional[dict] = None,
            json: Optional[dict] = None,
            **kwargs,
        ) -> MagicMock:
            nonlocal captured_url, captured_body, captured_headers
            captured_url = url
            captured_body = json
            captured_headers = headers

            mock_response = MagicMock()
            mock_response.status_code = status_code
            mock_response.json.return_value = response or {
                "UniqueIdentifier": "TEST123456",
                "AcceptedTimestamp": "2024-01-15T10:30:00.000Z",
            }
            return mock_response

        with patch.object(requests, "post", side_effect=mock_post):
            result = client.send_return(request or self._create_test_request())

        assert captured_url is not None, "URL was not captured"
        assert captured_body is not None, "Request data was not captured"
        assert captured_headers is not None, "Headers were not captured"

        return result, captured_url, captured_body, captured_headers

    def _create_test_request(self) -> SendReturnRequest:
        seller = SellerDetails.create(
            seller_individual_or_corporate=SellerIndividualOrCorporate.INDIVIDUAL,
            seller_id="123456-7890",
            seller_name="Test Seller",
            seller_date_of_birth=date(1980, 1, 1),
        )

        asset = AssetDetails.create(
            asset=AssetType.RESIDENTIAL_PROPERTY,
            seller_details=[seller],
            residential_id="A123",
            buyer_share_selling_price=250000.00,
            buyer_share_total_selling_price=250000.00,
        )

        return SendReturnRequest.create(
            sender=SenderType.REAL_ESTATE_AGENT,
            signing_date=date(2024, 1, 15),
            transfer_type=TransferType.SALE,
            asset_details=[asset],
            buyer_id="098765-4321",
            buyer_date_of_birth=date(1990, 5, 20),
            buyer_name="Test Buyer",
        )
