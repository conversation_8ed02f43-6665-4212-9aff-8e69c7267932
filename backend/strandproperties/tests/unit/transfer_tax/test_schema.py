from datetime import date, datetime, timezone

import pytest
from pydantic import ValidationError

from strandproperties.schemas.transfer_tax import (
    AssetDetails,
    AssetType,
    SellerDetails,
    SellerIndividualOrCorporate,
    SenderType,
    SendReturnRequest,
    SendReturnResponse200,
    SendReturnResponse400,
    SendReturnResponse500,
    TransferType,
)


class TestFiTransferTaxSchemaConstraints:
    def test_sender_type_enum_values_match_schema_constraints(self):
        assert SenderType.REAL_ESTATE_AGENT == 1
        assert SenderType.STOCK_TRADER == 2
        assert SenderType.INDIVIDUAL_BUYER == 3

    def test_transfer_type_enum_values_match_schema_constraints(self):
        assert TransferType.SALE == 1
        assert TransferType.EXCHANGE_OF_PROPERTY_DEAL == 2
        assert TransferType.OTHER == 3

    def test_asset_type_enum_values_match_schema_constraints(self):
        assert AssetType.RESIDENTIAL_PROPERTY == 1
        assert AssetType.PARKING_SPACE_STORAGE_UNIT == 2
        assert AssetType.CORPORATE_SHARE == 3
        assert AssetType.TIME_SHARE == 4
        assert AssetType.SHARE_IN_TELEPHONE_COMPANY == 5
        assert AssetType.OTHER_SECURITY_THAN_SHARE == 6

    def test_acquired_portion_percentage_valid_range_values(self):
        asset_valid = self._create_asset_details(acquired_portion_percentage=50.0)
        assert asset_valid.acquired_portion_percentage == 50.0

        asset_min = self._create_asset_details(acquired_portion_percentage=0.0)
        assert asset_min.acquired_portion_percentage == 0.0

        asset_max = self._create_asset_details(acquired_portion_percentage=100.0)
        assert asset_max.acquired_portion_percentage == 100.0

    def test_acquired_portion_percentage_invalid_range_values(self):
        with pytest.raises(ValidationError):
            self._create_asset_details(acquired_portion_percentage=-1.0)

        with pytest.raises(ValidationError):
            self._create_asset_details(acquired_portion_percentage=101.0)

    def test_buyer_name_valid_length_constraint(self):
        request = self._create_send_return_request(buyer_name="A" * 200)
        assert request.buyer_name is not None
        assert len(request.buyer_name) == 200

    def test_buyer_name_invalid_length_constraint(self):
        with pytest.raises(ValidationError):
            self._create_send_return_request(buyer_name="A" * 201)

    def test_real_estate_agency_phone_valid_length_constraint(self):
        request_with_phone = self._create_send_return_request(
            real_estate_agency_phone="+" + "1" * 34
        )
        assert request_with_phone.real_estate_agency_phone is not None
        assert len(request_with_phone.real_estate_agency_phone) == 35

    def test_real_estate_agency_phone_invalid_length_constraint(self):
        with pytest.raises(ValidationError):
            self._create_send_return_request(real_estate_agency_phone="+" + "1" * 35)

    def test_acquired_portion_fractions_valid_length_constraint(self):
        asset = self._create_asset_details(acquired_portion_fractions="1/2")
        assert asset.acquired_portion_fractions == "1/2"

    def test_acquired_portion_fractions_invalid_length_constraint(self):
        with pytest.raises(ValidationError):
            self._create_asset_details(acquired_portion_fractions="123456789012")

    def test_share_numbers_valid_length_constraint(self):
        asset = self._create_asset_details(
            asset=AssetType.CORPORATE_SHARE,
            company_id="1234567-8",
            company_name="Test Company",
            share_numbers="A123456789",
            quantity_of_shares=100,
        )
        assert asset.share_numbers == "A123456789"

    def test_share_numbers_invalid_length_constraint(self):
        with pytest.raises(ValidationError):
            self._create_asset_details(
                asset=AssetType.CORPORATE_SHARE,
                company_id="1234567-8",
                company_name="Test Company",
                share_numbers="A" * 31,
                quantity_of_shares=100,
            )

    def test_residential_id_valid_length_constraint(self):
        asset = self._create_asset_details(residential_id="A" * 200)
        assert asset.residential_id is not None
        assert len(asset.residential_id) == 200

    def test_residential_id_invalid_length_constraint(self):
        with pytest.raises(ValidationError):
            self._create_asset_details(residential_id="A" * 201)

    def test_asset_details_min_items_constraint_valid(self):
        asset = self._create_asset_details()
        assert len(asset.seller_details) == 1

    def test_asset_details_min_items_constraint_invalid(self):
        with pytest.raises(ValidationError):
            SendReturnRequest.create(
                sender=SenderType.REAL_ESTATE_AGENT,
                signing_date=date(2024, 1, 15),
                transfer_type=TransferType.SALE,
                asset_details=[],
            )

    def test_seller_details_min_items_constraint_valid(self):
        asset = self._create_asset_details()
        assert len(asset.seller_details) == 1

    def test_seller_details_min_items_constraint_invalid(self):
        with pytest.raises(ValidationError):
            AssetDetails.create(
                asset=AssetType.RESIDENTIAL_PROPERTY,
                seller_details=[],
                residential_id="A123",
                buyer_share_selling_price=250000.00,
                buyer_share_total_selling_price=250000.00,
            )

    def test_send_return_request_required_fields_valid(self):
        request = self._create_send_return_request()
        assert request.sender == SenderType.REAL_ESTATE_AGENT
        assert request.signing_date == date(2024, 1, 15)
        assert request.transfer_type == TransferType.SALE
        assert len(request.asset_details) == 1

    def test_send_return_response_200_required_fields_valid(self):
        response = SendReturnResponse200.create(
            unique_identifier="TEST123456",
            accepted_timestamp="2024-01-15T10:30:00.000Z",
        )
        assert response.unique_identifier == "TEST123456"
        assert response.accepted_timestamp == datetime(
            2024, 1, 15, 10, 30, 0, 0, tzinfo=timezone.utc
        )

    def test_send_return_response_400_required_fields_valid(self):
        response = SendReturnResponse400.create(
            error_text="Validation error",
        )
        assert response.error_text == "Validation error"

    def test_send_return_response_500_required_fields_valid(self):
        response = SendReturnResponse500.create(
            error_text="Internal server error",
        )
        assert response.error_text == "Internal server error"

    def _create_seller_details(
        self, seller_type=SellerIndividualOrCorporate.INDIVIDUAL, **kwargs
    ):
        defaults = {
            "seller_individual_or_corporate": seller_type,
            "seller_id": "123456-7890",
            "seller_name": "Test Seller",
        }
        defaults.update(kwargs)
        return SellerDetails.create(**defaults)

    def _create_asset_details(
        self, asset_type=AssetType.RESIDENTIAL_PROPERTY, **kwargs
    ):
        seller = self._create_seller_details()
        defaults = {
            "asset": asset_type,
            "seller_details": [seller],
            "residential_id": "A123",
            "buyer_share_selling_price": 250000.00,
            "buyer_share_total_selling_price": 250000.00,
        }
        defaults.update(kwargs)
        return AssetDetails.create(**defaults)

    def _create_send_return_request(self, **kwargs):
        asset = self._create_asset_details()
        defaults = {
            "sender": SenderType.REAL_ESTATE_AGENT,
            "signing_date": date(2024, 1, 15),
            "transfer_type": TransferType.SALE,
            "asset_details": [asset],
        }
        defaults.update(kwargs)
        return SendReturnRequest.create(**defaults)
