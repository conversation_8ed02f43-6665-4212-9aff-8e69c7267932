from pyramid.authorization import AL<PERSON>_PERMISSIONS, DENY_ALL, Allow, Everyone

from strandproperties.constants import RoleType

DEFAULT_PERMISSION = "permission_not_set"

# ACE stands for Access Control Entry
photographer_ace = (
    Allow,
    f"role:{RoleType.PHOTOGRAPHER}",
    [
        "activity:read",
        "image:create",
        "image:read",
        "image:update",
        "image:delete",
        "property:read",
        "property:validate",
        "tag:read",
        "office:read",
        "user:read",
        "user:update",
        "user_image:create",
        "mapping:read",
    ],
)

# realtor has its own permissions + the ones from photographer
# INFO: remember that if a view does not have an explicit `permission` set,
# it won't block any user
realtor_ace = (
    Allow,
    f"role:{RoleType.REALTOR}",
    [
        "permission_not_set",  # this is the default permission whenever we didn't explicitly set
        "contact:create",
        "contact:read",
        "contact:update",
        "document_library:create",
        "document_library:read",
        "document_library:update",
        "document_library:delete",
        "event_logs.view",
        "expense:create",
        "expense:read",
        "expense:update",
        "advertisement:read",
        "advertisement:create",
        "advertisement:update",
        "advertisement:delete",
        "brokerage_offer:create",
        "brochure:create",
        "brochure:read",
    ]
    + photographer_ace[2],
)

# admin has its own permissions
admin_ace = (
    Allow,
    f"role:{RoleType.ADMIN}",
    ["details_of_sale.request_changes:update"],
)


class RootFactory:
    __acl__ = [
        # __no_permission_required__: login, health, swagger, sowise webhook, strandproperties endpoints (x-api-token)
        (Allow, Everyone, "__no_permission_required__"),
        (Allow, f"role:{RoleType.ADMIN}", ALL_PERMISSIONS),
        realtor_ace,
        photographer_ace,
        DENY_ALL,
    ]

    def __init__(self, request):
        self.request = request
