import csv
import re
from datetime import datetime, timezone
from io import String<PERSON>
from typing import Any, List, Mapping, Optional, Sequence, Union

import requests
from sqlalchemy.orm import Session

from strandproperties.logger import logger

_ADSET_DELIMITER: str = "!#!"


class SmartlyExportService:

    def __init__(self, db_session: Session):
        self.db_session = db_session

    def generate_csv(self, advertisements: List[Any]) -> str:
        output = StringIO()
        writer = csv.writer(output)
        writer.writerow(
            [
                "ad_id",
                "agent_id",
                "budget",
                "facebook_page",
                "instagram_account",
                "heading",
                "subheading",
                "description",
                "sales_argument_1",
                "sales_argument_2",
                "sales_argument_3",
                "sales_argument_4",
                "sales_argument_5",
                "date_start",
                "date_and_time_start",
                "date_end",
                "date_and_time_end",
                "ad_type",
                "agent_name",
                "agent_email",
                "agent_phone",
                "agent_office",
                "agent_photo_url",
                "agent_video_url",
                "template_id",
                "property_id",
                "landing_page_url",
                "property_image_1_square_url",
                "property_image_1_vertical_url",
                "property_image_1_landscape_url",
                "property_image_1_portrait_url",
                "property_image_2_square_url",
                "property_image_2_vertical_url",
                "property_image_2_landscape_url",
                "property_image_2_portrait_url",
                "property_image_3_square_url",
                "property_image_3_vertical_url",
                "property_image_3_landscape_url",
                "property_image_3_portrait_url",
                "property_image_4_square_url",
                "property_image_4_vertical_url",
                "property_image_4_landscape_url",
                "property_image_4_portrait_url",
                "property_image_5_square_url",
                "property_image_5_vertical_url",
                "property_image_5_landscape_url",
                "property_image_5_portrait_url",
                "language",
                "property_address",
                "property_area",
                "property_city",
                "property_country",
                "property_type",
                "property_size",
                "property_price",
                "target_postal_codes",
                "target_cities",
                "property_lat",
                "property_lng",
                "radius",
            ]
        )

        now = datetime.now(timezone.utc)
        for advertisement in advertisements:
            try:
                if advertisement.end_date:
                    if advertisement.end_date.tzinfo is None:
                        ad_end_date_aware = advertisement.end_date.replace(
                            tzinfo=timezone.utc
                        )
                    else:
                        ad_end_date_aware = advertisement.end_date

                    if now > ad_end_date_aware:
                        from strandproperties.models.advertisement import AdStatus

                        advertisement.status = AdStatus.COMPLETED
                        continue

                writer.writerow(self._create_row_data(advertisement))

            except Exception as e:
                logger.error(
                    f"Error processing advertisement {advertisement.id}: {str(e)}"
                )
                continue

        output.seek(0)
        return output.read()

    def _create_row_data(self, advertisement: Any) -> List[str]:
        agent_user, agent_id_str = self._select_agent(advertisement)
        first_name = getattr(agent_user, "first_name", "") or ""
        last_name = getattr(agent_user, "last_name", "") or ""
        combined_name = f"{first_name} {last_name}".strip()

        agent_name = (
            getattr(agent_user, "name", None)
            or getattr(agent_user, "full_name", None)
            or combined_name
        )
        agent_email = getattr(agent_user, "email", "")
        agent_phone = getattr(agent_user, "phone_number", "")
        agent_office = getattr(agent_user, "office", "")
        agent_photo_url = getattr(agent_user, "photo_url", "")
        facebook_page = getattr(agent_user, "facebook_page", "")
        instagram_account = getattr(agent_user, "instagram_account", "")

        property = self._select_property(advertisement)
        (
            property_address,
            property_area,
            property_city,
            property_country,
            property_type,
            property_size,
            property_lat,
            property_lng,
        ) = self._extract_property_fields(property)

        property_price = self._property_price(property)

        image_urls = [img.url for img in (advertisement.advertisement_images or [])]

        image_urls.extend([""] * (5 - len(image_urls)))

        (
            image_1_square_url,
            image_1_vertical_url,
            image_1_landscape_url,
            image_1_portrait_url,
        ) = (
            [image_urls[0]] * 4 if image_urls[0] else ["", "", "", ""]
        )
        (
            image_2_square_url,
            image_2_vertical_url,
            image_2_landscape_url,
            image_2_portrait_url,
        ) = (
            [image_urls[1]] * 4 if image_urls[1] else ["", "", "", ""]
        )
        (
            image_3_square_url,
            image_3_vertical_url,
            image_3_landscape_url,
            image_3_portrait_url,
        ) = (
            [image_urls[2]] * 4 if image_urls[2] else ["", "", "", ""]
        )
        (
            image_4_square_url,
            image_4_vertical_url,
            image_4_landscape_url,
            image_4_portrait_url,
        ) = (
            [image_urls[3]] * 4 if image_urls[3] else ["", "", "", ""]
        )
        (
            image_5_square_url,
            image_5_vertical_url,
            image_5_landscape_url,
            image_5_portrait_url,
        ) = (
            [image_urls[4]] * 4 if image_urls[4] else ["", "", "", ""]
        )

        date_start, date_and_time_start = self._format_date_fields(
            advertisement.start_date
        )
        date_end, date_and_time_end = self._format_date_fields(advertisement.end_date)

        heading = advertisement.title or ""
        subheading = advertisement.description or ""
        description = advertisement.primary_text or ""

        feature_names = [
            feat.name.strip().title()
            for feat in getattr(property, "features", [])
            if getattr(feat, "name", None)
        ]
        sales_arguments = (feature_names + [""] * 5)[:5]
        (
            sales_argument_1,
            sales_argument_2,
            sales_argument_3,
            sales_argument_4,
            sales_argument_5,
        ) = sales_arguments

        property_id_str = (
            str(advertisement.property_id) if advertisement.property_id else ""
        )

        radius_value = self._radius_value(advertisement)

        target_city = advertisement.municipality or property_city
        target_country = advertisement.country or property_country
        target_cities = self._format_target_cities(
            target_city, target_country, radius_value
        )
        radius_km = str(radius_value)
        ad_type_str = advertisement.type.value if advertisement.type else ""
        budget_str = (
            str((advertisement.budget_total or 0) / 100)
            if advertisement.budget_total is not None
            else ""
        )
        property_reference = getattr(property, "reference", "") if property else ""
        landing_page_url = (
            f"https://strandproperties.com/single-property/?ref={property_reference}&a={agent_id_str}"
            if property_reference and agent_id_str
            else ""
        )

        agent_video_url = ""
        template_id = ""
        target_postal_codes = ""

        language_mapping = {"es": "Spanish", "fi": "Finnish", "en": "English"}
        advertisement_language = getattr(advertisement, "language", None) or "en"
        language = language_mapping.get(advertisement_language.lower(), "English")

        return [
            str(advertisement.id),
            agent_id_str,
            budget_str,
            facebook_page,
            instagram_account,
            heading,
            subheading,
            description,
            sales_argument_1,
            sales_argument_2,
            sales_argument_3,
            sales_argument_4,
            sales_argument_5,
            date_start,
            date_and_time_start,
            date_end,
            date_and_time_end,
            ad_type_str,
            agent_name,
            agent_email,
            agent_phone,
            agent_office,
            agent_photo_url,
            agent_video_url,
            template_id,
            property_id_str,
            landing_page_url,
            image_1_square_url,
            image_1_vertical_url,
            image_1_landscape_url,
            image_1_portrait_url,
            image_2_square_url,
            image_2_vertical_url,
            image_2_landscape_url,
            image_2_portrait_url,
            image_3_square_url,
            image_3_vertical_url,
            image_3_landscape_url,
            image_3_portrait_url,
            image_4_square_url,
            image_4_vertical_url,
            image_4_landscape_url,
            image_4_portrait_url,
            image_5_square_url,
            image_5_vertical_url,
            image_5_landscape_url,
            image_5_portrait_url,
            language,
            property_address,
            property_area,
            property_city,
            property_country,
            property_type,
            property_size,
            property_price,
            target_postal_codes,
            target_cities,
            property_lat,
            property_lng,
            radius_km,
        ]

    def _select_agent(self, advertisement: Any) -> tuple[Any, str]:
        if hasattr(advertisement, "agent_id") and advertisement.agent_id:
            return advertisement.agent, str(advertisement.agent_id)
        return advertisement.owner, str(advertisement.owner_id or "")

    def _select_property(self, advertisement: Any) -> Optional[Any]:
        return advertisement.property or advertisement.fi_property

    def _radius_value(self, ad: Any) -> int:
        default_radius = 50
        try:
            return (
                int(ad.target_area_radius_km)
                if ad.target_area_radius_km
                else default_radius
            )
        except (TypeError, ValueError):
            return default_radius

    def _extract_property_fields(
        self, property: Optional[Any]
    ) -> tuple[str, str, str, str, str, str, str, str]:
        if property is None:
            return ("", "", "", "", "", "", "", "")

        from strandproperties.models.fi_property import FIProperty

        if isinstance(property, FIProperty):
            fi_address = getattr(
                getattr(property, "fi_realty", None), "fi_address", None
            )
            address = fi_address.street_address if fi_address else ""
            area = fi_address.district if fi_address else ""
            city = fi_address.municipality if fi_address else ""

            country_dict = (
                fi_address.location.get("country")
                if fi_address and isinstance(fi_address.location, dict)
                else None
            )
            country = country_dict or "Finland"
            property_type = (
                property.fi_property_type.property_type
                if property.fi_property_type
                else ""
            )

            size = ""
            if (
                property.fi_residential_share_overview
                and property.fi_residential_share_overview.apartment
            ):
                apt = property.fi_residential_share_overview.apartment
                area_dict = (
                    apt.get("area", {}).get("total_area")
                    if isinstance(apt, dict)
                    else None
                )
                if isinstance(area_dict, dict):
                    size = str(area_dict.get("value", ""))
            if (
                not size
                and property.fi_plot_overview
                and isinstance(property.fi_plot_overview.area, dict)
            ):
                size = str(property.fi_plot_overview.area.get("value", ""))

            latitude = str(property.latitude) if property.latitude is not None else ""
            longitude = (
                str(property.longitude) if property.longitude is not None else ""
            )

            return (
                address,
                area,
                city,
                country,
                property_type,
                size,
                latitude,
                longitude,
            )

        address_info = (
            property.private_info.get("location", {})
            if hasattr(property, "private_info")
            else {}
        )
        address = address_info.get("address", "")
        city = (
            property._area_level_1.name
            if getattr(property, "_area_level_1", None)
            else ""
        )
        area = (
            property._area_level_2.name
            if getattr(property, "_area_level_2", None)
            else ""
        )
        country = getattr(property, "country", "") or (
            property._area_level_1.country
            if getattr(property, "_area_level_1", None)
            else ""
        )
        property_type = getattr(property, "property_type", "")

        size = ""
        for field in ("built_area", "interior_area", "plot_area"):
            value = getattr(property, field, None)
            if value:
                size = str(value)
                break

        latitude = (
            str(property.latitude)
            if getattr(property, "latitude", None) is not None
            else ""
        )
        longitude = (
            str(property.longitude)
            if getattr(property, "longitude", None) is not None
            else ""
        )

        return (address, area, city, country, property_type, size, latitude, longitude)

    def _property_price(self, property: Optional[Any]) -> str:
        if property is None:
            return ""

        from strandproperties.models.fi_property import FIProperty

        if isinstance(property, FIProperty):
            realty = getattr(property, "fi_realty", None)
            for field in ("selling_price", "monthly_rent", "starting_price_amount"):
                value = getattr(realty, field, None) if realty else None
                if value:
                    return str(value)
            return ""

        for field in ("price_sale", "price_rent_long_term", "price_rent_short_term"):
            value = getattr(property, field, None)
            if value:
                return str(value)
        return ""

    def _format_target_cities(self, city: str, country: str, radius_value: int) -> str:
        return f"{city}, {country}/{radius_value}/kilometer" if city and country else ""

    def _format_date_fields(self, date: Optional[datetime]) -> tuple[str, str]:
        if date:
            return date.strftime("%d-%m-%Y"), date.astimezone(timezone.utc).isoformat()
        return "", ""


class SmartlyStatsService:
    _NORMALISE_RE = re.compile(r"[^0-9a-zA-Z]+")

    def __init__(
        self,
        *,
        token: str | None = None,
        view_id: str,
        timeframe: str = "last_30_days",
        timezone_: str = "Europe/Helsinki",
        base_url: str | None = None,
        request_timeout: int = 30,
    ) -> None:
        if token is None:
            from strandproperties.config import app_cfg

            token = getattr(app_cfg, "smartly_token", None)

        if not token:
            raise ValueError(
                "Smartly token must be provided (via argument or SMARTLY_TOKEN env var)"
            )
        if not view_id:
            raise ValueError("Smartly view_id must be provided")

        self.token = token
        self.view_id = view_id
        self.timeframe = timeframe
        self.timezone = timezone_
        self.request_timeout = request_timeout

        self.endpoint = base_url or (
            f"https://api.smartly.io/stats/v2/view/{view_id}"
            f"?token={token}&timeframe={timeframe}"
            f"&timezone={timezone_}&format=json"
        )

    def get_stats(self) -> list[dict[str, Any]]:
        raw = self._fetch_json()
        headers: Sequence[str] = raw.get("headers", [])
        rows: Sequence[Sequence[Any]] = raw.get("report", [])

        if not headers:
            logger.warning(
                "Smartly stats response contained no headers – url='%s'", self.endpoint
            )
            return []

        norm_headers = [self._normalise_header(h) for h in headers]

        def parse_row(r: Sequence[Any]) -> dict[str, Any]:
            return {
                hdr: self._convert_value(val)
                for hdr, val in zip(norm_headers, r, strict=False)
            }

        return [parse_row(r) for r in rows]

    def _fetch_json(self) -> dict[str, Any]:
        try:
            response = requests.get(self.endpoint, timeout=self.request_timeout)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as exc:
            logger.error(
                "Smartly stats fetch failed – %s", exc, extra={"url": self.endpoint}
            )
            return {}

    @classmethod
    def _normalise_header(cls, header: str) -> str:
        header = header.strip().lower()
        header = header.replace("%", "_pct")
        header = cls._NORMALISE_RE.sub("_", header)
        header = re.sub(r"_+", "_", header)
        return header.strip("_")

    @staticmethod
    def _convert_value(value: Any) -> Any:
        if value is None:
            return None
        if isinstance(value, (int, float)):
            return value
        if isinstance(value, str):
            v = value.strip()
            if v == "":
                return None
            if v.isdigit():
                try:
                    return int(v)
                except ValueError:
                    pass
            try:
                return float(v.replace(",", ""))
            except ValueError:
                return v
        return value

    def update_advertisements(self, db_session, *, commit: bool = True) -> int:
        from datetime import datetime, timezone

        from sqlalchemy import select

        from strandproperties.models.advertisement import Advertisement

        updated = 0
        for row in self.get_stats():
            ad_set = row.get("ad_set") or row.get("ad")
            if isinstance(ad_set, str) and _ADSET_DELIMITER in ad_set:
                try:
                    ad_id = int(ad_set.split(_ADSET_DELIMITER, 1)[0])
                except ValueError:
                    continue
            else:
                continue

            ad: Advertisement | None = db_session.scalar(
                select(Advertisement).where(Advertisement.id == ad_id)
            )
            if ad is None:
                logger.warning(
                    "Smartly stats row for missing advertisement id=%s", ad_id
                )
                continue

            logger.info(
                (
                    "Smartly row – ad_id=%s impressions=%s "
                    "link_clicks=%s ctr=%s spend=%s"
                ),
                ad_id,
                row.get("impressions"),
                row.get("link_clicks"),
                row.get("ctr"),
                row.get("spend"),
            )

            changed = False

            _numeric_attrs: Mapping[str, tuple[type, ...]] = {
                "impressions": (int,),
                "link_clicks": (int,),
                "ctr": (int, float),
                "cpm": (int, float),
                "cpc": (int, float),
            }

            def set_if_diff(attr: str, new_value: Any) -> None:
                nonlocal changed

                if new_value is None:
                    return

                expected = _numeric_attrs.get(attr)
                if expected is not None and not isinstance(new_value, expected):
                    return

                if getattr(ad, attr) != new_value:
                    setattr(ad, attr, new_value)
                    changed = True

            set_if_diff("ctr", row.get("ctr"))
            cpc_raw = row.get("cpc")
            cpc_cents: int | None = None
            if isinstance(cpc_raw, (int, float)):
                cpc_cents = round(cpc_raw * 100)
            set_if_diff("cpc", cpc_cents)

            link_clicks_val = row.get("link_clicks")
            if isinstance(link_clicks_val, float):
                link_clicks_val = int(link_clicks_val)

            impressions_val = row.get("impressions")
            if isinstance(impressions_val, float):
                impressions_val = int(impressions_val)

            set_if_diff("link_clicks", link_clicks_val)
            set_if_diff("impressions", impressions_val)

            metrics_obj = dict(row)
            metrics_obj["link_clicks"] = link_clicks_val
            metrics_obj["impressions"] = impressions_val
            metrics_obj["cpc"] = cpc_raw

            preview_urls = row.get("facebook_ad_preview_link")
            if isinstance(preview_urls, str) and preview_urls.strip():
                first_preview_url = preview_urls.split(",", 1)[0].strip()
                set_if_diff("preview_url", first_preview_url)
                metrics_obj["preview_url"] = first_preview_url

            set_if_diff("metrics", metrics_obj)

            spend = row.get("spend")
            impressions_val_for_cpm = impressions_val or row.get("impressions")
            if spend is not None and impressions_val_for_cpm:
                try:
                    cpm_eur = (spend / impressions_val_for_cpm) * 1000
                    cpm_cents = round(cpm_eur * 100)
                    set_if_diff("cpm", cpm_cents)
                except Exception:
                    pass

            if changed:
                ad.updated_at = datetime.now(timezone.utc)
                updated += 1

        if updated and commit:
            db_session.commit()
        elif updated and not commit:
            db_session.flush()

        return updated
