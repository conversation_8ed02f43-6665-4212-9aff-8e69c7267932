from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional

import pytz
from weasyprint import HTML

from strandproperties.schemas.transfer_tax import (
    SendReturnRequest,
    SendReturnResponse200,
)
from strandproperties.services.documents.config import TemplatesEnum
from strandproperties.services.documents.document_renderer import DocumentRenderer


@dataclass
class PdfSection:
    title: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    subsections: Optional[List["PdfSection"]] = None


@dataclass
class PdfContext:
    sections: List[PdfSection]


class TransferTaxReturnsPdfGenerator:
    def __init__(self, timezone: str = "Europe/Helsinki"):
        self._timezone = timezone

    def generate(
        self, request: SendReturnRequest, response: SendReturnResponse200
    ) -> bytes:
        context = self._build_context(request, response)
        html = self._build_html(context)

        result = HTML(string=html).write_pdf()
        if result is None:
            raise RuntimeError("Failed to generate PDF")

        return result

    def _build_html(self, context: PdfContext) -> str:
        renderer = DocumentRenderer()
        return renderer.render_template(
            TemplatesEnum.TRANSFER_TAX_RETURNS, asdict(context)
        )

    def _build_context(
        self, request: SendReturnRequest, response: SendReturnResponse200
    ) -> PdfContext:
        return PdfContext(
            sections=[
                PdfSection(
                    title="Varainsiirtoveroilmoitus",
                    content={
                        "Tila": "Lähetetty",
                        "Vastaanottaja": "Verohallinto",
                        "Vastaanottonumero": response.unique_identifier,
                        "Vastaanottoaika": response.accepted_timestamp.astimezone(
                            pytz.timezone(self._timezone)
                        ).strftime("%d.%m.%Y %H:%M:%S"),
                    },
                    subsections=[
                        PdfSection(
                            title="Ilmoittajan tiedot",
                            content={
                                "Ilmoittaja": "todo",
                                "Kiinteistönvälitysliikkeen nimi": "todo",
                                "Y-tunnus": "todo",
                                "Yhteyshenkilö": "todo",
                                "Postiosoite": "todo",
                                "Postinumero": "todo",
                                "Puhelin": "todo",
                            },
                        ),
                        PdfSection(
                            title="Ostaja tai muu luovutuksensaaja",
                            content={
                                "Henkilötunnus/Y-tunnus": "todo",
                                "Nimi": "todo",
                            },
                        ),
                        PdfSection(
                            title="Kauppakirjan tai muun sopimuksen tiedot",
                            content={
                                "Allekirjoituspäivä": "todo",
                                "Luovutustyyppi": "todo",
                                "Onko Verohallinto antanut sopimusta koskevan ennakkoratkaisun, jota ostaja vaatii sovellettavaksi?": "todo",
                            },
                        ),
                        PdfSection(
                            title="Hankittu omaisuus",
                            content={
                                "Omaisuuslaji": "todo",
                                "Taloyhtiön y-tunnus": "todo",
                                "Taloyhtiön nimi": "todo",
                                "Hankittu osuus": "todo",
                                "Osakkeiden numerot": "todo",
                                "Pinta-ala": "todo",
                                "Huoneisto": "todo",
                                "Omistusoikeuden siirtymispäivä": "todo",
                                "Ostajan osuus kauppahinnasta": "todo",
                                "Ostajan osuus yhtiölainaosuudesta": "todo",
                                "Onko kauppakirjassa lisäkauppahintaehto?": "todo",
                                "Ensiasunnon ostajan ehtojen täyttyminen": "todo",
                                "Se osa kauppahinnasta tai muista vastikkeista, josta maksetaan varainsiirtovero (jos kauppa on osittain verovapaa)": "todo",
                                "Ostajan osuus kauppahinnasta ja muista vastikkeista": "todo",
                                "Varainsiirtoveron määrä": "todo",
                            },
                        ),
                        PdfSection(
                            title="Myyjät",
                            subsections=[
                                PdfSection(
                                    content={
                                        "Nimi": "todo",
                                        "Onko myyjä henkilö vai yhteisö?": "todo",
                                        "Hetu/Y-tunnus": "todo",
                                        "Onko myyjä ostajayhtiön osakas tai yhtiömies tai ostajayhtiön osakkaan tai yhtiömiehen vanhempi, puoliso tai lapsi?": "todo",
                                        "Oliko ostaja jo ennen kauppaa osakkaana tai yhtiömiehenä myyjäyhtiössä tai onko ostaja myyjäyhtiön osakkaan tai yhtiömiehen vanhempi, puoliso tai lapsi?": "todo",
                                        "Onko kyseessä yhtiön ja sen osakkaan tai yhtiömiehen välinen luovutus tai konsernin sisäinen luovutus?": "todo",
                                        "Myyty osuus": "todo",
                                    },
                                ),
                                PdfSection(
                                    content={
                                        "Nimi": "todo",
                                        "Onko myyjä henkilö vai yhteisö?": "todo",
                                        "Hetu/Y-tunnus": "todo",
                                        "Onko myyjä ostajayhtiön osakas tai yhtiömies tai ostajayhtiön osakkaan tai yhtiömiehen vanhempi, puoliso tai lapsi?": "todo",
                                        "Oliko ostaja jo ennen kauppaa osakkaana tai yhtiömiehenä myyjäyhtiössä tai onko ostaja myyjäyhtiön osakkaan tai yhtiömiehen vanhempi, puoliso tai lapsi?": "todo",
                                        "Onko kyseessä yhtiön ja sen osakkaan tai yhtiömiehen välinen luovutus tai konsernin sisäinen luovutus?": "todo",
                                        "Myyty osuus": "todo",
                                    },
                                ),
                            ],
                        ),
                    ],
                ),
            ]
        )
