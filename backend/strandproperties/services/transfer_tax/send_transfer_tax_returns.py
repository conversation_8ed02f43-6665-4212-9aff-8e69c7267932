from dataclasses import dataclass, field
from typing import List, Optional, Union, cast

from pydantic import ValidationError
from sqlalchemy.orm import Session

from strandproperties.constants import (
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
    RoleType,
)
from strandproperties.logger import logger
from strandproperties.models.fi_details_of_sale import (
    FIBuyerDetailsOfSale,
    FIDetailsOfSale,
)
from strandproperties.models.fi_property import FIProperty
from strandproperties.models.user import User
from strandproperties.schemas.transfer_tax import (
    ErrorDetail,
    SendReturnRequest,
    SendReturnResponse,
    SendReturnResponse200,
    SendReturnResponse400,
    SendReturnResponse500,
)
from strandproperties.services.document_library.service import (
    DocumentLibrary,
    DocumentType,
)
from strandproperties.services.transfer_tax.mapper import TransferTaxReturnsMapper
from strandproperties.services.transfer_tax.pdf import TransferTaxReturnsPdfGenerator
from strandproperties.services.transfer_tax.transfer_tax_client import TransferTaxClient
from strandproperties.utils.event_log import create_event_log


@dataclass
class BuyerTransferTaxReturnsSuccessResult:
    buyer_id: int
    unique_identifier: str
    document_id: int


@dataclass
class BuyerTransferTaxReturnsErrorResult:
    buyer_id: int
    error_text: str
    error_description_fi: List[str]
    error_description_en: List[str]


BuyerTransferTaxReturnsResult = Union[
    BuyerTransferTaxReturnsSuccessResult, BuyerTransferTaxReturnsErrorResult
]


@dataclass
class SendTransferTaxReturnsResult:
    success: bool
    buyers: List[BuyerTransferTaxReturnsResult] = field(default_factory=list)


class SendTransferTaxReturns:
    def __init__(
        self,
        db_session: Session,
        document_library: DocumentLibrary,
        api_client: TransferTaxClient,
        transfer_tax_pdf_generator: Optional[TransferTaxReturnsPdfGenerator] = None,
        mapper: Optional[TransferTaxReturnsMapper] = None,
    ):
        self._db_session = db_session
        self._document_library = document_library
        self._api_client = api_client
        self._transfer_tax_pdf_generator = (
            transfer_tax_pdf_generator or TransferTaxReturnsPdfGenerator()
        )
        self._mapper = mapper or TransferTaxReturnsMapper()

    def run(
        self,
        *,
        details_of_sale_id: int,
        user_id: int,
    ) -> SendTransferTaxReturnsResult:
        details_of_sale = self._get_details_of_sale_by_id(details_of_sale_id)
        user = self._get_user_by_id(user_id)

        if not self._user_is_allowed_to_send_transfer_tax_returns_for_property(
            user, details_of_sale.property
        ):
            raise UserNotAuthorizedError(details_of_sale.id, user_id)

        if not details_of_sale.buyers or not details_of_sale.sellers:
            raise UnableToSendTransferTaxReturnsError(details_of_sale.id)

        results: List[BuyerTransferTaxReturnsResult] = []

        for buyer in details_of_sale.buyers:
            try:
                result = self._send_transfer_tax_returns_for_buyer(
                    details_of_sale, buyer, user
                )
            except ValidationError as e:
                result = self._map_validation_error_to_result(buyer, e)
            except Exception as e:
                result = self._map_exception_to_result(buyer, e)

            results.append(result)

        return SendTransferTaxReturnsResult(
            success=all(
                isinstance(result, BuyerTransferTaxReturnsSuccessResult)
                for result in results
            ),
            buyers=results,
        )

    def _user_is_allowed_to_send_transfer_tax_returns_for_property(
        self, user: User, property: FIProperty
    ) -> bool:
        return self._user_has_access_to_property(
            user, property
        ) or self._user_is_admin_for_organization(user, property.organization_id)

    def _send_transfer_tax_returns_for_buyer(
        self, details_of_sale: FIDetailsOfSale, buyer: FIBuyerDetailsOfSale, user: User
    ) -> BuyerTransferTaxReturnsResult:
        request = self._map_to_request(details_of_sale, buyer)
        response = self._api_client.send_return(request)

        if not self._is_response_successful(response):
            logger.error(
                f"Unable to send transfer tax returns for details of sale with id {details_of_sale.id}",
                extra={"request": request, "response": response},
            )

            return self._map_error_response_to_result(buyer, response)

        response = cast(SendReturnResponse200, response)

        self._log_success_event(details_of_sale=details_of_sale, buyer=buyer, user=user)

        document_id = self._save_transfer_tax_filing_pdf(
            pdf_bytes=self._generate_transfer_tax_filing_pdf(request, response),
            filename=f"transfer-tax-return-{response.unique_identifier}.pdf",
        )

        return self._map_success_response_to_result(buyer, response, document_id)

    def _is_response_successful(self, response: SendReturnResponse) -> bool:
        return isinstance(response, SendReturnResponse200)

    def _map_error_response_to_result(
        self, buyer: FIBuyerDetailsOfSale, response: SendReturnResponse
    ) -> BuyerTransferTaxReturnsErrorResult:
        def map_error_details(
            details: List[ErrorDetail] | None, field_name: str
        ) -> List[str]:
            return (
                [
                    getattr(detail, field_name)
                    for detail in details
                    if getattr(detail, field_name)
                ]
                if details
                else []
            )

        if isinstance(response, SendReturnResponse400):
            return BuyerTransferTaxReturnsErrorResult(
                buyer_id=buyer.id,
                error_text=response.error_text,
                error_description_fi=map_error_details(
                    response.error_details, "error_description_fi"
                ),
                error_description_en=map_error_details(
                    response.error_details, "error_description_en"
                ),
            )

        if isinstance(response, SendReturnResponse500):
            return BuyerTransferTaxReturnsErrorResult(
                buyer_id=buyer.id,
                error_text=response.error_text,
                error_description_fi=[],
                error_description_en=[],
            )

        raise UnableToHandleResponseError(buyer.id)

    def _map_success_response_to_result(
        self,
        buyer: FIBuyerDetailsOfSale,
        response: SendReturnResponse200,
        document_id: int,
    ) -> BuyerTransferTaxReturnsSuccessResult:
        return BuyerTransferTaxReturnsSuccessResult(
            buyer_id=buyer.id,
            unique_identifier=response.unique_identifier,
            document_id=document_id,
        )

    def _map_validation_error_to_result(
        self, buyer: FIBuyerDetailsOfSale, error: ValidationError
    ) -> BuyerTransferTaxReturnsErrorResult:
        detailed_messages = [e["msg"] for e in error.errors()]

        return BuyerTransferTaxReturnsErrorResult(
            buyer_id=buyer.id,
            error_text="Validation error",
            error_description_fi=detailed_messages,
            error_description_en=detailed_messages,
        )

    def _map_exception_to_result(
        self, buyer: FIBuyerDetailsOfSale, error: Exception
    ) -> BuyerTransferTaxReturnsErrorResult:
        return BuyerTransferTaxReturnsErrorResult(
            buyer_id=buyer.id,
            error_text="System error",
            error_description_fi=[str(error)],
            error_description_en=[str(error)],
        )

    def _map_to_request(
        self, details_of_sale: FIDetailsOfSale, buyer: FIBuyerDetailsOfSale
    ) -> SendReturnRequest:
        return self._mapper.map_to_request(details_of_sale, buyer)

    def _generate_transfer_tax_filing_pdf(
        self, request: SendReturnRequest, response: SendReturnResponse200
    ) -> bytes:
        return self._transfer_tax_pdf_generator.generate(request, response)

    def _save_transfer_tax_filing_pdf(self, pdf_bytes: bytes, filename: str) -> int:
        return self._document_library.create_item_from_content(
            content=pdf_bytes,
            filename=filename,
            mime_type="application/pdf",
            document_type=DocumentType.FI_OTHER,
        ).id

    def _get_details_of_sale_by_id(self, details_of_sale_id: int) -> FIDetailsOfSale:
        if result := (
            self._db_session.query(FIDetailsOfSale)
            .filter(FIDetailsOfSale.id == details_of_sale_id)
            .first()
        ):
            return result

        raise DetailsOfSaleNotFoundError(details_of_sale_id)

    def _get_user_by_id(self, user_id: int) -> User:
        if result := self._db_session.query(User).filter(User.id == user_id).first():
            return result

        raise UserNotFoundError(user_id)

    def _user_is_admin_for_organization(self, user: User, organization_id: int) -> bool:
        return any(
            role.role == RoleType.ADMIN and role.organization_id == organization_id
            for role in user.roles
        )

    def _user_has_access_to_property(self, user: User, property: FIProperty) -> bool:
        return user.id in [user.id for user in property.realtor_users]

    def _log_success_event(
        self,
        *,
        details_of_sale: FIDetailsOfSale,
        user: User,
        buyer: FIBuyerDetailsOfSale,
    ) -> None:
        create_event_log(
            db_session=self._db_session,
            actor_id=user.id,
            actor_type=EventLogActorType.USER,
            object_type=EventLogObjectType.DETAILS_OF_SALE,
            object_id=details_of_sale.id,
            action=EventLogAction.TRANSFER_TAX_RETURN_SENT,
            details={
                "buyer_id": buyer.id,
            },
        )


class DetailsOfSaleNotFoundError(Exception):
    def __init__(self, details_of_sale_id: int):
        super().__init__(f"Details of sale with id {details_of_sale_id} not found")
        self.details_of_sale_id = details_of_sale_id


class UserNotFoundError(Exception):
    def __init__(self, user_id: int):
        super().__init__(f"User with id {user_id} not found")
        self.user_id = user_id


class UserNotAuthorizedError(Exception):
    def __init__(self, details_of_sale_id: int, user_id: int):
        super().__init__(
            f"User with id {user_id} is not authorized to send transfer tax returns for details of sale with id {details_of_sale_id}"
        )
        self.details_of_sale_id = details_of_sale_id
        self.user_id = user_id


class UnableToSendTransferTaxReturnsError(Exception):
    def __init__(self, details_of_sale_id: int):
        super().__init__(
            f"Unable to send transfer tax returns for details of sale with id {details_of_sale_id}"
        )
        self.details_of_sale_id = details_of_sale_id


class UnableToHandleResponseError(Exception):
    def __init__(self, buyer_id: int):
        super().__init__(f"Unable to handle response for buyer with id {buyer_id}")
        self.buyer_id = buyer_id
