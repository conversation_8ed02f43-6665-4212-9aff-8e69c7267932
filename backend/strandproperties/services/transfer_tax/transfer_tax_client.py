"""
Transfer Tax API Client

This module provides a client for interacting with the Finnish Tax Administration's Transfer Tax API.
https://api-developer.vero.fi/api-details#api=TransferTax&operation=postSendReturn
"""

import json
from datetime import date
from typing import Optional

import requests
from pydantic import ValidationError

from strandproperties.schemas.transfer_tax import (
    SendReturnRequest,
    SendReturnResponse,
    SendReturnResponse200,
    SendReturnResponse400,
    SendReturnResponse500,
)


class TransferTaxClient:
    def __init__(
        self,
        *,
        software_key: str,
        base_url: str,
        user_agent: str,
        software_id: Optional[str] = None,
        subscription_key: Optional[str] = None,
        timeout: Optional[int] = None,
    ):
        """
        Initialize the Transfer Tax API client.

        Args:
            base_url: Base URL for the API
            software_id: Software ID for identification
            software_key: API key for authentication
            subscription_key: API key for sandbox authentication (required for sandbox)
            user_agent: User agent string
            timeout: Request timeout in seconds
        """
        if not software_key:
            raise ValueError("software_key cannot be empty")

        if not base_url:
            raise ValueError("base_url cannot be empty")

        if not user_agent:
            raise ValueError("user_agent cannot be empty")

        self._base_url = base_url.rstrip("/")
        self._software_id = software_id
        self._software_key = software_key
        self._subscription_key = subscription_key
        self._user_agent = user_agent
        self._timeout = timeout

    def send_return(
        self,
        request: SendReturnRequest,
    ) -> SendReturnResponse:
        url = f"{self._base_url}/SendReturn/v1"
        headers = self._prepare_headers()
        body = self._prepare_body(request)

        try:
            response = requests.post(
                url,
                headers=headers,
                json=body,
                timeout=self._timeout,
            )

            if response.status_code == 200:
                return SendReturnResponse200(**response.json())
            elif response.status_code == 400:
                return SendReturnResponse400(**response.json())
            elif response.status_code == 500:
                return SendReturnResponse500(**response.json())
            else:
                return SendReturnResponse500.create(
                    error_code="UNKNOWN",
                    error_text=f"Unexpected status code: {response.status_code}",
                )
        except (json.JSONDecodeError, ValidationError) as e:
            return SendReturnResponse500.create(
                error_code="INVALID_JSON",
                error_text=str(e),
            )

    def _prepare_headers(self) -> dict:
        headers = {
            "Vero-SoftwareKey": self._software_key,
            "User-Agent": self._user_agent,
            "Content-Type": "application/json",
            "Accept": "*/*",
        }

        if self._subscription_key:
            headers["Ocp-Apim-Subscription-Key"] = self._subscription_key

        if self._software_id:
            headers["Vero-SoftwareId"] = self._software_id

        return headers

    def _prepare_body(self, request: SendReturnRequest) -> dict:
        result = request.model_dump(exclude_none=True, by_alias=True)

        for field in [
            "SigningDate",
            "BuyerDateOfBirth",
            "SellerDateOfBirth",
            "OwnershipTransferDate",
            "AddSellingPriceClarifiedDate",
        ]:
            self._convert_dates_in_dict(result, field)

        self._convert_enums_in_dict(result)

        return result

    def _convert_dates_in_dict(self, data: dict, field_name: str):
        if isinstance(data, dict):
            for key, value in data.items():
                if key == field_name and isinstance(value, date):
                    data[key] = value.isoformat()
                elif isinstance(value, dict):
                    self._convert_dates_in_dict(value, field_name)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            self._convert_dates_in_dict(item, field_name)

    def _convert_enums_in_dict(self, data: dict):
        if isinstance(data, dict):
            for key, value in data.items():
                if hasattr(value, "value"):
                    data[key] = value.value
                elif isinstance(value, dict):
                    self._convert_enums_in_dict(value)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            self._convert_enums_in_dict(item)
