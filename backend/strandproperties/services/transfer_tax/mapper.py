from datetime import date
from typing import Optional, Union

from strandproperties.models.contact import Contact
from strandproperties.models.fi_details_of_sale import (
    FIBuyerDetailsOfSale,
    FIDetailsOfSale,
    FISellerDetailsOfSale,
)
from strandproperties.schemas.transfer_tax import (
    AssetDetails,
    AssetType,
    SellerDetails,
    SellerIndividualOrCorporate,
    SenderType,
    SendReturnRequest,
    TransferType,
)


class TransferTaxReturnsMapper:
    def map_to_request(
        self, details_of_sale: FIDetailsOfSale, buyer: FIBuyerDetailsOfSale
    ) -> SendReturnRequest:
        return SendReturnRequest.create(
            sender=SenderType.REAL_ESTATE_AGENT,
            transfer_type=TransferType.SALE,
            buyer_id=self._get_buyer_id(buyer),
            buyer_date_of_birth=self._get_buyer_date_of_birth(buyer),
            buyer_name=self._get_buyer_name(buyer),
            signing_date=self._get_signing_date(details_of_sale),
            asset_details=[self._map_asset_details(details_of_sale, buyer)],
        )

    def _get_signing_date(self, details_of_sale: FIDetailsOfSale) -> date:
        if not details_of_sale.estimated_transaction_date:
            raise ValueError("Estimated transaction date is required")

        return details_of_sale.estimated_transaction_date

    def _get_buyer_id(self, buyer: FIBuyerDetailsOfSale) -> Optional[str]:
        contact: Contact = buyer.buyer
        return contact.social_security_number

    def _get_buyer_date_of_birth(self, buyer: FIBuyerDetailsOfSale) -> Optional[date]:
        contact: Contact = buyer.buyer
        return self._map_contact_date_of_birth(contact)

    def _get_buyer_name(self, buyer: FIBuyerDetailsOfSale) -> Optional[str]:
        contact: Contact = buyer.buyer
        return self._map_contact_name(contact)

    def _map_asset_details(
        self, details_of_sale: FIDetailsOfSale, buyer: FIBuyerDetailsOfSale
    ) -> AssetDetails:
        return AssetDetails.create(
            asset=AssetType.RESIDENTIAL_PROPERTY,
            seller_details=[
                self._map_seller_details(seller) for seller in details_of_sale.sellers
            ],
        )

    def _map_seller_details(self, seller: FISellerDetailsOfSale) -> SellerDetails:
        contact: Contact = seller.seller
        return SellerDetails.create(
            seller_individual_or_corporate=SellerIndividualOrCorporate.INDIVIDUAL,
            seller_id=contact.social_security_number,
            seller_name=self._map_contact_name(contact),
            seller_date_of_birth=self._map_contact_date_of_birth(contact),
            seller_ownership_share_percentage=seller.ownership_share_percent,
        )

    def _map_contact_name(self, contact: Contact) -> Union[str, None]:
        if contact.first_name and contact.last_name:
            return f"{contact.first_name} {contact.last_name}"

        return contact.name

    def _map_contact_date_of_birth(self, contact: Contact) -> Union[date, None]:
        return (
            date.fromisoformat(contact.date_of_birth) if contact.date_of_birth else None
        )
