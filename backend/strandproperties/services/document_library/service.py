from dataclasses import dataclass
from typing import Generic, List, Set, TypeVar

import requests
from sqlalchemy import Select, event, select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session, UOWTransaction, selectinload

from strandproperties.config import app_cfg
from strandproperties.constants import (
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
)
from strandproperties.libs.aws import S3Service
from strandproperties.logger import logger
from strandproperties.models.contact import Contact
from strandproperties.models.document_library import (
    DocumentLibraryItem,
    DocumentLibraryItemOwner,
    DocumentType,
    OwnerType,
    UploadState,
)
from strandproperties.models.fi_property import FIProperty
from strandproperties.models.fi_purchase_offer import FIPurchaseOffer
from strandproperties.models.fi_sales_agreement import FISalesAgreement
from strandproperties.utils.event_log import create_event_log
from strandproperties.utils.file.file import (
    extract_filename_from_response,
    extract_pdfs_from_zip,
    is_pdf_file,
    is_zip_file,
    validate_supported_file_type,
)

"""
Document Library Service
-----------------------

The DocumentLibrary service provides a high-level interface for managing documents. Think of it as a virtual file system
where each owner (like a property, agreement, or user) has its own directory. Documents are stored in S3 and linked to
their owners through the database, similar to how files are stored on disk and referenced by directory entries.

Key concepts:
1. Each owner has its own isolated "directory" of documents
2. Documents can be shared between multiple owners (like hard links in a file system)
3. When the last owner is removed, the document is automatically deleted
4. All operations are scoped to the owner's context
5. Access control is enforced at the owner level

Example usage:

```python
# Create a document library instance (like opening a specific directory)
document_library = create_document_library(
    db_session=session,
    user_id=user_id,
    organization_id=org_id,
    owner_type=OwnerType.FI_PROPERTY,
    owner_id=property_id
)

# Create new document from content (like creating a new file)
with open('document.pdf', 'rb') as f:
    content = f.read()
    item = document_library.create_item_from_content(
        content=content,
        filename='document.pdf',
        mime_type='application/pdf',
        document_type=DocumentType.FI_OTHER,
        description='Property contract'
    )

# Get all documents (like listing directory contents)
all_items = document_library.get_all()

# Get specific document (like accessing a specific file)
item = document_library.get_item(item_id)

# Download document content (like reading a file)
content = document_library.get_item_content(item)

# Generate download URL (like creating a temporary access link)
url = document_library.generate_download_url(item)

# Update document metadata (like changing file attributes)
document_library.update_item(
    id=item.id,
    document_type=DocumentType.SOME_OTHER_TYPE,
    description='Updated property contract'
)

# Share document with another owner (like creating a hard link)
other_library = create_document_library(
    db_session=session,
    user_id=user_id,
    organization_id=org_id,
    owner_type=OwnerType.FI_PROPERTY,
    owner_id=other_property_id
)
other_library.add_to_library(item)

# Remove document from an owner (like removing a hard link)
# Removing the last owner will delete the document
other_library.remove_from_library(item)

# Delete document globally
# This will remove the document from all owners and delete the actual content
document_library.delete_item(item.id)
```

Common pitfalls to avoid:
1. DO NOT create DocumentLibraryItem instances directly
2. DO NOT manipulate DocumentLibraryItemOwner relationships directly
3. DO NOT bypass the service layer for document operations
4. DO NOT store document content directly in the database
5. DO NOT handle S3 operations directly

The service handles all these concerns automatically and ensures proper cleanup and state management.

Adding New Document Owner Types
-----------------------------

The document library system supports multiple types of document owners, such as properties, agreements, or users.
Each owner type can have its own set of documents and access control rules. To add support for a new owner type:

1. Add the new owner type to the OwnerType enum in models/document_library.py
2. Create a new factory function in this file following the pattern of create_fi_property_document_library
3. Add the new owner type to the create_document_library factory function
4. If the new owner type needs special document types:
   - Add them to the DocumentType enum
   - Implement filtering in the get_document_types method to return only the relevant types for your owner type
   - Note: This is a temporary solution. A more robust type system will be implemented when more owner types are added
5. Update the record_deleted_owners event listener to handle cleanup of the new owner type
6. Create a new database migration to:
   - Add the new owner type
   - Add any new document types

Example implementation for a new owner type:

```python
def create_new_owner_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    owner_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    # Add your authorization logic here
    owner = db_session.scalars(
        select(NewOwner).where(
            NewOwner.id == owner_id,
            # Add your access control conditions
        )
    ).one_or_none()

    if owner is None:
        raise PermissionError("Access denied to document library")

    return DocumentLibrary(
        db_session=db_session,
        user_id=user_id,
        owner_type=OwnerType.NEW_OWNER,
        owner_id=owner_id,
    )
```

Then update the create_document_library function:

```python
def create_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    owner_type: OwnerType,
    owner_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    if owner_type == OwnerType.FI_PROPERTY:
        return create_fi_property_document_library(...)
    elif owner_type == OwnerType.NEW_OWNER:
        return create_new_owner_document_library(...)
    raise ValueError("Unsupported owner type")
```

Finally, update the cleanup_orphaned_links event listener:

```python
@event.listens_for(Session, "before_flush")
def cleanup_orphaned_links(session: Session, context: UOWTransaction, instances):
    deleted_owners: dict[OwnerType, Set[int]] = {
        OwnerType.FI_PROPERTY: {
            obj.id for obj in session.deleted if isinstance(obj, FIProperty)
        },
        OwnerType.NEW_OWNER: {
            obj.id for obj in session.deleted if isinstance(obj, NewOwner)
        },
    }
```

Note: Make sure to implement proper access control in your factory function to ensure users can only access documents they are authorized to view.
"""


@dataclass
class DocumentLibraryItemLoadOptions:
    load_creator: bool = False
    load_signings: bool = False


TValue = TypeVar("TValue")


@dataclass
class ValueChange(Generic[TValue]):
    before: TValue
    after: TValue


ChangeSet = dict[str, ValueChange]


class DocumentLibrary:
    def __init__(
        self,
        db_session: Session,
        user_id: int,
        owner_type: OwnerType,
        owner_id: int,
    ):
        self._db = db_session
        self._user_id = user_id
        self._owner_type = owner_type
        self._owner_id = owner_id
        self._s3_service = S3Service(app_cfg, app_cfg.aws_s3_files_bucket_name)

    def get_owner_type(self) -> OwnerType:
        return self._owner_type

    def get_owner_id(self) -> int:
        return self._owner_id

    def add_to_library(self, item: DocumentLibraryItem) -> None:
        item._add_owner(self._owner_type, self._owner_id)

    def remove_from_library(self, item: DocumentLibraryItem) -> None:
        if not item.is_owned_by(self._owner_type, self._owner_id):
            raise ValueError("Item is not owned by this library")

        if len(item.owners) == 1:
            self.delete_item(item.id)
        else:
            item._remove_owner(self._owner_type, self._owner_id)

    def get_item(
        self,
        id: int,
        options: DocumentLibraryItemLoadOptions = DocumentLibraryItemLoadOptions(),
    ) -> DocumentLibraryItem:
        try:
            stmt = self._build_base_query(options).where(DocumentLibraryItem.id == id)
            return self._db.execute(stmt).scalar_one()
        except NoResultFound:
            raise DocumentLibraryItemNotFoundError(id)

    def get_items(
        self,
        ids: List[int],
        options: DocumentLibraryItemLoadOptions = DocumentLibraryItemLoadOptions(),
    ) -> List[DocumentLibraryItem]:
        if not ids:
            return []

        stmt = self._build_base_query(options).where(DocumentLibraryItem.id.in_(ids))
        result = list(self._db.execute(stmt).scalars().all())
        self._ensure_all_items_found(result, ids)
        return result

    def get_all(
        self,
        options: DocumentLibraryItemLoadOptions = DocumentLibraryItemLoadOptions(),
    ) -> List[DocumentLibraryItem]:
        stmt = self._build_base_query(options).where(
            DocumentLibraryItem.upload_state == UploadState.READY
        )
        return list(self._db.execute(stmt).scalars().all())

    def update_item(
        self, id: int, document_type: DocumentType, description: str | None = None
    ) -> DocumentLibraryItem:
        item = self.get_item(id)
        changes = calculate_changes(
            item, document_type=document_type, description=description
        )
        item.document_type = document_type
        item.description = description
        self._log_update_event(item, changes)
        return item

    def update_item_content(
        self,
        id: int,
        content: bytes,
        filename: str,
        mime_type: str,
    ) -> DocumentLibraryItem:
        item = self.get_item(id)

        changes = calculate_changes(
            item,
            filename=filename,
            mime_type=mime_type,
            file_size=len(content),
        )

        old_s3_key = item.s3_key

        item.filename = filename
        item.mime_type = mime_type
        item.file_size = len(content)
        item.s3_key = self._generate_s3_key(item)

        self._s3_service.upload(
            self._add_document_library_path_prefix(item.s3_key),
            content,
            content_type=mime_type,
        )

        if old_s3_key and old_s3_key != item.s3_key:
            self._s3_service.delete_objects(
                [self._add_document_library_path_prefix(old_s3_key)]
            )

        self._log_update_event(item, changes)
        return item

    def delete_item(self, id: int) -> None:
        self.delete_items([id])

    def delete_items(self, ids: List[int]) -> None:
        if not ids:
            return

        items = self.get_items(ids)
        for item in items:
            self._db.delete(item)

        s3_keys_to_delete = [
            self._add_document_library_path_prefix(item.s3_key)
            for item in items
            if item.s3_key is not None
        ]
        if s3_keys_to_delete:
            self._s3_service.delete_objects(s3_keys_to_delete)

        for item in items:
            if item.upload_state == UploadState.READY:
                self._log_delete_event(item)

    def create_item_from_content(
        self,
        content: bytes,
        filename: str,
        mime_type: str,
        document_type: str,
        description: str | None = None,
    ) -> DocumentLibraryItem:
        item = DocumentLibraryItem(
            owners=[
                DocumentLibraryItemOwner(
                    owner_type=self._owner_type,
                    owner_id=self._owner_id,
                )
            ],
            created_by_id=self._user_id,
            s3_key=None,
            filename=filename,
            mime_type=mime_type,
            file_size=len(content),
            document_type=document_type,
            description=description,
            upload_state=UploadState.PENDING_UPLOAD,
        )
        self._db.add(item)
        self._db.flush()

        item.s3_key = self._generate_s3_key(item)

        self._s3_service.upload(
            self._add_document_library_path_prefix(item.s3_key),
            content,
            content_type=mime_type,
        )

        item.upload_state = UploadState.READY
        self._log_create_event(item)

        return item

    def create_pending_upload(
        self,
        filename: str,
        mime_type: str,
        file_size: int,
        document_type: str,
        description: str | None = None,
    ) -> DocumentLibraryItem:
        item = DocumentLibraryItem(
            owners=[
                DocumentLibraryItemOwner(
                    owner_type=self._owner_type,
                    owner_id=self._owner_id,
                )
            ],
            created_by_id=self._user_id,
            s3_key=None,
            filename=filename,
            mime_type=mime_type,
            file_size=file_size,
            document_type=document_type,
            description=description,
            upload_state=UploadState.PENDING_UPLOAD,
        )

        self._db.add(item)
        self._db.flush()

        item.s3_key = self._generate_s3_key(item)

        return item

    def confirm_pending_upload(self, id: int) -> None:
        self.confirm_pending_uploads([id])

    def confirm_pending_uploads(self, ids: List[int]) -> None:
        items = self.get_items(ids)

        for item in items:
            if item.created_by_id != self._user_id:
                raise DocumentLibraryItemNotFoundError(item.id)

            if item.upload_state != UploadState.PENDING_UPLOAD:
                raise UploadNotPendingError(item.id)

            if not item.s3_key or not self._s3_service.exists(
                self._add_document_library_path_prefix(item.s3_key)
            ):
                raise UploadedFileNotFoundError(item.id)

            item.upload_state = UploadState.READY

            self._log_create_event(item)

    def cancel_pending_upload(self, id: int) -> None:
        self.cancel_pending_uploads([id])

    def cancel_pending_uploads(self, ids: List[int]) -> None:
        items = self.get_items(ids)

        for item in items:
            if item.created_by_id != self._user_id:
                raise DocumentLibraryItemNotFoundError(item.id)

            if item.upload_state != UploadState.PENDING_UPLOAD:
                raise UploadNotPendingError(item.id)

        self.delete_items(ids)

    def generate_upload_url(self, item: DocumentLibraryItem) -> str:
        return self._s3_service.generate_upload_url(
            self._add_document_library_path_prefix(item.s3_key)
        )

    def get_item_content(self, item: DocumentLibraryItem) -> bytes:
        return self._s3_service.get_content(
            self._add_document_library_path_prefix(item.s3_key)
        )

    def generate_download_url(self, item: DocumentLibraryItem) -> str:
        self._log_viewed_event(item)
        return self._s3_service.generate_download_url(
            self._add_document_library_path_prefix(item.s3_key)
        )

    def get_document_types(self) -> List[DocumentType]:
        if self._owner_type not in [
            OwnerType.FI_PROPERTY,
            OwnerType.FI_SALES_AGREEMENT,
            OwnerType.CONTACT,
        ]:
            raise ValueError("Invalid owner type")

        return list(DocumentType)

    def download_and_save_from_url(
        self,
        url: str,
        file_name_prefix,
        document_type: DocumentType,
        description: str = None,
    ) -> List[DocumentLibraryItem]:
        try:
            logger.info(f"Starting download from URL: {url}")
            response = requests.get(url, stream=True, timeout=60)
            response.raise_for_status()

            content_type = response.headers.get(
                "content-type", "application/octet-stream"
            )
            filename = f"{file_name_prefix} {extract_filename_from_response(response)}"
            file_content = response.content

            validate_supported_file_type(content_type, filename)

            if is_zip_file(content_type, filename):
                return self._process_zip_file(
                    document_type,
                    file_content,
                    filename,
                    file_name_prefix,
                    description,
                )
            elif is_pdf_file(content_type, filename):
                return self._process_pdf_file(
                    document_type, file_content, filename, description
                )

        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading file from URL {url}: {str(e)}")
            raise ValueError(f"Failed to download file from URL: {str(e)}")
        except Exception as e:
            logger.error(f"Error processing file from URL {url}: {str(e)}")
            raise

    def _process_zip_file(
        self,
        document_type: DocumentType,
        file_content: bytes,
        filename: str,
        file_name_prefix: str,
        description: str,
    ) -> List[DocumentLibraryItem]:
        created_items = []

        try:
            pdf_files = extract_pdfs_from_zip(file_content, filename)

            for pdf_filename, pdf_content in pdf_files:
                try:
                    prefixed_filename = f"{file_name_prefix} {pdf_filename}"

                    item = self.create_item_from_content(
                        filename=prefixed_filename,
                        content=pdf_content,
                        mime_type="application/pdf",
                        document_type=document_type,
                        description=f"Extracted from {filename} - {description}",
                    )

                    created_items.append(item)
                    logger.info(f"Successfully saved PDF: {pdf_filename}")

                except Exception as e:
                    logger.error(
                        f"Error processing PDF {pdf_filename} from ZIP: {str(e)}"
                    )
                    continue

        except Exception as e:
            logger.error(f"Error processing ZIP file {filename}: {str(e)}")
            raise

        return created_items

    def _process_pdf_file(
        self,
        document_type: DocumentType,
        file_content: bytes,
        filename: str,
        description: str,
    ) -> List[DocumentLibraryItem]:
        if not filename.lower().endswith(".pdf"):
            filename = f"{filename}.pdf"

        item = self.create_item_from_content(
            filename=filename,
            document_type=document_type,
            content=file_content,
            mime_type="application/pdf",
            description=description,
        )

        logger.info(f"Successfully saved PDF: {filename}")
        return [item]

    def _build_base_query(
        self, options: DocumentLibraryItemLoadOptions | None = None
    ) -> Select:
        result: Select = (
            select(DocumentLibraryItem)
            .join(DocumentLibraryItem.owners)
            .where(
                DocumentLibraryItemOwner.owner_type == self._owner_type,
                DocumentLibraryItemOwner.owner_id == self._owner_id,
            )
        )

        if options is not None:
            result = self._with_load_options(result, options)

        return result

    def _with_load_options(
        self,
        stmt: Select,
        options: DocumentLibraryItemLoadOptions,
    ) -> Select:
        if options.load_creator:
            stmt = stmt.options(selectinload(DocumentLibraryItem.created_by))

        if options.load_signings:
            stmt = stmt.options(selectinload(DocumentLibraryItem.signings))

        return stmt

    @staticmethod
    def _generate_s3_key(item: DocumentLibraryItem) -> str:
        return f"{item.id}-{item.filename}"

    @staticmethod
    def _add_document_library_path_prefix(s3_key: str) -> str:
        return f"document-library/{s3_key}"

    @staticmethod
    def _ensure_all_items_found(
        items: List[DocumentLibraryItem], expected_ids: List[int]
    ) -> None:
        found_ids = [item.id for item in items]
        DocumentLibrary._ensure_all_ids_found(found_ids, expected_ids)

    @staticmethod
    def _ensure_all_ids_found(found_ids: List[int], expected_ids: List[int]) -> None:
        missing_ids = [item_id for item_id in expected_ids if item_id not in found_ids]
        if missing_ids:
            raise DocumentLibraryItemNotFoundError(missing_ids[0])

    def _log_create_event(self, item: DocumentLibraryItem) -> None:
        self._log_event(item, EventLogAction.CREATED)

    def _log_update_event(self, item: DocumentLibraryItem, changes: ChangeSet) -> None:
        if not changes:
            return

        create_event_log(
            db_session=self._db,
            object_type=EventLogObjectType.DOCUMENT_LIBRARY_ITEM,
            object_id=item.id,
            actor_id=self._user_id,
            action=EventLogAction.UPDATED,
            data_before={key: change.before for key, change in changes.items()},
            data_after={key: change.after for key, change in changes.items()},
        )

    def _log_viewed_event(
        self,
        item: DocumentLibraryItem,
        *,
        actor_id: int | None = None,
        actor_type: EventLogActorType = EventLogActorType.USER,
    ) -> None:
        self._log_event(
            item,
            EventLogAction.VIEWED,
            actor_id=actor_id,
            actor_type=actor_type,
        )

    def _log_delete_event(self, item: DocumentLibraryItem) -> None:
        self._log_event(item, EventLogAction.DELETED)

    def _log_event(
        self,
        item: DocumentLibraryItem,
        action: EventLogAction,
        *,
        actor_id: int | None = None,
        actor_type: EventLogActorType = EventLogActorType.USER,
    ) -> None:
        create_event_log(
            db_session=self._db,
            actor_id=actor_id if actor_id is not None else self._user_id,
            actor_type=actor_type,
            object_type=EventLogObjectType.DOCUMENT_LIBRARY_ITEM,
            object_id=item.id,
            action=action,
        )


class DocumentLibraryItemNotFoundError(Exception):
    def __init__(self, id: int):
        super().__init__(f"DocumentLibraryItem with id {id} not found")
        self.id = id


class UploadNotPendingError(Exception):
    def __init__(self, id: int):
        super().__init__(
            f"DocumentLibraryItem with id {id} is not in PENDING_UPLOAD state"
        )
        self.id = id


class UploadedFileNotFoundError(Exception):
    def __init__(self, id: int):
        super().__init__(f"File for DocumentLibraryItem with id {id} not found in S3")
        self.id = id


def create_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    owner_type: OwnerType,
    owner_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    if owner_type == OwnerType.FI_PROPERTY:
        return create_fi_property_document_library(
            db_session,
            user_id,
            organization_id,
            owner_id,
            is_admin,
        )
    if owner_type == OwnerType.FI_SALES_AGREEMENT:
        return create_fi_sales_agreement_document_library(
            db_session,
            user_id,
            organization_id,
            owner_id,
            is_admin,
        )
    if owner_type == OwnerType.FI_PURCHASE_OFFER:
        return create_fi_purchase_offer_document_library(
            db_session,
            user_id,
            organization_id,
            owner_id,
            is_admin,
        )
    if owner_type == OwnerType.CONTACT:
        return create_contact_document_library(
            db_session,
            user_id,
            organization_id,
            owner_id,
            is_admin,
        )

    raise ValueError("Unsupported owner type")


def create_fi_property_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    property_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    property = db_session.scalars(
        select(FIProperty).where(
            FIProperty.id == property_id,
            FIProperty.realtor_users.any(id=user_id) | is_admin,
            FIProperty.organization_id == organization_id,
        )
    ).one_or_none()

    if property is None:
        raise PermissionError("Access denied to document library")

    return DocumentLibrary(
        db_session=db_session,
        user_id=user_id,
        owner_type=OwnerType.FI_PROPERTY,
        owner_id=property_id,
    )


def calculate_changes(item: DocumentLibraryItem, **values: object) -> ChangeSet:
    changes: ChangeSet = {}
    for field, new_value in values.items():
        current_value = getattr(item, field, None)
        if current_value != new_value:
            changes[field] = ValueChange(before=current_value, after=new_value)
    return changes


def create_fi_sales_agreement_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    sales_agreement_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    return DocumentLibrary(
        db_session=db_session,
        user_id=user_id,
        owner_type=OwnerType.FI_SALES_AGREEMENT,
        owner_id=sales_agreement_id,
    )


def create_fi_purchase_offer_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    purchase_offer_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    return DocumentLibrary(
        db_session=db_session,
        user_id=user_id,
        owner_type=OwnerType.FI_PURCHASE_OFFER,
        owner_id=purchase_offer_id,
    )


def create_contact_document_library(
    db_session: Session,
    user_id: int,
    organization_id: int,
    contact_id: int,
    is_admin: bool = False,
) -> DocumentLibrary:
    contact = db_session.scalars(
        select(Contact).where(
            Contact.id == contact_id,
            Contact.assigned_to_users.any(id=user_id) | is_admin,
            Contact.organization_id == organization_id,
        )
    ).one_or_none()

    if contact is None:
        raise PermissionError("Access denied to document library")

    return DocumentLibrary(
        db_session=db_session,
        user_id=user_id,
        owner_type=OwnerType.CONTACT,
        owner_id=contact_id,
    )


@event.listens_for(Session, "before_flush")
def cleanup_orphaned_links(session: Session, context: UOWTransaction, instances):
    deleted_owners: dict[OwnerType, Set[int]] = {
        OwnerType.FI_PROPERTY: {
            obj.id for obj in session.deleted if isinstance(obj, FIProperty)
        },
        OwnerType.FI_SALES_AGREEMENT: {
            obj.id for obj in session.deleted if isinstance(obj, FISalesAgreement)
        },
        OwnerType.FI_PURCHASE_OFFER: {
            obj.id for obj in session.deleted if isinstance(obj, FIPurchaseOffer)
        },
        OwnerType.CONTACT: {
            obj.id for obj in session.deleted if isinstance(obj, Contact)
        },
    }

    for owner_type, owner_ids in deleted_owners.items():
        if not owner_ids:
            continue

        item_owners = (
            session.query(DocumentLibraryItemOwner)
            .filter(
                DocumentLibraryItemOwner.owner_type == owner_type,
                DocumentLibraryItemOwner.owner_id.in_(owner_ids),
            )
            .all()
        )

        for item_owner in item_owners:
            item = session.get(DocumentLibraryItem, item_owner.item_id)
            if item:
                document_library = DocumentLibrary(
                    db_session=session,
                    user_id=item.created_by_id,
                    owner_type=item_owner.owner_type,
                    owner_id=item_owner.owner_id,
                )
                document_library.remove_from_library(item)
