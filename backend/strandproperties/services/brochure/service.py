import os
from dataclasses import dataclass
from datetime import datetime

from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload, selectinload

from strandproperties.constants import (
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
)
from strandproperties.logger import logger
from strandproperties.models.brochure import (
    Brochure,
    BrochureImagePage,
    BrochureImages,
    BrochureImageWithPosition,
    BrochureLanguage,
    CoverTheme,
)
from strandproperties.models.document_library import DocumentType, OwnerType
from strandproperties.models.fi_address import FIAddress
from strandproperties.models.fi_housing_company import FIHousingCompany
from strandproperties.models.fi_property import FIProperty, FIRealty
from strandproperties.models.user import User
from strandproperties.schemas.brochure import (
    BrochureImagesCreate,
    BrochureInformationCreate,
    PropertyImagePage,
)
from strandproperties.schemas.fi_property.fi_property import FIPropertyRead
from strandproperties.services.document_library.service import create_document_library
from strandproperties.services.documents.config import (
    COVER_THEME_IMAGES,
    PDFS_DIR,
    TemplatesEnum,
)
from strandproperties.services.documents.document_renderer import DocumentRenderer
from strandproperties.services.documents.image_processor import ImageProcessor
from strandproperties.services.documents.pdf_utils import add_stamp_to_pdf, combine_pdfs
from strandproperties.utils.event_log import create_event_log


@dataclass(frozen=True)
class GeneratePropertyImagesPdfResult:
    pdf: bytes
    id: int


class PropertyNotFoundError(Exception):
    def __init__(self, property_id: int):
        super().__init__(f"Property with id {property_id} not found")
        self.property_id = property_id


class BrochureNotFoundError(Exception):
    def __init__(self, property_id: int):
        super().__init__(f"Brochure for property with id {property_id} not found")
        self.property_id = property_id


class BrochureService:
    def __init__(
        self,
        db_session: Session,
        user_id: int = None,
        organization_id: int = None,
        is_admin: bool = False,
    ):
        self._db_session = db_session
        self._user_id = user_id
        self._organization_id = organization_id
        self._is_admin = is_admin
        self._renderer = DocumentRenderer()
        self._image_processor = ImageProcessor()

    def generate_brochure_pdf(
        self,
        property_id: int,
        realtor_id: int,
        is_complete: bool,
        theme: CoverTheme,
        language: BrochureLanguage,
        include_cover: bool,
        cover_page_type: int,
        property_blueprint_image: str,
        property_images_data: BrochureImagesCreate,
        property_information_data: BrochureInformationCreate,
    ) -> bytes:
        """
        Generate a complete brochure PDF based on request parameters.

        Saves the property images data and the whole brochure data to the database.
        Adds Brochure PDF to the document library under the property.

        Returns the PDF as bytes.
        """
        try:
            realtor = self._db_session.scalars(
                select(User).where(User.id == realtor_id)
            ).one_or_none()

            cover_page_pdf = None
            blueprint_page_pdf = None

            if include_cover:
                property = self._db_session.scalars(
                    select(FIProperty).where(FIProperty.id == property_id)
                ).one_or_none()
                if not property:
                    raise PropertyNotFoundError(property_id)

                subtitle = f"Property in {property.fi_realty.fi_address.municipality}, {property.fi_realty.fi_address.street_address}, {property.fi_realty.fi_address.district}, REFERENCE NUMBER"
                cover_page_image = COVER_THEME_IMAGES.get(theme, {})

                cover_page_data = {
                    "sub_header": subtitle,
                    "cover_page_type": int(cover_page_type or 1),
                }
                cover_page_html = self._renderer.render_template(
                    TemplatesEnum.FRONT_COVER,
                    cover_page_data,
                    custom_images={"image": cover_page_image.get("image", "")},
                    auto_inject_images=True,
                )
                cover_page_pdf = self._renderer.generate_pdf(
                    cover_page_html, TemplatesEnum.BASE_VISUAL
                )

            if property_blueprint_image:
                blueprint_page_html = self._renderer.render_template(
                    TemplatesEnum.BROCHURE_PROPERTY_BLUEPRINT_IMAGE,
                    {},
                    custom_images={"image": property_blueprint_image},
                    auto_inject_images=True,
                )
                blueprint_page_pdf = self._renderer.generate_pdf(
                    blueprint_page_html, TemplatesEnum.BASE_VISUAL
                )

            property_short_intro_pdf = self._generate_short_intro_pdf(
                property_id, realtor, property_information_data
            )

            property_images_result = (
                self._generate_property_images_pdf(property_images_data)
                if property_images_data
                else self._get_property_images_version(property_id)
            )
            property_images_pdf = property_images_result.pdf
            property_images_version_id = property_images_result.id

            property_information_pdf = self._generate_property_information_pdf(
                property_id
            )

            brochure_last_page_path = os.path.join(
                PDFS_DIR, "brochure_last_page_fi.pdf"
            )
            with open(brochure_last_page_path, "rb") as f:
                brochure_last_page_pdf = f.read()

            latest_version = self._db_session.scalars(
                select(Brochure)
                .where(Brochure.property_id == property_id)
                .order_by(Brochure.version_number.desc())
                .limit(1)
            ).one_or_none()

            version_number = (
                1 if not latest_version else latest_version.version_number + 1
            )
            last_version_creation_date = (
                latest_version.created_at if latest_version else None
            )

            version_number_stamp_pdf = self._generate_version_number_stamp_pdf(
                version_number, last_version_creation_date
            )
            stamped_property_information_pdf = add_stamp_to_pdf(
                property_information_pdf, version_number_stamp_pdf
            )

            parts = []
            if cover_page_pdf:
                parts.append(cover_page_pdf)
            if blueprint_page_pdf:
                parts.append(blueprint_page_pdf)
            parts.extend(
                [
                    property_short_intro_pdf,
                    property_images_pdf,
                    stamped_property_information_pdf,
                    brochure_last_page_pdf,
                ]
            )
            combined_pdf, pages_amount = combine_pdfs(parts, add_empty_pages=True)

            brochure_db_row = self._create_brochure_db_row(
                property_id=property_id,
                user_id=realtor_id,
                theme=theme,
                language=language,
                is_complete=is_complete,
                property_information_version_id=None,
                property_images_version_id=property_images_version_id,
                version_number=version_number,
            )

            document_library = create_document_library(
                db_session=self._db_session,
                user_id=self._user_id,
                organization_id=self._organization_id,
                owner_type=OwnerType.FI_PROPERTY,
                owner_id=property_id,
                is_admin=self._is_admin,
            )
            document_item = document_library.create_item_from_content(
                content=combined_pdf,
                filename=f"brochure_{property_id}_v{version_number}.pdf",
                mime_type="application/pdf",
                document_type=DocumentType.FI_COMPREHENSIVE_BROCHURE,
                description=f"Brochure PDF",
            )

            create_event_log(
                db_session=self._db_session,
                object_type=EventLogObjectType.BROCHURE,
                object_id=brochure_db_row.id,
                action=EventLogAction.CREATED,
                actor_id=self._user_id,
                actor_type=EventLogActorType.USER,
                details={
                    "generated_brochure_pdf": [
                        {
                            "document_id": document_item.id,
                            "brochure_id": brochure_db_row.id,
                        }
                    ]
                },
            )

            return combined_pdf
        except PropertyNotFoundError:
            raise
        except BrochureNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error generating brochure PDF: {e}")
            raise ValueError(f"Error generating brochure PDF: {e}")

    def save_brochure_draft(
        self,
        property_id: int,
        realtor_id: int,
        theme: CoverTheme = CoverTheme.WHITE,
        language: BrochureLanguage = BrochureLanguage.FI,
        include_cover: bool = False,
        cover_page_type: int = 1,
        property_blueprint_image: str = None,
        property_information_data: BrochureInformationCreate = None,
        property_images_data: BrochureImagesCreate = None,
    ) -> Brochure:
        previous_version = self._db_session.scalars(
            select(Brochure)
            .where(Brochure.property_id == property_id)
            .order_by(Brochure.version_number.desc())
            .limit(1)
        ).one_or_none()

        brochure = Brochure(
            property_id=property_id,
            realtor_id=realtor_id,
            theme=theme,
            language=language,
            is_complete=False,
            version_number=(
                previous_version.version_number + 1 if previous_version else 1
            ),
            property_information_version_id=None,
            property_images_version_id=(
                property_images_data.id if property_images_data else None
            ),
        )

        self._db_session.add(brochure)
        self._db_session.flush()

        create_event_log(
            db_session=self._db_session,
            object_type=EventLogObjectType.BROCHURE,
            object_id=brochure.id,
            action=EventLogAction.CREATED,
            actor_id=self._user_id,
            actor_type=EventLogActorType.USER,
            details={
                "saved_brochure_draft": {
                    "brochure_id": brochure.id,
                }
            },
        )
        return brochure

    def _generate_property_images_pdf(
        self, property_images_request: BrochureImagesCreate
    ) -> GeneratePropertyImagesPdfResult:
        try:
            processed_pages = []
            for page in property_images_request.images_data:
                processed_page = self._process_image_page(page)
                processed_pages.append(processed_page)

            db_model = self._create_property_images_db_row(
                pages_data=property_images_request.images_data,
            )

            template_data = {
                "pages": processed_pages,
            }

            html_content = self._renderer.render_template(
                TemplatesEnum.BROCHURE_PROPERTY_IMAGES,
                template_data,
                auto_inject_images=True,
            )

            generated_pdf = self._renderer.generate_pdf(
                html_content, TemplatesEnum.BASE_VISUAL
            )

            return GeneratePropertyImagesPdfResult(
                pdf=generated_pdf,
                id=db_model.id,
            )

        except Exception as e:
            logger.error(f"Error generating PDF from images: {e}")
            raise ValueError(f"Error generating PDF from images: {e}")

    def _process_image_page(self, page: PropertyImagePage):
        try:
            processed_image_page = []

            for image in page.images:
                base64_image = self._image_processor.convert_image_to_base64(
                    image.image_url
                )
                processed_image_page.append(
                    {"image_str": base64_image, "position": image.position}
                )

            return processed_image_page
        except Exception as e:
            logger.error(f"Error processing image page: {e}")
            raise ValueError(f"Error processing image page: {e}")

    def _create_property_images_db_row(self, pages_data: list) -> BrochureImages:
        new_images = BrochureImages()
        self._db_session.add(new_images)
        self._db_session.flush()

        for page_data in pages_data:
            page_dict = (
                page_data.model_dump()
                if hasattr(page_data, "model_dump")
                else page_data
            )

            image_page = BrochureImagePage(
                brochure_images_id=new_images.id, page_number=page_dict["page_number"]
            )
            self._db_session.add(image_page)
            self._db_session.flush()

            for image_data in page_dict["images"]:
                image_with_position = BrochureImageWithPosition(
                    page_id=image_page.id,
                    image_url=image_data["image_url"],
                    position=image_data["position"],
                )
                self._db_session.add(image_with_position)

        self._db_session.flush()
        logger.info("Created new property images version with typed models")
        return new_images

    def _create_brochure_db_row(
        self,
        property_id: int,
        user_id: int,
        theme: CoverTheme,
        language: BrochureLanguage,
        is_complete: bool,
        property_information_version_id: int,
        property_images_version_id: int,
        version_number: int,
    ) -> Brochure:
        brochure = Brochure(
            property_id=property_id,
            realtor_id=user_id,
            theme=theme,
            language=language,
            is_complete=is_complete,
            version_number=version_number,
            property_information_version_id=property_information_version_id,
            property_images_version_id=property_images_version_id,
        )
        self._db_session.add(brochure)
        self._db_session.flush()
        logger.info(
            f"Created new brochure for property {property_id}, version: {version_number}, in the database"
        )
        return brochure

    def _get_property_images_version(
        self, property_id: int
    ) -> GeneratePropertyImagesPdfResult:
        latest_brochure = self._db_session.scalars(
            select(Brochure)
            .where(Brochure.property_id == property_id)
            .order_by(Brochure.version_number.desc())
            .limit(1)
        ).one_or_none()

        if not latest_brochure:
            raise BrochureNotFoundError(property_id)

        if not latest_brochure.property_images_version_id:
            raise BrochureNotFoundError(property_id)

        latest_brochure_images = self._db_session.scalars(
            select(BrochureImages)
            .options(
                selectinload(BrochureImages.pages).selectinload(
                    BrochureImagePage.images
                )
            )
            .where(BrochureImages.id == latest_brochure.property_images_version_id)
        ).one_or_none()

        if not latest_brochure_images:
            raise ValueError(
                f"No property images version found for brochure: {latest_brochure.id}"
            )

        deserialized_images = []
        for page in latest_brochure_images.pages:
            page_images = []
            for image_with_pos in page.images:
                page_images.append(
                    {
                        "image_url": image_with_pos.image_url,
                        "position": image_with_pos.position,
                    }
                )

            page_data = {"page_number": page.page_number, "images": page_images}

            try:
                page_schema = PropertyImagePage.model_validate(page_data)
                deserialized_images.append(page_schema)
            except Exception as e:
                raise ValueError(f"Invalid stored image data: {str(e)}")

        brochure_images_create = BrochureImagesCreate(
            images_data=deserialized_images,
        )

        return self._generate_property_images_pdf(brochure_images_create)

    def _generate_property_information_pdf(self, property_id: int) -> bytes:
        try:
            property_query = (
                select(FIProperty)
                .options(
                    selectinload(FIProperty.contacts),
                    selectinload(FIProperty.realtor_users),
                    selectinload(FIProperty.descriptions),
                    selectinload(FIProperty.images),
                    selectinload(FIProperty.video_streams),
                    selectinload(FIProperty.video_tours),
                    joinedload(FIProperty.fi_property_type),
                    joinedload(FIProperty.fi_realty)
                    .joinedload(FIRealty.fi_address)
                    .joinedload(FIAddress.area),
                    joinedload(FIProperty.fi_realty).selectinload(FIRealty.agents),
                    joinedload(FIProperty.fi_property_overview),
                    joinedload(FIProperty.fi_residential_property_overview),
                    joinedload(FIProperty.fi_commercial_property_overview),
                    joinedload(FIProperty.fi_estate_overview),
                    joinedload(FIProperty.fi_plot_overview),
                    joinedload(FIProperty.fi_other_share_overview),
                    joinedload(FIProperty.fi_residential_share_overview),
                    joinedload(FIProperty.fi_housing_company)
                    .joinedload(FIHousingCompany.fi_address)
                    .joinedload(FIAddress.area),
                    joinedload(FIProperty.fi_housing_company).selectinload(
                        FIHousingCompany.fi_plot_overviews
                    ),
                )
                .where(FIProperty.id == property_id)
            )

            property = self._db_session.scalars(property_query).unique().one_or_none()
            if not property:
                raise PropertyNotFoundError(property_id)

            property_read = FIPropertyRead.model_validate(property)

            template_data = {
                "title": f"{property_read.fi_realty.fi_address.street_address}, {property_read.fi_realty.fi_address.district}, {property_read.fi_realty.fi_address.postal_code} {property_read.fi_realty.fi_address.municipality}",
                "building": {
                    "building_year": self._safe_get_nested(
                        property_read,
                        "fi_housing_company",
                        "construction",
                        "construction_year",
                    ),
                    "year_of_commissioning": self._safe_get_nested(
                        property_read,
                        "fi_housing_company",
                        "construction",
                        "usage_start_year",
                    ),
                    "building_permit": self._safe_get_nested(
                        property_read,
                        "fi_residential_property_overview",
                        "construction_permit_grant_year",
                    ),
                    "rooms": self._safe_get_nested(
                        property_read, "fi_property_overview", "floor_plan"
                    ),
                    "floors": self._safe_get_nested(
                        property_read,
                        "fi_residential_share_overview",
                        "apartment",
                        "floors",
                        "total_floor_count",
                    ),
                },
                "price": {
                    "sales_price": self._safe_get_nested(
                        property_read, "fi_realty", "selling_price"
                    ),
                },
            }

            html_content = self._renderer.render_template(
                TemplatesEnum.BROCHURE_PROPERTY_INFORMATION, template_data
            )

            generated_pdf = self._renderer.generate_pdf(
                html_content, TemplatesEnum.BASE_TEXT
            )

            return generated_pdf

        except Exception as e:
            logger.error(f"Error generating property information PDF: {e}")
            raise ValueError(f"Error generating property information PDF: {e}")

    def _generate_short_intro_pdf(
        self,
        property_id: int,
        realtor: User,
        property_information_data: BrochureInformationCreate,
    ) -> bytes:
        property = self._db_session.scalars(
            select(FIProperty).where(FIProperty.id == property_id)
        ).one_or_none()

        if not property:
            raise PropertyNotFoundError(property_id)

        property_read = FIPropertyRead.model_validate(property)

        template_data = {
            "title": "Tähän tulee kiinteistön osoite",
            "subtitle": "Tähän kuvaus kiinteistön tyypistä ja huoneista",
            "price_area_year": f"{self._safe_get_nested(property_read, 'fi_realty', 'selling_price')} € • {self._safe_get_nested(property_read, 'fi_residential_share_overview', 'apartment', 'area', 'living_area', 'value')} m² / {self._safe_get_nested(property_read, 'fi_residential_share_overview', 'apartment', 'area', 'total_area', 'value')} m² • {self._safe_get_nested(property_read, 'fi_housing_company', 'construction', 'construction_year')}",
            "descriptions": (
                [{"description": property_information_data.property_description}]
                if property_information_data
                and property_information_data.property_description
                else property_read.descriptions
            ),
            "realtor": {
                "name": f"{realtor.first_name} {realtor.last_name}",
                "title": self._safe_get_nested(realtor, "details", "position"),
                "team_name": realtor.team_name or "",
                "phone": realtor.phone_number,
                "email": realtor.email,
                "business_name": self._safe_get_nested(
                    realtor, "details", "company", "company_name"
                ),
                "business_number": self._safe_get_nested(
                    realtor, "details", "company", "company_id"
                ),
                "image": f"{realtor.photo_url}?width=300&height=300&grayscale",
            },
        }

        html_content = self._renderer.render_template(
            TemplatesEnum.BROCHURE_SHORT_INTRO, template_data, auto_inject_images=True
        )
        generated_pdf = self._renderer.generate_pdf(
            html_content, TemplatesEnum.BASE_VISUAL
        )
        return generated_pdf

    def _generate_version_number_stamp_pdf(
        self, version_number: int, last_version_creation_date: datetime = None
    ) -> bytes:
        template_data = {
            "date": datetime.now().strftime("%d.%m.%Y"),
            "version_number": version_number,
            "creation_date": (
                last_version_creation_date.strftime("%d.%m.%Y")
                if last_version_creation_date
                else datetime.now().strftime("%d.%m.%Y")
            ),
        }
        html_content = self._renderer.render_template(
            TemplatesEnum.VERSION_NUMBER_STAMP, template_data
        )
        return self._renderer.generate_pdf(html_content, TemplatesEnum.BASE_TEXT)

    def _safe_get_nested(self, obj, *keys, default=""):
        try:
            for key in keys:
                obj = obj.get(key) if isinstance(obj, dict) else getattr(obj, key, None)
                if obj is None:
                    return default
            return obj or default
        except (AttributeError, TypeError):
            return default
