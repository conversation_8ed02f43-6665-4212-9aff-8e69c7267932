"""
Utility functions for brochure service
"""

from typing import Optional


def validate_image_url(image_url: str) -> bool:
    """
    Validate if an image URL is valid
    """
    return image_url and (
        image_url.startswith("http://")
        or image_url.startswith("https://")
        or image_url.startswith("data:image/")
    )


def format_price(price: Optional[float]) -> str:
    """
    Format a price value with proper formatting
    """
    if price is None:
        return ""

    return f"{price:,.2f} €".replace(",", " ")


def format_address(
    street_address: Optional[str] = None,
    postal_code: Optional[str] = None,
    municipality: Optional[str] = None,
    district: Optional[str] = None,
) -> str:
    """
    Format an address for display
    """
    parts = []

    if street_address:
        parts.append(street_address)

    location_part = ""
    if postal_code and municipality:
        location_part = f"{postal_code} {municipality}"
    elif postal_code:
        location_part = postal_code
    elif municipality:
        location_part = municipality

    if location_part:
        parts.append(location_part)

    if district and district not in parts:
        parts.append(district)

    return ", ".join(parts)
