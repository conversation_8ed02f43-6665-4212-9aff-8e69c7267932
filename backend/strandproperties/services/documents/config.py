import os
from enum import Enum

from strandproperties.models.brochure import CoverTheme

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TEMPLATES_DIR = os.path.join(BASE_DIR, "templates")
FONTS_DIR = os.path.join(BASE_DIR, "fonts")
IMAGES_DIR = os.path.join(BASE_DIR, "images")
PDFS_DIR = os.path.join(BASE_DIR, "pdfs")


class TemplatesEnum(str, Enum):
    SALES_AGREEMENT = "sales_agreement.html"
    PURCHASE_OFFER = "purchase_offer.html"
    COUNTER_OFFER = "counter_offer.html"
    FRONT_COVER = "front-cover.html"
    BASE_TEXT = "base_text.html"
    BASE_VISUAL = "base_visual.html"
    BROKERAGE_OFFER = "brokerage_offer_template.html"
    TRANSFER_TAX_RETURNS = "transfer_tax_returns.html"
    BROCHURE = "brochure_template.html"
    BROCHURE_PROPERTY_IMAGES = "brochure_property_images_template.html"
    BROCHURE_PROPERTY_INFORMATION = "brochure_property_information_template.html"
    BROCHURE_SHORT_INTRO = "brochure_short_intro_template.html"
    BROCHURE_PROPERTY_BLUEPRINT_IMAGE = (
        "brochure_property_blueprint_image_template.html"
    )
    VERSION_NUMBER_STAMP = "version_number_stamp_template.html"
    CANCELLATION_FORM = "cancellation_form.html"


class BrokerageOfferImagesEnum(str, Enum):
    BIG_IMAGE = "brokerage_offer_big_image.jpeg"
    SMALL_IMAGE_SIDE = "brokerage_offer_small_image_side.jpeg"
    BIG_IMAGE_SIDE = "brokerage_offer_big_image_side.png"
    BACKGROUND_IMAGE = "brokerage_offer_bg_image.jpeg"


COVER_THEME_IMAGES = {
    CoverTheme.FLOWER: {
        "image": "covers/cover_image_flower.jpg",
    },
    CoverTheme.LIVINGROOM: {
        "image": "covers/cover_image_living_room.jpg",
    },
    CoverTheme.LIVINGROOM2: {
        "image": "covers/cover_image_living_room_2.jpg",
    },
    CoverTheme.OLIVE: {
        "image": "covers/cover_image_olive.jpg",
    },
}


TEMPLATE_IMAGES = {
    TemplatesEnum.BROKERAGE_OFFER: {
        "big_image": BrokerageOfferImagesEnum.BIG_IMAGE.value,
        "small_image_side": BrokerageOfferImagesEnum.SMALL_IMAGE_SIDE.value,
        "big_image_side": BrokerageOfferImagesEnum.BIG_IMAGE_SIDE.value,
        "background_image": BrokerageOfferImagesEnum.BACKGROUND_IMAGE.value,
        "logo": "strand_properties_logo_black.svg",
    },
    TemplatesEnum.BROCHURE_PROPERTY_IMAGES: {},
    TemplatesEnum.BROCHURE_PROPERTY_BLUEPRINT_IMAGE: {},
    TemplatesEnum.BROCHURE_SHORT_INTRO: {},
    TemplatesEnum.FRONT_COVER: {
        "logo": "strand_properties_logo_black.svg",
    },
}
