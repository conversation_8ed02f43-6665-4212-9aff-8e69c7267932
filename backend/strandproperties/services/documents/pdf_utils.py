from io import Bytes<PERSON>

from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter


def combine_pdfs(pdf_files: list[bytes], add_empty_pages: bool = False) -> bytes:
    """
    Combine multiple PDF files (provided as bytes) into a single PDF.

    Args:
        pdf_files: List of PDF files as bytes objects

    Returns:
        Combined PDF as bytes
        Amount of pages in the combined PDF

    Raises:
        ValueError: If any of the input files is not a valid PDF
        RuntimeError: If PDF combination fails
    """
    if not pdf_files:
        raise ValueError("At least one PDF file must be provided")

    output_buffer = BytesIO()
    writer = PdfWriter()

    try:
        for pdf_bytes in pdf_files:
            if not pdf_bytes:
                continue

            reader = PdfReader(BytesIO(pdf_bytes))

            for page in reader.pages:
                writer.add_page(page)

    except Exception as e:
        raise RuntimeError(f"Failed to combine PDFs: {str(e)}") from e

    pages_amount = len(writer.pages)
    pages_to_add = _calculate_pages_to_add(pages_amount)

    if add_empty_pages and pages_to_add > 0:
        try:
            for _ in range(pages_to_add):
                writer.insert_blank_page(index=(len(writer.pages) - 1))
        except Exception as e:
            raise RuntimeError(f"Failed to add empty pages: {str(e)}") from e

    writer.write(output_buffer)
    output_buffer.seek(0)

    return output_buffer.getvalue(), len(writer.pages)


def _calculate_pages_to_add(pages_amount: int) -> int:
    return (4 - (pages_amount % 4)) % 4


def add_stamp_to_pdf(pdf_bytes: bytes, stamp_bytes: bytes) -> bytes:
    stamp = PdfReader(BytesIO(stamp_bytes)).pages[0]
    writer = PdfWriter(clone_from=BytesIO(pdf_bytes))

    for page in writer.pages:
        page.merge_page(stamp, over=True)

    output_buffer = BytesIO()
    writer.write(output_buffer)
    output_buffer.seek(0)
    return output_buffer.read()
