<style>
  .property-info h1 {
    text-align: center;
    font-size: 20px;
  }
  .property-info h3 {
    text-align: center;
    font-size: 18px;
  }

  .property-info p {
    font-size: 16px;
  }

  .page-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 50mm;
    left: 20mm;
    right: 20mm;
  }

  .page-footer .profile-picture {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-color: grey;
    margin-right: 30px;
    flex-shrink: 0;
  }

  .page-footer .profile-text {
    text-align: left;
    line-height: 0.6;
  }
</style>

<section>
  <section class="page">
    <div class="property-info">
      <h1>{{title}}</h1>
      <h3>{{subtitle}}</h3>
      <br />
      <h3>{{price_area_year}}</h3>
      <br />
      {% for description in descriptions %}
      <p>{{description.description}}</p>
      {% endfor %}
    </div>
    <div class="page-footer">
      <div
        class="profile-picture"
        style="background-image: url('{{realtor.image}}');"
      ></div>
      <div class="profile-text">
        <div class="name">{{ realtor.name }}</div>
        <div class="title">
          <p>{{ realtor.title}}</p>
          <p>{{ realtor.team_name}}</p>
        </div>
        <div class="details">
          <p>{{ [realtor.phone, realtor.email] | select | join(' | ') }}</p>
          <p>
            {{ [realtor.business_name, realtor.business_number] | select |
            join(' | ') }}
          </p>
        </div>
      </div>
    </div>
  </section>
</section>
