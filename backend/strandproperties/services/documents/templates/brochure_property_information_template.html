<!-- prettier-ignore -->
<style>

  .property-information-key {
    font-weight: bold;
  }

  .property-information-value {
    font-weight: normal;
  }

  .realtor-image {
    filter: grayscale(100%);
  }
</style>

<section class="property-information">
  <h1>{{title}}</h1>
  <div class="property-section">
    <h2>Building</h2>
    <table>
      {% for key, value in building.items() %} {% if value %}
      <tr>
        <td>{{key}}</td>
        <td>{{value}}</td>
      </tr>
      {% endif %} {% endfor %}
    </table>
    <h2>Price</h2>
    <table>
      {% for key, value in price.items() %} {% if value %}
      <tr>
        <td>{{key}}</td>
        <td>{{value}}</td>
      </tr>
      {% endif %} {% endfor %}
    </table>
  </div>
</section>
