<!-- prettier-ignore -->
{% extends "base_text.html" %}

{% block title %}Strand Properties Brochure{% endblock %}

{% block additional_styles %}
@page{
  size: A4;
  margin: 0;
  padding: 0;
}

body {
  width: 210mm;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 20mm;
}
.cover-logo-below-image {
  position: absolute;
  bottom: 50mm;
  left: 0;
  right: 0;
}
.cover-logo-below-image img {
  max-width: 450px;
  max-height: 130px;
  width: auto;
  height: auto;
}
.sub-header.below-image {
  bottom: 40mm;
}
.page {
  width: 210mm;
  height: 297mm;
  padding: 15mm;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page-footer {
  font-family: "AvenirRoman", sans-serif;
  position: absolute;
  text-align: center;
  bottom: 50mm;
  left: 20mm;
  right: 20mm;
}
.page-footer .title {
  font-size: 9pt;
  font-family: "SangBleuRepublic", sans-serif;
  font-style: italic;
  padding-bottom: 10px;
}
.page-footer .name {
  font-size: 12pt;
  text-transform: uppercase;
  font-family: "AvenirHeavy", sans-serif;
  font-weight: 500;
  padding-bottom: 10px;
  letter-spacing: 1.2px;
}
.page-footer .details {
  font-family: "SangBleuRepublic", sans-serif;
  font-size: 11pt;
  padding-bottom: 12px;
}
.page-footer .details .separator {
  margin: 0 12px;
}
.page-footer .business-info {
  font-size: 8pt;
  color: #555;
  letter-spacing: 1px;
  font-family: sans-serif;
}

/* 1st page */
.logo-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.logo-container img {
  max-width: 300px;
  max-height: 150px;
}

/* 3rd page */
.realtor-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}
.realtor-profile .background-image {
  width: 100%;
  height: 450px;
  background-size: cover;
  background-position: center;
  background-color: darkgray;
}
.realtor-profile .profile-picture {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  margin-top: -125px;
  position: relative;
  z-index: 1;
  background-size: cover;
  background-position: center;
  background-color: grey;
}
.realtor-profile .description {
  font-family: "SangBleuRepublic", sans-serif;
  margin-top: 20px;
  max-width: 550px;
  font-size: 14pt;
  line-height: 1.8;
}

/* 4th & 5th page */
.strand-properties-marketing {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.strand-properties-marketing .top-image {
  width: 100%;
  height: 720px;
  background-size: cover;
  background-position: center;
  background-color: darkgray;
  margin-bottom: 40px;
}
.content-wrapper {
  display: table;
  width: 100%;
}
.text-content {
  font-family: "SangBleuRepublic", sans-serif;
  display: table-cell;
  width: 40%;
  padding-right: 40px;
  vertical-align: top;
  overflow-wrap: break-word;
  hyphens: auto;
}
.img-content {
  display: table-cell;
  width: 50%;
  vertical-align: top;
}
.text-content h2 {
  font-size: 16pt;
  font-weight: 600;
  margin-bottom: 15px;
  line-height: 1.3;
}
.text-content p {
  font-size: 9pt;
  line-height: 1.8;
  letter-spacing: 1px;
  font-family: "AvenirRoman", sans-serif;
}
.img-content .small-image {
  width: 100%;
  height: 250px;
  background-size: cover;
  background-color: grey;
}
.img-content .big-image {
  width: 100%;
  height: 980px;
  background-color: grey;
  background-size: cover;
  background-position: center;
}

/* 6th page */
.offer-details {
  width: 100%;
}
.offer-details h1 {
  font-size: 22pt;
  font-family: "SangBleuRepublic", sans-serif;
  text-align: center;
  margin-bottom: 80px;
}
.offer-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14pt;
}
.offer-table tr {
  border-bottom: 1px solid #ccc;
}
.offer-table td {
  padding: 20px 0;
  font-family: sans-serif;
}
.offer-table td:first-child {
  width: 50%;
  font-weight: 600;
  font-family: "SangBleuRepublic", sans-serif;
}
.offer-table h2 {
  font-size: 14pt;
}
.notes {
  padding-top: 20px;
}
.notes h2 {
  font-family: "SangBleuRepublic", sans-serif;
  font-size: 14pt;
}
.notes p {
  font-size: 12pt;
  line-height: 1.6;
  letter-spacing: 0.2px;
  font-family: sans-serif;
}

/* 8th page */
.signature {
  position: absolute;
  bottom: 60mm;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.signature img {
  max-width: 180px;
  max-height: 50px;
  width: auto;
  height: auto;
}
{% endblock %}
