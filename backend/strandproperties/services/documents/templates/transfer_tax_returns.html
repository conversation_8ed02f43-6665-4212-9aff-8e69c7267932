<html>
  <head>
    <style>
      @font-face {
        font-family: "AvenirRoman";
        src: url("{{ font_url_roman }}") format("woff");
      }

      @page {
        size: A4;
        margin: 2cm;
        margin-top: 2cm;
        margin-bottom: 2cm;
        margin-left: 2cm;
        margin-right: 2cm;
      }

      @page {
        @bottom-center {
          content: counter(page) " / " counter(pages);
        }
      }

      body {
        font-family: "AvenirRoman", sans-serif;
        font-size: 12px;
      }

      h1, h2 {
        margin-top: 16px;
        margin-bottom: 8px;
      }

      h1 {
        font-size: 18px;
      }

      h2 {
        font-size: 16px;
      }

      table {
        width: 100%;
        page-break-inside: avoid;
        border: 1px solid #cccccc;
      }

      table td {
        padding: 8px;
        vertical-align: top;
      }

      table td:first-child {
        width: 70%;
        font-weight: bold;
      }

      table td:last-child {
        width: 30%;
      }
    </style>
  </head>
  <body>
    {% macro print_title(title, level=0) %}
      <h{{ level + 1 }}>{{ title }}</h{{ level + 1 }}>
    {% endmacro %}

    {% macro print_section(section, level=0) %}
      {% if section.title %}
        {{ print_title(section.title, level) }}
      {% endif %}

      {% if section.content %}
        <table>
          {% for key, value in section.content.items() %}
            <tr>
              <td>{{ key }}</td>
              <td>{{ value }}</td>
            </tr>
          {% endfor %}
        </table>
      {% endif %}

      {% if section.subsections %}
        {% for subsection in section.subsections %}
          {{ print_section(subsection, level + 1) }}
        {% endfor %}
      {% endif %}
    {% endmacro %}

    <section>
      {% for section in sections %}
        {{ print_section(section) }}
      {% endfor %}
    </section>
  </body>
</html>
