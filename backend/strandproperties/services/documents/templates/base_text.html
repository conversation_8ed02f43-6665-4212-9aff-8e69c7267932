<!DOCTYPE html>
<html>
  <head>
    <title>
      {% block title %}Strand Properties Sales Agreement{% endblock %}
    </title>
    <style>
      @font-face {
        font-family: "SangBleuRepublic";
        src: url("{{ font_url_sangbleurepublic }}") format("woff");
      }
      @font-face {
        font-family: "SangBleuRepublic";
        src: url("{{ font_url_sangbleurepublic_italic }}") format("woff");
        font-style: italic;
      }
      @font-face {
        font-family: "AvenirHeavy";
        src: url("{{ font_url_heavy }}") format("woff");
      }
      @font-face {
        font-family: "AvenirRoman";
        src: url("{{ font_url_roman }}") format("woff");
      }
      body {
        font-family: "AvenirRoman", sans-serif;
        font-size: 12px;
      }
      .cover {
        font-family: "SangBleuRepublic", sans-serif;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 297mm;
        text-align: center;
      }
      .header {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .header img {
        max-width: 450px;
        max-height: 130px;
        width: auto;
        height: auto;
      }
      .sub-header {
        font-family: "AvenirHeavy",sans-serif;
        text-transform: uppercase;
        font-size: 11pt;
        font-weight: 600;
        position: absolute;
        bottom: 20mm;
        left: 0;
        right: 0;
        letter-spacing: 1px;
      }

      .content {
        margin-top: 20px;
        text-align: center;
      }
      .content p {
        margin-bottom: 10px;
      }
      .content table {
        width: 100%;
        border-collapse: collapse;
      }
      .content table th,
      .content table td {
        border: 1px solid #000;
        padding: 5px;
      }
      .content table th {
        background-color: #f0f0f0;
      }
      .footer {
        margin-top: 20px;
        text-align: center;
      }

      /* Ensure each section starts on a new page */
      section {
        page-break-before: always;
      }
      h1,
      h2,
      h3 {
        color: #333;
      }

      .highlight-green {
        color: green;
      }

      .highlight-red {
        color: red;
      }

      .highlight-black {
        color: black;
      }

      .contract-section {
        font-family: Arial, sans-serif;
        font-size: 12px;
      }

      {% block additional_styles %}{% endblock %}
    </style>
  </head>
  <body>
    {{content}}
  </body>
</html>
