import re

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from strandproperties.config import app_cfg
from strandproperties.constants import (
    DocumentSignatureStatus,
    EventLogAction,
    EventLogActorType,
    EventLogObjectType,
    FIPurchaseOfferStatusEnum,
    FISalesAgreementStatusEnum,
)
from strandproperties.models.contact import Contact
from strandproperties.models.document_signing import (
    DocumentEvent,
    DocumentSigner,
    DocumentSigning,
    DocumentSigningEntity,
    DocumentSigningEntityType,
)
from strandproperties.models.fi_counter_offer import FICounterOffer
from strandproperties.models.fi_purchase_offer import FIPurchaseOffer
from strandproperties.models.fi_sales_agreement import FISalesAgreement
from strandproperties.models.user import User
from strandproperties.services.dokobit_service import DokobitService
from strandproperties.utils.event_log import create_event_log
from strandproperties.utils.validation import is_valid_finnish_ssn
from strandproperties.views.exceptions import ApiError
from strandproperties.schemas.common import Error<PERSON><PERSON><PERSON>


def parse_external_id(external_id: str):
    """
    Parses an external_id string in the format "{user-type}-{user-id}".

    Args:
        external_id (str): The external ID string from the Dokobit response.

    Returns:
        tuple: (user_type, user_id) if valid, otherwise ("unknown", None).
    """
    match = re.match(r"^([a-zA-Z]+)-(\d+)$", external_id)
    if match:
        return match.group(1), int(match.group(2))  # Convert user_id to integer
    return "unknown", None  # Default values if parsing fails


def create_document_signing(
    db_session,
    actor_id,
    entity_type,
    entity_ids,
    document_external_id,
    saved_signers,
    deadline,
):
    """
    Create a new document signing and document signers

    Args:
        db_session (Session): SQLAlchemy database session.
        actor_id (int): The ID of the user creating the document signing.
        entity_type (str): Type of document (e.g., "sales_agreement", "purchase_offer", "document_library").
        entity_ids (List[int]): IDs of the entities being signed.
        document_external_id (str): External ID from the document provider.
        signers (List[dict]): List of signers with `{external_id, token}`.

    Returns:
        DocumentSigning: The created DocumentSigning record.

    Raises:
        ApiError: If database saving fails.
    """
    try:
        document_signing = DocumentSigning(
            document_external_id=document_external_id,
            status="created",
            deadline=deadline,
        )
        db_session.add(document_signing)
        db_session.flush()

        # Add entities to DocumentSigningEntity
        for entity_id in entity_ids:
            document_signing_entity = DocumentSigningEntity(
                document_signing_id=document_signing.id,
                entity_id=entity_id,
                entity_type=entity_type,
            )
            db_session.add(document_signing_entity)
        db_session.flush()

        for signer in saved_signers:
            external_id = signer.get("external_id", "")
            user_type, user_id = parse_external_id(external_id)

            document_signer = DocumentSigner(
                document_signing_id=document_signing.id,
                signing_external_id=signer["token"],
                user_type=user_type,
                user_id=user_id,
                status="pending",
            )
            db_session.add(document_signer)
        db_session.flush()

        create_event_log(
            db_session=db_session,
            object_type=EventLogObjectType.DOCUMENT_SIGNING,
            object_id=document_signing.id,
            actor_id=actor_id,
            action=EventLogAction.CREATED,
            data_after={
                "entity_type": entity_type,
                "entity_ids": entity_ids,
                "deadline": deadline,
            },
        )
        return document_signing

    except SQLAlchemyError:
        db_session.rollback()
        raise ApiError("Database error while saving signing request", status=500)


def update_signing_status(db_session, document_external_id, new_status, signers):
    """
    Updates the signing status and signer statuses in the database.
    Also updates the parent entity to completed if all signers have signed

    Args:
        db_session (Session): SQLAlchemy database session.
        document_external_id (str): The external document ID from the signing provider.
        new_status (str): The updated signing status.
        signers_data (List[dict]): List of updated signer statuses {token, status}.

    Raises:
        ApiError: If the document signing is not found or the update fails.
    """
    try:
        document_signing = db_session.scalars(
            select(DocumentSigning).where(
                DocumentSigning.document_external_id == document_external_id
            )
        ).one_or_none()

        if not document_signing:
            raise ApiError(
                f"DocumentSigning with external ID {document_external_id} not found",
                status=404,
            )

        document_signing.status = new_status
        db_session.flush()

        for signer_data in signers:
            signer = db_session.scalars(
                select(DocumentSigner).where(
                    DocumentSigner.signing_external_id == signer_data["token"]
                )
            ).one_or_none()

            if signer:
                signer.status = signer_data.get("status")

        db_session.flush()

        if new_status in ("completed", "declined", "cancelled"):
            # Update related entities based on signing outcome
            entities = db_session.scalars(
                select(DocumentSigningEntity).where(
                    DocumentSigningEntity.document_signing_id == document_signing.id
                )
            ).all()

            _apply_status_to_entities(
                db_session=db_session,
                entities=entities,
                new_status=new_status,
                document_signing_id=document_signing.id,
            )

            db_session.flush()

    except SQLAlchemyError:
        db_session.rollback()
        raise ApiError("Database error while updating signing status", status=500)


def create_document_event(
    db_session,
    document_external_id: str,
    action: str,
    status: str,
    raw_payload: dict,
):
    """
    Persists a new event from a webhook into the DocumentEvent table.

    Args:
        db_session: SQLAlchemy session.
        document_external_id (str): External ID of the document.
        action (str): Event action (e.g. signer_signed, signing_completed).
        status (str): Current status of the document (pending, completed, etc.).
        raw_payload (dict): Full webhook payload for auditing.

    Raises:
        ApiError: If signing not found or DB error occurs.
    """
    try:
        signing = db_session.scalars(
            select(DocumentSigning).where(
                DocumentSigning.document_external_id == document_external_id
            )
        ).one_or_none()

        if not signing:
            raise ApiError(
                f"Document signing not found for token: {document_external_id}",
                status=404,
            )

        event = DocumentEvent(
            document_signing_id=signing.id,
            action=action,
            status=status,
            raw_payload=raw_payload,
        )

        db_session.add(event)
        db_session.flush()

    except SQLAlchemyError as e:
        db_session.rollback()
        raise ApiError("Failed to save document event", status=500)


def fetch_signers(db_session, signers, language, require_ssn: bool = False):
    """
    Fetches user/contact details and ensures data integrity before returning formatted signers.

    Args:
        db_session (Session): SQLAlchemy session.
        signers (List[dict]): List of signers with `{id, type, role}`.

    Returns:
        List[dict]: Formatted list of signers with enriched details.

    Raises:
        ApiError: If first name or last name is missing or empty.
    """
    user_ids = [s["id"] for s in signers if s["type"] == "user"]
    contact_ids = [s["id"] for s in signers if s["type"] == "contact"]

    users = {
        u.id: u
        for u in db_session.execute(select(User).where(User.id.in_(user_ids)))
        .scalars()
        .all()
    }

    contacts = {
        c.id: c
        for c in db_session.execute(select(Contact).where(Contact.id.in_(contact_ids)))
        .scalars()
        .all()
    }

    lang = language or "fi"

    formatted_signers = []
    missing_ssn_info = []
    for signer in signers:
        details = None

        if signer["type"] == "user":
            user = users.get(signer["id"])
            if user:
                if not user.first_name or not user.last_name:
                    raise ApiError(
                        f"User {user.id} is missing a valid first name or last name",
                        status=400,
                        label=ErrorLabel.CREATE_DOCUMENT_SIGNING_USER_MISSING_NAME,
                    )
                if not app_cfg.is_dokobit_allowed_email(user.email):
                    continue

                ssn_value = user.social_security_number
                if not ssn_value and isinstance(user.details, dict):
                    ssn_value = user.details.get("social_security_number")

                if ssn_value:
                    if not is_valid_finnish_ssn(ssn_value):
                        ssn_value = None

                if require_ssn and signer["role"] == "signer" and not ssn_value:
                    missing_ssn_info.append(
                        {
                            "id": user.id,
                            "type": "user",
                            "email": user.email,
                        }
                    )
                    continue

                details = {
                    "external_id": f"user-{user.id}",
                    "email": user.email,
                    "name": user.first_name.strip(),
                    "surname": user.last_name.strip(),
                    "country_code": "FI",
                    "role": signer["role"],
                    "notifications_language": lang,
                    "annotation": {
                        "page": 1,
                        "top": 1,
                        "left": 1,
                        "language": lang,
                    },
                }
                if ssn_value:
                    details["code"] = ssn_value.strip()

        elif signer["type"] == "contact":
            contact = contacts.get(signer["id"])
            if contact:
                if not contact.name:
                    raise ApiError(
                        f"Contact {contact.id} is missing a valid name",
                        status=400,
                        label=ErrorLabel.CREATE_DOCUMENT_SIGNING_CONTACT_MISSING_NAME,
                    )
                if not app_cfg.is_dokobit_allowed_email(contact.email):
                    continue

                ssn_value = contact.social_security_number

                if ssn_value:
                    if not is_valid_finnish_ssn(ssn_value):
                        ssn_value = None

                if require_ssn and signer["role"] == "signer" and not ssn_value:
                    missing_ssn_info.append(
                        {
                            "id": contact.id,
                            "type": "contact",
                            "email": contact.email,
                        }
                    )
                    continue

                details = {
                    "external_id": f"contact-{contact.id}",
                    "email": contact.email,
                    "name": contact.name.strip(),
                    "surname": contact.name.strip(),
                    "country_code": "FI",
                    "role": signer["role"],
                    "notifications_language": lang,
                    "annotation": {
                        "page": 1,
                        "top": 1,
                        "left": 1,
                        "language": lang,
                    },
                }
                if ssn_value:
                    details["code"] = ssn_value.strip()

        if details:
            formatted_signers.append(details)

    if require_ssn and missing_ssn_info:
        signer_emails = ", ".join(s["email"] for s in missing_ssn_info)
        message = f"Social security number is missing or invalid for: {signer_emails}."
        raise ApiError(
            message,
            status=400,
            label=ErrorLabel.CREATE_DOCUMENT_SIGNING_SSN_INVALID_OR_MISSING,
        )

    return formatted_signers


def add_signer_to_document_signing(
    db_session,
    actor_id: int,
    document_signing_id: int,
    signer_data: dict,
    language: str = "fi",
) -> dict:
    """
    Adds a signer to an existing document signing in both database and Dokobit.

    Args:
        db_session (Session): SQLAlchemy database session.
        actor_id (int): The ID of the user adding the signer.
        document_signing_id (int): The document signing ID.
        signer_data (dict): Signer data with keys:
        language (str): Language for notifications (default: "fi").
        comment (str, optional): Message for the signer.

    Returns:
        dict: Response from Dokobit API containing signer information.

    Raises:
        ApiError: If document signing not found, signer validation fails, or API calls fail.
    """
    try:
        document_signing = db_session.scalars(
            select(DocumentSigning).where(DocumentSigning.id == document_signing_id)
        ).one_or_none()

        if not document_signing:
            raise ApiError(
                f"Document signing with ID {document_signing_id} not found",
                status=404,
                label=ErrorLabel.ADD_SIGNER_DOCUMENT_SIGNING_NOT_FOUND,
            )

        if document_signing.status in ("completed", "cancelled", "declined"):
            raise ApiError(
                "Cannot add signers to a completed or cancelled signing process",
                status=400,
                label=ErrorLabel.ADD_SIGNER_DOCUMENT_SIGNING_NOT_ALLOWED_STATUS,
            )

        existing_signer = db_session.scalars(
            select(DocumentSigner).where(
                DocumentSigner.document_signing_id == document_signing_id,
                DocumentSigner.user_id == signer_data["id"],
                DocumentSigner.user_type == signer_data["type"],
            )
        ).first()

        if existing_signer:
            raise ApiError(
                f"Signer with ID {signer_data['id']} and type {signer_data['type']} is already added to this document signing",
                status=400,
                label=ErrorLabel.ADD_SIGNER_SIGNER_ALREADY_ADDED,
            )

        signer_input = [
            {
                "id": signer_data["id"],
                "type": signer_data["type"],
                "role": signer_data.get("role", "signer"),
            }
        ]
        formatted_signers = fetch_signers(
            db_session=db_session,
            signers=signer_input,
            language=language,
            require_ssn=True,
        )

        if not formatted_signers:
            raise ApiError(
                "Unable to create signer - user/contact not found or invalid email domain",
                status=400,
            )

        formatted_signer = formatted_signers[0]

        dokobit_service = DokobitService()
        dokobit_response = dokobit_service.add_signer(
            token=document_signing.document_external_id,
            signers=formatted_signers,
        )

        if (
            dokobit_response.get("status") == "ok"
            and "signers" in dokobit_response
            and dokobit_response["signers"]
        ):
            expected_external_id = formatted_signer["external_id"]
            added_signer = None

            for signer in dokobit_response["signers"]:
                if signer.get("external_id") == expected_external_id:
                    added_signer = signer
                    break

            if not added_signer:
                raise ApiError(
                    f"Could not find added signer with external_id {expected_external_id} in Dokobit response",
                    status=500,
                )

            signer_token = added_signer.get("token")
            if not signer_token:
                raise ApiError(
                    "Failed to get signer token from Dokobit response", status=500
                )

            user_type, user_id = parse_external_id(formatted_signer["external_id"])

            document_signer = DocumentSigner(
                document_signing_id=document_signing.id,
                signing_external_id=signer_token,
                user_type=user_type,
                user_id=user_id,
                status="pending",
            )

            db_session.add(document_signer)
            db_session.flush()

            create_event_log(
                db_session=db_session,
                object_type=EventLogObjectType.DOCUMENT_SIGNING,
                object_id=document_signing.id,
                actor_id=actor_id,
                action=EventLogAction.ADDED_SIGNER,
                data_after={
                    "id": document_signer.id,
                    "user_id": document_signer.user_id,
                    "user_type": document_signer.user_type,
                    "status": document_signer.status,
                },
            )

            return dokobit_response
        else:
            raise ApiError(
                "Failed to add signer to Dokobit - invalid response", status=500
            )

    except SQLAlchemyError as e:
        db_session.rollback()
        raise ApiError("Database error while adding signer", status=500)
    except Exception as e:
        db_session.rollback()
        if isinstance(e, ApiError):
            raise
        raise ApiError(f"Unexpected error while adding signer: {str(e)}", status=500)


def process_dokobit_create_response(
    db_session, actor_id, entity_type, entity_ids, response, deadline
):
    status = response.get("status")
    document_external_id = response.get("token")
    saved_signers = response.get("signers")
    if status != "ok" or not document_external_id:
        raise ApiError("Failed to retrieve signing details from Dokobit", status=500)

    return create_document_signing(
        db_session,
        actor_id,
        entity_type,
        entity_ids,
        document_external_id,
        saved_signers,
        deadline,
    )


def delete_signer_from_document_signing(
    db_session,
    actor_id: int,
    document_signing_id: int,
    signer_id: int,
) -> dict:
    """
    Deletes a signer from an existing document signing in both database and Dokobit.

    Args:
        db_session (Session): SQLAlchemy database session.
        actor_id (int): The ID of the user deleting the signer.
        document_signing_id (int): The document signing ID.
        signer_id (int): The signer's user or contact ID.

    Returns:
        dict: Response from Dokobit API.

    Raises:
        ApiError: If document signing not found, signer not found, or API calls fail.
    """
    try:
        document_signing = db_session.scalars(
            select(DocumentSigning).where(DocumentSigning.id == document_signing_id)
        ).one_or_none()

        if not document_signing:
            raise ApiError(
                f"Document signing with ID {document_signing_id} not found",
                status=404,
            )

        if document_signing.status in ("completed", "cancelled", "declined"):
            raise ApiError(
                "Cannot delete signers from a completed or cancelled signing process",
                status=400,
                label=ErrorLabel.DELETE_DOCUMENT_SIGNER_NOT_ALLOWED_STATUS,
            )

        signer_to_delete = db_session.scalars(
            select(DocumentSigner).where(
                DocumentSigner.document_signing_id == document_signing_id,
                DocumentSigner.id == signer_id,
            )
        ).first()

        if not signer_to_delete:
            raise ApiError(
                f"Signer with ID {signer_id} not found in this document signing",
                status=404,
                label=ErrorLabel.DELETE_DOCUMENT_SIGNER_NOT_FOUND,
            )

        if signer_to_delete.status != DocumentSignatureStatus.PENDING:
            raise ApiError(
                "Cannot delete a signer that is not pending",
                status=400,
                label=ErrorLabel.DELETE_DOCUMENT_SIGNER_NOT_ALLOWED_STATUS,
            )

        dokobit_service = DokobitService()
        dokobit_response = dokobit_service.delete_signer(
            token=document_signing.document_external_id,
            signer_tokens=[signer_to_delete.signing_external_id],
        )

        if dokobit_response.get("status") == "ok":
            db_session.delete(signer_to_delete)
            db_session.flush()
            create_event_log(
                db_session=db_session,
                object_type=EventLogObjectType.DOCUMENT_SIGNING,
                object_id=document_signing.id,
                actor_id=actor_id,
                action=EventLogAction.DELETED_SIGNER,
                data_before={
                    "id": signer_to_delete.id,
                    "user_id": signer_to_delete.user_id,
                    "user_type": signer_to_delete.user_type,
                    "status": signer_to_delete.status,
                },
            )
            return dokobit_response
        else:
            raise ApiError(
                "Failed to delete signer from Dokobit - invalid response", status=500
            )

    except SQLAlchemyError as e:
        db_session.rollback()
        raise ApiError("Database error while deleting signer", status=500)
    except Exception as e:
        db_session.rollback()
        if isinstance(e, ApiError):
            raise
        raise ApiError(f"Unexpected error while deleting signer: {str(e)}", status=500)


def delete_document_signing(
    db_session,
    actor_id: int,
    document_signing_id: int,
) -> dict:
    """
    Deletes a document signing in both database and Dokobit.
    """

    try:
        document_signing = db_session.scalars(
            select(DocumentSigning).where(DocumentSigning.id == document_signing_id)
        ).one_or_none()

        if not document_signing:
            raise ApiError(
                f"Document signing with ID {document_signing_id} not found",
                status=404,
            )

        if document_signing.status in ("completed", "cancelled", "declined"):
            raise ApiError(
                "Cannot delete a completed or cancelled document signing",
                status=400,
            )

        if any(
            signer.status != DocumentSignatureStatus.PENDING
            for signer in document_signing.signers
        ):
            raise ApiError(
                "Cannot delete a document signing with signers that are not pending",
                status=400,
            )

        # This will raise an ApiError if the document signing entities are not compatible with deletion
        _validate_document_signing_entities_for_deletion(
            db_session, actor_id, document_signing.id
        )

        dokobit_service = DokobitService()
        dokobit_response = dokobit_service.delete_signing(
            token=document_signing.document_external_id,
        )

        _apply_status_to_entities(
            db_session, document_signing.entities, "cancelled", document_signing.id
        )
        db_session.delete(document_signing)
        db_session.flush()
        create_event_log(
            db_session=db_session,
            object_type=EventLogObjectType.DOCUMENT_SIGNING,
            object_id=document_signing.id,
            actor_id=actor_id,
            action=EventLogAction.DELETED,
        )
        return dokobit_response

    except SQLAlchemyError as e:
        db_session.rollback()
        raise ApiError("Database error while deleting document signing", status=500)
    except Exception as e:
        db_session.rollback()
        if isinstance(e, ApiError):
            raise
        raise ApiError(
            f"Unexpected error while deleting document signing: {str(e)}", status=500
        )


def _apply_status_to_entities(db_session, entities, new_status, document_signing_id):
    from strandproperties.logger import logger

    for entity in entities:
        match entity.entity_type:
            case DocumentSigningEntityType.FI_SALES_AGREEMENT:
                _apply_status_to_sales_agreement(
                    db_session, entity, new_status, document_signing_id, logger
                )
            case DocumentSigningEntityType.FI_PURCHASE_OFFER:
                _apply_status_to_purchase_offer(db_session, entity, new_status)
            case DocumentSigningEntityType.FI_COUNTER_OFFER:
                _apply_status_to_counter_offer(db_session, entity, new_status)
            case _:
                continue


def _apply_status_to_sales_agreement(
    db_session, entity, new_status, document_signing_id, logger
):

    sales_agreement = db_session.scalars(
        select(FISalesAgreement).where(FISalesAgreement.id == entity.entity_id)
    ).one_or_none()

    if not sales_agreement:
        return

    if (
        new_status == "completed"
        and sales_agreement.status == FISalesAgreementStatusEnum.PENDING_SIGNATURES
    ):
        sales_agreement.status = FISalesAgreementStatusEnum.COMPLETED
        logger.info(
            "FI Sales Agreement marked as COMPLETED after signing finished",
            extra={
                "salesAgreementId": sales_agreement.id,
                "documentSigningId": document_signing_id,
            },
        )

    elif (
        new_status in ("declined", "cancelled")
        and sales_agreement.status == FISalesAgreementStatusEnum.PENDING_SIGNATURES
    ):
        sales_agreement.status = FISalesAgreementStatusEnum.VALIDATED
        logger.info(
            "FI Sales Agreement reverted to VALIDATED after signing %s",
            new_status,
            extra={
                "salesAgreementId": sales_agreement.id,
                "documentSigningId": document_signing_id,
            },
        )


def _apply_status_to_purchase_offer(db_session, entity, new_status):
    fi_purchase_offer = db_session.scalars(
        select(FIPurchaseOffer).where(FIPurchaseOffer.id == entity.entity_id)
    ).one_or_none()

    if not fi_purchase_offer:
        return

    if (
        new_status == "completed"
        and fi_purchase_offer.status
        == FIPurchaseOfferStatusEnum.PENDING_OFFEROR_SIGNATURE
    ):
        fi_purchase_offer.status = FIPurchaseOfferStatusEnum.OFFEROR_SIGNED
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            object_id=fi_purchase_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": FIPurchaseOfferStatusEnum.PENDING_OFFEROR_SIGNATURE},
            data_after={"status": FIPurchaseOfferStatusEnum.OFFEROR_SIGNED},
        )

    elif (
        new_status == "completed"
        and fi_purchase_offer.status
        == FIPurchaseOfferStatusEnum.PENDING_OFFEREE_SIGNATURE
    ):
        fi_purchase_offer.status = FIPurchaseOfferStatusEnum.OFFEREE_SIGNED
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            object_id=fi_purchase_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": FIPurchaseOfferStatusEnum.PENDING_OFFEREE_SIGNATURE},
            data_after={"status": FIPurchaseOfferStatusEnum.OFFEREE_SIGNED},
        )

    elif new_status in ("declined", "cancelled"):
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            object_id=fi_purchase_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": fi_purchase_offer.status},
            data_after={"status": FIPurchaseOfferStatusEnum.REJECTED},
        )
        fi_purchase_offer.status = FIPurchaseOfferStatusEnum.REJECTED


def _apply_status_to_counter_offer(db_session, entity, new_status):
    counter_offer = db_session.scalars(
        select(FICounterOffer).where(FICounterOffer.id == entity.entity_id)
    ).one_or_none()

    if not counter_offer:
        return

    if (
        new_status == "completed"
        and counter_offer.status == FIPurchaseOfferStatusEnum.PENDING_OFFEROR_SIGNATURE
    ):
        counter_offer.status = FIPurchaseOfferStatusEnum.OFFEROR_SIGNED
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_COUNTER_OFFER,
            object_id=counter_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": FIPurchaseOfferStatusEnum.PENDING_OFFEROR_SIGNATURE},
            data_after={"status": FIPurchaseOfferStatusEnum.OFFEROR_SIGNED},
            relation_object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            relation_object_id=counter_offer.purchase_offer_id,
        )

    elif (
        new_status == "completed"
        and counter_offer.status == FIPurchaseOfferStatusEnum.PENDING_OFFEREE_SIGNATURE
    ):
        counter_offer.status = FIPurchaseOfferStatusEnum.OFFEREE_SIGNED
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_COUNTER_OFFER,
            object_id=counter_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": FIPurchaseOfferStatusEnum.PENDING_OFFEREE_SIGNATURE},
            data_after={"status": FIPurchaseOfferStatusEnum.OFFEREE_SIGNED},
            relation_object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            relation_object_id=counter_offer.purchase_offer_id,
        )

    elif new_status in ("declined", "cancelled"):
        create_event_log(
            db_session,
            object_type=EventLogObjectType.FI_COUNTER_OFFER,
            object_id=counter_offer.id,
            actor_type=EventLogActorType.SYSTEM,
            action=EventLogAction.STATUS_CHANGED,
            data_before={"status": counter_offer.status},
            data_after={"status": FIPurchaseOfferStatusEnum.REJECTED},
            relation_object_type=EventLogObjectType.FI_PURCHASE_OFFER,
            relation_object_id=counter_offer.purchase_offer_id,
        )
        counter_offer.status = FIPurchaseOfferStatusEnum.REJECTED


def _validate_document_signing_entities_for_deletion(
    db_session, actor_id, document_signing_id
):
    """
    Validates that the document signing entities are compatible with deletion.
    """
    document_signing_entities = db_session.scalars(
        select(DocumentSigningEntity).where(
            DocumentSigningEntity.document_signing_id == document_signing_id,
        )
    ).all()

    if not document_signing_entities:
        return

    for entity in document_signing_entities:
        if entity.entity_type == DocumentSigningEntityType.FI_SALES_AGREEMENT:
            _validate_sales_agreement_for_deletion(
                db_session, actor_id, entity.entity_id
            )
        elif entity.entity_type == DocumentSigningEntityType.FI_PURCHASE_OFFER:
            _validate_purchase_offer_for_deletion(
                db_session, actor_id, entity.entity_id
            )
        elif entity.entity_type == DocumentSigningEntityType.FI_COUNTER_OFFER:
            _validate_counter_offer_for_deletion(db_session, actor_id, entity.entity_id)
        elif entity.entity_type == DocumentSigningEntityType.DOCUMENT_LIBRARY_ITEM:
            _validate_document_library_item_for_deletion(
                db_session, actor_id, entity.entity_id
            )
        else:
            raise ApiError(f"Unsupported entity type: {entity.entity_type}", status=400)


def _validate_sales_agreement_for_deletion(db_session, actor_id, sales_agreement_id):
    sales_agreement = db_session.scalars(
        select(FISalesAgreement).where(FISalesAgreement.id == sales_agreement_id)
    ).one_or_none()

    if not sales_agreement:
        raise ApiError(
            f"Sales agreement with ID {sales_agreement_id} not found", status=404
        )

    if sales_agreement.status != FISalesAgreementStatusEnum.PENDING_SIGNATURES:
        raise ApiError(
            f"Sales agreement with ID {sales_agreement_id} is not pending signatures",
            status=400,
        )
    # TODO: we need access quard rails here (but they are on a really dummy level even in the views right now)


# TODO: Implement these, would be nice to validate that the user has access to the entity and that the entity status allows signing deletion
def _validate_purchase_offer_for_deletion(db_session, actor_id, purchase_offer_id):
    pass


def _validate_counter_offer_for_deletion(db_session, actor_id, counter_offer_id):
    pass


def _validate_document_library_item_for_deletion(
    db_session, actor_id, document_library_item_id
):
    pass
