import base64
import mimetypes
from pathlib import Path
from typing import Dict, Optional
from urllib.parse import urlparse

import requests

from strandproperties.logger import logger
from strandproperties.services.documents.config import (
    IMAGES_DIR,
    TEMPLATE_IMAGES,
    TemplatesEnum,
)


class ImageDecodeException(Exception):
    pass


class ImageProcessor:
    """
    A reusable class for handling image processing operations including
    Base64 conversion, caching, and template image management.
    """

    def __init__(self, images_dir: str = IMAGES_DIR):
        self.images_dir = images_dir
        self._image_cache: Dict[str, str] = {}

    def _is_url(self, path: str) -> bool:
        parsed = urlparse(path)
        return parsed.scheme in ["http", "https"]

    def _convert_local_image_to_base64(self, image_path: Path) -> str:
        if not image_path.exists():
            logger.warning(f"Image file not found: {image_path}")
            return "data:image/png;base64,"

        mime_type, _ = mimetypes.guess_type(str(image_path))
        if not mime_type or not mime_type.startswith("image/"):
            mime_type = "image/png"

        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            base64_encoded = base64.b64encode(image_data).decode("utf-8")

        return f"data:{mime_type};base64,{base64_encoded}"

    def _convert_url_image_to_base64(self, image_url: str) -> str:
        try:
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()

            content_type = response.headers.get("content-type", "")
            if content_type.startswith("image/"):
                mime_type = content_type.split(";")[0]
            else:
                mime_type, _ = mimetypes.guess_type(image_url)
                if not mime_type or not mime_type.startswith("image/"):
                    mime_type = "image/png"

            image_data = response.content
            base64_encoded = base64.b64encode(image_data).decode("utf-8")

            return f"data:{mime_type};base64,{base64_encoded}"

        except requests.RequestException as e:
            error_msg = f"Error fetching image from URL {image_url}: {e}"
            raise ImageDecodeException(error_msg) from e
        except Exception as e:
            error_msg = f"Error converting URL image {image_url}: {e}"
            raise ImageDecodeException(error_msg) from e

    def convert_image_to_base64(self, image_filename: str) -> str:
        if image_filename in self._image_cache:
            return self._image_cache[image_filename]
        try:
            if self._is_url(image_filename):
                data_uri = self._convert_url_image_to_base64(image_filename)
            else:
                image_path = Path(self.images_dir) / image_filename
                if not image_path.is_absolute():
                    image_path = Path(self.images_dir) / image_filename
                data_uri = self._convert_local_image_to_base64(image_path)

            self._image_cache[image_filename] = data_uri
            return data_uri

        except Exception as e:
            error_msg = f"Error converting image {image_filename}: {e}"
            raise ImageDecodeException(error_msg) from e

    def get_template_images(
        self,
        template_name: TemplatesEnum,
        custom_images: Optional[Dict[str, str]] = None,
    ) -> Dict[str, str]:
        image_config: Dict[str, str] = {}

        if template_name in TEMPLATE_IMAGES:
            image_config.update(TEMPLATE_IMAGES[template_name])
        if custom_images:
            image_config.update(custom_images)
        if not image_config and template_name not in TEMPLATE_IMAGES:
            logger.warning(f"Template {template_name} does not support images")
            return {}

        base64_images = {
            key: self.convert_image_to_base64(filename)
            for key, filename in image_config.items()
            if filename
        }
        return base64_images
