import logging
from datetime import datetime

import cronitor
import pytz
from huey import crontab
from sqlalchemy import select

from strandproperties.constants import FIPurchaseOfferStatusEnum
from strandproperties.tasks.broker import huey_app

logger = logging.getLogger("huey")
expiring_statuses = [
    FIPurchaseOfferStatusEnum.VALIDATED,
    FIPurchaseOfferStatusEnum.PENDING_OFFEROR_SIGNATURE,
    FIPurchaseOfferStatusEnum.OFFEROR_SIGNED,
    FIPurchaseOfferStatusEnum.PENDING_OFFEREE_SIGNATURE,
]


@huey_app.periodic_task(crontab(minute="*"))
@cronitor.job("expire_outdated_fi_purchase_offers")
def task_expire_outdated_fi_purchase_offers():
    from strandproperties.models.fi_purchase_offer import FIPurchaseOffer

    with huey_app.get_db_session() as db_session:
        with db_session.begin():
            purchase_offers = db_session.scalars(
                select(FIPurchaseOffer).where(
                    FIPurchaseOffer.status.in_(expiring_statuses)
                )
            ).all()
            for purchase_offer in purchase_offers:
                valid_until = purchase_offer.valid_until
                if valid_until.tzinfo is None:
                    valid_until = valid_until.astimezone(
                        pytz.timezone("Europe/Helsinki")
                    )

                if valid_until.astimezone(pytz.UTC) < datetime.now(pytz.UTC):
                    purchase_offer.status = FIPurchaseOfferStatusEnum.EXPIRED


@huey_app.periodic_task(crontab(minute="*"))
@cronitor.job("expire_outdated_fi_counter_offers")
def task_expire_outdated_fi_counter_offers():
    from strandproperties.models.fi_counter_offer import FICounterOffer

    with huey_app.get_db_session() as db_session:
        with db_session.begin():
            counter_offers = db_session.scalars(
                select(FICounterOffer).where(
                    FICounterOffer.status.in_(expiring_statuses)
                )
            ).all()

            for counter_offer in counter_offers:
                valid_until = counter_offer.valid_until
                if valid_until.tzinfo is None:
                    valid_until = valid_until.astimezone(
                        pytz.timezone("Europe/Helsinki")
                    )

                if valid_until.astimezone(pytz.UTC) < datetime.now(pytz.UTC):
                    counter_offer.status = FIPurchaseOfferStatusEnum.EXPIRED
