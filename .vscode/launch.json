{"version": "0.2.0", "configurations": [{"name": "Debug SP App", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/dev_run.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "python": "${workspaceFolder}/backend/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "envFile": "${workspaceFolder}/backend/.env", "justMyCode": false, "stopOnEntry": false}, {"name": "Backend: PyTest (All tests)", "type": "debugpy", "request": "launch", "cwd": "${workspaceFolder}/backend", "python": "${workspaceFolder}/backend/.venv/bin/python", "module": "pytest", "args": ["-s"], "console": "integratedTerminal", "envFile": "${workspaceFolder}/backend/.env", "env": {"DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "justMyCode": false}, {"name": "Backend: PyTest (Filter by Name/Path)", "type": "debugpy", "request": "launch", "cwd": "${workspaceFolder}/backend", "python": "${workspaceFolder}/backend/.venv/bin/python", "module": "pytest", "args": ["-k", "${input:testName}", "-s"], "console": "integratedTerminal", "envFile": "${workspaceFolder}/backend/.env", "env": {"DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "justMyCode": false}], "inputs": [{"id": "testName", "type": "promptString", "description": "<PERSON><PERSON><PERSON><PERSON> tên test hoặc đường dẫn file test", "default": "test_"}]}