from wsgiref.simple_server import make_server

# WSGI application
from strandproperties.wsgi import application
from strandproperties.config import app_cfg


if __name__ == "__main__":
    host = "0.0.0.0"
    port = 6543

    print("Starting dev WSGI server (wsgiref.simple_server)")
    print(f"Env: {app_cfg.env}")
    print(f"Listening at: http://{host}:{port}")

    with make_server(host, port, application) as httpd:
        httpd.serve_forever()
